package service

import (
	"context"
	"encoding/json"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"github.com/apaxa-go/helper/strconvh"
)

type ConfigService struct {
}

func NewConfigService() *ConfigService {
	return &ConfigService{}
}

func (c *ConfigService) GetConfig(ctx context.Context, identity *fwe_trade_common.BizIdentity) (json.RawMessage, error) {
	configMap := map[string]json.RawMessage{}
	get, err := caller.DefaultTccClient.Get(ctx, "biz_scene_config")
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal([]byte(get), &configMap); err != nil {
		return nil, err
	}

	// logs.CtxInfo(ctx, "[GetConfig] %s", get)

	return configMap[strconvh.FormatInt32(identity.GetBizScene())], nil
}

func (c *ConfigService) GetSnapshotGray(ctx context.Context, bizScene int32) (flag bool, bizErr *errdef.BizErr) {
	str, err := caller.DefaultTccClient.Get(ctx, "snapshot_gray")
	if err != nil {
		bizErr = errdef.NewRpcErr(err)
		logs.CtxError(ctx, "[GetSnapshotGray] err=%s", bizErr.Error())
		return
	}
	var grayList []int32
	err = json.Unmarshal([]byte(str), &grayList)
	if err != nil {
		bizErr = errdef.NewRpcErr(err)
		logs.CtxError(ctx, "[GetSnapshotGray] err=%s", bizErr.Error())
		return
	}
	return slices.ContainsInt32(grayList, bizScene), nil
}

func (c *ConfigService) GetBoss(ctx context.Context, userID string) (flag bool, bizErr *errdef.BizErr) {
	str, err := caller.DefaultTccClient.Get(ctx, "boss_list")
	if err != nil {
		bizErr = errdef.NewRpcErr(err)
		logs.CtxError(ctx, "[GetBoss] err=%s", bizErr.Error())
		return
	}
	var bossList []string
	err = json.Unmarshal([]byte(str), &bossList)
	if err != nil {
		bizErr = errdef.NewRpcErr(err)
		logs.CtxError(ctx, "[GetBoss] err=%s", bizErr.Error())
		return
	}
	return slices.ContainsString(bossList, userID), nil
}
