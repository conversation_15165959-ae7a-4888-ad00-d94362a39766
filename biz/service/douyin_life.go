package service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/overpass/motor_dealer_douyin_open_proxy/kitex_gen/motor/dealer/douyin_open_proxy"
	"code.byted.org/overpass/motor_dealer_douyin_open_proxy/rpc/motor_dealer_douyin_open_proxy"
	"context"
)

type DouyinLifeService struct {
}

func NewDouyinLifeService() *DouyinLifeService {
	return &DouyinLifeService{}
}

func init() {
	motor_dealer_douyin_open_proxy.DefaultClient().Conf().EnableReqRespLog = true
	motor_dealer_douyin_open_proxy.DefaultClient().Conf().EnableErrHandler = false
}

func (d *DouyinLifeService) PushDelivery(ctx context.Context, req *douyin_open_proxy.DouyinLifePushDeliveryReq) ([]*douyin_open_proxy.VerifyResult_, *errdef.BizErr) {
	lifeResp, err := motor_dealer_douyin_open_proxy.RawCall.LifePushDelivery(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[DouyinLifeService][PushDelivery] err=%s", err.Error())
		return nil, errdef.NewRawErr(errdef.DouyinLifeProxyErr, err.Error())
	}
	baseResp := lifeResp.BaseResp
	if baseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[DouyinLifeService][PushDelivery] err=%s", baseResp.StatusMessage)
		return nil, errdef.NewRawErr(errdef.DouyinLifeProxyErr, baseResp.StatusMessage)
	}
	return lifeResp.VerifyResults, nil
}
