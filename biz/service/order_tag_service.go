package service

import (
	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"

	"code.byted.org/gopkg/lang/maps"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metrics"
	"code.byted.org/motor/fwe_trade_common/trade_meta"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	OrderRpc "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order"
	"code.byted.org/motor/gopkg/tools/tools_recover_kite"
)

type TagService struct {
	defaultJsonStr string
}

func NewTagService() *TagService {
	return &TagService{
		defaultJsonStr: "{}",
	}
}

func (s *TagService) UpdateV2(ctx context.Context, orderID string, tagMap map[string]string) (bizErr *errdef.BizErr) {
	var (
		rsp *OrderRpc.UpdateOrderResp
		err error
	)
	if rsp, err = orderClient.UpdateOrder(ctx, &OrderRpc.UpdateOrderReq{
		Condition: &OrderRpc.UpdateOrderCond{
			OrderID: orderID,
		},
		UpdateData: nil,
		UpdateTag: &OrderRpc.UpdateTagData{
			UpdateTag: tagMap,
		},
	}); err != nil {
		bizErr = errdef.NewRpcErr(err)
		logs.CtxError(ctx, "[TagService] err=%v", bizErr.Error())
		return
	} else if rsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewBizErr(errdef.OrderRpcErr, nil, rsp.BaseResp.StatusMessage)
		logs.CtxError(ctx, "[TagService] err=%v", bizErr.Error())
		return
	}
	return
}

func (s *TagService) UpdateTag(ctx context.Context, orderID string, bizScene int32, tagMap map[string]string) (newMap map[string]string, bizErr *errdef.BizErr) {
	if len(tagMap) == 0 || orderID == "" {
		return
	}
	s.AsyncCheckKey(ctx, orderID, bizScene, tagMap)

	// 灰度BizScene 走 rpc调用
	var grayFlag bool
	if grayFlag, bizErr = NewConfigService().GetSnapshotGray(ctx, bizScene); bizErr == nil && grayFlag {
		logs.CtxInfo(ctx, "[CreateOrder] use orderRpc order_id=%s", orderID)
		if bizErr = s.UpdateV2(ctx, orderID, tagMap); bizErr != nil {
			return
		}
		return
	}
	logs.CtxInfo(ctx, "[CreateOrder] use directDB order_id=%s", orderID)

	var err error
	var dataList []*db_model.FweOrderTag
	wdb := db_query.Use(caller.WriteDB(ctx))
	dataList, err = wdb.FweOrderTag.WithContext(ctx).
		Where(wdb.FweOrderTag.OrderID.Eq(orderID)).
		Limit(1).Find()
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "TagService")
		logs.CtxError(ctx, "[tagService] err=%s", bizErr.Error())
		return
	}
	if len(dataList) == 0 {
		bizErr = errdef.NewParamsErr(fmt.Sprintf("order_id=%s not find tag", orderID))
		logs.CtxError(ctx, "[tagService] err=%s", bizErr.Error())
		return
	}
	var tagStr string
	tagStr, newMap, bizErr = s.mergeColumnWithMap(ctx, dataList[0].Tag, tagMap)
	if bizErr != nil {
		logs.CtxError(ctx, "[tagService] err=%s", bizErr.Error())
		return
	}

	_, err = db_query.Q.FweOrderTag.WithContext(ctx).
		Where(db_query.Q.FweOrderTag.OrderID.Eq(orderID)).
		UpdateColumnSimple(
			db_query.Q.FweOrderTag.Tag.Value(tagStr),
		)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "TagService")
		return
	}

	return
}

func (s *TagService) mergeColumnWithMap(ctx context.Context, columnValue *string, tagMap map[string]string) (newColumn string, newMap map[string]string, bizErr *errdef.BizErr) {
	if len(tagMap) == 0 {
		return
	}
	var err error
	oldMap := make(map[string]string)
	if columnValue != nil {
		err = utils.Unmarshal(*columnValue, &oldMap)
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "TagService")
			logs.CtxError(ctx, "[TagService] err=%s", bizErr.Error())
			return
		}
	}
	maps.MergeStrStr(oldMap, tagMap)
	newColumn, err = utils.Marshal(oldMap)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "TagService")
		logs.CtxError(ctx, "[TagService] err=%s", bizErr.Error())
		return
	}
	err = utils.Unmarshal(newColumn, &newMap)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "TagService")
		logs.CtxError(ctx, "[TagService] err=%s", bizErr.Error())
		return
	}
	return
}

func (s *TagService) AsyncCheckKey(ctx context.Context, orderID string, bizScene int32, tagMap map[string]string) {
	go func() {
		defer func() {
			tools_recover_kite.CheckRecover(ctx, recover(), nil)
		}()
		_ = s.checkKey(ctx, orderID, bizScene, tagMap)
	}()
}

func (s *TagService) checkKey(ctx context.Context, orderID string, bizScene int32, tagMap map[string]string) (bizErr *errdef.BizErr) {
	tradeMeta, err := trade_meta.GetTradeMeta(ctx, bizScene, nil)
	if err != nil {
		bizErr = errdef.NewRpcErr(err)
		logs.CtxError(ctx, "[TagService] err=%v", bizErr.Error())
		return
	}
	if tradeMeta == nil || tradeMeta.TagConfig == nil || len(tradeMeta.TagConfig.TagMap) == 0 {
		logs.CtxInfo(ctx, "[tagService] biz_scene=%s no tag meta")
		return
	}
	const (
		ErrTypeTagKeyNotFound      = "NotFound"
		ErrTypeTagValueTypeIllegal = "TypeIllegal"
	)
	for k, v := range tagMap {
		tagConfig := tradeMeta.TagConfig.TagMap[k]
		if tagConfig == nil {
			tags := []metrics.T{
				{Name: consts.TagBizScene, Value: fmt.Sprintf("%d", bizScene)},
				{Name: "tag_key", Value: k},
				{Name: "err_type", Value: ErrTypeTagKeyNotFound},
			}
			utils.EmitCounter("tag_service", 1, tags...)
			logs.CtxWarn(ctx, "[TagService] order_id=%s tag_key=%s key not found", orderID, k)
			continue
		}
		if s.checkValue(ctx, tagConfig.TagType, v) {
			continue
		}
		tags := []metrics.T{
			{Name: consts.TagBizScene, Value: fmt.Sprintf("%d", bizScene)},
			{Name: "tag_key", Value: k},
			{Name: "err_type", Value: ErrTypeTagValueTypeIllegal},
		}
		utils.EmitCounter("tag_service", 1, tags...)
		logs.CtxWarn(ctx, "[TagService] order_id=%s tag_key=%s value type illegal", orderID, k)
	}
	return
}

func (s *TagService) checkValue(ctx context.Context, tagType trade_meta.TagType, v string) (isValid bool) {
	switch tagType {
	case trade_meta.TagTypeJson:
		if v == "" {
			isValid = true
		} else {
			isValid = json.Valid([]byte(v))
		}
	case trade_meta.TagTypeFloat:
		if v == "" {
			isValid = true
		} else {
			floatRegex := regexp.MustCompile(`^[+\-]?(?:(?:0|[1-9]\d*)(?:\.\d*)?|\.\d+)(?:\d[eE][+\-]?\d+)?$`)
			if floatRegex.MatchString(v) {
				isValid = true
			}
		}
	case trade_meta.TagTypeInteger:
		if v == "" {
			isValid = true
		} else if _, err := strconv.ParseInt(v, 10, 64); err == nil {
			isValid = true
		}
	case trade_meta.TagTypeString:
		isValid = true
	default:
	}
	return
}
