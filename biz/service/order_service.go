package service

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/maps"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitutil"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/tidwall/gjson"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strconv"

	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	OrderRpc "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

/*** service 层 order 域 统一CURD方法收敛 ***/

type OptionID int32

const (
	OptionNeedFinance OptionID = 1 + iota
	OptionNeedCont
	OptionNeedSettle
	OptionNeedRefund
)

type OrderOption struct {
	OptionID OptionID
}

type Order struct{}

func NewOrderService() *Order {
	return &Order{}
}

func (service *Order) GenOrderID(ctx context.Context, bizScene int32) (string, error) {
	orderID, err := utils.MustGenIDStr()
	if err != nil {
		logs.CtxError(ctx, "[shSellOrderCreateExecution] gen order_id failed, err=%s", err)
		return "", err
	}
	return fmt.Sprintf("%d_%s", bizScene, orderID), nil
}

func (service *Order) GetOrderByID(ctx context.Context, orderID string, options ...*OrderOption) (*service_model.Order, *errdef.BizErr) {
	var (
		dataMap map[string]*service_model.Order
		bizErr  *errdef.BizErr
	)
	dataMap, bizErr = service.MGetOrderByIDs(ctx, []string{orderID}, options...)
	if bizErr != nil {
		return nil, bizErr
	}
	if order := dataMap[orderID]; order == nil || order.FweOrder == nil {
		bizErr = errdef.NewRawErr(errdef.ParamErr, fmt.Sprintf("order_id=%s not exist", orderID))
		return nil, bizErr
	} else {
		return order, nil
	}
}

// GetBizSceneByID 根据订单号查询bizScene,可自由选择主库从库
func (service *Order) GetBizSceneByID(ctx context.Context, db *gorm.DB, orderID string) (bizScene int32, smVersion int32, bizErr *errdef.BizErr) {
	q := db_query.Use(db)
	orderList, err := q.WithContext(ctx).FweOrder.
		Select(db_query.Q.FweOrder.BizScene, db_query.Q.FweOrder.SmVersion).
		Where(db_query.Q.FweOrder.OrderID.Eq(orderID)).Find()
	if err != nil {
		logs.CtxError(ctx, "[OrderService] err=%v", err)
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
		return
	}
	if orderList == nil || len(orderList) == 0 {
		logs.CtxError(ctx, "[OrderService] err=%v", err)
		bizErr = errdef.NewRawErr(errdef.ParamErr, "the bizScene corresponding to the order id was not found")
		return
	}
	bizScene = orderList[0].BizScene
	smVersion = orderList[0].SmVersion
	return
}

func (service *Order) MGetOrderByIDs(ctx context.Context, orderIDs []string, options ...*OrderOption) (map[string]*service_model.Order, *errdef.BizErr) {
	var (
		eg, _               = utils.WithContext(ctx)
		orderList           []*db_model.FweOrder
		tmpFinanceList      []*db_model.FFinanceOrder
		financeList         []*db_model.FFinanceOrder
		refundFinanceList   []*db_model.FFinanceOrder
		settleFinanceList   []*db_model.FFinanceOrder
		withdrawFinanceList []*db_model.FFinanceOrder
		transferFinanceList []*db_model.FFinanceOrder
		tagList             []*db_model.FweOrderTag
		contList            []*db_model.FweOrderContract
		settleFList         []*db_model.FSettleFinanceOrder
		refundFList         []*db_model.FRefundFinanceOrder
		bizErr              *errdef.BizErr
		wdb                 = caller.WriteDB(ctx)
		query               = db_query.Use(wdb) // 查主库
	)

	// 拉主订单数据
	eg.Go(ctx, func(ctx context.Context) error {
		var gErr error
		orderList, gErr = query.FweOrder.WithContext(ctx).
			Where(db_query.FweOrder.OrderID.In(orderIDs...)).Find()
		return gErr
	})
	// 拉标签数据
	eg.Go(ctx, func(ctx context.Context) error {
		var gErr error
		tagList, gErr = query.FweOrderTag.WithContext(ctx).
			Where(db_query.FweOrderTag.OrderID.In(orderIDs...)).Find()
		return gErr
	})

	for _, option := range options {
		if option == nil {
			continue
		}
		switch option.OptionID {
		case OptionNeedFinance:
			// 拉资金单数据
			eg.Go(ctx, func(ctx context.Context) error {
				var gErr error
				tmpFinanceList, gErr = query.FFinanceOrder.WithContext(ctx).
					Where(db_query.FFinanceOrder.OrderID.In(orderIDs...)).Find()

				categoryMap := slices.GroupBy(tmpFinanceList, func(financeOrder *db_model.FFinanceOrder) int32 { return financeOrder.TradeCategory }).(map[int32][]*db_model.FFinanceOrder)
				financeList = categoryMap[int32(0)]
				financeList = append(financeList, categoryMap[int32(fwe_trade_common.TradeCategory_Pay)]...)
				refundFinanceList = categoryMap[int32(fwe_trade_common.TradeCategory_Refund)]
				settleFinanceList = categoryMap[int32(fwe_trade_common.TradeCategory_Settle)]
				withdrawFinanceList = categoryMap[int32(fwe_trade_common.TradeCategory_Withdraw)]
				transferFinanceList = categoryMap[int32(fwe_trade_common.TradeCategory_Transfer)]
				return gErr
			})
		case OptionNeedCont:
			// 拉合同数据
			eg.Go(ctx, func(ctx context.Context) error {
				var gErr error
				contList, gErr = query.FweOrderContract.WithContext(ctx).
					Where(db_query.FweOrderContract.OrderID.In(orderIDs...)).Find()
				return gErr
			})
		case OptionNeedSettle:
			// 拉分账资金单数据
			eg.Go(ctx, func(ctx context.Context) error {
				var gErr error
				settleFList, gErr = query.FSettleFinanceOrder.WithContext(ctx).
					Where(db_query.FSettleFinanceOrder.OrderID.In(orderIDs...)).Find()
				return gErr
			})
		case OptionNeedRefund:
			// 拉退款资金单数据
			eg.Go(ctx, func(ctx context.Context) error {
				var gErr error
				refundFList, gErr = query.FRefundFinanceOrder.WithContext(ctx).
					Where(db_query.FRefundFinanceOrder.OrderID.In(orderIDs...)).Find()
				return gErr
			})
		}
	}

	// 不允许错误
	err := eg.Wait()
	if err != nil {
		logs.CtxError(ctx, "[OrderService] err=%v", err)
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
		return nil, bizErr
	}

	// 打包
	return packer.OrderDB2ServiceMap(ctx, orderIDs, orderList, tagList,
		financeList, refundFinanceList, settleFinanceList, withdrawFinanceList, transferFinanceList,
		contList, settleFList, refundFList), nil
}

func (service *Order) CreateV2(ctx context.Context, order *service_model.Order) (bizErr *errdef.BizErr) {
	var (
		rpcReq *OrderRpc.CreateOrderReq
		rsp    *OrderRpc.CreateOrderResp
		err    error
	)
	rpcReq, bizErr = service.buildCreateOrderReq(ctx, order)
	if bizErr != nil {
		return
	}
	if rsp, err = orderClient.CreateOrder(ctx, rpcReq); err != nil {
		bizErr = errdef.NewRpcErr(err)
		logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
		return
	} else if rsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewBizErr(errdef.OrderRpcErr, nil, rsp.BaseResp.StatusMessage)
		logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
		return
	}
	return
}

func (service *Order) CreateOrder(ctx context.Context, order *service_model.Order) (bizErr *errdef.BizErr) {
	if order == nil || order.FweOrder == nil || order.FweOrder.OrderID == "" {
		logs.CtxInfo(ctx, "[OrderService] input nil")
		return
	}
	var orderID = order.FweOrder.OrderID

	// 判读有没有幂等号，没有就取当前订单id
	if order.FweOrder.IdempotentID == "" {
		order.FweOrder.IdempotentID = order.FweOrder.OrderID
	}

	// mobileID为空，使用buyerExtra的进行填充 方便筛选
	if order.FweOrder.MobileID == 0 && order.FweOrder.BuyerExtra != nil {
		order.FweOrder.MobileID = gjson.Get(*order.FweOrder.BuyerExtra, "mobile_id").Int()
	}

	// 灰度BizScene 走 rpc调用
	var grayFlag bool
	if grayFlag, bizErr = NewConfigService().GetSnapshotGray(ctx, order.FweOrder.BizScene); bizErr == nil && grayFlag {
		logs.CtxInfo(ctx, "[CreateOrder] use orderRpc order_id=%s", order.FweOrder.OrderID)
		if bizErr = service.CreateV2(ctx, order); bizErr != nil {
			return
		}
		return
	}
	logs.CtxInfo(ctx, "[CreateOrder] use directDB order_id=%s", order.FweOrder.OrderID)

	// 写主表
	err := db_query.Q.FweOrder.WithContext(ctx).
		Omit(db_query.FweOrder.CreateTime, db_query.FweOrder.UpdateTime).
		Create(order.FweOrder)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
		logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
		return
	}

	// 写资金单表
	if len(order.FinanceList) > 0 {
		bizErr = NewFinanceOrderService().CreateFinanceOrderList(ctx, order.FinanceList)
		if bizErr != nil {
			logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
			return
		}
	}

	var orderTag = &db_model.FweOrderTag{
		OrderID:  orderID,
		Tag:      conv.StringPtr("{}"),
		BizExtra: conv.StringPtr("{}"),
	}
	// 写业务透传字段
	if len(order.BizExtra) > 0 {
		*orderTag.BizExtra, err = sonic.MarshalString(order.BizExtra)
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
			logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
			return
		}
	}
	// 添加环境标签
	order.TagMap = appendEnvTag(ctx, order.TagMap)
	// 写标签表
	if len(order.TagMap) > 0 {
		*orderTag.Tag, err = sonic.MarshalString(order.TagMap)
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
			logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
			return
		}
	}
	err = db_query.Q.FweOrderTag.WithContext(ctx).Create(orderTag)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
		logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
		return
	}

	// 创建分账信息
	if order.TradeSplitInfo != nil && len(order.TradeSplitInfo.Detail) > 0 {
		bizErr = NewSplitInfoService().MCreateSplitInfo(ctx, orderID, order.TradeSplitInfo)
		if bizErr != nil {
			logs.CtxError(ctx, "[OrderService] MCreateSplitInfo err=%v", bizErr.Error())
			return
		}
	}

	return nil
}

func appendEnvTag(ctx context.Context, tagMap map[string]string) map[string]string {
	if tagMap == nil {
		tagMap = make(map[string]string)
	}
	curEnv := env.Env()
	ctxEnv, exist := kitutil.GetCtxEnv(ctx)
	if exist {
		curEnv = ctxEnv
	}
	tagMap[consts.InfraCreateOrderENV] = curEnv
	return tagMap
}

func (service *Order) UpdateV2(ctx context.Context, orderID string, params *service_model.UpdateOrderParams) (bizErr *errdef.BizErr) {
	var (
		rsp             *OrderRpc.UpdateOrderResp
		updateData      *OrderRpc.FweOrderData
		updateCondition *OrderRpc.UpdateOrderCond
		err             error
	)
	if updateData, bizErr = packer.OrderUpdatePack(orderID, params); bizErr != nil {
		return
	}
	if updateCondition, bizErr = packer.OrderCondPack(orderID, params); bizErr != nil {
		return
	}
	if rsp, err = orderClient.UpdateOrder(ctx, &OrderRpc.UpdateOrderReq{
		Condition:  updateCondition,
		UpdateData: updateData,
	}); err != nil {
		bizErr = errdef.NewRpcErr(err)
		logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
		return
	} else if rsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewBizErr(errdef.OrderRpcErr, nil, rsp.BaseResp.StatusMessage)
		logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
		return
	}
	return
}

func (service *Order) UpdateOrder(ctx context.Context, orderID string, params *service_model.UpdateOrderParams) (bizErr *errdef.BizErr) {

	// 兜底处理where条件
	if len(params.WhereOrderStatus) == 0 && params.UpdateOrderStatus != nil && params.UpdateBeforeStatus != nil {
		params.WhereOrderStatus = []int32{*params.UpdateBeforeStatus}
	}

	// 灰度BizScene 走 rpc调用
	var (
		fweOrderDB *db_model.FweOrder
		grayFlag   bool
		err        error
	)
	fweOrderDB, err = db_query.Q.FweOrder.WithContext(ctx).Select(db_query.FweOrder.BizScene).Where(db_query.FweOrder.OrderID.Eq(orderID)).First()
	if err != nil {
		bizErr = errdef.NewDBErr(err)
		logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
		return
	}
	if grayFlag, bizErr = NewConfigService().GetSnapshotGray(ctx, fweOrderDB.BizScene); bizErr == nil && grayFlag {
		if bizErr = service.UpdateV2(ctx, orderID, params); bizErr != nil {
			return
		}
		return
	}

	// 拼接条件字段值
	var whereCond []gen.Condition
	whereCond = append(whereCond, db_query.FweOrder.OrderID.Eq(orderID))
	if len(params.WhereOrderStatus) > 0 {
		whereCond = append(whereCond, db_query.FweOrder.OrderStatus.In(params.WhereOrderStatus...))
	}
	// 拼接更新字段值
	var updateField []field.AssignExpr
	if params.Operator != nil {
		updateField = append(updateField, db_query.Q.FweOrder.Operator.Value(params.Operator.OperatorID))
		updateField = append(updateField, db_query.Q.FweOrder.OperatorName.Value(params.Operator.OperatorName))
	}
	if params.UpdateOrderStatus != nil {
		updateField = append(updateField, db_query.Q.FweOrder.OrderStatus.Value(*params.UpdateOrderStatus))
	}
	if params.UpdateBeforeStatus != nil {
		updateField = append(updateField, db_query.Q.FweOrder.BeforeStatus.Value(*params.UpdateBeforeStatus))
	}
	if params.UpdateFinishTime != nil {
		updateField = append(updateField, db_query.Q.FweOrder.FinishTime.Value(*params.UpdateFinishTime))
	}
	if params.UpdateOrderName != nil {
		updateField = append(updateField, db_query.Q.FweOrder.OrderName.Value(*params.UpdateOrderName))
	}
	if params.UpdateOrderDesc != nil {
		updateField = append(updateField, db_query.Q.FweOrder.OrderDesc.Value(*params.UpdateOrderDesc))
	}
	if params.UpdateBuyerID != nil {
		updateField = append(updateField, db_query.Q.FweOrder.BuyerID.Value(*params.UpdateBuyerID))
	}
	if params.UpdateBuyerExtra != nil {
		updateField = append(updateField, db_query.Q.FweOrder.BuyerExtra.Value(*params.UpdateBuyerExtra))
	}
	if params.UpdateSellerID != nil {
		updateField = append(updateField, db_query.Q.FweOrder.SellerID.Value(*params.UpdateSellerID))
	}
	if params.UpdateSellerExtra != nil {
		updateField = append(updateField, db_query.Q.FweOrder.SellerExtra.Value(*params.UpdateSellerExtra))
	}
	if params.UpdateSProviderID != nil {
		updateField = append(updateField, db_query.Q.FweOrder.ServiceProviderID.Value(*params.UpdateSProviderID))
	}
	if params.UpdateSProviderExtra != nil {
		updateField = append(updateField, db_query.Q.FweOrder.ServiceProviderExtra.Value(*params.UpdateSProviderExtra))
	}
	if params.UpdateTalentID != nil {
		updateField = append(updateField, db_query.Q.FweOrder.TalentID.Value(*params.UpdateTalentID))
	}
	if params.UpdateTalentExtra != nil {
		updateField = append(updateField, db_query.Q.FweOrder.TalentExtra.Value(*params.UpdateTalentExtra))
	}
	if params.UpdateProductID != nil {
		updateField = append(updateField, db_query.Q.FweOrder.ProductID.Value(*params.UpdateProductID))
	}
	if params.UpdateProductName != nil {
		updateField = append(updateField, db_query.Q.FweOrder.ProductName.Value(*params.UpdateProductName))
	}
	if params.UpdateSkuID != nil {
		updateField = append(updateField, db_query.Q.FweOrder.SkuID.Value(*params.UpdateSkuID))
	}
	if params.UpdateProductType != nil {
		updateField = append(updateField, db_query.Q.FweOrder.ProductType.Value(*params.UpdateProductType))
	}
	if params.UpdateProductUnitPrice != nil {
		updateField = append(updateField, db_query.Q.FweOrder.ProductUnitPrice.Value(*params.UpdateProductUnitPrice))
	}
	if params.UpdateProductQuantity != nil {
		tmp := *params.UpdateProductQuantity
		updateField = append(updateField, db_query.Q.FweOrder.ProductQuantity.Value(int32(tmp)))
	}
	if params.UpdateProductExtra != nil {
		updateField = append(updateField, db_query.Q.FweOrder.ProductExtra.Value(*params.UpdateProductExtra))
	}
	if params.UpdateTotalAmount != nil {
		updateField = append(updateField, db_query.Q.FweOrder.TotalAmount.Value(*params.UpdateTotalAmount))
	}
	if params.UpdateTotalPayAmount != nil {
		updateField = append(updateField, db_query.Q.FweOrder.TotalPayAmount.Value(*params.UpdateTotalPayAmount))
	}
	if params.UpdateSkuVersion != nil {
		updateField = append(updateField, db_query.Q.FweOrder.SkuVersion.Value(*params.UpdateSkuVersion))
	}
	if params.UpdateProductVersion != nil {
		updateField = append(updateField, db_query.Q.FweOrder.ProductVersion.Value(*params.UpdateProductVersion))
	}
	if params.UpdateTradeType != nil {
		updateField = append(updateField, db_query.Q.FweOrder.TradeType.Value(*params.UpdateTradeType))
	}
	if params.UpdateOrderSubStatus != nil {
		updateField = append(updateField, db_query.Q.FweOrder.OrderSubStatus.Value(*params.UpdateOrderSubStatus))
	}
	if params.UpdateProductDetail != nil {
		updateField = append(updateField, db_query.Q.FweOrder.ProductDetail.Value(*params.UpdateProductDetail))
	}
	if params.UpdatePurchasePlan != nil {
		updateField = append(updateField, db_query.Q.FweOrder.PurchasePlan.Value(*params.UpdatePurchasePlan))
	}

	if len(updateField) == 0 || orderID == "" {
		bizErr = errdef.NewParamsErr("update field list is 0")
		logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
		return
	}
	// 执行
	var result gen.ResultInfo
	result, err = db_query.Q.FweOrder.WithContext(ctx).
		Where(whereCond...).UpdateColumnSimple(updateField...)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
		logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
		return
	}
	if result.RowsAffected == 0 {
		bizErr = errdef.NewParamsErr("no column update")
		logs.CtxWarn(ctx, "[OrderService] err=%v", bizErr.Error())
		return nil
	}
	return nil
}

// Deprecated: please use tagService.UpdateTag
func (service *Order) UpdateOrderTag(ctx context.Context, orderID string, tagMap map[string]string) (bizErr *errdef.BizErr) {
	if len(tagMap) == 0 || orderID == "" {
		return
	}

	var err error
	var dataList []*db_model.FweOrderTag
	wdb := db_query.Use(caller.WriteDB(ctx))
	dataList, err = wdb.FweOrderTag.WithContext(ctx).
		Where(wdb.FweOrderTag.OrderID.Eq(orderID)).
		Limit(1).Find()
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "UpdateOrderTag")
		logs.CtxError(ctx, "[UpdateOrderTag] err=%s", bizErr.Error())
		return
	}
	if len(dataList) == 0 {
		bizErr = errdef.NewParamsErr(fmt.Sprintf("order_id=%s not find tag", orderID))
		logs.CtxError(ctx, "[UpdateOrderTag] err=%s", bizErr.Error())
		return
	}
	var tagStr string
	tagStr, _, bizErr = service.mergeColumnWithMap(ctx, dataList[0].Tag, tagMap)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderTag] err=%s", bizErr.Error())
		return
	}

	_, err = db_query.Q.FweOrderTag.WithContext(ctx).
		Where(db_query.Q.FweOrderTag.OrderID.Eq(orderID)).
		UpdateColumnSimple(
			db_query.Q.FweOrderTag.Tag.Value(tagStr),
		)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "UpdateOrderTag")
		return
	}

	return
}

func (service *Order) mergeColumnWithMap(ctx context.Context, columnValue *string, tagMap map[string]string) (newColumn string, newMap map[string]string, bizErr *errdef.BizErr) {
	if len(tagMap) == 0 {
		return
	}
	var err error
	oldMap := make(map[string]string)
	if columnValue != nil {
		err = utils.Unmarshal(*columnValue, &oldMap)
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "TagService")
			logs.CtxError(ctx, "[TagService] err=%s", bizErr.Error())
			return
		}
	}
	maps.MergeStrStr(oldMap, tagMap)
	newColumn, err = utils.Marshal(oldMap)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "TagService")
		logs.CtxError(ctx, "[TagService] err=%s", bizErr.Error())
		return
	}
	err = utils.Unmarshal(newColumn, &newMap)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "TagService")
		logs.CtxError(ctx, "[TagService] err=%s", bizErr.Error())
		return
	}
	return
}

func (service *Order) UpdateOrderExtraMarshal(ctx context.Context, orderID string, extra map[string]string) (bizErr *errdef.BizErr) {
	if len(extra) == 0 {
		return
	}

	var err error
	var dataList []*db_model.FweOrderTag
	var newExtra = make(map[string]string)

	// 查
	dataList, err = db_query.Q.FweOrderTag.WithContext(ctx).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where(db_query.Q.FweOrderTag.OrderID.Eq(orderID)).
		Find()
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "UpdateOrderExtraMarshal")
		return
	}
	if len(dataList) == 0 {
		bizErr = errdef.NewParamsErr(fmt.Sprintf("order_id=%s no tag", orderID))
		return
	}

	// 写
	if dataList[0].BizExtra != nil {
		var oldMap map[string]string
		err = sonic.UnmarshalString(*dataList[0].BizExtra, &oldMap)
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "UpdateOrderExtraMarshal")
			return
		}
		maps.MergeStrStr(newExtra, oldMap)
	}

	maps.MergeStrStr(newExtra, extra)
	var extraStr string
	extraStr, err = sonic.MarshalString(newExtra)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "UpdateOrderExtraMarshal")
		return
	}
	_, err = db_query.Q.FweOrderTag.WithContext(ctx).
		Where(db_query.Q.FweOrderTag.OrderID.Eq(orderID)).
		UpdateColumnSimple(db_query.Q.FweOrderTag.BizExtra.Value(extraStr))
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "UpdateOrderExtraMarshal")
		return
	}
	return
}

func (service *Order) UpdateOrderStatusStr(ctx context.Context, orderID string, originStatusStr, orderStatusStr string) (bizErr *errdef.BizErr) {
	logs.CtxInfo(ctx, "[UpdateOrderStatusStr] origin status=%v new status=%v", originStatusStr, orderStatusStr)
	status, err := strconv.ParseInt(orderStatusStr, 10, 64)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "UpdateOrderStatusStr")
		return bizErr
	}
	originStatus, _ := strconv.Atoi(originStatusStr)
	return service.UpdateOrder(ctx, orderID,
		&service_model.UpdateOrderParams{
			WhereOrderStatus:  []int32{int32(originStatus)},
			UpdateOrderStatus: conv.Int32Ptr(int32(status)),
		})
}

// BindUserOrder ... 谨慎使用
func (service *Order) BindUserOrder(ctx context.Context, uid int64, orderIDs []string) (bizErr *errdef.BizErr) {
	if len(orderIDs) == 0 {
		return
	}
	if len(orderIDs) > 50 {
		bizErr = errdef.NewParamsErr("can not support len(order_ids) over 50")
		return
	}

	err := db_query.Use(caller.WriteDB(ctx)).Transaction(func(tx *db_query.Query) error {
		// 1. 更新表的uid字段
		_, err := tx.FweOrder.WithContext(ctx).
			Where(tx.FweOrder.OrderID.In(orderIDs...)).
			UpdateColumnSimple(tx.FweOrder.UID.Value(uid))
		if err != nil {
			return err
		}

		// 2. 更新表buyer_extra的uid字段
		_, err = tx.FweOrder.WithContext(ctx).Where(tx.FweOrder.OrderID.In(orderIDs...)).
			Updates(map[string]interface{}{
				"buyer_extra": gorm.Expr(fmt.Sprintf("JSON_SET(buyer_extra, '$.uid', %d)", uid)),
			})

		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		bizErr = errdef.NewBizErrWithCode(errdef.MysqlException, err)
		return
	}

	return
}

func (service *Order) GetOrderByIdemID(ctx context.Context, idemID string, bizScene int32) (*db_model.FweOrder, *errdef.BizErr) {
	if idemID == "" {
		return nil, nil
	}
	fweOrder, err := db_query.Use(caller.WriteDB(ctx)).FweOrder.WithContext(ctx).
		Where(db_query.FweOrder.BizScene.Eq(bizScene)).
		Where(db_query.FweOrder.IdempotentID.Eq(idemID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if err != nil {
		logs.CtxWarn(ctx, "[Order-GetOrderByIdemID] db query error, err = %v", err)
		return nil, errdef.NewDBErr(err)
	}
	return fweOrder, nil
}

// CreateOrUpdateOrderSubject 查是否存在，如果存在则更新，如果不存在则创建,由order服务实现
func (service *Order) CreateOrUpdateOrderSubject(ctx context.Context, subjects []*fwe_trade_common.TradeSubjectInfo) *errdef.BizErr {
	// 过滤非空
	filters := slices.Filter(subjects, func(subject *fwe_trade_common.TradeSubjectInfo) bool {
		return subject != nil && int64(subject.SubjectType) != int64(0)
	}).([]*fwe_trade_common.TradeSubjectInfo)

	// 直接使用底层order 服务
	rpcReq := &OrderRpc.CreateOrUpdateSubjectReq{
		SubjectList: filters,
		Base:        base.NewBase(),
	}
	resp, err := orderClient.CreateOrUpdateSubject(ctx, rpcReq)
	if err != nil {
		logs.CtxError(ctx, "[Order-CreateOrUpdateOrderSubject] CreateOrUpdateSubject rpc error, err = %v", err)
		return errdef.NewBizErrWithCode(errdef.OrderRpcErr, err)
	}
	baseResp := resp.BaseResp
	if baseResp != nil && baseResp.StatusCode != int32(0) {
		msg := fmt.Sprintf("code:%v,message:%v", baseResp.StatusCode, baseResp.StatusMessage)
		logs.CtxError(ctx, "[Order-CreateOrUpdateOrderSubject] CreateOrUpdateSubject biz error, err = %v", msg)
		return errdef.NewRawErr(errdef.OrderRpcErr, msg)
	}
	return nil
}

func (service *Order) buildCreateOrderReq(ctx context.Context, order *service_model.Order) (output *OrderRpc.CreateOrderReq, bizErr *errdef.BizErr) {
	var (
		buyerInfo    *fwe_trade_common.TradeSubjectInfo
		sellerInfo   *fwe_trade_common.TradeSubjectInfo
		providerInfo *fwe_trade_common.TradeSubjectInfo
		talentInfo   *fwe_trade_common.TradeSubjectInfo
		operator     = &fwe_trade_common.OperatorInfo{
			OperatorID:   order.FweOrder.Creator,
			OperatorName: order.FweOrder.CreatorName,
		}
	)
	if order.FweOrder.BuyerExtra != nil {
		if buyerInfo, bizErr = packer.CommonTradeSubjectDeserialize(order.FweOrder.BuyerID, *order.FweOrder.BuyerExtra); bizErr != nil {
			logs.CtxError(ctx, "[buildCreateOrderReq] err=%s", bizErr.Error())
			return
		}
	}
	if order.FweOrder.SellerExtra != nil {
		if sellerInfo, bizErr = packer.CommonTradeSubjectDeserialize(order.FweOrder.SellerID, *order.FweOrder.SellerExtra); bizErr != nil {
			logs.CtxError(ctx, "[buildCreateOrderReq] err=%s", bizErr.Error())
			return
		}
	}
	if order.FweOrder.ServiceProviderExtra != nil {
		if providerInfo, bizErr = packer.CommonTradeSubjectDeserialize(order.FweOrder.ServiceProviderID, *order.FweOrder.ServiceProviderExtra); bizErr != nil {
			logs.CtxError(ctx, "[buildCreateOrderReq] err=%s", bizErr.Error())
			return
		}
	}
	if order.FweOrder.TalentExtra != nil {
		if talentInfo, bizErr = packer.CommonTradeSubjectDeserialize(order.FweOrder.TalentID, *order.FweOrder.TalentExtra); talentInfo != nil {
			logs.CtxError(ctx, "[buildCreateOrderReq] err=%s", bizErr.Error())
			return
		}
	}
	output = &OrderRpc.CreateOrderReq{
		OrderData:    packer.OrderDB2BaseInfo(ctx, order.FweOrder),
		ProductInfo:  packer.OrderDB2ProductInfo(ctx, order.FweOrder),
		BuyerInfo:    buyerInfo,
		SellerInfo:   sellerInfo,
		ProviderInfo: providerInfo,
		TalentInfo:   talentInfo,
		FinanceData:  packer.FinanceDBList2OrderThriftList(order.FinanceList),
		TagMap:       order.TagMap,
		ExtraMap:     order.BizExtra,
		Operator:     operator,
	}
	return
}

func (service *Order) QueryOneSuccessPay(ctx context.Context, orderId string, tradeTypeList []string, needSubsidyAmount int64, financeOrderID string, inputMerchantID string) (*fwe_trade_common.FinancePay, *errdef.BizErr) {
	// 查询资金模型
	req := &OrderRpc.QueryFinanceModelByOrderIDReq{
		OrderID:      orderId,
		ReadStrategy: OrderRpc.ReadStrategyPtr(OrderRpc.ReadStrategy_ReadMaster),
		NeedRefund:   nil,
		NeedSettle:   conv.BoolPtr(true),
		NeedWithdraw: nil,
		NeedTransfer: nil,
		Base:         nil,
	}
	financeModel, bizErr := service.QueryFinanceModel(ctx, req)
	if bizErr != nil {
		logs.CtxError(ctx, "[Order-QueryOneSuccessPay] QueryFinanceModel error, err = %v", bizErr)
		return nil, bizErr
	}
	var (
		financePays       []*fwe_trade_common.FinancePay    // 当前资金单下的所有有效支付单
		financeSettles    []*fwe_trade_common.FinanceSettle // 当前资金单下的所有有效分账单
		checkStatus       = []fwe_trade_common.CommonStatus{fwe_trade_common.CommonStatus_Success}
		financePayListMap = make(map[string][]*fwe_trade_common.FinancePay) // key: financeOrderID
	)
	// 预处理:paySettleListMap
	paySettleListMap := gslice.GroupBy(financeModel.GetSettleList(), func(settleItem *fwe_trade_common.FinanceSettle) string {
		return settleItem.PayOrderNo
	})
	// financePayListMap
	for _, payUnionOrder := range financeModel.PayUnionOrderList {
		financePayListMap[payUnionOrder.OutID] = payUnionOrder.PayList
	}
	// 过滤支付方式，已支付
	financePays = gslice.Filter(financePayListMap[financeOrderID], func(payItem *fwe_trade_common.FinancePay) bool {
		return slices.Contains(tradeTypeList, payItem.TradeType) && payItem.Status == fwe_trade_common.CommonStatus_Success
	})
	// 没有有效支付，不需要补贴
	if len(financePays) == 0 {
		bizErr = errdef.NewRawErr(errdef.DataErr, "dont have a effective payOrder")
		logs.CtxError(ctx, "[Order-QueryOneSuccessPay] filter payOrders error, err = %v", bizErr.Error())
		return nil, bizErr
	}
	// 找分账单
	for _, financePay := range financePays {
		for payOrderNoKey, financeSettlesValues := range paySettleListMap {
			if financePay.PayOrderNo == payOrderNoKey {
				financeSettles = append(financeSettles, financeSettlesValues...)
			}
		}
	}
	// 过滤带有补贴信息的分账单，进行中和已完成
	financeSettles = gslice.Filter(financeSettles, func(settleItem *fwe_trade_common.FinanceSettle) bool {
		return settleItem.SubsidyAmount > 0 && slices.Contains(checkStatus, settleItem.Status)
	})
	// 存在有效分账单
	if len(financeSettles) >= 1 {
		for _, financeSettle := range financeSettles {
			find, i := slices.Find(financePays, func(financePay *fwe_trade_common.FinancePay) bool {
				return financePay.PayOrderNo == financeSettle.PayOrderNo
			})
			if i < 0 || find == nil {
				bizErr = errdef.NewRawErr(errdef.DirtyDataException, "payOrder data error")
				logs.CtxError(ctx, "[Order-QueryOneSuccessPay] error ,err = %v ", bizErr.Error())
				return nil, bizErr
			}
			pay := find.(*fwe_trade_common.FinancePay)
			if slices.Contains(consts.YztOfflineTradeType, pay.TradeType) {
				financeSettle.SubsidyAmount -= pay.PayAmount
			}
			financeSettle.SubsidyAmount -= pay.ChargeFee
			// 没在现金支付单上补贴
			if financeSettle.SubsidyAmount == 0 {
				continue
			}
			if financeSettle.SubsidyAmount == needSubsidyAmount { // 强校验 实际case: (pos补贴|云直通离线支付|null) + 贷款 == financeSettle.SubsidyAmount
				logs.CtxInfo(ctx, "[Order-QueryOneSuccessPay] has finish subsidy in settleOrder,dont need subsidy again,its = %v", tools.GetLogStr(financeSettles))
				return nil, nil
			}
			return nil, errdef.NewRawErr(errdef.DataErr, "settleOrder SubsidyAmount data error")
		}
	}
	// 不存在有效补贴分账单，选择目标资金单的第一笔成功支付单进行补贴
	if len(financeSettles) == 0 {
		for _, financePay := range financePays {
			if financePay.Status == fwe_trade_common.CommonStatus_Success {
				if !strings.IsBlank(inputMerchantID) && inputMerchantID != financePay.MerchantID { // 优先找业务指定的一级户的支付单
					continue
				}
				return financePay, nil
			}
		}
		return nil, errdef.NewRawErr(errdef.DataErr, "dont have a effective payOrder")
	}
	bizErr = errdef.NewRawErr(errdef.DataErr, "settleOrder SubsidyAmount data error")
	logs.CtxError(ctx, "[Order-QueryOneSuccessPay] error, err", bizErr.Error())
	return nil, bizErr
}

func (service *Order) QueryFinanceModel(ctx context.Context, req *OrderRpc.QueryFinanceModelByOrderIDReq) (*fwe_trade_common.OrderFinanceModel, *errdef.BizErr) {
	resp, err := orderClient.QueryFinanceModelByOrderID(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[Order-QueryFinanceModel] QueryFinanceModelByOrderID rpc error, err = %v", err)
		return nil, errdef.NewRawErr(errdef.OrderRpcErr, err.Error())
	}
	baseResp := resp.BaseResp
	if baseResp != nil && baseResp.StatusCode != int32(0) {
		bizErr := errdef.NewRawErr(errdef.OrderRpcErr, baseResp.StatusMessage)
		logs.CtxError(ctx, "[Order-QueryFinanceModel] QueryFinanceModelByOrderID biz error, err = %v", bizErr)
		return nil, bizErr
	}
	return resp.OrderFinanceModel, nil
}
