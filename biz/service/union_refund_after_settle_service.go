package service

import (
	"context"

	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UnionRefundAfterSettleService struct {
}

func NewUnionRefundAfterSettleService() *UnionRefundAfterSettleService {
	return &UnionRefundAfterSettleService{}
}

func (s *UnionRefundAfterSettleService) UnionRefundAfterSettle(ctx context.Context, param *service_model.UnionRefundAfterSettleParam) (string, *errdef.BizErr) {

	var (
		refundParam         = param.Req
		financeOrderService = NewFinanceOrderService()
	)
	// 生成资金单
	baseOrder := &service_model.OrderBaseParam{
		Identity:  param.Req.Identity,
		OrderID:   param.OrderID,
		OrderName: param.OrderName,
	}
	financeInfo := &fwe_trade_common.FinanceInfo{
		FinanceOrderID:   refundParam.OutID,
		FinanceOrderType: param.RefundType,
		PayStatus:        fwe_trade_common.FinanceStatus_NotHandle,
		Amount:           param.RefundAmount,
		TradeCategory:    fwe_trade_common.TradeCategory_Refund,
		FeeRecordID:      param.FeeRecordID,
		FeeItemList:      param.FeeItemList,
		DeductItemList:   param.DeductItemList,
	}

	bizErr := financeOrderService.CreateV2(ctx, baseOrder, []*fwe_trade_common.FinanceInfo{financeInfo})
	if bizErr != nil {
		logs.CtxWarn(ctx, "[MergeRefundAfterSettle] CreateV2 error, err = %v", bizErr.Error())
		return "", bizErr
	}
	// 调用payment 退款
	mergeRefundID, bizErr := NewTradePayment().MergeRefundAfterSettle(ctx, refundParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[MergeRefundAfterSettle] MergeRefundAfterSettle error, err = %v", bizErr.Error())
		return "", bizErr
	}
	// 结束
	return mergeRefundID, nil
}
