package service

import (
	"code.byted.org/gopkg/gorm"
	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	common "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"errors"
)

type SplitInfoService struct{}

func NewSplitInfoService() *SplitInfoService {
	return &SplitInfoService{}
}

func (s *SplitInfoService) MCreateSplitInfo(ctx context.Context, orderID string, info *common.TradeSpiltInfo) *errdef.BizErr {
	if info == nil || len(info.Detail) == 0 {
		return nil
	}

	// 校验分账信息
	checkErr := s.checkTradeSplitInfo(info)
	if checkErr != nil {
		return checkErr
	}

	// 库里存在直接返回错误
	if blitzInfo, bizErr := s.GetSplitInfosByOrderID(ctx, orderID); bizErr != nil {
		return bizErr
	} else if blitzInfo != nil && len(blitzInfo.Detail) > 0 {
		return errdef.NewParamsErr("该订单已存在分账方信息，不可重新创建")
	}

	list := packer.SplitInfoService2DB(orderID, info)
	for _, v := range list {
		err := db_query.FOrderSplitInfo.WithContext(ctx).
			Omit(db_query.FOrderSplitInfo.CreatedTime, db_query.FOrderSplitInfo.UpdatedTime).
			Create(v)
		if err != nil {
			return errdef.NewBizErrWithCode(errdef.MysqlException, err)
		}
	}
	//if err := db_query.FOrderSplitInfo.WithContext(ctx).
	//	Omit(db_query.FOrderSplitInfo.CreatedTime, db_query.FOrderSplitInfo.UpdatedTime).
	//	CreateInBatches(list, 100); err != nil {
	//	return errdef.NewBizErrWithCode(errdef.MysqlException, err)
	//}

	return nil
}

func (s *SplitInfoService) checkTradeSplitInfo(splitInfo *common.TradeSpiltInfo) *errdef.BizErr {
	if splitInfo.SplitMethod == common.SplitMethod_ByScale {
		// 校验百分比等于100% 存储为万分位 10000
		total := int64(0)
		for _, info := range splitInfo.Detail {
			if info.Scale <= 0 {
				return errdef.NewParamsErr("分账比例不可小于等于0")
			}
			total += info.Scale
		}

		if total != 10000 {
			return errdef.NewParamsErr("分账方分账比例之和不等于100%")
		}
	} else if splitInfo.SplitMethod == common.SplitMethod_ByAmount {
		for _, info := range splitInfo.Detail {
			if info.Amount <= 0 {
				return errdef.NewParamsErr("分账金额不可小于等于0")
			}
		}
	}
	return nil
}

func (s *SplitInfoService) GetSplitInfosByOrderID(ctx context.Context, orderID string) (*common.TradeSpiltInfo, *errdef.BizErr) {
	splitInfos, err := db_query.FOrderSplitInfo.WithContext(ctx).
		Where(db_query.FOrderSplitInfo.OrderID.Eq(orderID)).Find()

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	if err != nil {
		return nil, errdef.NewBizErrWithCode(errdef.MysqlException, err)
	}

	return packer.SplitInfoDB2Service(splitInfos), nil
}
