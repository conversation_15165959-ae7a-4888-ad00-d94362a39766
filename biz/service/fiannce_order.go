package service

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/client"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"gorm.io/gen"
	"gorm.io/gen/field"

	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order/tradeorderservice"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type FinanceOrderService struct{}

var (
	orderClient tradeorderservice.Client
)

func init() {
	orderClient = tradeorderservice.MustNewClient("motor.fwe_trade.order",
		client.WithMiddleware(middleware.LogMiddleware))
}

func NewFinanceOrderService() *FinanceOrderService {
	return &FinanceOrderService{}
}

func (s *FinanceOrderService) Create(ctx context.Context, tradeType CommonConsts.OrderTradeType,
	req *service_model.CreateFinanceOrderReq, finances ...*fwe_trade_common.FinanceInfo) (bizErr *errdef.BizErr) {

	if req == nil {
		bizErr = errdef.NewParamsErr("req is nil")
		return
	}

	if len(finances) == 0 {
		return
	}

	var fType []CommonConsts.FinanceOrderType
	switch tradeType {
	case CommonConsts.OrderEarnestFinal:
		fType = append(fType, CommonConsts.FinanceEarnest, CommonConsts.FinanceFinal)
	case CommonConsts.OrderTradeFull:
		fType = append(fType, CommonConsts.FinanceTotal)
	default:
		bizErr = errdef.NewParamsErr(fmt.Sprintf("trade type %d not found", tradeType))
		return
	}

	var validFinances []*db_model.FFinanceOrder
	for _, fOrder := range finances {
		for _, ft := range fType {
			if fOrder.FinanceOrderType == ft.Value() {
				vf := &db_model.FFinanceOrder{
					FinanceOrderID:   utils.MakeFinanceOrderIDTool(req.OrderID, fOrder.FinanceOrderType),
					FinanceOrderType: fOrder.FinanceOrderType,
					OrderID:          req.OrderID,
					OrderName:        req.OrderName,
					TradeType:        req.FOrderType2FTradeTypeMap[fOrder.FinanceOrderType],
					TradeCategory:    int32(fOrder.TradeCategory),
					TenantType:       int32(req.TenantType),
					BizScene:         req.BizScene,
					AppID:            req.AppID,
					MerchantID:       req.MerchantID,
					Mid:              req.MID,
					Amount:           fOrder.Amount,
					Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
				}
				validFinances = append(validFinances, vf)
			}
		}
	}

	return s.CreateFinanceOrderList(ctx, validFinances)
}

// Deprecated: 后续请使用：CreateV2
func (s *FinanceOrderService) CreateFinanceOrderList(ctx context.Context, financeList []*db_model.FFinanceOrder) (bizErr *errdef.BizErr) {
	if len(financeList) == 0 {
		return nil
	}
	err := db_query.Q.FFinanceOrder.WithContext(ctx).
		Omit(db_query.FFinanceOrder.CreateTime, db_query.FFinanceOrder.UpdateTime).
		Create(financeList...)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
		logs.CtxError(ctx, "[OrderService] err=%v", bizErr.Error())
		return
	}
	return nil
}

// Deprecated: 后续请使用：UpdateV2
func (s *FinanceOrderService) UpdateOrderFinance(ctx context.Context, financeOrderID string, params *service_model.UpdateFinanceParams) (bizErr *errdef.BizErr) {
	if params == nil {
		return nil
	}
	// 拼接条件字段值
	var whereCond []gen.Condition
	whereCond = append(whereCond, db_query.Q.FFinanceOrder.FinanceOrderID.Eq(financeOrderID))
	// 拼接更新字段值
	var updateField []field.AssignExpr
	if params.UpdateAmount != nil {
		updateField = append(updateField, db_query.Q.FFinanceOrder.Amount.Value(*params.UpdateAmount))
	}
	if params.UpdateFinanceStatus != nil {
		updateField = append(updateField, db_query.Q.FFinanceOrder.Status.Value(*params.UpdateFinanceStatus))
	}
	if params.UpdateLoanAmount != nil {
		updateField = append(updateField, db_query.Q.FFinanceOrder.LoanAmount.Value(*params.UpdateLoanAmount))
	}
	if params.UpdateTradeType != nil {
		updateField = append(updateField, db_query.Q.FFinanceOrder.TradeType.Value(*params.UpdateTradeType))
	}
	if params.UpdateMerchantID != nil {
		updateField = append(updateField, db_query.Q.FFinanceOrder.MerchantID.Value(*params.UpdateMerchantID))
	}
	if params.UpdateAppID != nil {
		updateField = append(updateField, db_query.Q.FFinanceOrder.AppID.Value(*params.UpdateAppID))
	}
	if params.UpdateMid != nil {
		updateField = append(updateField, db_query.Q.FFinanceOrder.Mid.Value(*params.UpdateMid))
	}
	if params.UpdateFeeItemDetail != nil {
		updateField = append(updateField, db_query.Q.FFinanceOrder.FeeItemDetail.Value(*params.UpdateFeeItemDetail))
	}

	result, err := db_query.Q.FFinanceOrder.WithContext(ctx).
		Where(whereCond...).UpdateColumnSimple(updateField...)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
		logs.CtxError(ctx, "[FinanceService] err=%v", bizErr.Error())
		return
	}
	if result.RowsAffected == 0 {
		bizErr = errdef.NewParamsErr("no column update")
		logs.CtxWarn(ctx, "[FinanceService] err=%v", bizErr.Error())
		return nil
	}
	return nil
}

func (s *FinanceOrderService) CreateV2(ctx context.Context, baseParam *service_model.OrderBaseParam, financeInfoList []*fwe_trade_common.FinanceInfo) *errdef.BizErr {
	var (
		financeOrderList []*order.FinanceOrderData
	)
	for _, v := range financeInfoList {
		financeOrderList = append(financeOrderList, &order.FinanceOrderData{
			Identity:                baseParam.Identity,
			FinanceOrderID:          v.FinanceOrderID,
			FinanceOrderType:        v.FinanceOrderType,
			TradeCategory:           v.TradeCategory,
			FeeRecordID:             conv.StringPtr(v.FeeRecordID),
			OrderID:                 baseParam.OrderID,
			FulfillID:               baseParam.FulfillID,
			OrderName:               &baseParam.OrderName,
			Amount:                  &v.Amount,
			LoanAmount:              &v.LoanAmount,
			PlatformPromotionAmount: &v.PlatformPromotionAmount,
			Status:                  &v.PayStatus,
			FinishTime:              &v.FinishTime,
			FeeItemDetail:           v.FeeItemList,
			DeductItemDetail:        v.DeductItemList,
		})
	}
	req := &order.CreateFinanceOrderReq{
		FinanceOrderList: financeOrderList,
		Base:             base.NewBase(),
	}
	rsp, err := orderClient.CreateFinanceOrder(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[FinanceOrderService.CreateV2] create failed, err=%+v", err)
		return errdef.NewBizErr(errdef.OrderRpcErr, err, err.Error())
	}
	if rsp.BaseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[FinanceOrderService.CreateV2] create failed, msg=%s", rsp.BaseResp.StatusMessage)
		return errdef.NewRawErr(errdef.OrderRpcErr, rsp.BaseResp.StatusMessage)
	}
	return nil
}

func (s *FinanceOrderService) UpdateV2(ctx context.Context, financeOrderID string, params *service_model.UpdateFinanceParams) *errdef.BizErr {
	var (
		financeOrder = new(order.FinanceOrderData)
	)
	if params.UpdateAmount != nil {
		financeOrder.Amount = params.UpdateAmount
	}
	if params.UpdateLoanAmount != nil {
		financeOrder.LoanAmount = params.UpdateLoanAmount
	}
	if params.UpdateProcessAmount != nil {
		financeOrder.ProcessAmount = params.UpdateProcessAmount
	}
	if params.UpdateOfflineLoanAmount != nil {
		financeOrder.OfflineLoanAmount = params.UpdateOfflineLoanAmount
	}
	if params.UpdatePlatformPromotionAmount != nil {
		financeOrder.PlatformPromotionAmount = params.UpdatePlatformPromotionAmount
	}
	if params.UpdatePlatformPromotionDetail != nil {
		financeOrder.PlatformPromotionDetail = params.UpdatePlatformPromotionDetail
	}
	if params.UpdateFinishTime != nil {
		financeOrder.FinishTime = params.UpdateFinishTime
	}
	if params.UpdateFinanceStatus != nil {
		status := fwe_trade_common.FinanceStatus(*params.UpdateFinanceStatus)
		financeOrder.Status = &status
	}
	if params.UpdateFeeItemDetail != nil {
		var feeItems []*fwe_trade_common.FeeItem
		err := utils.Unmarshal(*params.UpdateFeeItemDetail, &feeItems)
		if err != nil {
			logs.CtxError(ctx, "[FinanceOrderService.UpdateV2] unmarshal fee_items failed, err=%+v", err)
			return errdef.NewBizErr(errdef.DataErr, err, "unmarshal failed")
		}
		financeOrder.FeeItemDetail = feeItems
	}
	if params.UpdateDeductFeeItemDetail != nil {
		var tempFeeItems []*fwe_trade_common.FeeItem
		err := utils.Unmarshal(*params.UpdateDeductFeeItemDetail, &tempFeeItems)
		if err != nil {
			logs.CtxError(ctx, "[FinanceOrderService.UpdateV2] unmarshal fee_items failed, err=%+v", err)
			return errdef.NewBizErr(errdef.DataErr, err, "unmarshal failed")
		}
		financeOrder.DeductItemDetail = tempFeeItems
	}

	req := &order.UpdateFinanceOrderReq{
		FinanceOrderID: financeOrderID,
		FinanceOrder:   financeOrder,
		Base:           base.NewBase(),
	}
	rsp, err := orderClient.UpdateFinanceOrder(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[FinanceOrderService.UpdateV2] update failed, err=%+v", err)
		return errdef.NewBizErr(errdef.OrderRpcErr, err, err.Error())
	}
	if rsp.BaseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[FinanceOrderService.UpdateV2] update failed, msg=%s", rsp.BaseResp.StatusMessage)
		return errdef.NewRawErr(errdef.OrderRpcErr, rsp.BaseResp.StatusMessage)
	}
	return nil
}
