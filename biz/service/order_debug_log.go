package service

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
)

type FweOrderDebugLog struct{}

func NewFweOrderDebugLog() *FweOrderDebugLog {
	return &FweOrderDebugLog{}
}

func (service *FweOrderDebugLog) WriteOrderLog(ctx context.Context, debugLog *model.OrderDebugLog) (bizErr *errdef.BizErr) {
	var err error
	err = db_query.Q.WithContext(ctx).FweOrderDebugLog.
		Create(packer.PackOrderDebugLogService2DB(debugLog))
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
		return
	}

	return nil
}

//func (service *FweOrderDebugLog) GetOrderLogList(ctx context.Context, orderID string) (dataList []*model.OrderDebugLog, bizErr *errdef.BizErr) {
//	var err error
//	debugList, err = db_query.Q.WithContext(ctx).FweOrderDebugLog.
//		Where(db_query.Q.FweOrderDebugLog.OrderID.Eq(orderID)).
//		Limit(100).Find()
//	if err != nil {
//		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
//		return
//	}
//	return
//}
