package service

import (
	"context"

	"code.byted.org/kite/kitex/client"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_invoice/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_invoice/core/invoiceservice"
)

var invoiceClient invoiceservice.Client

func init() {
	invoiceClient = invoiceservice.MustNewClient("motor.fwe_invoice.core",
		client.WithMiddleware(middleware.LogMiddleware))
}

type InvoiceService struct{}

func NewInvoiceService() *InvoiceService {
	return &InvoiceService{}
}

func (s *InvoiceService) Invoice(ctx context.Context, req *core.InvoiceReq) (invoiceOrderID string, bizErr *errdef.BizErr) {
	rpcRsp, err := invoiceClient.Invoice(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.InvoiceRpcErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewInvoiceRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}

	return rpcRsp.InvoiceOrderID, nil
}
