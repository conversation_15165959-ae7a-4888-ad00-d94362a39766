package service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type UnionWithdrawService struct {
}

func NewUnionWithdrawService() *UnionWithdrawService {
	return &UnionWithdrawService{}
}

func (s *UnionWithdrawService) WithdrawDeposit(ctx context.Context, param *service_model.UnionWithdrawParam) (string, *errdef.BizErr) {

	var (
		withdrawReq         = param.WithdrawReq
		financeOrderService = NewFinanceOrderService()
	)
	// 生成资金单
	baseOrder := &service_model.OrderBaseParam{
		Identity:  param.WithdrawReq.Identity,
		OrderID:   param.OrderID,
		OrderName: param.OrderName,
	}
	financeInfo := &fwe_trade_common.FinanceInfo{
		FinanceOrderID:   utils.MakeFinanceOrderIDTool(param.OrderID, param.FinanceType),
		FinanceOrderType: param.FinanceType,
		PayStatus:        fwe_trade_common.FinanceStatus_NotHandle,
		Amount:           param.WithdrawAmount,
		TradeCategory:    fwe_trade_common.TradeCategory_Withdraw,
		FeeItemList:      param.FeeItemList,
	}
	bizErr := financeOrderService.CreateV2(ctx, baseOrder, []*fwe_trade_common.FinanceInfo{financeInfo})
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionWithdrawService-UnionSettle] CreateV2 error, err = %v", bizErr.Error())
		return "", bizErr
	}
	// 调用payment 出款
	withdrawNo, bizErr := NewTradePayment().WithdrawDeposit(ctx, withdrawReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionWithdrawService-UnionSettle] MergeSettleV2 error, err = %v", bizErr.Error())
		return "", bizErr
	}
	// 结束
	return withdrawNo, nil
}
