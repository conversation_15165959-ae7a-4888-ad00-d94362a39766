package service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type UnionRefundService struct {
}

func NewUnionRefundService() *UnionRefundService {
	return &UnionRefundService{}
}

func (s *UnionRefundService) UnionRefund(ctx context.Context, param *service_model.UnionRefundParam) (string, *errdef.BizErr) {

	var (
		refundParam         = param.MergeRefundReq
		financeOrderService = NewFinanceOrderService()
	)
	// 生成资金单
	baseOrder := &service_model.OrderBaseParam{
		Identity:  param.MergeRefundReq.Identity,
		OrderID:   param.OrderID,
		OrderName: param.OrderName,
	}
	financeInfo := &fwe_trade_common.FinanceInfo{
		FinanceOrderID:   refundParam.OutID,
		FinanceOrderType: param.RefundType,
		PayStatus:        fwe_trade_common.FinanceStatus_NotHandle,
		Amount:           param.RefundAmount,
		TradeCategory:    fwe_trade_common.TradeCategory_Refund,
		FeeRecordID:      param.FeeRecordID,
		FeeItemList:      param.FeeItemList,
		DeductItemList:   param.DeductItemList,
	}

	bizErr := financeOrderService.CreateV2(ctx, baseOrder, []*fwe_trade_common.FinanceInfo{financeInfo})
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionRefundService-UnionRefund] CreateV2 error, err = %v", bizErr.Error())
		return "", bizErr
	}
	// 调用payment 退款
	mergeRefundID, bizErr := NewTradePayment().MergeRefund(ctx, refundParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionRefundService-UnionRefund] MergeRefund error, err = %v", bizErr.Error())
		return "", bizErr
	}
	// 结束
	return mergeRefundID, nil
}
