package service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metrics"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/base"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/motor/trade/audit"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/motor/trade/audit/motortradeauditservice"
	"context"
	"errors"
	"strconv"
)

var (
	auditClient motortradeauditservice.Client
)

func init() {
	auditClient = motortradeauditservice.MustNewClient("motor.trade.audit",
		client.WithMiddleware(middleware.LogMiddleware))
}

type AuditService struct{}

func NewAuditService() *AuditService {
	return &AuditService{}
}

func (s *AuditService) ApplyAudit(ctx context.Context, req *audit.ApplyAuditReq) *errdef.BizErr {
	rsp, err := auditClient.ApplyAudit(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[ApplyAudit] rpc failed, err=%+v", err)
		return errdef.NewBizErrWithCode(errdef.AuditRpcErr, err)
	}

	if rsp == nil || rsp.BaseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[ApplyAudit] rpc result not ok, rsp=%s", tools.GetLogStr(rsp))
		return errdef.NewRawErr(errdef.AuditRpcErr, "rpc result status error")
	}

	tag := metrics.Tag(consts.TagAuditSystemId, strconv.Itoa(int(req.SystemId)))
	utils.EmitCounter(consts.MetricsAuditStart, 1, tag)

	return nil
}

func (s *AuditService) ApplyAuditWithAuditNum(ctx context.Context, req *audit.ApplyAuditReq) (int64, *errdef.BizErr) {
	rsp, err := auditClient.ApplyAudit(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[ApplyAudit] rpc failed, err=%+v", err)
		return 0, errdef.NewBizErrWithCode(errdef.AuditRpcErr, err)
	}

	if rsp == nil || rsp.BaseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[ApplyAudit] rpc result not ok, rsp=%s", tools.GetLogStr(rsp))
		return 0, errdef.NewRawErr(errdef.AuditRpcErr, "rpc result status error")
	}

	tag := metrics.Tag(consts.TagAuditSystemId, strconv.Itoa(int(req.SystemId)))
	utils.EmitCounter(consts.MetricsAuditStart, 1, tag)

	return rsp.AuditNum, nil
}

func (s *AuditService) GetAuditDetail(ctx context.Context, tenant int32, auditNum int64) (*audit.AuditApplyInfo, error) {
	req := &audit.AuditDetailReq{
		Tenant:   tenant,
		AuditNum: auditNum,
		Base:     base.NewBase(),
	}
	rsp, err := auditClient.AuditDetail(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[AuditService.getAuditDetail] rpc failed, err=%+v", err)
		return nil, err
	}
	if rsp == nil || rsp.BaseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[AuditService.getAuditDetail] rpc status not ok, err=%+v", err)
		return nil, errors.New("status is not zero")
	}
	if rsp.AuditApplyInfo == nil {
		logs.CtxError(ctx, "[AuditService.getAuditDetail] rpc status not ok, err=%+v", err)
		return nil, errors.New("detail is nil")
	}
	return rsp.AuditApplyInfo, nil
}
