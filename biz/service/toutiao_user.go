package service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/overpass/toutiao_user_hash_mobile/rpc/toutiao_user_hash_mobile"
	"context"
)

// 中台云 HashMobile服务 https://bytedance.feishu.cn/wiki/wikcn3ve68HFseUJ0rctvMMKX0e#NssZOB

type ToutiaoUserService struct {
}

func NewToutiaoUserService() *ToutiaoUserService {
	return &ToutiaoUserService{}
}

func (s *ToutiaoUserService) GetMobileIdByPhone(ctx context.Context, phoneID string) (mobileID int64, bizErr *errdef.BizErr) {
	resp, err := toutiao_user_hash_mobile.GetMobileIdByMobile(ctx, phoneID)
	if err != nil {
		logs.CtxWarn(ctx, "[ToutiaoUserService] GetMobileIdByPhone failed, err is %+v", err)
		return 0, errdef.NewRpcErr(err)
	}

	return resp.GetMobileId(), nil
}
