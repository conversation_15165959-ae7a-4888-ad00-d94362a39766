package service

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop/fweaccountshopservice"
)

var motorFweAccountShop fweaccountshopservice.Client

func init() {
	motorFweAccountShop = fweaccountshopservice.MustNewClient("motor.fwe_account.shop",
		client.WithMiddleware(middleware.LogMiddleware))
}

type AccountShop struct{}

func NewAccountShop() *AccountShop {
	return &AccountShop{}
}

func (s *AccountShop) GetShopInfo(ctx context.Context, fweAccountID string) (info *shop.ShopInfo, bizErr *errdef.BizErr) {
	req := &shop.MGetShopReq{
		FweAccountIDList: []string{fweAccountID},
	}
	shopResp, err := motorFweAccountShop.MGetShop(ctx, req)
	if err != nil {
		bizErr = errdef.NewRpcErr(err)
		logs.CtxError(ctx, "[GetShopInfo] err=%s", bizErr.Error())
		return
	}
	if shopResp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewRawErr(errdef.AccountShopErr, shopResp.BaseResp.StatusMessage)
		logs.CtxError(ctx, "[MGetFinanceAccount] err=%v", bizErr.Error())
		return
	}
	res := shopResp.ShopInfo[fweAccountID]
	if res == nil {
		msg := fmt.Sprintf("motor_fwe_account_shop biz error, err = %+v", "not query dada")
		return nil, errdef.NewRawErr(errdef.AccountShopErr, msg)
	}
	return res, nil
}

func (s *AccountShop) MGetShopInfo(ctx context.Context, fweAccountIDs []string) (shopMap map[string]*shop.ShopInfo, bizErr *errdef.BizErr) {
	req := &shop.MGetShopReq{
		FweAccountIDList: fweAccountIDs,
	}
	shopResp, err := motorFweAccountShop.MGetShop(ctx, req)
	if err != nil {
		bizErr = errdef.NewRpcErr(err)
		logs.CtxError(ctx, "[MGetShopInfo] err=%s", bizErr.Error())
		return
	}
	if shopResp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewRawErr(errdef.AccountShopErr, shopResp.BaseResp.StatusMessage)
		logs.CtxError(ctx, "[MGetShopInfo] err=%v", bizErr.Error())
		return
	}
	return shopResp.ShopInfo, nil
}

func (s *AccountShop) GetFinanceAccountOne(ctx context.Context, shopID, merchantID string) (data *shop.FinanceAccount, bizErr *errdef.BizErr) {
	var dataMap map[string]*shop.FinanceAccount
	dataMap, _, bizErr = s.MGetFinanceAccount(ctx, []string{shopID}, merchantID)
	if bizErr != nil {
		logs.CtxError(ctx, "[GetFinanceAccountOne] err=%v", bizErr.Error())
		return
	}
	if dataMap[shopID] == nil {
		bizErr = errdef.NewRawErr(errdef.FinanceAccountNotExist, fmt.Sprintf("shop_id=%s no data", shopID))
		logs.CtxError(ctx, "[GetFinanceAccountOne] err=%v", bizErr.Error())
		return
	}
	data = dataMap[shopID]
	return
}

func (s *AccountShop) MGetFinanceAccount(ctx context.Context, fweShopIDs []string, merchantID string) (dataMap map[string]*shop.FinanceAccount, missIDs []string, bizErr *errdef.BizErr) {
	req := &shop.MGetFinanceAccountReq{
		FweAccountIds: fweShopIDs,
		MerchantID:    merchantID,
	}

	if len(fweShopIDs) == 0 {
		return nil, nil, nil
	}

	rpcRsp, err := motorFweAccountShop.MGetFinanceAccount(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.AccountShopErr, err, "")
		logs.CtxError(ctx, "[MGetFinanceAccount] err=%v", bizErr.Error())
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewRawErr(errdef.AccountShopErr, rpcRsp.BaseResp.StatusMessage)
		logs.CtxError(ctx, "[MGetFinanceAccount] err=%v", bizErr.Error())
		return
	}
	dataMap = rpcRsp.FinanceAccountMap
	missIDs = rpcRsp.MissFweAccountIds
	return
}

func (s *AccountShop) QuerySubMerchantInfo(ctx context.Context, merchantID, fweAccountID string, tradeChannel shop.TradeChannel) (*shop.SubMerchantInfo, *errdef.BizErr) {
	req := &shop.QuerySubMerchantInfoReq{
		MerchantID:   merchantID,
		FweAccountID: fweAccountID,
		TradeChannel: tradeChannel,
		Base:         base.NewBase(),
	}

	infoResp, err := motorFweAccountShop.QuerySubMerchantInfo(ctx, req)
	if err != nil || infoResp == nil {
		logs.CtxError(ctx, "[QuerySubMerchantInfo] QuerySubMerchantInfo rpc error, err = %+v", err)
		return nil, errdef.NewBizErrWithCode(errdef.AccountRpcErr, err)
	}
	if infoResp.BaseResp != nil && infoResp.BaseResp.StatusCode != int32(0) {
		message := infoResp.BaseResp.StatusMessage
		logs.CtxError(ctx, "[QuerySubMerchantInfo] QuerySubMerchantInfo biz error, message = %+v", message)
		return nil, errdef.NewRawErr(errdef.AccountRpcErr, message)
	}
	return infoResp.SubMerchantInfo, nil
}
