package service

import (
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account/financeaccountservice"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
	"context"
)

type FinanceAccountService struct{}

var (
	financeAccountClient financeaccountservice.Client
	financeAccountPSM    = "motor.fwe_trade.finance_account"
)

func init() {
	financeAccountClient = financeaccountservice.MustNewClient(financeAccountPSM, client.WithMiddleware(middleware.LogMiddleware))
}

func NewFinanceAccountService() *FinanceAccountService {
	return &FinanceAccountService{}
}

// GetFweSubMerchantInfo 获取四轮商户财经入驻信息， 返回map: key: merchant_id  value: 开通渠道的数组  使用时需要判断状态
func (s *FinanceAccountService) GetFweSubMerchantInfo(ctx context.Context, tenantType tenant_base.TenantType,
	fweAccountId string) (map[string][]*finance_account.SubMerchantInfo, *errdef.BizErr) {
	req := &finance_account.GetFweSubMerchantInfoReq{
		TenantType: tenantType,
		AccountID:  fweAccountId,
		Base:       base.NewBase(),
	}
	rsp, err := financeAccountClient.GetFweSubMerchantInfo(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[GetFweSubMerchantInfo] rpc failed, err=%+v", err)
		return nil, errdef.NewBizErr(errdef.FinanceAccountRpcErr, err, "")
	}
	if rsp.BaseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[GetFweSubMerchantInfo] rpc failed, statusCode=%d", rsp.BaseResp.StatusCode)
		return nil, errdef.NewFinanceAccountRawErr(rsp.BaseResp.StatusCode, rsp.BaseResp.StatusMessage)
	}
	if rsp.FweSubMerchantInfo == nil {
		return nil, nil
	}

	return rsp.FweSubMerchantInfo.SubMerchantInfos, nil
}

func (s FinanceAccountService) MGetSubMerchantInfo(ctx context.Context, tenantType tenant_base.TenantType, accountIds []string) (map[string][]*finance_account.SubMerchantInfo, *errdef.BizErr) {
	var (
		res = make(map[string][]*finance_account.SubMerchantInfo)
	)
	// 这里因为存在账号映射的问题，只能单独查询
	for _, accountId := range accountIds {
		subMerchantInfoMap, bizErr := s.GetFweSubMerchantInfo(ctx, tenantType, accountId)
		if bizErr != nil {
			logs.CtxError(ctx, "[MGetSubMerchantInfo] rpc failed, err=%+v", bizErr)
			return nil, bizErr
		}
		for _, merchantInfos := range subMerchantInfoMap {
			res[accountId] = append(res[accountId], merchantInfos...)
		}
	}
	return res, nil
}

func (s *FinanceAccountService) ConvertCommonSplitInfo(ctx context.Context, tenantType tenant_base.TenantType, splitInfo *fwe_trade_common.TradeSpiltInfo) ([]*payment.SplitInfo, *errdef.BizErr) {
	if splitInfo == nil || len(splitInfo.Detail) == 0 {
		return nil, nil
	}
	var (
		fweAccountIDs      = make([]string, 0)
		subMerchantInfoMap = make(map[string][]*finance_account.SubMerchantInfo)
	)

	for _, splitUnit := range splitInfo.Detail {
		if splitUnit.SplitUIDType == fwe_trade_common.SplitUIDType_Shop {
			fweAccountIDs = append(fweAccountIDs, splitUnit.SplitUID)
		}
	}
	fweAccountIDs = slices.DistinctString(fweAccountIDs)
	// 没有四轮商户
	if len(fweAccountIDs) == 0 {
		payments, _ := packer.SplitInfoCommonList2PaymentListV2(splitInfo, subMerchantInfoMap)
		return payments, nil
	}
	// 查询
	subMerchantInfoMap, bizErr := s.MGetSubMerchantInfo(ctx, tenantType, fweAccountIDs)
	if bizErr != nil {
		logs.CtxError(ctx, "[FinanceAccountService-ConvertCommonSplitInfo] MGetSubMerchantInfo error, err = %v", bizErr.Error())
		return nil, bizErr
	}
	// check
	if bizErr = s.checkSubMerchantInfo(ctx, fweAccountIDs, subMerchantInfoMap); bizErr != nil {
		logs.CtxError(ctx, "[FinanceAccountService-ConvertCommonSplitInfo] CheckSubMerchantInfo error, err = %v", bizErr.Error())
		return nil, bizErr
	}
	// convert
	payments, _ := packer.SplitInfoCommonList2PaymentListV2(splitInfo, subMerchantInfoMap)
	return payments, nil
}

func (s *FinanceAccountService) checkSubMerchantInfo(ctx context.Context, fweAccountIDs []string, merchantInfoMap map[string][]*finance_account.SubMerchantInfo) *errdef.BizErr {
	fweAccountIDs = slices.DistinctString(fweAccountIDs)
	if len(merchantInfoMap) != len(fweAccountIDs) {
		return errdef.NewRawErr(errdef.FinanceAccountRpcErr, "查询资金账户信息跟分账列表不相符")
	}
	var validChannels = []finance_account.TradeChannel{
		finance_account.TradeChannel_hz,
		finance_account.TradeChannel_yzt_hz,
		finance_account.TradeChannel_yzt_alipay,
		finance_account.TradeChannel_jst_tob_in,
	}
	for _, infos := range merchantInfoMap {
		filters := slices.Filter(infos, func(dto *finance_account.SubMerchantInfo) bool {
			return slices.Contains(validChannels, dto.TradeChannel) && dto.ChannelStatus == finance_account.ChannelStatus_Ready
		}).([]*finance_account.SubMerchantInfo)
		if len(filters) == 0 {
			return errdef.NewRawErr(errdef.FinanceAccountRpcErr, "当前资金账户没有开户成功的渠道")
		}
	}
	return nil
}
