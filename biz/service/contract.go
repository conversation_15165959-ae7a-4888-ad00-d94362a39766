package service

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/gopkg/tools"
	"gorm.io/gen/field"
	"gorm.io/gorm"

	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
)

type ContractService struct{}

func NewContractService() *ContractService {
	return &ContractService{}
}

func (s *ContractService) UnionCreateContract(ctx context.Context, db *gorm.DB, req *service_model.UnionContCreateReq) (rsp *service_model.UnionContCreateRsp, bizErr *errdef.BizErr) {
	if req == nil {
		bizErr = errdef.NewParamsErr("req is nil")
		logs.CtxError(ctx, "[UnionCreateContract] err=%s", bizErr.Error())
		return
	}

	// 函数变量
	var (
		err             error
		outerContSerial string
		// StringPtrWithoutZero 空串为空指针
		StringPtrWithoutZero = func(input string) *string {
			if input == "" {
				return nil
			}
			return &input
		}
	)

	outerContSerial, bizErr = s.getUnionCreateContractNo(ctx, db, req.OrderID, req.ContType, req.Operator)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionCreateContract] err=%s", bizErr.Error())
		return
	}

	// 构造请求
	rpcReq := &core.UnionCreateContractReq{
		ReqBase: &core.ReqBase{
			TenantType: tenant_base.TenantType(req.TenantType),
			BizScene:   core.BizScene(req.BizScene),
		},
		OuterContSerial: outerContSerial,
		ContFileData: &core.ContFileData{
			TmplID:     req.TmplID,
			TmplParams: req.TmplParams,
		},
		SignPartyMap:       req.SignPartyMap,
		InOutData:          req.InOutData,
		ContractType:       nil, // 这里不传 // 合同业务类型，我司/非我司，不传取模板类型
		CallbackEvent:      StringPtrWithoutZero(req.CallbackAction),
		CallbackExtra:      StringPtrWithoutZero(req.CallbackExtra),
		Extra:              StringPtrWithoutZero(req.ContExtra),
		Operator:           req.Operator,
		IsTest:             req.IsTest,
		RelatedContSerials: req.RelatedContSerials,
	}

	// 请求
	var rpcRsp *core.UnionCreateContractResp
	rpcRsp, bizErr = NewContractCore().UnionCreateContract(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionCreateContract] err=%s", bizErr.Error())
		return
	}

	// 构造返回请求
	rsp = &service_model.UnionContCreateRsp{
		ContSerial: rpcRsp.ContSerial,
		SignLinks:  rpcRsp.SignLinks,
	}

	// 合同域结果落表
	_, err = db_query.Use(db).FweOrderContract.WithContext(ctx).
		Where(db_query.FweOrderContract.ContractNo.Eq(outerContSerial)).
		Where(db_query.FweOrderContract.Status.In([]int32{int32(fwe_trade_common.CommonStatus_ToHandle), int32(fwe_trade_common.CommonStatus_Handling)}...)).
		Limit(1).
		UpdateColumnSimple(
			db_query.FweOrderContract.InfraContSerial.Value(rpcRsp.ContSerial),
			db_query.FweOrderContract.InfraContName.Value(rpcRsp.ContName),
			db_query.FweOrderContract.Status.Value(int32(fwe_trade_common.CommonStatus_Handling)),
		)
	if err != nil {
		bizErr = errdef.NewDBErr(err)
		logs.CtxError(ctx, "[UnionCreateContract] err=%s", bizErr.Error())
		return
	}
	return
}

func (s *ContractService) getUnionCreateContractNo(ctx context.Context, db *gorm.DB, orderID string, contType int32, op *core.Operator) (outerContSerial string, bizErr *errdef.BizErr) {
	var (
		err              error
		idempotentStatus = []int32{
			int32(fwe_trade_common.CommonStatus_ToHandle),
			int32(fwe_trade_common.CommonStatus_Handling),
			int32(fwe_trade_common.CommonStatus_Success),
		}
	)
	// 先查
	var contList []*db_model.FweOrderContract
	contList, err = db_query.Use(db).FweOrderContract.WithContext(ctx).
		Select(db_query.FweOrderContract.ContractNo, db_query.FweOrderContract.Status).
		Where(db_query.FweOrderContract.OrderID.Eq(orderID)).
		Where(db_query.FweOrderContract.ContractType.Eq(contType)).
		Find()
	if err != nil {
		bizErr = errdef.NewDBErr(err)
		logs.CtxError(ctx, "[UnionCreateContract] err=%s", bizErr.Error())
		return
	}
	for _, v := range contList {
		// 状态处于待处理 or 处理中 or 已成功
		if slices.ContainsInt32(idempotentStatus, v.Status) {
			if contType == int32(consts.PrepaymentContType) {
				// 对于提前还款合同，仅在一天内可复用
				if v.CreateTime.Format("2006-01-02") == time.Now().Format("2006-01-02") {
					outerContSerial = v.ContractNo
				}
			} else {
				outerContSerial = v.ContractNo
			}
		}
	}

	if outerContSerial != "" {
		logs.CtxInfo(ctx, "[UnionCreateContract] no need create")
		return
	}

	outerContSerial = utils.MakeOuterContSerial(orderID, contType, int32(len(contList)+1))
	err = db_query.Use(db).FweOrderContract.WithContext(ctx).
		Omit(db_query.FweOrderContract.CreateTime, db_query.FweOrderContract.UpdateTime, db_query.FweOrderContract.SignTime).
		Create(&db_model.FweOrderContract{
			OrderID:         orderID,
			ContractType:    contType,
			ContractNo:      outerContSerial,
			InfraContSerial: "", // response to fill
			InfraContName:   "", // no need to fill
			Status:          int32(fwe_trade_common.CommonStatus_ToHandle),
			Creator:         op.OperatorID,
			CreatorName:     op.OperatorName,
			Operator:        op.OperatorID,
			OperatorName:    op.OperatorName,
		})
	if err != nil {
		bizErr = errdef.NewDBErr(err)
		logs.CtxError(ctx, "[UnionCreateContract] err=%s", bizErr.Error())
		return
	}

	return
}

func (s *ContractService) UpdateOrderContStatus(ctx context.Context, db *gorm.DB, orderID string, contType int32,
	fromStatus, toStatus fwe_trade_common.CommonStatus) (bizErr *errdef.BizErr) {
	_, err := db_query.Use(db).FweOrderContract.WithContext(ctx).
		Where(db_query.FweOrderContract.OrderID.Eq(orderID)).
		Where(db_query.FweOrderContract.ContractType.Eq(contType)).
		Where(db_query.FweOrderContract.Status.Eq(int32(fromStatus))).
		UpdateSimple(db_query.FweOrderContract.Status.Value(int32(toStatus)))
	if err != nil {
		bizErr = errdef.NewDBErr(err)
		logs.CtxError(ctx, "[UpdateOrderContStatus] update failed, err=%+v", err)
		return
	}
	return
}

// Deprecated: Please Use UnionCreateContract
var needIn = []int32{
	int32(fwe_trade_common.CommonStatus_ToHandle),
	int32(fwe_trade_common.CommonStatus_Handling),
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) CreateContract(ctx context.Context, req *service_model.ContractCreateParam) (string, *errdef.BizErr) {
	//1.获取合同号
	innerContNo, bizErr := s.GetInnerContNo(ctx, req)
	if bizErr != nil {
		logs.CtxError(ctx, "[ContractService-CreateContract] GetInnerContNo error, err= %+v", bizErr.Error())
		return "", bizErr
	}

	//2。[rpc] 创建合同
	rpcParam := s.buildContCreateParam(req, innerContNo)
	contResp, bizErr := NewContractCore().CreateContractV2(ctx, rpcParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateContract] contract core rpc error ,err = %+v", bizErr)
		return "", bizErr
	}
	//3。回填 contract_no并保存
	contractDO := s.buildOrderContract(req, innerContNo, contResp)
	err := db_query.FweOrderContract.WithContext(ctx).
		Omit(db_query.FweOrderContract.CreateTime, db_query.FweOrderContract.UpdateTime, db_query.FweOrderContract.SignTime).
		Create(contractDO)
	if err != nil && !utils.IsDuplicateEntry(err) {
		return "", errdef.NewBizErr(errdef.MysqlException, err, err.Error())
	}
	return contResp.ContSerial, nil
}

// CancelContract 合同取消
func (s *ContractService) CancelContract(ctx context.Context, req *service_model.ContractCancelParam) (bizErr *errdef.BizErr) {
	if req == nil || req.Operator == nil {
		bizErr = errdef.NewParamsErr("operator is nil")
		logs.CtxError(ctx, "[CancelContract] err=%s", bizErr.Error())
		return
	}
	// 查
	dataList, err := db_query.FweOrderContract.WithContext(ctx).
		Where(db_query.FweOrderContract.OrderID.Eq(req.OrderID)).
		Where(db_query.FweOrderContract.ContractType.Eq(req.ContType)).
		Where(db_query.FweOrderContract.Status.In(needIn...)).
		Find()
	if err != nil {
		bizErr = errdef.NewDBErr(err)
		logs.CtxError(ctx, "[CancelContract] err=%s", bizErr.Error())
		return
	}
	if len(dataList) == 0 {
		bizErr = errdef.NewRawErr(errdef.DataNotFound, fmt.Sprintf("order_id=%s, cont_type=%d has not found", req.OrderID, req.ContType))
		return
	}
	if len(dataList) != 1 {
		bizErr = errdef.NewRawErr(errdef.DataErr, fmt.Sprintf("order_id=%s, cont_type=%d has not 1 cont", req.OrderID, req.ContType))
		logs.CtxError(ctx, "[CancelContract] err=%s", bizErr.Error())
		return
	}
	outContractSerial := dataList[0].ContractNo
	primaryKeyID := dataList[0].ID
	// rpc
	_, bizErr = NewContractCore().CancelContract(ctx, &core.CancelContractReq{
		OuterContSerial: outContractSerial,
		Operator:        req.Operator,
		ReqBase: &core.ReqBase{
			BizScene:   core.BizScene(req.BizScene),
			TenantType: tenant_base.TenantType(req.TenantType),
		},
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[CancelContract] err=%s", bizErr.Error())
		return
	}
	// 写
	_, err = db_query.FweOrderContract.WithContext(ctx).
		Where(db_query.FweOrderContract.ID.Eq(primaryKeyID)).
		UpdateColumnSimple(
			db_query.FweOrderContract.Operator.Value(req.Operator.OperatorID),
			db_query.FweOrderContract.OperatorName.Value(req.Operator.OperatorName),
			db_query.FweOrderContract.Status.Value(int32(fwe_trade_common.CommonStatus_Closed)))
	if err != nil {
		bizErr = errdef.NewDBErr(err)
		logs.CtxError(ctx, "[CancelContract] err=%s", bizErr.Error())
		return
	}
	return
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) CreateContractWithApply(ctx context.Context, req *service_model.ContractCreateParam) (string, *errdef.BizErr) {
	//1.获取合同号
	innerContNo, bizErr := s.GetInnerContNo(ctx, req)
	if bizErr != nil {
		logs.CtxError(ctx, "[ContractService-CreateContract] GetInnerContNo error, err= %+v", bizErr.Error())
		return "", bizErr
	}
	//2。[rpc] 创建合同
	rpcParam := s.buildContCreateParam(req, innerContNo)
	contResp, bizErr := NewContractCore().CreateContractV2WithApply(ctx, rpcParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateContract] contract core rpc error ,err = %+v", bizErr)
		return "", bizErr
	}
	//3。回填 contract_no并保存
	contractDO := s.buildOrderContract(req, innerContNo, contResp)
	err := db_query.FweOrderContract.WithContext(ctx).
		Omit(db_query.FweOrderContract.CreateTime, db_query.FweOrderContract.UpdateTime, db_query.FweOrderContract.SignTime).
		Create(contractDO)
	if err != nil && !utils.IsDuplicateEntry(err) {
		return "", errdef.NewBizErr(errdef.MysqlException, err, err.Error())
	}
	return contResp.SignURL, nil
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) CreateContractForNotInnerWithApply(ctx context.Context, req *service_model.ContractCreateParamForNotInner) (contSerial string, signLink string, bizErr *errdef.BizErr) {
	//1。生成唯一out_cont_no (orderId+contType)
	innerContNo, bizErr := s.GetInnerContNo(ctx, &service_model.ContractCreateParam{
		OrderID:  req.OrderID,
		ContType: req.ContType,
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateContractForNotInnerWithApply] GetInnerContNo error, err= %+v", bizErr.Error())
		return "", "", bizErr
	}

	//2。[rpc] 创建合同
	rpcParam := s.buildContCreateParamForNotInner(req, innerContNo)
	contResp, bizErr := NewContractCore().CreateContract(ctx, rpcParam)
	if bizErr != nil {
		if bizErr.Code() != 34003 {
			logs.CtxError(ctx, "[CreateContractForNotInnerWithApply] CreateContractForNotInnerWithApply, contract core rpc error ,err = %+v", bizErr.Error())
			return "", "", bizErr
		}
	}

	//3. [rpc] 申请签章
	applyStampReq := s.buildApplyStampParamForNotInner(req, innerContNo)
	_, bizErr = NewContractCore().ApplyStamp(ctx, applyStampReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateContractForNotInnerWithApply] CreateContractForNotInnerWithApply, err = %+v", bizErr.Error())
		return "", "", bizErr
	}

	//4. 回填 contract_no并保存
	contractDO := s.buildOrderContractForNotInner(req, innerContNo, contResp)
	err := db_query.FweOrderContract.WithContext(ctx).
		Omit(db_query.FweOrderContract.CreateTime, db_query.FweOrderContract.UpdateTime, db_query.FweOrderContract.SignTime).
		Create(contractDO)

	if err != nil && !utils.IsDuplicateEntry(err) {
		return "", "", errdef.NewBizErr(errdef.MysqlException, err, err.Error())
	}

	//5. 获取链接
	getStampLinkReq := &core.GetStampLinkReq{
		ReqBase:         rpcParam.ReqBase,
		OuterContSerial: &innerContNo,
		SignPosition:    core.SignPosition_PB,
	}
	stampLinkResp, bizErr := NewContractCore().GetStampLink(ctx, getStampLinkReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateContractForNotInnerWithApply] CreateContractForNotInnerWithApply, GetStampLink err = %+v", bizErr.Error())
		return "", "", bizErr
	}

	return contResp.ContSerial, stampLinkResp.GetLink(), nil
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) genInnerContNo(orderNo string, contType int32, version int32) string {
	return fmt.Sprintf("%v_%d_%d", orderNo, contType, version)
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) buildContCreateParam(req *service_model.ContractCreateParam, contNo string) *core.CreateContractV2Req {
	rpcParam := &core.CreateContractV2Req{
		ReqBase: &core.ReqBase{
			TenantType: tenant_base.TenantType(req.TenantType),
			BizScene:   core.BizScene(req.BizScene),
		},

		Operator: &core.Operator{
			OperatorID:   req.OperatorID,
			OperatorName: req.OperatorName,
		},
		OuterContSerial: contNo,
		TmplID:          req.TmplID,
		NeedSignNoCert:  req.NeedSignNoCert,
		TmplParams:      req.TmplParams,
		SignPartyList:   nil,
		InOutData:       nil,
		SmsConfig:       nil,
		CallbackEvent:   &req.CallbackEvent,
		CallbackExtra:   &req.CallbackExtra,
		ReturnURL:       conv.StringPtr(req.ReturnUrl),
		Base:            base.NewBase(),
	}

	if req.SmsTmplID != 0 {
		rpcParam.SmsConfig = &core.SmsConfig{
			SmsTmplID:    req.SmsTmplID,
			SmsChannelID: req.SmsChannelID,
		}
	} else {
		rpcParam.SmsConfigMap = req.SmsConfigMap
	}

	if len(req.SignPartList) > 0 {
		contSignParts := make([]*core.SignPartyData, 0)
		for _, part := range req.SignPartList {
			contSignParts = append(contSignParts, transferSignPart(part))
		}
		rpcParam.SignPartyList = contSignParts
	} else {
		rpcParam.SignPartyList = req.SignPartyDataList
	}

	if req.InOutData != nil {
		rpcParam.InOutData = transferInOutData(req.InOutData)
	}
	return rpcParam
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) buildContCreateParamForNotInner(req *service_model.ContractCreateParamForNotInner, contNo string) *core.CreateContractReq {
	signatoryCompanyList := make([]*core.ContSignatoryCompany, 0)
	for _, SignPartyInfo := range req.SignPartyInfoMap {
		signatoryCompanyList = append(signatoryCompanyList, &core.ContSignatoryCompany{
			SignPosition: SignPartyInfo.SignPosition,
			TaxID:        &SignPartyInfo.TaxID,
		})
	}

	rpcParam := &core.CreateContractReq{
		ReqBase: &core.ReqBase{
			TenantType: tenant_base.TenantType(req.TenantType),
			BizScene:   core.BizScene(req.BizScene),
		},
		Operator: &core.Operator{
			OperatorID:   req.OperatorID,
			OperatorName: req.OperatorName,
		},
		OuterContSerial:   contNo,
		UseTmpl:           true,
		TmplID:            &req.TmplID,
		TmplParams:        req.TmplParams,
		SignatoryCompanys: signatoryCompanyList,
		CallbackEvent:     &req.CallbackEvent,
	}

	return rpcParam
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) buildApplyStampParamForNotInner(req *service_model.ContractCreateParamForNotInner, contNo string) *core.ApplyStampReq {
	stampSeq := make([]core.SignPosition, 0)
	StampDetails := make(map[core.SignPosition]*core.StampDetail, 0)
	for _, stampInfo := range req.SignPartyInfoMap {
		if stampDetail := stampInfo.StampDetail; stampDetail != nil {
			stampSeq = append(stampSeq, stampInfo.SignPosition)
			StampDetails[stampInfo.SignPosition] = stampDetail
		}
	}

	rpcParam := &core.ApplyStampReq{
		ReqBase: &core.ReqBase{
			TenantType: tenant_base.TenantType(req.TenantType),
			BizScene:   core.BizScene(req.BizScene),
		},
		Operator: &core.Operator{
			OperatorID:   req.OperatorID,
			OperatorName: req.OperatorName,
		},
		OuterContSerial:      &contNo,
		StampPattern:         core.StampPattern_Part,
		StampSeq:             stampSeq,
		StampDetails:         StampDetails,
		ManualSignExpireTime: conv.Int64Ptr(time.Now().AddDate(1, 0, 0).Unix()), // 180 天过期事件
	}

	return rpcParam
}

// Deprecated: Please use UnionCreateContract
func transferInOutData(data *service_model.InOutData) *core.InOutData {
	return &core.InOutData{
		Currency: core.Currency(data.Currency),
		TotalOut: data.TotalOut,
		TotalIn:  data.TotalIn,
	}
}

// Deprecated: Please use UnionCreateContract
func transferSignPart(part *service_model.SignPart) *core.SignPartyData {
	res := &core.SignPartyData{
		SignPosition: core.SignPosition(part.SignPosition),
		IsInner:      part.IsInner,
		CardType:     core.CardType(part.CardType),
		IdentName:    part.IdentName,
		IdentID:      part.IdentID,
		SignerName:   nil,
		SignerPhone:  nil,
	}

	if part.SignerName != "" {
		res.SignerName = &part.SignerName
	}
	if part.SignerPhone != "" {
		res.SignerPhone = &part.SignerPhone
	}
	return res
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) buildOrderContract(req *service_model.ContractCreateParam, innerNo string, cont *core.CreateContractV2Resp) *db_model.FweOrderContract {

	return &db_model.FweOrderContract{
		OrderID:         req.OrderID,
		ContractType:    req.ContType,
		ContractNo:      innerNo,
		InfraContSerial: cont.ContSerial,
		Status:          int32(fwe_trade_common.CommonStatus_Handling),
		InfraContName:   cont.ContName,
		Creator:         req.OperatorID,
		CreatorName:     req.OperatorName,
		Operator:        req.OperatorID,
		OperatorName:    req.OperatorName,
	}
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) buildOrderContractForNotInner(req *service_model.ContractCreateParamForNotInner, innerNo string, cont *core.CreateContractResp) *db_model.FweOrderContract {
	return &db_model.FweOrderContract{
		OrderID:         req.OrderID,
		ContractType:    req.ContType,
		ContractNo:      innerNo,
		InfraContSerial: cont.ContSerial,
		Status:          int32(fwe_trade_common.CommonStatus_Handling),
		Creator:         req.OperatorID,
		CreatorName:     req.OperatorName,
		Operator:        req.OperatorID,
		OperatorName:    req.OperatorName,
	}
}

// CompleteCont : 更新订单合同表的签署时间和状态
func (s *ContractService) CompleteCont(ctx context.Context, cbModel *callback_model.ContractCallbackModel) *errdef.BizErr {
	var (
		innerNo     = cbModel.OutContID
		signTime    = cbModel.SignTime
		whereStatus = []int32{int32(fwe_trade_common.CommonStatus_ToHandle), int32(fwe_trade_common.CommonStatus_Handling)}
	)
	updateField := []field.AssignExpr{
		db_query.FweOrderContract.Status.Value(int32(fwe_trade_common.CommonStatus_Success)),
		db_query.FweOrderContract.SignTime.Value(time.Unix(signTime, 0)),
		db_query.FweOrderContract.InfraContSerial.Value(cbModel.ContSerial),
		db_query.FweOrderContract.InfraContName.Value(cbModel.ContName),
	}
	if len(updateField) == 0 || innerNo == "" {
		bizErr := errdef.NewParamsErr("update field list is 0")
		logs.CtxError(ctx, "[ContractService] err=%v", bizErr.Error())
		return bizErr
	}
	result, err := db_query.FweOrderContract.WithContext(ctx).
		Where(db_query.FweOrderContract.ContractNo.Eq(innerNo)).
		Where(db_query.FweOrderContract.Status.In(whereStatus...)).
		Limit(1).
		UpdateColumnSimple(updateField...)
	if err != nil {
		bizErr := errdef.NewBizErr(errdef.MysqlException, err, "")
		logs.CtxError(ctx, "[ContractService] err=%v", bizErr.Error())
		return bizErr
	}
	if result.RowsAffected == 0 {
		bizErr := errdef.NewParamsErr("no column update")
		logs.CtxWarn(ctx, "[ContractService] err=%v", bizErr.Error())
		return nil
	}
	return nil
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) GetInnerContNo(ctx context.Context, req *service_model.ContractCreateParam) (string, *errdef.BizErr) {
	orderContracts, dbErr := db_query.FweOrderContract.WithContext(ctx).Where(db_query.FweOrderContract.OrderID.Eq(req.OrderID)).
		Where(db_query.FweOrderContract.ContractType.Eq(req.ContType)).Find()
	if dbErr != nil {
		logs.CtxError(ctx, "[ContractService-GetInnerContNo] query FweOrderContract error, err= %v", dbErr)
		return "", errdef.NewBizErr(errdef.MysqlException, dbErr, "query FweOrderContract error")
	}
	// 幂等创建
	filters, ok := slices.Filter(orderContracts, func(contract *db_model.FweOrderContract) bool {
		return slices.Contains(needIn, contract.Status)
	}).([]*db_model.FweOrderContract)
	if !ok {
		logs.CtxError(ctx, "[ContractService-GetInnerContNo] query FweOrderContract data error, req=%v", tools.GetLogStr(req))
		return "", errdef.NewRawErr(errdef.DirtyDataException, "query FweOrderContract conversion error")
	}
	if filters != nil && len(filters) > 1 {
		logs.CtxError(ctx, "[ContractService-GetInnerContNo] query FweOrderContract data error, req=%v", tools.GetLogStr(req))
		return "", errdef.NewRawErr(errdef.DirtyDataException, "query FweOrderContract data error")
	} else if len(filters) == 1 {
		return filters[0].ContractNo, nil
	}
	// 撤销后新建/新建
	version := int32(len(orderContracts)) + int32(1)
	innerContNo := s.genInnerContNo(req.OrderID, req.ContType, version)
	return innerContNo, nil
}

// Deprecated: Please use UnionCreateContract
func (s *ContractService) ApplySignNoCertLink(ctx context.Context, req *service_model.ContractCreateParam, contSerial string, signPart core.SignPartyData) (string, *errdef.BizErr) {
	rpcParam := &core.ApplySignNoCertLinkReq{
		ReqBase: &core.ReqBase{
			TenantType: tenant_base.TenantType(req.TenantType),
			BizScene:   core.BizScene(req.BizScene),
		},

		Operator: &core.Operator{
			OperatorID:   req.OperatorID,
			OperatorName: req.OperatorName,
		},
		ContSerial:     &contSerial,
		SignPosition:   signPart.SignPosition,
		SignerName:     signPart.GetSignerName(),
		SignerPhone:    signPart.GetSignerPhone(),
		SignerIdentity: signPart.IdentID,
		ReturnURL:      conv.StringPtr(req.ReturnUrl),
		Base:           base.NewBase(),
	}

	applyResp, bizErr := NewContractCore().ApplySignNoCertLink(ctx, rpcParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[ApplySignNoCertLink] contract core rpc error ,err = %s", bizErr.Error())
		return "", bizErr
	}
	return applyResp.SignLink, nil
}

func (s *ContractService) QueryRelatedContract(ctx context.Context, db *gorm.DB, orderID string) ([]string, *errdef.BizErr) {
	fweOrderContract := db_query.Use(db).FweOrderContract
	orderContracts, err := fweOrderContract.WithContext(ctx).
		Where(fweOrderContract.OrderID.Eq(orderID)).
		Where(fweOrderContract.FulfillID.Eq("")).
		Where(fweOrderContract.AfterSaleID.Eq("")).
		Where(fweOrderContract.Status.Eq(int32(fwe_trade_common.CommonStatus_Success))).
		Find()
	if err != nil {
		logs.CtxError(ctx, "[QueryRelatedContract] query FweOrderContract error, err= %v", err)
		return nil, errdef.NewDBErr(err)
	}

	contractSerials := make([]string, 0)
	for _, orderContract := range orderContracts {
		contractSerials = append(contractSerials, orderContract.InfraContSerial)
	}
	return contractSerials, nil
}
