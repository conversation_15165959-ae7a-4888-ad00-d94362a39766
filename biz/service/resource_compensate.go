package service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/types"
	"context"
	"encoding/json"

	"code.byted.org/kite/kitex/client"
	"github.com/cloudwego/kitex/pkg/kerrors"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/resource_compensate"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/resource_compensate/resourcecompensateservice"
)

type CompensateWrapper struct{}

func NewCompensateWrapper() *CompensateWrapper {
	return &CompensateWrapper{}
}

type OccupyFn func(ctx context.Context) *errdef.BizErr

var resourceCompensateClient resourcecompensateservice.Client

func init() {
	resourceCompensateClient = resourcecompensateservice.MustNewClient("motor.fwe_trade.resource_compensate",
		client.WithMiddleware(middleware.LogMiddleware))
}

// Wrapper 资源占用时注册待补偿资源
func (w *CompensateWrapper) Wrapper(ctx context.Context, fn OccupyFn, param *model.OrderResource) *errdef.BizErr {
	bizErr := fn(ctx)

	// 当调用成功，或者 超时时，注册资源
	if bizErr == nil || (bizErr.Err() != nil && kerrors.IsTimeoutError(bizErr.Err())) {
		rsp, err := resourceCompensateClient.RegisterResource(ctx, w.buildReq(param))
		if err != nil {
			return errdef.NewBizErrWithCode(errdef.RegisterResourceErr, err)
		}
		if rsp.BaseResp.StatusCode != 0 {
			return errdef.NewRawErr(errdef.RegisterResourceErr, rsp.BaseResp.StatusMessage)
		}
	}

	return bizErr
}

func (w *CompensateWrapper) buildReq(param *model.OrderResource) *resource_compensate.RegisterResourceReq {
	var (
		resourceInfoList = make([]*resource_compensate.ResourceInfo, 0)
	)

	for outID, occupyNum := range param.OutIDInfo {
		resourceInfoList = append(resourceInfoList, &resource_compensate.ResourceInfo{
			OrderID:         param.OrderID,
			OrderType:       param.OrderType,
			ResourceType:    param.ResourceType,
			ResourceSubType: param.ResourceSubType,
			OutID:           outID,
			OccupyNum:       occupyNum,
			Extra:           param.Extra,
		})
	}

	req := &resource_compensate.RegisterResourceReq{
		RegisterList: resourceInfoList,
		Base:         base.NewBase(),
	}
	return req
}

func ProduceOrderCancelMessage(ctx context.Context, cancelMessage *fwe_trade_common.OrderCancelMessage) *errdef.BizErr {
	cancelMessageBytes, _ := json.Marshal(cancelMessage)
	msg := types.NewDefaultMessage(consts.OrderCancelTopic, cancelMessageBytes)
	sendResp, err := caller.OrderCancelEventProducer.Send(ctx, msg)
	if err != nil {
		logs.CtxError(ctx, "[ProduceOrderCancelMessage] send message failed, err=%v, msg=%s", err, string(cancelMessageBytes))
		utils.EmitCounter("produce_order_cancel_msg.fail", 1)
		return errdef.NewBizErrWithCode(errdef.ProduceMsgErr, err)
	}
	logs.CtxInfo(ctx, "[ProduceOrderCancelMessage] produce msg: %s, res: %s", string(cancelMessageBytes), tools.GetLogStr(sendResp))
	return nil
}
