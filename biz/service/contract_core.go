package service

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core/contractservice"
)

const (
	ContractNotReady       = 4023 // oa合同未生成
	ContractStatusNotReady = 4024 // 合同状态未就绪
)

var motorFweContractCoreClient contractservice.Client

func init() {
	motorFweContractCoreClient = contractservice.MustNewClient("motor.fwe_contract.core",
		client.WithMiddleware(middleware.LogMiddleware))
}

type ContractCore struct{}

func NewContractCore() *ContractCore {
	return &ContractCore{}
}

func (cont *ContractCore) UnionCreateContract(ctx context.Context, rpcReq *core.UnionCreateContractReq) (rpcRsp *core.UnionCreateContractResp, bizErr *errdef.BizErr) {
	var err error
	rpcRsp, err = motorFweContractCoreClient.UnionCreateContract(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ContractRpcErr, err, "")
		logs.CtxError(ctx, "[UnionCreateContract] err=%s", bizErr.Error())
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewContractRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		logs.CtxError(ctx, "[UnionCreateContract] err=%s", bizErr.Error())
		return
	}
	return
}

func (cont *ContractCore) CreateContractV2(ctx context.Context, rpcReq *core.CreateContractV2Req) (resp *core.CreateContractV2Resp, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweContractCoreClient.CreateContractV2(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ContractRpcErr, err, "")
		return
	}

	if rpcRsp.BaseResp.StatusCode == ContractNotReady || rpcRsp.BaseResp.StatusCode == ContractStatusNotReady {
		bizErr = errdef.NewRawErr(errdef.ContractNotReady, rpcRsp.BaseResp.StatusMessage)
		return
	} else if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewContractRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	resp = rpcRsp
	return
}

func (cont *ContractCore) CreateContractV2WithApply(ctx context.Context, rpcReq *core.CreateContractV2Req) (resp *core.CreateContractV2Resp, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweContractCoreClient.CreateContractV2WithApplySign(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ContractRpcErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewContractRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	resp = rpcRsp
	return
}

func (cont *ContractCore) CancelContract(ctx context.Context, req *core.CancelContractReq) (resp *core.CancelContractResp, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweContractCoreClient.CancelContract(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ContractRpcErr, err, "")
		return
	}

	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewContractRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}

	resp = rpcRsp
	return
}

func (cont *ContractCore) ApplyStamp(ctx context.Context, rpcReq *core.ApplyStampReq) (resp *core.ApplyStampResp, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweContractCoreClient.ApplyStamp(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ContractRpcErr, err, "")
		return
	}

	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewContractRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}

	resp = rpcRsp
	return
}

func (cont *ContractCore) CreateContract(ctx context.Context, rpcReq *core.CreateContractReq) (resp *core.CreateContractResp, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweContractCoreClient.CreateContract(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ContractRpcErr, err, "")
		return
	}

	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewContractRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		resp = rpcRsp
		return
	}

	resp = rpcRsp
	return
}

func (cont *ContractCore) GetStampLink(ctx context.Context, rpcReq *core.GetStampLinkReq) (resp *core.GetStampLinkResp, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweContractCoreClient.GetStampLink(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ContractRpcErr, err, "")
		return
	}

	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewContractRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}

	resp = rpcRsp
	return
}

func (cont *ContractCore) ApplySignNoCertLink(ctx context.Context, req *core.ApplySignNoCertLinkReq) (resp *core.ApplySignNoCertLinkResp, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweContractCoreClient.ApplySignNoCertLink(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ContractRpcErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewContractRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	resp = rpcRsp
	return
}
