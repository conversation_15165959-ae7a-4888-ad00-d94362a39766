package service

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_activity/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_activity/engine/activityengineservice"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/resource_compensate"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

var (
	activityClient activityengineservice.Client
	activityPsm    = "motor.fwe_activity.engine"
)

func init() {
	activityClient = activityengineservice.MustNewClient(activityPsm, client.WithMiddleware(middleware.LogMiddleware))
}

type ActivityService struct{}

func NewActivityService() *ActivityService {
	return &ActivityService{}
}

func (s *ActivityService) ApproveActivityOrder(ctx context.Context, req *engine.ApproveOrderReq) *errdef.BizErr {
	rsp, err := activityClient.ApproveOrder(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[ApproveActivityOrder] rpc failed, err=%+v, rsp=%s", err, tools.GetLogStr(rsp))
		return errdef.NewBizErrWithCode(errdef.ActivityRpcErr, err)
	}
	if rsp.BaseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[ApproveActivityOrder] rpc status is not zero, err=%+v, rsp=%s", err, tools.GetLogStr(rsp))
		return errdef.NewRawErr(errdef.ActivityRpcErr, rsp.BaseResp.StatusMessage)
	}

	return nil
}

type BatchActivityParam struct {
	OrderID    string
	ProductID  int64
	FweAccID   string
	List       []*fwe_trade_common.ActivityOrder
	LinkSource string
	Operator   *fwe_trade_common.OperatorInfo
}

func (s *ActivityService) BatchFreezeOrder(ctx context.Context, param *BatchActivityParam) *errdef.BizErr {
	var (
		errWg, errCtx = utils.WithContext(ctx)
	)

	for _, v := range param.List {
		order := v
		errWg.Go(errCtx, func(ctx context.Context) error {
			if order.RealAmount == 0 {
				return nil
			}
			req := &engine.FreezeOrderReq{
				ActOrderID:   order.ActOrderID,
				TradeOrderID: param.OrderID,
				ProductID:    &param.ProductID,
				FweAccID:     &param.FweAccID,
				LinkSource:   &param.LinkSource,
				Operator:     param.Operator,
				Base:         base.NewBase(),
			}
			bizErr := s.FreezeOrder(ctx, req)
			if bizErr != nil {
				return bizErr
			}
			return nil
		})
	}

	if err := errWg.Wait(); err != nil {
		logs.CtxError(ctx, "[BatchFreezeOrder] freeze failed, err=%s", err.Error())
		if bizErr, ok := err.(*errdef.BizErr); ok {
			return bizErr
		} else {
			return errdef.NewBizErrWithCode(errdef.ActivityRpcErr, err)
		}
	}
	return nil
}

func (s *ActivityService) FreezeOrder(ctx context.Context, req *engine.FreezeOrderReq) *errdef.BizErr {
	f := func(ctx context.Context) *errdef.BizErr {
		rsp, err := activityClient.FreezeOrder(ctx, req)
		if err != nil {
			logs.CtxError(ctx, "[FreezeOrder] rpc failed, err=%+v, rsp=%s", err, tools.GetLogStr(rsp))
			return errdef.NewBizErrWithCode(errdef.ActivityRpcErr, err)
		}
		if rsp.BaseResp.StatusCode != 0 {
			logs.CtxError(ctx, "[FreezeOrder] rpc status is not zero, err=%+v, rsp=%s", err, tools.GetLogStr(rsp))
			return errdef.NewRawErr(errdef.ActivityRpcErr, rsp.BaseResp.StatusMessage)
		}

		return nil
	}

	occupyParam := &model.OrderResource{
		OrderID:         req.TradeOrderID,
		OrderType:       fwe_trade_common.OrderType_Trade,
		ResourceType:    resource_compensate.ResourceType_Coupon,
		ResourceSubType: int32(engine.CouponType_Cashback),
		OutIDInfo:       map[string]int32{req.ActOrderID: 1},
		Extra:           nil,
	}

	return NewCompensateWrapper().Wrapper(ctx, f, occupyParam)
}
