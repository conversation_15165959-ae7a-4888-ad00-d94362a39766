package service

import (
	"code.byted.org/lang/gg/choose"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/overpass/motor_fwe_ecom_product_item/kitex_gen/motor/fwe_ecom/product_item"
	"code.byted.org/overpass/motor_fwe_ecom_product_item/rpc/motor_fwe_ecom_product_item"
	"context"
)

var DefaultProductItemService = NewProductItemService()

// ProductItemService 商品库存
type ProductItemService struct{}

func NewProductItemService() *ProductItemService {
	return &ProductItemService{}
}

func (s *ProductItemService) GetProductItem(ctx context.Context, productID int64, skuID *int64) (*product_item.ProductDetail, *errdef.BizErr) {
	resp, err := motor_fwe_ecom_product_item.RawCall.MGetProduct2C(ctx, &product_item.MGetProduct2CReq{
		Param: []*product_item.GetProductInfoParam{
			{
				ProductId: productID,
				SkuIdList: choose.IfLazyL(skuID != nil, func() []int64 {
					return []int64{*skuID}
				}, nil),
			},
		},
		Option: &product_item.GetProductOption{
			NeedSku:                true,
			NeedSkuStock:           true,
			NeedProductMediaInfo:   false,
			NeedProductFulfillInfo: false,
			NeedProductSalePackage: false,
			NeedProductTag:         false,
			NeedFinanceProduct:     false,
			NeedInsuranceProduct:   false,
			NeedDstServiceFeeRatio: false,
			NeedFinanceCapitalInfo: false,
			NeedBoardCostInfo:      false,
		},
		Scene: "",
	})

	if err != nil {
		return nil, errdef.NewRpcErr(err)
	}

	if resp == nil || len(resp.Data) == 0 || resp.Data[productID] == nil {
		return nil, nil
	}

	return resp.Data[productID].ProductDetail, nil
}
