package service

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/safe"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/safe/tradesafeservice"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type SafeService struct{}

func NewSafeService() *SafeService {
	return &SafeService{}
}

var safeClient tradesafeservice.Client

func init() {
	safeClient = tradesafeservice.MustNewClient("motor.fwe_trade.safe",
		client.WithMiddleware(middleware.LogMiddleware))
}

type CheckAmountByContParam struct {
	OrderID                   string
	FinanceOrderType          int32
	Amount                    int64
	AllowIncomeAmountOverflow bool
	Params                    string
	FulfillID                 string
	TradeCategory             fwe_trade_common.TradeCategory
}

type CheckContTotalAmountParam struct {
	TmplID           int64
	TmplParams       map[string]string
	OrderTotalAmount int64
	Params           string
	BizScene         int32
	ContType         int32
	OrderID          string
}

func (s *SafeService) CheckAmountByCont(ctx context.Context, param *CheckAmountByContParam) (bool, string, *errdef.BizErr) {

	req := &safe.CheckAmountByContStructReq{
		OrderID:                   param.OrderID,
		FinanceOrderType:          param.FinanceOrderType,
		Amount:                    param.Amount,
		AllowIncomeAmountOverflow: conv.BoolPtr(param.AllowIncomeAmountOverflow),
		Params:                    conv.StringPtr(param.Params),
		FulfillID:                 &param.FulfillID,
		TradeCatogory:             param.TradeCategory,
		Base:                      base.NewBase(),
	}
	rsp, err := safeClient.CheckAmountByContStruct(ctx, req)
	if err != nil || rsp == nil || rsp.BaseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[SafeService.CheckAmountByCont] rpc failed, err=%+v", err)
		return false, "", errdef.NewBizErrWithCode(errdef.SafeRpcErr, err)
	}

	if rsp.BlockStatus == safe.BlockStatus_PASS {
		return true, "", nil
	}
	return false, rsp.BlockMessage, nil
}

func (s *SafeService) CheckContTotalAmount(ctx context.Context, param *CheckContTotalAmountParam) (bool, string, *errdef.BizErr) {

	req := &safe.CheckContTotalAmountReq{
		TmplID:           param.TmplID,
		TmplParams:       param.TmplParams,
		OrderTotalAmount: param.OrderTotalAmount,
		Params:           conv.StringPtr(param.Params),
		BizScene:         &param.BizScene,
		ContType:         &param.ContType,
		OrderID:          &param.OrderID,
	}
	rsp, err := safeClient.CheckContTotalAmount(ctx, req)
	if err != nil || rsp == nil || rsp.BaseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[SafeService.CheckContTotalAmount] rpc failed, err=%+v", err)
		return false, "", errdef.NewBizErrWithCode(errdef.SafeRpcErr, err)
	}

	if rsp.BlockStatus == safe.BlockStatus_PASS {
		return true, "", nil
	}
	return false, rsp.BlockMessage, nil
}
