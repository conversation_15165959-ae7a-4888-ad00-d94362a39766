package service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/fee"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/fee/feeservice"
	"code.byted.org/motor/gopkg/tools"
	"context"
)

var motorTradeFeeClient feeservice.Client

func init() {
	motorTradeFeeClient = feeservice.MustNewClient("motor.fwe_trade.fee",
		client.WithMiddleware(middleware.LogMiddleware))
}

type TradeFeeService struct {
}

func NewTradeFeeService() *TradeFeeService {
	return &TradeFeeService{}
}

func (s *TradeFeeService) ChargeRefund(ctx context.Context, req *fee.ChargeRefundReq) (*fee.ChargeRefundResp, *errdef.BizErr) {
	chargeRefundResp, err := motorTradeFeeClient.ChargeRefund(ctx, req)
	if err != nil || chargeRefundResp == nil {
		logs.CtxWarn(ctx, "[TradeFeeService-ChargeRefund] ChargeRefund rpc error, err = %v", err)
		return nil, errdef.NewBizErr(errdef.TradeFeeRpcErr, err, "ChargeRefund rpc error")
	}
	baseResp := chargeRefundResp.BaseResp
	if baseResp != nil && baseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[TradeFeeService-ChargeRefund] ChargeRefund biz error, res = %v", tools.GetLogStr(baseResp))
		return nil, errdef.NewTradeFeeRawErr(baseResp.StatusCode, baseResp.StatusMessage)
	}
	return chargeRefundResp, nil
}

func (s *TradeFeeService) ChargeSettle(ctx context.Context, req *fee.ChargeSettleReq) (*fee.ChargeSettleResp, *errdef.BizErr) {
	chargeSettleResp, err := motorTradeFeeClient.ChargeSettle(ctx, req)
	if err != nil || chargeSettleResp == nil {
		logs.CtxWarn(ctx, "[TradeFeeService-ChargeSettle] ChargeSettle rpc error, err = %v", err)
		return nil, errdef.NewBizErr(errdef.TradeFeeRpcErr, err, "ChargeRefund rpc error")
	}
	baseResp := chargeSettleResp.BaseResp
	if baseResp != nil && baseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[TradeFeeService-ChargeSettle] ChargeSettle biz error, res = %v", tools.GetLogStr(baseResp))
		return nil, errdef.NewTradeFeeRawErr(baseResp.StatusCode, baseResp.StatusMessage)
	}
	return chargeSettleResp, nil
}

func (s *TradeFeeService) GetOneTradeFeeRecord(ctx context.Context, recordID string) (*fee.FeeChargeRecord, *errdef.BizErr) {
	req := &fee.MGetChargeRecordReq{
		RecordIDList: []string{recordID},
	}
	mGetChargeRecordResp, err := motorTradeFeeClient.MGetChargeRecord(ctx, req)
	if err != nil || mGetChargeRecordResp == nil {
		logs.CtxWarn(ctx, "[TradeFeeService-GetOneTradeFeeRecord] MGetChargeRecord rpc error, err = %v", err)
		return nil, errdef.NewBizErr(errdef.TradeFeeRpcErr, err, "MGetChargeRecord rpc error")
	}
	baseResp := mGetChargeRecordResp.BaseResp
	if baseResp != nil && baseResp.StatusCode != 0 {
		logs.CtxError(ctx, "[TradeFeeService-GetOneTradeFeeRecord] MGetChargeRecord biz error, res = %v", tools.GetLogStr(baseResp))
		return nil, errdef.NewTradeFeeRawErr(baseResp.StatusCode, baseResp.StatusMessage)
	}
	if len(mGetChargeRecordResp.RecordList) == 0 {
		logs.CtxError(ctx, "[TradeFeeService-GetOneTradeFeeRecord] MGetChargeRecord data error, recordId = %v", recordID)
		return nil, errdef.NewRawErr(errdef.DataErr, "MGetChargeRecord data error")
	}
	for _, chargeRecord := range mGetChargeRecordResp.GetRecordList() {
		if chargeRecord.RecordID == recordID {
			return chargeRecord, nil
		}
	}
	logs.CtxError(ctx, "[TradeFeeService-GetOneTradeFeeRecord] MGetChargeRecord data error, recordId = %v", recordID)
	return nil, errdef.NewRawErr(errdef.DataErr, "MGetChargeRecord data error")
}
