package service

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

type FweOrderLog struct{}

func NewFweOrderLog() *FweOrderLog {
	return &FweOrderLog{}
}

func (service *FweOrderLog) WriteOrderLogList(ctx context.Context, logList []*db_model.FweOrderLog) (bizErr *errdef.BizErr) {
	var err error
	err = db_query.Q.WithContext(ctx).FweOrderLog.
		Omit(db_query.FweOrderLog.CreateTime).
		Create(logList...)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
		return
	}
	return nil
}

func (service *FweOrderLog) GetOrderLogList(ctx context.Context, orderID string) (dataList []*db_model.FweOrderLog, bizErr *errdef.BizErr) {
	var err error
	dataList, err = db_query.Q.WithContext(ctx).FweOrderLog.
		Where(db_query.Q.FweOrderLog.OrderID.Eq(orderID)).
		Limit(100).Find()
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.MysqlException, err, "")
		return
	}
	return
}
