package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metrics"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	CommonState "code.byted.org/motor/fwe_trade_common/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/gopkg/tools/tools_recover_kite"
)

/*
	文档参考 https://bytedance.feishu.cn/wiki/wikcnpJt063uU9anwbHQJ8RMpWe
*/

type EAIncome struct{}

func NewEAIncome() *EAIncome {
	return &EAIncome{}
}

func (e *EAIncome) AsyncCreateIncome(ctx context.Context, bizScene int32, orderInfo *service_model.Order) {
	go func() {
		defer func() {
			tools_recover_kite.CheckRecover(ctx, recover(), nil)
		}()
		bizErr := e.CreateIncome(ctx, bizScene, orderInfo)
		if bizErr != nil {
			tag := metrics.Tag("code", string(bizErr.Code()))
			utils.EmitCounter(consts.MetricsCreateEAIncome, 1, tag)
			logs.CtxError(ctx, "[EAIncome.AsyncCreateIncome] err=%v", bizErr.Error())
			return
		}
	}()
}

func (e *EAIncome) CreateIncome(ctx context.Context, bizScene int32, orderInfo *service_model.Order) (bizErr *errdef.BizErr) {
	if orderInfo == nil || orderInfo.FweOrder == nil || orderInfo.FweOrder.OrderID == "" {
		bizErr = errdef.NewParamsErr("order id is 0")
		logs.CtxError(ctx, "[EAIncome.CreateIncome] err=%v", bizErr.Error())
		return
	}
	if orderInfo.FweOrder.OrderStatus != int32(CommonState.OrderSuccessSt.StateCode) {
		bizErr = errdef.NewParamsErr("order status not success")
		logs.CtxError(ctx, "[EAIncome.CreateIncome] err=%v", bizErr.Error())
		return
	}
	var incomeDataList []*db_model.FEaIncome
	switch bizScene {
	case consts.BizSceneSHSellByEarnestFinal.Int32():
		bizErr, incomeDataList = e.createShSellEF(ctx, orderInfo)
	case consts.BizSceneSHSellByFull.Int32():
		bizErr, incomeDataList = e.createShSellFull(ctx, orderInfo)
	case consts.BizSceneSHSellInsurance.Int32():
		bizErr, incomeDataList = e.createShSellInsurance(ctx, orderInfo)
	case consts.BizSceneSHConsignByEF.Int32():
		bizErr, incomeDataList = e.createShConsignByEF(ctx, orderInfo)
	case consts.BizSceneSHConsignByFull.Int32():
		bizErr, incomeDataList = e.createShConsignByFull(ctx, orderInfo)
	case 1108: // todo
		bizErr, incomeDataList = e.createShConsignRevoke(ctx, orderInfo)
	case consts.BizSceneSHBackCompanyCar.Int32():
		bizErr, incomeDataList = e.createShBackCompany(ctx, orderInfo)
	case consts.BizSceneSHSellByEarnestFinalDeliveryCar.Int32():
		bizErr, incomeDataList = e.createShSellEFDC(ctx, orderInfo)
	case consts.BizSceneSHSellByFullDeliveryCar.Int32():
		bizErr, incomeDataList = e.createShSellFullDC(ctx, orderInfo)
	case consts.BizSceneSHConsignByEarnestFinalDeliveryCar.Int32():
		bizErr, incomeDataList = e.createShConsignByEFDC(ctx, orderInfo)
	case consts.BizSceneSHConsignByFullDeliveryCar.Int32():
		bizErr, incomeDataList = e.createShConsignByFullDC(ctx, orderInfo)
	default:
		logs.CtxWarn(ctx, "biz scene = %d not support income", bizScene)
		return
	}
	if bizErr != nil {
		logs.CtxError(ctx, "[EAIncome.CreateIncome] err=%v", bizErr.Error())
		return
	}
	bizErr = e.createIncome(ctx, incomeDataList)
	if bizErr != nil {
		logs.CtxError(ctx, "[EAIncome.CreateIncome] err=%v", bizErr.Error())
		return
	}
	return nil
}

func (e *EAIncome) createShSellEF(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 1101 订金 + 尾款 + 贷款
	var (
		income          int64
		merchantID, mid string
	)
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceEarnest.Value() ||
			v.FinanceOrderType == CommonConsts.FinanceFinal.Value() {
			income += v.Amount + v.LoanAmount
			merchantID = v.MerchantID
			mid = v.Mid
		}
	}
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, income, merchantID, mid, 0))
	return
}

func (e *EAIncome) createShSellFull(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 1102 全款 + 贷款
	var (
		income          int64
		merchantID, mid string
	)
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceTotal.Value() {
			income += v.Amount + v.LoanAmount
			merchantID = v.MerchantID
			mid = v.Mid
		}
	}
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, income, merchantID, mid, 0))
	return
}

func (e *EAIncome) createShSellEFDC(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 订金 + 尾款 交车模式
	var (
		income          int64
		merchantID, mid string
	)
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceEarnest.Value() ||
			v.FinanceOrderType == CommonConsts.FinanceFinal.Value() {
			income += v.Amount + v.LoanAmount
			merchantID = v.MerchantID
			mid = v.Mid
		}
	}
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, income, merchantID, mid, 0))
	return
}

func (e *EAIncome) createShSellFullDC(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 全款 交车模式
	var (
		income          int64
		merchantID, mid string
	)
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceTotal.Value() {
			income += v.Amount + v.LoanAmount
			merchantID = v.MerchantID
			mid = v.Mid
		}
	}
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, income, merchantID, mid, 0))
	return
}

func (e *EAIncome) createShSellInsurance(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 1103 全款
	var (
		income          int64
		merchantID, mid string
	)
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceTotal.Value() {
			merchantID = v.MerchantID
			mid = v.Mid
			income += v.Amount
		}
	}
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, income, merchantID, mid, 0))
	return
}

func (e *EAIncome) createShConsignByEF(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 1104 订金 + 尾款 + 贷款
	// 寄售 1 代理 2 检测 3 整备 4
	var merchantID, mid string
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceEarnest.Value() {
			merchantID = v.MerchantID
			mid = v.Mid
		}
	}
	consignIncome := e.packIncome(orderInfo.TagMap["const_consign_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, consignIncome, merchantID, mid, 1))
	agencyIncome := e.packIncome(orderInfo.TagMap["const_agency_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, agencyIncome, merchantID, mid, 2))
	detectIncome := e.packIncome(orderInfo.TagMap["const_detect_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, detectIncome, merchantID, mid, 3))
	outfitIncome := e.packIncome(orderInfo.TagMap["const_outfit_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, outfitIncome, merchantID, mid, 4))
	return
}

func (e *EAIncome) createShConsignByFull(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 1105 全款 + 贷款
	var merchantID, mid string
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceTotal.Value() {
			merchantID = v.MerchantID
			mid = v.Mid
		}
	}
	consignIncome := e.packIncome(orderInfo.TagMap["const_consign_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, consignIncome, merchantID, mid, 1))
	agencyIncome := e.packIncome(orderInfo.TagMap["const_agency_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, agencyIncome, merchantID, mid, 2))
	detectIncome := e.packIncome(orderInfo.TagMap["const_detect_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, detectIncome, merchantID, mid, 3))
	outfitIncome := e.packIncome(orderInfo.TagMap["const_outfit_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, outfitIncome, merchantID, mid, 4))
	return
}

func (e *EAIncome) createShConsignByEFDC(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 订金 + 尾款 交车模式
	// 寄售 1 代理 2 检测 3 整备 4
	var merchantID, mid string
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceEarnest.Value() {
			merchantID = v.MerchantID
			mid = v.Mid
		}
	}
	consignIncome := e.packIncome(orderInfo.TagMap["const_consign_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, consignIncome, merchantID, mid, 1))
	agencyIncome := e.packIncome(orderInfo.TagMap["const_agency_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, agencyIncome, merchantID, mid, 2))
	detectIncome := e.packIncome(orderInfo.TagMap["const_detect_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, detectIncome, merchantID, mid, 3))
	outfitIncome := e.packIncome(orderInfo.TagMap["const_outfit_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, outfitIncome, merchantID, mid, 4))
	return
}

func (e *EAIncome) createShConsignByFullDC(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 全款 交车模式
	var merchantID, mid string
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceTotal.Value() {
			merchantID = v.MerchantID
			mid = v.Mid
		}
	}
	consignIncome := e.packIncome(orderInfo.TagMap["const_consign_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, consignIncome, merchantID, mid, 1))
	agencyIncome := e.packIncome(orderInfo.TagMap["const_agency_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, agencyIncome, merchantID, mid, 2))
	detectIncome := e.packIncome(orderInfo.TagMap["const_detect_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, detectIncome, merchantID, mid, 3))
	outfitIncome := e.packIncome(orderInfo.TagMap["const_outfit_income"])
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, outfitIncome, merchantID, mid, 4))
	return
}

func (e *EAIncome) createShConsignRevoke(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 1108 全款
	var (
		income          int64
		merchantID, mid string
	)
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceTotal.Value() {
			merchantID = v.MerchantID
			mid = v.Mid
			income += v.Amount
		}
	}
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, income, merchantID, mid, 0))
	return
}

func (e *EAIncome) createShBackCompany(ctx context.Context, orderInfo *service_model.Order) (bizErr *errdef.BizErr, dataList []*db_model.FEaIncome) {
	// 1201 订金
	var (
		income          int64
		merchantID, mid string
	)
	for _, v := range orderInfo.FinanceList {
		if v.FinanceOrderType == CommonConsts.FinanceEarnest.Value() {
			merchantID = v.MerchantID
			mid = v.Mid
			income += v.Amount
		}
	}
	dataList = append(dataList, e.packEaIncome(ctx, orderInfo, income, merchantID, mid, 0))
	return
}

func (e *EAIncome) createIncome(ctx context.Context, dataList []*db_model.FEaIncome) (bizErr *errdef.BizErr) {
	var createDataList []*db_model.FEaIncome
	for _, v := range dataList {
		if v.Amount <= 0 {
			continue
		}
		createDataList = append(createDataList, v)
	}
	if len(createDataList) == 0 {
		logs.CtxInfo(ctx, "[EAIncome.createIncome] no create data list")
		return
	}
	wdb := caller.WriteDBDefault(ctx)
	err := db_query.Use(wdb).WithContext(ctx).FEaIncome.
		Omit(db_query.Q.FEaIncome.CreateTime, db_query.Q.FEaIncome.UpdateTime).
		Create(createDataList...)
	if err != nil {
		bizErr = errdef.NewBizErrWithCode(errdef.ServerException, err)
		logs.CtxError(ctx, "[EAIncome.CreateIncome] err=%v", bizErr.Error())
		return
	}
	return
}

func (e *EAIncome) packEaIncome(ctx context.Context, orderInfo *service_model.Order, income int64, merchantID, mid string, incomeType int32) (data *db_model.FEaIncome) {
	fweOrder := orderInfo.FweOrder
	data = &db_model.FEaIncome{
		ID:            0,
		TenantType:    fweOrder.TenantType,
		MerchantID:    merchantID,
		BizScene:      fweOrder.BizScene,
		Mid:           mid,
		OrderID:       fweOrder.OrderID,
		BuyerID:       fweOrder.BuyerID,
		SellerID:      fweOrder.SellerID,
		IncomeOrderNo: fmt.Sprintf("%s_%d", fweOrder.OrderID, incomeType),
		IncomeType:    incomeType,
		Currency:      "CNY",
		Amount:        income,
		Remark:        "",
		Extra:         nil,
		FinishTime:    time.Now(),
		CreateTime:    nil,
		UpdateTime:    nil,
	}
	return
}

func (e *EAIncome) packIncome(input string) (output int64) {
	output, _ = strconv.ParseInt(input, 10, 64)
	return
}
