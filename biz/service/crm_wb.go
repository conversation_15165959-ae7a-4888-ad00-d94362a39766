package service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/overpass/motor_crm_wb_core/kitex_gen/crm_leads"
	"code.byted.org/overpass/motor_crm_wb_core/rpc/motor_crm_wb_core"
	"context"
)

type CrmWbCore struct{}

func NewCrmWbCore() *CrmWbCore {
	return &CrmWbCore{}
}

func (c *CrmWbCore) SaveLeads(ctx context.Context, req *crm_leads.SaveLeadsReq) (*crm_leads.CRMLeads, *errdef.BizErr) {
	resp, err := motor_crm_wb_core.RawCall.SaveLeads(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "SaveLeads failed error:%v", err.Error())
		return nil, errdef.NewBizErr(errdef.CrmWbErr, err, err.Error())
	}
	return resp.Leads, nil
}
