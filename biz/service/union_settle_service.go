package service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type UnionSettleService struct {
}

func NewUnionSettleService() *UnionSettleService {
	return &UnionSettleService{}
}

func (s *UnionSettleService) UnionSettle(ctx context.Context, param *service_model.UnionSettleParam) (string, *errdef.BizErr) {

	var (
		settleParam         = param.MergeSettleReq
		financeOrderService = NewFinanceOrderService()
	)
	// 生成资金单
	baseOrder := &service_model.OrderBaseParam{
		Identity:  param.MergeSettleReq.Identity,
		OrderID:   param.OrderID,
		OrderName: param.OrderName,
	}
	financeInfo := &fwe_trade_common.FinanceInfo{
		FinanceOrderID:   settleParam.OutID,
		FinanceOrderType: param.SettleType,
		PayStatus:        fwe_trade_common.FinanceStatus_NotHandle,
		Amount:           param.SettleAmount,
		TradeCategory:    fwe_trade_common.TradeCategory_Settle,
		FeeRecordID:      param.FeeRecordID,
	}

	bizErr := financeOrderService.CreateV2(ctx, baseOrder, []*fwe_trade_common.FinanceInfo{financeInfo})
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionSettleService-UnionSettle] CreateV2 error, err = %v", bizErr.Error())
		return "", bizErr
	}
	// 调用payment 分账
	mergeSettleNo, bizErr := NewTradePayment().MergeSettleV2(ctx, settleParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionSettleService-UnionSettle] MergeSettleV2 error, err = %v", bizErr.Error())
		return "", bizErr
	}
	// 结束
	return mergeSettleNo, nil
}
