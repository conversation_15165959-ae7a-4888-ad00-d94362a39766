package service

import (
	"code.byted.org/overpass/common/rpc_error"
	"context"
	"errors"

	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/common/option/calloption"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/rpc/motor_fwe_ecom_product_stock"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
)

// ProductStockService 商品库存
type ProductStockService struct{}

func NewProductStockService() *ProductStockService {
	return &ProductStockService{}
}

// MGetStock 获取库存
func (s *ProductStockService) MGetStock(ctx context.Context, req *product_stock.MGetStockReq) (
	stockMap map[int64]*product_stock.StockItemFormat, bizErr *errdef.BizErr) {
	resp, err := motor_fwe_ecom_product_stock.RawCall.MGetStock(ctx, req, calloption.WithReqRespLogsInfo())
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ProductStockRpcErr, err, "")
		return
	}
	if resp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewRawErr(errdef.ProductStockRpcErr, resp.GetBaseResp().GetStatusMessage())
		return
	}

	stockMap = resp.GetStockMap()
	return
}

// IncrStock 增加库存
func (s *ProductStockService) IncrStock(ctx context.Context, req *product_stock.IncrStockReq) (bizErr *errdef.BizErr) {
	resp, err := motor_fwe_ecom_product_stock.RawCall.IncrStock(ctx, req, calloption.WithReqRespLogsInfo())
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ProductStockRpcErr, err, "")
		return
	}
	if resp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewRawErr(errdef.ProductStockRpcErr, resp.GetBaseResp().GetStatusMessage())
		return
	}

	return
}

// DecrStock 减少库存
func (s *ProductStockService) DecrStock(ctx context.Context, req *product_stock.DecrStockReq) (failedSkuIds []int64, bizErr *errdef.BizErr) {
	resp, err := motor_fwe_ecom_product_stock.RawCall.DecrStock(ctx, req, calloption.WithReqRespLogsInfo())
	if resp == nil {
		logs.CtxError(ctx, "[DecrStock] DecrStock resp is nil")
		bizErr = errdef.NewBizErr(errdef.ProductStockRpcErr, err, "")
		return
	}
	failedSkuIds = resp.GetFailedSkuIds()
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ProductStockRpcErr, err, "")
		return
	}
	if resp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewRawErr(errdef.ProductStockRpcErr, resp.GetBaseResp().GetStatusMessage())
		return
	}

	return
}

// IncrStockOne 增加库存
func (s *ProductStockService) IncrStockOne(ctx context.Context, req *product_stock.IncrStockForOneReq) *errdef.BizErr {
	resp, err := motor_fwe_ecom_product_stock.RawCall.IncrStockForOne(ctx, req, calloption.WithReqRespLogsInfo())
	if resp == nil || err != nil {
		return errdef.NewBizErr(errdef.ProductStockRpcErr, err, "")
	}
	if resp.BaseResp.StatusCode != 0 {
		return errdef.NewRawErr(errdef.ProductStockRpcErr, resp.GetBaseResp().GetStatusMessage())
	}

	return nil
}

// DecrStockOne 减少库存
func (s *ProductStockService) DecrStockOne(ctx context.Context, req *product_stock.DecrStockForOneReq) *errdef.BizErr {
	resp, err := motor_fwe_ecom_product_stock.RawCall.DecrStockForOne(ctx, req, calloption.WithReqRespLogsInfo())
	if err != nil {
		var rpcErr *rpc_error.RPCError
		errors.As(err, &rpcErr)
		if rpcErr.Is(rpc_error.RPC_STATUS_CODE_NOT_ZERO) {
			if rpcErr.BizStatusCode == consts.StockErrorCode_UnderStock {
				return errdef.NewRawErr(errdef.ProductStockNotEnough, rpcErr.BizStatusMessage)
			}
			if rpcErr.BizStatusCode == consts.StockErrorCode_Repeat {
				logs.CtxInfo(ctx, "[DecrStock] DecrStock 操作幂等 忽略报错")
				return nil
			}
		}

		return errdef.NewBizErrWithCode(errdef.ProductStockRpcErr, err)
	}

	if statusCode := resp.BaseResp.StatusCode; statusCode != 0 && statusCode != consts.StockErrorCode_Repeat {
		logs.CtxInfo(ctx, "[DecrStockOne] statusCode: =%v", statusCode)
		if statusCode == consts.StockErrorCode_UnderStock {
			return errdef.NewRawErr(errdef.ProductStockNotEnough, resp.GetBaseResp().GetStatusMessage())
		}
		return errdef.NewRawErr(errdef.ProductStockRpcErr, resp.GetBaseResp().GetStatusMessage())
	}

	return nil
}
