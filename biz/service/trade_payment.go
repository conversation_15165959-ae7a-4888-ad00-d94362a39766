package service

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/client"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	payment2 "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment/paymentservice"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

var motorFweTradePaymentClient paymentservice.Client

func init() {
	motorFweTradePaymentClient = paymentservice.MustNewClient("motor.fwe_trade.payment",
		client.WithMiddleware(middleware.LogMiddleware))
}

type TradePayment struct{}

func NewTradePayment() *TradePayment {
	return &TradePayment{}
}

func (payment *TradePayment) WithdrawDeposit(ctx context.Context, rpcReq *payment.WithdrawDepositReq) (withdrawNo string, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.WithdrawDeposit(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewRawErr(errdef.PaymentRPCErr, rpcRsp.BaseResp.StatusMessage)
		return
	}
	withdrawNo = rpcRsp.WithdrawTradeNo
	return
}

func (payment *TradePayment) CreateCashPay(ctx context.Context, rpcReq *payment.CreateCashPayReq) (payNo string, payData string, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.CreateCashPay(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	payNo = rpcRsp.PayOrderNo
	payData = rpcRsp.PayData
	return
}

func (payment *TradePayment) CreateCashRefund(ctx context.Context, rpcReq *payment.CreateCashRefundReq) (refundNo string, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.CreateCashRefund(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	refundNo = rpcRsp.RefundOrderNo
	return
}

func (payment *TradePayment) Transfer(ctx context.Context, req *payment.TransferReq) *errdef.BizErr {
	transferResp, err := motorFweTradePaymentClient.Transfer(ctx, req)
	if err != nil {
		return errdef.NewBizErr(errdef.PaymentRPCErr, err, err.Error())
	}
	baseResp := transferResp.BaseResp
	if !utils.IsBizSuccess(baseResp.StatusCode) {
		msg := fmt.Sprintf("code:%+v,message:%+v", baseResp.StatusCode, baseResp.StatusMessage)
		return errdef.NewRawErr(errdef.PaymentRPCErr, msg)
	}
	return nil
}

func (payment *TradePayment) CreatePOSPay(ctx context.Context, rpcReq *payment.CreatePOSPayReq) (rsp *action_model.CreatePosPayRsp, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.CreatePOSPay(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	rsp = &action_model.CreatePosPayRsp{
		PayNo:      rpcRsp.PayOrderNo,
		PayData:    rpcRsp.PayData,
		CreateTime: rpcRsp.CreateTime,
	}
	return
}

func (payment *TradePayment) CreateSettle(ctx context.Context, rpcReq *payment.CreatePOSSettleReq) (bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.CreatePOSSettle(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	return
}

func (payment *TradePayment) CreateGuaranteePay(ctx context.Context, rpcReq *payment.CreateGuaranteePayReq) (payData, payNo string, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.CreateGuaranteePay(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	payData = rpcRsp.PayData
	payNo = rpcRsp.PayOrderNo
	return
}

// Deprecated:  please use MergeSettleV2
func (payment *TradePayment) MergeSettle(ctx context.Context, rpcReq *payment.MergeSettleReq) (bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.MergeSettle(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	return
}

func (payment *TradePayment) MergeSettleV2(ctx context.Context, rpcReq *payment.MergeSettleV2Req) (mergeSettleNo string, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.MergeSettleV2(ctx, rpcReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	mergeSettleNo = rpcRsp.SettleFinanceOrderID
	return
}

func (payment *TradePayment) QueryFinancePayList(ctx context.Context, req *payment.QueryFinancePayListReq) (payList []*payment.FinancePay, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.QueryFinancePayList(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	payList = rpcRsp.FinancePayList
	return
}

func (payment *TradePayment) MergeRefund(ctx context.Context, req *payment.MergeRefundReq) (mergeRefundID string, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.MergeRefund(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	mergeRefundID = rpcRsp.MergeRefundID
	return
}

func (payment *TradePayment) MergeWithdrawDeposit(ctx context.Context, req *payment.MergeWithdrawDepositReq) (
	mergeWithdrawID string, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.MergeWithdrawDeposit(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	mergeWithdrawID = rpcRsp.MergeWithdrawID
	return
}

func (payment *TradePayment) CreateOfflinePay(ctx context.Context, req *payment.CreateOfflinePayReq) (paymentNo string, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.CreateOfflinePay(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}

	paymentNo = rpcRsp.GetPayOrderNo()
	return
}

func (payment *TradePayment) PayRecognition(ctx context.Context, req *payment.PayRecognitionReq) (bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.PayRecognition(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}

	return
}

func (payment *TradePayment) ClosePayOrder(ctx context.Context, req *payment.ClosePayOrderReq) (bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.ClosePayOrder(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}

	return
}

func (payment *TradePayment) CreateUnionPay(ctx context.Context, req *payment.CreateUnionPayReq) (resp *payment.CreateUnionPayResp, bizErr *errdef.BizErr) {

	rpcRsp, err := motorFweTradePaymentClient.CreateUnionPay(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	return rpcRsp, nil
}

func (payment *TradePayment) YztOfflineUpsertAndAcceptance(ctx context.Context, req *payment.YztOfflineUpsertAndAcceptanceRequest) (resp *payment.YztOfflineUpsertAndAcceptanceResponse, bizErr *errdef.BizErr) {

	rpcRsp, err := motorFweTradePaymentClient.YztOfflineUpsertAndAcceptance(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 && rpcRsp.BaseResp.StatusCode != 4099 {
		// 4099  幂等code
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	return rpcRsp, nil
}

func (payment *TradePayment) QueryOrderPaymentList(ctx context.Context, req *payment.QueryOrderPaymentListReq) (resp *payment.QueryOrderPaymentListResp, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.QueryOrderPaymentList(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	return rpcRsp, nil
}

func (payment *TradePayment) QueryOneSuccessPay(ctx context.Context, orderID string, tradeTypeList []string) (*payment.FinanceDetail, *errdef.BizErr) {
	var (
		firstPay *payment2.FinanceDetail
	)
	paymentReq := &payment2.QueryOrderPaymentListReq{
		OrderID: orderID,
		Base:    base.NewBase(),
	}
	paymentResp, bizErr := payment.QueryOrderPaymentList(ctx, paymentReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[QueryOneSuccessPay] query order pay failed, err=%s", bizErr.Error())
		return nil, bizErr
	}
	if paymentResp.FinanceList != nil {
		payList := paymentResp.FinanceList[payment2.TradeCategory_Pay]
		for _, v := range payList {
			if v.Status != fwe_trade_common.CommonStatus_Success {
				continue
			}
			if !slices.Contains(tradeTypeList, v.PayTradeType) {
				continue
			}
			firstPay = v
			break
		}
	}
	return firstPay, nil
}

func (payment *TradePayment) AgreementPay(ctx context.Context, req *payment.AgreementPayReq) *errdef.BizErr {
	rsp, err := motorFweTradePaymentClient.AgreementPay(ctx, req)
	if err != nil {
		bizErr := errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return bizErr
	}
	if rsp.BaseResp.StatusCode != 0 {
		bizErr := errdef.NewRawErr(errdef.PaymentRPCErr, rsp.BaseResp.StatusMessage)
		return bizErr
	}
	return nil
}

func (payment *TradePayment) MergeRefundAfterSettle(ctx context.Context, req *payment.MergeRefundAfterSettleReq) (
	mergeRefundID string, bizErr *errdef.BizErr) {
	rpcRsp, err := motorFweTradePaymentClient.MergeRefundAfterSettle(ctx, req)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.PaymentRPCErr, err, "")
		return
	}
	if rpcRsp.BaseResp.StatusCode != 0 {
		bizErr = errdef.NewPaymentRawErr(rpcRsp.BaseResp.StatusCode, rpcRsp.BaseResp.StatusMessage)
		return
	}
	mergeRefundID = rpcRsp.MergeRefundID
	return
}
