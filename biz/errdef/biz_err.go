package errdef

import (
	"fmt"
)

type errCode int32

const (
	OK errCode = 0

	// 2xxxx payment

	PaymentBaseErr errCode = 20000

	// 3xxxx contract core

	ContractCoreBaseErr errCode = 30000

	// 7xxxx trade_fee error
	TradeFeeBaseErr errCode = 70000

	// 8xxxx finance_account error
	FinanceAccountBaseErr errCode = 800000

	InvoiceAccountBaseErr errCode = 900000

	// 4xxx 请求参数错误

	ParamErr                      errCode = 40001 // 参数错误
	BizSceneNotExist              errCode = 40002 // 业务场景不存在
	ActionNotRegister             errCode = 40003 // 动作未注册
	OrderAlreadyLock              errCode = 40004 // 订单已被锁定 不要重复操作
	OrderStatusCanTrans           errCode = 40005 // 当前action 不能驱动订单变更
	UnknownContractType           errCode = 40006 // 无法识别的合同类型
	LackConfigErr                 errCode = 40007 // 缺少配置
	ContractNotReady              errCode = 40008 // 合同未就绪，还不能签署
	FinanceTypeErr                errCode = 40009 // 资金单类型传错
	AmountNotConsistErr           errCode = 40010 // 传入金额和资金单不一致
	ExistDifferentPayTradeTypeErr errCode = 40011 // 存在不同的支付方式
	SafeCheckNotPassErr           errCode = 40012 // 安全校验未通过
	SmVersionNotRegister          errCode = 40013 // 状态机版本不存在
	ExecutionNotExist             errCode = 40014 // 执行器不存在
	FinanceAccountNotExist        errCode = 40015 // 资金账户不存在

	// 5xxx 业务错误

	OtherErr            errCode = 50000
	ServerException     errCode = 50001
	MysqlException      errCode = 50002
	RedisException      errCode = 50003
	ByteTXException     errCode = 50004
	DirtyDataException  errCode = 50005 // 脏数据错误
	GenOrderIDException errCode = 50006 // 订单ID生成错误
	ReadTccErr          errCode = 50007 // 读tcc错误
	DataErr             errCode = 50008 // 数据错误
	FinanceStatusErr    errCode = 50009 // 资金单状态错误
	IdemErr             errCode = 50010 // 幂等错误
	FinanceFlowErr      errCode = 50020 // 资金流错误
	ShopUIDNotUniqueErr errCode = 50021 // 商户uid不唯一
	LackSplitRoleErr    errCode = 50022 // 缺少分账角色
	DataNotFound        errCode = 50023 // 数据不存在
	ProduceMsgErr       errCode = 50024 // 消息生产错误

	// 6xxx 第三方错误

	ContractRpcErr        errCode = 60001 // 合同服务报错
	PaymentRPCErr         errCode = 60002 // 基建支付服务报错
	AuditRpcErr           errCode = 60003 // 审批服务报错
	FinanceRpcErr         errCode = 60004 // 金融服务报错
	AccountRpcErr         errCode = 60005 // 商户rpc报错
	AccountShopErr        errCode = 60006 // 基建商铺报错
	ProductStockRpcErr    errCode = 60007 // 商品库存服务报错
	SafeRpcErr            errCode = 60008 // safe服务报错
	FinanceAccountRpcErr  errCode = 60009 // 资金账户报错
	OrderRpcErr           errCode = 60010 // 订单服务报错
	TradeFeeRpcErr        errCode = 60011 // 计费服务报错
	ActivityRpcErr        errCode = 60012 // 营销优惠券服务报错
	ProductStockNotEnough errCode = 60013 // 商品库存不足
	InvoiceRpcErr         errCode = 60014 // 发票服务错误
	RegisterResourceErr   errCode = 60015 // 资源注册失败
	DouyinLifeProxyErr    errCode = 60016 // 抖音生服服务报错
	SchedulerErr          errCode = 60017 // 延时调度错误
	CrmWbErr              errCode = 60018 // 销售Crm系统报错

)

type BizErr struct {
	err     error
	code    errCode
	message string
}

func NewBizErr(errCode errCode, err error, msg string) *BizErr {
	return &BizErr{
		code:    errCode,
		err:     err,
		message: msg,
	}
}

func NewBizErrWithCode(code errCode, err error) *BizErr {
	return &BizErr{
		err:     err,
		code:    code,
		message: code.Message(),
	}
}

func NewRawErr(errCode errCode, message string) *BizErr {
	return NewBizErr(errCode, nil, message)
}

func NewPaymentRawErr(code int32, message string) *BizErr {
	return NewBizErr(PaymentBaseErr+errCode(code), nil, message)
}

func NewContractRawErr(code int32, message string) *BizErr {
	return NewBizErr(ContractCoreBaseErr+errCode(code), nil, message)
}

func NewTradeFeeRawErr(code int32, message string) *BizErr {
	return NewBizErr(TradeFeeBaseErr+errCode(code), nil, message)
}

func NewFinanceAccountRawErr(code int32, message string) *BizErr {
	return NewBizErr(FinanceAccountBaseErr+errCode(code), nil, message)
}

func NewInvoiceRawErr(code int32, message string) *BizErr {
	return NewBizErr(InvoiceAccountBaseErr+errCode(code), nil, message)
}

func NewProductStockNotEnoughErr(msg string) *BizErr {
	return NewBizErr(ProductStockNotEnough, nil, msg)
}

func NewParamsErr(msg string) *BizErr {
	return NewBizErr(ParamErr, nil, msg)
}

func NewRpcErr(err error) *BizErr {
	return NewBizErr(ServerException, err, "")
}

func NewDBErr(err error) *BizErr {
	return NewBizErr(MysqlException, err, "")
}

// 只要实现此方法就是 error
func (e *BizErr) Error() string {
	var str string
	if e == nil {
		return str
	}
	if e.err != nil {
		str = fmt.Sprintf("%s", e.err.Error())
	} else {
		str = fmt.Sprintf("%s", e.code.Message())
	}
	if e.message != "" {
		str = fmt.Sprintf("%s msg:%s", str, e.message)
	}
	return str
}

func (e *BizErr) String() string {
	return e.Error()
}

func (e *BizErr) Err() error {
	return e.err
}

func (e *BizErr) Code() int32 {
	return int32(e.code)
}

func (e *BizErr) Message() string {
	return e.message
}

func (e errCode) Message() string {
	switch e {
	case OK:
		return "ok"
	// 4xxx
	case ParamErr:
		return "param error"
	case BizSceneNotExist:
		return "biz scene is not exist"
	case ActionNotRegister:
		return "action is not registered"
	case OrderAlreadyLock:
		return "order already lock"
	case OrderStatusCanTrans:
		return "order can not trans"
	case UnknownContractType:
		return "unknown contract type"
	case LackConfigErr:
		return "lack config"
	case ContractNotReady:
		return "contract not ready"
	case FinanceTypeErr:
		return "finance_type error"
	case AmountNotConsistErr:
		return "amount not consist with financeOrder"
	case SafeCheckNotPassErr:
		return "safe check not pass"
	case ExistDifferentPayTradeTypeErr:
		return "exist different pay trade type error"
	case SmVersionNotRegister:
		return "state machine version not register"
	case ExecutionNotExist:
		return "execution not exist"
	case FinanceAccountNotExist:
		return "finance account not exist"

	// 5xxx
	case OtherErr:
		return "other error"
	case ServerException:
		return "service exception"
	case MysqlException:
		return "mysql exception"
	case RedisException:
		return "redis exception"
	case ByteTXException:
		return "byte tx exception"
	case DirtyDataException:
		return "dirty data exception"
	case GenOrderIDException:
		return "gen order id exception"
	case ReadTccErr:
		return "read tcc error"
	case FinanceStatusErr:
		return "finance order status error"
	case IdemErr:
		return "idempotent error"
	case FinanceFlowErr:
		return "finance flow error"
	case LackSplitRoleErr:
		return "lack split role error"
	case ProduceMsgErr:
		return "produce message error"

	// 6xxx
	case ContractRpcErr:
		return "contract-core rpc error"
	case PaymentRPCErr:
		return "trade-payment rpc error"
	case AuditRpcErr:
		return "audit rpc error"
	case FinanceRpcErr:
		return "finance-core rpc error"
	case AccountRpcErr:
		return "account-shop rpc error"
	case AccountShopErr:
		return "account-shop biz error"
	case ProductStockRpcErr:
		return "product-stock rpc error"
	case SafeRpcErr:
		return "safe rpc error"
	case FinanceAccountRpcErr:
		return "finance account rpc error"
	case OrderRpcErr:
		return "order rpc error"
	case TradeFeeRpcErr:
		return "trade-fee rpc error"
	case ActivityRpcErr:
		return "activity rpc error"
	case ProductStockNotEnough:
		return "product stock not enough"
	case RegisterResourceErr:
		return "register resource error"
	case DouyinLifeProxyErr:
		return "douyin life proxy error"
	}
	return "unknown error"
}
