package packer

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	OrderRpc "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
	"code.byted.org/motor/gopkg/tools"
	"context"

	"code.byted.org/gopkg/lang/maths"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"github.com/bytedance/sonic"
)

func FinanceGetByType(input []*db_model.FFinanceOrder, financeType int32) *db_model.FFinanceOrder {
	for _, v := range input {
		if v == nil {
			continue
		}
		if v.FinanceOrderType == financeType {
			return v
		}
	}
	return nil
}

func FinanceGetByTypes(input []*db_model.FFinanceOrder, financeTypes []int32) []*db_model.FFinanceOrder {
	res := make([]*db_model.FFinanceOrder, 0)
	for _, financeOrder := range input {
		if financeOrder == nil {
			continue
		}
		if slices.Contains(financeTypes, financeOrder.FinanceOrderType) {
			res = append(res, financeOrder)
		}
	}
	return res
}

func FinanceGetByID(input []*db_model.FFinanceOrder, financeOrderID string) *db_model.FFinanceOrder {
	for _, v := range input {
		if v == nil {
			continue
		}
		if v.FinanceOrderID == financeOrderID {
			return v
		}
	}
	return nil
}

func FinanceDB2Common(order *db_model.FFinanceOrder) *fwe_trade_common.FinanceInfo {
	var feeItem []*fwe_trade_common.FeeItem
	if order.FeeItemDetail != nil {
		if err := sonic.UnmarshalString(*order.FeeItemDetail, &feeItem); err != nil {
			feeItem = nil
		}
	}

	res := &fwe_trade_common.FinanceInfo{
		FinanceOrderID:   order.FinanceOrderID,
		FinanceOrderType: order.FinanceOrderType,
		FeeItemList:      feeItem,
		TradeType:        order.TradeType,
		PayStatus:        fwe_trade_common.FinanceStatus(order.Status),
		Amount:           order.Amount,
		StartTime:        order.CreateTime.Unix(),
		FinishTime:       maths.MaxInt64(0, order.FinishTime.Unix()),
		LoanAmount:       order.LoanAmount,
		TradeCategory:    fwe_trade_common.TradeCategory(order.TradeCategory),
	}
	return res
}

func FinanceDBList2CommonList(list ...[]*db_model.FFinanceOrder) []*fwe_trade_common.FinanceInfo {
	res := make([]*fwe_trade_common.FinanceInfo, 0)
	for _, financeOrderList := range list {
		for _, financeOrder := range financeOrderList {
			res = append(res, FinanceDB2Common(financeOrder))
		}
	}
	return res
}

func FinanceCommon2DB(ctx context.Context, input *fwe_trade_common.FinanceInfo, orderID, orderName string, tenantType, bizScene int32) (output *db_model.FFinanceOrder) {
	if input == nil {
		return
	}
	output = &db_model.FFinanceOrder{
		FinanceOrderID:   utils.MakeFinanceOrderIDTool(orderID, input.FinanceOrderType),
		FinanceOrderType: input.FinanceOrderType,
		OrderID:          orderID,
		OrderName:        orderName,
		TradeType:        input.TradeType,
		TenantType:       tenantType,
		BizScene:         bizScene,
		AppID:            "", // todo
		MerchantID:       input.MerchantID,
		Amount:           input.Amount,
		Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
		FeeItemDetail:    conv.StringPtr(tools.GetLogStr(input.FeeItemList)),
	}
	return
}

func FinanceCommonList2DBList(ctx context.Context, input []*fwe_trade_common.FinanceInfo, orderID, orderName string, tenantType, bizScene int32) (output []*db_model.FFinanceOrder) {
	for _, v := range input {
		w := FinanceCommon2DB(ctx, v, orderID, orderName, tenantType, bizScene)
		if w == nil {
			logs.CtxError(ctx, "[FinanceCommonList2DBList] w is nil")
			continue
		}
		output = append(output, w)
	}
	return
}

// Deprecated
func BuildFinanceList(ctx context.Context, order *db_model.FweOrder, conf *model.ConfigWithFinance, input []*fwe_trade_common.FinanceInfo) (output []*db_model.FFinanceOrder, bizErr *errdef.BizErr) {
	if order == nil {
		bizErr = errdef.NewParamsErr("order is nil")
		logs.CtxError(ctx, "[CommonService] err=%v", bizErr.Error())
		return
	}
	for _, v := range input {
		if v == nil {
			logs.CtxError(ctx, "[CommonService] v is nil")
			continue
		}

		tradeType := conf.FinanceTradeTypeMap[v.FinanceOrderType]
		var merchantID string
		if tradeType == consts.TradeTypePayPos.String() {
			merchantID = conf.PosMerchantID
		} else if tradeType == consts.TradeTypePayGuarantee.String() {
			merchantID = conf.GuaranteeMerchantID
		} else {
			merchantID = conf.MerchantID
		}

		output = append(output, &db_model.FFinanceOrder{
			TenantType:       order.TenantType,
			BizScene:         order.BizScene,
			AppID:            conf.AppID,
			MerchantID:       merchantID,
			Mid:              conf.MID,
			OrderID:          order.OrderID,
			OrderName:        order.OrderName,
			TradeType:        tradeType,
			FinanceOrderID:   utils.MakeFinanceOrderIDTool(order.OrderID, v.FinanceOrderType),
			FinanceOrderType: v.FinanceOrderType,
			Amount:           v.Amount,
			ProcessAmount:    0,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
		})
	}
	return
}

func BuildFinanceListV1(ctx context.Context, order *db_model.FweOrder, conf *model.ConfigWithFinance, input []*fwe_trade_common.FinanceInfo) (output []*db_model.FFinanceOrder, bizErr *errdef.BizErr) {
	if order == nil {
		bizErr = errdef.NewParamsErr("order is nil")
		logs.CtxError(ctx, "[CommonService] err=%v", bizErr.Error())
		return
	}
	for _, v := range input {
		if v == nil {
			logs.CtxError(ctx, "[CommonService] v is nil")
			continue
		}

		tradeType := v.TradeType
		if tradeType == "" {
			tradeType = conf.FinanceTradeTypeMap[v.FinanceOrderType]
		}
		merchantID := GetMerchantId(conf, tradeType)

		feeStr, err := utils.Marshal(v.FeeItemList)
		if err != nil {
			logs.CtxError(ctx, "[CommonService] err=%s", err.Error())
		}
		if v.TradeCategory == 0 {
			v.TradeCategory = fwe_trade_common.TradeCategory_Pay
		}

		output = append(output, &db_model.FFinanceOrder{
			FinanceOrderID:   utils.MakeFinanceOrderIDTool(order.OrderID, v.FinanceOrderType),
			FinanceOrderType: v.FinanceOrderType,
			OrderID:          order.OrderID,
			OrderName:        order.OrderName,
			TradeType:        tradeType,
			TradeCategory:    int32(v.TradeCategory),
			TenantType:       order.TenantType,
			BizScene:         order.BizScene,
			AppID:            conf.AppID,
			MerchantID:       merchantID,
			Mid:              conf.MID,
			Amount:           v.Amount,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
			FeeItemDetail:    conv.StringPtr(feeStr),
		})
	}
	return
}

func GetMerchantId(conf *model.ConfigWithFinance, tradeType string) string {
	if tradeType == consts.TradeTypePayPos.String() {
		return conf.PosMerchantID
	} else if tradeType == consts.TradeTypePayGuarantee.String() {
		return conf.GuaranteeMerchantID
	} else if tradeType == consts.TradeTypePayOffline.String() {
		return ""
	} else {
		return conf.MerchantID
	}
}

func FeeItemGetByDetail(feeItemDetail string) ([]*fwe_trade_common.FeeItem, *errdef.BizErr) {
	if feeItemDetail == "" {
		return nil, nil
	}

	var list []*fwe_trade_common.FeeItem
	err := utils.Unmarshal(feeItemDetail, &list)
	if err != nil {
		return nil, errdef.NewRawErr(errdef.DataErr, "parse fee_item_detail failed")
	}

	return list, nil
}

func FeeItemGetByName(feeItems []*fwe_trade_common.FeeItem, feeItemName string) *fwe_trade_common.FeeItem {
	for _, feeItem := range feeItems {
		if feeItem.FeeItemName == feeItemName {
			return feeItem
		}
	}
	return nil
}

func FinanceDBList2OrderThriftList(input []*db_model.FFinanceOrder) (output []*OrderRpc.FinanceOrderData) {
	for _, v := range input {
		w := FinanceDB2OrderThrift(v)
		if w == nil {
			continue
		}
		output = append(output, w)
	}
	return
}

func FinanceDB2OrderThrift(input *db_model.FFinanceOrder) (output *OrderRpc.FinanceOrderData) {
	if input == nil {
		return
	}
	output = &OrderRpc.FinanceOrderData{
		Identity: &fwe_trade_common.BizIdentity{
			TenantType: tenant_base.TenantType(input.TenantType),
			BizScene:   input.BizScene,
			SmVersion:  0,
		},
		FinanceOrderID:          input.FinanceOrderID,
		FinanceOrderType:        input.FinanceOrderType,
		TradeCategory:           fwe_trade_common.TradeCategory(input.TradeCategory),
		OrderID:                 input.OrderID,
		FulfillID:               input.FulfillID,
		OrderName:               &input.OrderName,
		Amount:                  &input.Amount,
		LoanAmount:              &input.LoanAmount,
		Status:                  fwe_trade_common.FinanceStatusPtr(fwe_trade_common.FinanceStatus(input.Status)),
		FinishTime:              nil,
		FeeItemDetail:           nil,
		ProcessAmount:           nil,
		FeeRecordID:             conv.StringPtr(input.FeeRecordID),
		OfflineLoanAmount:       &input.OfflineLoanAmount,
		PlatformPromotionAmount: &input.PlatformPromotionAmount,
		PlatformPromotionDetail: nil,
		AfterSaleID:             nil,
	}
	return
}
