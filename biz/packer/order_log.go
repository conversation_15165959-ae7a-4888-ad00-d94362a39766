package packer

import (
	"code.byted.org/gopkg/lang/conv"
	"context"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

func PackOrderLog(ctx context.Context, oldOrder, newOrder *service_model.Order, operator *fwe_trade_common.OperatorInfo,
	machine *bfsm.FSM, action string, operateDesc string) *db_model.FweOrderLog {
	if newOrder == nil || newOrder.FweOrder == nil {
		logs.CtxError(ctx, "[PackOrderLog] order info is nil")
		return nil
	}

	// afterContent, _ := sonic.MarshalString(newOrder)
	beforeSubStatus, err := utils.Marshal(machine.GetOriginalSubStates())
	if err != nil {
		logs.CtxError(ctx, "[PackOrderLog] MarshalString beforeSubStatus error")
		return nil
	}
	afterSubStatus, err := utils.Marshal(machine.CurSubStates())
	if err != nil {
		logs.CtxError(ctx, "[PackOrderLog] MarshalString afterSubStatus error")
		return nil
	}

	log := &db_model.FweOrderLog{
		OrderID:         newOrder.FweOrder.OrderID,
		Action:          action,
		OperatorID:      "0",
		OperatorName:    "unknown",
		BeforeContent:   nil,
		AfterContent:    newOrder.FweOrder.SnapshotContent,
		BeforeStatus:    int32(machine.GetOriginalState()),
		AfterStatus:     int32(machine.CurState()),
		BeforeSubStatus: &beforeSubStatus,
		AfterSubStatus:  &afterSubStatus,
		LogID:           utils.GetTraceID(ctx),
		OperateTime:     time.Now().Unix(),
		OperateDesc:     conv.StringPtr(operateDesc),
	}
	if oldOrder != nil {
		log.BeforeContent = oldOrder.FweOrder.SnapshotContent
	}
	if operator != nil {
		log.OperatorID = operator.GetOperatorID()
		log.OperatorName = operator.GetOperatorName()
	}
	return log
}

func PackStaticOrderLog(ctx context.Context, oldOrder, newOrder *service_model.Order, operator *fwe_trade_common.OperatorInfo,
	beforeStatus, afterStatus int, action string, operateDesc string) *db_model.FweOrderLog {
	if newOrder == nil || newOrder.FweOrder == nil {
		logs.CtxError(ctx, "[PackOrderLog] order info is nil")
		return nil
	}

	// afterContent, _ := sonic.MarshalString(newOrder)
	log := &db_model.FweOrderLog{
		OrderID:       newOrder.FweOrder.OrderID,
		Action:        action,
		OperatorID:    "0",
		OperatorName:  "unknown",
		BeforeContent: nil, // 暂时不需要
		AfterContent:  nil, // 暂时不需要
		BeforeStatus:  int32(beforeStatus),
		AfterStatus:   int32(afterStatus),
		LogID:         utils.GetTraceID(ctx),
		OperateTime:   time.Now().Unix(),
		OperateDesc:   conv.StringPtr(operateDesc),
	}
	/*if oldOrder != nil {
		beforeContent, _ := sonic.MarshalString(oldOrder)
		log.BeforeContent = conv.StringPtr(beforeContent)
	}*/
	if operator != nil {
		log.OperatorID = operator.GetOperatorID()
		log.OperatorName = operator.GetOperatorName()
	}
	return log
}

func OrderLogDB2Common(input *db_model.FweOrderLog) (output *fwe_trade_common.OrderLog) {
	if input == nil {
		return
	}
	output = &fwe_trade_common.OrderLog{
		OrderID:      input.OrderID,
		BeforeStatus: input.BeforeStatus,
		AfterStatus:  input.AfterStatus,
		Action:       input.Action,
		ArrivalTime:  input.OperateTime,
		OperatorID:   input.OperatorID,
		OperatorName: input.OperatorName,
		LogID:        input.LogID,
	}
	return
}

func OrderLogDBList2CommonList(input []*db_model.FweOrderLog) (output []*fwe_trade_common.OrderLog) {
	for _, v := range input {
		w := OrderLogDB2Common(v)
		if w == nil {
			continue
		}
		output = append(output, w)
	}
	return
}
