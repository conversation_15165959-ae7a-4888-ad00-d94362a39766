package packer

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	OrderRpc "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order"
	"context"
	"encoding/json"

	"code.byted.org/gopkg/logs"
	"github.com/bytedance/sonic"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
)

func OrderDB2ServiceMap(ctx context.Context, orderIDs []string,
	orderList []*db_model.FweOrder, tagList []*db_model.FweOrderTag,
	financeList, refundFinanceList, settleFinanceList, withdrawFinanceList, transferFinanceList []*db_model.FFinanceOrder,
	contList []*db_model.FweOrderContract,
	settleFList []*db_model.FSettleFinanceOrder,
	refundList []*db_model.FRefundFinanceOrder,
) map[string]*service_model.Order {
	var (
		orderMap           = make(map[string]*db_model.FweOrder)
		tagMap             = make(map[string]map[string]string)
		extraMap           = make(map[string]map[string]string)
		financeMap         = make(map[string][]*db_model.FFinanceOrder)
		refundFinanceMap   = make(map[string][]*db_model.FFinanceOrder)
		settleFinanceMap   = make(map[string][]*db_model.FFinanceOrder)
		withdrawFinanceMap = make(map[string][]*db_model.FFinanceOrder)
		transferFinanceMap = make(map[string][]*db_model.FFinanceOrder)
		serviceOrderMap    = make(map[string]*service_model.Order)
		contMap            = make(map[string][]*db_model.FweOrderContract)
		settleFMap         = make(map[string][]*db_model.FSettleFinanceOrder)
		refundFMap         = make(map[string][]*db_model.FRefundFinanceOrder)
	)

	// 整理order
	for _, orderDB := range orderList {
		if orderDB == nil {
			continue
		}
		orderMap[orderDB.OrderID] = orderDB
	}

	// 整理tag
	for _, tagDB := range tagList {
		if tagDB == nil {
			continue
		}
		var (
			tags   = make(map[string]string)
			extras = make(map[string]string)
			err    error
		)
		if tagDB.Tag != nil && *tagDB.Tag != "" {
			err = sonic.UnmarshalString(*tagDB.Tag, &tags)
			if err != nil {
				logs.CtxError(ctx, "[PackServiceOrder]", err)
				continue
			}
			tagMap[tagDB.OrderID] = tags
		}
		if tagDB.BizExtra != nil && *tagDB.BizExtra != "" {
			err = sonic.UnmarshalString(*tagDB.BizExtra, &extras)
			if err != nil {
				logs.CtxError(ctx, "[PackServiceOrder]", err)
				continue
			}
			extraMap[tagDB.OrderID] = extras
		}
	}

	// 整理finance
	for _, financeDB := range financeList {
		if financeDB == nil {
			continue
		}
		financeMap[financeDB.OrderID] = append(financeMap[financeDB.OrderID], financeDB)
	}
	// 整理退款finance
	for _, financeDB := range refundFinanceList {
		if financeDB == nil {
			continue
		}
		refundFinanceMap[financeDB.OrderID] = append(refundFinanceMap[financeDB.OrderID], financeDB)
	}
	// 整理分账finance
	for _, financeDB := range settleFinanceList {
		if financeDB == nil {
			continue
		}
		settleFinanceMap[financeDB.OrderID] = append(settleFinanceMap[financeDB.OrderID], financeDB)
	}
	// 整理出款finance
	for _, financeDB := range withdrawFinanceList {
		if financeDB == nil {
			continue
		}
		withdrawFinanceMap[financeDB.OrderID] = append(withdrawFinanceMap[financeDB.OrderID], financeDB)
	}
	// 整理转账finance
	for _, financeDB := range transferFinanceList {
		if financeDB == nil {
			continue
		}
		transferFinanceMap[financeDB.OrderID] = append(transferFinanceMap[financeDB.OrderID], financeDB)
	}

	// 整理cont
	for _, contDB := range contList {
		if contDB == nil {
			continue
		}
		contMap[contDB.OrderID] = append(contMap[contDB.OrderID], contDB)
	}

	// 整理分账单
	for _, settleFOrder := range settleFList {
		if settleFOrder == nil {
			continue
		}
		settleFMap[settleFOrder.OrderID] = append(settleFMap[settleFOrder.OrderID], settleFOrder)
	}

	// 整理退款单
	for _, refundFOrder := range refundList {
		if refundFOrder == nil {
			continue
		}
		refundFMap[refundFOrder.OrderID] = append(refundFMap[refundFOrder.OrderID], refundFOrder)
	}

	// 总结
	for _, orderID := range orderIDs {
		serviceOrderMap[orderID] = &service_model.Order{
			FweOrder:            orderMap[orderID],
			TagMap:              tagMap[orderID],
			BizExtra:            extraMap[orderID],
			FinanceList:         financeMap[orderID],
			RefundFinanceList:   refundFinanceMap[orderID],
			SettleFinanceList:   settleFinanceMap[orderID],
			WithdrawFinanceList: withdrawFinanceMap[orderID],
			TransferFinanceList: transferFinanceMap[orderID],
			ContList:            contMap[orderID],
			SettleFList:         settleFMap[orderID],
			RefundFList:         refundFMap[orderID],
		}
	}
	return serviceOrderMap
}

func OrderService2Common(orderDB *service_model.Order) (*fwe_trade_common.OrderInfo, *errdef.BizErr) {
	if orderDB.FweOrder == nil {
		return nil, errdef.NewRawErr(errdef.DirtyDataException, "")
	}
	var bizErr *errdef.BizErr
	fweOrder := orderDB.FweOrder
	commonOrder := &fwe_trade_common.OrderInfo{
		Identity: &fwe_trade_common.BizIdentity{
			TenantType: tenant_base.TenantType(fweOrder.TenantType),
			BizScene:   fweOrder.BizScene,
			SmVersion:  fweOrder.SmVersion,
		},
		OrderID:      fweOrder.OrderID,
		OrderStatus:  fweOrder.OrderStatus,
		OrderName:    fweOrder.OrderName,
		OrderDesc:    fweOrder.OrderDesc,
		CreatedTime:  fweOrder.CreateTime.Unix(),
		UpdatedTime:  fweOrder.UpdateTime.Unix(),
		Amount:       fweOrder.TotalAmount,
		CreatorName:  fweOrder.CreatorName,
		OperatorName: fweOrder.OperatorName,
		CreatorID:    fweOrder.Creator,
		OperatorID:   fweOrder.Operator,
		TradeType:    fwe_trade_common.TradeType(fweOrder.TradeType),
		BeforeStatus: fweOrder.BeforeStatus,
		ProductInfo: &fwe_trade_common.ProductInfo{
			ProductType:      fwe_trade_common.ProductType(fweOrder.ProductType),
			ProductID:        fweOrder.ProductID,
			ProductName:      fweOrder.ProductName,
			ProductUnitPrice: fweOrder.ProductUnitPrice,
			ProductQuantity:  int64(fweOrder.ProductQuantity),
			ProductDetail:    ProductDetail2Common(fweOrder.ProductDetail),
			SkuID:            fweOrder.SkuID,
			SkuVersion:       fweOrder.SkuVersion,
			ProductVersion:   fweOrder.ProductVersion,
			ProductExtra:     fweOrder.ProductExtra,
		},
		ContList:    ContDBList2CommonList(orderDB.ContList),
		TradeOption: TradeOption2Common(fweOrder.TradeOption),
		FinanceList: FinanceDBList2CommonList(orderDB.FinanceList, orderDB.SettleFinanceList,
			orderDB.RefundFinanceList, orderDB.WithdrawFinanceList, orderDB.TransferFinanceList),
		SettleFinanceList: SettleDBList2CommonList(orderDB.SettleFList),
		RefundFinanceList: RefundDBList2CommonList(orderDB.RefundFList),
		IsTest:            fweOrder.IsTest == 1,
		OrderTag:          orderDB.TagMap,
		Extra:             orderDB.BizExtra,
		PurchasePlan:      PurchasePlan2Common(fweOrder.PurchasePlan),
	}

	if !fweOrder.FinishTime.IsZero() {
		commonOrder.FinishTime = fweOrder.FinishTime.Unix()
	}
	if fweOrder.BuyerExtra != nil {
		commonOrder.BuyerInfo, bizErr = CommonTradeSubjectDeserialize(fweOrder.BuyerID, *fweOrder.BuyerExtra)
		if bizErr != nil {
			return nil, bizErr
		}
	}
	if fweOrder.SellerExtra != nil {
		commonOrder.SellerInfo, bizErr = CommonTradeSubjectDeserialize(fweOrder.SellerID, *fweOrder.SellerExtra)
		if bizErr != nil {
			return nil, bizErr
		}
	}
	if fweOrder.ServiceProviderExtra != nil {
		commonOrder.ServiceProviderInfo, bizErr = CommonTradeSubjectDeserialize(fweOrder.ServiceProviderID, *fweOrder.ServiceProviderExtra)
		if bizErr != nil {
			return nil, bizErr
		}
	}
	if fweOrder.TalentExtra != nil {
		commonOrder.TalentInfo, bizErr = CommonTradeSubjectDeserialize(fweOrder.TalentID, *fweOrder.TalentExtra)
		if bizErr != nil {
			return nil, bizErr
		}
	}
	if fweOrder.OrderSubStatus != nil {
		subStatusMap := make(map[int32]int32)
		err := utils.Unmarshal(*fweOrder.OrderSubStatus, &subStatusMap)
		if err != nil {
			return nil, errdef.NewBizErr(errdef.DataErr, err, "数据解析失败")
		}
		commonOrder.OrderSubStatus = subStatusMap
	}
	return commonOrder, nil
}

func SettleDBList2CommonList(list []*db_model.FSettleFinanceOrder) []*fwe_trade_common.SettleFinanceInfo {
	res := make([]*fwe_trade_common.SettleFinanceInfo, 0)
	for _, financeOrder := range list {
		res = append(res, SettleFinanceDB2Common(financeOrder))
	}
	return res
}

func SettleFinanceDB2Common(settleFinanceOrder *db_model.FSettleFinanceOrder) *fwe_trade_common.SettleFinanceInfo {
	return &fwe_trade_common.SettleFinanceInfo{
		FinanceOrderID:   settleFinanceOrder.SettleFinanceOrderID,
		FinanceOrderType: settleFinanceOrder.SettleFinanceOrderType,
		PayStatus:        fwe_trade_common.FinanceStatus(settleFinanceOrder.Status),
		Amount:           settleFinanceOrder.Amount,
		StartTime:        settleFinanceOrder.CreateTime.Unix(),
		FinishTime:       settleFinanceOrder.FinishTime.Unix(),
	}
}

func RefundDBList2CommonList(list []*db_model.FRefundFinanceOrder) []*fwe_trade_common.RefundFinanceInfo {
	res := make([]*fwe_trade_common.RefundFinanceInfo, 0)
	for _, financeOrder := range list {
		res = append(res, RefundFinanceDB2Common(financeOrder))
	}
	return res
}

func RefundFinanceDB2Common(refundFinanceOrder *db_model.FRefundFinanceOrder) *fwe_trade_common.RefundFinanceInfo {
	return &fwe_trade_common.RefundFinanceInfo{
		RefundFinanceOrderID:   refundFinanceOrder.RefundFinanceOrderID,
		RefundFinanceOrderType: refundFinanceOrder.RefundFinanceOrderType,
		RefundStatus:           fwe_trade_common.FinanceStatus(refundFinanceOrder.Status),
		Amount:                 refundFinanceOrder.Amount,
		StartTime:              refundFinanceOrder.CreateTime.Unix(),
		FinishTime:             refundFinanceOrder.FinishTime.Unix(),
	}
}

func OrderServiceMap2CommonMap(ctx context.Context, orderMap map[string]*service_model.Order) (map[string]*fwe_trade_common.OrderInfo, *errdef.BizErr) {
	var (
		output      = make(map[string]*fwe_trade_common.OrderInfo)
		commonOrder *fwe_trade_common.OrderInfo
		bizErr      *errdef.BizErr
	)
	for k, v := range orderMap {
		commonOrder, bizErr = OrderService2Common(v)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[OrderServiceMap2CommonMap] OrderService2Common failed, err is %v", bizErr)
			continue
		}
		output[k] = commonOrder
	}
	return output, nil
}

func ProductDetail2Common(productDetail *string) *fwe_trade_common.ProductDetail {
	if productDetail == nil || *productDetail == "" {
		return nil
	}

	var productDetailCommon *fwe_trade_common.ProductDetail
	if err := json.Unmarshal([]byte(*productDetail), &productDetailCommon); err != nil {
		return nil
	}

	return productDetailCommon
}

func TradeOption2Common(tradeOption *string) *fwe_trade_common.TradeOption {
	if tradeOption == nil || *tradeOption == "" {
		return nil
	}
	var tradeOptionCommon *fwe_trade_common.TradeOption
	if err := json.Unmarshal([]byte(*tradeOption), &tradeOptionCommon); err != nil {
		return nil
	}
	return tradeOptionCommon
}

func PurchasePlan2Common(purchasePlanStr *string) *fwe_trade_common.PurchasePlan {
	if purchasePlanStr == nil {
		return nil
	}
	purchasePlan := &fwe_trade_common.PurchasePlan{}
	err := utils.Unmarshal(*purchasePlanStr, purchasePlan)
	if err != nil {
		return nil
	}
	return purchasePlan
}

func OrderDB2BaseInfo(ctx context.Context, input *db_model.FweOrder) (output *fwe_trade_common.OrderBaseInfo) {
	if input == nil {
		return
	}
	output = &fwe_trade_common.OrderBaseInfo{
		Identity: &fwe_trade_common.BizIdentity{
			TenantType: tenant_base.TenantType(input.TenantType),
			BizScene:   input.BizScene,
			SmVersion:  input.SmVersion,
		},
		OrderID:            input.OrderID,
		IdempotentID:       input.IdempotentID,
		OrderStatus:        input.OrderStatus,
		BeforeStatus:       input.BeforeStatus,
		OrderSubStatus:     conv.StringPtrToVal(input.OrderSubStatus, "{}"),
		OrderName:          input.OrderName,
		OrderDesc:          input.OrderDesc,
		TotalAmount:        input.TotalAmount,
		TotalPayAmount:     input.TotalPayAmount,
		TotalSubsidyAmount: input.TotalSubsidyAmount,
		TradeType:          input.TradeType,
		IsTest:             input.IsTest,
		UID:                input.UID,
		MobileID:           input.MobileID,
	}
	return
}

func OrderDB2ProductInfo(ctx context.Context, input *db_model.FweOrder) (output *fwe_trade_common.ProductInfo) {
	if input == nil {
		return
	}
	output = &fwe_trade_common.ProductInfo{
		ProductType:      fwe_trade_common.ProductType(input.ProductType),
		ProductID:        input.ProductID,
		ProductName:      input.ProductName,
		ProductQuantity:  int64(input.ProductQuantity),
		ProductUnitPrice: input.ProductUnitPrice,
		SkuID:            input.SkuID,
		SkuVersion:       input.SkuVersion,
		ProductVersion:   input.ProductVersion,
		ProductDetail:    ProductDetail2Common(input.ProductDetail),
		ProductExtra:     input.ProductExtra,
	}
	return
}

func OrderCondPack(orderID string, input *service_model.UpdateOrderParams) (output *OrderRpc.UpdateOrderCond, bizErr *errdef.BizErr) {
	if input == nil || orderID == "" {
		bizErr = errdef.NewParamsErr("input or order_id is nil")
		return
	}
	output = &OrderRpc.UpdateOrderCond{
		OrderID:     orderID,
		OrderStatus: input.WhereOrderStatus,
	}
	return
}

func OrderUpdatePack(orderID string, input *service_model.UpdateOrderParams) (output *OrderRpc.FweOrderData, bizErr *errdef.BizErr) {
	if input == nil || input.Operator == nil {
		bizErr = errdef.NewParamsErr("operator is nil")
		return
	}
	output = &OrderRpc.FweOrderData{
		Identity:             nil,
		OrderID:              orderID,
		OrderStatus:          input.UpdateOrderStatus,
		OrderSubStatus:       input.UpdateOrderSubStatus,
		BeforeStatus:         input.UpdateBeforeStatus,
		OrderName:            input.UpdateOrderName,
		OrderDesc:            input.UpdateOrderDesc,
		ProductID:            input.UpdateProductID,
		ProductType:          input.UpdateProductType,
		ProductName:          input.UpdateProductName,
		ProductDetail:        input.UpdateProductDetail,
		ProductExtra:         input.UpdateProductExtra,
		ProductVersion:       input.UpdateProductVersion,
		SkuID:                input.UpdateSkuID,
		SkuVersion:           input.UpdateSkuVersion,
		ProductQuantity:      input.UpdateProductQuantity,
		ProductUnitPrice:     input.UpdateProductUnitPrice,
		TotalAmount:          input.UpdateTotalAmount,
		TotalPayAmount:       input.UpdateTotalPayAmount,
		TradeType:            input.UpdateTradeType,
		BuyerID:              input.UpdateBuyerID,
		BuyerExtra:           input.UpdateBuyerExtra,
		SellerID:             input.UpdateSellerID,
		SellerExtra:          input.UpdateSellerExtra,
		ServiceProviderID:    input.UpdateSProviderID,
		ServiceProviderExtra: input.UpdateSProviderExtra,
		TalentID:             input.UpdateTalentID,
		TalentExtra:          input.UpdateTalentExtra,
		FinishTime:           utils.TimePtr2Int64Ptr(input.UpdateFinishTime),
		Operator:             &input.Operator.OperatorID,
		OperatorName:         &input.Operator.OperatorName,
	}
	return
}
