package packer

import (
	"code.byted.org/overpass/motor_dealer_douyin_open_proxy/kitex_gen/motor/dealer/douyin_open_proxy"
	"fmt"
	"strconv"
	"strings"

	"code.byted.org/gopkg/lang/slices"
	"github.com/bytedance/sonic"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

// myConfig 仅需全局初始化一次
var myConfig = sonic.Config{UseNumber: true}.Froze()

func CommonFinanceGetAmountByTypes(input []*fwe_trade_common.FinanceInfo, typeList ...int32) (totalAmount int64) {
	for _, v := range input {
		if v == nil {
			continue
		}
		if len(typeList) == 0 || slices.ContainsInt32(typeList, v.FinanceOrderType) {
			totalAmount += v.Amount
		}
	}
	return
}

func CommonFinanceGetTotalAmount(input []*fwe_trade_common.FinanceInfo) (totalAmount int64) {
	for _, v := range input {
		if v == nil {
			continue
		}
		totalAmount += v.Amount
	}
	return
}

func CommonTradeSubjectIDGet(subject *fwe_trade_common.TradeSubjectInfo) string {
	if subject == nil {
		return ""
	}
	encID := ""
	if subject.SubjectType == fwe_trade_common.TradeSubjectType_Person && subject.PersonInfo != nil {
		if subject.PersonInfo.IDCard != "" || subject.PersonInfo.PersonPhone != "" {
			encIDCard := utils.Encrypt(subject.PersonInfo.IDCard)
			encPhone := utils.Encrypt(subject.PersonInfo.PersonPhone)
			encID = fmt.Sprintf("%s_%s", encIDCard, encPhone)
		} else if subject.PersonInfo.MobileID != nil && *subject.PersonInfo.MobileID > int64(0) {
			encID = strconv.FormatInt(*subject.PersonInfo.MobileID, 10)
		} else if subject.PersonInfo.UID != nil && subject.PersonInfo.AppID != nil {
			encID = fmt.Sprintf("%d_%d", subject.PersonInfo.GetUID(), subject.PersonInfo.GetAppID())
		} else if subject.PersonInfo.UID != nil && subject.PersonInfo.GetUID() > 0 {
			encID = strconv.FormatInt(subject.PersonInfo.GetUID(), 10)
		}
	} else if subject.SubjectType == fwe_trade_common.TradeSubjectType_Company && subject.CompanyInfo != nil {
		encID = utils.Encrypt(subject.CompanyInfo.CreditCode)
	} else if subject.SubjectType == fwe_trade_common.TradeSubjectType_FweMerchant && subject.FweMerchant != nil {
		encID = subject.FweMerchant.FweAccountID
	} else if subject.SubjectType == fwe_trade_common.TradeSubjectType_CarSourcePlatform && subject.CarSourcePlatform != nil {
		encID = subject.CarSourcePlatform.ShopID
	}
	if encID == "" {
		return ""
	}
	return fmt.Sprintf("%s_%s", subject.SubjectType, encID)
}

func CommonTradeSubjectSerialize(subject *fwe_trade_common.TradeSubjectInfo) string {
	if subject == nil {
		return ""
	}
	var res string
	if subject.SubjectType == fwe_trade_common.TradeSubjectType_Person && subject.PersonInfo != nil {
		newPerson := CommonPersonEncrypt(subject.PersonInfo)
		res, _ = sonic.MarshalString(newPerson)
	} else if subject.SubjectType == fwe_trade_common.TradeSubjectType_Company && subject.CompanyInfo != nil {
		newCompany := CommonCompanyEncrypt(subject.CompanyInfo)
		res, _ = sonic.MarshalString(newCompany)
	} else if subject.SubjectType == fwe_trade_common.TradeSubjectType_FweMerchant && subject.FweMerchant != nil {
		res, _ = sonic.MarshalString(subject.FweMerchant)
	} else if subject.SubjectType == fwe_trade_common.TradeSubjectType_CarSourcePlatform && subject.CarSourcePlatform != nil {
		res, _ = sonic.MarshalString(subject.CarSourcePlatform)
	}
	return res
}

func CommonTradeSubjectDeserialize(subjectID string, info string) (subjectInfo *fwe_trade_common.TradeSubjectInfo, bizErr *errdef.BizErr) {
	subjectInfo = &fwe_trade_common.TradeSubjectInfo{
		SubjectID: subjectID,
	}
	subjectIDList := strings.Split(subjectID, "_")
	if len(subjectIDList) == 0 {
		bizErr = errdef.NewParamsErr("subject id err")
		return
	}
	switch subjectIDList[0] {
	case fwe_trade_common.TradeSubjectType_Person.String():
		var person *fwe_trade_common.PersonSubject
		dc := myConfig.NewDecoder(strings.NewReader(info))
		dc.UseNumber()
		err := dc.Decode(&person)
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
			return
		}
		subjectInfo.SubjectType = fwe_trade_common.TradeSubjectType_Person
		subjectInfo.PersonInfo = CommonPersonDecrypt(person)
		return
	case fwe_trade_common.TradeSubjectType_Company.String():
		var company *fwe_trade_common.CompanySubject
		err := sonic.UnmarshalString(info, &company)
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
			return
		}
		subjectInfo.SubjectType = fwe_trade_common.TradeSubjectType_Company
		subjectInfo.CompanyInfo = CommonCompanyDecrypt(company)
		return
	case fwe_trade_common.TradeSubjectType_FweMerchant.String():
		var merchant *fwe_trade_common.FweMerchant
		err := sonic.UnmarshalString(info, &merchant)
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
			return
		}
		subjectInfo.SubjectType = fwe_trade_common.TradeSubjectType_FweMerchant
		subjectInfo.FweMerchant = merchant
		return
	case fwe_trade_common.TradeSubjectType_CarSourcePlatform.String():
		var carSourcePlatform *fwe_trade_common.CarSourcePlatform
		err := sonic.UnmarshalString(info, &carSourcePlatform)
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
			return
		}
		subjectInfo.SubjectType = fwe_trade_common.TradeSubjectType_CarSourcePlatform
		subjectInfo.CarSourcePlatform = carSourcePlatform
		return
	default:
		bizErr = errdef.NewParamsErr("subject type err")
		return
	}
}

/*
 Encrypt
*/

func CommonPersonEncrypt(person *fwe_trade_common.PersonSubject) *fwe_trade_common.PersonSubject {
	return &fwe_trade_common.PersonSubject{
		PersonName:       person.PersonName,
		IDCard:           utils.Encrypt(person.IDCard),
		PersonPhone:      utils.Encrypt(person.PersonPhone),
		UID:              person.UID,
		AppID:            person.AppID,
		MobileID:         person.MobileID,
		ContractAddress:  person.ContractAddress,
		SessionAppID:     person.SessionAppID,
		IDType:           person.IDType,
		SessionUID:       person.SessionUID,
		SessionUserName:  person.SessionUserName,
		SessionUserPhone: person.SessionUserPhone,
		BankInfo:         CommonBankEncrypt(person.BankInfo),
		CrmID:            person.CrmID,
		SaleID:           person.SaleID,
	}
}

func CommonCompanyEncrypt(company *fwe_trade_common.CompanySubject) *fwe_trade_common.CompanySubject {
	return &fwe_trade_common.CompanySubject{
		CompanyName: company.CompanyName,
		CreditCode:  utils.Encrypt(company.CreditCode),
		OwnerName:   utils.Encrypt(company.OwnerName),
		OwnerPhone:  utils.Encrypt(company.OwnerPhone),
		BankInfo:    CommonBankEncrypt(company.BankInfo),
	}
}

func CommonBankEncrypt(bankInfo *fwe_trade_common.BankInfo) *fwe_trade_common.BankInfo {
	if bankInfo == nil {
		return nil
	}
	return &fwe_trade_common.BankInfo{
		AccountNo:   utils.Encrypt(bankInfo.AccountNo),
		AccountName: utils.Encrypt(bankInfo.AccountName),
		BankName:    utils.Encrypt(bankInfo.BankName),
		CnapsCode:   utils.Encrypt(bankInfo.CnapsCode),
		PubPriType:  bankInfo.PubPriType,
	}
}

/*
 Decrypt
*/

func CommonPersonDecrypt(info *fwe_trade_common.PersonSubject) *fwe_trade_common.PersonSubject {
	newPerson := &fwe_trade_common.PersonSubject{
		PersonName:       info.PersonName,
		IDCard:           utils.Decrypt(info.IDCard),
		PersonPhone:      utils.Decrypt(info.PersonPhone),
		UID:              info.UID,
		AppID:            info.AppID,
		MobileID:         info.MobileID,
		ContractAddress:  info.ContractAddress,
		SessionAppID:     info.SessionAppID,
		IDType:           info.IDType,
		SessionUID:       info.SessionUID,
		SessionUserName:  info.SessionUserName,
		SessionUserPhone: info.SessionUserPhone,
		CrmID:            info.CrmID,
		SaleID:           info.SaleID,
	}

	if info.BankInfo != nil {
		newBank := CommonBankDecrypt(info.BankInfo)
		newPerson.BankInfo = newBank
	}
	return newPerson
}

func CommonBankDecrypt(info *fwe_trade_common.BankInfo) *fwe_trade_common.BankInfo {
	return &fwe_trade_common.BankInfo{
		AccountNo:   utils.Decrypt(info.AccountNo),
		AccountName: utils.Decrypt(info.AccountName),
		BankName:    utils.Decrypt(info.BankName),
		CnapsCode:   utils.Decrypt(info.CnapsCode),
		PubPriType:  info.PubPriType,
	}
}

func CommonCompanyDecrypt(info *fwe_trade_common.CompanySubject) *fwe_trade_common.CompanySubject {
	newCompany := &fwe_trade_common.CompanySubject{
		CompanyName: info.CompanyName,
		CreditCode:  utils.Decrypt(info.CreditCode),
		OwnerName:   utils.Decrypt(info.OwnerName),
		OwnerPhone:  utils.Decrypt(info.OwnerPhone),
		// ShopID:      info.ShopID,
		BankInfo: nil,
	}
	if info.BankInfo != nil {
		newCompany.BankInfo = CommonBankDecrypt(info.BankInfo)
	}
	return newCompany
}

func CommonProductGetTotalAmount(productInfo *fwe_trade_common.ProductInfo) (totalAmount int64) {
	return productInfo.GetProductQuantity() * productInfo.GetProductUnitPrice()
}

func CommonOrderSubStatusDeserialize(orderID string, info *string) (map[int]int, *errdef.BizErr) {
	if info == nil {
		return map[int]int{}, nil
	}

	var subStatus map[int]int
	if err := sonic.UnmarshalString(*info, &subStatus); err != nil {
		return nil, errdef.NewBizErr(errdef.DataErr, err, fmt.Sprintf("订单: %s, 订单子状态数据异常", orderID))
	}

	return subStatus, nil
}

func CommonProductDetailSerialize(productDetail *fwe_trade_common.ProductDetail) string {
	str, _ := sonic.MarshalString(productDetail)
	return str
}

func PackItemOrderList(list []*fwe_trade_common.ItemOrder) []*douyin_open_proxy.ItemOrder {
	if len(list) == 0 {
		return nil
	}
	var res []*douyin_open_proxy.ItemOrder
	for _, v := range list {
		res = append(res, &douyin_open_proxy.ItemOrder{
			ItemOrderId: v.ItemOrderID,
		})
	}
	return res
}

func PackVerifyResults(list []*douyin_open_proxy.VerifyResult_) []*fwe_trade_common.VerifyResult_ {
	if len(list) == 0 {
		return nil
	}
	var res []*fwe_trade_common.VerifyResult_
	for _, v := range list {
		res = append(res, &fwe_trade_common.VerifyResult_{
			ItemOrderID:     v.ItemOrderId,
			CertificateCode: v.CertificateCode,
			ResultCode:      v.ResultCode,
			ResultMsg:       v.ResultMsg,
			VerifyTime:      v.VerifyTime,
			VerifyID:        v.VerifyId,
		})
	}
	return res
}
