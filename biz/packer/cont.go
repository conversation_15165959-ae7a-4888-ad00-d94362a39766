package packer

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

func ContDB2Common(contract *db_model.FweOrderContract) *fwe_trade_common.ContInfo {
	commonCont := &fwe_trade_common.ContInfo{
		ContSerial: contract.InfraContSerial,
		ContStatus: fwe_trade_common.CommonStatus(contract.Status),
		ContType:   contract.ContractType,
		SignTime:   contract.SignTime.Unix(),
		ContName:   contract.InfraContName,
		CreateTime: contract.CreateTime.Unix(),
	}
	if commonCont.SignTime <= 0 {
		commonCont.SignTime = 0
	}
	return commonCont
}

func ContDBList2CommonList(list []*db_model.FweOrderContract) []*fwe_trade_common.ContInfo {
	res := make([]*fwe_trade_common.ContInfo, 0)
	for _, contract := range list {
		res = append(res, ContDB2Common(contract))
	}
	return res
}
