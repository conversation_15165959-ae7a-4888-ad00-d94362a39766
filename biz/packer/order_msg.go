package packer

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

func PackEventMsg(order *service_model.Order, idt *fwe_trade_common.BizIdentity, beforeStatus, afterStatus int, action string) *fwe_trade_common.OrderMessage {
	var (
		fweOrder       = order.FweOrder
		commonOrder, _ = OrderService2Common(order)
	)
	msg := &fwe_trade_common.OrderMessage{
		TenantType:        idt.GetTenantType(),
		BizScene:          idt.GetBizScene(),
		SmVersion:         idt.GetSmVersion(),
		Version:           "v1",
		OrderID:           fweOrder.OrderID,
		OrderEvent:        action,
		BeforeOrderStatus: int32(beforeStatus),
		AfterOrderStatus:  int32(afterStatus),
		OrderSource:       commonOrder,
	}
	return msg
}
