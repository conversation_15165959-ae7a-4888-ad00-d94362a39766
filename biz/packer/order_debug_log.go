package packer

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func PackOrderDebugLogService2DB(debugLog *model.OrderDebugLog) *db_model.FweOrderDebugLog {
	return &db_model.FweOrderDebugLog{
		OrderID:         debugLog.OrderID,
		Action:          debugLog.Action,
		LogID:           debugLog.LogID,
		BizRequest:      conv.StringPtr(debugLog.BizRequest),
		BizResponse:     conv.StringPtr(debugLog.BizResponse),
		BeforeStatus:    debugLog.BeforeStatus,
		AfterStatus:     debugLog.AfterStatus,
		BeforeSubStatus: debugLog.BeforeSubStatus,
		AfterSubStatus:  debugLog.AfterSubStatus,
		Success:         conv.BoolToInt32(debugLog.Success),
		OperatedTime:    debugLog.OperatedTime,
	}
}
