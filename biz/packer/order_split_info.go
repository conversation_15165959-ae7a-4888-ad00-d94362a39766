package packer

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/fee"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	common "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"fmt"
)

const (
	feeRoleType = "fee_role_type"
)

func splitInfoCommon2Payment(input *common.TradeSplitUnit, splitMethod common.SplitMethod, shopAccount *shop.FinanceAccount) (output *payment.SplitInfo) {
	if input == nil {
		return nil
	}
	uid := input.SplitUID
	if shopAccount != nil {
		uid = *shopAccount.UID
	}
	output = &payment.SplitInfo{
		UID:         uid,
		UIDType:     consts.GetSplitUIDType(input.SplitUIDType),
		Scale:       int32(input.Scale),
		RoleType:    payment.RoleType(input.SplitUIDType),
		SplitMethod: splitMethod,
		Amount:      input.Amount,
	}
	return
}

func SplitInfoCommonList2PaymentList(input *common.TradeSpiltInfo, shopAccountMap map[string]*shop.FinanceAccount) (output []*payment.SplitInfo, err *errdef.BizErr) {
	for _, v := range input.Detail {
		w := splitInfoCommon2Payment(v, input.SplitMethod, shopAccountMap[v.SplitUID])
		if w == nil {
			continue
		}
		output = append(output, w)
	}
	return
}

func splitInfoCommon2PaymentV2(input *common.TradeSplitUnit, splitMethod common.SplitMethod, subMerchantInfos []*finance_account.SubMerchantInfo) (output *payment.SplitInfo) {
	if input == nil {
		return nil
	}
	uid := input.SplitUID
	if len(subMerchantInfos) != 0 {
		uid = subMerchantInfos[0].UID
	}
	output = &payment.SplitInfo{
		UID:          uid,
		UIDType:      consts.GetSplitUIDType(input.SplitUIDType),
		Scale:        int32(input.Scale),
		RoleType:     payment.RoleType(input.SplitUIDType),
		SplitMethod:  splitMethod,
		Amount:       input.Amount,
		FweAccID:     input.SplitUID,
		Extra:        input.Extra,
		AutoWithdraw: input.AutoWithdraw,
	}
	return
}

func SplitInfoCommonList2PaymentListV2(input *common.TradeSpiltInfo, subMerchantInfoMap map[string][]*finance_account.SubMerchantInfo) (output []*payment.SplitInfo, err *errdef.BizErr) {
	for _, v := range input.Detail {
		w := splitInfoCommon2PaymentV2(v, input.SplitMethod, subMerchantInfoMap[v.SplitUID])
		if w == nil {
			continue
		}
		output = append(output, w)
	}
	return
}

func SplitInfoService2DB(orderID string, splitInfo *common.TradeSpiltInfo) []*db_model.FOrderSplitInfo {
	dbSplitInfos := make([]*db_model.FOrderSplitInfo, 0, len(splitInfo.Detail))
	for _, info := range splitInfo.Detail {
		dbSplitInfos = append(dbSplitInfos, &db_model.FOrderSplitInfo{
			OrderID:      orderID,
			SplitUID:     info.SplitUID,
			SplitUIDType: int32(info.SplitUIDType),
			Scale:        info.Scale,
			Amount:       info.Amount,
			SplitMethod:  int32(splitInfo.SplitMethod),
			RoleID:       info.RoleID,
			RoleType:     info.RoleType,
			SplitDesc:    conv.StringPtr(info.Desc),
			Extra:        conv.StringPtr(info.Extra),
		})
	}

	return dbSplitInfos
}

func SplitInfoDB2Service(infos []*db_model.FOrderSplitInfo) *common.TradeSpiltInfo {
	res := new(common.TradeSpiltInfo)
	serviceSplitInfos := make([]*common.TradeSplitUnit, 0, len(infos))
	for _, info := range infos {
		serviceSplitInfos = append(serviceSplitInfos, &common.TradeSplitUnit{
			SplitUID:     info.SplitUID,
			SplitUIDType: common.SplitUIDType(info.SplitUIDType),
			Scale:        info.Scale,
			Amount:       info.Amount,
			RoleID:       info.RoleID,
			RoleType:     info.RoleType,
			Desc:         conv.StringPtrToVal(info.SplitDesc, ""),
			Extra:        conv.StringPtrToVal(info.Extra, ""),
		})
	}
	if len(infos) > 0 {
		res.SplitMethod = common.SplitMethod(infos[0].SplitMethod)
	}
	res.Detail = serviceSplitInfos
	return res
}

// FeeSplits2PaymentSplits ...
func FeeSplits2PaymentSplits(ctx context.Context, feeResults []*fee.ChargeResult_, fweOrder *db_model.FweOrder, platformSplitUID, rentSplitUID, advanceAccountUID string) (*common.TradeSpiltInfo, *errdef.BizErr) {
	if len(feeResults) == 0 {
		return nil, nil
	}

	sellerInfo, bizErr := CommonTradeSubjectDeserialize(fweOrder.SellerID, *fweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[FeeSplits2PaymentSplits] err=%s", bizErr.Error())
		return nil, bizErr
	}
	details := make([]*common.TradeSplitUnit, 0, len(feeResults))
	for _, feeResult := range feeResults {
		// 金额为0跳过
		if feeResult.Amount == int64(0) {
			continue
		}
		extraMap := make(map[string]string)
		extraMap[feeRoleType] = conv.Int64ToStr(int64(feeResult.RoleType))
		if feeResult.RoleType == common.RoleType_Merchant {
			fweAccountID := GetFinanceAccountId(sellerInfo.FweMerchant.GetFinanceAccountID(), sellerInfo.GetFweMerchant().GetFweAccountID())
			if fweAccountID == "" {
				logs.CtxError(ctx, "[FeeSplits2PaymentSplits] fwe_account_id is empty")
				return nil, errdef.NewRawErr(errdef.DataErr, "lack fwe_account_id")
			}
			desc := fmt.Sprintf("role:%v,accountId:%v,settleAmount: %v", feeResult.RoleType, fweAccountID, feeResult.Amount)
			details = append(details, &common.TradeSplitUnit{
				SplitUID:     fweAccountID,
				SplitUIDType: common.SplitUIDType_Shop,
				RoleID:       "",
				RoleType:     int32(feeResult.RoleType),
				Amount:       feeResult.Amount,
				Scale:        0,
				Desc:         desc,
				Extra:        tools.GetLogStr(extraMap),
				AutoWithdraw: feeResult.NeedAutoWithdraw,
			})
		} else if feeResult.RoleType == common.RoleType_Platform {
			// 校验平台分账uid是否为空
			if platformSplitUID == "" {
				logs.CtxError(ctx, "[FeeSplits2PaymentSplits] platform_split_uid is empty")
				return nil, errdef.NewRawErr(errdef.LackConfigErr, "lack config of platform_split_uid")
			}
			desc := fmt.Sprintf("role:%v,accountId:%v,settleAmount: %v", feeResult.RoleType, platformSplitUID, feeResult.Amount)
			details = append(details, &common.TradeSplitUnit{
				SplitUID:     platformSplitUID,
				SplitUIDType: common.SplitUIDType_Platform,
				RoleID:       "",
				RoleType:     int32(feeResult.RoleType),
				Amount:       feeResult.Amount,
				Scale:        0,
				Desc:         desc,
				Extra:        tools.GetLogStr(extraMap),
				AutoWithdraw: feeResult.NeedAutoWithdraw,
			})
		} else if feeResult.RoleType == common.RoleType_StoreMerchant {
			// 校验平台商家分账uid是否为空
			if sellerInfo.FweMerchant.PlatformMerchant == nil {
				// 校验平台分账uid是否为空
				if platformSplitUID == "" {
					logs.CtxError(ctx, "[FeeSplits2PaymentSplits] platform_split_uid is empty")
					return nil, errdef.NewRawErr(errdef.LackConfigErr, "lack config of platform_split_uid")
				}
				desc := fmt.Sprintf("role:%v,accountId:%v,settleAmount: %v", feeResult.RoleType, platformSplitUID, feeResult.Amount)
				extraMap[feeRoleType] = conv.Int64ToStr(int64(common.RoleType_Platform))
				details = append(details, &common.TradeSplitUnit{
					SplitUID:     platformSplitUID,
					SplitUIDType: common.SplitUIDType_Platform,
					RoleID:       "",
					RoleType:     int32(feeResult.RoleType),
					Amount:       feeResult.Amount,
					Scale:        0,
					Desc:         desc,
					Extra:        tools.GetLogStr(extraMap),
					AutoWithdraw: feeResult.NeedAutoWithdraw,
				})
			} else {
				storeMerchantId := *sellerInfo.FweMerchant.PlatformMerchant
				if storeMerchantId == "" {
					logs.CtxError(ctx, "[FeeSplits2PaymentSplits] storeMerchantId is empty")
					return nil, errdef.NewRawErr(errdef.DataErr, "lack storeMerchantId")
				}
				desc := fmt.Sprintf("role:%v,accountId:%v,settleAmount: %v", feeResult.RoleType, storeMerchantId, feeResult.Amount)
				details = append(details, &common.TradeSplitUnit{
					SplitUID:     storeMerchantId,
					SplitUIDType: common.SplitUIDType_Shop,
					RoleID:       "",
					RoleType:     int32(feeResult.RoleType),
					Amount:       feeResult.Amount,
					Scale:        0,
					Desc:         desc,
					Extra:        tools.GetLogStr(extraMap),
					AutoWithdraw: feeResult.NeedAutoWithdraw,
				})
			}
		} else if feeResult.RoleType == common.RoleType_RentCompany {
			// 校验融租公司分账uid
			if rentSplitUID == "" {
				logs.CtxError(ctx, "[FeeSplits2PaymentSplits] rent_split_uid is empty")
				return nil, errdef.NewRawErr(errdef.LackConfigErr, "lack config of rent_split_uid")
			}
			desc := fmt.Sprintf("role:%v,accountId:%v,settleAmount: %v", feeResult.RoleType, rentSplitUID, feeResult.Amount)
			details = append(details, &common.TradeSplitUnit{
				SplitUID:     rentSplitUID,
				SplitUIDType: common.SplitUIDType_Shop,
				RoleID:       "",
				RoleType:     int32(feeResult.RoleType),
				Amount:       feeResult.Amount,
				Scale:        0,
				Desc:         desc,
				Extra:        tools.GetLogStr(extraMap),
				AutoWithdraw: feeResult.NeedAutoWithdraw,
			})
		} else if feeResult.RoleType == common.RoleType_ServiceProvider {
			// 校验订单中是否存在服务商id
			if fweOrder.ServiceProviderID == "" || fweOrder.ServiceProviderExtra == nil || *fweOrder.ServiceProviderExtra == "" {
				bizErr = errdef.NewRawErr(errdef.LackSplitRoleErr, "lack serviceProvider in order")
				logs.CtxError(ctx, "[FeeSplits2PaymentSplits] err=%s", bizErr.Error())
				return nil, bizErr
			}
			var serviceProvider *common.TradeSubjectInfo
			serviceProvider, bizErr = CommonTradeSubjectDeserialize(fweOrder.ServiceProviderID, *fweOrder.ServiceProviderExtra)
			if bizErr != nil {
				logs.CtxError(ctx, "[FeeSplits2PaymentSplits] CommonTradeSubjectDeserialize failed, err=%s", bizErr.Error())
				return nil, bizErr
			}
			accountID := GetFinanceAccountId(serviceProvider.FweMerchant.GetFinanceAccountID(), serviceProvider.FweMerchant.GetFweAccountID())
			desc := fmt.Sprintf("role:%v,accountId:%v,settleAmount: %v", feeResult.RoleType, accountID, feeResult.Amount)
			details = append(details, &common.TradeSplitUnit{
				SplitUID:     accountID,
				SplitUIDType: common.SplitUIDType_Shop,
				Amount:       feeResult.Amount,
				RoleType:     int32(feeResult.RoleType),
				Desc:         desc,
				Extra:        tools.GetLogStr(extraMap),
				AutoWithdraw: feeResult.NeedAutoWithdraw,
			})
		} else if feeResult.RoleType == common.RoleType_AdvanceAccount {
			// 校验垫资账户分账uid
			if advanceAccountUID == "" {
				logs.CtxError(ctx, "[FeeSplits2PaymentSplits] advanceAccountUID is empty")
				return nil, errdef.NewRawErr(errdef.LackConfigErr, "lack config of advanceAccountUID")
			}
			desc := fmt.Sprintf("role:%v,accountId:%v,settleAmount: %v", feeResult.RoleType, advanceAccountUID, feeResult.Amount)
			details = append(details, &common.TradeSplitUnit{
				SplitUID:     advanceAccountUID,
				SplitUIDType: common.SplitUIDType_Platform,
				RoleID:       "",
				RoleType:     int32(feeResult.RoleType),
				Amount:       feeResult.Amount,
				Scale:        0,
				Desc:         desc,
				Extra:        tools.GetLogStr(extraMap),
				AutoWithdraw: feeResult.NeedAutoWithdraw,
			})

		} else if feeResult.RoleType == common.RoleType_Talent {
			// 校验订单中是否存在服务商id
			if fweOrder.TalentID == "" || fweOrder.TalentExtra == nil || *fweOrder.TalentExtra == "" {
				bizErr = errdef.NewRawErr(errdef.LackSplitRoleErr, "lack TalentExtra in order")
				logs.CtxError(ctx, "[FeeSplits2PaymentSplits] err=%s", bizErr.Error())
				return nil, bizErr
			}
			talentInfo, bizErr := CommonTradeSubjectDeserialize(fweOrder.TalentID, *fweOrder.TalentExtra)
			if bizErr != nil {
				logs.CtxError(ctx, "[FeeSplits2PaymentSplits] CommonTradeSubjectDeserialize failed, err=%s", bizErr.Error())
				return nil, bizErr
			}
			fweAccountID := GetFinanceAccountId(talentInfo.FweMerchant.GetFinanceAccountID(), talentInfo.GetFweMerchant().GetFweAccountID())
			if fweAccountID == "" {
				logs.CtxError(ctx, "[FeeSplits2PaymentSplits] fwe_account_id is empty")
				return nil, errdef.NewRawErr(errdef.DataErr, "lack fwe_account_id")
			}
			desc := fmt.Sprintf("role:%v,accountId:%v,settleAmount: %v", feeResult.RoleType, fweAccountID, feeResult.Amount)
			details = append(details, &common.TradeSplitUnit{
				SplitUID:     fweAccountID,
				SplitUIDType: common.SplitUIDType_Shop,
				RoleID:       "",
				RoleType:     int32(feeResult.RoleType),
				Amount:       feeResult.Amount,
				Scale:        0,
				Desc:         desc,
				Extra:        tools.GetLogStr(extraMap),
				AutoWithdraw: feeResult.NeedAutoWithdraw,
			})
		}
	}
	res := &common.TradeSpiltInfo{
		SplitMethod: common.SplitMethod_ByAmount,
		Detail:      details,
	}
	return res, nil
}

func GetFinanceAccountId(financeAccountID string, fweAccountID string) string {
	if financeAccountID != "" {
		return financeAccountID
	}
	return fweAccountID
}

func FeeRefunds2Payment(ctx context.Context, refunds []*fee.SingleRefund) []*payment.SingleRefundV2 {
	if len(refunds) == 0 {
		return nil
	}
	res := make([]*payment.SingleRefundV2, 0, len(refunds))
	for _, refund := range refunds {
		res = append(res, &payment.SingleRefundV2{
			OutPayUnionNo:    refund.FinanceOrderID,
			Amount:           refund.Amount,
			YztOfflineAmount: refund.YztOfflineAmount,
			JstRefundList:    refund.JstRefundList,
		})
	}
	return res
}
