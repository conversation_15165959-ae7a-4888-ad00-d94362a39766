package executor

import (
	"context"
	"fmt"

	"code.byted.org/aurora/block"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metrics"
	"code.byted.org/motor/gopkg/tools/tools_recover_kite"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
)

type Executor struct{}

func NewExecutor() *Executor {
	return &Executor{}
}
func (e *Executor) Exec(ctx context.Context, execution IExecution, tags ...metrics.T) (interface{}, error) {
	result, err := func() (interface{}, error) {
		timerAll := utils.NewTimer("executor.execute.latency", tags...)
		defer timerAll.Finish()
		logs.CtxInfo(ctx, "[Executor] execution name = %s", execution.Name())
		if err := execution.Init(ctx); err != nil {
			logs.CtxError(ctx, "execution=%s init step failed error=%v", execution.Name(), err)
			tags = append(tags, metrics.T{Name: "fail_stage", Value: "Init"})
			return execution.Result(), err
		}
		if err := execution.CheckParams(ctx); err != nil {
			logs.CtxError(ctx, "execution=%s check param step failed error=%v", execution.Name(), err)
			tags = append(tags, metrics.T{Name: "fail_stage", Value: "CheckParams"})
			return execution.Result(), err
		}
		if len(execution.LockTarget()) > 0 {
			locker, err := block.NewLock(execution.LockTarget())
			if err != nil {
				logs.CtxError(ctx, "[Executor] unlock lock failed err = %v, lock = %s", err, locker.LockKey())
				tags = append(tags, metrics.T{Name: "fail_stage", Value: "NewLock"})
				return execution.Result(), err
			}
			success, err := locker.TryLock(ctx, execution.LockTimeout())
			if err != nil {
				logs.CtxError(ctx, "[Executor] try lock failed error=%v", err)
				tags = append(tags, metrics.T{Name: "fail_stage", Value: "TryLock"})
				return execution.Result(), err
			}
			if !success {
				logs.CtxWarn(ctx, "[Executor] try lock failed lock already lock key：%s", execution.LockTarget())
				tags = append(tags, metrics.T{Name: "fail_stage", Value: "TryLock"})
				return execution.Result(), errdef.NewRawErr(errdef.OrderAlreadyLock, "")
			}
			defer func() {
				err = locker.UnLock(ctx)
				if err != nil {
					tags = append(tags, metrics.T{Name: "fail_stage", Value: "UnLock"})
					logs.CtxError(ctx, "[Executor] unlock lock failed err = %v, lock = %s", err, locker.LockKey())
				}
			}()
		}
		if err := execution.PreProcess(ctx); err != nil {
			logs.CtxError(ctx, "execution=%s pre process step failed error=%v", execution.Name(), err)
			tags = append(tags, metrics.T{Name: "fail_stage", Value: "PreProcess"})
			return execution.Result(), err
		}
		txFunc := func(byteCtx context.Context) (interface{}, error) {
			if err := execution.Process(byteCtx); err != nil {
				logs.CtxError(byteCtx, "execution=%s process step failed error=%v", execution.Name(), err)
				tags = append(tags, metrics.T{Name: "fail_stage", Value: "Process"})
				return execution.Result(), err
			}
			if err := execution.PostProcess(byteCtx); err != nil {
				logs.CtxError(ctx, "execution=%s post process step failed error=%v", execution.Name(), err)
				tags = append(tags, metrics.T{Name: "fail_stage", Value: "PostProcess"})
				return execution.Result(), err
			}
			return execution.Result(), nil
		}
		//if execution.OpenTX() {
		//	result, err := bytetx.Execute(ctx, execution.Name(), txFunc, bytetx.WithCommitMode(bytetx.Synchronous))
		//	if err != nil {
		//		logs.CtxError(ctx, "byte tx execute failed error=%v", err)
		//		return result, errdef.NewRawErr(errdef.ByteTXException, "")
		//	}
		//	if result != nil {
		//		return result.GetResult()
		//	}
		//	return nil, nil
		//} else {
		return txFunc(ctx)
		//}
	}()

	utils.EmitCounter("executor.execute.throughput", 1, tags...)
	if err != nil {
		utils.EmitCounter("executor.execute.failed", 1, tags...)
	} else {
		utils.EmitCounter("executor.execute.success", 1, tags...)
	}

	// 异步存储debugLog日志
	go func() {
		defer func() {
			tools_recover_kite.CheckRecover(ctx, recover(), nil)
		}()
		debugLog := execution.DebugLog(ctx)
		if debugLog == nil {
			return
		}

		if err == nil {
			debugLog.Success = true
			debugLog.BizResponse = conv.StringDefault(result, "")
		} else {
			debugLog.Success = false
			debugLog.BizResponse = fmt.Sprintf("Err: %s", err.Error())
		}

		// 错误日志 以及打点
		if bizErr := service.NewFweOrderDebugLog().WriteOrderLog(ctx, debugLog); bizErr != nil {
			logs.CtxWarn(ctx, "[Executor] WriteDebugLog failed, err is %+v", bizErr)
			utils.EmitCounter("executor.debuglog.failed", 1, tags...)
		} else {
			utils.EmitCounter("executor.debuglog.success", 1, tags...)
		}
	}()

	return result, err
}
