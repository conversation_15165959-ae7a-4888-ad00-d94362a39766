package executor

import (
	"context"
	"time"

	"code.byted.org/motor/fwe_trade_engine/biz/model"
)

type IDistributedLock interface {
	LockTarget() string
	LockTimeout() time.Duration
}

type ITransaction interface {
	OpenTX() bool
}

type IExecution interface {
	Init(ctx context.Context) error        // 初始化参数 配置
	CheckParams(ctx context.Context) error // 参数校验
	PreProcess(ctx context.Context) error  // 加载订单数据
	Process(ctx context.Context) error
	PostProcess(ctx context.Context) error
	// ITransaction
	IDistributedLock
	Name() string
	Result() interface{}
	DebugLog(ctx context.Context) *model.OrderDebugLog
}
