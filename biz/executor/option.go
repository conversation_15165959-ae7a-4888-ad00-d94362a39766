package executor

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
)

type OptionID int64

const (
	OptionAutoFire      OptionID = 1 + iota // 自动驱动订单状态
	OptionAutoFinish                        // 自动更新结束时间
	OptionStaticSendMsg                     // 静态修改发送订单事件消息
)

type Option struct {
	OptionID      OptionID
	Params        interface{}
	ConditionFunc ConditionFunc
}

// Params 定义

type ConditionFunc func(ctx context.Context) (condition map[string]interface{}, bizErr *errdef.BizErr)
