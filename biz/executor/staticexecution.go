package executor

import (
	"code.byted.org/motor/gopkg/tools"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"code.byted.org/gopkg/ctxvalues"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

/* 订单操作无状态基类 */

type StaticBaseExecution struct {
	*BaseExecution
	bizIdentity    *fwe_trade_common.BizIdentity
	actionOrderReq *engine.ActionOrderReq
	orderID        string
	confPtr        interface{}
	inputPtr       interface{}
	order          *service_model.Order
	OptionList     []*Option
}

func NewStaticBaseExecution(ctx context.Context, req *engine.ActionOrderReq, confPtr, inputPtr interface{}) *StaticBaseExecution {
	e := &StaticBaseExecution{
		BaseExecution:  NewBaseExecution(ctx, req.GetAction(), req.GetIdentity()),
		actionOrderReq: req,
		orderID:        req.GetOrderID(),
		bizIdentity:    req.GetIdentity(),
		confPtr:        confPtr,
		inputPtr:       inputPtr,
	}
	e.AppendOption(&Option{OptionID: OptionStaticSendMsg})
	return e
}

func NewStaticBaseNoMsgExecution(ctx context.Context, req *engine.ActionOrderReq, confPtr, inputPtr interface{}) *StaticBaseExecution {
	e := &StaticBaseExecution{
		BaseExecution:  NewBaseExecution(ctx, req.GetAction(), req.GetIdentity()),
		actionOrderReq: req,
		orderID:        req.GetOrderID(),
		bizIdentity:    req.GetIdentity(),
		confPtr:        confPtr,
		inputPtr:       inputPtr,
	}
	return e
}

func (e *StaticBaseExecution) LockTarget() string {
	return e.orderID
}

// Init 配置获取、参数序列化
func (e *StaticBaseExecution) Init(ctx context.Context) error {
	if e.confPtr != nil {
		conf, err := service.NewConfigService().GetConfig(ctx, e.GetBizIdentity())
		if err != nil {
			logs.CtxError(ctx, "[StaticBaseExecution] configService get conf failed error=%v", err)
			return err
		}
		logs.CtxInfo(ctx, "[StaticBaseExecution] configService get conf=%s", string(conf))
		if len(conf) > 0 {
			err = json.Unmarshal(conf, e.confPtr)
			if err != nil {
				logs.CtxError(ctx, "[StaticBaseExecution] configService Unmarshal conf failed error=%v", err)
				return err
			}
		}
	}
	if e.inputPtr != nil && e.actionOrderReq.GetBizRequest() != "" {
		logs.CtxInfo(ctx, "[StaticBaseExecution] configService get input=%s", e.actionOrderReq.GetBizRequest())
		err := json.Unmarshal([]byte(e.actionOrderReq.GetBizRequest()), e.inputPtr)
		if err != nil {
			logs.CtxError(ctx, "[StaticBaseExecution] Unmarshal input failed error=%v", err)
			return err
		}
	}
	return nil
}

// PreProcess 订单数据获取，场景校验
func (e *StaticBaseExecution) PreProcess(ctx context.Context) error {
	var bizErr *errdef.BizErr
	// load order data
	e.order, bizErr = service.NewOrderService().GetOrderByID(ctx, e.orderID, &service.OrderOption{OptionID: service.OptionNeedFinance})
	if bizErr != nil {
		logs.CtxError(ctx, "[ActionBaseExecution] err=%s", bizErr.Error())
		return bizErr
	}
	if e.order == nil || e.order.FweOrder == nil || e.order.FweOrder.OrderID == "" {
		return errdef.NewParamsErr("orderID error, can not find order")
	}
	logs.CtxInfo(ctx, "[ActionBaseExecution] origin order status=%v action=%v bizScene=%v",
		e.order.FweOrder.OrderStatus, e.action, e.bizIdentity.GetBizScene())

	bizScene := e.bizIdentity.GetBizScene()
	if bizScene != e.order.FweOrder.BizScene {
		bizErr = errdef.NewParamsErr(fmt.Sprintf("订单: %s 交易场景: %d 和 identity 交易场景: %d 不符合",
			e.actionOrderReq.OrderID, e.order.FweOrder.BizScene, bizScene))
		logs.CtxError(ctx, "[ActionBaseExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

// PostProcess 日志、消息
func (e *StaticBaseExecution) PostProcess(ctx context.Context) error {

	// load new order data
	var opts []*service.OrderOption
	opts = append(opts, &service.OrderOption{OptionID: service.OptionNeedFinance})
	opts = append(opts, &service.OrderOption{OptionID: service.OptionNeedCont})
	newOrder, bizErr := service.NewOrderService().GetOrderByID(ctx, e.orderID, opts...)
	if bizErr != nil {
		logs.CtxError(ctx, "[ActionBaseExecution.PostProcess] get new order, err=%+v", bizErr.Error())
		return bizErr
	}

	// event message
	orderStatus := int(newOrder.FweOrder.OrderStatus)
	if option := e.ContainOption(e.OptionList, OptionStaticSendMsg); option != nil { // 发送订单事件
		e.BaseExecution.AppendEventMessage(&model.EventMessage{
			PartitionKey: e.orderID,
			Tag:          fmt.Sprintf("%d", e.bizIdentity.GetBizScene()),
			Message:      packer.PackEventMsg(newOrder, e.bizIdentity, orderStatus, orderStatus, e.action),
		})
	}

	// event log
	operator := e.actionOrderReq.GetOperator()
	log := packer.PackStaticOrderLog(ctx, e.order, newOrder, operator, orderStatus, orderStatus, e.action, e.actionOrderReq.OperateDesc)
	e.BaseExecution.AppendEventLog(log)

	if err := e.BaseExecution.PostProcess(ctx); err != nil {
		return err
	}
	return nil
}

func (e *StaticBaseExecution) GetActionOrderReq() *engine.ActionOrderReq {
	return e.actionOrderReq
}

func (e *StaticBaseExecution) GetOrder() *service_model.Order {
	return e.order
}

func (e *StaticBaseExecution) DebugLog(ctx context.Context) *model.OrderDebugLog {
	if e.GetOrder() == nil {
		return nil
	}
	return &model.OrderDebugLog{
		OrderID:      e.orderID,
		Action:       e.action,
		LogID:        ctxvalues.LogIDDefault(ctx),
		BizRequest:   tools.GetLogStr(e.actionOrderReq),
		BeforeStatus: e.GetOrder().FweOrder.OrderStatus,
		AfterStatus:  e.GetOrder().FweOrder.OrderStatus,
		OperatedTime: time.Now(),
	}
}

func (e *StaticBaseExecution) AppendOption(option *Option) {
	e.OptionList = append(e.OptionList, option)
}
