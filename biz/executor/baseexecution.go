package executor

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/types"

	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type BaseExecution struct {
	action      string
	bizIdentity *fwe_trade_common.BizIdentity
	// 消息相关
	eventMessageMutex sync.Mutex
	eventMessage      []*model.EventMessage
	// 日志相关
	eventLogMutex sync.Mutex
	eventLog      []*db_model.FweOrderLog
}

func NewBaseExecution(ctx context.Context, action string, bizIdentity *fwe_trade_common.BizIdentity) *BaseExecution {
	return &BaseExecution{
		action:            action,
		bizIdentity:       bizIdentity,
		eventLogMutex:     sync.Mutex{},
		eventLog:          make([]*db_model.FweOrderLog, 0, 5),
		eventMessageMutex: sync.Mutex{},
		eventMessage:      make([]*model.EventMessage, 0, 5),
	}
}

func (e *BaseExecution) Init(ctx context.Context) error {
	return nil
}

func (e *BaseExecution) CheckParams(ctx context.Context) error {
	return nil
}

func (e *BaseExecution) PreProcess(ctx context.Context) error {
	return nil
}

func (e *BaseExecution) Process(ctx context.Context) error {
	/*if len(e.nodeList) == 0 {
		return nil
	}
	for index, items := range e.nodeList {
		logs.CtxInfo(ctx, "[FSMExecution] action:%s round:%d process start", e.action, index)
		g, _ := WithContext(ctx)
		for _, item := range items {
			g.Go(item.Process, ctx)
		}
		if err := g.Wait(); err != nil {
			logs.CtxError(ctx, "[FSMExecution] action:%s round:%d process failed error=%v", e.action, err)
			return err
		}
	}*/
	return nil
}

func (e *BaseExecution) PostProcess(ctx context.Context) error {
	// 订单事件
	if err := e.sendOrderEventMessage(ctx); err != nil {
		logs.CtxError(ctx, "[BaseExecution] err=%v", err.Error())
		return err
	}
	// 订单日志
	if err := e.writeOrderLog(ctx); err != nil {
		logs.CtxError(ctx, "[BaseExecution] err=%v", err.Error())
		return err
	}
	return nil
}

func (e *BaseExecution) LockTarget() string {
	return ""
}

func (e *BaseExecution) LockTimeout() time.Duration {
	return 5 * time.Second
}

func (e *BaseExecution) OpenTX() bool {
	return false
}

func (e *BaseExecution) Name() string {
	return fmt.Sprintf("%d_%s", e.bizIdentity.GetBizScene(), e.action)
}

func (e *BaseExecution) Result() interface{} {
	return nil
}

func (e *BaseExecution) AppendEventMessage(message *model.EventMessage) {
	e.eventMessageMutex.Lock()
	e.eventMessage = append(e.eventMessage, message)
	e.eventMessageMutex.Unlock()
	return
}

func (e *BaseExecution) AppendEventLog(log *db_model.FweOrderLog) {
	e.eventLogMutex.Lock()
	e.eventLog = append(e.eventLog, log)
	e.eventLogMutex.Unlock()
	return
}

func (e *BaseExecution) GetBizIdentity() *fwe_trade_common.BizIdentity {
	return e.bizIdentity
}

func (e *BaseExecution) sendOrderEventMessage(ctx context.Context) error {
	if len(e.eventMessage) == 0 {
		return nil
	}
	msgs := make([]*types.Message, 0, len(e.eventMessage))
	for _, item := range e.eventMessage {
		if item.Tag == "" || item.PartitionKey == "" || item.Message == nil {
			logs.CtxError(ctx, "[BaseExecution] PostProcess event message illegal %s", tools.GetLogStr(e.eventMessage))
			continue
		}
		msgStr, err := json.Marshal(item.Message)
		if err != nil {
			logs.CtxError(ctx, "[BaseExecution] PostProcess Marshal order Message failed error=%v", err)
			return err
		}
		msg := types.NewOrderlyMessage(consts.OrderEventTopic, item.PartitionKey, msgStr).WithTag(item.Tag)
		logs.CtxInfo(ctx, "[sendOrderEventMessage] topic = %+v, msgID = %+v", consts.OrderEventTopic, msg.Msg.UniqId)
		msgs = append(msgs, msg)
	}
	sendResp, err := caller.OrderEventProducer.SendBatch(ctx, msgs)
	if err != nil {
		return err
	}
	logs.CtxInfo(ctx, "[BaseExecution] PostProcess send order Message success resp=%v", tools.GetLogStr(sendResp))
	return nil
}

func (e *BaseExecution) writeOrderLog(ctx context.Context) (bizErr *errdef.BizErr) {
	if len(e.eventLog) == 0 {
		return
	}
	bizErr = service.NewFweOrderLog().WriteOrderLogList(ctx, e.eventLog)
	return
}

func (e *BaseExecution) ContainOption(options []*Option, optionID OptionID) *Option {
	for _, v := range options {
		if v == nil {
			continue
		}
		if v.OptionID == optionID {
			return v
		}
	}
	return nil
}

func (e *BaseExecution) DebugLog(ctx context.Context) *model.OrderDebugLog {
	return nil
}

func (e *BaseExecution) MergeCondition(paramMap map[string]interface{}, calMap map[string]interface{}) map[string]interface{} {
	for key, value := range calMap {
		_, ok := paramMap[key]
		if !ok {
			paramMap[key] = value
		}
	}
	return paramMap
}
