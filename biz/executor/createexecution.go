package executor

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/motor_fwe_ecom_product_item/kitex_gen/motor/fwe_ecom/product_item"
	"context"
	"encoding/json"
	"fmt"
	"github.com/tidwall/gjson"
	"time"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

// 订单创建执行体基类

type CreateBaseExecution struct {
	*BaseExecution
	bizIdentity    *fwe_trade_common.BizIdentity
	createOrderReq *engine.CreateOrderReq
	stateMachine   *bfsm.FSM
	productDetail  *product_item.ProductDetail
	skuDetail      *product_item.SpecPriceInfo
	confPtr        interface{}
	orderID        string
}

func NewCreateBaseExecution(ctx context.Context, req *engine.CreateOrderReq, confPtr interface{}) *CreateBaseExecution {
	return &CreateBaseExecution{
		BaseExecution:  NewBaseExecution(ctx, consts.CreateAction, req.GetIdentity()),
		createOrderReq: req,
		bizIdentity:    req.GetIdentity(),
		confPtr:        confPtr,
	}
}

func (e *CreateBaseExecution) Init(ctx context.Context) error {
	// init config
	if e.confPtr != nil {
		conf, err := service.NewConfigService().GetConfig(ctx, e.GetBizIdentity())
		if err != nil {
			logs.CtxError(ctx, "[CreateBaseExecution] configService get conf failed error=%v", err)
			return err
		}
		logs.CtxInfo(ctx, "[CreateBaseExecution] configService get conf=%s", string(conf))
		if len(conf) > 0 {
			err = json.Unmarshal(conf, e.confPtr)
			if err != nil {
				logs.CtxError(ctx, "[CreateBaseExecution] configService Unmarshal conf failed error=%v", err)
				return err
			}
		}
	}
	// 检查幂等
	var createReq = e.createOrderReq
	fweOrder, bizErr := service.NewOrderService().GetOrderByIdemID(ctx, createReq.GetIdemID(), e.bizIdentity.BizScene)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] GetOrderByIdemID error, err = %v", bizErr.Error())
		return bizErr
	}
	if fweOrder != nil && fweOrder.ID != int64(0) {
		e.orderID = fweOrder.OrderID
		return errdef.NewRawErr(errdef.IdemErr, "createOrder idem error")
	}
	// init order id
	orderID, err := utils.MustGenIDStr()
	if err != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] err=%v", err)
		return errdef.NewBizErrWithCode(errdef.GenOrderIDException, err)
	}
	e.orderID = orderID
	return nil
}

func (e *CreateBaseExecution) PreProcess(ctx context.Context) error {
	// load order data
	bizScene := e.bizIdentity.GetBizScene()
	smVersion := e.bizIdentity.GetSmVersion()
	// stateMachine, err := bfsm.NewFSM(strconvh.FormatInt32(bizScene), int(statemachine.OrderInitStatus), nil)
	stateMachine, err := bfsm.NewFSM(statemachine.MakeStateMachineKey(bizScene, smVersion), int(statemachine.OrderInitStatus), nil)
	if err != nil {
		logs.CtxError(ctx, "[CreateBaseExecution] err=%v", err)
		return err
	}
	e.stateMachine = stateMachine
	// 只做订单状态判断，不做驱动
	can := e.stateMachine.Can(e.action)
	if !can {
		return errdef.NewRawErr(errdef.OrderStatusCanTrans, "")
	}

	// 1. 如果创建订单为个人类型且没有传mid 没有传mobile_id + 传了手机号
	if buyerInfo := e.createOrderReq.GetBuyerInfo(); buyerInfo != nil && buyerInfo.SubjectType == fwe_trade_common.TradeSubjectType_Person &&
		buyerInfo.PersonInfo != nil && buyerInfo.PersonInfo.GetMobileID() == 0 && buyerInfo.PersonInfo.GetPersonPhone() != "" {

		mobileID, bizErr := service.NewToutiaoUserService().GetMobileIdByPhone(ctx, e.createOrderReq.GetBuyerInfo().GetPersonInfo().GetPersonPhone())
		if bizErr != nil {
			return bizErr
		}
		e.createOrderReq.BuyerInfo.PersonInfo.SetMobileID(&mobileID)
	}

	// 资金安全风控校验
	if bizErr := e.checkFundRiskOfAmount(ctx); bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] checkFundRiskOfAmount error, err = %v", bizErr.Error())
		return bizErr
	}

	// 使用商品中心库存策略 (扣减库存还是需要在Process中调用)
	if e.needLoadInfraProduct() {
		if e.GetCreateOrderReq().ProductInfo == nil {
			logs.CtxWarn(ctx, "[CreateOrderExecutionV2.PreProcess] lack product_info")
			return errdef.NewParamsErr("lack product_info")
		}

		productID := conv.StrToInt64(e.GetCreateOrderReq().ProductInfo.ProductID, 0)
		skuID := conv.StrToInt64(e.GetCreateOrderReq().ProductInfo.SkuID, 0)

		// 加载商品中心基本数据
		productDetail, bizErr := service.DefaultProductItemService.GetProductItem(ctx, productID, conv.Int64Ptr(skuID))
		if bizErr != nil {
			logs.CtxWarn(ctx, "[CreateOrderExecutionV2.PreProcess] GetProductItem err: %+v", bizErr)
			return bizErr
		}

		if productDetail == nil || len(productDetail.TradeInfo.SpecPrices) == 0 {
			return errdef.NewParamsErr("商品不存在或者已经下架 请刷新后重试")
		}

		if e.createOrderReq.ProductInfo.ProductVersion != 0 && e.createOrderReq.ProductInfo.ProductVersion != productDetail.ProductVersion {
			return errdef.NewParamsErr("商品版本已更新 请刷新后重新下单")
		}

		skuInfo := gslice.Find(productDetail.TradeInfo.SpecPrices, func(sku *product_item.SpecPriceInfo) bool {
			return sku.SkuId == skuID
		}).Value()

		if skuInfo == nil {
			return errdef.NewParamsErr("商品Sku不存在或者已经下架 请刷新后重试")
		}

		// 校验库存
		if e.createOrderReq.TradeOption != nil && e.createOrderReq.TradeOption.UseInfraProductStockStrategy {
			if skuInfo.StockNum < e.createOrderReq.ProductInfo.ProductQuantity {
				return errdef.NewProductStockNotEnoughErr("商品库存不足 请刷新后重试")
			}
		}

		e.productDetail = productDetail
		e.skuDetail = skuInfo
	}
	return nil
}

// 需要加载商品中心商品 / 订单使用商品中心商品
func (e *CreateBaseExecution) needLoadInfraProduct() bool {
	if e.createOrderReq.TradeOption != nil && e.createOrderReq.TradeOption.UseInfraProductStockStrategy {
		return true
	}

	return false
}

func (e *CreateBaseExecution) PostProcess(ctx context.Context) error {
	// load new order data
	options := []*service.OrderOption{{OptionID: service.OptionNeedFinance}}
	order, err := service.NewOrderService().GetOrderByID(ctx, e.orderID, options...)
	if err != nil {
		return err
	}
	// save trade subject
	go func() {
		createReq := e.createOrderReq
		subjects := []*fwe_trade_common.TradeSubjectInfo{
			createReq.BuyerInfo, createReq.SellerInfo, createReq.ServiceProviderInfo, createReq.TalentInfo,
		}
		bizErr := service.NewOrderService().CreateOrUpdateOrderSubject(ctx, subjects)
		if bizErr != nil {
			logs.CtxError(ctx, "[CreateBaseExecution-PostProcess] CreateOrUpdateOrderSubject error, err = %v", bizErr.Error())
			utils.EmitCounter(consts.MetricUpsertOrderSubjectFail, 1)
		}
	}()

	// event message
	e.BaseExecution.AppendEventMessage(&model.EventMessage{
		PartitionKey: e.orderID,
		Tag:          fmt.Sprintf("%d", e.bizIdentity.GetBizScene()),
		Message:      e.packEventMsg(ctx, order),
	})
	// event log
	operator := e.GetCreateOrderReq().GetOperator()
	machine := e.GetStateMachine()
	log := packer.PackOrderLog(ctx, nil, order, operator, machine, e.action, "")
	e.BaseExecution.AppendEventLog(log)
	if err := e.BaseExecution.PostProcess(ctx); err != nil {
		return err
	}
	return nil
}

func (e *CreateBaseExecution) LockTarget() string {
	target := e.orderID
	if e.createOrderReq.GetIdemID() != "" {
		target = fmt.Sprintf("create_order_idem_id|%d|%s", e.bizIdentity.GetBizScene(), e.createOrderReq.GetIdemID())
	}
	return target
}

func (e *CreateBaseExecution) GetCreateOrderReq() *engine.CreateOrderReq {
	return e.createOrderReq
}

func (e *CreateBaseExecution) GetSkuDetail() *product_item.SpecPriceInfo {
	return e.skuDetail
}

func (e *CreateBaseExecution) GetStateMachine() *bfsm.FSM {
	return e.stateMachine
}

func (e *CreateBaseExecution) GetOrderID() string {
	return e.orderID
}

func (e *CreateBaseExecution) OpenTX() bool {
	return true
}

func (e *CreateBaseExecution) DebugLog(ctx context.Context) *model.OrderDebugLog {
	if e.GetStateMachine() == nil {
		return nil
	}
	return &model.OrderDebugLog{
		OrderID:      e.GetOrderID(),
		Action:       e.action,
		LogID:        ctxvalues.LogIDDefault(ctx),
		BizRequest:   tools.GetLogStr(e.createOrderReq),
		BeforeStatus: int32(e.GetStateMachine().GetOriginalState()),
		AfterStatus:  int32(e.GetStateMachine().CurState()),
		OperatedTime: time.Now(),
	}
}

func (e *CreateBaseExecution) Result() interface{} {
	return e.GetOrderID()
}

func (e *CreateBaseExecution) packEventMsg(ctx context.Context, order *service_model.Order) *fwe_trade_common.OrderMessage {
	var (
		fweOrder       = order.FweOrder
		commonOrder, _ = packer.OrderService2Common(order)
	)
	msg := &fwe_trade_common.OrderMessage{
		TenantType:        e.bizIdentity.GetTenantType(),
		BizScene:          e.bizIdentity.GetBizScene(),
		Version:           "v1",
		OrderID:           fweOrder.OrderID,
		OrderEvent:        e.action,
		BeforeOrderStatus: int32(e.stateMachine.GetOriginalState()),
		AfterOrderStatus:  fweOrder.OrderStatus,
		OrderSource:       commonOrder,
		SmVersion:         e.bizIdentity.GetSmVersion(),
	}
	return msg
}

func (e *CreateBaseExecution) FireWithCondition(ctx context.Context, conditionMap map[string]interface{}) (bizErr *errdef.BizErr) {
	logs.CtxInfo(ctx, "[CreateBaseExecution] origin orderId=%v action=%v bizScene=%v condition=%s",
		e.orderID, e.action, e.bizIdentity.GetBizScene(), tools.GetLogStr(conditionMap))
	err := e.stateMachine.Fire(ctx, e.action, conditionMap, nil)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		logs.CtxError(ctx, "[ActionBaseExecution.PreProcess] fire event failed, err=%+v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *CreateBaseExecution) checkFundRiskOfAmount(ctx context.Context) *errdef.BizErr {
	var (
		req             = e.createOrderReq
		testAmountLimit *int64
	)
	// 配置项读取
	if e.confPtr != nil {
		i := gjson.Get(tools.GetLogStr(e.confPtr), "test_order_limit_amount").Int()
		if i > 0 {
			testAmountLimit = conv.Int64Ptr(i)
		}
	}
	// 检查
	bizErr := utils.CheckFundRiskOfAmount(ctx, req.IsTest, req.TotalAmount, testAmountLimit)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CreateBaseExecution.checkFundRiskOfAmount] check fund risk failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
