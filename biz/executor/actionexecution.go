package executor

import (
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"context"
	"encoding/json"
	"fmt"
	"github.com/tidwall/gjson"
	"time"

	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	CommonStatemachine "code.byted.org/motor/fwe_trade_common/statemachine"
	"code.byted.org/motor/gopkg/tools"
	"github.com/bytedance/sonic"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/condition"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

// 订单操作执行体基类

type ActionBaseExecution struct {
	*BaseExecution
	bizIdentity    *fwe_trade_common.BizIdentity
	actionOrderReq *engine.ActionOrderReq
	orderID        string
	stateMachine   *bfsm.FSM
	confPtr        interface{}
	inputPtr       interface{}
	order          *service_model.Order
	OptionList     []*Option
	fsmConditions  map[string]interface{}
}

func NewActionBaseExecution(ctx context.Context, req *engine.ActionOrderReq, confPtr interface{}, inputPtr interface{}, options ...*Option) *ActionBaseExecution {
	e := &ActionBaseExecution{
		BaseExecution:  NewBaseExecution(ctx, req.GetAction(), req.GetIdentity()),
		actionOrderReq: req,
		orderID:        req.GetOrderID(),
		bizIdentity:    req.GetIdentity(),
		confPtr:        confPtr,
		inputPtr:       inputPtr,
		OptionList:     options,
	}
	e.fsmConditions = make(map[string]interface{})
	return e
}

func (e *ActionBaseExecution) Init(ctx context.Context) error {
	if e.confPtr != nil {
		conf, err := service.NewConfigService().GetConfig(ctx, e.GetBizIdentity())
		if err != nil {
			logs.CtxError(ctx, "[ActionBaseExecution] configService get conf failed error=%v", err)
			return err
		}
		logs.CtxInfo(ctx, "[ActionBaseExecution] configService get conf=%s", string(conf))
		if len(conf) > 0 {
			err = json.Unmarshal(conf, e.confPtr)
			if err != nil {
				logs.CtxError(ctx, "[ActionBaseExecution] configService Unmarshal conf failed error=%v", err)
				return err
			}
		}
	}
	if e.inputPtr != nil && e.actionOrderReq.GetBizRequest() != "" {
		logs.CtxInfo(ctx, "[ActionBaseExecution] configService get input=%s", e.actionOrderReq.GetBizRequest())
		err := json.Unmarshal([]byte(e.actionOrderReq.GetBizRequest()), e.inputPtr)
		if err != nil {
			logs.CtxError(ctx, "[ActionBaseExecution] Unmarshal input failed error=%v", err)
			return err
		}
	}
	// 通用驱动参数解析
	if e.actionOrderReq.GetActionCondition() != "" {
		err := json.Unmarshal([]byte(e.actionOrderReq.GetActionCondition()), &e.fsmConditions)
		if err != nil {
			logs.CtxError(ctx, "[ActionBaseExecution] Unmarshal conditionMap failed error=%v", err)
			return err
		}
	}
	return nil
}

func (e *ActionBaseExecution) PreProcess(ctx context.Context) error {
	var (
		err    error
		bizErr *errdef.BizErr
	)

	// load order data
	e.order, bizErr = service.NewOrderService().GetOrderByID(ctx, e.orderID, &service.OrderOption{OptionID: service.OptionNeedFinance})
	if bizErr != nil {
		logs.CtxError(ctx, "[ActionBaseExecution] err=%s", bizErr.Error())
		return bizErr
	}
	if e.order == nil || e.order.FweOrder == nil || e.order.FweOrder.OrderID == "" {
		return errdef.NewParamsErr("orderID error, can not find order")
	}

	bizScene := e.bizIdentity.GetBizScene()
	if bizScene != e.order.FweOrder.BizScene {
		bizErr = errdef.NewParamsErr(fmt.Sprintf("订单: %s 交易场景: %d 和 identity 交易场景: %d 不符合",
			e.actionOrderReq.OrderID, e.order.FweOrder.BizScene, bizScene))
		logs.CtxError(ctx, "[ActionBaseExecution] err=%s", bizErr.Error())
		return bizErr
	}
	smVersion := e.bizIdentity.GetSmVersion()
	if smVersion != e.order.FweOrder.SmVersion {
		bizErr = errdef.NewParamsErr(fmt.Sprintf("订单: %s 交易状态版本: %d 和 identity 交易场景: %d 不符合",
			e.actionOrderReq.OrderID, e.order.FweOrder.SmVersion, smVersion))
		logs.CtxError(ctx, "[ActionBaseExecution] err=%s", bizErr.Error())
		return bizErr
	}

	orderSubStatus, bizErr := packer.CommonOrderSubStatusDeserialize(e.orderID, e.order.FweOrder.OrderSubStatus)
	if bizErr != nil {
		logs.CtxError(ctx, "[ActionBaseExecution] deserialize order subStatus failed, err = %s", bizErr)
		return bizErr
	}

	// init stateMachine
	// e.stateMachine, err = bfsm.NewFSM(strconvh.FormatInt32(bizScene), int(e.order.FweOrder.OrderStatus), orderSubStatus)
	e.stateMachine, err = bfsm.NewFSM(statemachine.MakeStateMachineKey(bizScene, smVersion), int(e.order.FweOrder.OrderStatus), orderSubStatus)

	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		logs.CtxError(ctx, "[ActionBaseExecution] err=%v", bizErr.Error())
		return bizErr
	}

	// 只做订单状态判断，不做驱动
	can := e.stateMachine.Can(e.action)
	if !can {
		errMsg := statemachine.GetCanErrMsg(consts.BizScene(bizScene), statemachine.Status(e.order.FweOrder.OrderStatus))
		bizErr = errdef.NewRawErr(errdef.OrderStatusCanTrans, errMsg)
		logs.CtxError(ctx, "[ActionBaseExecution] err=%v", bizErr.Error())
		return bizErr
	}

	// 使用注册的条件获取方法增加状态机条件
	conditionFunc := condition.NewOrderConditionFuncFactory().GetActionOrderConditionFunc(ctx, e.GetActionOrderReq())
	if conditionFunc != nil {
		calCondition := conditionFunc(e.BuildConditionParam())
		e.fsmConditions = e.MergeCondition(e.fsmConditions, calCondition)
	}

	// 可选自动驱动订单状态
	if option := e.ContainOption(e.OptionList, OptionAutoFire); option != nil {
		var conditionMap map[string]interface{}
		if option.ConditionFunc != nil {
			conditionMap, bizErr = option.ConditionFunc(ctx)
			if bizErr != nil {
				logs.CtxError(ctx, "[ActionBaseExecution] err=%v", bizErr.Error())
				return bizErr
			}
		}
		if len(e.fsmConditions) > 0 {
			conditionMap = e.fsmConditions
		}
		logs.CtxInfo(ctx, "[ActionBaseExecution] condition = %s", tools.GetLogStr(conditionMap))
		err = e.stateMachine.Fire(ctx, e.action, conditionMap, nil)
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
			logs.CtxError(ctx, "[ActionBaseExecution.PreProcess] fire event failed, err=%+v", bizErr.Error())
			return bizErr
		}
	}
	// 测试订单风控检查
	if bizErr = e.CheckFundRisk(ctx); bizErr != nil {
		logs.CtxError(ctx, "[ActionBaseExecution] CheckFundRisk err:%v", bizErr.Error())
		return bizErr
	}
	return nil
}

// Deprecated: SetFSMCondition 不推荐使用
func (e *ActionBaseExecution) SetFSMCondition(conditions map[string]interface{}) {
	for condition, val := range conditions {
		e.fsmConditions[condition] = val
	}
}

func (e *ActionBaseExecution) GetFSMCondition() map[string]interface{} {
	return e.fsmConditions
}

func (e *ActionBaseExecution) FireWithCondition(ctx context.Context, conditionMap map[string]interface{}) (bizErr *errdef.BizErr) {
	logs.CtxInfo(ctx, "[ActionBaseExecution] origin order status=%v action=%v bizScene=%v condition=%s",
		e.order.FweOrder.OrderStatus, e.action, e.bizIdentity.GetBizScene(), tools.GetLogStr(conditionMap))
	err := e.stateMachine.Fire(ctx, e.action, conditionMap, nil)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		logs.CtxError(ctx, "[ActionBaseExecution.PreProcess] fire event failed, err=%+v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *ActionBaseExecution) FireDefault(ctx context.Context) (bizErr *errdef.BizErr) {
	return e.FireWithCondition(ctx, nil)
}

// Deprecated: FireFSM 不推荐使用, please use FireWithCondition
func (e *ActionBaseExecution) FireFSM(ctx context.Context) error {
	err := e.stateMachine.Fire(ctx, e.action, e.fsmConditions, nil)
	if err != nil {
		bizErr := errdef.NewBizErr(errdef.ServerException, err, "")
		logs.CtxError(ctx, "[ActionBaseExecution.FireFSM] fire event failed, err=%+v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *ActionBaseExecution) AppendOption(option *Option) {
	e.OptionList = append(e.OptionList, option)
}

func (e *ActionBaseExecution) PostProcess(ctx context.Context) error {
	// 业务post_process
	if err := e.bizPostProcess(ctx); err != nil {
		return err
	}
	if err := e.BaseExecution.PostProcess(ctx); err != nil {
		return err
	}
	return nil
}

func (e *ActionBaseExecution) bizPostProcess(ctx context.Context) error {

	var updateParams = &service_model.UpdateOrderParams{
		Operator: e.GetActionOrderReq().Operator,
	}

	// 目标状态 callback
	if cbFunc := statemachine.GetDstCallback(ctx, e.bizIdentity.GetBizScene(), e.stateMachine.CurState()); cbFunc != nil {
		if bizErr := cbFunc(ctx, updateParams); bizErr != nil {
			logs.CtxError(ctx, "[ActionBaseExecution.PostProcess] err=%+v", bizErr.Error())
			return bizErr
		}
	}

	// 可选自动驱动变更数据库状态
	if option := e.ContainOption(e.OptionList, OptionAutoFire); option != nil {
		if e.stateMachine.GetOriginalState() != e.stateMachine.CurState() { // 主流程有发生流转
			updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.stateMachine.GetOriginalState()))
			updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.stateMachine.GetOriginalState()))
			updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.stateMachine.CurState()))
		}
		updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(e.stateMachine.CurSubStates()))
	}

	// 可选自动驱动结束时间，废弃
	if option := e.ContainOption(e.OptionList, OptionAutoFinish); option != nil {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	// 根据状态机结束属性来更新订单完成字段
	if e.stateMachine.GetState(e.stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}

	// 落库
	if len(e.OptionList) > 0 {
		if bizErr := service.NewOrderService().UpdateOrder(ctx, e.orderID, updateParams); bizErr != nil {
			logs.CtxError(ctx, "[ActionBaseExecution.PostProcess] update order_status failed, err=%+v", bizErr.Error())
			return bizErr
		}
	}

	// load new order data
	var opts []*service.OrderOption
	opts = append(opts, &service.OrderOption{OptionID: service.OptionNeedFinance})
	opts = append(opts, &service.OrderOption{OptionID: service.OptionNeedCont})
	newOrder, bizErr := service.NewOrderService().GetOrderByID(ctx, e.orderID, opts...)
	if bizErr != nil {
		logs.CtxError(ctx, "[ActionBaseExecution.PostProcess] get new order, err=%+v", bizErr.Error())
		return bizErr
	}

	// event message
	e.BaseExecution.AppendEventMessage(&model.EventMessage{
		PartitionKey: e.orderID,
		Tag:          fmt.Sprintf("%d", e.bizIdentity.GetBizScene()),
		Message:      e.packEventMsg(ctx, newOrder),
	})

	// event log
	operator := e.GetActionOrderReq().GetOperator()
	machine := e.GetStateMachine()
	log := packer.PackOrderLog(ctx, e.order, newOrder, operator, machine, e.action, e.GetActionOrderReq().OperateDesc)
	e.BaseExecution.AppendEventLog(log)

	// 正向订单完成才计收
	if machine.CurState() == CommonStatemachine.OrderSuccessSt.Value() {
		service.NewEAIncome().AsyncCreateIncome(ctx, e.bizIdentity.GetBizScene(), newOrder)
	}

	// 订单逆向 发送逆向消息 motor.fwe_trade.resource_compensate
	if machine.GetState(machine.CurState()).StateType == bfsm.Cancel {
		orderDTO, _ := packer.OrderService2Common(e.order)
		_ = service.ProduceOrderCancelMessage(ctx, &fwe_trade_common.OrderCancelMessage{
			TenantType:  e.bizIdentity.GetTenantType(),
			BizScene:    e.bizIdentity.GetBizScene(),
			SmVersion:   e.bizIdentity.GetSmVersion(),
			Version:     "0",
			OrderID:     e.orderID,
			OrderEvent:  e.action,
			OrderStatus: int32(e.stateMachine.CurState()),
			OrderSource: orderDTO,
		})
	}

	return nil
}

func (e *ActionBaseExecution) LockTarget() string {
	return e.orderID
}

// Deprecated: SetOrderID  不建议设置订单id
func (e *ActionBaseExecution) SetOrderID(orderID string) {
	e.orderID = orderID
	return
}

func (e *ActionBaseExecution) DebugLog(ctx context.Context) *model.OrderDebugLog {
	if e.GetStateMachine() == nil {
		return nil
	}

	beforeSubStatus, err := sonic.MarshalString(e.GetStateMachine().GetOriginalSubStates())
	if err != nil {
		logs.CtxError(ctx, "[ActionBaseExecution-DebugLog] MarshalString beforeSubStatus error")
		return nil
	}
	afterSubStatus, err := sonic.MarshalString(e.GetStateMachine().CurSubStates())
	if err != nil {
		logs.CtxError(ctx, "[ActionBaseExecution-DebugLog] MarshalString afterSubStatus error")
		return nil
	}

	return &model.OrderDebugLog{
		OrderID:         e.orderID,
		Action:          e.action,
		LogID:           ctxvalues.LogIDDefault(ctx),
		BizRequest:      tools.GetLogStr(e.actionOrderReq),
		BeforeStatus:    int32(e.GetStateMachine().GetOriginalState()),
		AfterStatus:     int32(e.GetStateMachine().CurState()),
		BeforeSubStatus: &beforeSubStatus,
		AfterSubStatus:  &afterSubStatus,
		OperatedTime:    time.Now(),
	}
}

func (e *ActionBaseExecution) OpenTX() bool {
	return false
}

func (e *ActionBaseExecution) GetActionOrderReq() *engine.ActionOrderReq {
	return e.actionOrderReq
}

func (e *ActionBaseExecution) GetStateMachine() *bfsm.FSM {
	return e.stateMachine
}

func (e *ActionBaseExecution) GetOrder() *service_model.Order {
	return e.order
}

func (e *ActionBaseExecution) packEventMsg(ctx context.Context, order *service_model.Order) *fwe_trade_common.OrderMessage {
	var (
		fweOrder       = order.FweOrder
		commonOrder, _ = packer.OrderService2Common(order)
	)
	if e.GetActionOrderReq() != nil && e.GetActionOrderReq().Operator != nil {
		commonOrder.OperatorID = e.GetActionOrderReq().Operator.OperatorID
		commonOrder.OperatorName = e.GetActionOrderReq().Operator.OperatorName
	}

	beforeSubStatus := make(map[int32]int32)
	afterSubStatus := make(map[int32]int32)
	for key, value := range e.stateMachine.GetOriginalSubStates() {
		beforeSubStatus[int32(key)] = int32(value)
	}
	for key, value := range e.stateMachine.CurSubStates() {
		afterSubStatus[int32(key)] = int32(value)
	}

	msg := &fwe_trade_common.OrderMessage{
		TenantType:        e.bizIdentity.GetTenantType(),
		BizScene:          e.bizIdentity.GetBizScene(),
		Version:           "v1",
		OrderID:           e.orderID,
		OrderEvent:        e.action,
		BeforeOrderStatus: int32(e.stateMachine.GetOriginalState()),
		AfterOrderStatus:  fweOrder.OrderStatus,
		OrderSource:       commonOrder,
		BeforeSubStatus:   beforeSubStatus,
		AfterSubStatus:    afterSubStatus,
		SmVersion:         e.bizIdentity.SmVersion,
	}
	return msg
}

func (e *ActionBaseExecution) BuildConditionParam() *condition.ActionConditionGetParam {
	return &condition.ActionConditionGetParam{
		ActionOrderReq: e.actionOrderReq,
		Order:          e.order,
	}
}

func (e *ActionBaseExecution) CheckFundRisk(ctx context.Context) *errdef.BizErr {
	var (
		fweOrder        = e.GetOrder().FweOrder
		reqAmount       int64
		testAmountLimit *int64
	)
	// 读请求参数
	if e.inputPtr == nil {
		return nil
	}
	switch v := e.inputPtr.(type) {
	case *execution_common.WithdrawReq: // 标准出款请求
		reqAmount = v.Amount
	case *execution_common.TransferReq: // 标准转账请求
		reqAmount = v.Amount
	default:
		logs.CtxInfo(ctx, "[ActionBaseExecution.CheckFundRisk] unsupported input type")
		return nil
	}
	// 配置项读取
	if e.confPtr != nil {
		i := gjson.Get(tools.GetLogStr(e.confPtr), "test_order_limit_amount").Int()
		if i > 0 {
			testAmountLimit = conv.Int64Ptr(i)
		}
	}
	// 检查
	bizErr := utils.CheckFundRiskOfAmount(ctx, fweOrder.IsTest == 1, reqAmount, testAmountLimit)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[ActionBaseExecution.CheckFundRisk] check fund risk failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
