package middleware

import (
	"context"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/pkg/endpoint"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/gopkg/tools"
)

func LogMiddleware(next endpoint.Endpoint) endpoint.Endpoint {
	return func(ctx context.Context, request interface{}, response interface{}) error {
		ctx = logs.NewNoticeCtx(ctx)
		start := time.Now()
		method := utils.GetMethod(ctx)
		upStream := utils.GetCaller(ctx)
		logs.CtxPushNotice(ctx, "method", method)
		logs.CtxPushNotice(ctx, "caller", upStream)
		logs.CtxPushNotice(ctx, "req", tools.GetLogStr(request))
		err := next(ctx, request, response)
		logs.CtxPushNotice(ctx, "resp", tools.GetLogStr(response))
		logs.CtxPushNotice(ctx, "latency", time.Since(start).Nanoseconds()/1000)
		logs.CtxFlushNotice(ctx)
		return err
	}
}
