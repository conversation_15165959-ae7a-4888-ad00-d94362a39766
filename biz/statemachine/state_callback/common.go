package state_callback

import (
	"context"
	"time"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
)

func OrderFinish(ctx context.Context, input interface{}) (bizErr *errdef.BizErr) {
	if updateOrder, ok := input.(*service_model.UpdateOrderParams); ok && updateOrder != nil {
		updateOrder.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	return
}
