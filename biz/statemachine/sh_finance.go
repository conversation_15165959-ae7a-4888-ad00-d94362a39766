package statemachine

import (
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

// 状态机
const (
	ShFinanceInit          Status = 0   // 待创建
	ShFinanceWaitingReview Status = 1   // 待审核
	ShFinanceWaitingSign   Status = 2   // 待签署
	ShFinanceSigning       Status = 3   // 签署中
	ShFinanceWaitingLoan   Status = 4   // 待放款 可以跳步
	ShFinanceLoaning       Status = 5   // 放款中 可以跳步
	ShFinanceWaitingRepay  Status = 6   // 待还款
	ShFinanceWaitingVerify Status = 7   // 待核销
	ShFinanceRepayFinnish  Status = 100 // 履约完成
	ShFinanceRepayBreak    Status = 101 // 违约
	ShFinanceInvalid       Status = 102 // 已作废
)

var ShFinanceOrderStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		StateNSConf: map[bfsm.NameSpace]map[int]string{bfsm.NameSpace("default"): {
			int(ShFinanceWaitingReview): "待审核",
			int(ShFinanceWaitingSign):   "待签署",
			int(ShFinanceSigning):       "签署中",
			int(ShFinanceWaitingRepay):  "待还款",
			int(ShFinanceWaitingVerify): "待核销",
			int(ShFinanceRepayFinnish):  "履约完成",
			int(ShFinanceInvalid):       "已作废",
		}},
	},
	TransDescList: []bfsm.TransDesc{
		// 创单
		{
			Event: consts.CreateAction,
			Src:   []int{int(OrderInitStatus)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ShFinanceWaitingReview),
				},
			},
		},
		// 初审通过
		{
			Event: consts.SHFinanceFirstReviewResult,
			Src:   []int{int(ShFinanceWaitingReview)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ShFinanceWaitingReview),
				},
			},
		},
		// 审核通过
		{
			Event: consts.SHFinanceReviewApprove,
			Src:   []int{int(ShFinanceWaitingReview)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ShFinanceWaitingSign),
				},
			},
		},
		// 审核拒绝
		{
			Event: consts.SHFinanceReviewReject,
			Src:   []int{int(ShFinanceWaitingReview)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ShFinanceInvalid),
				},
			},
		},
		// 开始合同签署
		{
			Event: consts.SHFinanceBeginSignContract,
			Src:   []int{int(ShFinanceWaitingSign), int(ShFinanceSigning)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ShFinanceSigning),
				},
			},
		},

		// 签署成功并且转账成功
		{
			Event: consts.SHFinanceSignSucAndTransferSuc,
			Src:   []int{int(ShFinanceSigning)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ShFinanceWaitingRepay),
				},
			},
		},

		// 发起核销
		{
			Event: consts.SHFinanceBeginVerify,
			Src:   []int{int(ShFinanceWaitingRepay)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ShFinanceWaitingVerify),
				},
			},
		},
		// 核销初审通过
		{
			Event: consts.SHFinanceFirstVerifyResult,
			Src:   []int{int(ShFinanceWaitingVerify)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ShFinanceWaitingVerify),
				},
			},
		},

		// 核销通过
		{
			Event: consts.SHFinanceVerifyApprove,
			Src:   []int{int(ShFinanceWaitingVerify)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ShFinanceRepayFinnish),
				},
			},
		},
	},
}
