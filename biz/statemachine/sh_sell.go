package statemachine

import (
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

const (
	// 订金-尾款 状态
	shSellEFCreate                Status = 1   // 订单创建
	shSellEFSigningIntentContract Status = 2   // 意向合同签署中
	shSellEFToPayEarnestMoney     Status = 3   // 订金待支付
	shSellEFPayingEarnestMoney    Status = 4   // 订金支付中
	shSellEFToSignSellContract    Status = 5   // 买卖合同待签署
	shSellEFSigningSellContract   Status = 6   // 买卖合同签署中
	shSellEFToPayFinalMoney       Status = 7   // 尾款待支付
	shSellEFPayingFinalMoney      Status = 8   // 尾款支付中
	shSellEFToTransferOwner       Status = 9   // 待过户
	shSellEFToDeliveryCar         Status = 10  // 待交车
	shSellEFToSelectLoan          Status = 11  // 待选择贷款金融方式
	shSellEFToConfirmLoan         Status = 12  // 待确认贷款
	shSellEFToApproveLoan         Status = 13  // 待审核贷款
	shSellEFLoaning               Status = 14  // 贷款中
	shSellEFSettling              Status = 50  // 结算中
	shSellEFOver                  Status = 100 // 已完成
	shSellEFCanceling             Status = 199 // 作废中（从"买卖合同待签署"作废时，有作废中的状态，因为需要退订金）
	shSellEFCancellation          Status = 200 // 已作废

	// 全款 状态
	shSellFullCreate          Status = 1   // 订单创建
	shSellFullSigningContract Status = 2   // 买卖合同签署中
	shSellFullToPay           Status = 3   // 全款待支付
	shSellFullPaying          Status = 4   // 全款支付中
	shSellFullToTransferOwner Status = 5   // 待过户
	shSellFullToDeliveryCar   Status = 6   // 待交车
	shSellFullToSelectLoan    Status = 11  // 待选择贷款金融方式
	shSellFullToConfirmLoan   Status = 12  // 待确认贷款
	shSellFullToApproveLoan   Status = 13  // 待审核贷款
	shSellFullLoaning         Status = 14  // 贷款中
	shSellFullSettling        Status = 50  // 结算中
	shSellFullOver            Status = 100 // 已完成
	shSellFullCancellation    Status = 200 // 已作废

	// 延保状态
	shSellWarrantyCreate  Status = 1   // 订单创建
	shSellWarrantySigning Status = 2   // 延保合同签署中
	shSellWarrantToPay    Status = 3   // 待支付
	shSellWarrantPaying   Status = 4   // 支付中
	shSellWarrantOver     Status = 100 // 已完成
	shSellWarrantCancel   Status = 200 // 已作废
)

var shSellEarnestFinalStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		StateNSConf: map[bfsm.NameSpace]map[int]string{bfsm.NameSpace("default"): {
			int(shSellEFCreate):                "订单创建",
			int(shSellEFSigningIntentContract): "意向合同签署中",
			int(shSellEFToPayEarnestMoney):     "订金待支付",
			int(shSellEFPayingEarnestMoney):    "订金支付中",
			int(shSellEFToSignSellContract):    "买卖合同待签署",
			int(shSellEFSigningSellContract):   "买卖合同签署中",
			int(shSellEFToPayFinalMoney):       "尾款待支付",
			int(shSellEFPayingFinalMoney):      "尾款支付中",
			int(shSellEFToTransferOwner):       "待过户",
			int(shSellEFToDeliveryCar):         "待交车",
			int(shSellEFToSelectLoan):          "待选择贷款金融方式",
			int(shSellEFToConfirmLoan):         "待确认贷款",
			int(shSellEFToApproveLoan):         "待审核贷款",
			int(shSellEFLoaning):               "贷款中",
			int(shSellEFOver):                  "已完成",
			int(shSellEFCanceling):             "作废中",
			int(shSellEFCancellation):          "已作废",
		}},
		EventNSConf: map[bfsm.NameSpace]map[string]string{bfsm.NameSpace("default"): {
			consts.ShSellEFSignIntentContractEvent:     "签署意向合同",
			consts.ShSellEFSignIntentContractOverEvent: "意向合同签署完成",
			consts.ShSellEFPayEarnestEvent:             "支付订金",
			consts.ShSellEFPayEarnestOverEvent:         "订金支付完成",
			consts.ShSellEFRefundEarnestOverEvent:      "退订金完成",
			consts.ShSellEFSignSellContractEvent:       "签署买卖合同",
			consts.ShSellEFSignSellContractOverEvent:   "买卖合同签署完成",
			consts.ShSellEFPayFinalEvent:               "支付尾款",
			consts.ShSellEFPayFinalOverEvent:           "尾款支付完成",
			consts.ShSellEFTransferOwnerEvent:          "过户",
			consts.ShSellEFSelectLoanEvent:             "确定金融贷款方式",
			consts.ShSellEFConfirmLoanEvent:            "确认贷款",
			consts.ShSellEFApproveLoanPassEvent:        "审核贷款通过",
			consts.ShSellEFApproveLoanFailEvent:        "审核贷款不通过",
			consts.ShSellEFLoanOverEvent:               "金融贷款完成",
			consts.ShSellEFDeliveryEvent:               "交车",
			consts.ShSellEFSettleOverEvent:             "结算完成",
			consts.ShSellEFCancelEvent:                 "作废",
			consts.ShSellEFCancelWithRefundEvent:       "作废并退款",
			consts.ShSellEFPayEarnestTimeoutEvent:      "支付订金超时",
			consts.ShSellEFPayPOSTimeoutEvent:          "支付POS超时",
		}},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src:   []int{int(OrderInitStatus)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFCreate),
				},
			},
		},
		{
			// 签署意向合同（发短信）
			Event: consts.ShSellEFSignIntentContractEvent,
			Src:   []int{int(shSellEFCreate), int(shSellEFSigningIntentContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFSigningIntentContract),
				},
			},
		},
		{
			// 意向合同签署完成
			Event: consts.ShSellEFSignIntentContractOverEvent,
			Src:   []int{int(shSellEFCreate), int(shSellEFSigningIntentContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFToPayEarnestMoney),
				},
			},
		},
		{
			// 支付意向金(获取链接)
			Event: consts.ShSellEFPayEarnestEvent,
			Src:   []int{int(shSellEFToPayEarnestMoney), int(shSellEFPayingEarnestMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFPayingEarnestMoney),
				},
			},
		},
		{
			// 支付意向金完成
			Event: consts.ShSellEFPayEarnestOverEvent,
			Src:   []int{int(shSellEFToPayEarnestMoney), int(shSellEFPayingEarnestMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFToSignSellContract),
				},
			},
		},
		{
			// 支付意向金超时
			Event: consts.ShSellEFPayEarnestTimeoutEvent,
			Src:   []int{int(shSellEFPayingEarnestMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFToPayEarnestMoney),
				},
			},
		},
		{
			// 签署买卖合同(发短信)
			Event: consts.ShSellEFSignSellContractEvent,
			Src:   []int{int(shSellEFToSignSellContract), int(shSellEFSigningSellContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFSigningSellContract),
				},
			},
		},
		{
			// 签署买卖合同完成
			Event: consts.ShSellEFSignSellContractOverEvent,
			Src:   []int{int(shSellEFToSignSellContract), int(shSellEFSigningSellContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFToPayFinalMoney),
				},
			},
		},
		{
			// 支付尾款（获取链接）
			Event: consts.ShSellEFPayFinalEvent,
			Src:   []int{int(shSellEFToPayFinalMoney), int(shSellEFPayingFinalMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFPayingFinalMoney),
				},
			},
		},
		{
			// 支付尾款完成
			Event: consts.ShSellEFPayFinalOverEvent,
			Src:   []int{int(shSellEFToPayFinalMoney), int(shSellEFPayingFinalMoney)},
			Matchers: []bfsm.Matcher{
				{
					// 没有金融节点，流转到"待过户"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       int(shSellEFToTransferOwner),
				},
				{
					// 有金融节点，流转到"待选择金融方式"
					Condition: consts.CondShSellHasLoan.Val(),
					Dst:       int(shSellEFToSelectLoan),
				},
			},
		},
		{
			// 支付尾款超时（回调时会判断是否有进行中的支付）
			Event: consts.ShSellEFPayPOSTimeoutEvent,
			Src:   []int{int(shSellEFPayingFinalMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFToPayFinalMoney),
				},
			},
		},
		{
			// 选择贷款方式
			Event: consts.ShSellEFSelectLoanEvent,
			Src:   []int{int(shSellEFToSelectLoan)},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       int(shSellEFToTransferOwner),
				},
				{
					// 先贷款后过户，流转到"待确认贷款"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       int(shSellEFToConfirmLoan),
				},
			},
		},
		{
			// 确认贷款
			Event: consts.ShSellEFConfirmLoanEvent,
			Src:   []int{int(shSellEFToConfirmLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFToApproveLoan),
				},
			},
		},
		{
			// 审核贷款通过 --> 贷款中
			Event: consts.ShSellEFApproveLoanPassEvent,
			Src:   []int{int(shSellEFToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFLoaning),
				},
			},
		},
		{
			// 审核贷款不通过 --> 待确认贷款
			Event: consts.ShSellEFApproveLoanFailEvent,
			Src:   []int{int(shSellEFToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFToConfirmLoan),
				},
			},
		},
		{
			// 贷款完成
			Event: consts.ShSellEFLoanOverEvent,
			Src:   []int{int(shSellEFLoaning)},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待交车"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       int(shSellEFToDeliveryCar),
				},
				{
					// 先贷款后过户，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       int(shSellEFToTransferOwner),
				},
			},
		},
		{
			// 过户
			Event: consts.ShSellEFTransferOwnerEvent,
			Src:   []int{int(shSellEFToTransferOwner)},
			Matchers: []bfsm.Matcher{
				{
					// 没有有金融节点，流转到"待交车"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       int(shSellEFToDeliveryCar),
				},
				{
					// 有金融节点 && 先过户后贷款，流转到"待确认贷款"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst.Not()).Val(),
					Dst:       int(shSellEFToConfirmLoan),
				},
				{
					// 有金融节点 && 先贷款后过户，流转到"待交车"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst).Val(),
					Dst:       int(shSellEFToDeliveryCar),
				},
			},
		},
		{
			// 交车
			Event: consts.ShSellEFDeliveryEvent,
			Src:   []int{int(shSellEFToDeliveryCar)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFOver),
				},
			},
		},
		{
			// 作废，涉及到订金退款
			Event: consts.ShSellEFCancelWithRefundEvent,
			Src:   []int{int(shSellEFToSignSellContract), int(shSellEFSigningSellContract), int(shSellEFToPayFinalMoney), int(shSellEFCanceling)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFCanceling),
				},
			},
		},
		{
			// 作废，涉及到订金退款回调成功，退款完成
			Event: consts.ShSellEFRefundEarnestOverEvent,
			Src:   []int{int(shSellEFCanceling), int(shSellEFToSignSellContract), int(shSellEFSigningSellContract), int(shSellEFToPayFinalMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFCancellation),
				},
			},
		},
		{
			// 作废
			Event: consts.ShSellEFCancelEvent,
			Src:   []int{int(shSellEFCreate), int(shSellEFSigningIntentContract), int(shSellEFToPayEarnestMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellEFCancellation),
				},
			},
		},
	},
}

var shSellFullStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		StateNSConf: map[bfsm.NameSpace]map[int]string{bfsm.NameSpace("default"): {
			int(shSellFullCreate):          "订单创建",
			int(shSellFullSigningContract): "买卖合同签署中",
			int(shSellFullToPay):           "全款待支付",
			int(shSellFullPaying):          "全款支付中",
			int(shSellFullToTransferOwner): "待过户",
			int(shSellFullToDeliveryCar):   "待交车",
			int(shSellFullToSelectLoan):    "待选择贷款金融方式",
			int(shSellFullToConfirmLoan):   "待确认贷款",
			int(shSellFullToApproveLoan):   "待审核贷款",
			int(shSellFullLoaning):         "贷款中",
			int(shSellFullOver):            "已完成",
			int(shSellFullCancellation):    "已作废",
		}},
		EventNSConf: map[bfsm.NameSpace]map[string]string{bfsm.NameSpace("default"): {
			consts.ShSellFullSignSellContractEvent:     "签署买卖合同",
			consts.ShSellFullSignSellContractOverEvent: "买卖合同签署完成",
			consts.ShSellFullPayMoneyEvent:             "支付",
			consts.ShSellFullPayMoneyOverEvent:         "支付完成",
			consts.ShSellFullTransferOwnerEvent:        "过户",
			consts.ShSellFullSelectLoanEvent:           "确定金融贷款方式",
			consts.ShSellFullConfirmLoanEvent:          "确认贷款",
			consts.ShSellFullApproveLoanPassEvent:      "审核贷款通过",
			consts.ShSellFullApproveLoanFailEvent:      "审核贷款不通过",
			consts.ShSellFullLoanOverEvent:             "金融贷款完成",
			consts.ShSellFullDeliveryEvent:             "交车",
			consts.ShSellFullSettleOverEvent:           "结算完成",
			consts.ShSellFullCancelEvent:               "作废",
			consts.ShSellFullPayPOSTimeoutEvent:        "支付POS超时",
		}},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src:   []int{int(OrderInitStatus)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellFullCreate),
				},
			},
		},
		{
			// 签署买卖合同（发短信）
			Event: consts.ShSellFullSignSellContractEvent,
			Src:   []int{int(shSellFullCreate), int(shSellFullSigningContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellFullSigningContract),
				},
			},
		},
		{
			// 签署买卖合同完成
			Event: consts.ShSellFullSignSellContractOverEvent,
			Src:   []int{int(shSellFullCreate), int(shSellFullSigningContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellFullToPay),
				},
			},
		},
		{
			// 支付全款（获取链接）
			Event: consts.ShSellFullPayMoneyEvent,
			Src:   []int{int(shSellFullToPay), int(shSellFullPaying)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellFullPaying),
				},
			},
		},
		{
			// 支付完成
			Event: consts.ShSellFullPayMoneyOverEvent,
			Src:   []int{int(shSellFullToPay), int(shSellFullPaying)},
			Matchers: []bfsm.Matcher{
				{
					// 没有金融节点，流转到"待过户"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       int(shSellFullToTransferOwner),
				},
				{
					// 有金融节点，流转到"待选择金融方式"
					Condition: consts.CondShSellHasLoan.Val(),
					Dst:       int(shSellFullToSelectLoan),
				},
			},
		},
		{
			// 支付尾款超时（回调时会判断是否有进行中的支付）
			Event: consts.ShSellFullPayPOSTimeoutEvent,
			Src:   []int{int(shSellFullPaying)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellFullToPay),
				},
			},
		},
		{
			// 选择贷款方式
			Event: consts.ShSellFullSelectLoanEvent,
			Src:   []int{int(shSellFullToSelectLoan)},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       int(shSellFullToTransferOwner),
				},
				{
					// 先贷款后过户，流转到"待确认贷款"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       int(shSellFullToConfirmLoan),
				},
			},
		},
		{
			// 确认贷款
			Event: consts.ShSellFullConfirmLoanEvent,
			Src:   []int{int(shSellFullToConfirmLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellFullToApproveLoan),
				},
			},
		},
		{
			// 审核贷款通过 --> 贷款中
			Event: consts.ShSellFullApproveLoanPassEvent,
			Src:   []int{int(shSellFullToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellFullLoaning),
				},
			},
		},
		{
			// 审核贷款不通过 --> 待确认贷款
			Event: consts.ShSellFullApproveLoanFailEvent,
			Src:   []int{int(shSellFullToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellFullToConfirmLoan),
				},
			},
		},
		{
			// 贷款完成
			Event: consts.ShSellFullLoanOverEvent,
			Src:   []int{int(shSellFullLoaning)},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待交车"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       int(shSellFullToDeliveryCar),
				},
				{
					// 先贷款后过户，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       int(shSellFullToTransferOwner),
				},
			},
		},
		{
			// 过户
			Event: consts.ShSellFullTransferOwnerEvent,
			Src:   []int{int(shSellFullToTransferOwner)},
			Matchers: []bfsm.Matcher{
				{
					// 没有有金融节点，流转到"待交车"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       int(shSellFullToDeliveryCar),
				},
				{
					// 有金融节点 && 先过户后贷款，流转到"待确认贷款"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst.Not()).Val(),
					Dst:       int(shSellFullToConfirmLoan),
				},
				{
					// 有金融节点 && 先贷款后过户，流转到"待交车"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst).Val(),
					Dst:       int(shSellFullToDeliveryCar),
				},
			},
		},
		{
			// 交车
			Event: consts.ShSellFullDeliveryEvent,
			Src:   []int{int(shSellFullToDeliveryCar)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellFullOver),
				},
			},
		},
		{
			// 作废
			Event: consts.ShSellFullCancelEvent,
			Src:   []int{int(shSellFullCreate), int(shSellFullSigningContract), int(shSellFullToPay)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellFullCancellation),
				},
			},
		},
	},
}

var shSellExtWarrantyStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		StateNSConf: map[bfsm.NameSpace]map[int]string{bfsm.NameSpace("default"): {
			int(shSellWarrantyCreate):  "订单创建",
			int(shSellWarrantySigning): "延保合同签署中",
			int(shSellWarrantToPay):    "待支付",
			int(shSellWarrantPaying):   "支付中",
			int(shSellWarrantOver):     "已完成",
			int(shSellWarrantCancel):   "已作废",
		}},
		EventNSConf: map[bfsm.NameSpace]map[string]string{bfsm.NameSpace("default"): {
			consts.ShSellWarrantySignContractEvent:     "签署合同",
			consts.ShSellWarrantySignContractOverEvent: "合同签署完成",
			consts.ShSellWarrantyPayEvent:              "支付",
			consts.ShSellWarrantyPayOverEvent:          "支付完成",
			consts.ShSellWarrantyCancelEvent:           "作废",
			consts.ShSellWarrantyPayTimeoutEvent:       "支付超时事件",
		}},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src:   []int{int(OrderInitStatus)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellWarrantyCreate),
				},
			},
		},
		{
			// 签署合同（发短信）
			Event: consts.ShSellWarrantySignContractEvent,
			Src:   []int{int(shSellWarrantyCreate), int(shSellWarrantySigning)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellWarrantySigning),
				},
			},
		},
		{
			// 签署合同完成
			Event: consts.ShSellWarrantySignContractOverEvent,
			Src:   []int{int(shSellWarrantyCreate), int(shSellWarrantySigning)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellWarrantToPay),
				},
			},
		},
		{
			// 支付（获取支付链接）
			Event: consts.ShSellWarrantyPayEvent,
			Src:   []int{int(shSellWarrantToPay), int(shSellWarrantPaying)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellWarrantPaying),
				},
			},
		},
		{
			// 支付完成
			Event: consts.ShSellWarrantyPayOverEvent,
			Src:   []int{int(shSellWarrantToPay), int(shSellWarrantPaying)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellWarrantOver),
				},
			},
		},
		{
			// 支付超时
			Event: consts.ShSellWarrantyPayTimeoutEvent,
			Src:   []int{int(shSellWarrantPaying)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellWarrantToPay),
				},
			},
		},
		{
			// 作废
			Event: consts.ShSellWarrantyCancelEvent,
			Src:   []int{int(shSellWarrantyCreate), int(shSellWarrantySigning), int(shSellWarrantToPay)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shSellWarrantCancel),
				},
			},
		},
	},
}
