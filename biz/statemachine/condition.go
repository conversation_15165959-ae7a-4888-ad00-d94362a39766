package statemachine

import "fmt"

const (
	BeforeStatusKey    = "beforeStatus"
	BigDepositPriceKey = "bigDepositPrice"
	FinalPriceKey      = "finalPrice"
	NeedSettle         = "needSettle"
	NeedRefund         = "needRefund"
	RefundAmount       = "refundAmount"
)

func GetBeforeStatusCondition(status Status) string {
	return fmt.Sprintf("%s == %d", BeforeStatusKey, status)
}

func GetBigDepositPriceZeroCondition() string {
	return fmt.Sprintf("%s <= 0", BigDepositPriceKey)
}

func GetBigDepositPriceCondition() string {
	return fmt.Sprintf("%s > 0", BigDepositPriceKey)
}

func GetFinalPriceZeroCondition() string {
	return fmt.Sprintf("%s <= 0", FinalPriceKey)
}

func GetFinalPriceCondition() string {
	return fmt.Sprintf("%s > 0", FinalPriceKey)
}
