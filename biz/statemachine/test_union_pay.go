package statemachine

import "code.byted.org/motor/bfsm"

// 测试用，上线前删除

var (
	initState   = bfsm.NewState(0, "订单初始状态", bfsm.WithStateTypeOption(bfsm.Begin))
	createState = bfsm.NewState(1, "订单创建")
	payingState = bfsm.NewState(2, "支付中")
	payedState  = bfsm.NewState(2, "已支付")

	CreateEt      = bfsm.NewEvent("create", "创建订单")
	PayEt         = bfsm.NewEvent("Pay", "支付")
	PayCallbackEt = bfsm.NewEvent("PayCallback", "支付完成")
)

var testPayUnionFSM = bfsm.BizDesc{
	Conf: bfsm.Conf{
		States: []bfsm.State{
			createState, payingState, payedState,
		},
		Events: []bfsm.Event{
			CreateEt, PayEt, PayCallbackEt,
		},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: CreateEt.Value(),
			Src:   []int{initState.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: createState.Value(),
				},
			},
		},
		{
			// 支付
			Event: PayEt.Value(),
			Src:   []int{createState.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: payingState.Value(),
				},
			},
		},
		{
			// 支付完成
			Event: PayCallbackEt.Value(),
			Src:   []int{payingState.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: payedState.Value(),
				},
			},
		},
	},
}
