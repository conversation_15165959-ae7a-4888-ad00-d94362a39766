package statemachine

import (
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

/*
	新车 大订模式
*/

const (
	// 订单状态

	// 合同
	ncBigDepositBusinessContWait                      Status = 3  // 买卖合同、待签署
	ncBigDepositBusinessContProcess                   Status = 4  // 买卖合同、签约中
	ncBigDepositCheckCarContWait                      Status = 5  // 验车合同、待签署
	ncBigDepositCheckCarContProcess                   Status = 6  // 验车合同、签署中
	ncBigDepositTerminationContWait                   Status = 51 // 终止合同、待签署
	ncBigDepositTerminationContProcess                Status = 52 // 终止合同、签署中
	ncBigDepositAfterBigEarnestTerminationContWait    Status = 55 // 退款大订终止合同、待签署
	ncBigDepositAfterBigEarnestTerminationContProcess Status = 56 // 退款大订终止合同、签署中

	// 支付
	ncBigDepositBigEarnestPayWait                   Status = 13 // 大订资金、待支付
	ncBigDepositBigEarnestPayProcess                Status = 14 // 大订资金、支付中
	ncBigDepositFinalPayWait                        Status = 17 // 尾款支付，待支付
	ncBigDepositFinalPayProcess                     Status = 18 // 尾款支付，支付中
	ncBigDepositSettleWait                          Status = 61 // 确认订单、待分账
	ncBigDepositSettleProcess                       Status = 62 // 确认订单、分账中
	ncBigDepositAfterBigEarnestRefundEarnestProcess Status = 64 // 退款大订、退款中

	// 审核
	ncBigDepositReviewWait                      Status = 41 // 创建订单、待审核
	ncBigDepositOnlyCancelReviewWait            Status = 42 // 直接取消、待审核
	ncBigDepositAfterContCancelReviewWait       Status = 43 // 终止合同、待审核
	ncBigDepositAfterBigEarnestCancelReviewWait Status = 44 // 退款资金、待审核
	ncBigDepositReviewFail                      Status = 45 // 审核失败、待审核

	// 车辆
	ncBigDepositCustomerCheckCarWait Status = 32 // 客户验车、待确认
	ncBigDepositCustomerGetCarWait   Status = 33 // 客户提车、待确认

	// 	终止状态
	ncBigDepositFinish                Status = 100 // 订单成功
	ncBigDepositOnlyCancel            Status = 101 // 直接作废
	ncBigDepositTerminationContCancel Status = 102 // 签署终止合同作废
	ncBigDepositRefundCancel          Status = 104 // 退大订作废
)

var ncBigDepositStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		EventNSConfV2: map[bfsm.NameSpace]map[string]bfsm.EventConfV2{
			bfsm.NameSpace("default"): {
				consts.NCBigDepositBusinessContFinish:          bfsm.EventConfV2{IsAsync: true},
				consts.NCBigDepositTerminationContFinish:       bfsm.EventConfV2{IsAsync: true},
				consts.NCBigDepositBigEarnestPayFinish:         bfsm.EventConfV2{IsAsync: true},
				consts.NCBigDepositBigEarnestPayTimeoutFinish:  bfsm.EventConfV2{IsAsync: true},
				consts.NCBigDepositBigEarnestRefundFinish:      bfsm.EventConfV2{IsAsync: true},
				consts.NCBigDepositCheckCarContFinish:          bfsm.EventConfV2{IsAsync: true},
				consts.NCBigDepositFinalPayFinish:              bfsm.EventConfV2{IsAsync: true},
				consts.NCBigDepositFinalPayTimeoutFinish:       bfsm.EventConfV2{IsAsync: true},
				consts.NCBigDepositSettleFinish:                bfsm.EventConfV2{IsAsync: true},
				consts.NCBigDepositRefundTerminationContFinish: bfsm.EventConfV2{IsAsync: true},
			},
		},
		StateNSConf: map[bfsm.NameSpace]map[int]string{
			bfsm.NameSpace("default"): {
				OrderInitStatus.Int(): "开始",
				// 订单状态

				// 合同
				ncBigDepositBusinessContWait.Int():                      "买卖合同、待签署",
				ncBigDepositBusinessContProcess.Int():                   "买卖合同、签约中",
				ncBigDepositCheckCarContWait.Int():                      "验车合同、待签署",
				ncBigDepositCheckCarContProcess.Int():                   "验车合同、签署中",
				ncBigDepositTerminationContWait.Int():                   "终止合同、待签署",
				ncBigDepositTerminationContProcess.Int():                "终止合同、签署中",
				ncBigDepositAfterBigEarnestTerminationContWait.Int():    "退款终止合同、待签署",
				ncBigDepositAfterBigEarnestTerminationContProcess.Int(): "退款终止合同、签署中",

				// 支付",
				ncBigDepositBigEarnestPayWait.Int():                   "大订资金、待支付",
				ncBigDepositBigEarnestPayProcess.Int():                "大订资金、支付中",
				ncBigDepositFinalPayWait.Int():                        "尾款支付，待支付",
				ncBigDepositFinalPayProcess.Int():                     "尾款支付，支付中",
				ncBigDepositSettleWait.Int():                          "确认订单、待分账",
				ncBigDepositSettleProcess.Int():                       "确认订单、分账中",
				ncBigDepositAfterBigEarnestRefundEarnestProcess.Int(): "退款大订、退款中",

				// 审核",
				ncBigDepositReviewWait.Int():                      "创建订单、待审核",
				ncBigDepositOnlyCancelReviewWait.Int():            "直接取消、待审核",
				ncBigDepositAfterContCancelReviewWait.Int():       "终止合同、待审核",
				ncBigDepositAfterBigEarnestCancelReviewWait.Int(): "退款资金、待审核",
				ncBigDepositReviewFail.Int():                      "审核驳回",

				// 车辆",
				ncBigDepositCustomerCheckCarWait.Int(): "待客户验车、待确认",
				ncBigDepositCustomerGetCarWait.Int():   "待客户提车、待确认",

				// 	终止状态",
				ncBigDepositFinish.Int():                "订单成功",
				ncBigDepositOnlyCancel.Int():            "直接作废",
				ncBigDepositTerminationContCancel.Int(): "签署终止合同作废",
				ncBigDepositRefundCancel.Int():          "退大订作废",
			},
		},
		EventNSConf: map[bfsm.NameSpace]map[string]string{
			bfsm.NameSpace("default"): {
				consts.CreateAction:                            "创建订单",
				consts.NCBigDepositReviewFailUpdateOrder:       "审核失败修改订单",
				consts.NCBigDepositReviewPass:                  "审核通过",
				consts.NCBigDepositCancelStart:                 "订单取消",
				consts.NCBigDepositReviewReject:                "审核驳回",
				consts.NCBigDepositBusinessContStart:           "买卖合同签署发起",
				consts.NCBigDepositBusinessContFinish:          "买卖合同签署回调",
				consts.NCBigDepositTerminationContStart:        "终止合同签约发起",
				consts.NCBigDepositTerminationContFinish:       "终止合同签约回调",
				consts.NCBigDepositBigEarnestPayStart:          "大订支付发起",
				consts.NCBigDepositBigEarnestPayFinish:         "大订支付回调",
				consts.NCBigDepositBigEarnestPayTimeoutFinish:  "大订支付超时回调",
				consts.NCBigDepositBigEarnestRefundFinish:      "大订退款回调",
				consts.NCBigDepositCustomerCheckCarFinish:      "客户验车完成",
				consts.NCBigDepositCustomerGetCarFinish:        "客户提车完成",
				consts.NCBigDepositCheckCarContStart:           "验车合同签约发起",
				consts.NCBigDepositCheckCarContFinish:          "验车合同签约回调",
				consts.NCBigDepositFinalPayStart:               "尾款支付发起",
				consts.NCBigDepositFinalPayFinish:              "尾款支付回调",
				consts.NCBigDepositFinalPayTimeoutFinish:       "尾款支付超时回调",
				consts.NCBigDepositSettleStart:                 "交车完成分账发起",
				consts.NCBigDepositSettleFinish:                "分账回调",
				consts.NCBigDepositRefundTerminationContFinish: "退款终止合同回调",
			},
		},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src: []int{
				int(OrderInitStatus),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositReviewWait),
				},
			},
		},
		{
			// 审核驳回
			Event: consts.NCBigDepositReviewReject,
			Src: []int{
				int(ncBigDepositReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositReviewFail),
				},
			},
		},
		{
			// 审核失败修改订单
			Event: consts.NCBigDepositReviewFailUpdateOrder,
			Src: []int{
				int(ncBigDepositReviewFail),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositReviewWait),
				},
			},
		},
		{
			// 审核通过
			Event: consts.NCBigDepositReviewPass,
			Src: []int{
				int(ncBigDepositReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositBusinessContWait),
				},
			},
		},
		{
			// 审核驳回
			Event: consts.NCBigDepositReviewReject,
			Src: []int{
				int(ncBigDepositOnlyCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncBigDepositBusinessContWait),
					Condition: GetBeforeStatusCondition(ncBigDepositBusinessContWait),
				},
				{
					Dst:       int(ncBigDepositReviewFail),
					Condition: GetBeforeStatusCondition(ncBigDepositReviewFail),
				},
			},
		},
		{
			// 直接取消待审核
			Event: consts.NCBigDepositCancelStart,
			Src: []int{
				int(ncBigDepositReviewFail),
				int(ncBigDepositBusinessContWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositOnlyCancelReviewWait),
				},
			},
		},
		{
			// 直接取消审核通过
			Event: consts.NCBigDepositReviewPass,
			Src: []int{
				int(ncBigDepositOnlyCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositOnlyCancel),
				},
			},
		},
		{
			// 发起服务合同签署
			Event: consts.NCBigDepositBusinessContStart,
			Src: []int{
				int(ncBigDepositBusinessContWait),
				int(ncBigDepositBusinessContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositBusinessContProcess),
				},
			},
		},
		{
			// 服务合同异步回调
			Event: consts.NCBigDepositBusinessContFinish,
			Src: []int{
				int(ncBigDepositBusinessContWait),
				int(ncBigDepositBusinessContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositBigEarnestPayWait),
				},
			},
		},
		{
			// 签署合同后取消待审核
			Event: consts.NCBigDepositCancelStart,
			Src: []int{
				int(ncBigDepositBigEarnestPayWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositAfterContCancelReviewWait),
				},
			},
		},
		{
			// 签署合同后取消驳回
			Event: consts.NCBigDepositReviewReject,
			Src: []int{
				int(ncBigDepositAfterContCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncBigDepositBigEarnestPayWait),
					Condition: GetBeforeStatusCondition(ncBigDepositBigEarnestPayWait),
				},
			},
		},
		{
			// 签署合同后取消审核通过
			Event: consts.NCBigDepositReviewPass,
			Src: []int{
				int(ncBigDepositAfterContCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositTerminationContWait),
				},
			},
		},
		{
			// 签署合同后取消待发起终止合同
			Event: consts.NCBigDepositTerminationContStart,
			Src: []int{
				int(ncBigDepositTerminationContWait),
				int(ncBigDepositTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositTerminationContProcess),
				},
			},
		},
		{
			// 签署合同后取消待签署终止合同回调
			Event: consts.NCBigDepositTerminationContFinish,
			Src: []int{
				int(ncBigDepositTerminationContWait),
				int(ncBigDepositTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositTerminationContCancel),
				},
			},
		},
		{
			// 发起大订支付
			Event: consts.NCBigDepositBigEarnestPayStart,
			Src: []int{
				int(ncBigDepositBigEarnestPayWait),
				int(ncBigDepositBigEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositBigEarnestPayProcess),
				},
			},
		},
		{
			// 大订完成回调
			Event: consts.NCBigDepositBigEarnestPayFinish,
			Src: []int{
				int(ncBigDepositBigEarnestPayWait),
				int(ncBigDepositBigEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositCustomerGetCarWait),
				},
			},
		},
		{
			// 大订超时回调
			Event: consts.NCBigDepositBigEarnestPayTimeoutFinish,
			Src: []int{
				int(ncBigDepositBigEarnestPayWait),
				int(ncBigDepositBigEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositBigEarnestPayWait),
				},
			},
		},
		{
			// 门店订车确认
			Event: consts.NCBigDepositCustomerGetCarFinish,
			Src: []int{
				int(ncBigDepositCustomerGetCarWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositCustomerCheckCarWait),
				},
			},
		},
		{
			// 客户验车确认
			Event: consts.NCBigDepositCustomerCheckCarFinish,
			Src: []int{
				int(ncBigDepositCustomerCheckCarWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositCheckCarContWait),
				},
			},
		},
		{
			// 大订完成取消待审核
			Event: consts.NCBigDepositCancelStart,
			Src: []int{
				int(ncBigDepositCustomerGetCarWait),
				int(ncBigDepositCustomerCheckCarWait),
				int(ncBigDepositCheckCarContWait),
				int(ncBigDepositFinalPayWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositAfterBigEarnestCancelReviewWait),
				},
			},
		},
		{
			// 大订完成取消待驳回
			Event: consts.NCBigDepositReviewReject,
			Src: []int{
				int(ncBigDepositAfterBigEarnestCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncBigDepositCustomerGetCarWait),
					Condition: GetBeforeStatusCondition(ncBigDepositCustomerGetCarWait),
				},
				{
					Dst:       int(ncBigDepositCustomerCheckCarWait),
					Condition: GetBeforeStatusCondition(ncBigDepositCustomerCheckCarWait),
				},
				{
					Dst:       int(ncBigDepositCheckCarContWait),
					Condition: GetBeforeStatusCondition(ncBigDepositCheckCarContWait),
				},
				{
					Dst:       int(ncBigDepositFinalPayWait),
					Condition: GetBeforeStatusCondition(ncBigDepositFinalPayWait),
				},
			},
		},
		{
			// 大订完成取消待审核
			Event: consts.NCBigDepositReviewPass,
			Src: []int{
				int(ncBigDepositAfterBigEarnestCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositAfterBigEarnestTerminationContWait),
				},
			},
		},
		{
			// 全款完成后发起签署终止合同
			Event: consts.NCBigDepositTerminationContStart,
			Src: []int{
				int(ncBigDepositAfterBigEarnestTerminationContWait),
				int(ncBigDepositAfterBigEarnestTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositAfterBigEarnestTerminationContProcess),
				},
			},
		},
		{
			// 全款完成后发起签署终止合同回调
			Event: consts.NCBigDepositRefundTerminationContFinish,
			Src: []int{
				int(ncBigDepositAfterBigEarnestTerminationContWait),
				int(ncBigDepositAfterBigEarnestTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositAfterBigEarnestRefundEarnestProcess),
				},
			},
		},
		{
			// 全款完成后发起退款回调
			Event: consts.NCBigDepositBigEarnestRefundFinish,
			Src: []int{
				int(ncBigDepositAfterBigEarnestTerminationContWait),
				int(ncBigDepositAfterBigEarnestTerminationContProcess),
				int(ncBigDepositAfterBigEarnestRefundEarnestProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositRefundCancel),
				},
			},
		},
		{
			// 签署验车合同
			Event: consts.NCBigDepositCheckCarContStart,
			Src: []int{
				int(ncBigDepositCheckCarContWait),
				int(ncBigDepositCheckCarContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositCheckCarContProcess),
				},
			},
		},
		{
			// 验车合同回调
			Event: consts.NCBigDepositCheckCarContFinish,
			Src: []int{
				int(ncBigDepositCheckCarContWait),
				int(ncBigDepositCheckCarContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncBigDepositSettleWait),
					Condition: GetFinalPriceZeroCondition(),
				},
				{
					Dst:       int(ncBigDepositFinalPayWait),
					Condition: GetFinalPriceCondition(),
				},
			},
		},
		{
			// 尾款支付发起
			Event: consts.NCBigDepositFinalPayStart,
			Src: []int{
				int(ncBigDepositFinalPayWait),
				int(ncBigDepositFinalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositFinalPayProcess),
				},
			},
		},
		{
			// 尾款支付回调
			Event: consts.NCBigDepositFinalPayFinish,
			Src: []int{
				int(ncBigDepositFinalPayWait),
				int(ncBigDepositFinalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositSettleWait),
				},
			},
		},
		{
			// 尾款超时回调
			Event: consts.NCBigDepositFinalPayTimeoutFinish,
			Src: []int{
				int(ncBigDepositFinalPayWait),
				int(ncBigDepositFinalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositFinalPayWait),
				},
			},
		},
		{
			// 交车完成分账发起
			Event: consts.NCBigDepositSettleStart,
			Src: []int{
				int(ncBigDepositSettleWait),
				int(ncBigDepositSettleProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositSettleProcess),
				},
			},
		},
		{
			// 分账回调
			Event: consts.NCBigDepositSettleFinish,
			Src: []int{
				int(ncBigDepositSettleWait),
				int(ncBigDepositSettleProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBigDepositFinish),
				},
			},
		},
	},
}
