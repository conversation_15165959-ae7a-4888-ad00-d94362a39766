package statemachine

import (
	"code.byted.org/motor/bfsm"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

// 二手车-收车 订单状态

const (
	sHBuyGuaranteeContSignWait    Status = 1
	sHBuyGuaranteeContSignProcess Status = 2
	sHBuyBusinessContSignWait     Status = 3
	sHBuyBusinessContSignProcess  Status = 4
	sHBuyEarnestPayWait           Status = 11
	sHBuyEarnestPayProcess        Status = 12
	sHBuyTransOwnerWait           Status = 31
	sHBuyFinalPayWait             Status = 13
	sHBuyFinalPayProcess          Status = 14
	sHBuyStorageInWait            Status = 32
	sHBuyTotalPayWait             Status = 15
	sHBuyTotalPayProcess          Status = 16
	sHBuyEarnestRefundProcess     Status = 17
	sHBuyEarnestPayFailed         Status = 50
	sHBuyFinish                   Status = 100
	sHBuyCancel                   Status = 101
	sHBuyCancelWithRefundEarnest  Status = 102
)

var sHBuyPersonCarStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		StateNSConf: map[bfsm.NameSpace]map[int]string{
			bfsm.NameSpace("default"): {
				OrderInitStatus.Int():              "开始",
				sHBuyBusinessContSignWait.Int():    "买卖合同待签署",
				sHBuyBusinessContSignProcess.Int(): "买卖合同签署中",
				sHBuyEarnestPayWait.Int():          "订金待支付",
				sHBuyEarnestPayProcess.Int():       "订金支付中",
				sHBuyEarnestPayFailed.Int():        "订金支付失败",
				sHBuyTransOwnerWait.Int():          "待过户",
				sHBuyFinalPayWait.Int():            "尾款待支付",
				sHBuyFinalPayProcess.Int():         "尾款支付中",
				sHBuyStorageInWait.Int():           "待入库",
				sHBuyFinish.Int():                  "订单完成",
				sHBuyCancel.Int():                  "订单取消",
			},
		},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src:   []int{int(OrderInitStatus)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyBusinessContSignWait),
				},
			},
		},
		{
			// 作废接口
			Event: consts.SHBuyPersonCarCancelOrderFinish,
			Src:   []int{int(sHBuyBusinessContSignWait), int(sHBuyEarnestPayFailed)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyCancel),
				},
			},
		},
		{
			// 发起买卖合同签约
			Event: consts.SHBuyPersonCarBusinessContStart,
			Src: []int{
				int(sHBuyBusinessContSignWait),
				int(sHBuyBusinessContSignProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyBusinessContSignProcess),
				},
			},
		},
		{
			// 买卖合同回调
			Event: consts.SHBuyPersonCarBusinessContFinish,
			Src: []int{
				int(sHBuyBusinessContSignWait),
				int(sHBuyBusinessContSignProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyEarnestPayWait),
				},
			},
		},
		{
			// 发起订金支付
			Event: consts.SHBuyPersonCarEarnestPayStart,
			Src: []int{
				int(sHBuyEarnestPayWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyEarnestPayProcess),
				},
			},
		},
		{
			// 订金支付回调
			Event: consts.SHBuyPersonCarEarnestPayFinish,
			Src: []int{
				int(sHBuyEarnestPayWait),
				int(sHBuyEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyTransOwnerWait),
				},
			},
		},
		{
			// 订金支付失败
			Event: consts.SHBuyPersonCarEarnestPayFailed,
			Src: []int{
				int(sHBuyEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyEarnestPayFailed),
				},
			},
		},
		{
			// 过户接口
			Event: consts.SHBuyPersonCarTransOwnerFinish,
			Src:   []int{int(sHBuyTransOwnerWait)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyFinalPayWait),
				},
			},
		},
		{
			// 发起尾款支付
			Event: consts.SHBuyPersonCarFinalPayStart,
			Src: []int{
				int(sHBuyFinalPayWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyFinalPayProcess),
				},
			},
		},
		{
			// 尾款支付回调
			Event: consts.SHBuyPersonCarFinalPayFinish,
			Src: []int{
				int(sHBuyFinalPayWait),
				int(sHBuyFinalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyStorageInWait),
				},
			},
		},
		{
			// 入库接口
			Event: consts.SHBuyPersonCarStorageInFinish,
			Src:   []int{int(sHBuyStorageInWait)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyFinish),
				},
			},
		},
	},
}

var sHBuyCompanyCarStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		StateNSConf: map[bfsm.NameSpace]map[int]string{
			bfsm.NameSpace("default"): {
				OrderInitStatus.Int():               "开始",
				sHBuyGuaranteeContSignWait.Int():    "保证金合同待签署",
				sHBuyGuaranteeContSignProcess.Int(): "保证金合同签署中",
				sHBuyBusinessContSignWait.Int():     "买卖合同待签署",
				sHBuyBusinessContSignProcess.Int():  "买卖合同签署中",
				sHBuyTransOwnerWait.Int():           "待过户",
				sHBuyStorageInWait.Int():            "待入库",
				sHBuyTotalPayWait.Int():             "车款待支付",
				sHBuyTotalPayProcess.Int():          "车款支付中",
				sHBuyFinish.Int():                   "订单完成",
				sHBuyCancel.Int():                   "订单取消",
			},
		},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src:   []int{int(OrderInitStatus)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyGuaranteeContSignWait),
				},
			},
		},
		{
			// 取消订单
			Event: consts.SHBuyCompanyCarCancelOrderFinish,
			Src: []int{
				int(sHBuyGuaranteeContSignWait),
				int(sHBuyBusinessContSignWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyCancel),
				},
			},
		},
		{
			// 发起保证金合同签约
			Event: consts.SHBuyCompanyCarGuaranteeContStart,
			Src: []int{
				int(sHBuyGuaranteeContSignWait),
				int(sHBuyGuaranteeContSignProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyGuaranteeContSignProcess),
				},
			},
		},
		{
			// 保证金合同回调
			Event: consts.SHBuyCompanyCarGuaranteeContFinish,
			Src: []int{
				int(sHBuyGuaranteeContSignWait),
				int(sHBuyGuaranteeContSignProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyBusinessContSignWait),
				},
			},
		},
		{
			// 发起买卖合同
			Event: consts.SHBuyCompanyCarBusinessContStart,
			Src: []int{
				int(sHBuyBusinessContSignWait),
				int(sHBuyBusinessContSignProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyBusinessContSignProcess),
				},
			},
		},
		{
			// 买卖合同回调
			Event: consts.SHBuyCompanyCarBusinessContFinish,
			Src: []int{
				int(sHBuyBusinessContSignWait),
				int(sHBuyBusinessContSignProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyTransOwnerWait),
				},
			},
		},
		{
			// 过户接口
			Event: consts.SHBuyCompanyCarTransOwnerFinish,
			Src:   []int{int(sHBuyTransOwnerWait)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyStorageInWait),
				},
			},
		},
		{
			// 入库接口
			Event: consts.SHBuyCompanyCarStorageInFinish,
			Src:   []int{int(sHBuyStorageInWait)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyTotalPayWait),
				},
			},
		},
		{
			// 发起车款支付
			Event: consts.SHBuyCompanyCarTotalPayStart,
			Src: []int{
				int(sHBuyTotalPayWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyTotalPayProcess),
				},
			},
		},
		{
			// 车款支付回调
			Event: consts.SHBuyCompanyCarTotalPayFinish,
			Src: []int{
				int(sHBuyTotalPayWait),
				int(sHBuyTotalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyFinish),
				},
			},
		},
	},
}

var sHBackCompanyCarStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		StateNSConf: map[bfsm.NameSpace]map[int]string{
			bfsm.NameSpace("default"): {
				OrderInitStatus.Int():              "开始",
				sHBuyEarnestPayWait.Int():          "订金待支付",
				sHBuyEarnestPayProcess.Int():       "订金支付中",
				sHBuyFinalPayWait.Int():            "尾款待支付",
				sHBuyFinalPayProcess.Int():         "尾款支付中",
				sHBuyTransOwnerWait.Int():          "待过户",
				sHBuyEarnestRefundProcess.Int():    "订金退款中",
				sHBuyFinish.Int():                  "订单完成",
				sHBuyCancel.Int():                  "订单取消",
				sHBuyCancelWithRefundEarnest.Int(): "订单取消（退款完成）",
			},
		},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src:   []int{int(OrderInitStatus)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyEarnestPayWait),
				},
			},
		},
		{
			// 发起订金支付
			Event: consts.SHBackCompanyCarEarnestPayStart,
			Src: []int{
				int(sHBuyEarnestPayWait),
				int(sHBuyEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyEarnestPayProcess),
				},
			},
		},
		{
			// 订金支付回调
			Event: consts.SHBackCompanyCarEarnestPayFinish,
			Src:   []int{int(sHBuyEarnestPayProcess)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyFinalPayWait),
				},
			},
		},
		{
			// 作废订单
			Event: consts.SHBackCompanyCarCancelOrderFinish,
			Src:   []int{int(sHBuyFinalPayWait)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyCancel),
				},
			},
		},
		{
			// 发起退款订金
			Event: consts.SHBackCompanyCarEarnestRefundStart,
			Src: []int{
				int(sHBuyFinalPayWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyEarnestRefundProcess),
				},
			},
		},
		{
			// 退款订金回调
			Event: consts.SHBackCompanyCarEarnestRefundFinish,
			Src: []int{
				int(sHBuyFinalPayWait),
				int(sHBuyEarnestRefundProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyCancelWithRefundEarnest),
				},
			},
		},
		{
			// 发起尾款支付
			Event: consts.SHBackCompanyCarFinalPayStart,
			Src: []int{
				int(sHBuyFinalPayWait),
				int(sHBuyFinalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyFinalPayProcess),
				},
			},
		},
		{
			// 尾款支付回调
			Event: consts.SHBackCompanyCarFinalPayFinish,
			Src:   []int{int(sHBuyFinalPayProcess)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyTransOwnerWait),
				},
			},
		},
		{
			// 过户接口
			Event: consts.SHBackCompanyCarTransOwnerFinish,
			Src:   []int{int(sHBuyTransOwnerWait)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(sHBuyFinish),
				},
			},
		},
	},
}

var sHBuyPersonCanErr = map[Status]string{
	sHBuyTransOwnerWait: "订金支付已完成，请刷新页面重试",
}

var sHBuyBackPersonCanErr = map[Status]string{
	sHBuyTransOwnerWait: "订金支付已完成，请刷新页面重试",
}

var sHBuyCompanyCanErr = map[Status]string{
	sHBuyFinish: "车款支付已完成，请刷新页面",
}

var sHBackCompanyCanErr = map[Status]string{
	sHBuyFinalPayWait: "订金支付已完成，请刷新页面重试",
}
