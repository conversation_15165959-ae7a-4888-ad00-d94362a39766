package statemachine

import (
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

/*
	新车 返佣模式
*/

const (
	// 订单状态

	// 合同
	ncBrokerageServiceContWait                Status = 1  // 服务合同、待签署
	ncBrokerageServiceContProcess             Status = 2  // 服务合同、签约中
	ncBrokerageTerminationContWait            Status = 51 // 终止合同、待签署
	ncBrokerageTerminationContProcess         Status = 52 // 终止合同、签署中
	ncBrokerageAfterPayTerminationContWait    Status = 53 // 退款车款终止合同、待签署
	ncBrokerageAfterPayTerminationContProcess Status = 54 // 退款车款终止合同、签署中

	// 支付
	ncBrokerageTotalPayWait       Status = 11 // 全款资金、待支付
	ncBrokerageTotalPayProcess    Status = 12 // 全款资金、支付中
	ncBrokerageSettleWait         Status = 61 // 确认订单、待分账
	ncBrokerageSettleProcess      Status = 62 // 确认订单、分账中
	ncBrokerageTotalRefundProcess Status = 63 // 退款资金、退款中

	// 审核
	ncBrokerageReviewWait                Status = 41 // 创建订单、待审核
	ncBrokerageOnlyCancelReviewWait      Status = 42 // 直接取消、待审核
	ncBrokerageAfterContCancelReviewWait Status = 43 // 终止合同、待审核
	ncBrokerageAfterPayCancelReviewWait  Status = 44 // 退款资金、待审核
	ncBrokerageReviewFail                Status = 45 // 审核失败、待审核

	// 车辆
	ncBrokerageCustomerBookCarWait Status = 34 // 待客户下订

	// 	终止状态
	ncBrokerageFinish                Status = 100 // 订单成功
	ncBrokerageOnlyCancel            Status = 101 // 直接作废
	ncBrokerageTerminationContCancel Status = 102 // 签署终止合同作废
	ncBrokerageRefundCancel          Status = 103 // 退款作废
)

var ncBrokerageStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		StateNSConfV2: map[bfsm.NameSpace]map[int]bfsm.StateConfV2{},
		EventNSConfV2: map[bfsm.NameSpace]map[string]bfsm.EventConfV2{
			bfsm.NameSpace("default"): {
				consts.NCBrokerageServiceContFinish:           bfsm.EventConfV2{IsAsync: true},
				consts.NCBrokerageTerminationContFinish:       bfsm.EventConfV2{IsAsync: true},
				consts.NCBrokerageTotalPayFinish:              bfsm.EventConfV2{IsAsync: true},
				consts.NCBrokerageTotalPayTimeoutFinish:       bfsm.EventConfV2{IsAsync: true},
				consts.NCBrokerageSettleFinish:                bfsm.EventConfV2{IsAsync: true},
				consts.NCBrokerageTotalRefundFinish:           bfsm.EventConfV2{IsAsync: true},
				consts.NCBrokerageRefundTerminationContFinish: bfsm.EventConfV2{IsAsync: true},
			},
		},
		StateNSConf: map[bfsm.NameSpace]map[int]string{
			bfsm.NameSpace("default"): {
				OrderInitStatus.Int(): "开始",
				// 订单状态
				ncBrokerageReviewWait.Int():                     "创建订单、待审核",
				ncBrokerageOnlyCancelReviewWait.Int():           "直接取消、待审核",
				ncBrokerageServiceContWait.Int():                "服务合同、待签署",
				ncBrokerageServiceContProcess.Int():             "服务合同、签约中",
				ncBrokerageTotalPayWait.Int():                   "全款资金、待支付",
				ncBrokerageAfterContCancelReviewWait.Int():      "终止合同、待审核",
				ncBrokerageTerminationContWait.Int():            "终止合同、待签署",
				ncBrokerageTerminationContProcess.Int():         "终止合同、签署中",
				ncBrokerageTotalPayProcess.Int():                "全款资金、支付中",
				ncBrokerageSettleWait.Int():                     "待客户提车、待分账",
				ncBrokerageSettleProcess.Int():                  "订单完成、分账中",
				ncBrokerageAfterPayCancelReviewWait.Int():       "退款资金、待审核",
				ncBrokerageAfterPayTerminationContWait.Int():    "退款终止合同、待签署",
				ncBrokerageAfterPayTerminationContProcess.Int(): "退款终止合同、签署中",
				ncBrokerageReviewFail.Int():                     "审核驳回",
				ncBrokerageTotalRefundProcess.Int():             "退款资金、退款中",
				ncBrokerageCustomerBookCarWait.Int():            "待客户下订",
				// 	终止状态
				ncBrokerageFinish.Int():                "订单成功",
				ncBrokerageOnlyCancel.Int():            "直接作废",
				ncBrokerageTerminationContCancel.Int(): "签署终止合同作废",
				ncBrokerageRefundCancel.Int():          "退款作废",
			},
		},
		EventNSConf: map[bfsm.NameSpace]map[string]string{
			bfsm.NameSpace("default"): {
				consts.CreateAction:                           "创建订单",
				consts.NCBrokerageReviewFailUpdateOrder:       "审核失败修改订单",
				consts.NCBrokerageReviewPass:                  "审核通过",
				consts.NCBrokerageReviewReject:                "审核驳回",
				consts.NCBrokerageServiceContStart:            "发起签署服务合同",
				consts.NCBrokerageServiceContFinish:           "服务合同签署回调",
				consts.NCBrokerageCancelStart:                 "订单取消",
				consts.NCBrokerageTerminationContStart:        "终止合同签署发起",
				consts.NCBrokerageTerminationContFinish:       "终止合同签署回调",
				consts.NCBrokerageTotalPayStart:               "全款支付发起",
				consts.NCBrokerageTotalPayFinish:              "全款支付回调",
				consts.NCBrokerageTotalPayTimeoutFinish:       "全款支付超时回调",
				consts.NCBrokerageTotalRefundFinish:           "全款退款回调",
				consts.NCBrokerageCustomerBookCarFinish:       "销售确认客户下订",
				consts.NCBrokerageSettleStart:                 "销售确认客户提车发起分账",
				consts.NCBrokerageSettleFinish:                "分账完成",
				consts.NCBrokerageRefundTerminationContFinish: "退款终止合同回调",
			},
		},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src: []int{
				int(OrderInitStatus),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageReviewWait),
				},
			},
		},
		{
			// 审核失败修改订单
			Event: consts.NCBrokerageReviewReject,
			Src: []int{
				int(ncBrokerageReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageReviewFail),
				},
			},
		},
		{
			// 审核失败修改订单
			Event: consts.NCBrokerageReviewFailUpdateOrder,
			Src: []int{
				int(ncBrokerageReviewFail),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageReviewWait),
				},
			},
		},
		{
			// 审核通过
			Event: consts.NCBrokerageReviewPass,
			Src: []int{
				int(ncBrokerageReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageServiceContWait),
				},
			},
		},
		{
			// 直接取消待审核
			Event: consts.NCBrokerageCancelStart,
			Src: []int{
				int(ncBigDepositReviewFail),
				int(ncBrokerageServiceContWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageOnlyCancelReviewWait),
				},
			},
		},
		{
			// 直接取消审核通过
			Event: consts.NCBrokerageReviewPass,
			Src: []int{
				int(ncBrokerageOnlyCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageOnlyCancel),
				},
			},
		},
		{
			// 直接取消审核通过
			Event: consts.NCBrokerageReviewReject,
			Src: []int{
				int(ncBrokerageOnlyCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncBrokerageServiceContWait),
					Condition: GetBeforeStatusCondition(ncBrokerageServiceContWait),
				},
				{
					Dst:       int(ncBrokerageReviewFail),
					Condition: GetBeforeStatusCondition(ncBrokerageReviewFail),
				},
			},
		},
		{
			// 发起服务合同签署
			Event: consts.NCBrokerageServiceContStart,
			Src: []int{
				int(ncBrokerageServiceContWait),
				int(ncBrokerageServiceContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageServiceContProcess),
				},
			},
		},
		{
			// 服务合同异步回调
			Event: consts.NCBrokerageServiceContFinish,
			Src: []int{
				int(ncBrokerageServiceContProcess),
				int(ncBrokerageServiceContWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageTotalPayWait),
				},
			},
		},
		{
			// 签署合同后取消待审核
			Event: consts.NCBrokerageCancelStart,
			Src: []int{
				int(ncBrokerageTotalPayWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageAfterContCancelReviewWait),
				},
			},
		},
		{
			// 签署合同后取消审核通过
			Event: consts.NCBrokerageReviewPass,
			Src: []int{
				int(ncBrokerageAfterContCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageTerminationContWait),
				},
			},
		},
		{
			// 签署合同后取消审核驳回
			Event: consts.NCBrokerageReviewReject,
			Src: []int{
				int(ncBrokerageAfterContCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncBrokerageTotalPayWait),
					Condition: GetBeforeStatusCondition(ncBrokerageTotalPayWait),
				},
			},
		},
		{
			// 签署合同后取消待发起终止合同
			Event: consts.NCBrokerageTerminationContStart,
			Src: []int{
				int(ncBrokerageTerminationContWait),
				int(ncBrokerageTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageTerminationContProcess),
				},
			},
		},
		{
			// 签署合同后取消待签署终止合同回调
			Event: consts.NCBrokerageTerminationContFinish,
			Src: []int{
				int(ncBrokerageTerminationContWait),
				int(ncBrokerageTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageTerminationContCancel),
				},
			},
		},
		{
			// 发起全款支付
			Event: consts.NCBrokerageTotalPayStart,
			Src: []int{
				int(ncBrokerageTotalPayWait),
				int(ncBrokerageTotalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageTotalPayProcess),
				},
			},
		},
		{
			// 全款完成回调
			Event: consts.NCBrokerageTotalPayFinish,
			Src: []int{
				int(ncBrokerageTotalPayWait),
				int(ncBrokerageTotalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageCustomerBookCarWait),
				},
			},
		},
		{
			// 全款超时回调
			Event: consts.NCBrokerageTotalPayTimeoutFinish,
			Src: []int{
				int(ncBrokerageTotalPayWait),
				int(ncBrokerageTotalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageTotalPayWait),
				},
			},
		},
		{
			// 销售确认客户下订
			Event: consts.NCBrokerageCustomerBookCarFinish,
			Src: []int{
				int(ncBrokerageCustomerBookCarWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageSettleWait),
				},
			},
		},
		{
			// 全款完成取消待审核
			Event: consts.NCBrokerageCancelStart,
			Src: []int{
				int(ncBrokerageCustomerBookCarWait),
				int(ncBrokerageSettleWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageAfterPayCancelReviewWait),
				},
			},
		},
		{
			// 全款完成取消待审核
			Event: consts.NCBrokerageReviewPass,
			Src: []int{
				int(ncBrokerageAfterPayCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageAfterPayTerminationContWait),
				},
			},
		},
		{
			// 全款完成取消驳回
			Event: consts.NCBrokerageReviewReject,
			Src: []int{
				int(ncBrokerageAfterPayCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncBrokerageSettleWait),
					Condition: GetBeforeStatusCondition(ncBrokerageSettleWait),
				},
				{
					Dst:       int(ncBrokerageCustomerBookCarWait),
					Condition: GetBeforeStatusCondition(ncBrokerageCustomerBookCarWait),
				},
			},
		},
		{
			// 全款完成后发起签署终止合同
			Event: consts.NCBrokerageTerminationContStart,
			Src: []int{
				int(ncBrokerageAfterPayTerminationContWait),
				int(ncBrokerageAfterPayTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageAfterPayTerminationContProcess),
				},
			},
		},
		{
			// 全款完成后发起签署终止合同回调
			Event: consts.NCBrokerageRefundTerminationContFinish,
			Src: []int{
				int(ncBrokerageAfterPayTerminationContWait),
				int(ncBrokerageAfterPayTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageTotalRefundProcess),
				},
			},
		},
		{
			// 全款完成后发起退款回调
			Event: consts.NCBrokerageTotalRefundFinish,
			Src: []int{
				// int(ncBrokerageAfterPayRefundWait),
				int(ncBrokerageAfterPayTerminationContWait),
				int(ncBrokerageAfterPayTerminationContProcess),
				int(ncBrokerageTotalRefundProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageRefundCancel),
				},
			},
		},
		{
			// 客户提车确认完成，发起分账
			Event: consts.NCBrokerageSettleStart,
			Src: []int{
				int(ncBrokerageSettleWait),
				int(ncBrokerageSettleProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageSettleProcess),
				},
			},
		},
		{
			// 分账完成
			Event: consts.NCBrokerageSettleFinish,
			Src: []int{
				int(ncBrokerageSettleWait),
				int(ncBrokerageSettleProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncBrokerageFinish),
				},
			},
		},
	},
}
