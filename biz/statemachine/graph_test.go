package statemachine

import (
	"code.byted.org/motor/fwe_trade_common/statemachine/finance_saas"
	"context"
	"fmt"
	"sort"
	"strconv"
	"testing"

	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

/*
	BFSM 改进 https://bytedance.feishu.cn/docx/doxcnHMUXQWmURk5e2wOHNSOpif
*/

func TestGraph(t *testing.T) {
	_ = finance_saas.SFMachine.ExportDOTV2("./dots/self_finance.dot", 0)
	/*_ = shSellEarnestFinalStateMachine.ExportDOT("./dots/sh_sell_earnest", "default")
	_ = shSellFullStateMachine.ExportDOT("./dots/sh_sell_full", "default")
	_ = shSellExtWarrantyStateMachine.ExportDOT("./dots/sh_sell_ext", "default")
	_ = shACNFullStateMachine.ExportDOT("./dots/sh_acn_full", "default")
	_ = shACNEFStateMachine.ExportDOT("./dots/sh_acn_ef", "default")
	_ = shConsignEFStateMachine.ExportDOT("./dots/sh_consign_earnest", "default")
	_ = shConsignFullStateMachine.ExportDOT("./dots/sh_consign_full", "default")
	_ = sHBuyPersonCarStateMachine.ExportDOT("./dots/sh_buy_person", "default")
	_ = sHBuyCompanyCarStateMachine.ExportDOT("./dots/sh_buy_company", "default")
	_ = sHBackCompanyCarStateMachine.ExportDOT("./dots/sh_back_company", "default")
	_ = ShFinanceOrderStateMachine.ExportDOT("./dots/sh_finance", "default")
	_ = ncBrokerageStateMachine.ExportDOT("./dots/nc_brokerage", "default")
	_ = ncBigDepositStateMachine.ExportDOT("./dots/nc_big_deposit", "default")
	_ = ncSmallDepositStateMachine.ExportDOT("./dots/nc_small_deposit", "default")
	_ = ams_state.AfterMarketRetailStateMachine.ExportDOT("./dots/aftermarket_retail", "default")
	_ = nc_state.NCPurchaseMachine.ExportDOT("./dots/nc_purchase", "default")

	_ = sh_sell.SHSellConsignRevokeSM.ExportDOT("./dots/sh_sell_consign_revoke", "default")*/

}

func TestCondition(t *testing.T) {
	Init()
	ctx := context.Background()
	m, err := bfsm.NewFSM(consts.BizSceneTest.String(), 1, map[int]int{})
	if err != nil {
		panic(err)
	}
	flag := m.Can("Skip")
	fmt.Println(flag)
	err = m.Fire(ctx, "Skip", map[string]interface{}{"a": 0})
	if err != nil {
		panic(err)
	}
	fmt.Println(m.GetOriginalState(), m.CurState())
}

func TestSameEvent(t *testing.T) {
	Init()
	ctx := context.Background()
	m, err := bfsm.NewFSM(consts.BizSceneTest.String(), 2, map[int]int{})
	if err != nil {
		panic(err)
	}
	flag := m.Can("SameEvent")
	fmt.Println(flag)
	err = m.Fire(ctx, "SameEvent", nil)
	if err != nil {
		panic(err)
	}
	fmt.Println(m.GetOriginalState(), m.CurState())
}

type Data struct {
	BizScene int32  `json:"biz_scene"`
	Status   int32  `json:"status"`
	Name     string `json:"name"`
}

// go test ./biz/statemachine -v -count=1 -run="TestGetFSMConfig$"
func TestGetFSMConfig(t *testing.T) {
	var list []*Data
	for bizScene, fsm := range stateMachineConfig {
		if fsm.NSConf.StateNSConf == nil || fsm.NSConf.StateNSConf["default"] == nil {
			continue
		}
		stateMap := fsm.NSConf.StateNSConf["default"]
		bizSceneInt, _ := strconv.Atoi(bizScene)
		for state, name := range stateMap {
			data := &Data{
				BizScene: int32(bizSceneInt),
				Status:   int32(state),
				Name:     name,
			}
			list = append(list, data)
		}
	}

	sort.Slice(list, func(i, j int) bool {
		if list[i].BizScene < list[j].BizScene {
			return true
		}
		return list[i].BizScene == list[j].BizScene && list[i].Status < list[j].Status
	})

	fmt.Println("场景\t状态\t名称")
	bizScenes := make([]int32, 0)
	for _, v := range list {
		bizScenes = append(bizScenes, v.BizScene)
		fmt.Printf("%d\t%d\t%s\n", v.BizScene, v.Status, v.Name)
	}
}
