package statemachine

import (
	"fmt"
	"strconv"

	"code.byted.org/motor/fwe_trade_common/scene"

	"code.byted.org/motor/fwe_trade_common/statemachine/nc_state/nc_ecom"

	"code.byted.org/motor/fwe_trade_common/statemachine/supply_state"

	"code.byted.org/motor/bfsm"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/statemachine/ams_state"
	"code.byted.org/motor/fwe_trade_common/statemachine/finance_saas"
	"code.byted.org/motor/fwe_trade_common/statemachine/nc_state"
	CommonSHSell "code.byted.org/motor/fwe_trade_common/statemachine/sh_sell"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

// BFSM 状态机 https://tech.bytedance.net/articles/6979877701590974471#heading13

type Status int

const OrderInitStatus Status = 0

func (s Status) Int32() int32 {
	return int32(s)
}

func (s Status) Int() int {
	return int(s)
}

func (s Status) String() string {
	return strconv.Itoa(int(s))
}

func S(s Status) string {
	return s.String()
}

var stateMachineConfig = map[string]bfsm.BizDesc{
	consts.BizSceneTest.String():                                                   testStateMachine,
	consts.BizSceneSHSellByEarnestFinal.String():                                   shSellEarnestFinalStateMachine,
	consts.BizSceneSHSellByFull.String():                                           shSellFullStateMachine,
	consts.BizSceneSHSellInsurance.String():                                        shSellExtWarrantyStateMachine,
	consts.BizSceneSHConsignByEF.String():                                          shConsignEFStateMachine,
	consts.BizSceneSHConsignByFull.String():                                        shConsignFullStateMachine,
	consts.BizSceneSHBuyPersonCar.String():                                         sHBuyPersonCarStateMachine,
	consts.BizSceneSHBuyCompanyCar.String():                                        sHBuyCompanyCarStateMachine,
	consts.BizSceneSHBackCompanyCar.String():                                       sHBackCompanyCarStateMachine,
	consts.BizSceneSHDirectBuyCompanyCar.String():                                  sh_state.SHDirectBuyCompanyCarStateMachine,
	consts.BizScene(CommonConsts.BizSceneSHBuyBackPersonCar.Value()).String():      sh_state.SHBuyBackPersonCarStateMachine,
	consts.BizScene(CommonConsts.BizSceneSHBuyBackCompanyCar.Value()).String():     sh_state.SHBuyBackCompanyCarStateMachine,
	consts.BizSceneSHFinance.String():                                              ShFinanceOrderStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneNCBrokerage.Value()):                    nc_state.NCBrokerageStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneNCBrokerageNoHorizon.Value()):           nc_state.NCBrokerageNoHorizonStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneNCBigDeposit.Value()):                   nc_state.NCBigDepositStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneNCSmallDeposit.Value()):                 nc_state.NCSmallDepositStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneNCFranchiseeBrokerage.Value()):          nc_state.NCFranchiseeBrokerageStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneNCFranchiseeBrokerageNoHorizon.Value()): nc_state.NCFranchiseeBrokerageNoHorizonStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneNCFranchiseeBigDeposit.Value()):         nc_state.NCFranchiseeBigDepositStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneNCFranchiseeSmallDeposit.Value()):       nc_state.NCFranchiseeSmallDepositStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneAfterMarketRetail.Value()):              ams_state.AfterMarketRetailStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneNCPurchase.Value()):                     nc_state.NCPurchaseMachine,
	strconv.Itoa(int(CommonConsts.BizSceneSHNationSell.Value())):                   sh_state.SHNationSellStateMachine,
	strconv.Itoa(int(CommonConsts.BizSceneSHNationSellV2.Value())):                 sh_state.SHNationSellV2StateMachine,
	strconv.Itoa(int(CommonConsts.BizSceneSHNationInsurance.Value())):              sh_state.SHNationWarrantyStateMachine,
	fmt.Sprintf("%d", CommonConsts.BizSceneFinanceSaas.Value()):                    finance_saas.FSMachine,
	consts.BizSceneSHSellConsignRevoke.String():                                    CommonSHSell.SHSellConsignRevokeSM,
	consts.BizScene(CommonConsts.BizSceneSHAuctionSell.Value()).String():           sh_state.SHAuctionSellSM,
	consts.BizScene(CommonConsts.BizSceneSHAuctionDeposit.Value()).String():        sh_state.SHAuctionDepositSM,
	consts.BizScene(CommonConsts.BizSceneSHAuctionDisputeRefund.Value()).String():  sh_state.SHAuctionDisputeRefundSM,
	consts.BizScene(CommonConsts.BizSceneSHAuctionDisputePayout.Value()).String():  sh_state.SHAuctionDisputePayoutSM,
	consts.BizScene(CommonConsts.BizScenePurchaseFinance.Value()).String():         finance_saas.PFMachine,
	consts.BizScene(CommonConsts.BizSceneSelfFinance.Value()).String():             finance_saas.SFMachine,
	consts.BizScene(CommonConsts.BizSceneSHAuctionSellV2.Value()).String():         sh_state.SHAuctionSellV2SM,
	consts.BizSceneSHSellByEarnestFinalDeliveryCar.String():                        sh_state.SHSellEarnestFinalDeliveryCarStateMachine,
	consts.BizSceneSHSellByFullDeliveryCar.String():                                sh_state.SHSellFullDeliveryCarStateMachine,
	consts.BizSceneSHConsignByEarnestFinalDeliveryCar.String():                     sh_state.SHConsignEFDeliveryCarStateMachine,
	consts.BizSceneSHConsignByFullDeliveryCar.String():                             sh_state.ShConsignFullDeliveryCarStateMachine,
	consts.BizScene(CommonConsts.BizSceneNewCarEcom.Value()).String():              nc_state.NCEcomMachine,
	consts.BizScene(CommonConsts.BizSceneNewCarEcomFixedPrice.Value()).String():    nc_ecom.NCEcomFixedPriceSM,
	consts.BizScene(CommonConsts.BizSceneNewCarEcomServiceFee.Value()).String():    nc_ecom.NCEcomServiceFeeSM,

	// ----------------- 供应链业务 start -----------------
	consts.BizScene(CommonConsts.BizSceneCarSupplyIncreaseTicket.Value()).String():           supply_state.CarSupplyIncreaseTicketSM,           // 供应链-二网增票
	consts.BizScene(CommonConsts.BizSceneCarSupplyPassengerTicket.Value()).String():          supply_state.CarSupplyPassengerTicketSM,          // 供应链-二网客票
	consts.BizScene(CommonConsts.BizSceneCarSupplyIncreaseTicketNC.Value()).String():         supply_state.CarSupplyIncreaseTicketNCSM,         // 供应链-门店增票
	consts.BizScene(CommonConsts.BizSceneCarSupplyPassengerTicketNC.Value()).String():        supply_state.CarSupplyPassengerTicketNCSM,        // 供应链-门店客票
	consts.BizScene(CommonConsts.BizSceneCarSupplyInner.Value()).String():                    supply_state.CarSupplyInnerSM,                    // 供应链-体验店
	consts.BizScene(CommonConsts.BizSceneCarSupplyIncreaseTicketFranchise.Value()).String():  supply_state.CarSupplyIncreaseTicketFranchiseSM,  // 供应链-加盟店增票
	consts.BizScene(CommonConsts.BizSceneCarSupplyPassengerTicketFranchise.Value()).String(): supply_state.CarSupplyPassengerTicketFranchiseSM, // 供应链-加盟店客票
	consts.BizScene(CommonConsts.BizSceneCarSupplyFinance.Value()).String():                  supply_state.CarSupplyFinanceSM,                  // 供应链 - 金融
	consts.BizScene(CommonConsts.BizSceneCarSupplySecuredTransaction.Value()).String():       supply_state.CarSupplySecuredTransactionSM,       // 供应链-普通担保
	consts.BizScene(CommonConsts.BizSceneCarSupplySecuredTransactionNC.Value()).String():     supply_state.CarSupplySecuredTransactionNCSM,     // 供应链-门店担保
	consts.BizScene(CommonConsts.BizSceneCarSupplyExhibitionCarNC.Value()).String():          supply_state.CarSupplyExhibitionCarNCSM,          // 供应链-门店展车
	consts.BizScene(CommonConsts.BizSceneCarSupplyExhibitionChangeCarNC.Value()).String():    supply_state.CarSupplyExhibitionChangeCarNCSM,    // 供应链-门店展车-换车
	consts.BizScene(CommonConsts.BizSceneCarSupplyFinanceMatch.Value()).String():             supply_state.CarSupplyFinanceMatchSM,             // 供应链-金融撮合
	consts.BizScene(CommonConsts.BizSceneCarSupplyEcomSecuredTransaction.Value()).String():   supply_state.CarSupplyEcomSTSM,                   // 供应链-电商担保
	consts.BizScene(CommonConsts.BizSceneCarSupplyPreOrder.Value()).String():                 supply_state.CarSupplyPreOrderSM,                 // 供应链-预订单
	consts.BizScene(CommonConsts.BizSceneCarSupplyClearanceAuction.Value()).String():         supply_state.CarSupplyClearanceAuctionSM,         // 供应链-斩仓拍卖
	consts.BizScene(CommonConsts.BizSceneCarSupplyTPAPPassengerTicket.Value()).String():      supply_state.CarSupplyTPAPPassengerTicketSM,      // 供应链 - 三方代采 - 普通客票
	consts.BizScene(CommonConsts.BizSceneCarSupplyTPAPPassengerTicketNC.Value()).String():    supply_state.CarSupplyTPAPPassengerTicketNCSM,    // 供应链 - 三方代采 - 门店客票
	consts.BizScene(CommonConsts.BizSceneCarSupplyWarehouseFinance.Value()).String():         supply_state.CarSupplyWarehouseFinanceSM,         // 供应链-自营库融
	consts.BizScene(CommonConsts.BizSceneCarSupplySearchCarsOrder.Value()).String():          supply_state.CarSupplySearchCarsOrderSM,          // 供应链-寻车单
	// ----------------- 供应链业务 end  -----------------
}

var canErrMsgConfig = map[consts.BizScene]map[Status]string{
	consts.BizSceneSHBuyPersonCar:     sHBuyPersonCanErr,
	consts.BizSceneSHBuyCompanyCar:    sHBuyCompanyCanErr,
	consts.BizSceneSHBackCompanyCar:   sHBackCompanyCanErr,
	consts.BizSceneSHBuyBackPersonCar: sHBuyBackPersonCanErr,
}

func MakeStateMachineKey(bizScene, smVersion int32) string {
	if smVersion == 0 {
		return fmt.Sprintf("%d", bizScene)
	}
	return fmt.Sprintf("%d_%d", bizScene, smVersion)
}

func Init() {
	// 直接从SDK读取配置
	for _, v := range scene.AllBizSceneMap {
		for _, w := range v.SmMap() {
			stateMachineConfig[MakeStateMachineKey(v.Value(), w.Value())] = *w.BFsmDesc()
		}
	}
	// 初始化
	if err := bfsm.Init(stateMachineConfig, nil); err != nil {
		panic(err)
	}
}

func GetCanErrMsg(bizScene consts.BizScene, status Status) string {
	return canErrMsgConfig[bizScene][status]
}
