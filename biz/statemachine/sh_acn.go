package statemachine

import (
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

var (
	// 订金-尾款 状态
	shACNEFOrderInit             = bfsm.NewState(0, "订单初始状态", bfsm.WithStateTypeOption(bfsm.Begin))
	shACNEFCreate                = bfsm.NewState(1, "订单创建")
	shACNEFSigningIntentContract = bfsm.NewState(2, "意向合同签署中")
	shACNEFToPayEarnestMoney     = bfsm.NewState(3, "订金待支付")
	shACNEFPayingEarnestMoney    = bfsm.NewState(4, "订金支付中")
	shACNEFToSignSellContract    = bfsm.NewState(5, "买卖合同待签署")
	shACNEFSigningSellContract   = bfsm.NewState(6, "买卖合同签署中")
	shACNEFToPayFinalMoney       = bfsm.NewState(7, "尾款待支付")
	shACNEFPayingFinalMoney      = bfsm.NewState(8, "尾款支付中")
	shACNEFRefunding             = bfsm.NewState(9, "退款中")
	shACNEFToTransferOwner       = bfsm.NewState(10, "待过户")
	shACNEFToSelectLoan          = bfsm.NewState(11, "待选择贷款金融方式")
	shACNEFToConfirmLoan         = bfsm.NewState(12, "待确认贷款")
	shACNEFToApproveLoan         = bfsm.NewState(13, "待审核贷款")
	shACNEFLoaning               = bfsm.NewState(14, "贷款中")
	shACNEFToDeliveryCar         = bfsm.NewState(16, "待交车")
	shACNEFOver                  = bfsm.NewState(100, "已完成")
	shACNEFCanceling             = bfsm.NewState(199, "作废中") // （从"买卖合同待签署"作废时，有作废中的状态，因为需要退订金）
	shACNEFCancellation          = bfsm.NewState(200, "已作废", bfsm.WithStateTypeOption(bfsm.End))

	// 全款
	shACNFullOrderInit       = bfsm.NewState(0, "订单初始状态", bfsm.WithStateTypeOption(bfsm.Begin))
	shACNFullCreate          = bfsm.NewState(1, "订单创建")
	shACNFullSigningContract = bfsm.NewState(2, "买卖合同签署中")
	shACNFullToPay           = bfsm.NewState(3, "全款待支付")
	shACNFullPaying          = bfsm.NewState(4, "全款支付中")
	shACNFullToTransferOwner = bfsm.NewState(5, "待过户")
	shACNFullToSelectLoan    = bfsm.NewState(11, "待选择贷款金融方式")
	shACNFullToConfirmLoan   = bfsm.NewState(12, "待确认贷款")
	shACNFullToApproveLoan   = bfsm.NewState(13, "待审核贷款")
	shACNFullLoaning         = bfsm.NewState(14, "贷款中")
	shACNFullToDeliveryCar   = bfsm.NewState(16, "待交车")
	shACNFullOver            = bfsm.NewState(100, "已完成")
	shACNFullCancellation    = bfsm.NewState(200, "已作废", bfsm.WithStateTypeOption(bfsm.End))

	ShACNEFCreateEvent                    = bfsm.NewEvent("create", "创建订单")
	ShACNEFSignIntentContractEvent        = bfsm.NewEvent("SignIntentContract", "签署意向合同")
	ShACNEFSignIntentContractOverEvent    = bfsm.NewEvent("SignIntentContractOver", "意向合同签署完成", bfsm.WithAsyncEventOption())
	ShACNEFPayEarnestEvent                = bfsm.NewEvent("PayEarnest", "支付订金")
	ShACNEFPayEarnestOverEvent            = bfsm.NewEvent("PayEarnestOver", "订金支付完成", bfsm.WithAsyncEventOption())
	ShACNEFRefundEarnestOverEvent         = bfsm.NewEvent("RefundEarnestOver", "退订金完成", bfsm.WithAsyncEventOption())
	ShACNEFRefundEarnestOverAfterPOSEvent = bfsm.NewEvent("RefundEarnestOverAfterPOS", "POS支付后退订金完成", bfsm.WithAsyncEventOption())
	ShACNEFSignSellContractEvent          = bfsm.NewEvent("SignSellContract", "签署买卖合同")
	ShACNEFSignSellContractOverEvent      = bfsm.NewEvent("SignSellContractOver", "买卖合同签署完成", bfsm.WithAsyncEventOption())
	ShACNEFPayFinalEvent                  = bfsm.NewEvent("PayFinal", "支付尾款")
	ShACNEFPayFinalOverEvent              = bfsm.NewEvent("PayFinalOver", "尾款支付完成", bfsm.WithAsyncEventOption())
	ShACNEFTransferOwnerEvent             = bfsm.NewEvent("TransferOwner", "过户")
	ShACNEFSelectLoanEvent                = bfsm.NewEvent("SelectLoan", "确定金融贷款方式")
	ShACNEFConfirmLoanEvent               = bfsm.NewEvent("ConfirmLoan", "确认贷款")
	ShACNEFApproveLoanPassEvent           = bfsm.NewEvent("ApproveLoanPass", "审核贷款通过")
	ShACNEFApproveLoanFailEvent           = bfsm.NewEvent("ApproveLoanFail", "审核贷款不通过")
	ShACNEFLoanOverEvent                  = bfsm.NewEvent("LoanOver", "金融贷款完成", bfsm.WithAsyncEventOption())
	ShACNEFDeliveryEvent                  = bfsm.NewEvent("DeliveryCar", "交车")
	ShACNEFCancelEvent                    = bfsm.NewEvent("Cancel", "作废")
	ShACNEFCancelWithRefundEvent          = bfsm.NewEvent("CancelWithRefund", "作废并退款")
	ShACNEFPayPOSTimeoutEvent             = bfsm.NewEvent("PayPOSTimeout", "支付POS超时", bfsm.WithAsyncEventOption())

	ShACNFullCreateEvent               = bfsm.NewEvent("create", "创建订单")
	ShACNFullSignSellContractEvent     = bfsm.NewEvent("SignSellContract", "签署买卖合同")
	ShACNFullSignSellContractOverEvent = bfsm.NewEvent("SignSellContractOver", "买卖合同签署完成", bfsm.WithAsyncEventOption())
	ShACNFullPayMoneyEvent             = bfsm.NewEvent("PayMoney", "支付")
	ShACNFullPayMoneyOverEvent         = bfsm.NewEvent("PayMoneyOver", "支付完成", bfsm.WithAsyncEventOption())
	ShACNFullTransferOwnerEvent        = bfsm.NewEvent("TransferOwner", "过户")
	ShACNFullSelectLoanEvent           = bfsm.NewEvent("SelectLoan", "确定金融贷款方式")
	ShACNFullConfirmLoanEvent          = bfsm.NewEvent("ConfirmLoan", "确认贷款")
	ShACNFullApproveLoanPassEvent      = bfsm.NewEvent("ApproveLoanPass", "审核贷款通过")
	ShACNFullApproveLoanFailEvent      = bfsm.NewEvent("ApproveLoanFail", "审核贷款不通过")
	ShACNFullLoanOverEvent             = bfsm.NewEvent("LoanOver", "金融贷款完成", bfsm.WithAsyncEventOption())
	ShACNFullDeliveryEvent             = bfsm.NewEvent("DeliveryCar", "交车")
	ShACNFullCancelEvent               = bfsm.NewEvent("Cancel", "作废")
	ShACNFullPayPOSTimeoutEvent        = bfsm.NewEvent("PayPOSTimeout", "支付POS超时", bfsm.WithAsyncEventOption())
)

var shACNEFStateMachine = bfsm.BizDesc{
	Conf: bfsm.Conf{
		States: []bfsm.State{
			shACNEFCreate,
			shACNEFSigningIntentContract,
			shACNEFToPayEarnestMoney,
			shACNEFPayingEarnestMoney,
			shACNEFToSignSellContract,
			shACNEFSigningSellContract,
			shACNEFToPayFinalMoney,
			shACNEFPayingFinalMoney,
			shACNEFRefunding,
			shACNEFToTransferOwner,
			shACNEFToSelectLoan,
			shACNEFToConfirmLoan,
			shACNEFToApproveLoan,
			shACNEFLoaning,
			shACNEFToDeliveryCar,
			shACNEFOver,
			shACNEFCanceling,
			shACNEFCancellation},
		Events: []bfsm.Event{
			ShACNEFSignIntentContractEvent,
			ShACNEFSignIntentContractOverEvent,
			ShACNEFPayEarnestEvent,
			ShACNEFPayEarnestOverEvent,
			ShACNEFRefundEarnestOverEvent,
			ShACNEFRefundEarnestOverAfterPOSEvent,
			ShACNEFSignSellContractEvent,
			ShACNEFSignSellContractOverEvent,
			ShACNEFPayFinalEvent,
			ShACNEFPayFinalOverEvent,
			ShACNEFTransferOwnerEvent,
			ShACNEFSelectLoanEvent,
			ShACNEFConfirmLoanEvent,
			ShACNEFApproveLoanPassEvent,
			ShACNEFApproveLoanFailEvent,
			ShACNEFLoanOverEvent,
			ShACNEFDeliveryEvent,
			ShACNEFCancelEvent,
			ShACNEFCancelWithRefundEvent,
			ShACNEFPayPOSTimeoutEvent,
		},
		MasterFLow: []bfsm.State{
			shACNEFCreate,
			shACNEFSigningIntentContract,
			shACNEFToPayEarnestMoney,
			shACNEFPayingEarnestMoney,
			shACNEFToSignSellContract,
			shACNEFSigningSellContract,
			shACNEFToPayFinalMoney,
			shACNEFPayingFinalMoney,
			shACNEFRefunding,
			shACNEFToSelectLoan,
			shACNEFToConfirmLoan,
			shACNEFToApproveLoan,
			shACNEFLoaning,
			shACNEFToTransferOwner,
			shACNEFToDeliveryCar,
			shACNEFOver,
		},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: ShACNEFCreateEvent.Value(),
			Src:   []int{shACNEFOrderInit.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFCreate.Value(),
				},
			},
		},
		{
			// 签署意向合同（发短信）
			Event: ShACNEFSignIntentContractEvent.Value(),
			Src:   []int{shACNEFCreate.Value(), shACNEFSigningIntentContract.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFSigningIntentContract.Value(),
				},
			},
		},
		{
			// 意向合同签署完成
			Event: ShACNEFSignIntentContractOverEvent.Value(),
			Src:   []int{shACNEFCreate.Value(), shACNEFSigningIntentContract.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFToPayEarnestMoney.Value(),
				},
			},
		},
		{
			// 支付意向金(获取链接)
			Event: ShACNEFPayEarnestEvent.Value(),
			Src:   []int{shACNEFToPayEarnestMoney.Value(), shACNEFPayingEarnestMoney.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFPayingEarnestMoney.Value(),
				},
			},
		},
		{
			// 支付意向金完成
			Event: ShACNEFPayEarnestOverEvent.Value(),
			Src:   []int{shACNEFToPayEarnestMoney.Value(), shACNEFPayingEarnestMoney.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFToSignSellContract.Value(),
				},
			},
		},
		{
			// 签署买卖合同(发短信)
			Event: ShACNEFSignSellContractEvent.Value(),
			Src:   []int{shACNEFToSignSellContract.Value(), shACNEFSigningSellContract.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFSigningSellContract.Value(),
				},
			},
		},
		{
			// 签署买卖合同完成
			Event: ShACNEFSignSellContractOverEvent.Value(),
			Src:   []int{shACNEFToSignSellContract.Value(), shACNEFSigningSellContract.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFToPayFinalMoney.Value(),
				},
			},
		},
		{
			// 支付尾款（获取链接）
			Event: ShACNEFPayFinalEvent.Value(),
			Src:   []int{shACNEFToPayFinalMoney.Value(), shACNEFPayingFinalMoney.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFPayingFinalMoney.Value(),
				},
			},
		},
		{
			// 支付尾款完成
			Event: ShACNEFPayFinalOverEvent.Value(),
			Src:   []int{shACNEFToPayFinalMoney.Value(), shACNEFPayingFinalMoney.Value()},
			Matchers: []bfsm.Matcher{
				{
					// 退订金
					Dst: shACNEFRefunding.Value(),
				},
			},
		},
		{
			// 支付尾款超时（回调时会判断是否有进行中的支付）
			Event: ShACNEFPayPOSTimeoutEvent.Value(),
			Src:   []int{shACNEFPayingFinalMoney.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFToPayFinalMoney.Value(),
				},
			},
		},
		{
			// POS支付后退款完成
			Event: ShACNEFRefundEarnestOverAfterPOSEvent.Value(),
			Src:   []int{shACNEFToPayFinalMoney.Value(), shACNEFPayingFinalMoney.Value(), shACNEFRefunding.Value()},
			Matchers: []bfsm.Matcher{
				{
					// 没有金融节点，流转到"待过户"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       shACNEFToTransferOwner.Value(),
				},
				{
					// 有金融节点，流转到"待选择金融方式"
					Condition: consts.CondShSellHasLoan.Val(),
					Dst:       shACNEFToSelectLoan.Value(),
				},
			},
		},
		{
			// 选择贷款方式
			Event: ShACNEFSelectLoanEvent.Value(),
			Src:   []int{shACNEFToSelectLoan.Value()},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       shACNEFToTransferOwner.Value(),
				},
				{
					// 先贷款后过户，流转到"待确认贷款"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       shACNEFToConfirmLoan.Value(),
				},
			},
		},
		{
			// 确认贷款
			Event: ShACNEFConfirmLoanEvent.Value(),
			Src:   []int{shACNEFToConfirmLoan.Value(), shACNFullToApproveLoan.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFToApproveLoan.Value(),
				},
			},
		},
		{
			// 审核贷款通过 --> 贷款中
			Event: ShACNEFApproveLoanPassEvent.Value(),
			Src:   []int{shACNEFToApproveLoan.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFLoaning.Value(),
				},
			},
		},
		{
			// 审核贷款不通过 --> 待确认贷款
			Event: ShACNEFApproveLoanFailEvent.Value(),
			Src:   []int{shACNEFToApproveLoan.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFToConfirmLoan.Value(),
				},
			},
		},
		{
			// 贷款完成
			Event: ShACNEFLoanOverEvent.Value(),
			Src:   []int{shACNEFLoaning.Value()},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待交车"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       shACNEFToDeliveryCar.Value(),
				},
				{
					// 先贷款后过户，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       shACNEFToTransferOwner.Value(),
				},
			},
		},
		{
			// 过户
			Event: ShACNEFTransferOwnerEvent.Value(),
			Src:   []int{shACNEFToTransferOwner.Value()},
			Matchers: []bfsm.Matcher{
				{
					// 没有有金融节点，流转到"待交车"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       shACNEFToDeliveryCar.Value(),
				},
				{
					// 有金融节点 && 先过户后贷款，流转到"待确认贷款"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst.Not()).Val(),
					Dst:       shACNEFToConfirmLoan.Value(),
				},
				{
					// 有金融节点 && 先贷款后过户，流转到"待交车"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst).Val(),
					Dst:       shACNEFToDeliveryCar.Value(),
				},
			},
		},
		{
			// 交车
			Event: ShACNEFDeliveryEvent.Value(),
			Src:   []int{shACNEFToDeliveryCar.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFOver.Value(),
				},
			},
		},
		{
			// 作废，涉及到订金退款
			Event: ShACNEFCancelWithRefundEvent.Value(),
			Src:   []int{shACNEFToSignSellContract.Value(), shACNEFSigningSellContract.Value(), shACNEFToPayFinalMoney.Value(), shACNEFCanceling.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFCanceling.Value(),
				},
			},
		},
		{
			// 作废，涉及到订金退款回调成功，退款完成
			Event: ShACNEFRefundEarnestOverEvent.Value(),
			Src:   []int{shACNEFCanceling.Value(), shACNEFToSignSellContract.Value(), shACNEFSigningSellContract.Value(), shACNEFToPayFinalMoney.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFCancellation.Value(),
				},
			},
		},
		{
			// 作废
			Event: ShACNEFCancelEvent.Value(),
			Src:   []int{shACNEFCreate.Value(), shACNEFSigningIntentContract.Value(), shACNEFToPayEarnestMoney.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNEFCancellation.Value(),
				},
			},
		},
	},
}

var shACNFullStateMachine = bfsm.BizDesc{
	Conf: bfsm.Conf{
		States: []bfsm.State{
			shACNFullOrderInit,
			shACNFullCreate,
			shACNFullSigningContract,
			shACNFullToPay,
			shACNFullPaying,
			shACNFullToTransferOwner,
			shACNFullToSelectLoan,
			shACNFullToConfirmLoan,
			shACNFullToApproveLoan,
			shACNFullLoaning,
			shACNFullToDeliveryCar,
			shACNFullOver,
			shACNFullCancellation,
		},
		Events: []bfsm.Event{
			ShACNFullSignSellContractEvent,
			ShACNFullSignSellContractOverEvent,
			ShACNFullPayMoneyEvent,
			ShACNFullPayMoneyOverEvent,
			ShACNFullTransferOwnerEvent,
			ShACNFullSelectLoanEvent,
			ShACNFullConfirmLoanEvent,
			ShACNFullApproveLoanPassEvent,
			ShACNFullApproveLoanFailEvent,
			ShACNFullLoanOverEvent,
			ShACNFullDeliveryEvent,
			ShACNFullCancelEvent,
			ShACNFullPayPOSTimeoutEvent,
		},
		MasterFLow: []bfsm.State{
			shACNFullOrderInit,
			shACNFullCreate,
			shACNFullSigningContract,
			shACNFullToPay,
			shACNFullPaying,
			shACNFullToSelectLoan,

			shACNFullToDeliveryCar,
			shACNFullOver,
		},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: ShACNFullCreateEvent.Value(),
			Src:   []int{shACNEFOrderInit.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNFullCreate.Value(),
				},
			},
		},
		{
			// 签署买卖合同（发短信）
			Event: ShACNFullSignSellContractEvent.Value(),
			Src:   []int{shACNFullCreate.Value(), shACNFullSigningContract.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNFullSigningContract.Value(),
				},
			},
		},
		{
			// 签署买卖合同完成
			Event: ShACNFullSignSellContractOverEvent.Value(),
			Src:   []int{shACNFullCreate.Value(), shACNFullSigningContract.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNFullToPay.Value(),
				},
			},
		},
		{
			// 支付全款（获取链接）
			Event: ShACNFullPayMoneyEvent.Value(),
			Src:   []int{shACNFullToPay.Value(), shACNFullPaying.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNFullPaying.Value(),
				},
			},
		},
		{
			// 支付完成
			Event: ShACNFullPayMoneyOverEvent.Value(),
			Src:   []int{shACNFullToPay.Value(), shACNFullPaying.Value()},
			Matchers: []bfsm.Matcher{
				{
					// 没有金融节点，流转到"待过户"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       shACNFullToTransferOwner.Value(),
				},
				{
					// 有金融节点，流转到"待选择金融方式"
					Condition: consts.CondShSellHasLoan.Val(),
					Dst:       shACNFullToSelectLoan.Value(),
				},
			},
		},
		{
			// 支付尾款超时（回调时会判断是否有进行中的支付）
			Event: ShACNFullPayPOSTimeoutEvent.Value(),
			Src:   []int{shACNFullPaying.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNFullToPay.Value(),
				},
			},
		},
		{
			// 选择贷款方式
			Event: ShACNFullSelectLoanEvent.Value(),
			Src:   []int{shACNFullToSelectLoan.Value()},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       shACNFullToTransferOwner.Value(),
				},
				{
					// 先贷款后过户，流转到"待确认贷款"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       shACNFullToConfirmLoan.Value(),
				},
			},
		},
		{
			// 确认贷款
			Event: ShACNFullConfirmLoanEvent.Value(),
			Src:   []int{shACNFullToConfirmLoan.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNFullToApproveLoan.Value(),
				},
			},
		},
		{
			// 审核贷款通过 --> 贷款中
			Event: ShACNFullApproveLoanPassEvent.Value(),
			Src:   []int{shACNFullToApproveLoan.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNFullLoaning.Value(),
				},
			},
		},
		{
			// 审核贷款不通过 --> 待确认贷款
			Event: ShACNFullApproveLoanFailEvent.Value(),
			Src:   []int{shACNFullToApproveLoan.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNFullToConfirmLoan.Value(),
				},
			},
		},
		{
			// 贷款完成
			Event: ShACNFullLoanOverEvent.Value(),
			Src:   []int{shACNFullLoaning.Value()},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"结算中"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       shACNEFToDeliveryCar.Value(),
				},
				{
					// 先贷款后过户，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       shACNFullToTransferOwner.Value(),
				},
			},
		},
		{
			// 过户
			Event: ShACNFullTransferOwnerEvent.Value(),
			Src:   []int{shACNFullToTransferOwner.Value()},
			Matchers: []bfsm.Matcher{
				{
					// 没有有金融节点，流转到"待交车"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       shACNEFToDeliveryCar.Value(),
				},
				{
					// 有金融节点 && 先过户后贷款，流转到"待确认贷款"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst.Not()).Val(),
					Dst:       shACNFullToConfirmLoan.Value(),
				},
				{
					// 有金融节点 && 先贷款后过户，流转到"待交车"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst).Val(),
					Dst:       shACNEFToDeliveryCar.Value(),
				},
			},
		},
		{
			// 交车
			Event: ShACNFullDeliveryEvent.Value(),
			Src:   []int{shACNFullToDeliveryCar.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNFullOver.Value(),
				},
			},
		},
		{
			// 作废
			Event: ShACNFullCancelEvent.Value(),
			Src:   []int{shACNFullCreate.Value(), shACNFullSigningContract.Value(), shACNFullToPay.Value()},
			Matchers: []bfsm.Matcher{
				{
					Dst: shACNFullCancellation.Value(),
				},
			},
		},
	},
}
