package statemachine

import (
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

/*
	新车 小订模式
*/

const (
	// 订单状态

	// 合同
	ncSmallDepositBusinessContWait                        Status = 3  // 买卖合同、待签署
	ncSmallDepositBusinessContProcess                     Status = 4  // 买卖合同、签约中
	ncSmallDepositCheckCarContWait                        Status = 5  // 验车合同、待签署
	ncSmallDepositCheckCarContProcess                     Status = 6  // 验车合同、签署中
	ncSmallDepositIntentionContWait                       Status = 7  // 意向合同、待签署
	ncSmallDepositIntentionContProcess                    Status = 8  // 意向合同、签约中
	ncSmallDepositTerminationContWait                     Status = 51 // 终止合同、待签署
	ncSmallDepositTerminationContProcess                  Status = 52 // 终止合同、签署中
	ncSmallDepositAfterBigEarnestTerminationContWait      Status = 55 // 退大订终止合同、待签署
	ncSmallDepositAfterBigEarnestTerminationContProcess   Status = 56 // 退大订终止合同、签署中
	ncSmallDepositAfterSmallEarnestTerminationContWait    Status = 57 // 退小订终止合同、待签署
	ncSmallDepositAfterSmallEarnestTerminationContProcess Status = 58 // 退小订终止合同、签署中

	// 支付
	ncSmallDepositSmallEarnestPayWait                        Status = 15 // 小订资金、待支付
	ncSmallDepositSmallEarnestPayProcess                     Status = 16 // 小订资金、支付中
	ncSmallDepositBigEarnestPayWait                          Status = 13 // 大订资金、待支付
	ncSmallDepositBigEarnestPayProcess                       Status = 14 // 大订资金、支付中
	ncSmallDepositFinalPayWait                               Status = 17 // 尾款支付，待支付
	ncSmallDepositFinalPayProcess                            Status = 18 // 尾款支付，支付中
	ncSmallDepositSettleWait                                 Status = 61 // 确认订单、待分账
	ncSmallDepositSettleProcess                              Status = 62 // 确认订单、分账中
	ncSmallDepositAfterSmallEarnestRefundSmallEarnestProcess Status = 65 // 小订后退款小订、退款中
	ncSmallDepositAfterBigEarnestRefundAllEarnestProcess     Status = 66 // 大订后退大小订、退款中

	// 审核
	ncSmallDepositReviewWait                        Status = 41 // 创建订单、待审核
	ncSmallDepositOnlyCancelReviewWait              Status = 42 // 直接取消、待审核
	ncSmallDepositSecondReviewWait                  Status = 46 // 大订审核、待审核
	ncSmallDepositAfterContCancelReviewWait         Status = 43 // 意向合同后、待审核
	ncSmallDepositAfterSmallEarnestCancelReviewWait Status = 47 // 小订后取消、待审核
	ncSmallDepositAfterBigEarnestCancelReviewWait   Status = 48 // 大订后取消、待审核
	ncSmallDepositReviewFail                        Status = 45 // 审核失败、待审核
	ncSmallDepositSecondReviewFail                  Status = 49 // 大订审核失败、待审核

	// 车辆
	ncSmallDepositConfirmCarSourceWait Status = 31 // 确认车源、待确认
	ncSmallDepositCustomerCheckCarWait Status = 32 // 客户验车、待确认
	ncSmallDepositCustomerGetCarWait   Status = 33 // 客户提车、待确认

	// 	终止状态
	ncSmallDepositFinish                   Status = 100 // 订单成功
	ncSmallDepositOnlyCancel               Status = 101 // 直接作废
	ncSmallDepositTerminationContCancel    Status = 102 // 签署终止合同作废
	ncSmallDepositRefundSmallEarnestCancel Status = 105 // 退小订作废
	ncSmallDepositRefundBigEarnestCancel   Status = 104 // 退大订作废
)

var ncSmallDepositStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		EventNSConfV2: map[bfsm.NameSpace]map[string]bfsm.EventConfV2{
			bfsm.NameSpace("default"): {
				consts.NCSmallDepositIntentionContFinish:                 bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositSmallEarnestPayFinish:               bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositSmallEarnestPayTimeoutFinish:        bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositBusinessContFinish:                  bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositTerminationContFinish:               bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositSmallEarnestRefundFinish:            bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositBigEarnestPayFinish:                 bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositBigEarnestPayTimeoutFinish:          bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositEarnestRefundFinish:                 bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositCheckCarContFinish:                  bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositFinalPayFinish:                      bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositFinalPayTimeoutFinish:               bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositSettleFinish:                        bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositRefundSmallTerminationContFinish:    bfsm.EventConfV2{IsAsync: true},
				consts.NCSmallDepositRefundSmallBigTerminationContFinish: bfsm.EventConfV2{IsAsync: true},
			},
		},
		StateNSConf: map[bfsm.NameSpace]map[int]string{
			bfsm.NameSpace("default"): {
				OrderInitStatus.Int(): "开始",
				// 合同
				ncSmallDepositIntentionContWait.Int():                       "意向合同、待签署",
				ncSmallDepositIntentionContProcess.Int():                    "意向合同、签约中",
				ncSmallDepositBusinessContWait.Int():                        "买卖合同、待签署",
				ncSmallDepositBusinessContProcess.Int():                     "买卖合同、签约中",
				ncSmallDepositCheckCarContWait.Int():                        "验车合同、待签署",
				ncSmallDepositCheckCarContProcess.Int():                     "验车合同、签署中",
				ncSmallDepositTerminationContWait.Int():                     "终止合同、待签署",
				ncSmallDepositTerminationContProcess.Int():                  "终止合同、签署中",
				ncSmallDepositAfterSmallEarnestTerminationContWait.Int():    "退小订终止合同、待签署",
				ncSmallDepositAfterSmallEarnestTerminationContProcess.Int(): "退小订终止合同、签署中",
				ncSmallDepositAfterBigEarnestTerminationContWait.Int():      "退大订终止合同、待签署",
				ncSmallDepositAfterBigEarnestTerminationContProcess.Int():   "退大订终止合同、签署中",

				// 支付",
				ncSmallDepositSmallEarnestPayWait.Int():                        "小订资金、待支付",
				ncSmallDepositSmallEarnestPayProcess.Int():                     "小订资金、支付中",
				ncSmallDepositBigEarnestPayWait.Int():                          "大订资金、待支付",
				ncSmallDepositBigEarnestPayProcess.Int():                       "大订资金、支付中",
				ncSmallDepositFinalPayWait.Int():                               "尾款支付，待支付",
				ncSmallDepositFinalPayProcess.Int():                            "尾款支付，支付中",
				ncSmallDepositSettleWait.Int():                                 "确认订单、待分账",
				ncSmallDepositSettleProcess.Int():                              "确认订单、分账中",
				ncSmallDepositAfterSmallEarnestRefundSmallEarnestProcess.Int(): "小订后退款小订、退款中",
				ncSmallDepositAfterBigEarnestRefundAllEarnestProcess.Int():     "大订后退大小订、退款中",

				// 审核",
				ncSmallDepositReviewWait.Int():                        "创建订单、待审核",
				ncSmallDepositOnlyCancelReviewWait.Int():              "直接取消、待审核",
				ncSmallDepositSecondReviewWait.Int():                  "大订审核、待审核",
				ncSmallDepositAfterContCancelReviewWait.Int():         "意向合同后、待审核",
				ncSmallDepositAfterSmallEarnestCancelReviewWait.Int(): "小订后取消、待审核",
				ncSmallDepositAfterBigEarnestCancelReviewWait.Int():   "大订后取消、待审核",
				ncSmallDepositReviewFail.Int():                        "审核驳回",
				ncSmallDepositSecondReviewFail.Int():                  "大订审核失败、待审核",

				// 车辆",
				ncSmallDepositConfirmCarSourceWait.Int(): "待确认车源、待确认",
				ncSmallDepositCustomerCheckCarWait.Int(): "客户验车、待确认",
				ncSmallDepositCustomerGetCarWait.Int():   "客户提车、待确认",

				// 	终止状态",
				ncSmallDepositFinish.Int():                   "订单成功",
				ncSmallDepositOnlyCancel.Int():               "直接作废",
				ncSmallDepositTerminationContCancel.Int():    "签署终止合同作废",
				ncSmallDepositRefundSmallEarnestCancel.Int(): "退小订作废",
				ncSmallDepositRefundBigEarnestCancel.Int():   "退大订作废",
			},
		},
		EventNSConf: map[bfsm.NameSpace]map[string]string{
			bfsm.NameSpace("default"): {
				consts.CreateAction: "创建订单",
				// 新车大小订

				consts.NCSmallDepositReviewFailUpdateOrder:               "审核失败修改订单",
				consts.NCSmallDepositReviewPass:                          "审核通过",
				consts.NCSmallDepositReviewReject:                        "审核驳回到主流程",
				consts.NCSmallDepositCancelStart:                         "订单取消",
				consts.NCSmallDepositIntentionContStart:                  "意向合同发起",
				consts.NCSmallDepositIntentionContFinish:                 "意向合同回调",
				consts.NCSmallDepositSmallEarnestPayStart:                "小订支付发起",
				consts.NCSmallDepositSmallEarnestPayFinish:               "小订支付回调",
				consts.NCSmallDepositSmallEarnestPayTimeoutFinish:        "小订支付超时回调",
				consts.NCSmallDepositBusinessContStart:                   "买卖合同签署发起",
				consts.NCSmallDepositBusinessContFinish:                  "买卖合同签署回调",
				consts.NCSmallDepositTerminationContStart:                "终止合同签约发起",
				consts.NCSmallDepositTerminationContFinish:               "终止合同签约回调",
				consts.NCSmallDepositRefundSmallTerminationContFinish:    "小订退款终止合同回调",
				consts.NCSmallDepositRefundSmallBigTerminationContFinish: "大小订退款终止合同回调",
				consts.NCSmallDepositSmallEarnestRefundFinish:            "小订支付后退款回调",
				consts.NCSmallDepositBigEarnestPayStart:                  "大订支付发起",
				consts.NCSmallDepositBigEarnestPayFinish:                 "大订支付回调",
				consts.NCSmallDepositBigEarnestPayTimeoutFinish:          "大订支付超时回调",
				consts.NCSmallDepositEarnestRefundFinish:                 "大订支付后退款回调",
				consts.NCSmallDepositShopBookCarFinish:                   "门店订车、确认车源完成，更新订单",
				consts.NCSmallDepositCustomerCheckCarFinish:              "客户验车完成",
				consts.NCSmallDepositCustomerGetCarFinish:                "客户提车完成",
				consts.NCSmallDepositCheckCarContStart:                   "验车合同签约发起",
				consts.NCSmallDepositCheckCarContFinish:                  "验车合同签约回调",
				consts.NCSmallDepositFinalPayStart:                       "尾款支付发起",
				consts.NCSmallDepositFinalPayFinish:                      "尾款支付回调",
				consts.NCSmallDepositFinalPayTimeoutFinish:               "尾款支付超时回调",
				consts.NCSmallDepositSettleStart:                         "确认交车分账发起",
				consts.NCSmallDepositSettleFinish:                        "分账完成",
			},
		},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src: []int{
				int(OrderInitStatus),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositReviewWait),
				},
			},
		},
		{
			// 审核失败修改订单
			Event: consts.NCSmallDepositReviewReject,
			Src: []int{
				int(ncSmallDepositReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositReviewFail),
				},
			},
		},
		{
			// 审核失败修改订单
			Event: consts.NCSmallDepositReviewFailUpdateOrder,
			Src: []int{
				int(ncSmallDepositReviewFail),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositReviewWait),
				},
			},
		},
		{
			// 审核通过
			Event: consts.NCSmallDepositReviewPass,
			Src: []int{
				int(ncSmallDepositReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositIntentionContWait),
				},
			},
		},
		{
			// 直接取消待审核
			Event: consts.NCSmallDepositReviewReject,
			Src: []int{
				int(ncSmallDepositOnlyCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncSmallDepositIntentionContWait),
					Condition: GetBeforeStatusCondition(ncSmallDepositIntentionContWait),
				},
				{
					Dst:       int(ncSmallDepositReviewFail),
					Condition: GetBeforeStatusCondition(ncSmallDepositReviewFail),
				},
			},
		},
		{
			// 直接取消待审核
			Event: consts.NCSmallDepositCancelStart,
			Src: []int{
				int(ncSmallDepositReviewFail),
				int(ncSmallDepositIntentionContWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositOnlyCancelReviewWait),
				},
			},
		},
		{
			// 直接取消审核通过
			Event: consts.NCSmallDepositReviewPass,
			Src: []int{
				int(ncSmallDepositOnlyCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositOnlyCancel),
				},
			},
		},
		{
			// 发起意向合同签署
			Event: consts.NCSmallDepositIntentionContStart,
			Src: []int{
				int(ncSmallDepositIntentionContWait),
				int(ncSmallDepositIntentionContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositIntentionContProcess),
				},
			},
		},
		{
			// 意向合同异步回调
			Event: consts.NCSmallDepositIntentionContFinish,
			Src: []int{
				int(ncSmallDepositIntentionContWait),
				int(ncSmallDepositIntentionContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositSmallEarnestPayWait),
				},
			},
		},
		{
			// 签署合同后取消待审核
			Event: consts.NCSmallDepositCancelStart,
			Src: []int{
				int(ncSmallDepositSmallEarnestPayWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositAfterContCancelReviewWait),
				},
			},
		},
		{
			// 签署合同后取消待审核
			Event: consts.NCSmallDepositReviewReject,
			Src: []int{
				int(ncSmallDepositAfterContCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncSmallDepositSmallEarnestPayWait),
					Condition: GetBeforeStatusCondition(ncSmallDepositSmallEarnestPayWait),
				},
			},
		},
		{
			// 签署合同后取消审核通过
			Event: consts.NCSmallDepositReviewPass,
			Src: []int{
				int(ncSmallDepositAfterContCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositTerminationContWait),
				},
			},
		},
		{
			// 签署合同后取消待发起终止合同
			Event: consts.NCSmallDepositTerminationContStart,
			Src: []int{
				int(ncSmallDepositTerminationContWait),
				int(ncSmallDepositTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositTerminationContProcess),
				},
			},
		},
		{
			// 签署合同后取消待签署终止合同回调
			Event: consts.NCSmallDepositTerminationContFinish,
			Src: []int{
				int(ncSmallDepositTerminationContWait),
				int(ncSmallDepositTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositTerminationContCancel),
				},
			},
		},
		{
			// 发起小订支付
			Event: consts.NCSmallDepositSmallEarnestPayStart,
			Src: []int{
				int(ncSmallDepositSmallEarnestPayWait),
				int(ncSmallDepositSmallEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositSmallEarnestPayProcess),
				},
			},
		},
		{
			// 小订支付回调
			Event: consts.NCSmallDepositSmallEarnestPayFinish,
			Src: []int{
				int(ncSmallDepositSmallEarnestPayWait),
				int(ncSmallDepositSmallEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositConfirmCarSourceWait),
				},
			},
		},
		{
			// 小订支付回调
			Event: consts.NCSmallDepositSmallEarnestPayTimeoutFinish,
			Src: []int{
				int(ncSmallDepositSmallEarnestPayWait),
				int(ncSmallDepositSmallEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositSmallEarnestPayWait),
				},
			},
		},
		{
			// 门店订车确认
			Event: consts.NCSmallDepositShopBookCarFinish,
			Src: []int{
				int(ncSmallDepositConfirmCarSourceWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositSecondReviewWait),
				},
			},
		},
		{
			// 二次审核驳回
			Event: consts.NCSmallDepositReviewReject,
			Src: []int{
				int(ncSmallDepositSecondReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositSecondReviewFail),
				},
			},
		},
		{
			// 二次审核通过
			Event: consts.NCSmallDepositReviewPass,
			Src: []int{
				int(ncSmallDepositSecondReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositBusinessContWait),
				},
			},
		},
		{
			// 二次审核更新订单
			Event: consts.NCSmallDepositReviewFailUpdateOrder,
			Src: []int{
				int(ncSmallDepositSecondReviewFail),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositSecondReviewWait),
				},
			},
		},
		{
			// 小订后取消订单
			Event: consts.NCSmallDepositCancelStart,
			Src: []int{
				int(ncSmallDepositConfirmCarSourceWait),
				int(ncSmallDepositSecondReviewFail),
				int(ncSmallDepositSecondReviewWait),
				int(ncSmallDepositBusinessContWait),
				int(ncSmallDepositBigEarnestPayWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositAfterSmallEarnestCancelReviewWait),
				},
			},
		},
		{
			// 小订后取消订单
			Event: consts.NCSmallDepositReviewReject,
			Src: []int{
				int(ncSmallDepositAfterSmallEarnestCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncSmallDepositConfirmCarSourceWait),
					Condition: GetBeforeStatusCondition(ncSmallDepositConfirmCarSourceWait),
				},
				{
					Dst:       int(ncSmallDepositSecondReviewFail),
					Condition: GetBeforeStatusCondition(ncSmallDepositSecondReviewFail),
				},
				{
					Dst:       int(ncSmallDepositSecondReviewWait),
					Condition: GetBeforeStatusCondition(ncSmallDepositSecondReviewWait),
				},
				{
					Dst:       int(ncSmallDepositBusinessContWait),
					Condition: GetBeforeStatusCondition(ncSmallDepositBusinessContWait),
				},
				{
					Dst:       int(ncSmallDepositBigEarnestPayWait),
					Condition: GetBeforeStatusCondition(ncSmallDepositBigEarnestPayWait),
				},
			},
		},
		{
			// 小订后审核通过
			Event: consts.NCSmallDepositReviewPass,
			Src: []int{
				int(ncSmallDepositAfterSmallEarnestCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositAfterSmallEarnestTerminationContWait),
				},
			},
		},
		{
			// 小订后签署终止合同
			Event: consts.NCSmallDepositTerminationContStart,
			Src: []int{
				int(ncSmallDepositAfterSmallEarnestTerminationContWait),
				int(ncSmallDepositAfterSmallEarnestTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositAfterSmallEarnestTerminationContProcess),
				},
			},
		},
		{
			// 小订后终止合同回调
			Event: consts.NCSmallDepositRefundSmallTerminationContFinish,
			Src: []int{
				int(ncSmallDepositAfterSmallEarnestTerminationContWait),
				int(ncSmallDepositAfterSmallEarnestTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositAfterSmallEarnestRefundSmallEarnestProcess),
				},
			},
		},
		{
			// 小订后退小订回调
			Event: consts.NCSmallDepositSmallEarnestRefundFinish,
			Src: []int{
				int(ncSmallDepositAfterSmallEarnestTerminationContWait),
				int(ncSmallDepositAfterSmallEarnestTerminationContProcess),
				int(ncSmallDepositAfterSmallEarnestRefundSmallEarnestProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositRefundSmallEarnestCancel),
				},
			},
		},
		{
			// 发起买卖合同签约
			Event: consts.NCSmallDepositBusinessContStart,
			Src: []int{
				int(ncSmallDepositBusinessContWait),
				int(ncSmallDepositBusinessContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositBusinessContProcess),
				},
			},
		},
		{
			// 买卖合同签约回调
			Event: consts.NCSmallDepositBusinessContFinish,
			Src: []int{
				int(ncSmallDepositBusinessContWait),
				int(ncSmallDepositBusinessContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncSmallDepositBigEarnestPayWait),
					Condition: GetBigDepositPriceCondition(),
				},
				{
					Dst:       int(ncSmallDepositCustomerGetCarWait),
					Condition: GetBigDepositPriceZeroCondition(),
				},
			},
		},
		{
			// 发起大订支付
			Event: consts.NCSmallDepositBigEarnestPayStart,
			Src: []int{
				int(ncSmallDepositBigEarnestPayWait),
				int(ncSmallDepositBigEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositBigEarnestPayProcess),
				},
			},
		},
		{
			// 大订完成回调
			Event: consts.NCSmallDepositBigEarnestPayFinish,
			Src: []int{
				int(ncSmallDepositBigEarnestPayWait),
				int(ncSmallDepositBigEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositCustomerGetCarWait),
				},
			},
		},
		{
			// 大订超时回调
			Event: consts.NCSmallDepositBigEarnestPayTimeoutFinish,
			Src: []int{
				int(ncSmallDepositBigEarnestPayWait),
				int(ncSmallDepositBigEarnestPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositBigEarnestPayWait),
				},
			},
		},
		{
			// 客户验车确认
			Event: consts.NCSmallDepositCustomerGetCarFinish,
			Src: []int{
				int(ncSmallDepositCustomerGetCarWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositCustomerCheckCarWait),
				},
			},
		},
		{
			// 客户提车确认
			Event: consts.NCSmallDepositCustomerCheckCarFinish,
			Src: []int{
				int(ncSmallDepositCustomerCheckCarWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositCheckCarContWait),
				},
			},
		},
		{
			// 大订完成取消待审核
			Event: consts.NCSmallDepositCancelStart,
			Src: []int{
				int(ncSmallDepositCustomerGetCarWait),
				int(ncSmallDepositCustomerCheckCarWait),
				int(ncSmallDepositCheckCarContWait),
				int(ncSmallDepositFinalPayWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositAfterBigEarnestCancelReviewWait),
				},
			},
		},
		{
			// 大订完成取消待审核驳回
			Event: consts.NCSmallDepositReviewReject,
			Src: []int{
				int(ncSmallDepositAfterBigEarnestCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncSmallDepositCustomerGetCarWait),
					Condition: GetBeforeStatusCondition(ncSmallDepositCustomerGetCarWait),
				},
				{
					Dst:       int(ncSmallDepositCustomerCheckCarWait),
					Condition: GetBeforeStatusCondition(ncSmallDepositCustomerCheckCarWait),
				},
				{
					Dst:       int(ncSmallDepositCheckCarContWait),
					Condition: GetBeforeStatusCondition(ncSmallDepositCheckCarContWait),
				},
				{
					Dst:       int(ncSmallDepositFinalPayWait),
					Condition: GetBeforeStatusCondition(ncSmallDepositFinalPayWait),
				},
			},
		},
		{
			// 大订完成取消待审核
			Event: consts.NCSmallDepositReviewPass,
			Src: []int{
				int(ncSmallDepositAfterBigEarnestCancelReviewWait),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositAfterBigEarnestTerminationContWait),
				},
			},
		},
		{
			// 全款完成后发起签署终止合同
			Event: consts.NCSmallDepositTerminationContStart,
			Src: []int{
				int(ncSmallDepositAfterBigEarnestTerminationContWait),
				int(ncSmallDepositAfterBigEarnestTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositAfterBigEarnestTerminationContProcess),
				},
			},
		},
		{
			// 大订完成后发起签署终止合同回调
			Event: consts.NCSmallDepositRefundSmallBigTerminationContFinish,
			Src: []int{
				int(ncSmallDepositAfterBigEarnestTerminationContWait),
				int(ncSmallDepositAfterBigEarnestTerminationContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositAfterBigEarnestRefundAllEarnestProcess),
				},
			},
		},
		{
			// 大订完成后发起退款回调
			Event: consts.NCSmallDepositEarnestRefundFinish,
			Src: []int{
				int(ncSmallDepositAfterBigEarnestTerminationContWait),
				int(ncSmallDepositAfterBigEarnestTerminationContProcess),
				int(ncSmallDepositAfterBigEarnestRefundAllEarnestProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositRefundBigEarnestCancel),
				},
			},
		},
		{
			// 签署验车合同
			Event: consts.NCSmallDepositCheckCarContStart,
			Src: []int{
				int(ncSmallDepositCheckCarContWait),
				int(ncSmallDepositCheckCarContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositCheckCarContProcess),
				},
			},
		},
		{
			// 验车合同回调
			Event: consts.NCSmallDepositCheckCarContFinish,
			Src: []int{
				int(ncSmallDepositCheckCarContWait),
				int(ncSmallDepositCheckCarContProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst:       int(ncSmallDepositSettleWait),
					Condition: GetFinalPriceZeroCondition(),
				},
				{
					Dst:       int(ncSmallDepositFinalPayWait),
					Condition: GetFinalPriceCondition(),
				},
			},
		},
		{
			// 尾款支付发起
			Event: consts.NCSmallDepositFinalPayStart,
			Src: []int{
				int(ncSmallDepositFinalPayWait),
				int(ncSmallDepositFinalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositFinalPayProcess),
				},
			},
		},
		{
			// 尾款支付回调
			Event: consts.NCSmallDepositFinalPayFinish,
			Src: []int{
				int(ncSmallDepositFinalPayWait),
				int(ncSmallDepositFinalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositSettleWait),
				},
			},
		},
		{
			// 尾款超时回调
			Event: consts.NCSmallDepositFinalPayTimeoutFinish,
			Src: []int{
				int(ncSmallDepositFinalPayWait),
				int(ncSmallDepositFinalPayProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositFinalPayWait),
				},
			},
		},
		{
			// 交车确认，发起分账
			Event: consts.NCSmallDepositSettleStart,
			Src: []int{
				int(ncSmallDepositSettleWait),
				int(ncSmallDepositSettleProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositSettleProcess),
				},
			},
		},
		{
			// 分账回调
			Event: consts.NCSmallDepositSettleFinish,
			Src: []int{
				int(ncSmallDepositSettleWait),
				int(ncSmallDepositSettleProcess),
			},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(ncSmallDepositFinish),
				},
			},
		},
	},
}
