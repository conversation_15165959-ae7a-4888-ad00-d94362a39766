package statemachine

import (
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

const (
	// 订金-尾款 状态
	shConsignEFCreate                Status = 1   // 订单创建
	shConsignEFSigningIntentContract Status = 2   // 意向合同签署中
	shConsignEFToPayEarnestMoney     Status = 3   // 订金待支付
	shConsignEFPayingEarnestMoney    Status = 4   // 订金支付中
	shConsignEFToSignSellContract    Status = 5   // 买卖合同待签署
	shConsignEFSigningSellContract   Status = 6   // 买卖合同签署中
	shConsignEFToPayFinalMoney       Status = 7   // 尾款待支付
	shConsignEFPayingFinalMoney      Status = 8   // 尾款支付中
	shConsignEFRefunding             Status = 9   // 退款中
	shConsignEFToTransferOwner       Status = 10  // 待过户
	shConsignEFToSelectLoan          Status = 11  // 待选择贷款金融方式
	shConsignEFToConfirmLoan         Status = 12  // 待确认贷款
	shConsignEFToApproveLoan         Status = 13  // 待审核贷款
	shConsignEFLoaning               Status = 14  // 贷款中
	shConsignEFSettling              Status = 15  // 结算中
	shConsignEFToDeliveryCar         Status = 16  // 待交车
	shConsignEFOver                  Status = 100 // 已完成
	shConsignEFCanceling             Status = 199 // 作废中（从"买卖合同待签署"作废时，有作废中的状态，因为需要退订金）
	shConsignEFCancellation          Status = 200 // 已作废

	// 全款 状态
	shConsignFullCreate          Status = 1   // 订单创建
	shConsignFullSigningContract Status = 2   // 买卖合同签署中
	shConsignFullToPay           Status = 3   // 全款待支付
	shConsignFullPaying          Status = 4   // 全款支付中
	shConsignFullToTransferOwner Status = 5   // 待过户
	shConsignFullToSelectLoan    Status = 11  // 待选择贷款金融方式
	shConsignFullToConfirmLoan   Status = 12  // 待确认贷款
	shConsignFullToApproveLoan   Status = 13  // 待审核贷款
	shConsignFullLoaning         Status = 14  // 贷款中
	shConsignFullSettling        Status = 15  // 结算中
	shConsignFullToDeliveryCar   Status = 16  // 待交车
	shConsignFullOver            Status = 100 // 已完成
	shConsignFullCancellation    Status = 200 // 已作废
)

var shConsignEFStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		StateNSConf: map[bfsm.NameSpace]map[int]string{bfsm.NameSpace("default"): {
			int(shConsignEFCreate):                "订单创建",
			int(shConsignEFSigningIntentContract): "意向合同签署中",
			int(shConsignEFToPayEarnestMoney):     "订金待支付",
			int(shConsignEFPayingEarnestMoney):    "订金支付中",
			int(shConsignEFToSignSellContract):    "买卖合同待签署",
			int(shConsignEFSigningSellContract):   "买卖合同签署中",
			int(shConsignEFToPayFinalMoney):       "尾款待支付",
			int(shConsignEFPayingFinalMoney):      "尾款支付中",
			int(shConsignEFRefunding):             "退款中",
			int(shConsignEFToTransferOwner):       "待过户",
			int(shConsignEFToSelectLoan):          "待选择贷款金融方式",
			int(shConsignEFToConfirmLoan):         "待确认贷款",
			int(shConsignEFToApproveLoan):         "待审核贷款",
			int(shConsignEFLoaning):               "贷款中",
			int(shConsignEFSettling):              "结算中",
			int(shConsignEFToDeliveryCar):         "待交车",
			int(shConsignEFOver):                  "已完成",
			int(shConsignEFCanceling):             "作废中",
			int(shConsignEFCancellation):          "已作废",
		}},
		EventNSConf: map[bfsm.NameSpace]map[string]string{bfsm.NameSpace("default"): {
			consts.ShConsignEFSignIntentContractEvent:        "签署意向合同",
			consts.ShConsignEFSignIntentContractOverEvent:    "意向合同签署完成",
			consts.ShConsignEFPayEarnestEvent:                "支付订金",
			consts.ShConsignEFPayEarnestOverEvent:            "订金支付完成",
			consts.ShConsignEFRefundEarnestOverEvent:         "退订金完成",
			consts.ShConsignEFRefundEarnestOverAfterPOSEvent: "POS支付后退订金完成",
			consts.ShConsignEFSignSellContractEvent:          "签署买卖合同",
			consts.ShConsignEFSignSellContractOverEvent:      "买卖合同签署完成",
			consts.ShConsignEFPayFinalEvent:                  "支付尾款",
			consts.ShConsignEFPayFinalOverEvent:              "尾款支付完成",
			consts.ShConsignEFTransferOwnerEvent:             "过户",
			consts.ShConsignEFSelectLoanEvent:                "确定金融贷款方式",
			consts.ShConsignEFConfirmLoanEvent:               "确认贷款",
			consts.ShConsignEFApproveLoanPassEvent:           "审核贷款通过",
			consts.ShConsignEFApproveLoanFailEvent:           "审核贷款不通过",
			consts.ShConsignEFLoanOverEvent:                  "金融贷款完成",
			consts.ShConsignEFDeliveryEvent:                  "交车",
			consts.ShConsignEFSettleOverEvent:                "结算完成",
			consts.ShConsignEFCancelEvent:                    "作废",
			consts.ShConsignEFCancelWithRefundEvent:          "作废并退款",
		}},
		EventNSConfV2: map[bfsm.NameSpace]map[string]bfsm.EventConfV2{},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src:   []int{int(OrderInitStatus)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFCreate),
				},
			},
		},
		{
			// 签署意向合同（发短信）
			Event: consts.ShConsignEFSignIntentContractEvent,
			Src:   []int{int(shConsignEFCreate), int(shConsignEFSigningIntentContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFSigningIntentContract),
				},
			},
		},
		{
			// 意向合同签署完成
			Event: consts.ShConsignEFSignIntentContractOverEvent,
			Src:   []int{int(shConsignEFCreate), int(shConsignEFSigningIntentContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFToPayEarnestMoney),
				},
			},
		},
		{
			// 支付意向金(获取链接)
			Event: consts.ShConsignEFPayEarnestEvent,
			Src:   []int{int(shConsignEFToPayEarnestMoney), int(shConsignEFPayingEarnestMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFPayingEarnestMoney),
				},
			},
		},
		{
			// 支付意向金完成
			Event: consts.ShConsignEFPayEarnestOverEvent,
			Src:   []int{int(shConsignEFToPayEarnestMoney), int(shConsignEFPayingEarnestMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFToSignSellContract),
				},
			},
		},
		{
			// 签署买卖合同(发短信)
			Event: consts.ShConsignEFSignSellContractEvent,
			Src:   []int{int(shConsignEFToSignSellContract), int(shConsignEFSigningSellContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFSigningSellContract),
				},
			},
		},
		{
			// 签署买卖合同完成
			Event: consts.ShConsignEFSignSellContractOverEvent,
			Src:   []int{int(shConsignEFToSignSellContract), int(shConsignEFSigningSellContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFToPayFinalMoney),
				},
			},
		},
		{
			// 支付尾款（获取链接）
			Event: consts.ShConsignEFPayFinalEvent,
			Src:   []int{int(shConsignEFToPayFinalMoney), int(shConsignEFPayingFinalMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFPayingFinalMoney),
				},
			},
		},
		{
			// 支付尾款完成
			Event: consts.ShConsignEFPayFinalOverEvent,
			Src:   []int{int(shConsignEFToPayFinalMoney), int(shConsignEFPayingFinalMoney)},
			Matchers: []bfsm.Matcher{
				{
					// 退订金
					Dst: int(shConsignEFRefunding),
				},
			},
		},
		{
			// 支付尾款超时（回调时会判断是否有进行中的支付）
			Event: consts.ShConsignEFPayPOSTimeoutEvent,
			Src:   []int{int(shConsignEFPayingFinalMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFToPayFinalMoney),
				},
			},
		},
		{
			// POS支付后退款完成
			Event: consts.ShConsignEFRefundEarnestOverAfterPOSEvent,
			Src:   []int{int(shConsignEFToPayFinalMoney), int(int(shConsignEFPayingFinalMoney)), int(shConsignEFRefunding)},
			Matchers: []bfsm.Matcher{
				{
					// 没有金融节点，流转到"待过户"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       int(shConsignEFToTransferOwner),
				},
				{
					// 有金融节点，流转到"待选择金融方式"
					Condition: consts.CondShSellHasLoan.Val(),
					Dst:       int(shConsignEFToSelectLoan),
				},
			},
		},
		{
			// 选择贷款方式
			Event: consts.ShConsignEFSelectLoanEvent,
			Src:   []int{int(shConsignEFToSelectLoan)},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       int(shConsignEFToTransferOwner),
				},
				{
					// 先贷款后过户，流转到"待确认贷款"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       int(shConsignEFToConfirmLoan),
				},
			},
		},
		{
			// 确认贷款
			Event: consts.ShConsignEFConfirmLoanEvent,
			Src:   []int{int(shConsignEFToConfirmLoan), int(shConsignEFToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFToApproveLoan),
				},
			},
		},
		{
			// 审核贷款通过 --> 贷款中
			Event: consts.ShConsignEFApproveLoanPassEvent,
			Src:   []int{int(shConsignEFToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFLoaning),
				},
			},
		},
		{
			// 审核贷款不通过 --> 待确认贷款
			Event: consts.ShConsignEFApproveLoanFailEvent,
			Src:   []int{int(shConsignEFToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFToConfirmLoan),
				},
			},
		},
		{
			// 贷款完成
			Event: consts.ShConsignEFLoanOverEvent,
			Src:   []int{int(shConsignEFLoaning), int(shConsignEFToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待交车"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       int(shConsignEFToDeliveryCar),
				},
				{
					// 先贷款后过户，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       int(shConsignEFToTransferOwner),
				},
			},
		},
		{
			// 过户
			Event: consts.ShConsignEFTransferOwnerEvent,
			Src:   []int{int(shConsignEFToTransferOwner)},
			Matchers: []bfsm.Matcher{
				{
					// 没有有金融节点，流转到"待交车"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       int(shConsignEFToDeliveryCar),
				},
				{
					// 有金融节点 && 先过户后贷款，流转到"待确认贷款"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst.Not()).Val(),
					Dst:       int(shConsignEFToConfirmLoan),
				},
				{
					// 有金融节点 && 先贷款后过户，流转到"待交车"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst).Val(),
					Dst:       int(shConsignEFToDeliveryCar),
				},
			},
		},
		{
			// 交车
			Event: consts.ShConsignEFDeliveryEvent,
			Src:   []int{int(shConsignEFToDeliveryCar)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFOver),
				},
			},
		},
		{
			// 作废，涉及到订金退款
			Event: consts.ShConsignEFCancelWithRefundEvent,
			Src:   []int{int(shConsignEFToSignSellContract), int(shConsignEFSigningSellContract), int(shConsignEFToPayFinalMoney), int(shConsignEFCanceling)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFCanceling),
				},
			},
		},
		{
			// 作废，涉及到订金退款回调成功，退款完成
			Event: consts.ShConsignEFRefundEarnestOverEvent,
			Src:   []int{int(shConsignEFCanceling), int(shConsignEFToSignSellContract), int(shConsignEFSigningSellContract), int(shConsignEFToPayFinalMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFCancellation),
				},
			},
		},
		{
			// 作废
			Event: consts.ShConsignEFCancelEvent,
			Src:   []int{int(shConsignEFCreate), int(shConsignEFSigningIntentContract), int(shConsignEFToPayEarnestMoney)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignEFCancellation),
				},
			},
		},
	},
}

var shConsignFullStateMachine = bfsm.BizDesc{
	NSConf: bfsm.NSConf{
		StateNSConf: map[bfsm.NameSpace]map[int]string{bfsm.NameSpace("default"): {
			int(shConsignFullCreate):          "订单创建",
			int(shConsignFullSigningContract): "买卖合同签署中",
			int(shConsignFullToPay):           "全款待支付",
			int(shConsignFullPaying):          "全款支付中",
			int(shConsignFullToTransferOwner): "待过户",
			int(shConsignFullToSelectLoan):    "待选择贷款金融方式",
			int(shConsignFullToConfirmLoan):   "待确认贷款",
			int(shConsignFullToApproveLoan):   "待审核贷款",
			int(shConsignFullLoaning):         "贷款中",
			int(shConsignFullSettling):        "结算中",
			int(shConsignFullToDeliveryCar):   "待交车",
			int(shConsignFullOver):            "已完成",
			int(shConsignFullCancellation):    "已作废",
		}},
		EventNSConf: map[bfsm.NameSpace]map[string]string{bfsm.NameSpace("default"): {
			consts.ShConsignFullSignSellContractEvent:     "签署买卖合同",
			consts.ShConsignFullSignSellContractOverEvent: "买卖合同签署完成",
			consts.ShConsignFullPayMoneyEvent:             "支付",
			consts.ShConsignFullPayMoneyOverEvent:         "支付完成",
			consts.ShConsignFullTransferOwnerEvent:        "过户",
			consts.ShConsignFullSelectLoanEvent:           "确定金融贷款方式",
			consts.ShConsignFullConfirmLoanEvent:          "确认贷款",
			consts.ShConsignFullApproveLoanPassEvent:      "审核贷款通过",
			consts.ShConsignFullApproveLoanFailEvent:      "审核贷款不通过",
			consts.ShConsignFullLoanOverEvent:             "金融贷款完成",
			consts.ShConsignFullDeliveryEvent:             "交车",
			consts.ShConsignFullSettleOverEvent:           "结算完成",
			consts.ShConsignFullCancelEvent:               "作废",
			consts.ShConsignFullPayPOSTimeoutEvent:        "支付POS超时",
		}},
	},
	TransDescList: []bfsm.TransDesc{
		{
			// 创建订单
			Event: consts.CreateAction,
			Src:   []int{int(OrderInitStatus)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignFullCreate),
				},
			},
		},
		{
			// 签署买卖合同（发短信）
			Event: consts.ShConsignFullSignSellContractEvent,
			Src:   []int{int(shConsignFullCreate), int(shConsignFullSigningContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignFullSigningContract),
				},
			},
		},
		{
			// 签署买卖合同完成
			Event: consts.ShConsignFullSignSellContractOverEvent,
			Src:   []int{int(shConsignFullCreate), int(shConsignFullSigningContract)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignFullToPay),
				},
			},
		},
		{
			// 支付全款（获取链接）
			Event: consts.ShConsignFullPayMoneyEvent,
			Src:   []int{int(shConsignFullToPay), int(shConsignFullPaying)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignFullPaying),
				},
			},
		},
		{
			// 支付完成
			Event: consts.ShConsignFullPayMoneyOverEvent,
			Src:   []int{int(shConsignFullToPay), int(shConsignFullPaying)},
			Matchers: []bfsm.Matcher{
				{
					// 没有金融节点，流转到"待过户"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       int(shConsignFullToTransferOwner),
				},
				{
					// 有金融节点，流转到"待选择金融方式"
					Condition: consts.CondShSellHasLoan.Val(),
					Dst:       int(shConsignFullToSelectLoan),
				},
			},
		},
		{
			// 支付尾款超时（回调时会判断是否有进行中的支付）
			Event: consts.ShConsignFullPayPOSTimeoutEvent,
			Src:   []int{int(shConsignFullPaying)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignFullToPay),
				},
			},
		},
		{
			// 选择贷款方式
			Event: consts.ShConsignFullSelectLoanEvent,
			Src:   []int{int(shConsignFullToSelectLoan)},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       int(shConsignFullToTransferOwner),
				},
				{
					// 先贷款后过户，流转到"待确认贷款"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       int(shConsignFullToConfirmLoan),
				},
			},
		},
		{
			// 确认贷款
			Event: consts.ShConsignFullConfirmLoanEvent,
			Src:   []int{int(shConsignFullToConfirmLoan), int(shConsignFullToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignFullToApproveLoan),
				},
			},
		},
		{
			// 审核贷款通过 --> 贷款中
			Event: consts.ShConsignFullApproveLoanPassEvent,
			Src:   []int{int(shConsignFullToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignFullLoaning),
				},
			},
		},
		{
			// 审核贷款不通过 --> 待确认贷款
			Event: consts.ShConsignFullApproveLoanFailEvent,
			Src:   []int{int(shConsignFullToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignFullToConfirmLoan),
				},
			},
		},
		{
			// 贷款完成
			Event: consts.ShConsignFullLoanOverEvent,
			Src:   []int{int(shConsignFullLoaning), int(shConsignFullToApproveLoan)},
			Matchers: []bfsm.Matcher{
				{
					// 先过户后贷款，流转到"待交车"
					Condition: consts.CondShSellIsLoanFirst.Not().Val(),
					Dst:       int(shConsignFullToDeliveryCar),
				},
				{
					// 先贷款后过户，流转到"待过户"
					Condition: consts.CondShSellIsLoanFirst.Val(),
					Dst:       int(shConsignFullToTransferOwner),
				},
			},
		},
		{
			// 过户
			Event: consts.ShConsignFullTransferOwnerEvent,
			Src:   []int{int(shConsignFullToTransferOwner)},
			Matchers: []bfsm.Matcher{
				{
					// 没有有金融节点，流转到"待交车"
					Condition: consts.CondShSellHasLoan.Not().Val(),
					Dst:       int(shConsignFullToDeliveryCar),
				},
				{
					// 有金融节点 && 先过户后贷款，流转到"待确认贷款"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst.Not()).Val(),
					Dst:       int(shConsignFullToConfirmLoan),
				},
				{
					// 有金融节点 && 先贷款后过户，流转到"待交车"
					Condition: consts.CondShSellHasLoan.And(consts.CondShSellIsLoanFirst).Val(),
					Dst:       int(shConsignFullToDeliveryCar),
				},
			},
		},
		{
			// 交车
			Event: consts.ShConsignFullDeliveryEvent,
			Src:   []int{int(shConsignFullToDeliveryCar)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignFullOver),
				},
			},
		},
		{
			// 作废
			Event: consts.ShConsignFullCancelEvent,
			Src:   []int{int(shConsignFullCreate), int(shConsignFullSigningContract), int(shConsignFullToPay)},
			Matchers: []bfsm.Matcher{
				{
					Dst: int(shConsignFullCancellation),
				},
			},
		},
	},
}
