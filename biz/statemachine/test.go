package statemachine

import "code.byted.org/motor/bfsm"

var testStateMachine = bfsm.BizDesc{
	CommonLockFreeCallback:    nil,
	CommonBeforeTransCallback: nil,
	CommonSrcCallback:         nil,
	CommonDstCallback:         nil,
	CommonAfterTransCallback:  nil,
	TransDescList: []bfsm.TransDesc{
		{
			// 事件
			Event: "Create",
			// 现态
			Src: []int{0},
			Matchers: []bfsm.Matcher{
				// 无matcher表达式的简单实现
				{
					// 次态
					Dst: 1,
				},
			},
		},
		// 当安全门状态为open时，接收到pull事件，门的状态将变为open
		{
			Event: "Pay",
			Src:   []int{1},
			Matchers: []bfsm.Matcher{
				{
					Dst: 2,
				},
			},
		},
		// 当安全门状态为closed时，接收到pull事件，门的状态将变为open
		// 当安全门状态为closed时，接收到pull事件，门的状态将变为closed
		{
			Event: "Refund",
			Src:   []int{2},
			Matchers: []bfsm.Matcher{
				{
					Dst: 3,
				},
			},
		},
		{
			Event: "Skip",
			Src:   []int{1},
			Matchers: []bfsm.Matcher{
				{
					Dst:       2,
					Condition: "a > 0",
				},
				{
					Dst:       3,
					Condition: "a <= 0",
				},
			},
		},
		{
			Event: "SameEvent",
			Src:   []int{1},
			Matchers: []bfsm.Matcher{
				{
					Dst: 4,
				},
			},
		},
		{
			Event: "SameEvent",
			Src:   []int{2},
			Matchers: []bfsm.Matcher{
				{
					Dst: 5,
				},
			},
		},
	},
}
