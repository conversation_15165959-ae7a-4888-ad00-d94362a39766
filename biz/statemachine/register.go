package statemachine

import (
	"context"

	"code.byted.org/motor/fwe_trade_common/statemachine/nc_state"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine/state_callback"
)

/*
状态对应的callback
*/

type StateCallback func(ctx context.Context, input interface{}) (bizErr *errdef.BizErr)

var state2Callback = map[consts.BizScene]map[Status]StateCallback{
	consts.BizSceneNCBrokerage: {
		ncBrokerageFinish:                state_callback.OrderFinish,
		ncBrokerageOnlyCancel:            state_callback.OrderFinish,
		ncBrokerageTerminationContCancel: state_callback.OrderFinish,
		ncBrokerageRefundCancel:          state_callback.OrderFinish,
	},
	consts.BizSceneNCBigDeposit: {
		ncBigDepositFinish:                state_callback.OrderFinish,
		ncBigDepositOnlyCancel:            state_callback.OrderFinish,
		ncBigDepositTerminationContCancel: state_callback.OrderFinish,
		ncBigDepositRefundCancel:          state_callback.OrderFinish,
	},
	consts.BizSceneNCSmallDeposit: {
		ncSmallDepositFinish:                   state_callback.OrderFinish,
		ncSmallDepositOnlyCancel:               state_callback.OrderFinish,
		ncSmallDepositTerminationContCancel:    state_callback.OrderFinish,
		ncSmallDepositRefundSmallEarnestCancel: state_callback.OrderFinish,
		ncSmallDepositRefundBigEarnestCancel:   state_callback.OrderFinish,
	},
	consts.BizSceneNCFranchiseeBrokerage: {
		ncBrokerageFinish:                state_callback.OrderFinish,
		ncBrokerageOnlyCancel:            state_callback.OrderFinish,
		ncBrokerageTerminationContCancel: state_callback.OrderFinish,
		ncBrokerageRefundCancel:          state_callback.OrderFinish,
	},
	consts.BizSceneNCFranchiseeBigDeposit: {
		ncBigDepositFinish:                state_callback.OrderFinish,
		ncBigDepositOnlyCancel:            state_callback.OrderFinish,
		ncBigDepositTerminationContCancel: state_callback.OrderFinish,
		ncBigDepositRefundCancel:          state_callback.OrderFinish,
	},
	consts.BizSceneNCFranchiseeSmallDeposit: {
		ncSmallDepositFinish:                   state_callback.OrderFinish,
		ncSmallDepositOnlyCancel:               state_callback.OrderFinish,
		ncSmallDepositTerminationContCancel:    state_callback.OrderFinish,
		ncSmallDepositRefundSmallEarnestCancel: state_callback.OrderFinish,
		ncSmallDepositRefundBigEarnestCancel:   state_callback.OrderFinish,
	},
	consts.BizScene(nc_state.NCPurchaseOrderSuccessSt.Value()): {
		Status(nc_state.NCPurchaseOrderSuccessSt.Value()):         state_callback.OrderFinish,
		Status(nc_state.NCPurchaseEarnestFailCancelSt.Value()):    state_callback.OrderFinish,
		Status(nc_state.NCPurchaseEarnestSuccessCancelSt.Value()): state_callback.OrderFinish,
		Status(nc_state.NCPurchaseFinalFailCancelSt.Value()):      state_callback.OrderFinish,
	},
}

func GetDstCallback(ctx context.Context, bizScene int32, status int) StateCallback {
	callbackMap := state2Callback[consts.GetBizScene(bizScene)]
	if callbackMap == nil {
		return nil
	}
	callback := callbackMap[Status(status)]
	if callbackMap == nil {
		return nil
	}
	return callback
}
