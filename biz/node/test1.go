package node

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"context"
	"encoding/json"
	"fmt"
)

type NodeTest1Context interface {
	GetNodeTest1Req() *model.NodeTest1Req
	SetNodeTest1Resp(resp *model.NodeTest1Resp)
}

type NodeTest1 struct {
	*BaseNode
	Req         *model.NodeTest1Req
	nodeContext NodeTest1Context
}

func NewNodeTest1(test1Context NodeTest1Context) INode {
	return &NodeTest1{
		BaseNode:    NewBaseNode("test1"),
		nodeContext: test1Context,
	}
}

func (n *NodeTest1) Process(ctx context.Context) error {
	req := n.nodeContext.GetNodeTest1Req()
	reqS, _ := json.Marshal(req)
	fmt.Printf("%s process req=%s", n.name, reqS)
	return nil
}
