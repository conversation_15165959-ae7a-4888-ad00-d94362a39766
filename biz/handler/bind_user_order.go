package handler

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type BindUserOrderHandler struct {
	*BaseHandler
}

func NewBindUserOrderHandler() *BindUserOrderHandler {
	return &BindUserOrderHandler{
		BaseHandler: NewBaseHandler(),
	}
}

func (handler *BindUserOrderHandler) Process(ctx context.Context, req *engine.BindUserOrderReq) (resp *engine.BindUserOrderResp) {
	resp = engine.NewBindUserOrderResp()
	resp.BaseResp = base.NewBaseResp()

	bizErr := service.NewOrderService().BindUserOrder(ctx, req.GetUID(), req.GetOrderIds())
	if bizErr != nil {
		logs.CtxError(ctx, "[MGetOrderHandler] build execution failed error=%v", bizErr.Error())
		handler.GenResp(ctx, resp, bizErr)
		return
	}

	return
}
