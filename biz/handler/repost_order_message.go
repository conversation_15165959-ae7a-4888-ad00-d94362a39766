package handler

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/types"
	"context"
	"encoding/json"
	"fmt"
)

type RepostOrderMessageHandler struct {
	*BaseHandler
}

func NewRepostOrderMessageHandler() *RepostOrderMessageHandler {
	return &RepostOrderMessageHandler{
		BaseHandler: NewBaseHandler(),
	}
}

func (h *RepostOrderMessageHandler) Process(ctx context.Context, req *engine.RepostOrderMessageReq) *engine.RepostOrderMessageResp {
	resp := new(engine.RepostOrderMessageResp)
	resp.BaseResp = base.NewBaseResp()
	// check param
	bizErr := checkRepostOrderMessageReq(ctx, req)
	if bizErr != nil {
		h.GenResp(ctx, resp, bizErr)
		return resp
	}
	// load new order data
	options := []*service.OrderOption{{OptionID: service.OptionNeedFinance}}
	order, bizErr := service.NewOrderService().GetOrderByID(ctx, req.OrderID, options...)
	if bizErr != nil {
		h.GenResp(ctx, resp, bizErr)
		return resp
	}
	// 转化
	msgs, bizErr := packMessage(ctx, req, order)
	if bizErr != nil {
		h.GenResp(ctx, resp, bizErr)
		return resp
	}
	// send mq
	sendResp, err := caller.OrderEventProducer.SendBatch(ctx, msgs)
	if err != nil {
		bizErr := errdef.NewRawErr(errdef.ProduceMsgErr, "SendBatch error")
		h.GenResp(ctx, resp, bizErr)
		return resp
	}
	logs.CtxInfo(ctx, "[RepostOrderMessageHandler] Process send order Message success resp=%v", tools.GetLogStr(sendResp))
	return resp
}

func packMessage(ctx context.Context, req *engine.RepostOrderMessageReq, order *service_model.Order) ([]*types.Message, *errdef.BizErr) {
	var (
		fweOrder       = order.FweOrder
		commonOrder, _ = packer.OrderService2Common(order)
		tag            = fmt.Sprintf("%d", fweOrder.BizScene)
		partitionKey   = fweOrder.OrderID
	)
	orderMessage := &fwe_trade_common.OrderMessage{
		TenantType:        tenant_base.TenantType(fweOrder.TenantType),
		BizScene:          fweOrder.BizScene,
		Version:           "v1",
		OrderID:           fweOrder.OrderID,
		OrderEvent:        req.OrderAction,
		BeforeOrderStatus: req.GetBeforeStatus(),
		AfterOrderStatus:  req.GetAfterStatus(),
		OrderSource:       commonOrder,
		SmVersion:         fweOrder.SmVersion,
	}
	var msgs []*types.Message
	msgStr, err := json.Marshal(orderMessage)
	if err != nil {
		logs.CtxError(ctx, "[packMessage] Marshal order Message failed error=%v", err)
		return nil, errdef.NewRawErr(errdef.DataErr, "Marshal orderMessage error")
	}
	msg := types.NewOrderlyMessage(consts.OrderEventTopic, partitionKey, msgStr).WithTag(tag)
	logs.CtxInfo(ctx, "[packMessage] topic = %+v, msgID = %+v", consts.OrderEventTopic, msg.Msg.UniqId)
	msgs = append(msgs, msg)
	return msgs, nil
}

func checkRepostOrderMessageReq(ctx context.Context, req *engine.RepostOrderMessageReq) *errdef.BizErr {
	if req.OrderID == "" {
		return errdef.NewParamsErr("OrderID is nil")
	}
	if req.OrderAction == "" {
		return errdef.NewParamsErr("OrderAction is empty")
	}
	if req.AfterStatus == int32(0) {
		return errdef.NewParamsErr("AfterStatus error")
	}
	return nil
}
