package handler

import (
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"fmt"
	"testing"
)

// doas -p motor.fwe_trade.engine go test ./biz/handler -v -count=1 -run="TestActionOrderHandler$"
func TestActionOrderHandler(t *testing.T) {
	param := &sh_sell_model.LoanConfirmModel{
		FweAccountId: "1",
		ShopId:       "1",
		ShopName:     "测试门店",
		CarVin:       "123456",
		BorrowerName: "借款测试人",
		Amount:       1000,
		FinanceName:  "测试",
		OutBankInfo: &sh_sell_model.LoanBankInfo{
			AccountName: "测试账户名",
			BranchName:  "测试银行名称",
			AccountNo:   "测试账号",
		},
	}
	bizReq, _ := utils.Marshal(param)
	actionReq := &engine.ActionOrderReq{
		OrderID:    "7126880298616246316",
		Action:     consts.ShSellEFConfirmLoanEvent,
		BizRequest: bizReq,
		Operator: &fwe_trade_common.OperatorInfo{
			OperatorID:   "9525",
			OperatorName: "teohubo",
		},
		Identity: &fwe_trade_common.BizIdentity{
			TenantType: tenant_base.TenantType_SecondHandTrade,
			BizScene:   int32(consts.BizSceneSHSellByEarnestFinal),
		},
	}

	rsp := NewActionOrderHandler().Process(context.Background(), actionReq)
	fmt.Println("rsp: ", tools.GetLogStr(rsp))
}
