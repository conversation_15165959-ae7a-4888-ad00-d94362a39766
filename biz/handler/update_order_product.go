package handler

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"unicode/utf8"
)

type UpdateOrderProductHandler struct {
	*BaseHandler
}

func NewUpdateOrderProductHandler() *UpdateOrderProductHandler {
	return &UpdateOrderProductHandler{
		BaseHandler: NewBaseHandler(),
	}
}

func (handler *UpdateOrderProductHandler) Process(ctx context.Context, req *engine.UpdateOrderProductReq) (resp *engine.UpdateOrderProductResp) {
	resp = engine.NewUpdateOrderProductResp()
	resp.BaseResp = base.NewBaseResp()

	if bizErr := handler.checkParams(ctx, req); bizErr != nil {
		logs.CtxWarn(ctx, "[UpdateOrderProductHandler] checkParams failed, err iz %+v", bizErr)
		handler.GenResp(ctx, resp, bizErr)
		return
	}

	// 查询订单确认订单存在
	_, bizErr := service.NewOrderService().GetOrderByID(ctx, req.OrderID)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderProductHandler-Process] GetOrderByID error, err = %v", bizErr.Error())
		handler.GenResp(ctx, resp, bizErr)
		return
	}
	updateParams := &service_model.UpdateOrderParams{
		UpdateProductDetail: conv.StringPtr(tools.GetLogStr(req.GetProductDetail())),
		Operator:            req.GetOperator(),
	}

	bizErr = service.NewOrderService().UpdateOrder(ctx, req.GetOrderID(), updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[MGetOrderHandler] build execution failed error=%v", bizErr.Error())
		handler.GenResp(ctx, resp, bizErr)
		return
	}
	// 更新tag
	err := service.NewOrderService().UpdateOrderTag(ctx, req.GetOrderID(), req.TagMap)
	if err != nil {
		logs.CtxError(ctx, "[UpdateOrderProductHandler]UpdateOrderTag error=%v", bizErr.Error())
		handler.GenResp(ctx, resp, bizErr)
		return
	}
	return
}

func (handler *UpdateOrderProductHandler) checkParams(ctx context.Context, req *engine.UpdateOrderProductReq) *errdef.BizErr {
	if req.Operator == nil {
		return errdef.NewParamsErr("operator 不能为空")
	}
	if req.ProductDetail == nil {
		return errdef.NewParamsErr("商品详情不能为空")
	}
	if utf8.RuneCountInString(req.ProductDetail.ProductHeadImageURI) > 128 {
		return errdef.NewParamsErr("商品头图链接不能超过 128 个字符")
	}
	if utf8.RuneCountInString(req.ProductDetail.ProductDesc) > 128 {
		return errdef.NewParamsErr("商品描述不能超过 128 个字符")
	}
	return nil
}
