package handler

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type CommonReq interface {
	GetIdentity() (v *fwe_trade_common.BizIdentity)
}

type CommonRsp interface {
	GetBaseResp() *base.BaseResp
}

type BaseHandler struct{}

func NewBaseHandler() *BaseHandler {
	return &BaseHandler{}
}

func (handler *BaseHandler) CheckCommonReq(ctx context.Context, req CommonReq) (bizErr *errdef.BizErr) {
	// 校验请求
	if req == nil {
		bizErr = errdef.NewParamsErr("req is nil")
		return
	}
	// 校验身份
	if req.GetIdentity() == nil || req.GetIdentity().GetTenantType() == 0 {
		bizErr = errdef.NewParamsErr("identity tenant type is nil")
		return
	}
	return
}

func (handler *BaseHandler) GenResp(ctx context.Context, resp CommonRsp, err error) {
	if resp == nil || resp.GetBaseResp() == nil {
		logs.CtxError(ctx, "resp and base resp can never been nil!")
		return
	}
	if err == nil {
		return
	}
	bizErr, ok := err.(*errdef.BizErr)
	if !ok {
		bizErr = errdef.NewRawErr(errdef.ServerException, err.Error())
	}
	if bizErr == nil {
		return
	}
	baseResp := resp.GetBaseResp()
	baseResp.StatusMessage = bizErr.Error()
	baseResp.StatusCode = bizErr.Code()
	return
}
