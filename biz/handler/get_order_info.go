package handler

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type GetOrderInfoHandler struct {
	*BaseHandler
}

func NewGetOrderInfoHandler() *GetOrderInfoHandler {
	return &GetOrderInfoHandler{
		BaseHandler: NewBaseHandler(),
	}
}

func (handler *GetOrderInfoHandler) Process(ctx context.Context, req *engine.GetOrderInfoReq) (resp *engine.OrderInfoDetailResp) {
	resp = engine.NewOrderInfoDetailResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		dataMap map[string]*service_model.Order
		bizErr  *errdef.BizErr
	)

	cond := []*service.OrderOption{
		{OptionID: service.OptionNeedFinance},
		{OptionID: service.OptionNeedCont},
	}

	if req.GetNeedSettleInfo() {
		cond = append(cond, &service.OrderOption{OptionID: service.OptionNeedSettle})
	}
	if req.GetNeedRefundInfo() {
		cond = append(cond, &service.OrderOption{OptionID: service.OptionNeedRefund})
	}

	dataMap, bizErr = service.NewOrderService().MGetOrderByIDs(ctx, []string{req.OrderID}, cond...)
	if bizErr != nil {
		logs.CtxError(ctx, "[MGetOrderHandler] build execution failed error=%v", bizErr.Error())
		handler.GenResp(ctx, resp, bizErr)
		return
	}
	orderMap, bizErr := packer.OrderServiceMap2CommonMap(ctx, dataMap)
	if bizErr != nil {
		logs.CtxError(ctx, "[MGetOrderHandler] build execution failed error=%v", bizErr.Error())
		handler.GenResp(ctx, resp, bizErr)
		return
	}
	resp.OrderInfo = orderMap[req.OrderID]
	return
}
