package handler

import (
	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/dal"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_finance_model"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
	"context"
	"encoding/json"
	"gotest.tools/assert"
	"testing"
)

var operator = &fwe_trade_common.OperatorInfo{
	OperatorID:   "9525",
	OperatorName: "teohubo",
}

var identity = &fwe_trade_common.BizIdentity{
	TenantType: tenant_base.TenantType_SecondHandTrade,
	BizScene:   int32(consts.BizSceneSHFinance),
}

func init() {
	caller.Init()
	dal.Init()
	statemachine.Init()
	utils.Init()
}

func TestFinanceOrderCreate(t *testing.T) {

	financeProduct := sh_finance_model.FinanceProductInfo{
		ProductId:       "123",
		SkuCode:         "123",
		BuycarOrderId:   "123456",
		VinCode:         "vin-12345",
		AssessAmount:    ********,
		LoanAmount:      ********,
		LoanDays:        60,
		TransferCount:   1,
		InterestRate:    0.025,
		CarsMileage:     1000,
		CarsLastOwner:   "lisi",
		CarsStatus:      "lianghao",
		CarsModelType:   "audi",
		CarsPlateNumber: "京a12138",
		CarsBond:        200000,
	}

	financeBytes, _ := json.Marshal(financeProduct)

	financeExtra := string(financeBytes)

	//附件
	attachMent := []*sh_finance_model.OrderAttachment{
		{
			FileName: "大本",
			FileUrl:  "this is a url",
		},
	}

	bytes, _ := json.Marshal(attachMent)

	req := engine.CreateOrderReq{
		OrderName: "teohubo-order-name-test",
		OrderDesc: "teohubo-order-desc-test",
		BuyerInfo: &fwe_trade_common.TradeSubjectInfo{
			SubjectType: fwe_trade_common.TradeSubjectType_Company,
			PersonInfo:  nil,
			CompanyInfo: &fwe_trade_common.CompanySubject{
				CompanyName: "四轮融资租赁有限公司",
				CreditCode:  "**********",
				OwnerName:   "张三",
				OwnerPhone:  "***********",
				BankInfo:    nil,
			},
			FweMerchant: nil,
		},
		SellerInfo: &fwe_trade_common.TradeSubjectInfo{
			SubjectType: fwe_trade_common.TradeSubjectType_Company,
			PersonInfo:  nil,
			CompanyInfo: &fwe_trade_common.CompanySubject{
				CompanyName: "重庆空间变换科技有限公司懂懂分公司",
				CreditCode:  "**********",
				OwnerName:   "罗翔",
				OwnerPhone:  "***********",
				BankInfo:    nil,
			},
			FweMerchant: nil,
		},
		ProductInfo: &fwe_trade_common.ProductInfo{
			ProductType:      fwe_trade_common.ProductType_SecondHandFinance,
			ProductID:        "1234",
			ProductName:      "库融贷产品1",
			ProductQuantity:  1,
			ProductUnitPrice: 1000000,
			SkuID:            "12135",
			ProductExtra:     &financeExtra,
		},
		FinanceList: []*fwe_trade_common.FinanceInfo{
			{
				FinanceOrderType: sh_finance_model.DefaultContType,
				Amount:           ********,
			},
		},
		OrderTag: map[string]string{
			sh_finance_model.AttachmentTag: string(bytes),
		},
		Extra:    nil,
		Operator: operator,
		Identity: identity,
		Base:     nil,
	}

	NewCreateOrderHandler().Process(context.Background(), &req)
}

func TestOrderVerify(t *testing.T) {

	tag := []*sh_finance_model.OrderAttachment{
		{
			FileName: "this is a file name",
			FileUrl:  "this is a file url",
		},
	}

	bytes, _ := json.Marshal(tag)

	req := &engine.ActionOrderReq{
		OrderID:    "3001_7121937841566797868",
		Action:     consts.SHFinanceVerifyApprove,
		BizRequest: string(bytes),
		Operator:   operator,
		Identity:   identity,
		Base:       nil,
	}

	resp := NewActionOrderHandler().Process(context.Background(), req)
	t.Log(resp)
	assert.Equal(t, resp.BaseResp.StatusCode, int32(0))
}

func TestBeginSign(t *testing.T) {

	req := &engine.ActionOrderReq{
		OrderID:    "7122705044004147244",
		Action:     consts.SHFinanceBeginSignContract,
		BizRequest: "",
		Operator:   operator,
		Identity:   identity,
		Base:       base.NewBase(),
	}

	resp := NewActionOrderHandler().Process(context.Background(), req)
	t.Log(resp)
	assert.Equal(t, resp.BaseResp.StatusCode, int32(0))
}

func TestMGet(t *testing.T) {

	req := &engine.MGetOrderInfoReq{
		OrderIds: []string{
			"7122732227913322549",
		},
		Identity: identity,
		Base:     nil,
	}

	NewMGetOrderHandler().Process(context.Background(), req)
}
