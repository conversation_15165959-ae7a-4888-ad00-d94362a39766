package handler

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type GetOrderLogHandler struct {
	*BaseHandler
}

func NewGetOrderLogHandler() *GetOrderLogHandler {
	return &GetOrderLogHandler{
		BaseHandler: NewBaseHandler(),
	}
}

func (handler *GetOrderLogHandler) Process(ctx context.Context, req *engine.GetOrderLogReq) (resp *engine.GetOrderLogResp) {
	resp = engine.NewGetOrderLogResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		dataList []*db_model.FweOrderLog
		bizErr   *errdef.BizErr
	)

	dataList, bizErr = service.NewFweOrderLog().GetOrderLogList(ctx, req.GetOrderID())
	if bizErr != nil {
		logs.CtxError(ctx, "[GetOrderLogHandler] err=%v", bizErr.Error())
		handler.GenResp(ctx, resp, bizErr)
		return
	}

	resp.Data = packer.OrderLogDBList2CommonList(dataList)
	return
}
