package handler

import (
	"context"
	"strconv"

	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metrics"

	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/execution"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type ActionOrderHandler struct {
	*BaseHandler
}

func NewActionOrderHandler() *ActionOrderHandler {
	return &ActionOrderHandler{
		BaseHandler: NewBaseHandler(),
	}
}

func (handler *ActionOrderHandler) Process(ctx context.Context, req *engine.ActionOrderReq) (resp *engine.ActionOrderResp) {
	resp = engine.NewActionOrderResp()
	resp.BaseResp = base.NewBaseResp()

	// 处理前，将bytetx注入的变量清除
	// ctx = bytetx.ClearContext(ctx)

	// 根据orderId得到对应的biz_scene,查询主库
	if req.OrderID != "" && req.OrderID != callback_model.ContractOrderID {
		bizScene, smVersion, bizErr := service.NewOrderService().GetBizSceneByID(ctx, caller.WriteDB(ctx), req.GetOrderID())
		if bizErr != nil {
			logs.CtxError(ctx, "[ActionOrderHandler] query bizScene failed error=%s", bizErr.Error())
			handler.GenResp(ctx, resp, bizErr)
			return
		}
		if req.Identity.BizScene == 0 {
			req.Identity.BizScene = bizScene
		}
		req.Identity.SmVersion = smVersion
	}

	exe, err := execution.NewExecutionFactory().GetActionOrderExecution(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[ActionOrderHandler] build execution failed error=%v", err)
		handler.GenResp(ctx, resp, err)
		return
	}

	result, err := executor.NewExecutor().Exec(ctx, exe,
		metrics.T{Name: "biz_scene", Value: strconv.Itoa(int(req.GetIdentity().GetBizScene()))},
		metrics.T{Name: "execution", Value: exe.Name()},
	)
	if err != nil {
		logs.CtxError(ctx, "[ActionOrderHandler] Exec execution failed error=%v", err)
		handler.GenResp(ctx, resp, err)
		return
	}
	if result == nil {
		return
	}
	if r, ok := result.(string); ok {
		resp.BizResponse = r
	}
	return
}
