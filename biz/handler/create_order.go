package handler

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/execution"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type CreateOrderHandler struct {
	*BaseHandler
}

func NewCreateOrderHandler() *CreateOrderHandler {
	return &CreateOrderHandler{
		BaseHandler: NewBaseHandler(),
	}
}

func (handler *CreateOrderHandler) Process(ctx context.Context, req *engine.CreateOrderReq) (resp *engine.CreateOrderResp) {
	resp = engine.NewCreateOrderResp()
	resp.BaseResp = base.NewBaseResp()

	// 身份校验
	if bizErr := handler.CheckCommonReq(ctx, req); bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderHandler] build execution failed error=%v", bizErr.Error())
		handler.GenResp(ctx, resp, bizErr)
		return
	}

	// 获取对应场景执行器
	exe, err := execution.NewExecutionFactory().GetCreateOrderExecution(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[CreateOrderHandler] build execution failed error=%v", err)
		handler.GenResp(ctx, resp, err)
		return
	}

	// 执行获取返回参数
	result, err := executor.NewExecutor().Exec(ctx, exe)
	if result != nil {
		if r, ok := result.(string); ok {
			resp.OrderID = r
		} else if r1, ok1 := result.(*model.CreateOrderResult); ok1 && r1 != nil {
			resp.OrderID = r1.OrderID
			resp.BizResponse = r1.BizResponse
		}
	}
	if err != nil {
		logs.CtxError(ctx, "[CreateOrderHandler] Exec execution failed error=%v", err)
		handler.GenResp(ctx, resp, err)
		return
	}

	return
}
