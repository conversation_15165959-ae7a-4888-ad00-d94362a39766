package handler

import (
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type MGetOrderHandler struct {
	*BaseHandler
}

func NewMGetOrderHandler() *MGetOrderHandler {
	return &MGetOrderHandler{
		BaseHandler: NewBaseHandler(),
	}
}

func (handler *MGetOrderHandler) Process(ctx context.Context, req *engine.MGetOrderInfoReq) (resp *engine.MGetOrderInfoResp) {
	resp = engine.NewMGetOrderInfoResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		dataMap map[string]*service_model.Order
		bizErr  *errdef.BizErr
	)

	if len(req.GetOrderIds()) == 0 {
		handler.GenResp(ctx, resp, errdef.NewParamsErr("order_id is empty"))
		return
	}
	if len(req.GetOrderIds()) > 50 {
		logs.CtxError(ctx, "[MGetOrderHandler] input 50")
		handler.GenResp(ctx, resp, errdef.NewParamsErr("input 50"))
		return
	}

	dataMap, bizErr = service.NewOrderService().MGetOrderByIDs(ctx, req.GetOrderIds(),
		&service.OrderOption{OptionID: service.OptionNeedFinance},
		&service.OrderOption{OptionID: service.OptionNeedCont})
	if bizErr != nil {
		logs.CtxError(ctx, "[MGetOrderHandler] build execution failed error=%v", bizErr.Error())
		handler.GenResp(ctx, resp, bizErr)
		return
	}
	resp.Data, bizErr = packer.OrderServiceMap2CommonMap(ctx, dataMap)
	if bizErr != nil {
		logs.CtxError(ctx, "[MGetOrderHandler] build execution failed error=%v", bizErr.Error())
		handler.GenResp(ctx, resp, bizErr)
		return
	}
	return
}
