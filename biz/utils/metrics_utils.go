package utils

import (
	"code.byted.org/gopkg/metrics"
	"sync"
	"time"
)

var (
	metricsClient *metrics.MetricsClientV2
	allMetrics    sync.Map
)

func init() {
	metricsClient = metrics.NewDefaultMetricsClientV2("motor.fwe_trade.engine", true)
}

// EmitCounter ...
func EmitCounter(name string, value interface{}, tags ...metrics.T) {
	var err error
	if _, initialized := allMetrics.Load(name); !initialized {
		err = metricsClient.DefineCounter(name)
		if err != nil {
			return
		}
		allMetrics.Store(name, nil)
	}
	_ = metricsClient.EmitCounter(name, value, tags...)
}

// EmitTimer ...
func EmitTimer(name string, value interface{}, tags ...metrics.T) {
	var err error
	if _, initialized := allMetrics.Load(name); !initialized {
		err = metricsClient.DefineTimer(name)
		if err != nil {
			return
		}
		allMetrics.Store(name, nil)
	}
	_ = metricsClient.EmitTimer(name, value, tags...)
}

// Timer ...
type Timer struct {
	startNano int64
	prefix    string
	tags      []metrics.T
}

// NewTimer ...
func NewTimer(prefix string, tags ...metrics.T) *Timer {
	t := &Timer{time.Now().UnixNano(), prefix, tags}
	return t
}

// Finish ... 单位毫秒
func (t *Timer) Finish() {
	cost := (time.Now().UnixNano() - t.startNano) / 1e6

	EmitTimer(t.prefix, cost, t.tags...)
}

// Reset ...
func (t *Timer) Reset(prefix string, tags ...metrics.T) {
	t.startNano = time.Now().UnixNano()
	t.prefix = prefix
	t.tags = tags
}
