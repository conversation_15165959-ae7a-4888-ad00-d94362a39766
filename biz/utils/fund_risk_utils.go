package utils

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"context"
	"fmt"
)

func CheckFundRiskOfAmount(_ context.Context, isTest bool, checkAmount int64, limitAmountPtr *int64) *errdef.BizErr {
	var (
		limitAmount = consts.TestAmount
	)
	if env.IsBoe() {
		return nil
	}
	if limitAmountPtr != nil {
		limitAmount = *limitAmountPtr
	}
	if isTest && checkAmount > limitAmount {
		return errdef.NewParamsErr(fmt.Sprintf("测试订单金额不能超过 %v 分", limitAmount))
	}
	return nil
}
