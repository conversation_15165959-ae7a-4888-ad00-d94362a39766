package utils

import (
	"errors"
	"github.com/go-sql-driver/mysql"
)

func IsDuplicateEntry(err error) bool {
	for {
		if isMysqlError(err, 1062) {
			return true
		}

		err = errors.Unwrap(err)
		if err == nil {
			return false
		}
	}
}

func isMysqlError(err error, code uint16) bool {
	if err == nil {
		return false
	}
	if mysqlError, ok := err.(*mysql.MySQLError); ok {
		if mysqlError.Number == code {
			return true
		}
	}

	return false
}
