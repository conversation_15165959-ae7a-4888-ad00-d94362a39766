package utils

import (
	"encoding/json"
	"strings"

	"github.com/bytedance/sonic"
)

func <PERSON>(t interface{}) (string, error) {
	b, err := json.<PERSON>(t)
	if err != nil {
		return "", err
	}
	return string(b), nil
}

func MarshalToStr(t interface{}) string {
	b, err := json.<PERSON>(t)
	if err != nil {
		return ""
	}
	return string(b)
}

func Unmarshal(str string, t interface{}) error {
	d := json.NewDecoder(strings.NewReader(str))
	d.UseNumber()
	err := d.Decode(t)
	return err
}

var api = sonic.Config{
	UseInt64: true,
}.Froze()

func SonicUnmarshal(str string, t interface{}) error {
	d := api.NewDecoder(strings.NewReader(str))
	return d.Decode(t)
}

func DeepCopy[T any, E any](src *T, dst *E) error {
	byteArr, err := json.Marshal(src)
	if err != nil {
		return err
	}
	err = json.Unmarshal(byteArr, dst)
	return err
}
