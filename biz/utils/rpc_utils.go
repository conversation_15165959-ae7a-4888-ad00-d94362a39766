package utils

import (
	"code.byted.org/kite/kitex/pkg/rpcinfo"
	"code.byted.org/kite/kitutil"
	"context"
)

func GetCaller(ctx context.Context) string {
	rpcInfo := rpcinfo.GetRPCInfo(ctx)
	if rpcInfo != nil && rpcInfo.From() != nil {
		return rpcInfo.From().ServiceName()
	}
	return ""
}

// GetMethod 获取调用方法
func GetMethod(ctx context.Context) string {
	info := rpcinfo.GetRPCInfo(ctx)
	if info != nil && info.To() != nil {
		return info.To().Method()
	}
	return ""
}

func IsBizSuccess(code int32) bool {
	return code == int32(0)
}

func GetTraceID(ctx context.Context) string {
	logID, _ := kitutil.GetCtxLogID(ctx)
	return logID
}
