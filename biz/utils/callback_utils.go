package utils

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/gopkg/tools"
	"fmt"
	"os"
)

func MakeOuterContSerial(orderID string, contType int32, version int32) string {
	return fmt.Sprintf("%s_%d_%d", orderID, contType, version)
}

func MakeCallbackEvent(action string) string {
	if action == "" {
		return ""
	}
	var cluster, env string
	if cluster == "" {
		cluster = "default"
	}
	if env == "" {
		env = os.Getenv("TCE_ENV")
	}
	return fmt.Sprintf("%s:%s:%s", action, cluster, env)
}

var (
	refundPrefix = "refund"
	settlePrefix = "settle"
)

func MakeFinanceOrderID(financeOrderID int64, financeOrderType int32) string {
	return fmt.Sprintf("%d_%d", financeOrderID, financeOrderType)
}

func MakeFinanceOrderIDTool(orderID string, fType int32) string {
	return fmt.Sprintf("%s_%d", orderID, fType)
}

func MakeRefundFinanceOutID(orderID string, fType int32) string {
	return fmt.Sprintf("%s_%s_%d", refundPrefix, orderID, fType)
}

func MakeSettleFinanceOutID(orderID string, fType int32) string {
	return fmt.Sprintf("%s_%s_%d", settlePrefix, orderID, fType)
}

func MakeContractCallbackExtra(orderId string) string {
	extraModel := callback_model.ContractCBExtraModel{OrderID: orderId}
	return tools.GetLogStr(extraModel)
}
