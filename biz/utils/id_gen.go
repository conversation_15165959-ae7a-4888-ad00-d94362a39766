package utils

import (
	"fmt"

	"code.byted.org/gopkg/idgenerator"
)

var (
	genIdClient *idgenerator.IdGeneratorClient
)

func init() {
	genIdClient = idgenerator.New64BitIdGeneratorClient("global", "motor")
}

func GenId() (int64, error) {
	return genIdClient.Gen()
}

func TryGenId(maxTimes int) (id int64, err error) {
	for i := 0; i < maxTimes && id <= 0; i++ {
		id, err = GenId()
	}
	return id, err
}

func MustGenIDInt64() (id int64, err error) {
	id, err = TryGenId(3)
	if err != nil {
		return
	}
	if id <= 0 {
		err = fmt.Errorf("gen id <= 0")
		return
	}
	return
}

// MustGenIDStr ... 保证id生成是有效的，不然就抛出err
func MustGenIDStr() (idStr string, err error) {
	var id int64
	id, err = TryGenId(3)
	if err != nil {
		return
	}
	if id <= 0 {
		err = fmt.Errorf("gen id <= 0")
		return
	}
	idStr = fmt.Sprintf("%d", id)
	return
}
