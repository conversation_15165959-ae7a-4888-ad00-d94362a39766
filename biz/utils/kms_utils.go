package utils

import "code.byted.org/motor/dealer_utils_go/kms_v2"

func MEncrypt(list []string) []string {
	var encList []string
	for _, v := range list {
		enc, _ := kms_v2.Encrypt(v)
		encList = append(encList, enc)
	}
	return encList
}

func Encrypt(source string) string {
	enc, _ := kms_v2.Encrypt(source)
	return enc
}

func MDecrypt(encList []string) []string {
	var list []string
	for _, v := range encList {
		dec, _ := kms_v2.Decrypt(v)
		list = append(list, dec)
	}
	return list
}

func Decrypt(source string) string {
	dec, _ := kms_v2.Decrypt(source)
	return dec
}
