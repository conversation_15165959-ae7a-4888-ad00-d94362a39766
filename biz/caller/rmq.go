package caller

import (
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/config"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/producer"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

var (
	OrderEventProducer       producer.Producer
	OrderCancelEventProducer producer.Producer
)

func InitRMQ() {
	InitOrderEvent()
	InitOrderCancelProducer()
}

func InitOrderEvent() {
	var clusterName string
	if env.IsBoe() {
		clusterName = "rmq_new_sandbox"
	} else {
		clusterName = "dcar_normal"
	}
	cfg := config.NewProducerConfig(consts.PSM, clusterName)
	cfg.ProduceTimeout = 1000 * time.Millisecond
	cfg.StressTestStrategy = config.StressTestStrategyAbandon
	producer, err := producer.NewProducer(cfg)
	if err != nil {
		panic(err)
	}
	OrderEventProducer = producer
}

func InitOrderCancelProducer() {
	var clusterName string
	if env.IsBoe() {
		clusterName = "rmq_sandbox2_ipv6"
	} else {
		clusterName = "dcar_normal"
	}
	cfg := config.NewProducerConfig(consts.PSM, clusterName)
	cfg.ProduceTimeout = 1000 * time.Millisecond
	cfg.StressTestStrategy = config.StressTestStrategyAbandon
	producer, err := producer.NewProducer(cfg)
	if err != nil {
		panic(err)
	}
	OrderCancelEventProducer = producer
}

/*func commitMsg() {

	txFunc := func(bytetxCtx context.Context) (interface{}, error) {
		resp, err := sendNormalMsg(bytetxCtx)
		//resp, err := sendOrderMessage(bytetxCtx)
		//resp, err := sendBatchMessage(bytetxCtx)
		//resp, err := sendBatchOrderMessage(bytetxCtx)
		//resp, err := sendDeferMessage(bytetxCtx)
		//resp, err := sendAssignQueueMessage(bytetxCtx)

		if err != nil {
			log.Printf("put error : %v", err)
		} else {
			log.Println(resp)
		}

		return resp, err
	}

	ctx := context.TODO()
	result, err := bytetx.Execute(ctx, "rmq_test", txFunc)
	if err != nil {
		panic(err)
	}
	_, bizError := result.GetResult()
	if bizError != nil {
		panic("expect commit but failed")
	}
}

func sendNormalMsg(ctx context.Context) (interface{}, error) {
	msg := types.NewMessage(topic, []byte("bytetx normal msg : "+time.Now().String()))
	return p.Send(ctx, msg)
}

func sendOrderMessage(ctx context.Context) (interface{}, error) {
	msg := types.NewOrderlyMessage(topic, "abc", []byte("bytetx order msg : "+time.Now().String()))
	return p.Send(ctx, msg)
}

func sendBatchMessage(ctx context.Context) (interface{}, error) {
	msgs := make([]*types.Message, 0, 1)
	for i := 0; i < 3; i++ {
		msgs = append(msgs, types.NewMessage(topic, []byte("bytetx batch message "+strconv.Itoa(i))))
	}
	return p.SendBatch(ctx, msgs)
}

func sendBatchOrderMessage(ctx context.Context) (interface{}, error) {
	msgs := make([]*types.Message, 0, 1)
	for i := 0; i < 3; i++ {
		msgs = append(msgs, types.NewOrderlyMessage(topic, "abc", []byte("bytetx batch order msg : "+strconv.Itoa(i))))
	}
	return p.SendBatch(ctx, msgs)
}

func sendDeferMessage(ctx context.Context) (interface{}, error) {
	// defer range: [1s ~ 7Day] (by default)
	msg := types.NewDeferMessage(topic, 10*time.Second, []byte("bytetx defer msg : "+time.Now().String()))
	msg.DeferLoops = 3
	return p.Send(ctx, msg)

}

func sendAssignQueueMessage(ctx context.Context) (interface{}, error) {
	queues, err := p.QueryTopicQueues(topic)
	if err != nil {
		log.Printf("query topic: %s queues, error : %v", topic, err)
	}

	result := make([]*types.SendResult, 0, 1)
	for i, messageQueue := range queues {
		msg := types.NewMessage(topic, []byte("bytetx queued msg "+strconv.Itoa(i)))
		resp, err := p.SendMessageByQueue(ctx, msg, messageQueue)
		if err != nil {
			return nil, err
		}
		result = append(result, resp)
	}
	return result, nil
}
*/
