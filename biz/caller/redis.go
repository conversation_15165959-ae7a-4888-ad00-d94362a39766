package caller

import (
	"context"

	"code.byted.org/aurora/block"
	// _ "code.byted.org/gopkg/bytetx/client/redis"
	"code.byted.org/kv/goredis"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

var (
	OrderRedisClient *goredis.Client
)

func InitRedisAndBlock() {
	cluster := "toutiao.redis.motor_fwe_trade_order"
	var err error
	options := goredis.NewOption()
	options.SetServiceDiscoveryWithConsul()
	OrderRedisClient, err = goredis.NewClientWithOption(cluster, options)
	if err != nil {
		panic(err)
	}
	// 初始化分布式锁
	err = block.InitWithCli(context.Background(), consts.PSM, OrderRedisClient)
	if err != nil {
		panic(err)
	}
}
