package caller

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/dbresolver"

	// _ "code.byted.org/gopkg/bytetx/client/mysql"
	"code.byted.org/gopkg/env"
	"code.byted.org/gorm/bytedgorm"
)

/*
Gorm Gen文档 https://bytedance.feishu.cn/wiki/wikcnbYLEL78aOYLBsT2ExUGiEd#QVZLFk
ByteTx接入文档 https://bytedance.feishu.cn/docs/doccnBpjcO9t1ZqmNoAl5G7PNfb#2Xk8wk
*/

var (
	TradeDBClient        *gorm.DB
	tradeDBDefaultClient *gorm.DB
)

func InitMysql() {
	var err error
	TradeDBClient, err = gorm.Open(
		bytedgorm.MySQL("toutiao.mysql.motor_trade_infra", "motor_trade_infra"),
		//With(func(config *bytedgorm.DBConfig) {
		//	config.DriverName = "bytetx_mysql"
		//}),
		bytedgorm.WithDefaults(),
		bytedgorm.WithStressTestSupport(),
		bytedgorm.Logger{LogLevel: logger.Info},
	)
	if err != nil {
		panic(err)
	}
	TradeDBClient = TradeDBClient.Debug()
}

// WriteDB ...
func WriteDB(ctx context.Context) *gorm.DB {
	return TradeDBClient.Clauses(dbresolver.Write).WithContext(ctx)
}

// ReadDB ...
func ReadDB(ctx context.Context) *gorm.DB {
	return TradeDBClient.Clauses(dbresolver.Read).WithContext(ctx)
}

func DB(ctx context.Context) *gorm.DB {
	return TradeDBClient.WithContext(ctx)
}

/* 不使用byteTx的数据库 */

func InitMysqlDefault() {
	var err error
	tradeDBDefaultClient, err = gorm.Open(
		bytedgorm.MySQL("toutiao.mysql.motor_trade_infra", "motor_trade_infra").WithReadReplicas(),
		bytedgorm.WithDefaults(),
		bytedgorm.WithStressTestSupport(),
		bytedgorm.Logger{LogLevel: logger.Info},
	)
	if err != nil {
		panic(err)
	}
	if env.IsBoe() || env.IsPPE() {
		tradeDBDefaultClient = tradeDBDefaultClient.Debug()
	}
}

// WriteDBDefault ...
func WriteDBDefault(ctx context.Context) *gorm.DB {
	return tradeDBDefaultClient.Clauses(dbresolver.Write).WithContext(ctx)
}

// ReadDBDefault ...
func ReadDBDefault(ctx context.Context) *gorm.DB {
	return tradeDBDefaultClient.Clauses(dbresolver.Read).WithContext(ctx)
}

// DBDefault ... Read write separation
func DBDefault(ctx context.Context) *gorm.DB {
	return tradeDBDefaultClient.WithContext(ctx)
}
