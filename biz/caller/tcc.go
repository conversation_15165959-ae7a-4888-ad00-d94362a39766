package caller

import (
	"code.byted.org/gopkg/tccclient"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
)

var (
	DefaultTccClient *tccclient.ClientV2
)

func InitTCC() {
	config := tccclient.NewConfigV2()
	config.Confspace = "default" // 配置空间, 可不传，默认为default
	config.Auth = true
	var err error
	DefaultTccClient, err = tccclient.NewClientV2(consts.PSM, config)
	if err != nil {
		panic(err)
	}
}
