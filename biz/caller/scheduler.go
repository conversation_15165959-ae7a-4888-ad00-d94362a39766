package caller

import (
	"code.byted.org/gopkg/env"
	sdk "code.byted.org/motor/scheduler-go"
)

const (
	namespaceBoe  = "dongchedi_fwe_trade_test"
	namespaceProd = "dongchedi_trade"
	timeoutGroup  = "timeout_group"
)

var (
	Scheduler sdk.Scheduler
	namespace string
)

func InitScheduler() {
	namespace = namespaceProd
	if !env.IsProduct() {
		namespace = namespaceBoe
	}

	var err error
	Scheduler, err = sdk.NewScheduler(namespace, timeoutGroup)
	if err != nil {
		panic(err)
	}
}
