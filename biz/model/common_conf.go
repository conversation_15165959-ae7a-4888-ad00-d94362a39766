package model

type CommonConf struct {
	CheckContStructField                     bool                   `json:"check_cont_struct_field"`
	CheckContOptionAllowIncomeAmountOverflow bool                   `json:"check_cont_option_allow_income_amount_overflow"`
	PlatformSplitUID                         string                 `json:"platform_split_uid"`                // 平台分账uid
	PlatformSplitUIDTest                     string                 `json:"platform_split_uid_test"`           // 平台分账测试uid
	RentSplitUID                             string                 `json:"rent_split_uid"`                    // 融租公司分账uid
	AdvanceAccountUID                        string                 `json:"advance_account_uid"`               // 垫资账户uid
	ApproveStarter                           *ApproveStarter        `json:"approve_starter"`                   // 审批发起人
	AgreementMerchant                        *AgreementMerchantInfo `json:"agreement_merchant"`                // 协议扣款商户信息, key: merchant_id
	TestOrderLimitAmount                     *int64                 `json:"test_order_limit_amount,omitempty"` // 测试订单限制金额
}

type ApproveStarter struct {
	Name        string `json:"name"`
	OpenID      string `json:"open_id"`
	ApproveCode string `json:"approve_code"`
}

type AgreementMerchantInfo struct {
	AppID            string `json:"app_id"`
	AppName          string `json:"app_name"`
	MerchantID       string `json:"merchant_id"`
	MerchantName     string `json:"merchant_name"`
	Uid              string `json:"uid"`
	UidType          int32  `json:"uid_type"`
	CardNo           string `json:"card_no"`
	InnerAgreementNo string `json:"inner_agreement_no"`
	AccountProp      string `json:"account_prop"`
}
