package callback_model

import "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"

const (
	ContractOrderID = "Fix_OrderID"
)

type PayCallbackModel struct {
	TenantType       int32  `json:"tenant_type,omitempty"`        // 业务线
	BizScene         int32  `json:"biz_scene,omitempty"`          // 场景
	MerchantID       string `json:"merchant_id,omitempty"`        // 商户id
	Mid              string `json:"mid,omitempty"`                // 商户mid
	OrderID          string `json:"order_id,omitempty"`           // 订单id
	OrderName        string `json:"order_name,omitempty"`         // 订单名称
	FinanceOrderID   string `json:"finance_order_id,omitempty"`   // 资金单id
	FinanceOrderType int32  `json:"finance_order_type,omitempty"` // 资金单类型
	TotalAmount      int64  `json:"total_amount,omitempty"`       // 资金单总体金额
	Status           int32  `json:"status,omitempty"`             // 支付成功回调，只有一个成功状态，枚举值： fwe_trade_common.CommonStatus_Success

	Extra string `json:"extra,omitempty"` // 其他参数
}

type UnionPayCallbackModel struct {
	TenantType  int32                         `json:"tenant_type"`  // 业务线
	BizScene    int32                         `json:"biz_scene"`    // 场景
	OutID       string                        `json:"out_id"`       // 业务id
	OrderType   fwe_trade_common.OrderType    `json:"order_type"`   // 订单类型
	OrderID     string                        `json:"order_id"`     // 订单id
	TotalAmount int64                         `json:"total_amount"` // 总金额
	Status      fwe_trade_common.CommonStatus `json:"status"`       // 状态， 只有成功
	Extra       string                        `json:"extra"`        // 回调透传信息
}

type RefundCallbackModel struct {
	TenantType       int32  `json:"tenant_type,omitempty"`        // 业务线
	BizScene         int32  `json:"biz_scene,omitempty"`          // 场景
	MerchantID       string `json:"merchant_id,omitempty"`        // 商户id
	Mid              string `json:"mid,omitempty"`                // 商户mid
	OrderID          string `json:"order_id,omitempty"`           // 订单id
	OrderName        string `json:"order_name,omitempty"`         // 订单名称
	FinanceOrderID   string `json:"finance_order_id,omitempty"`   // 资金单id
	FinanceOrderType int32  `json:"finance_order_type,omitempty"` // 资金单类型
	PayOrderNo       string `json:"pay_order_no,omitempty"`       // 支付单号
	RefundOrderNo    string `json:"refund_order_no,omitempty"`    // 退款单号
	RefundAmount     int64  `json:"refund_amount,omitempty"`      // 退款金额
	Status           int32  `json:"status,omitempty"`             // 退款状态回调，理论上只有一个成功状态，枚举值： fwe_trade_common.CommonStatus_Success
	TradeNo          string `json:"trade_no,omitempty"`           // 财经退款单号
	OutID            string `json:"out_id"`                       // 外部单号

	Extra string `json:"extra,omitempty"` // 其他参数
}

type WithdrawCallbackModel struct {
	TenantType       int32  `json:"tenant_type,omitempty"`        // 业务线
	BizScene         int32  `json:"biz_scene,omitempty"`          // 场景
	MerchantID       string `json:"merchant_id,omitempty"`        // 商户id
	Mid              string `json:"mid,omitempty"`                // 商户mid
	OrderID          string `json:"order_id,omitempty"`           // 订单id
	OrderName        string `json:"order_name,omitempty"`         // 订单名称
	FinanceOrderID   string `json:"finance_order_id,omitempty"`   // 资金单id
	FinanceOrderType int32  `json:"finance_order_type,omitempty"` // 资金单类型
	TotalAmount      int64  `json:"total_amount,omitempty"`       // 出款总金额
	Status           int32  `json:"status,omitempty"`             // 出款回调状态，目前只有一个成功状态，枚举值： fwe_trade_common.CommonStatus_Success

	Extra string `json:"extra,omitempty"` // 其他参数
}

type SettleCallbackModel struct {
	TenantType       int32  `json:"tenant_type,omitempty"`        // 业务线
	BizScene         int32  `json:"biz_scene,omitempty"`          // 场景
	MerchantID       string `json:"merchant_id,omitempty"`        // 商户id
	Mid              string `json:"mid,omitempty"`                // 商户mid
	OrderID          string `json:"order_id,omitempty"`           // 订单id
	OrderName        string `json:"order_name,omitempty"`         // 订单名称
	FinanceOrderID   string `json:"finance_order_id,omitempty"`   // 资金单id
	FinanceOrderType int32  `json:"finance_order_type,omitempty"` // 资金单类型
	TotalAmount      int64  `json:"total_amount,omitempty"`       // 出款总金额
	Status           int32  `json:"status,omitempty"`             // 出款回调状态，目前只有一个成功状态，枚举值： fwe_trade_common.CommonStatus_Success
	OutID            string `json:"out_id"`                       // 外部单号

	Extra string `json:"extra,omitempty"` // 其他参数
}

type ContractCallbackModel struct {
	TenantType      int32  `json:"tenant_type,omitempty"`       // 业务线
	BizScene        int32  `json:"biz_scene,omitempty"`         // 场景
	OutContID       string `json:"out_cont_id,omitempty"`       // 外部合同id
	ContSerial      string `json:"cont_serial,omitempty"`       // 合同编号
	ESignContSerial string `json:"esign_cont_serial,omitempty"` // 电子签底层合同编号
	Status          int32  `json:"status,omitempty"`            // 状态， 目前只有一个成功状态，枚举值：fwe_trade_common.CommonStatus_Success
	SignTime        int64  `json:"sign_time"`                   // 签署时间，
	Extra           string `json:"extra,omitempty"`             // 其他参数
	ContName        string `json:"cont_name"`                   // 合同名称
}

type ContractCBExtraModel struct {
	OrderID string `json:"order_id"`
}

// InvoiceResult 发票结果
type InvoiceResult struct {
	InvoiceBizScene   int32    `json:"invoice_biz_scene"`
	InvoiceMode       int32    `json:"invoice_mode"`
	InvoiceType       int32    `json:"invoice_type"`
	InvoiceFuncType   int32    `json:"invoice_func_type"`
	RelatedOutIDs     []string `json:"related_out_ids"`
	InvoiceOrderIDs   []string `json:"invoice_order_ids"`
	InvoiceItem       string   `json:"invoice_item"`
	ActualInvoiceItem string   `json:"actual_invoice_item"`
	InvoiceStatus     int32    `json:"invoice_status"`
	CreateTime        int64    `json:"create_time"`
	FinishTime        int64    `json:"finish_time"`
	Extra             string   `json:"extra"`
}
