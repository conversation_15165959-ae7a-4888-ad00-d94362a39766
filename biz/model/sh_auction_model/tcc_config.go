package sh_auction_model

type MerchantInfo struct {
	AppID        string `json:"app_id"`
	MerchantID   string `json:"merchant_id"`
	MerchantName string `json:"merchant_name"`
	PlatformUID  string `json:"platform_uid"`
}
type ShAuctionConfig struct {
	NormalPayMerchant      *MerchantInfo `json:"normal_pay_merchant"`      // 代收财经商户信息
	NormalWithdrawMerchant *MerchantInfo `json:"normal_withdraw_merchant"` // 代付财经商户信息
	GuaranteePayMerchant   *MerchantInfo `json:"guarantee_pay_merchant"`   // 担保财经商户信息
}
