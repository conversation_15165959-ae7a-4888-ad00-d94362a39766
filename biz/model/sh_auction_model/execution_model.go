package sh_auction_model

import (
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type SingleRefund struct {
	FinanceOrderType int32 `thrift:"finance_order_type,1" json:"finance_order_type"`
	Amount           int64 `thrift:"amount,2" json:"amount"`
}

type CashRefundReq struct {
	RefundType    int32             `thrift:"refund_type,1,required" json:"refund_type"`
	RefundList    []*SingleRefund   `thrift:"refund_list,3,required" json:"refund_list"`
	Reason        string            `thrift:"reason,4,required" json:"reason"`
	OrderTag      map[string]string `thrift:"order_tag,5,optional" json:"order_tag,omitempty"`
	Extra         map[string]string `thrift:"extra,201,optional" json:"extra,omitempty"`
	IpAddress     string            `thrift:"ip_address,14" json:"ip_address"`
	CallbackEvent string            `json:"callback_event"`
}

type SettleReq struct {
	SplitInfo      *fwe_trade_common.TradeSpiltInfo `thrift:"split_info,3" json:"split_info"`
	Reason         string                           `thrift:"reason,4,required" json:"reason"`
	SettleType     int32                            `thrift:"settle_type,5,required" json:"settle_type"`
	Extra          map[string]string                `thrift:"extra,201,optional" json:"extra,omitempty"`
	IpAddress      string                           `thrift:"ip_address,12,required" json:"ip_address"`
	CallbackEvent  string                           `json:"callback_event"`
	IsAutoWithdraw bool                             `json:"is_auto_withdraw"`
	FcType         string                           `json:"fc_type"`
	FcSceneCode    int64                            `json:"fc_scene_code"`
	OrderTag       map[string]string                `json:"order_tag,omitempty"`
}

type CreateFinanceOrderReq struct {
	TotalAmount  int64                         `json:"total_amount"`
	FinanceOrder *fwe_trade_common.FinanceInfo `json:"finance_order"`
	OrderTag     map[string]string             `json:"order_tag"`
}
