package sh_finance_model

import (
	"encoding/json"
	"testing"
)

func TestGenConfig(t *testing.T) {

	configProd := &ShFinanceConfig{
		MerchantID:   "1200006181",
		MerchantName: "四轮融租",
		Mid:          "",
		AppID:        "************",
		Uid:          "DCD_SL_OUT_1200006181",
		UidType:      1005,
		ContTmplID:   2,
		Payer: &Participant{
			IdentifyType: "PLATFORM",
			Aid:          36,
			MerchantId:   "1200006181",
			Uid:          "DCD_SL_OUT_1200006181",
			UidType:      1005,
			AppId:        "************",
		},
		Payee: &Participant{
			IdentifyType: "PLATFORM",
			Aid:          36,
			MerchantId:   "1200003192",
			Uid:          "DCD_IN_1200003192",
			UidType:      1005,
			AppId:        "80003192307",
		},
	}
	bytes, _ := json.<PERSON>(configProd)
	t.Log(string(bytes))
}

func TestGenConfigForBoe(t *testing.T) {

	configBoe := &ShFinanceConfig{
		MerchantID:   "6200002999",
		MerchantName: "四轮融租",
		Mid:          "",
		AppID:        "************",
		Uid:          "DCD_OUT_6200002999",
		UidType:      1005,
		ContTmplID:   185,
		Payer: &Participant{
			IdentifyType: "PLATFORM",
			Aid:          36,
			MerchantId:   "6200002999",
			Uid:          "DCD_OUT_6200002999",
			UidType:      1005,
			AppId:        "************",
		},
		Payee: &Participant{
			IdentifyType: "PLATFORM",
			Aid:          36,
			MerchantId:   "6200008400",
			Uid:          "DCD_OUT_6200008400",
			UidType:      1005,
			AppId:        "************",
		},
	}
	bytes, _ := json.Marshal(configBoe)
	t.Log(string(bytes))
}
