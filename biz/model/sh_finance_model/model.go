package sh_finance_model

type ShFinanceConfig struct {
	MerchantID   string       `json:"merchant_id"`
	MerchantName string       `json:"merchant_name"`
	Mid          string       `json:"mid"`
	AppID        string       `json:"app_id"`
	Uid          string       `json:"uid"`
	UidType      int64        `json:"uid_type"`
	ContTmplID   int64        `json:"cont_tmpl_id"`
	Payer        *Participant `json:"payer"`
	Payee        *Participant `json:"payee"`
}

type Participant struct {
	IdentifyType string `json:"identify_type"`
	Aid          int32  `json:"aid"`
	MerchantId   string `json:"merchant_id"`
	Uid          string `json:"uid"`
	UidType      int32  `json:"uid_type"`
	AppId        string `json:"app_id"`
}

//DefaultContType 默认合同类型

var (
	DefaultContType  = int32(0)
	MinePartBizScene = int32(2)
	AttachmentTag    = "attachment"
)

// 金融商品信息

type FinanceProductInfo struct {
	ProductId       string  `thrift:"product_id,1" json:"product_id"`
	SkuCode         string  `thrift:"sku_code,2" json:"sku_code"`
	BuycarOrderId   string  `thrift:"buycar_order_id,3" json:"buycar_order_id"`
	VinCode         string  `thrift:"vin_code,4" json:"vin_code"`
	AssessAmount    int64   `thrift:"assess_amount,5" json:"assess_amount"`
	LoanAmount      int64   `thrift:"loan_amount,6" json:"loan_amount"`
	LoanDays        int64   `thrift:"loan_days,7" json:"loan_days"`
	TransferCount   int64   `thrift:"transfer_count,8" json:"transfer_count"`
	InterestRate    float64 `thrift:"interest_rate,9" json:"interest_rate"`
	CarsMileage     float64 `thrift:"cars_mileage,10" json:"cars_mileage"`
	CarsLastOwner   string  `thrift:"cars_last_owner,11" json:"cars_last_owner"`
	CarsStatus      string  `thrift:"cars_status,12" json:"cars_status"`
	CarsModelType   string  `thrift:"cars_model_type,20" json:"cars_model_type"`
	CarsPlateNumber string  `thrift:"cars_plate_number,21" json:"cars_plate_number"`
	CarsBond        int64   `thrift:"cars_bond,22" json:"cars_bond"`
}

//OrderAttachment 金融订单附件
type OrderAttachment struct {
	FileName string `json:"file_name"`
	FileUrl  string `json:"file_url"`
}
