package sh_buy_model

import (
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type ConfCompany struct {
	Name       string `json:"name"`
	CreditCode string `json:"credit_code"`
}

type Conf struct {
	*model.BaseConfig
	SMSChannel         int32        `json:"sms_channel"`
	GuaranteeContSmsID int64        `json:"guarantee_cont_sms_id"`
	BusinessContSmsID  int64        `json:"business_cont_sms_id"`
	CompanyInfo        *ConfCompany `json:"company_info"` // 收车买方
}

type ConfV2 struct {
	*model.BaseConfig
	MerchantInfo []*model.MerchantBase `json:"merchant_info"` // merchantInfo
	CompanyInfo  *ConfCompany          `json:"company_info"`  // 收车买方
}

func (v ConfV2) GetMerchantBase(deskType fwe_trade_common.CashierDeskType) (*model.MerchantBase, *errdef.BizErr) {
	if v.MerchantInfo == nil || len(v.MerchantInfo) == 0 {
		return nil, errdef.NewRawErr(errdef.LackConfigErr, "no merchant_info config")
	}
	var res *model.MerchantBase
	for _, base := range v.MerchantInfo {
		if slices.Contains(base.CashierType, deskType.String()) {
			res = base
			break
		}
	}
	if res == nil {
		return nil, errdef.NewRawErr(errdef.LackConfigErr, "no target merchant_info config")
	}
	return res, nil
}

type ContSignReq struct {
	ContTmplID int64             `json:"cont_tmpl_id"`
	ContParams map[string]string `json:"cont_params"`
}

type ContSignRsp struct {
	ContSerial string `json:"cont_serial"`
}

type WithdrawReq struct {
	FinanceOrderType consts.FinanceOrderType `json:"finance_order_type"`
	Amount           int64                   `json:"amount"`
}

type WithdrawRsp struct {
	WithdrawNo string `json:"withdraw_no"`
}

type CashPayReq struct {
	CashierDeskType  fwe_trade_common.CashierDeskType `thrift:"cashier_desk_type,2" json:"cashier_desk_type"`
	FinanceOrderType consts.FinanceOrderType          `json:"finance_order_type"`
	ExpireTime       int64                            `thrift:"expire_time,10" json:"expire_time"`
	RedirectUrl      string                           `thrift:"redirect_url,11" json:"redirect_url"`
	Ip               string                           `thrift:"ip,12" json:"ip"`
}

type CashPayRsp struct {
	PayNo   string `json:"pay_no"`
	PayData string `json:"pay_data"`
}

type CashRefundReq struct {
	FinanceOrderType consts.FinanceOrderType `json:"finance_order_type"`
	Reason           string                  `json:"reason"`
}

type CashRefundRsp struct {
	RefundNo string `json:"refund_no"`
}

type CancelOrderReq struct {
	Reason string `json:"reason"`
}
