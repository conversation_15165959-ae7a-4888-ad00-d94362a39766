package sh_nation_sell_model

type Conf struct {
	NormalPayMerchant *MerchantInfo          `json:"normal_pay_merchant"` // 代收代付财经商户信息
	ContInfoMap       map[string]*ContInfo   `json:"cont_info_map"`       // 合同信息，key: 合同类型 intent、sell、insurance
	ApproveStarter    *ApproveStarter        `json:"approve_starter"`     // 审批发起人
	AgreementMerchant *AgreementMerchantInfo `json:"agreement_merchant"`  // 协议扣款商户信息
}

type ConfV2 struct {
	YZTPayMerchant         *MerchantInfo          `json:"yzt_pay_merchant"`                  // 云直通支付配置
	NormalPayMerchant      *MerchantInfo          `json:"normal_pay_merchant"`               // 代收财经商户信息
	NormalWithdrawMerchant *MerchantInfo          `json:"normal_withdraw_merchant"`          // 代付财经商户信息
	GuaranteePayMerchant   *MerchantInfo          `json:"guarantee_pay_merchant"`            // 担保财经商户信息
	POSPayMerchant         *MerchantInfo          `json:"pos_pay_merchant"`                  // POS财经商户信息
	SpecialPOSConfig       *SpecialPOSConfig      `json:"special_pos_config"`                // 全国购POS特殊配置
	RentBankInfo           *RentBankInfo          `json:"rent_bank_info"`                    // 融租公司银行卡信息
	ForbidPrivateBankCard  bool                   `json:"forbid_private_bank_card"`          // 是否禁止对私银行卡信息
	ContInfoMap            map[string]*ContInfo   `json:"cont_info_map"`                     // 合同信息，key: 合同类型 intent、sell、insurance
	ApproveStarter         *ApproveStarter        `json:"approve_starter"`                   // 审批发起人
	AgreementMerchant      *AgreementMerchantInfo `json:"agreement_merchant"`                // 协议扣款商户信息
	CheckContStructField   bool                   `json:"check_cont_struct_field"`           // 是否开启合同机构化校验
	TestOrderLimitAmount   *int64                 `json:"test_order_limit_amount,omitempty"` // 测试订单限制金额
}

type MerchantInfo struct {
	AppID              string `json:"app_id"`
	MerchantID         string `json:"merchant_id"`
	MerchantName       string `json:"merchant_name"`
	HelperFweAccountID string `json:"helper_fwe_account_id"`
	PlatformUID        string `json:"platform_uid"`
}

type ContInfo struct {
	TmplID     int64 `json:"tmpl_id"`
	SmsTmplID  int64 `json:"sms_tmpl_id"`
	SmsChannel int32 `json:"sms_channel"`
}

type CreditInfo struct {
	CreditCode string `json:"credit_code"`
	Name       string `json:"name"`
}

type ApproveStarter struct {
	Name        string `json:"name"`
	OpenID      string `json:"open_id"`
	ApproveCode string `json:"approve_code"`
}

type AgreementMerchantInfo struct {
	AppID            string `json:"app_id"`
	AppName          string `json:"app_name"`
	MerchantID       string `json:"merchant_id"`
	MerchantName     string `json:"merchant_name"`
	Uid              string `json:"uid"`
	UidType          int32  `json:"uid_type"`
	CardNo           string `json:"card_no"`
	InnerAgreementNo string `json:"inner_agreement_no"`
	AccountProp      string `json:"account_prop"`
}

type RentBankInfo struct {
	AccountNo   string `json:"account_no"`
	AccountName string `json:"account_name"`
	BankName    string `json:"bank_name"`
	CnapsCode   string `json:"cnaps_code"`
	PubPriType  int32  `json:"pub_pri_type"`
}

type SpecialPOSConfig struct {
	FixedSplitUID     string `json:"fixed_split_uid"`
	FixedSplitUIDType int32  `json:"fixed_split_uid_type"`
}
