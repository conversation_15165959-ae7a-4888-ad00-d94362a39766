package sh_nation_sell_model

import (
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type OrderTagModel struct {
	OrderTag map[string]string `json:"order_tag"`
}

func (m OrderTagModel) GetOrderTag() map[string]string {
	if m.OrderTag == nil {
		return map[string]string{}
	}

	return m.OrderTag
}

type TransferOwnerModel struct {
	OrderTagModel
}

type DeliveryCarModel struct {
	OrderTagModel
	FweAccountBankInfo map[string]*fwe_trade_common.BankInfo `json:"fwe_account_bank_info"`
}

type CancelModel struct {
	OrderTagModel
}

type SingleRefund struct {
	FinanceOrderType int32 `json:"finance_order_type"`
	Amount           int64 `json:"amount"`
}

type CancelWithRefund struct {
	OrderTagModel
	RefundList []*SingleRefund `json:"refund_list"`
	Reason     string          `json:"reason"`
}

type CancelWithRefundResult struct {
	RefundOrderNo string `json:"refund_order_no"`
}

type CreateCashPayModel struct {
	OrderTagModel
	Identity            *fwe_trade_common.BizIdentity         `json:"identity"`
	OrderId             string                                `json:"order_id"`
	FinanceType         int32                                 `json:"finance_type"`
	CashierDeskType     fwe_trade_common.CashierDeskType      `json:"cashier_desk_type"`
	OsType              *fwe_trade_common.OSType              `json:"os_type"`
	PayOrderNo          *string                               `json:"pay_order_no"`
	RedirectUrl         *string                               `json:"redirect_url"`
	IpAddress           *string                               `json:"ip_address"`
	ExpireTime          *int64                                `json:"expire_time"`
	PayLimitList        []fwe_trade_common.PayLimitType       `json:"pay_limit_list"`
	Operator            *fwe_trade_common.OperatorInfo        `json:"operator"`
	Amount              int64                                 `json:"amount"`
	PosSellerUid        *string                               `json:"pos_seller_uid"`
	PayOfflineCheckData *fwe_trade_common.PayOfflineCheckData `json:"pay_offline_check_data"`
}

type CreateCashPayResult struct {
	PayData    string `json:"pay_data"`
	PayOrderNo string `json:"pay_order_no"`
}

type OrderPayDetailModel struct {
	Identity    *fwe_trade_common.BizIdentity  `json:"identity"`
	OrderId     string                         `json:"order_id"`
	FinanceType int32                          `json:"finance_type"`
	Operator    *fwe_trade_common.OperatorInfo `json:"operator"`
}

type OrderPayDetailResult struct {
	Identity     *fwe_trade_common.BizIdentity `json:"identity"`
	OrderId      string                        `json:"order_id"`
	TotalAmount  int64                         `json:"total_amount"`
	PayedAmount  int64                         `json:"payed_amount"`
	PayingAmount int64                         `json:"paying_amount"`
	PayList      []*PayDetail                  `json:"pay_list"`
}

type PayDetail struct {
	PayOrderNo   string                        `json:"pay_order_no"`
	PayAmount    int64                         `json:"pay_amount"`
	Status       fwe_trade_common.CommonStatus `json:"status"`
	PayStartTime string                        `json:"pay_start_time"`
	PayEndTime   string                        `json:"pay_end_time"`
}

type SignContractModel struct {
	OrderTagModel
	Identity      *fwe_trade_common.BizIdentity         `json:"identity"`
	OrderId       string                                `json:"order_id"`
	ContractType  int32                                 `json:"contract_type"`
	SignPartyList []*core.SignPartyData                 `json:"sign_party_list"`
	ContParams    map[string]string                     `json:"cont_params"`
	SmsConfigList map[core.SignPosition]*core.SmsConfig `json:"sms_config_list"`
	ContTmplID    int64                                 `json:"cont_tmpl_id"`
	Operator      *fwe_trade_common.OperatorInfo        `json:"operator"`
}

type SignContractResult struct {
	ContSerial string `json:"cont_serial"`
}

type FinanceInfo struct {
	FinanceType int32                       `json:"finance_type"`
	Amount      int64                       `json:"amount"`
	FeeItemList []*fwe_trade_common.FeeItem `json:"fee_item_list"`
}

type OrderFinanceInfo struct {
	TradeType   fwe_trade_common.TradeType `json:"trade_type"`
	FinanceList []*FinanceInfo             `json:"finance_list"`
	TotalAmount *int64                     `json:"total_amount"`
}

type ConfirmOrderModel struct {
	OrderTagModel
	OrderFinanceInfo *OrderFinanceInfo                  `json:"order_finance_info"`
	ProductInfo      *fwe_trade_common.ProductInfo      `json:"product_info"`
	BuyerInfo        *fwe_trade_common.TradeSubjectInfo `json:"buyer_info"`
}

type LoanTypeSelectModel struct {
	OrderTagModel
	LoanType int32 `json:"loan_type"`
}

type LoanBankInfo struct {
	AccountName string `json:"account_name"`
	BranchName  string `json:"branch_name"`
	AccountNo   string `json:"account_no"`
}

type LoanConfirmModel struct {
	OrderTagModel
	FweAccountId string        `json:"fwe_account_id"` // 四轮商户id
	ShopId       string        `json:"shop_id"`        // 门店id
	ShopName     string        `json:"shop_name"`      // 门店名称
	CarVin       string        `json:"car_vin"`        // 车辆vin码
	BorrowerName string        `json:"borrower_name"`  // 借款人
	Amount       int64         `json:"amount"`         // 放款金额
	FinanceName  string        `json:"finance_name"`   // 资方名称
	OutBankInfo  *LoanBankInfo `json:"out_bank_info"`  // 出款银行信息
	BizOrderID   string        `json:"biz_order_id"`   // 业务订单号
}

type OrderLoanCallbackModel struct {
	// 订单贷走分账时，有如下参数
	Status int32 `json:"status"`
	Amount int64 `json:"amount"`

	// 备选方案，订单贷不走分账时有如下参数
	RealAmount      string `json:"real_amount"`
	RealDate        string `json:"real_date"`
	RepaymentAmount string `json:"repayment_amount"`
	RepaymentDate   string `json:"repayment_date"`
}

type LoanOverModel struct {
}

type LoanApproveSuccModel struct {
}

type LoanApproveFailModel struct {
}

/**
  3: required string online_sale
  4: required string car_source_bd
  5: optional map<string, string> order_tag
*/

type LaunchOrderLoanModel struct {
	OrderTagModel
	OnlineSale  string `json:"online_sale"`
	CarSourceBD string `json:"car_source_bd"`
}

type TransferMoneyApprovePassModel struct {
	OrderTagModel
	OrderID     string `json:"order_id"`     // 订单id
	FinanceType int32  `json:"finance_type"` // 资金类型
	Amount      int64  `json:"amount"`       // 金额
}

type SetWithdrawBankcardModel struct {
	OrderTagModel
	FweAccountID string                     `json:"fwe_account_id,omitempty"`
	BankCardInfo *fwe_trade_common.BankInfo `json:"bank_card_info"`
}
