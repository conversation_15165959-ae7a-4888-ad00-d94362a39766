package new_car_ecom_model

import "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"

type UpdateOrderParam struct {
	OrderName   *string                            `json:"order_name"`
	OrderDesc   *string                            `json:"order_desc"`
	TotalAmount *int64                             `json:"total_amount"`
	ProductInfo *fwe_trade_common.ProductInfo      `json:"product_info"`
	BuyerInfo   *fwe_trade_common.TradeSubjectInfo `json:"buyer_info"`
	SellerInfo  *fwe_trade_common.TradeSubjectInfo `json:"seller_info"`
	Tag         map[string]string                  `json:"tag"`
	Extra       map[string]string                  `json:"extra"`
	Operator    *fwe_trade_common.OperatorInfo     `json:"operator"`
	FinanceList []*fwe_trade_common.FinanceInfo    `json:"finance_list"`
}
