package o2o_ecom_model

import (
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UpdateInstallmentModel struct {
	FinalFinanceInfo *fwe_trade_common.FinanceInfo `json:"final_finance_info"`
	ProductExtra     *string                       `json:"product_extra"`
	OrderTag         map[string]string             `json:"order_tag"`
	Extra            map[string]string             `json:"extra"`
}

type PayModel struct {
	FinalFinanceInfo *fwe_trade_common.FinanceInfo `json:"final_finance_info"`
	UnionPayReq      *execution_common.UnionPayReq `json:"union_pay_req"`
}
