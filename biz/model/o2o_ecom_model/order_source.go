package o2o_ecom_model

type OrderSourceInfo struct {
	AnchorUID    int64             `json:"anchor_uid"`
	AppID        int32             `json:"app_id"`
	SessionAppID int32             `json:"session_app_id"`
	SourceType   int32             `json:"source_type"`
	ItemID       string            `json:"item_id"`
	LinkSource   string            `json:"link_source"`
	RoomID       string            `json:"room_id"`
	AdLink       string            `json:"ad_link"`
	PublicParams map[string]string `json:"public_params"`
}
