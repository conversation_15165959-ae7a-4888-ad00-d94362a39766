package service_model

import (
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UnionRefundParam struct {
	OrderID        string
	OrderName      string
	RefundType     int32
	RefundAmount   int64
	FeeRecordID    string
	MergeRefundReq *payment.MergeRefundReq
	DeductItemList []*fwe_trade_common.FeeItem
	FeeItemList    []*fwe_trade_common.FeeItem
}

type UnionSettleParam struct {
	OrderID        string
	OrderName      string
	SettleType     int32
	SettleAmount   int64
	FeeRecordID    string
	MergeSettleReq *payment.MergeSettleV2Req
}

type UnionWithdrawParam struct {
	OrderID        string
	OrderName      string
	FinanceType    int32
	WithdrawAmount int64
	FeeItemList    []*fwe_trade_common.FeeItem
	WithdrawReq    *payment.WithdrawDepositReq
}

type UnionRefundAfterSettleParam struct {
	OrderID        string
	OrderName      string
	RefundType     int32
	RefundAmount   int64
	FeeRecordID    string
	Req            *payment.MergeRefundAfterSettleReq
	DeductItemList []*fwe_trade_common.FeeItem
	FeeItemList    []*fwe_trade_common.FeeItem
}
