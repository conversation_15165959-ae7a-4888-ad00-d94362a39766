package service_model

type EBSCustomer struct {
	SourceId       string `json:"sourceId"` // sourceId
	CustomerNo     string `json:"value1"`   // 客户编码
	CustomerName   string `json:"value2"`   // 客户名称
	CustomerType   string `json:"value3"`   // 客户类型
	CustomerStatus string `json:"value4"`   // 客户状态
	ExpireDate     string `json:"value5"`   // 失效日期
	Version        string `json:"value6"`   // 版本号
	Company        string `json:"value7"`   // 所属公司
	CustomerOwner  string `json:"value8"`   // 客户归属
}

type EBSCustomerPushReq struct {
	DataCount int            `json:"dataCount"`
	DataList  []*EBSCustomer `json:"datas"`
}

type EBSCustomerPushResp struct {
	Message string `json:"message"`
	Status  string `json:"status"`
	Data    []struct {
		SourceId string `json:"sourceId"`
		Status   string `json:"status"`
		Message  string `json:"message"`
	} `json:"data"`
}
