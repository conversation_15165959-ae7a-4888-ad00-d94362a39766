package service_model

import (
	"time"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type Order struct {
	FweOrder            *db_model.FweOrder               `json:"fwe_order"`             // 订单主表
	FinanceList         []*db_model.FFinanceOrder        `json:"finance_list"`          // 资金单
	RefundFinanceList   []*db_model.FFinanceOrder        `json:"refund_finance_list"`   // 退款资金单
	SettleFinanceList   []*db_model.FFinanceOrder        `json:"settle_finance_list"`   // 分账资金单
	WithdrawFinanceList []*db_model.FFinanceOrder        `json:"withdraw_finance_list"` // 出款资金单
	TransferFinanceList []*db_model.FFinanceOrder        `json:"transfer_finance_list"` // 转账资金单
	TagMap              map[string]string                `json:"tag_map"`               // 标签kv
	ContList            []*db_model.FweOrderContract     `json:"cont_list"`             // 合同
	BizExtra            map[string]string                `json:"biz_extra"`             // 业务透传字段
	TradeSplitInfo      *fwe_trade_common.TradeSpiltInfo `json:"trade_split_info"`      // 分账信息
	SettleFList         []*db_model.FSettleFinanceOrder  `json:"settle_f_list"`         // 分账列表
	RefundFList         []*db_model.FRefundFinanceOrder  `json:"refund_f_list"`         // 退款列表
}

type UpdateOrderParams struct {
	// where
	WhereOrderStatus []int32 `json:"where_order_status,omitempty"`
	// update
	UpdateOrderStatus      *int32     `json:"update_order_status"`     // 订单状态
	UpdateBeforeStatus     *int32     `json:"update_before_status"`    // 订单前一个状态
	UpdateOrderSubStatus   *string    `json:"update_order_sub_status"` // 订单子状态
	UpdateFinishTime       *time.Time `json:"update_finish_time"`      // 订单完成时间
	UpdateOrderName        *string    `json:"update_order_name"`       // 订单名称
	UpdateOrderDesc        *string    `json:"update_order_desc"`
	UpdateTotalAmount      *int64     `json:"update_total_amount"`
	UpdateTotalPayAmount   *int64     `json:"total_pay_amount"` // 总支付金额 ，单位分
	UpdateBuyerID          *string    `json:"update_buyer_id"`
	UpdateBuyerExtra       *string    `json:"update_buyer_extra"`
	UpdateSellerID         *string    `json:"update_seller_id"`
	UpdateSellerExtra      *string    `json:"update_seller_extra"`
	UpdateSProviderID      *string    `json:"update_s_provider_id"`
	UpdateSProviderExtra   *string    `json:"update_s_provider_extra"`
	UpdateTalentID         *string    `json:"update_talent_id"`
	UpdateTalentExtra      *string    `json:"update_talent_extra"`
	UpdateProductID        *string    `json:"update_product_id"`
	UpdateProductType      *int32     `json:"update_product_type"`
	UpdateProductName      *string    `json:"update_product_name"`
	UpdateProductExtra     *string    `json:"update_product_extra"`
	UpdateSkuID            *string    `json:"update_sku_id"`
	UpdateSkuVersion       *int64     `json:"update_sku_version"`
	UpdateProductVersion   *int64     `json:"update_product_version"`
	UpdateProductQuantity  *int64     `json:"update_product_quantity"`
	UpdateProductUnitPrice *int64     `json:"update_product_unit_price"`
	UpdateTradeType        *int32     `json:"update_trade_type"`
	UpdateProductDetail    *string    `json:"update_product_detail"`
	UpdatePurchasePlan     *string    `json:"update_purchase_plan"`
	// operator
	Operator *fwe_trade_common.OperatorInfo `json:"operator"`
}

type UpdateFinanceParams struct {
	// update
	UpdateAmount                  *int64                                    `json:"update_amount"`
	UpdateLoanAmount              *int64                                    `json:"update_loan_amount"`
	UpdateFinanceStatus           *int32                                    `json:"update_finance_status"`
	UpdateTradeType               *string                                   `json:"update_trade_type"`
	UpdateMerchantID              *string                                   `json:"update_merchant_id"`
	UpdateAppID                   *string                                   `json:"update_app_id"`
	UpdateMid                     *string                                   `json:"update_mid"`
	UpdateFeeItemDetail           *string                                   `json:"update_fee_item_detail"`
	UpdateDeductFeeItemDetail     *string                                   `json:"update_deduct_fee_item_detail"`
	UpdateProcessAmount           *int64                                    `json:"update_process_amount"`
	UpdateFinishTime              *int64                                    `json:"update_finish_time"`
	UpdateOfflineLoanAmount       *int64                                    `json:"update_offline_loan_amount"`
	UpdatePlatformPromotionAmount *int64                                    `json:"update_platform_promotion_amount"`
	UpdatePlatformPromotionDetail *fwe_trade_common.PlatformPromotionDetail `json:"update_platform_promotion_detail"`
}

type OrderBaseParam struct {
	Identity  *fwe_trade_common.BizIdentity
	OrderID   string
	FulfillID string
	OrderName string
}
