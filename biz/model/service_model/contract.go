package service_model

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model/car_supply_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
)

type UnionContCreateReq struct {
	TenantType         int32                                 // 租户
	BizScene           int32                                 // 场景号
	OrderID            string                                // 订单号
	ContType           int32                                 // 合同阶段
	TmplID             int64                                 // 合同模版ID
	TmplParams         map[string]string                     // 合同模版参数
	SignPartyMap       map[core.SignPosition]*core.SignParty // 签约人
	InOutData          *core.InOutData                       // 合同收支数据
	CallbackAction     string                                // 回调事件
	CallbackExtra      string                                // 回调扩展
	ContExtra          string                                // 合同扩展
	Operator           *core.Operator                        // 操作人
	IsTest             bool                                  // 是否测试
	RelatedContSerials []string                              // 关联合同列表
}

type UnionContCreateRsp struct {
	ContSerial string
	SignLinks  map[core.SignPosition]string
}

type ContractCreateParam struct {
	OrderID        string
	TenantType     int32
	BizScene       int32
	ContType       int32
	OperatorID     int64
	OperatorName   string
	TmplID         int64
	NeedSignNoCert bool
	SmsTmplID      int64
	SmsChannelID   int32
	TmplParams     map[string]string
	SignPartList   []*SignPart
	InOutData      *InOutData
	CallbackEvent  string
	CallbackExtra  string
	ReturnUrl      string

	SignPartyDataList []*core.SignPartyData
	SmsConfigMap      map[core.SignPosition]*core.SmsConfig
}

type ContractCancelParam struct {
	TenantType int32
	BizScene   int32

	OrderID  string
	ContType int32

	Operator *core.Operator
}

type SignPart struct {
	SignPosition int32
	IsInner      bool
	CardType     int32
	IdentName    string
	IdentID      string
	SignerName   string
	SignerPhone  string
}

type InOutData struct {
	Currency int32
	TotalOut int64
	TotalIn  int64
}

type ContractCreateParamForNotInner struct {
	OrderID       string
	TenantType    int32
	BizScene      int32
	ContType      int32
	OperatorID    int64
	OperatorName  string
	TmplID        int64
	TmplParams    map[string]string
	SignPartList  []*SignPart
	InOutData     *InOutData
	CallbackEvent string
	CallbackExtra string
	ReturnUrl     string

	SignPartyInfoMap map[core.SignPosition]*car_supply_model.SignPartyInfo
}
