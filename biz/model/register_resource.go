package model

import (
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/resource_compensate"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type OrderResource struct {
	OrderID         string                           `json:"order_id"`
	OrderType       fwe_trade_common.OrderType       `json:"order_type"`
	ResourceType    resource_compensate.ResourceType `json:"resource_type"`
	ResourceSubType int32                            `json:"resource_sub_type"`
	OutIDInfo       map[string]int32                 `json:"out_id_info"`
	Extra           map[string]string                `json:"extra"`
}
