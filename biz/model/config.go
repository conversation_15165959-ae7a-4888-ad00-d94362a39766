package model

type BaseConfig struct {
	AppID        string `json:"app_id"`
	MerchantID   string `json:"merchant_id"`
	MID          string `json:"mid"`
	MerchantName string `json:"merchant_name"`
}

type ConfigWithFinance struct {
	*BaseConfig
	PosMerchantID        string           `json:"pos_merchant_id"`
	GuaranteeMerchantID  string           `json:"guarantee_merchant_id"`
	FinanceTradeTypeMap  map[int32]string `json:"finance_trade_type_map"`
	CheckContStructField bool             `json:"check_cont_struct_field"`
}

//MerchantBase 支持 cashType 选择appid
type MerchantBase struct {
	MerchantID  string   `json:"merchant_id"`
	AppID       string   `json:"app_id"`
	TradeType   string   `json:"trade_type"`
	CashierType []string `json:"cashier_type"`
}

// CreateOrderResult .
type CreateOrderResult struct {
	OrderID     string `json:"order_id"`
	BizResponse string `json:"biz_response"`
}
