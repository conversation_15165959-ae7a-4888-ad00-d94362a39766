package sh_sell_model

import (
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

const (
	CarSourcesIDTag = "car_source_id"
	LoanAmountTag   = "loan_amount"
)

type TransferOwnerModel struct{}

type DeliveryModel struct{}

type CancelModel struct{}

type CreateCashPayModel struct {
	Identity        *fwe_trade_common.BizIdentity    `json:"identity"`
	OrderId         string                           `json:"order_id"`
	FinanceType     int32                            `json:"finance_type"`
	CashierDeskType fwe_trade_common.CashierDeskType `json:"cashier_desk_type"`
	OsType          *fwe_trade_common.OSType         `json:"os_type"`
	PayOrderNo      *string                          `json:"pay_order_no"`
	RedirectUrl     *string                          `json:"redirect_url"`
	IpAddress       *string                          `json:"ip_address"`
	ExpireTime      *int64                           `json:"expire_time"`
	PayLimitList    []fwe_trade_common.PayLimitType  `json:"pay_limit_list"`
	Operator        *fwe_trade_common.OperatorInfo   `json:"operator"`
}
type CreateCashPayResult struct {
	PayData    string `json:"pay_data"`
	PayOrderNo string `json:"pay_order_no"`
}

type CreatePOSPayModel struct {
	Identity    *fwe_trade_common.BizIdentity  `json:"identity"`
	OrderId     string                         `json:"order_id"`
	FinanceType int32                          `json:"finance_type"`
	Amount      int64                          `json:"amount"`
	PayOrderNo  *string                        `json:"pay_order_no"`
	IpAddress   *string                        `json:"ip_address"`
	ExpireTime  *int64                         `json:"expire_time"`
	Operator    *fwe_trade_common.OperatorInfo `json:"operator"`
}
type CreateUnionPayModel struct {
	Identity    *fwe_trade_common.BizIdentity `json:"identity"`
	OrderId     string                        `json:"order_id"`
	FinanceType int32                         `json:"finance_type"`
	TradeType   string                        `json:"trade_type"`
}

type CreateOfflinePayModel struct {
	Identity    *fwe_trade_common.BizIdentity         `json:"identity"`
	OrderId     string                                `json:"order_id"`
	FinanceType int32                                 `json:"finance_type"`
	Amount      int64                                 `json:"amount"`
	PayOrderNo  *string                               `json:"pay_order_no,omitempty"`
	IpAddress   *string                               `json:"ip_address,omitempty"`
	TradeType   string                                `json:"trade_type"`
	CheckData   *fwe_trade_common.PayOfflineCheckData `json:"check_data"`
	Operator    *fwe_trade_common.OperatorInfo        `json:"operator"`
}

type CreatePOSPayResult struct {
	PayData    string `json:"pay_data"`
	PayOrderNo string `json:"pay_order_no"`
}

type OrderPayDetailModel struct {
	Identity    *fwe_trade_common.BizIdentity  `json:"identity"`
	OrderId     string                         `json:"order_id"`
	FinanceType int32                          `json:"finance_type"`
	Operator    *fwe_trade_common.OperatorInfo `json:"operator"`
}
type OrderPayDetailResult struct {
	Identity     *fwe_trade_common.BizIdentity `json:"identity"`
	OrderId      string                        `json:"order_id"`
	TotalAmount  int64                         `json:"total_amount"`
	PayedAmount  int64                         `json:"payed_amount"`
	PayingAmount int64                         `json:"paying_amount"`
	PayList      []*PayDetail                  `json:"pay_list"`
}
type PayDetail struct {
	PayOrderNo   string                        `json:"pay_order_no"`
	PayAmount    int64                         `json:"pay_amount"`
	Status       fwe_trade_common.CommonStatus `json:"status"`
	PayStartTime string                        `json:"pay_start_time"`
	PayEndTime   string                        `json:"pay_end_time"`
}

type SignContractModel struct {
	Identity     *fwe_trade_common.BizIdentity  `json:"identity"`
	OrderId      string                         `json:"order_id"`
	ContractType int32                          `json:"contract_type"`
	SignerName   string                         `json:"signer_name"`
	SignerPhone  string                         `json:"signer_phone"`
	ContParams   map[string]string              `json:"cont_params"`
	Operator     *fwe_trade_common.OperatorInfo `json:"operator"`
	TmplId       int64                          `json:"tmpl_id"`
}
type SignContractResult struct {
	ContSerial string `json:"cont_serial"`
}

type LoanTypeSelectModel struct {
	LoanType int32 `json:"loan_type"`
}

type TransferOwnerTypeSelectModel struct {
	TransferOwnerType            string `json:"transfer_owner_type"`
	TransferOwnerGuaranteeAmount int64  `json:"transfer_owner_guarantee_amount"`
}
type StartRemoteDeliveryCarModel struct {
}
type StartDeliveryCarProcessModel struct {
}
type ConfirmLoanOverModel struct {
}
type UpdateLoanSubStatusModel struct {
	LoanSubStatus string `json:"loan_sub_status"`
}

type RefundTransferOwnerGuaranteeModel struct {
	RefundAmount int64 `json:"refund_amount"`
}

type LoanBankInfo struct {
	AccountName string `json:"account_name"`
	BranchName  string `json:"branch_name"`
	AccountNo   string `json:"account_no"`
}
type LoanConfirmModel struct {
	FweAccountId string        `json:"fwe_account_id"` // 四轮商户id
	ShopId       string        `json:"shop_id"`        // 门店id
	ShopName     string        `json:"shop_name"`      // 门店名称
	CarVin       string        `json:"car_vin"`        // 车辆vin码
	BorrowerName string        `json:"borrower_name"`  // 借款人
	Amount       int64         `json:"amount"`         // 放款金额
	FinanceName  string        `json:"finance_name"`   // 资方名称
	OutBankInfo  *LoanBankInfo `json:"out_bank_info"`  // 出款银行信息
	BizOrderID   string        `json:"biz_order_id"`   // 业务订单号
}

type LoanOverModel struct {
}

type LoanApproveSuccModel struct {
}

type LoanApproveFailModel struct {
}

type RevokeContSignParam struct {
	ContractType int32             `json:"contract_type"`
	ContParams   map[string]string `json:"cont_params"`
	RedirectUrl  string            `json:"redirect_url"`
	SignerName   string            `json:"signer_name"`
	SignerPhone  string            `json:"signer_phone"`
	CompanyName  string            `json:"company_name"`
	CreditCode   string            `json:"credit_code"`
}

type SingleRefund struct {
	FinanceOrderType int32 `thrift:"finance_order_type,1" json:"finance_order_type"`
	Amount           int64 `thrift:"amount,2" json:"amount"`
}

type RefundReq struct {
	RefundList     []*SingleRefund   `thrift:"refund_list,1" json:"refund_list"`
	RefundType     int32             `thrift:"refund_type,2" json:"refund_type"`
	Reason         string            `thrift:"reason,10" json:"reason"`
	IpAddress      string            `thrift:"ip_address,11" json:"ip_address"`
	CallbackAction string            `thrift:"callback_action,100" json:"callback_action"`
	Tag            map[string]string `thrift:"tag,200" json:"tag"`
}
