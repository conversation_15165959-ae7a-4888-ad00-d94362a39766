package sh_sell_model

import "code.byted.org/motor/fwe_trade_engine/biz/model"

type Conf struct {
	NormalPayMerchant                        *MerchantInfo            `json:"normal_pay_merchant"`        // 代收代付财经商户信息, key: fwe_account_id 四轮商户id
	POSPayMerchant                           *MerchantInfo            `json:"pos_pay_merchant"`           // POS财经商户信息, key: fwe_account_id 四轮商户id
	YZTPayMerchant                           *MerchantInfo            `json:"yzt_pay_merchant,omitempty"` // 云直通财经一级商户信息
	ContInfoMap                              map[string]*ContInfo     `json:"cont_info_map"`              // 合同信息，key: 合同类型 intent、sell、insurance
	CreditInfo                               map[string]*CreditInfo   `json:"credit_info"`                // 四轮商户公司信息，key: fwe_account_id 四轮商户id
	ApproveStarter                           *ApproveStarter          `json:"approve_starter"`            // 审批发起人
	AgreementMerchant                        *AgreementMerchantInfo   `json:"agreement_merchant"`         // 协议扣款商户信息
	ConfigWithFinance                        *model.ConfigWithFinance `json:"config_with_finance"`        // 资金单配置
	CheckContStructField                     bool                     `json:"check_cont_struct_field"`    //校验合同结构化字段
	CheckContOptionAllowIncomeAmountOverflow bool                     `json:"check_cont_option_allow_income_amount_overflow"`
	TestOrderLimitAmount                     *int64                   `json:"test_order_limit_amount,omitempty"` // 测试订单限制金额
}

type MerchantInfo struct {
	AppID         string `json:"app_id"`
	MerchantID    string `json:"merchant_id"`
	MerchantName  string `json:"merchant_name"`
	ReceiveUID    string `json:"receive_uid"`
	FcType        string `json:"fc_type"`
	FcSceneCode   int64  `json:"fc_scene_code"`
	SettleUID     string `json:"settle_uid"`
	SettleUIDType int32  `json:"settle_uid_type"`
}

type ContInfo struct {
	TmplID     int64 `json:"tmpl_id"`
	SmsTmplID  int64 `json:"sms_tmpl_id"`
	SmsChannel int32 `json:"sms_channel"`
}

type CreditInfo struct {
	CreditCode string `json:"credit_code"`
	Name       string `json:"name"`
}

type ApproveStarter struct {
	Name        string `json:"name"`
	OpenID      string `json:"open_id"`
	ApproveCode string `json:"approve_code"`
}

type AgreementMerchantInfo struct {
	AppID            string `json:"app_id"`
	AppName          string `json:"app_name"`
	MerchantID       string `json:"merchant_id"`
	MerchantName     string `json:"merchant_name"`
	Uid              string `json:"uid"`
	UidType          int32  `json:"uid_type"`
	CardNo           string `json:"card_no"`
	InnerAgreementNo string `json:"inner_agreement_no"`
	AccountProp      string `json:"account_prop"`
}
