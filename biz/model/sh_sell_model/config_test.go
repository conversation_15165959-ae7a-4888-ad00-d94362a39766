package sh_sell_model

import (
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/gopkg/tools"
	"testing"
)

func TestGenConfig(t *testing.T) {

	// boe 环境

	// "credit_code": "91110108MA01R70K8D",
	// "name": "北京火山引擎科技有限公司"

	boeConfig := &Conf{
		NormalPayMerchant: &MerchantInfo{
			AppID:        "************",
			MerchantID:   "6200002101",
			MerchantName: "懂车帝二手支付-测试",
			ReceiveUID:   "",
		},
		ContInfoMap: map[string]*ContInfo{
			consts.SHSellRevokeCont.Name(): {
				TmplID:     2206,
				SmsTmplID:  13271,
				SmsChannel: 0,
			},
		},
		CreditInfo: map[string]*CreditInfo{
			"platform": {
				CreditCode: "91110108MA01R70K8D",
				Name:       "北京火山引擎科技有限公司",
			},
		},
	}

	// prod 环境
	// "credit_code": "91500105MA61CC1Q62",
	// "name": "重庆空间变换科技有限公司"
	prodConfig := &Conf{
		NormalPayMerchant: &MerchantInfo{
			AppID:        "************",
			MerchantID:   "1200009448",
			MerchantName: "懂车帝二手车付款",
			ReceiveUID:   "",
		},
		ContInfoMap: map[string]*ContInfo{
			consts.SHSellRevokeCont.Name(): {
				TmplID:     26,
				SmsTmplID:  30829,
				SmsChannel: 1740,
			},
		},

		CreditInfo: map[string]*CreditInfo{
			"platform": {
				CreditCode: "91500105MA61CC1Q62",
				Name:       "重庆空间变换科技有限公司",
			},
		},
	}
	t.Log(tools.GetLogStr(boeConfig), tools.GetLogStr(prodConfig))
}
