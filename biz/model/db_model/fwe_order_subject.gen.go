// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

import (
	"time"
)

const TableNameFweOrderSubject = "fwe_order_subject"

// FweOrderSubject 基建订单主体表
type FweOrderSubject struct {
	ID          int64      `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                                                 // 主键id
	SubjectID   string     `gorm:"column:subject_id;type:varchar(255);not null;uniqueIndex:idx_subject_id,priority:1;comment:订单表上buyer_id/seller_id" json:"subject_id"` // 订单表上buyer_id/seller_id
	SubjectKey  string     `gorm:"column:subject_key;type:varchar(32);not null;comment:主体key最长30" json:"subject_key"`                                                   // 主体key最长30
	SubjectName string     `gorm:"column:subject_name;type:varchar(255);not null;comment:主体名称" json:"subject_name"`                                                     // 主体名称
	UID         string     `gorm:"column:uid;type:varchar(255);not null;comment:和财经交互的uid" json:"uid"`                                                                  // 和财经交互的uid
	EbsKey      string     `gorm:"column:ebs_key;type:varchar(255);not null;comment:ebsKey， ATEG_前缀" json:"ebs_key"`                                                    // ebsKey， ATEG_前缀
	CreateTime  *time.Time `gorm:"column:create_time;type:datetime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                                          // 创建时间
	UpdateTime  *time.Time `gorm:"column:update_time;type:datetime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                          // 更新时间
}

// TableName FweOrderSubject's table name
func (*FweOrderSubject) TableName() string {
	return TableNameFweOrderSubject
}
