// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

import (
	"time"
)

const TableNameFRefundFinanceOrder = "f_refund_finance_order"

// FRefundFinanceOrder 退款资金单表
type FRefundFinanceOrder struct {
	ID                     int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                                                                                  // 主键id
	TenantType             int32     `gorm:"column:tenant_type;type:int;not null;comment:租户类型" json:"tenant_type"`                                                                                        // 租户类型
	BizScene               int32     `gorm:"column:biz_scene;type:int;not null;comment:业务场景" json:"biz_scene"`                                                                                            // 业务场景
	RefundFinanceOrderID   string    `gorm:"column:refund_finance_order_id;type:varchar(255);not null;uniqueIndex:uniq_refund_finance_order_id,priority:1;comment:退款资金单号" json:"refund_finance_order_id"` // 退款资金单号
	RefundFinanceOrderType int32     `gorm:"column:refund_finance_order_type;type:int;not null;comment:退款阶段，根绝bizScene自定义" json:"refund_finance_order_type"`                                              // 退款阶段，根绝bizScene自定义
	OutID                  string    `gorm:"column:out_id;type:varchar(255);not null;index:idx_out_id,priority:1;comment:外部id，幂等用" json:"out_id"`                                                         // 外部id，幂等用
	OrderType              int32     `gorm:"column:order_type;type:tinyint;not null;comment:订单类型，冗余存储" json:"order_type"`                                                                                 // 订单类型，冗余存储
	OrderID                string    `gorm:"column:order_id;type:varchar(255);not null;index:idx_order_id,priority:1;comment:外部订单号" json:"order_id"`                                                      // 外部订单号
	OrderName              string    `gorm:"column:order_name;type:varchar(255);not null;comment:外部订单名称" json:"order_name"`                                                                               // 外部订单名称
	Reason                 string    `gorm:"column:reason;type:varchar(255);not null;comment:退款原因" json:"reason"`                                                                                         // 退款原因
	RefundList             *string   `gorm:"column:refund_list;type:json;comment:退款组成" json:"refund_list"`                                                                                                // 退款组成
	Amount                 int64     `gorm:"column:amount;type:bigint;not null;comment:退款金额，单位：分" json:"amount"`                                                                                          // 退款金额，单位：分
	ProcessAmount          int64     `gorm:"column:process_amount;type:bigint;not null;comment:进行中的金额，单位：分" json:"process_amount"`                                                                        // 进行中的金额，单位：分
	Status                 int32     `gorm:"column:status;type:tinyint;not null;comment:状态" json:"status"`                                                                                                // 状态
	CallbackEvent          string    `gorm:"column:callback_event;type:varchar(255);not null;comment:回调事件" json:"callback_event"`                                                                         // 回调事件
	CalllbackExtra         *string   `gorm:"column:calllback_extra;type:text;comment:回调参数" json:"calllback_extra"`                                                                                        // 回调参数
	FinishTime             time.Time `gorm:"column:finish_time;type:datetime;not null;default:1970-01-01 08:00:00;comment:完成时间" json:"finish_time"`                                                       // 完成时间
	CreateTime             time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                                                         // 创建时间
	UpdateTime             time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                                         // 更新时间
}

// TableName FRefundFinanceOrder's table name
func (*FRefundFinanceOrder) TableName() string {
	return TableNameFRefundFinanceOrder
}
