// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

import (
	"time"
)

const TableNameFFinanceOrder = "f_finance_order"

// FFinanceOrder 资金单表
type FFinanceOrder struct {
	ID             int64  `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                                                  // 主键id
	FinanceOrderID string `gorm:"column:finance_order_id;type:varchar(255);not null;uniqueIndex:uniq_finance_order_id,priority:1;comment:资金单号" json:"finance_order_id"` // 资金单号
	/*
		资金单类型 根据biz_scene自定义
		新车门店：1返佣 2小订 3大定 4尾款
	*/
	FinanceOrderType        int32     `gorm:"column:finance_order_type;type:int;not null;comment:资金单类型 根据biz_scene自定义\n新车门店：1返佣 2小订 3大定 4尾款" json:"finance_order_type"`
	OrderID                 string    `gorm:"column:order_id;type:varchar(255);not null;index:idx_order_id,priority:1;comment:外部订单号" json:"order_id"`       // 外部订单号
	FulfillID               string    `gorm:"column:fulfill_id;type:varchar(255);not null;index:idx_fulfill,priority:1;comment:履约单号" json:"fulfill_id"`     // 履约单号
	OrderName               string    `gorm:"column:order_name;type:varchar(255);not null;comment:外部订单名称" json:"order_name"`                                // 外部订单名称
	TradeType               string    `gorm:"column:trade_type;type:varchar(255);not null;comment:交易模式" json:"trade_type"`                                  // 交易模式
	TradeCategory           int32     `gorm:"column:trade_category;type:int;not null;comment:交易分类： 1-支付 2-分账 3-退款 4-出款 6-转账" json:"trade_category"`         // 交易分类： 1-支付 2-分账 3-退款 4-出款 6-转账
	TenantType              int32     `gorm:"column:tenant_type;type:int;not null;comment:租户类型" json:"tenant_type"`                                         // 租户类型
	BizScene                int32     `gorm:"column:biz_scene;type:int;not null;comment:业务场景" json:"biz_scene"`                                             // 业务场景
	AppID                   string    `gorm:"column:app_id;type:varchar(255);not null;comment:财经app_id" json:"app_id"`                                      // 财经app_id
	MerchantID              string    `gorm:"column:merchant_id;type:varchar(255);not null;comment:一级商户id" json:"merchant_id"`                              // 一级商户id
	Mid                     string    `gorm:"column:mid;type:varchar(255);not null;comment:二级商户id" json:"mid"`                                              // 二级商户id
	Amount                  int64     `gorm:"column:amount;type:bigint;not null;comment:金额，单位分" json:"amount"`                                              // 金额，单位分
	LoanAmount              int64     `gorm:"column:loan_amount;type:bigint;not null;comment:贷款金额，单位分" json:"loan_amount"`                                  // 贷款金额，单位分
	ProcessAmount           int64     `gorm:"column:process_amount;type:bigint;not null;comment:进行中的金额，单位分" json:"process_amount"`                          // 进行中的金额，单位分
	OfflineLoanAmount       int64     `gorm:"column:offline_loan_amount;type:bigint;not null;comment:线下贷款金额" json:"offline_loan_amount"`                    // 线下贷款金额
	PlatformPromotionAmount int64     `gorm:"column:platform_promotion_amount;type:bigint;not null;comment:平台优惠总金额，线上资金流" json:"platform_promotion_amount"` // 平台优惠总金额，线上资金流
	PlatformPromotionDetail *string   `gorm:"column:platform_promotion_detail;type:json;comment:平台优惠详情" json:"platform_promotion_detail"`                   // 平台优惠详情
	Status                  int32     `gorm:"column:status;type:tinyint;not null;comment:状态 1未处理 2处理中 3完成 4失败 5关闭" json:"status"`                           // 状态 1未处理 2处理中 3完成 4失败 5关闭
	FeeItemDetail           *string   `gorm:"column:fee_item_detail;type:text;comment:支付计费详情" json:"fee_item_detail"`                                       // 支付计费详情
	DeductItemDetail        *string   `gorm:"column:deduct_item_detail;type:text;comment:扣除费项详情" json:"deduct_item_detail"`                                 // 扣除费项详情
	FeeRecordID             string    `gorm:"column:fee_record_id;type:varchar(255);not null;comment:支付计费记录id" json:"fee_record_id"`                        // 支付计费记录id
	CallbackEvent           string    `gorm:"column:callback_event;type:varchar(128);not null;comment:回调事件" json:"callback_event"`                          // 回调事件
	CallbackExtra           *string   `gorm:"column:callback_extra;type:text;comment:回调透传参数" json:"callback_extra"`                                         // 回调透传参数
	FinishTime              time.Time `gorm:"column:finish_time;type:datetime;not null;default:1970-01-01 08:00:00;comment:资金单结束时间" json:"finish_time"`     // 资金单结束时间
	CreateTime              time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`          // 创建时间
	UpdateTime              time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`          // 更新时间
}

// TableName FFinanceOrder's table name
func (*FFinanceOrder) TableName() string {
	return TableNameFFinanceOrder
}
