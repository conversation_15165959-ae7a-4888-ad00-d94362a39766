// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

import (
	"time"
)

const TableNameFSettleFinanceOrder = "f_settle_finance_order"

// FSettleFinanceOrder 结算资金单表
type FSettleFinanceOrder struct {
	ID                     int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:自增主键" json:"id"`                                                                                  // 自增主键
	TenantType             int32     `gorm:"column:tenant_type;type:int;not null;comment:租户类型" json:"tenant_type"`                                                                                        // 租户类型
	BizScene               int32     `gorm:"column:biz_scene;type:int;not null;comment:业务场景" json:"biz_scene"`                                                                                            // 业务场景
	SettleFinanceOrderID   string    `gorm:"column:settle_finance_order_id;type:varchar(512);not null;uniqueIndex:uniq_settle_finance_order_id,priority:1;comment:分账资金单号" json:"settle_finance_order_id"` // 分账资金单号
	IsAutoWithdraw         int32     `gorm:"column:is_auto_withdraw;type:tinyint;not null;comment:0-非自动提现 1-自动提现" json:"is_auto_withdraw"`                                                                // 0-非自动提现 1-自动提现
	OutID                  string    `gorm:"column:out_id;type:varchar(255);not null;index:idx_out_id,priority:1;comment:外部id，幂等用" json:"out_id"`                                                         // 外部id，幂等用
	OrderType              int32     `gorm:"column:order_type;type:tinyint;not null;comment:订单类型，冗余存储" json:"order_type"`                                                                                 // 订单类型，冗余存储
	OrderID                string    `gorm:"column:order_id;type:varchar(255);not null;index:idx_order_id,priority:1;comment:外部订单号" json:"order_id"`                                                      // 外部订单号
	SettleFinanceOrderType int32     `gorm:"column:settle_finance_order_type;type:int;not null;comment:结算阶段，上层定义" json:"settle_finance_order_type"`                                                       // 结算阶段，上层定义
	Amount                 int64     `gorm:"column:amount;type:bigint;not null;comment:分账总金额，单位分" json:"amount"`                                                                                          // 分账总金额，单位分
	ProcessAmount          int64     `gorm:"column:process_amount;type:bigint;not null;comment:进行中的金额，单位分" json:"process_amount"`                                                                         // 进行中的金额，单位分
	Status                 int32     `gorm:"column:status;type:int;not null;comment:状态" json:"status"`                                                                                                    // 状态
	FinanceList            *string   `gorm:"column:finance_list;type:text;comment:资金单列表" json:"finance_list"`                                                                                             // 资金单列表
	PayUnionList           *string   `gorm:"column:pay_union_list;type:text;comment:聚合支付单号列表" json:"pay_union_list"`                                                                                      // 聚合支付单号列表
	SplitList              *string   `gorm:"column:split_list;type:text;comment:分账比例信息" json:"split_list"`                                                                                                // 分账比例信息
	SubsidyList            *string   `gorm:"column:subsidy_list;type:text;comment:补贴信息" json:"subsidy_list"`                                                                                              // 补贴信息
	CallbackEvent          string    `gorm:"column:callback_event;type:varchar(255);not null;comment:回调事件" json:"callback_event"`                                                                         // 回调事件
	CalllbackExtra         *string   `gorm:"column:calllback_extra;type:text;comment:回调参数" json:"calllback_extra"`                                                                                        // 回调参数
	Extra                  *string   `gorm:"column:extra;type:text;comment:额外信息" json:"extra"`                                                                                                            // 额外信息
	FinishTime             time.Time `gorm:"column:finish_time;type:datetime;not null;default:1970-01-01 08:00:00;comment:完成时间" json:"finish_time"`                                                       // 完成时间
	CreateTime             time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                                                         // 创建时间
	UpdateTime             time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                                         // 更新时间
}

// TableName FSettleFinanceOrder's table name
func (*FSettleFinanceOrder) TableName() string {
	return TableNameFSettleFinanceOrder
}
