// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

import (
	"time"
)

const TableNameFOrderSplitInfo = "f_order_split_info"

// FOrderSplitInfo 交易基建 订单分账信息表
type FOrderSplitInfo struct {
	ID           int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                    // 主键id
	OrderID      string    `gorm:"column:order_id;type:varchar(256);not null;index:idx_order_id,priority:1;comment:基建订单号" json:"order_id"` // 基建订单号
	SplitUID     string    `gorm:"column:split_uid;type:varchar(256);not null;comment:商户传四轮ID 平台户不用传" json:"split_uid"`                    // 商户传四轮ID 平台户不用传
	SplitUIDType int32     `gorm:"column:split_uid_type;type:tinyint;not null;comment:商户类型 Platform = 1 Shop = 2" json:"split_uid_type"`   // 商户类型 Platform = 1 Shop = 2
	Scale        int64     `gorm:"column:scale;type:bigint;not null;comment:比例-万分位 15 -> 0.15%" json:"scale"`                              // 比例-万分位 15 -> 0.15%
	Amount       int64     `gorm:"column:amount;type:bigint;not null;comment:金额" json:"amount"`                                            // 金额
	SplitMethod  int32     `gorm:"column:split_method;type:int;not null;comment:分账方式：1按比例 2按固定金额" json:"split_method"`                     // 分账方式：1按比例 2按固定金额
	RoleID       string    `gorm:"column:role_id;type:varchar(256);not null;comment:业务方ID" json:"role_id"`                                 // 业务方ID
	RoleType     int32     `gorm:"column:role_type;type:int;not null;comment:业务方自定义类型" json:"role_type"`                                   // 业务方自定义类型
	SplitDesc    *string   `gorm:"column:split_desc;type:text;comment:描述信息 分账描述" json:"split_desc"`                                        // 描述信息 分账描述
	Extra        *string   `gorm:"column:extra;type:text;comment:透传信息" json:"extra"`                                                       // 透传信息
	CreatedTime  time.Time `gorm:"column:created_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_time"` // 创建时间
	UpdatedTime  time.Time `gorm:"column:updated_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:修改时间" json:"updated_time"` // 修改时间
}

// TableName FOrderSplitInfo's table name
func (*FOrderSplitInfo) TableName() string {
	return TableNameFOrderSplitInfo
}
