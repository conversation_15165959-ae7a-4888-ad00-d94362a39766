// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

import (
	"time"
)

const TableNameFweOrderLog = "fwe_order_log"

// FweOrderLog 订单日志存储
type FweOrderLog struct {
	ID              int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:自增id" json:"id"`                     // 自增id
	OrderID         string    `gorm:"column:order_id;type:varchar(128);not null;index:idx_order_id,priority:1;comment:业务对象ID" json:"order_id"` // 业务对象ID
	Action          string    `gorm:"column:action;type:varchar(128);not null;comment:操作事件" json:"action"`                                     // 操作事件
	OperatorID      string    `gorm:"column:operator_id;type:varchar(128);not null;comment:操作人ID" json:"operator_id"`                          // 操作人ID
	OperatorName    string    `gorm:"column:operator_name;type:varchar(128);not null;comment:操作人名" json:"operator_name"`                       // 操作人名
	BeforeContent   *string   `gorm:"column:before_content;type:json;comment:操作前信息,JSON文本" json:"before_content"`                              // 操作前信息,JSON文本
	AfterContent    *string   `gorm:"column:after_content;type:json;comment:操作后信息,JSON文本" json:"after_content"`                                // 操作后信息,JSON文本
	BeforeStatus    int32     `gorm:"column:before_status;type:int;not null;comment:操作前状态" json:"before_status"`                               // 操作前状态
	AfterStatus     int32     `gorm:"column:after_status;type:int;not null;comment:操作后状态" json:"after_status"`                                 // 操作后状态
	BeforeSubStatus *string   `gorm:"column:before_sub_status;type:json;comment:操作前子状态" json:"before_sub_status"`                              // 操作前子状态
	AfterSubStatus  *string   `gorm:"column:after_sub_status;type:json;comment:操作后子状态" json:"after_sub_status"`                                // 操作后子状态
	LogID           string    `gorm:"column:log_id;type:varchar(128);not null;comment:log_id" json:"log_id"`                                   // log_id
	OperateTime     int64     `gorm:"column:operate_time;type:bigint unsigned;not null;comment:操作时间" json:"operate_time"`                      // 操作时间
	CreateTime      time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`    // 创建时间
	OperateDesc     *string   `gorm:"column:operate_desc;type:text;comment:操作描述" json:"operate_desc"`                                          // 操作描述
}

// TableName FweOrderLog's table name
func (*FweOrderLog) TableName() string {
	return TableNameFweOrderLog
}
