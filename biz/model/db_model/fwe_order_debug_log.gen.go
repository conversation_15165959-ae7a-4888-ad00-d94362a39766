// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

import (
	"time"
)

const TableNameFweOrderDebugLog = "fwe_order_debug_log"

// FweOrderDebugLog mapped from table <fwe_order_debug_log>
type FweOrderDebugLog struct {
	ID              int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                          // 主键id
	OrderID         string    `gorm:"column:order_id;type:varchar(256);not null;index:idx_order_id,priority:1;comment:订单主键ID" json:"order_id"`      // 订单主键ID
	Action          string    `gorm:"column:action;type:varchar(256);not null;comment:订单执行action" json:"action"`                                    // 订单执行action
	LogID           string    `gorm:"column:log_id;type:varchar(256);not null;comment:订单执行log_id" json:"log_id"`                                    // 订单执行log_id
	BizRequest      *string   `gorm:"column:biz_request;type:text;comment:订单action执行请求" json:"biz_request"`                                         // 订单action执行请求
	BizResponse     *string   `gorm:"column:biz_response;type:text;comment:订单action执行结果" json:"biz_response"`                                       // 订单action执行结果
	BeforeStatus    int32     `gorm:"column:before_status;type:int;not null;comment:操作前状态" json:"before_status"`                                    // 操作前状态
	AfterStatus     int32     `gorm:"column:after_status;type:int;not null;comment:操作后状态" json:"after_status"`                                      // 操作后状态
	BeforeSubStatus *string   `gorm:"column:before_sub_status;type:json;comment:操作前子状态" json:"before_sub_status"`                                   // 操作前子状态
	AfterSubStatus  *string   `gorm:"column:after_sub_status;type:json;comment:操作后子状态" json:"after_sub_status"`                                     // 操作后子状态
	Success         int32     `gorm:"column:success;type:tinyint;not null;comment:订单是否执行成功 0-失败 1-成功" json:"success"`                               // 订单是否执行成功 0-失败 1-成功
	OperatedTime    time.Time `gorm:"column:operated_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:订单操作执行时间" json:"operated_time"` // 订单操作执行时间
}

// TableName FweOrderDebugLog's table name
func (*FweOrderDebugLog) TableName() string {
	return TableNameFweOrderDebugLog
}
