// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

import (
	"time"
)

const TableNameFweOrderContract = "fwe_order_contract"

// FweOrderContract 订单-合同关联表
type FweOrderContract struct {
	ID              int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                              // 主键
	OrderID         string    `gorm:"column:order_id;type:varchar(64);not null;index:order_id,priority:1;comment:订单id" json:"order_id"`                      // 订单id
	FulfillID       string    `gorm:"column:fulfill_id;type:varchar(255);not null;index:idx_fulfill_id,priority:1;comment:履约订单号" json:"fulfill_id"`          // 履约订单号
	AfterSaleID     string    `gorm:"column:after_sale_id;type:varchar(255);not null;index:idx_after_sale_id,priority:1;comment:售后订单号" json:"after_sale_id"` // 售后订单号
	OrderType       int32     `gorm:"column:order_type;type:int;not null;comment:关联订单类型" json:"order_type"`                                                  // 关联订单类型
	ContractType    int32     `gorm:"column:contract_type;type:tinyint;not null;comment:合同类型" json:"contract_type"`                                          // 合同类型
	ContractNo      string    `gorm:"column:contract_no;type:varchar(64);not null;uniqueIndex:uniq,priority:1;comment:合同编码,（订单id +合同类型）" json:"contract_no"` // 合同编码,（订单id +合同类型）
	InfraContSerial string    `gorm:"column:infra_cont_serial;type:varchar(255);not null;comment:基建合同编码" json:"infra_cont_serial"`                           // 基建合同编码
	InfraContName   string    `gorm:"column:infra_cont_name;type:varchar(255);not null;comment:基建合同名称" json:"infra_cont_name"`                               // 基建合同名称
	Status          int32     `gorm:"column:status;type:int;not null;comment:状态：0-待处理，1-处理中，2-处理完成" json:"status"`                                           // 状态：0-待处理，1-处理中，2-处理完成
	SignTime        time.Time `gorm:"column:sign_time;type:datetime;not null;comment:签署时间" json:"sign_time"`                                                 // 签署时间
	Creator         int64     `gorm:"column:creator;type:bigint;not null;default:-1;comment:创建人" json:"creator"`                                             // 创建人
	CreatorName     string    `gorm:"column:creator_name;type:varchar(255);not null;default:unknow;comment:创建人名称" json:"creator_name"`                       // 创建人名称
	CreateTime      time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                   // 创建时间
	Operator        int64     `gorm:"column:operator;type:bigint;not null;default:-1;comment:操作人" json:"operator"`                                           // 操作人
	OperatorName    string    `gorm:"column:operator_name;type:varchar(255);not null;default:unknow;comment:操作人名称" json:"operator_name"`                     // 操作人名称
	UpdateTime      time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                   // 更新时间
}

// TableName FweOrderContract's table name
func (*FweOrderContract) TableName() string {
	return TableNameFweOrderContract
}
