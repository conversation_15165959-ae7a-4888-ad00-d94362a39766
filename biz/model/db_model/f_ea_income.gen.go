// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

import (
	"time"
)

const TableNameFEaIncome = "f_ea_income"

// FEaIncome 计收数据表
type FEaIncome struct {
	ID            int64      `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                                           // 主键id
	TenantType    int32      `gorm:"column:tenant_type;type:int;not null;comment:租户类型" json:"tenant_type"`                                                          // 租户类型
	MerchantID    string     `gorm:"column:merchant_id;type:varchar(255);not null;comment:一级商户id" json:"merchant_id"`                                               // 一级商户id
	BizScene      int32      `gorm:"column:biz_scene;type:int;not null;uniqueIndex:uniq_income_no,priority:1;comment:业务场景" json:"biz_scene"`                        // 业务场景
	Mid           string     `gorm:"column:mid;type:varchar(255);not null;comment:二级商户id" json:"mid"`                                                               // 二级商户id
	OrderID       string     `gorm:"column:order_id;type:varchar(255);not null;comment:基建订单号" json:"order_id"`                                                      // 基建订单号
	BuyerID       string     `gorm:"column:buyer_id;type:varchar(255);not null;comment:买方" json:"buyer_id"`                                                         // 买方
	SellerID      string     `gorm:"column:seller_id;type:varchar(255);not null;comment:卖方" json:"seller_id"`                                                       // 卖方
	IncomeOrderNo string     `gorm:"column:income_order_no;type:varchar(255);not null;uniqueIndex:uniq_income_no,priority:2;comment:业务计收单号" json:"income_order_no"` // 业务计收单号
	IncomeType    int32      `gorm:"column:income_type;type:int;not null;comment:计收类型" json:"income_type"`                                                          // 计收类型
	Currency      string     `gorm:"column:currency;type:varchar(255);not null;comment:币种，默认CNY" json:"currency"`                                                   // 币种，默认CNY
	Amount        int64      `gorm:"column:amount;type:bigint;not null;comment:计收金额，单位是分" json:"amount"`                                                            // 计收金额，单位是分
	Remark        string     `gorm:"column:remark;type:varchar(255);not null;comment:备注" json:"remark"`                                                             // 备注
	Extra         *string    `gorm:"column:extra;type:text;comment:扩展信息" json:"extra"`                                                                              // 扩展信息
	FinishTime    time.Time  `gorm:"column:finish_time;type:datetime;not null;default:1970-01-01 08:00:00;comment:完成时间" json:"finish_time"`                         // 完成时间
	CreateTime    *time.Time `gorm:"column:create_time;type:datetime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                                    // 创建时间
	UpdateTime    *time.Time `gorm:"column:update_time;type:datetime;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                    // 更新时间
}

// TableName FEaIncome's table name
func (*FEaIncome) TableName() string {
	return TableNameFEaIncome
}
