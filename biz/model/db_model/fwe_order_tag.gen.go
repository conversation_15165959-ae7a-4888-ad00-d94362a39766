// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

const TableNameFweOrderTag = "fwe_order_tag"

// FweOrderTag 交易基建订单扩展表
type FweOrderTag struct {
	ID       int64   `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                    // 主键id
	OrderID  string  `gorm:"column:order_id;type:varchar(255);not null;index:idx_order_id,priority:1;comment:基建订单号" json:"order_id"` // 基建订单号
	Tag      *string `gorm:"column:tag;type:json;comment:订单扩展字段" json:"tag"`                                                         // 订单扩展字段
	BizExtra *string `gorm:"column:biz_extra;type:json;comment:业务订单透传字段" json:"biz_extra"`                                           // 业务订单透传字段
}

// TableName FweOrderTag's table name
func (*FweOrderTag) TableName() string {
	return TableNameFweOrderTag
}
