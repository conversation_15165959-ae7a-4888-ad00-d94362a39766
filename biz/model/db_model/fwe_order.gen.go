// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_model

import (
	"time"
)

const TableNameFweOrder = "fwe_order"

// FweOrder 交易基建订单表
type FweOrder struct {
	ID         int64 `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                    // 主键id
	TenantType int32 `gorm:"column:tenant_type;type:int;not null;index:idx_tenant_scene,priority:1;comment:租户类型" json:"tenant_type"` // 租户类型
	/*
		业务场景
		（新车门店业务）2201:大定 2202:小订 2102:返佣无水平 2101:返佣有水平 2301:采购单
	*/
	BizScene             int32     `gorm:"column:biz_scene;type:int;not null;index:idx_biz_scene,priority:1;index:idx_idem_id,priority:2;index:idx_tenant_scene,priority:2;comment:业务场景 \n（新车门店业务）2201:大定 2202:小订 2102:返佣无水平 2101:返佣有水平 2301:采购单" json:"biz_scene"`
	OrderID              string    `gorm:"column:order_id;type:varchar(255);not null;index:idx_order_id,priority:1;comment:基建订单号" json:"order_id"`                                           // 基建订单号
	OrderStatus          int32     `gorm:"column:order_status;type:int;not null;comment:订单状态" json:"order_status"`                                                                           // 订单状态
	OrderSubStatus       *string   `gorm:"column:order_sub_status;type:json;comment:订单平行子状态" json:"order_sub_status"`                                                                        // 订单平行子状态
	BeforeStatus         int32     `gorm:"column:before_status;type:int;not null;comment:订单前一个状态" json:"before_status"`                                                                      // 订单前一个状态
	OrderName            string    `gorm:"column:order_name;type:varchar(255);not null;comment:订单名" json:"order_name"`                                                                       // 订单名
	OrderDesc            string    `gorm:"column:order_desc;type:varchar(255);not null;comment:订单描述" json:"order_desc"`                                                                      // 订单描述
	ProductID            string    `gorm:"column:product_id;type:varchar(255);not null;comment:商品id" json:"product_id"`                                                                      // 商品id
	ProductType          int32     `gorm:"column:product_type;type:int;not null;comment:商品类型" json:"product_type"`                                                                           // 商品类型
	ProductName          string    `gorm:"column:product_name;type:varchar(255);not null;comment:商品名称" json:"product_name"`                                                                  // 商品名称
	ProductDetail        *string   `gorm:"column:product_detail;type:json;comment:商品详情" json:"product_detail"`                                                                               // 商品详情
	ProductExtra         *string   `gorm:"column:product_extra;type:json;comment:商品扩展" json:"product_extra"`                                                                                 // 商品扩展
	ProductVersion       int64     `gorm:"column:product_version;type:bigint;not null;comment:商品版本" json:"product_version"`                                                                  // 商品版本
	SkuID                string    `gorm:"column:sku_id;type:varchar(255);not null;comment:skucode" json:"sku_id"`                                                                           // skucode
	SkuVersion           int64     `gorm:"column:sku_version;type:bigint;not null;comment:sku版本" json:"sku_version"`                                                                         // sku版本
	ProductQuantity      int32     `gorm:"column:product_quantity;type:int;not null;default:1;comment:商品数量" json:"product_quantity"`                                                         // 商品数量
	ProductUnitPrice     int64     `gorm:"column:product_unit_price;type:bigint;not null;comment:商品单价 单位分" json:"product_unit_price"`                                                        // 商品单价 单位分
	TotalAmount          int64     `gorm:"column:total_amount;type:bigint;not null;comment:订单总金额，单位是分" json:"total_amount"`                                                                  // 订单总金额，单位是分
	TotalPayAmount       int64     `gorm:"column:total_pay_amount;type:bigint;not null;comment:总支付金额 ，单位分" json:"total_pay_amount"`                                                          // 总支付金额 ，单位分
	TotalSubsidyAmount   int64     `gorm:"column:total_subsidy_amount;type:bigint;not null;comment:优惠总金额，单位分" json:"total_subsidy_amount"`                                                   // 优惠总金额，单位分
	TradeType            int32     `gorm:"column:trade_type;type:int;not null;comment:交易类型，由bizScene 指定" json:"trade_type"`                                                                  // 交易类型，由bizScene 指定
	UID                  int64     `gorm:"column:uid;type:bigint;not null;comment:uid" json:"uid"`                                                                                           // uid
	MobileID             int64     `gorm:"column:mobile_id;type:bigint;not null;comment:mobile_id" json:"mobile_id"`                                                                         // mobile_id
	BuyerID              string    `gorm:"column:buyer_id;type:varchar(255);not null;comment:买家id" json:"buyer_id"`                                                                          // 买家id
	BuyerExtra           *string   `gorm:"column:buyer_extra;type:json;comment:买家冗余信息" json:"buyer_extra"`                                                                                   // 买家冗余信息
	SellerID             string    `gorm:"column:seller_id;type:varchar(255);not null;comment:卖家id" json:"seller_id"`                                                                        // 卖家id
	SellerExtra          *string   `gorm:"column:seller_extra;type:json;comment:卖家冗余信息" json:"seller_extra"`                                                                                 // 卖家冗余信息
	ServiceProviderID    string    `gorm:"column:service_provider_id;type:varchar(255);not null;comment:服务商id" json:"service_provider_id"`                                                   // 服务商id
	ServiceProviderExtra *string   `gorm:"column:service_provider_extra;type:json;comment:服务商冗余信息" json:"service_provider_extra"`                                                            // 服务商冗余信息
	PurchasePlan         *string   `gorm:"column:purchase_plan;type:json;comment:购车方案" json:"purchase_plan"`                                                                                 // 购车方案
	TradeOption          *string   `gorm:"column:trade_option;type:json;comment:交易策略可选配置" json:"trade_option"`                                                                               // 交易策略可选配置
	TalentID             string    `gorm:"column:talent_id;type:varchar(255);not null;index:idx_talent_id,priority:1;comment:达人id" json:"talent_id"`                                         // 达人id
	TalentExtra          *string   `gorm:"column:talent_extra;type:json;comment:达人冗余信息" json:"talent_extra"`                                                                                 // 达人冗余信息
	IsTest               int32     `gorm:"column:is_test;type:int;not null;comment:是否测试，1-是，0-不是" json:"is_test"`                                                                            // 是否测试，1-是，0-不是
	FinishTime           time.Time `gorm:"column:finish_time;type:datetime;not null;default:1970-01-01 08:00:00;comment:完成时间(终止态时间)" json:"finish_time"`                                     // 完成时间(终止态时间)
	CreateTime           time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                                              // 创建时间
	Creator              string    `gorm:"column:creator;type:varchar(255);not null;comment:创建人id" json:"creator"`                                                                           // 创建人id
	CreatorName          string    `gorm:"column:creator_name;type:varchar(255);not null;comment:创建人名称" json:"creator_name"`                                                                 // 创建人名称
	UpdateTime           time.Time `gorm:"column:update_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                              // 更新时间
	Operator             string    `gorm:"column:operator;type:varchar(255);not null;comment:更新人id" json:"operator"`                                                                         // 更新人id
	OperatorName         string    `gorm:"column:operator_name;type:varchar(255);not null;comment:更新人名称" json:"operator_name"`                                                               // 更新人名称
	IdempotentID         string    `gorm:"column:idempotent_id;type:varchar(128);not null;index:idx_idem_id,priority:1;index:idx_idempotent_id,priority:1;comment:幂等号" json:"idempotent_id"` // 幂等号
	SmVersion            int32     `gorm:"column:sm_version;type:int;not null;comment:场景版本" json:"sm_version"`                                                                               // 场景版本
	SnapshotContent      *string   `gorm:"column:snapshot_content;type:json;comment:订单快照" json:"snapshot_content"`                                                                           // 订单快照
}

// TableName FweOrder's table name
func (*FweOrder) TableName() string {
	return TableNameFweOrder
}
