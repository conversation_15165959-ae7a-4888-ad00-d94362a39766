package action_model

import (
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UpdateOrder struct {
	OrderName   *string
	OrderDesc   *string
	TotalAmount *int64
	BuyerInfo   *fwe_trade_common.TradeSubjectInfo
	SellerInfo  *fwe_trade_common.TradeSubjectInfo
	ProductInfo *fwe_trade_common.ProductInfo
	FinanceList []*fwe_trade_common.FinanceInfo
	Tag         map[string]string
	Extra       map[string]string
}

type CreateSettleReq struct {
	CallbackEvent string
}

type CreateSettleRsp struct{}

type CreateRefundReq struct {
	CallbackEvent     string
	Reason            string
	RefundFinanceType int32
}

type CreateRefundRsp struct {
	MergeRefundID string
}

type CreatePosPayReq struct {
	CallbackEvent        string
	TimeoutCallbackEvent string
	FinanceOrderType     int32
	Amount               int64
	ExpireTime           int64
}

type CreatePosPayRsp struct {
	PayNo      string
	PayData    string
	CreateTime int64
}

type CreateGuaranteePayReq struct {
	CallbackEvent        string
	TimeoutCallbackEvent string
	FinanceOrderType     int32
	Amount               int64
	ExpireTime           int64
	CashierDeskType      fwe_trade_common.CashierDeskType
	Currency             *payment.CurrencyType
	RedirectURL          string
	PayLimitList         []fwe_trade_common.PayLimitType
}

type CreateGuaranteePayRsp struct {
	PayNo   string
	PayData string
}

type WithdrawReq struct {
	FinanceOrderType int32
	Amount           int64
	WithdrawDesc     string
	SellerInfo       *fwe_trade_common.TradeSubjectInfo
	Extra            map[string]string
}

type WithdrawRsp struct {
	WithdrawNo string
}

type ExtraUpdateReq struct {
	Extra map[string]string
}

type SignContReq struct {
	ContType       int32
	CallbackEvent  string
	SmsID          int64
	SmsChannelID   int32
	ContTmplID     int64
	ContTmplParams map[string]string
	PBInner        bool
	ReturnUrl      string
}

type SignContRsp struct {
	ContSerial string
	SignLink   string
}

type CreateFinanceReq struct {
	OrderTradeType CommonConsts.OrderTradeType
	Finances       []*fwe_trade_common.FinanceInfo
	TagMap         map[string]string
}

type CreateOfflinePayModel struct {
	CallbackEvent      string
	CloseCallbackEvent string
	FinanceOrderType   int32
	Amount             int64
	PayOrderNo         *string
	IpAddress          *string
	CheckData          *fwe_trade_common.PayOfflineCheckData
	TagMap             map[string]string
}

type CreateUnionPayReq struct {
	FinanceOrderType int32
	TradeType        string
}

type UpdateFinanceTradeTypeReq struct {
	FinanceOrderType int32
	NewTradeType     string
	MerchantId       string
	AppId            string
	Mid              string
	TagMap           map[string]string
}
