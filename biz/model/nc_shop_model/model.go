package nc_shop_model

import "code.byted.org/motor/fwe_trade_engine/biz/model"

type ConfCompany struct {
	Name         string `json:"name"`
	CreditCode   string `json:"credit_code"`
	IdentifyType string `json:"identify_type"`
	Aid          int32  `json:"aid"`
	MerchantID   string `json:"merchant_id"`
	MerchantName string `json:"merchant_name"`
	Uid          string `json:"uid"`
	UidType      int64  `json:"uid_type"`
	AppID        string `json:"app_id"`
	FcType       string `json:"fc_type"`
	FcSceneCode  int64  `json:"fc_scene_code"`
}

type Conf struct {
	*model.BaseConfig
	CheckContStructField bool                    `json:"check_cont_struct_field"`
	CompanyMap           map[string]*ConfCompany `json:"company_map"`
}

type ConfPurchase struct {
	CompanyMap map[string]*ConfCompany `json:"company_map"`
}

type UploadContPicReq struct {
	NeedRefund bool              `json:"need_refund"`
	Extra      map[string]string `json:"extra"`
}

type ReviewPassReq struct {
	RefundAmount *int64
	Extra        map[string]string
}
