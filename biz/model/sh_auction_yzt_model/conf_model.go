package sh_auction_yzt_model

type Conf struct {
	YZTPayMerchant                           *MerchantInfo `json:"yzt_pay_merchant,omitempty"` // 云直通财经一级商户信息
	CheckContStructField                     bool          `json:"check_cont_struct_field"`    //校验合同结构化字段
	CheckContOptionAllowIncomeAmountOverflow bool          `json:"check_cont_option_allow_income_amount_overflow"`
	TestOrderLimitAmount                     *int64        `json:"test_order_limit_amount,omitempty"` // 测试订单限制金额
}

type MerchantInfo struct {
	AppID         string `json:"app_id"`
	MerchantID    string `json:"merchant_id"`
	MerchantName  string `json:"merchant_name"`
	ReceiveUID    string `json:"receive_uid"`
	FcType        string `json:"fc_type"`
	FcSceneCode   int64  `json:"fc_scene_code"`
	SettleUID     string `json:"settle_uid"`
	SettleUIDType int32  `json:"settle_uid_type"`
}
