package car_supply_model

import (
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/motor/trade/audit"
)

type SingleRefund struct {
	FinanceOrderType int32 `thrift:"finance_order_type,1" json:"finance_order_type"`
	Amount           int64 `thrift:"amount,2" json:"amount"`
}

type RefundReq struct {
	RefundList     []*SingleRefund   `thrift:"refund_list,1" json:"refund_list"`
	RefundType     int32             `thrift:"refund_type,2" json:"refund_type"`
	Reason         string            `thrift:"reason,10" json:"reason"`
	IPAddress      string            `thrift:"ip_address,11" json:"ip_address"`
	CallbackAction string            `thrift:"callback_action,100" json:"callback_action"`
	Tag            map[string]string `thrift:"tag,200" json:"tag"`
	RefundExtra    map[string]string `thrift:"refund_extra,201" json:"refund_extra"`
}

type RefundResp struct {
	MergeRefundNo string `thrift:"merge_refund_no,1" json:"merge_refund_no"`
}

type SignPartyInfo struct {
	SignPosition  core.SignPosition   `json:"sign_position,omitempty"`
	TaxID         string              `json:"tax_id,omitempty"`          // 公司信用代码 非我司合同填写
	StampDetail   *core.StampDetail   `json:"stamp_detail,omitempty"`    // 自动签章信息 非我司合同填写
	SignPartyData *core.SignPartyData `json:"sign_party_data,omitempty"` // 我司合同填写
	SmsConfig     *core.SmsConfig     `json:"sms_config"`
}

type SignContractReq struct {
	IsInnerContract  bool                                  `json:"is_inner_contract"`
	ContType         int32                                 `json:"cont_type"`
	ContTmplID       int64                                 `json:"cont_tmpl_id"`
	ContTmplParams   map[string]string                     `json:"cont_tmpl_params"`
	RedirectURL      *string                               `json:"redirect_url"`
	SmsConfigMap     map[core.SignPosition]*core.SmsConfig `json:"sms_config_map"`
	CallbackAction   string                                `json:"callback_action"`
	Tag              map[string]string                     `json:"tag"`
	SignPartyInfoMap map[core.SignPosition]*SignPartyInfo  `json:"sign_party_data_map"`
}

type SignContractResp struct {
	ContSerial string                       `thrift:"cont_serial,1" json:"cont_serial"`
	SignLink   string                       `thrift:"sign_link,2" json:"sign_link"`
	SignLinks  map[core.SignPosition]string `json:"sign_links"`
}

type TerminateContractReq struct {
	ContType int32 `json:"cont_type"`
}

type ApplyAuditReq struct {
	Tenant            int64
	SystemId          int32
	BizType           int32                       // 自定义业务类型
	OuterID           *string                     // 业务方的主键ID
	AuditName         string                      // 审批名称
	AuditParams       string                      // 自定义审核参数
	Steps             []*audit.ApplyAuditStepInfo // 审批节点
	CreatorId         int64                       // 创建者ID
	CreatorName       string                      // 创建者名称
	OrderID           string                      // 订单ID - BizIndex1
	AuditPassAction   string                      // 审核通过Action - BizIndex2
	AuditRejectAction string                      // 审核失败Action - BizIndex3
	FulfillOrderID    string                      // 履约交付单ID   - BizIndex4
	UniqID            string                      // 审批批次ID用于召回展示同一批审批	- BizIndex5
	BizIndex6         string
	BizIndex7         string
	BizIndex8         string
	BizIndex9         string

	FinanceType int32 // 支付必传
}

type AuditCallbackReq struct {
	AuditNum          int64
	CreatorId         int64
	Tenant            int32
	BizType           int32
	OuterId           string
	AuditName         string
	Status            int32
	CreatedAt         int64
	UpdatedAt         int64
	EndAt             *int64
	OrderId           string
	AuditPassAction   string
	AuditRejectAction string
	FulfillOrderID    string
	UniqID            string
	BizIndex6         string
	BizIndex7         string
	BizIndex8         string
	BizIndex9         string
	Id                int64
}

type CashPayReq struct {
	MerchantID       string                           `thrift:"merchant_id,1" json:"merchant_id"`
	AppID            string                           `thrift:"app_id,2" json:"app_id"`
	UserID           *string                          `thrift:"user_id,3,optional" json:"user_id,omitempty"`
	PayOrderNo       *string                          `thrift:"pay_order_no,4,optional" json:"pay_order_no,omitempty"`
	FinanceOrderType int32                            `thrift:"finance_order_type,10" json:"finance_order_type"`
	Amount           int64                            `thrift:"amount,11" json:"amount"`
	CashierDeskType  fwe_trade_common.CashierDeskType `thrift:"cashier_desk_type,12" json:"cashier_desk_type"`
	OsType           fwe_trade_common.OSType          `json:"os_type"`
	ExpireTime       *int64                           `thrift:"expire_time,13,optional" json:"expire_time,omitempty"`
	RedirectURL      *string                          `thrift:"redirect_url,14,optional" json:"redirect_url,omitempty"`
	IPAddress        string                           `thrift:"ip_address,15" json:"ip_address"`
	PayLimitList     []fwe_trade_common.PayLimitType  `thrift:"pay_limit_list,16" json:"pay_limit_list"`
	CallbackAction   string                           `thrift:"callback_action,100" json:"callback_action"`
	TimeoutAction    string                           `thrift:"timeout_action,101" json:"timeout_action"`
	CloseAction      string                           `json:"close_action"`

	Tag map[string]string `thrift:"tag,200" json:"tag"`

	CheckData *fwe_trade_common.PayOfflineCheckData `json:"check_data,omitempty"`
}

type UpdateProductExtraReq struct {
	ProductExtra string            `json:"product_extra,omitempty"`
	Tag          map[string]string `json:"tag,omitempty"`
}
