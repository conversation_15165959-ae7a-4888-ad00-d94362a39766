// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFweOrderDebugLog(db *gorm.DB, opts ...gen.DOOption) fweOrderDebugLog {
	_fweOrderDebugLog := fweOrderDebugLog{}

	_fweOrderDebugLog.fweOrderDebugLogDo.UseDB(db, opts...)
	_fweOrderDebugLog.fweOrderDebugLogDo.UseModel(&db_model.FweOrderDebugLog{})

	tableName := _fweOrderDebugLog.fweOrderDebugLogDo.TableName()
	_fweOrderDebugLog.ALL = field.NewAsterisk(tableName)
	_fweOrderDebugLog.ID = field.NewInt64(tableName, "id")
	_fweOrderDebugLog.OrderID = field.NewString(tableName, "order_id")
	_fweOrderDebugLog.Action = field.NewString(tableName, "action")
	_fweOrderDebugLog.LogID = field.NewString(tableName, "log_id")
	_fweOrderDebugLog.BizRequest = field.NewString(tableName, "biz_request")
	_fweOrderDebugLog.BizResponse = field.NewString(tableName, "biz_response")
	_fweOrderDebugLog.BeforeStatus = field.NewInt32(tableName, "before_status")
	_fweOrderDebugLog.AfterStatus = field.NewInt32(tableName, "after_status")
	_fweOrderDebugLog.BeforeSubStatus = field.NewString(tableName, "before_sub_status")
	_fweOrderDebugLog.AfterSubStatus = field.NewString(tableName, "after_sub_status")
	_fweOrderDebugLog.Success = field.NewInt32(tableName, "success")
	_fweOrderDebugLog.OperatedTime = field.NewTime(tableName, "operated_time")

	_fweOrderDebugLog.fillFieldMap()

	return _fweOrderDebugLog
}

type fweOrderDebugLog struct {
	fweOrderDebugLogDo fweOrderDebugLogDo

	ALL             field.Asterisk
	ID              field.Int64  // 主键id
	OrderID         field.String // 订单主键ID
	Action          field.String // 订单执行action
	LogID           field.String // 订单执行log_id
	BizRequest      field.String // 订单action执行请求
	BizResponse     field.String // 订单action执行结果
	BeforeStatus    field.Int32  // 操作前状态
	AfterStatus     field.Int32  // 操作后状态
	BeforeSubStatus field.String // 操作前子状态
	AfterSubStatus  field.String // 操作后子状态
	Success         field.Int32  // 订单是否执行成功 0-失败 1-成功
	OperatedTime    field.Time   // 订单操作执行时间

	fieldMap map[string]field.Expr
}

func (f fweOrderDebugLog) Table(newTableName string) *fweOrderDebugLog {
	f.fweOrderDebugLogDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fweOrderDebugLog) As(alias string) *fweOrderDebugLog {
	f.fweOrderDebugLogDo.DO = *(f.fweOrderDebugLogDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fweOrderDebugLog) updateTableName(table string) *fweOrderDebugLog {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.OrderID = field.NewString(table, "order_id")
	f.Action = field.NewString(table, "action")
	f.LogID = field.NewString(table, "log_id")
	f.BizRequest = field.NewString(table, "biz_request")
	f.BizResponse = field.NewString(table, "biz_response")
	f.BeforeStatus = field.NewInt32(table, "before_status")
	f.AfterStatus = field.NewInt32(table, "after_status")
	f.BeforeSubStatus = field.NewString(table, "before_sub_status")
	f.AfterSubStatus = field.NewString(table, "after_sub_status")
	f.Success = field.NewInt32(table, "success")
	f.OperatedTime = field.NewTime(table, "operated_time")

	f.fillFieldMap()

	return f
}

func (f *fweOrderDebugLog) WithContext(ctx context.Context) *fweOrderDebugLogDo {
	return f.fweOrderDebugLogDo.WithContext(ctx)
}

func (f fweOrderDebugLog) TableName() string { return f.fweOrderDebugLogDo.TableName() }

func (f fweOrderDebugLog) Alias() string { return f.fweOrderDebugLogDo.Alias() }

func (f fweOrderDebugLog) Columns(cols ...field.Expr) gen.Columns {
	return f.fweOrderDebugLogDo.Columns(cols...)
}

func (f *fweOrderDebugLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fweOrderDebugLog) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 12)
	f.fieldMap["id"] = f.ID
	f.fieldMap["order_id"] = f.OrderID
	f.fieldMap["action"] = f.Action
	f.fieldMap["log_id"] = f.LogID
	f.fieldMap["biz_request"] = f.BizRequest
	f.fieldMap["biz_response"] = f.BizResponse
	f.fieldMap["before_status"] = f.BeforeStatus
	f.fieldMap["after_status"] = f.AfterStatus
	f.fieldMap["before_sub_status"] = f.BeforeSubStatus
	f.fieldMap["after_sub_status"] = f.AfterSubStatus
	f.fieldMap["success"] = f.Success
	f.fieldMap["operated_time"] = f.OperatedTime
}

func (f fweOrderDebugLog) clone(db *gorm.DB) fweOrderDebugLog {
	f.fweOrderDebugLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fweOrderDebugLog) replaceDB(db *gorm.DB) fweOrderDebugLog {
	f.fweOrderDebugLogDo.ReplaceDB(db)
	return f
}

type fweOrderDebugLogDo struct{ gen.DO }

func (f fweOrderDebugLogDo) Debug() *fweOrderDebugLogDo {
	return f.withDO(f.DO.Debug())
}

func (f fweOrderDebugLogDo) WithContext(ctx context.Context) *fweOrderDebugLogDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fweOrderDebugLogDo) ReadDB() *fweOrderDebugLogDo {
	return f.Clauses(dbresolver.Read)
}

func (f fweOrderDebugLogDo) WriteDB() *fweOrderDebugLogDo {
	return f.Clauses(dbresolver.Write)
}

func (f fweOrderDebugLogDo) Session(config *gorm.Session) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Session(config))
}

func (f fweOrderDebugLogDo) Clauses(conds ...clause.Expression) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fweOrderDebugLogDo) Returning(value interface{}, columns ...string) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fweOrderDebugLogDo) Not(conds ...gen.Condition) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fweOrderDebugLogDo) Or(conds ...gen.Condition) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fweOrderDebugLogDo) Select(conds ...field.Expr) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fweOrderDebugLogDo) Where(conds ...gen.Condition) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fweOrderDebugLogDo) Order(conds ...field.Expr) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fweOrderDebugLogDo) Distinct(cols ...field.Expr) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fweOrderDebugLogDo) Omit(cols ...field.Expr) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fweOrderDebugLogDo) Join(table schema.Tabler, on ...field.Expr) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fweOrderDebugLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fweOrderDebugLogDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fweOrderDebugLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *fweOrderDebugLogDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fweOrderDebugLogDo) Group(cols ...field.Expr) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fweOrderDebugLogDo) Having(conds ...gen.Condition) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fweOrderDebugLogDo) Limit(limit int) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fweOrderDebugLogDo) Offset(offset int) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fweOrderDebugLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fweOrderDebugLogDo) Unscoped() *fweOrderDebugLogDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fweOrderDebugLogDo) Create(values ...*db_model.FweOrderDebugLog) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fweOrderDebugLogDo) CreateInBatches(values []*db_model.FweOrderDebugLog, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fweOrderDebugLogDo) Save(values ...*db_model.FweOrderDebugLog) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fweOrderDebugLogDo) First() (*db_model.FweOrderDebugLog, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderDebugLog), nil
	}
}

func (f fweOrderDebugLogDo) Take() (*db_model.FweOrderDebugLog, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderDebugLog), nil
	}
}

func (f fweOrderDebugLogDo) Last() (*db_model.FweOrderDebugLog, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderDebugLog), nil
	}
}

func (f fweOrderDebugLogDo) Find() ([]*db_model.FweOrderDebugLog, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FweOrderDebugLog), err
}

func (f fweOrderDebugLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FweOrderDebugLog, err error) {
	buf := make([]*db_model.FweOrderDebugLog, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fweOrderDebugLogDo) FindInBatches(result *[]*db_model.FweOrderDebugLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fweOrderDebugLogDo) Attrs(attrs ...field.AssignExpr) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fweOrderDebugLogDo) Assign(attrs ...field.AssignExpr) *fweOrderDebugLogDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fweOrderDebugLogDo) Joins(fields ...field.RelationField) *fweOrderDebugLogDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fweOrderDebugLogDo) Preload(fields ...field.RelationField) *fweOrderDebugLogDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fweOrderDebugLogDo) FirstOrInit() (*db_model.FweOrderDebugLog, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderDebugLog), nil
	}
}

func (f fweOrderDebugLogDo) FirstOrCreate() (*db_model.FweOrderDebugLog, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderDebugLog), nil
	}
}

func (f fweOrderDebugLogDo) FindByPage(offset int, limit int) (result []*db_model.FweOrderDebugLog, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fweOrderDebugLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fweOrderDebugLogDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fweOrderDebugLogDo) Delete(models ...*db_model.FweOrderDebugLog) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fweOrderDebugLogDo) withDO(do gen.Dao) *fweOrderDebugLogDo {
	f.DO = *do.(*gen.DO)
	return f
}
