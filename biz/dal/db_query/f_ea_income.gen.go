// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFEaIncome(db *gorm.DB, opts ...gen.DOOption) fEaIncome {
	_fEaIncome := fEaIncome{}

	_fEaIncome.fEaIncomeDo.UseDB(db, opts...)
	_fEaIncome.fEaIncomeDo.UseModel(&db_model.FEaIncome{})

	tableName := _fEaIncome.fEaIncomeDo.TableName()
	_fEaIncome.ALL = field.NewAsterisk(tableName)
	_fEaIncome.ID = field.NewInt64(tableName, "id")
	_fEaIncome.TenantType = field.NewInt32(tableName, "tenant_type")
	_fEaIncome.MerchantID = field.NewString(tableName, "merchant_id")
	_fEaIncome.BizScene = field.NewInt32(tableName, "biz_scene")
	_fEaIncome.Mid = field.NewString(tableName, "mid")
	_fEaIncome.OrderID = field.NewString(tableName, "order_id")
	_fEaIncome.BuyerID = field.NewString(tableName, "buyer_id")
	_fEaIncome.SellerID = field.NewString(tableName, "seller_id")
	_fEaIncome.IncomeOrderNo = field.NewString(tableName, "income_order_no")
	_fEaIncome.IncomeType = field.NewInt32(tableName, "income_type")
	_fEaIncome.Currency = field.NewString(tableName, "currency")
	_fEaIncome.Amount = field.NewInt64(tableName, "amount")
	_fEaIncome.Remark = field.NewString(tableName, "remark")
	_fEaIncome.Extra = field.NewString(tableName, "extra")
	_fEaIncome.FinishTime = field.NewTime(tableName, "finish_time")
	_fEaIncome.CreateTime = field.NewTime(tableName, "create_time")
	_fEaIncome.UpdateTime = field.NewTime(tableName, "update_time")

	_fEaIncome.fillFieldMap()

	return _fEaIncome
}

// fEaIncome 计收数据表
type fEaIncome struct {
	fEaIncomeDo fEaIncomeDo

	ALL           field.Asterisk
	ID            field.Int64  // 主键id
	TenantType    field.Int32  // 租户类型
	MerchantID    field.String // 一级商户id
	BizScene      field.Int32  // 业务场景
	Mid           field.String // 二级商户id
	OrderID       field.String // 基建订单号
	BuyerID       field.String // 买方
	SellerID      field.String // 卖方
	IncomeOrderNo field.String // 业务计收单号
	IncomeType    field.Int32  // 计收类型
	Currency      field.String // 币种，默认CNY
	Amount        field.Int64  // 计收金额，单位是分
	Remark        field.String // 备注
	Extra         field.String // 扩展信息
	FinishTime    field.Time   // 完成时间
	CreateTime    field.Time   // 创建时间
	UpdateTime    field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (f fEaIncome) Table(newTableName string) *fEaIncome {
	f.fEaIncomeDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fEaIncome) As(alias string) *fEaIncome {
	f.fEaIncomeDo.DO = *(f.fEaIncomeDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fEaIncome) updateTableName(table string) *fEaIncome {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.TenantType = field.NewInt32(table, "tenant_type")
	f.MerchantID = field.NewString(table, "merchant_id")
	f.BizScene = field.NewInt32(table, "biz_scene")
	f.Mid = field.NewString(table, "mid")
	f.OrderID = field.NewString(table, "order_id")
	f.BuyerID = field.NewString(table, "buyer_id")
	f.SellerID = field.NewString(table, "seller_id")
	f.IncomeOrderNo = field.NewString(table, "income_order_no")
	f.IncomeType = field.NewInt32(table, "income_type")
	f.Currency = field.NewString(table, "currency")
	f.Amount = field.NewInt64(table, "amount")
	f.Remark = field.NewString(table, "remark")
	f.Extra = field.NewString(table, "extra")
	f.FinishTime = field.NewTime(table, "finish_time")
	f.CreateTime = field.NewTime(table, "create_time")
	f.UpdateTime = field.NewTime(table, "update_time")

	f.fillFieldMap()

	return f
}

func (f *fEaIncome) WithContext(ctx context.Context) *fEaIncomeDo {
	return f.fEaIncomeDo.WithContext(ctx)
}

func (f fEaIncome) TableName() string { return f.fEaIncomeDo.TableName() }

func (f fEaIncome) Alias() string { return f.fEaIncomeDo.Alias() }

func (f fEaIncome) Columns(cols ...field.Expr) gen.Columns { return f.fEaIncomeDo.Columns(cols...) }

func (f *fEaIncome) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fEaIncome) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 17)
	f.fieldMap["id"] = f.ID
	f.fieldMap["tenant_type"] = f.TenantType
	f.fieldMap["merchant_id"] = f.MerchantID
	f.fieldMap["biz_scene"] = f.BizScene
	f.fieldMap["mid"] = f.Mid
	f.fieldMap["order_id"] = f.OrderID
	f.fieldMap["buyer_id"] = f.BuyerID
	f.fieldMap["seller_id"] = f.SellerID
	f.fieldMap["income_order_no"] = f.IncomeOrderNo
	f.fieldMap["income_type"] = f.IncomeType
	f.fieldMap["currency"] = f.Currency
	f.fieldMap["amount"] = f.Amount
	f.fieldMap["remark"] = f.Remark
	f.fieldMap["extra"] = f.Extra
	f.fieldMap["finish_time"] = f.FinishTime
	f.fieldMap["create_time"] = f.CreateTime
	f.fieldMap["update_time"] = f.UpdateTime
}

func (f fEaIncome) clone(db *gorm.DB) fEaIncome {
	f.fEaIncomeDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fEaIncome) replaceDB(db *gorm.DB) fEaIncome {
	f.fEaIncomeDo.ReplaceDB(db)
	return f
}

type fEaIncomeDo struct{ gen.DO }

func (f fEaIncomeDo) Debug() *fEaIncomeDo {
	return f.withDO(f.DO.Debug())
}

func (f fEaIncomeDo) WithContext(ctx context.Context) *fEaIncomeDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fEaIncomeDo) ReadDB() *fEaIncomeDo {
	return f.Clauses(dbresolver.Read)
}

func (f fEaIncomeDo) WriteDB() *fEaIncomeDo {
	return f.Clauses(dbresolver.Write)
}

func (f fEaIncomeDo) Session(config *gorm.Session) *fEaIncomeDo {
	return f.withDO(f.DO.Session(config))
}

func (f fEaIncomeDo) Clauses(conds ...clause.Expression) *fEaIncomeDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fEaIncomeDo) Returning(value interface{}, columns ...string) *fEaIncomeDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fEaIncomeDo) Not(conds ...gen.Condition) *fEaIncomeDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fEaIncomeDo) Or(conds ...gen.Condition) *fEaIncomeDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fEaIncomeDo) Select(conds ...field.Expr) *fEaIncomeDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fEaIncomeDo) Where(conds ...gen.Condition) *fEaIncomeDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fEaIncomeDo) Order(conds ...field.Expr) *fEaIncomeDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fEaIncomeDo) Distinct(cols ...field.Expr) *fEaIncomeDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fEaIncomeDo) Omit(cols ...field.Expr) *fEaIncomeDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fEaIncomeDo) Join(table schema.Tabler, on ...field.Expr) *fEaIncomeDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fEaIncomeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fEaIncomeDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fEaIncomeDo) RightJoin(table schema.Tabler, on ...field.Expr) *fEaIncomeDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fEaIncomeDo) Group(cols ...field.Expr) *fEaIncomeDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fEaIncomeDo) Having(conds ...gen.Condition) *fEaIncomeDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fEaIncomeDo) Limit(limit int) *fEaIncomeDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fEaIncomeDo) Offset(offset int) *fEaIncomeDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fEaIncomeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fEaIncomeDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fEaIncomeDo) Unscoped() *fEaIncomeDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fEaIncomeDo) Create(values ...*db_model.FEaIncome) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fEaIncomeDo) CreateInBatches(values []*db_model.FEaIncome, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fEaIncomeDo) Save(values ...*db_model.FEaIncome) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fEaIncomeDo) First() (*db_model.FEaIncome, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FEaIncome), nil
	}
}

func (f fEaIncomeDo) Take() (*db_model.FEaIncome, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FEaIncome), nil
	}
}

func (f fEaIncomeDo) Last() (*db_model.FEaIncome, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FEaIncome), nil
	}
}

func (f fEaIncomeDo) Find() ([]*db_model.FEaIncome, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FEaIncome), err
}

func (f fEaIncomeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FEaIncome, err error) {
	buf := make([]*db_model.FEaIncome, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fEaIncomeDo) FindInBatches(result *[]*db_model.FEaIncome, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fEaIncomeDo) Attrs(attrs ...field.AssignExpr) *fEaIncomeDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fEaIncomeDo) Assign(attrs ...field.AssignExpr) *fEaIncomeDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fEaIncomeDo) Joins(fields ...field.RelationField) *fEaIncomeDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fEaIncomeDo) Preload(fields ...field.RelationField) *fEaIncomeDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fEaIncomeDo) FirstOrInit() (*db_model.FEaIncome, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FEaIncome), nil
	}
}

func (f fEaIncomeDo) FirstOrCreate() (*db_model.FEaIncome, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FEaIncome), nil
	}
}

func (f fEaIncomeDo) FindByPage(offset int, limit int) (result []*db_model.FEaIncome, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fEaIncomeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fEaIncomeDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fEaIncomeDo) Delete(models ...*db_model.FEaIncome) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fEaIncomeDo) withDO(do gen.Dao) *fEaIncomeDo {
	f.DO = *do.(*gen.DO)
	return f
}
