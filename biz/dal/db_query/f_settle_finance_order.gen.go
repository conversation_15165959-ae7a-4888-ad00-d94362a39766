// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFSettleFinanceOrder(db *gorm.DB, opts ...gen.DOOption) fSettleFinanceOrder {
	_fSettleFinanceOrder := fSettleFinanceOrder{}

	_fSettleFinanceOrder.fSettleFinanceOrderDo.UseDB(db, opts...)
	_fSettleFinanceOrder.fSettleFinanceOrderDo.UseModel(&db_model.FSettleFinanceOrder{})

	tableName := _fSettleFinanceOrder.fSettleFinanceOrderDo.TableName()
	_fSettleFinanceOrder.ALL = field.NewAsterisk(tableName)
	_fSettleFinanceOrder.ID = field.NewInt64(tableName, "id")
	_fSettleFinanceOrder.TenantType = field.NewInt32(tableName, "tenant_type")
	_fSettleFinanceOrder.BizScene = field.NewInt32(tableName, "biz_scene")
	_fSettleFinanceOrder.SettleFinanceOrderID = field.NewString(tableName, "settle_finance_order_id")
	_fSettleFinanceOrder.IsAutoWithdraw = field.NewInt32(tableName, "is_auto_withdraw")
	_fSettleFinanceOrder.OutID = field.NewString(tableName, "out_id")
	_fSettleFinanceOrder.OrderType = field.NewInt32(tableName, "order_type")
	_fSettleFinanceOrder.OrderID = field.NewString(tableName, "order_id")
	_fSettleFinanceOrder.SettleFinanceOrderType = field.NewInt32(tableName, "settle_finance_order_type")
	_fSettleFinanceOrder.Amount = field.NewInt64(tableName, "amount")
	_fSettleFinanceOrder.ProcessAmount = field.NewInt64(tableName, "process_amount")
	_fSettleFinanceOrder.Status = field.NewInt32(tableName, "status")
	_fSettleFinanceOrder.FinanceList = field.NewString(tableName, "finance_list")
	_fSettleFinanceOrder.PayUnionList = field.NewString(tableName, "pay_union_list")
	_fSettleFinanceOrder.SplitList = field.NewString(tableName, "split_list")
	_fSettleFinanceOrder.SubsidyList = field.NewString(tableName, "subsidy_list")
	_fSettleFinanceOrder.CallbackEvent = field.NewString(tableName, "callback_event")
	_fSettleFinanceOrder.CalllbackExtra = field.NewString(tableName, "calllback_extra")
	_fSettleFinanceOrder.Extra = field.NewString(tableName, "extra")
	_fSettleFinanceOrder.FinishTime = field.NewTime(tableName, "finish_time")
	_fSettleFinanceOrder.CreateTime = field.NewTime(tableName, "create_time")
	_fSettleFinanceOrder.UpdateTime = field.NewTime(tableName, "update_time")

	_fSettleFinanceOrder.fillFieldMap()

	return _fSettleFinanceOrder
}

// fSettleFinanceOrder 结算资金单表
type fSettleFinanceOrder struct {
	fSettleFinanceOrderDo fSettleFinanceOrderDo

	ALL                    field.Asterisk
	ID                     field.Int64  // 自增主键
	TenantType             field.Int32  // 租户类型
	BizScene               field.Int32  // 业务场景
	SettleFinanceOrderID   field.String // 分账资金单号
	IsAutoWithdraw         field.Int32  // 0-非自动提现 1-自动提现
	OutID                  field.String // 外部id，幂等用
	OrderType              field.Int32  // 订单类型，冗余存储
	OrderID                field.String // 外部订单号
	SettleFinanceOrderType field.Int32  // 结算阶段，上层定义
	Amount                 field.Int64  // 分账总金额，单位分
	ProcessAmount          field.Int64  // 进行中的金额，单位分
	Status                 field.Int32  // 状态
	FinanceList            field.String // 资金单列表
	PayUnionList           field.String // 聚合支付单号列表
	SplitList              field.String // 分账比例信息
	SubsidyList            field.String // 补贴信息
	CallbackEvent          field.String // 回调事件
	CalllbackExtra         field.String // 回调参数
	Extra                  field.String // 额外信息
	FinishTime             field.Time   // 完成时间
	CreateTime             field.Time   // 创建时间
	UpdateTime             field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (f fSettleFinanceOrder) Table(newTableName string) *fSettleFinanceOrder {
	f.fSettleFinanceOrderDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fSettleFinanceOrder) As(alias string) *fSettleFinanceOrder {
	f.fSettleFinanceOrderDo.DO = *(f.fSettleFinanceOrderDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fSettleFinanceOrder) updateTableName(table string) *fSettleFinanceOrder {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.TenantType = field.NewInt32(table, "tenant_type")
	f.BizScene = field.NewInt32(table, "biz_scene")
	f.SettleFinanceOrderID = field.NewString(table, "settle_finance_order_id")
	f.IsAutoWithdraw = field.NewInt32(table, "is_auto_withdraw")
	f.OutID = field.NewString(table, "out_id")
	f.OrderType = field.NewInt32(table, "order_type")
	f.OrderID = field.NewString(table, "order_id")
	f.SettleFinanceOrderType = field.NewInt32(table, "settle_finance_order_type")
	f.Amount = field.NewInt64(table, "amount")
	f.ProcessAmount = field.NewInt64(table, "process_amount")
	f.Status = field.NewInt32(table, "status")
	f.FinanceList = field.NewString(table, "finance_list")
	f.PayUnionList = field.NewString(table, "pay_union_list")
	f.SplitList = field.NewString(table, "split_list")
	f.SubsidyList = field.NewString(table, "subsidy_list")
	f.CallbackEvent = field.NewString(table, "callback_event")
	f.CalllbackExtra = field.NewString(table, "calllback_extra")
	f.Extra = field.NewString(table, "extra")
	f.FinishTime = field.NewTime(table, "finish_time")
	f.CreateTime = field.NewTime(table, "create_time")
	f.UpdateTime = field.NewTime(table, "update_time")

	f.fillFieldMap()

	return f
}

func (f *fSettleFinanceOrder) WithContext(ctx context.Context) *fSettleFinanceOrderDo {
	return f.fSettleFinanceOrderDo.WithContext(ctx)
}

func (f fSettleFinanceOrder) TableName() string { return f.fSettleFinanceOrderDo.TableName() }

func (f fSettleFinanceOrder) Alias() string { return f.fSettleFinanceOrderDo.Alias() }

func (f fSettleFinanceOrder) Columns(cols ...field.Expr) gen.Columns {
	return f.fSettleFinanceOrderDo.Columns(cols...)
}

func (f *fSettleFinanceOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fSettleFinanceOrder) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 22)
	f.fieldMap["id"] = f.ID
	f.fieldMap["tenant_type"] = f.TenantType
	f.fieldMap["biz_scene"] = f.BizScene
	f.fieldMap["settle_finance_order_id"] = f.SettleFinanceOrderID
	f.fieldMap["is_auto_withdraw"] = f.IsAutoWithdraw
	f.fieldMap["out_id"] = f.OutID
	f.fieldMap["order_type"] = f.OrderType
	f.fieldMap["order_id"] = f.OrderID
	f.fieldMap["settle_finance_order_type"] = f.SettleFinanceOrderType
	f.fieldMap["amount"] = f.Amount
	f.fieldMap["process_amount"] = f.ProcessAmount
	f.fieldMap["status"] = f.Status
	f.fieldMap["finance_list"] = f.FinanceList
	f.fieldMap["pay_union_list"] = f.PayUnionList
	f.fieldMap["split_list"] = f.SplitList
	f.fieldMap["subsidy_list"] = f.SubsidyList
	f.fieldMap["callback_event"] = f.CallbackEvent
	f.fieldMap["calllback_extra"] = f.CalllbackExtra
	f.fieldMap["extra"] = f.Extra
	f.fieldMap["finish_time"] = f.FinishTime
	f.fieldMap["create_time"] = f.CreateTime
	f.fieldMap["update_time"] = f.UpdateTime
}

func (f fSettleFinanceOrder) clone(db *gorm.DB) fSettleFinanceOrder {
	f.fSettleFinanceOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fSettleFinanceOrder) replaceDB(db *gorm.DB) fSettleFinanceOrder {
	f.fSettleFinanceOrderDo.ReplaceDB(db)
	return f
}

type fSettleFinanceOrderDo struct{ gen.DO }

func (f fSettleFinanceOrderDo) Debug() *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Debug())
}

func (f fSettleFinanceOrderDo) WithContext(ctx context.Context) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fSettleFinanceOrderDo) ReadDB() *fSettleFinanceOrderDo {
	return f.Clauses(dbresolver.Read)
}

func (f fSettleFinanceOrderDo) WriteDB() *fSettleFinanceOrderDo {
	return f.Clauses(dbresolver.Write)
}

func (f fSettleFinanceOrderDo) Session(config *gorm.Session) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Session(config))
}

func (f fSettleFinanceOrderDo) Clauses(conds ...clause.Expression) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fSettleFinanceOrderDo) Returning(value interface{}, columns ...string) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fSettleFinanceOrderDo) Not(conds ...gen.Condition) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fSettleFinanceOrderDo) Or(conds ...gen.Condition) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fSettleFinanceOrderDo) Select(conds ...field.Expr) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fSettleFinanceOrderDo) Where(conds ...gen.Condition) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fSettleFinanceOrderDo) Order(conds ...field.Expr) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fSettleFinanceOrderDo) Distinct(cols ...field.Expr) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fSettleFinanceOrderDo) Omit(cols ...field.Expr) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fSettleFinanceOrderDo) Join(table schema.Tabler, on ...field.Expr) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fSettleFinanceOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fSettleFinanceOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fSettleFinanceOrderDo) Group(cols ...field.Expr) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fSettleFinanceOrderDo) Having(conds ...gen.Condition) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fSettleFinanceOrderDo) Limit(limit int) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fSettleFinanceOrderDo) Offset(offset int) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fSettleFinanceOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fSettleFinanceOrderDo) Unscoped() *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fSettleFinanceOrderDo) Create(values ...*db_model.FSettleFinanceOrder) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fSettleFinanceOrderDo) CreateInBatches(values []*db_model.FSettleFinanceOrder, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fSettleFinanceOrderDo) Save(values ...*db_model.FSettleFinanceOrder) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fSettleFinanceOrderDo) First() (*db_model.FSettleFinanceOrder, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FSettleFinanceOrder), nil
	}
}

func (f fSettleFinanceOrderDo) Take() (*db_model.FSettleFinanceOrder, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FSettleFinanceOrder), nil
	}
}

func (f fSettleFinanceOrderDo) Last() (*db_model.FSettleFinanceOrder, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FSettleFinanceOrder), nil
	}
}

func (f fSettleFinanceOrderDo) Find() ([]*db_model.FSettleFinanceOrder, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FSettleFinanceOrder), err
}

func (f fSettleFinanceOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FSettleFinanceOrder, err error) {
	buf := make([]*db_model.FSettleFinanceOrder, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fSettleFinanceOrderDo) FindInBatches(result *[]*db_model.FSettleFinanceOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fSettleFinanceOrderDo) Attrs(attrs ...field.AssignExpr) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fSettleFinanceOrderDo) Assign(attrs ...field.AssignExpr) *fSettleFinanceOrderDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fSettleFinanceOrderDo) Joins(fields ...field.RelationField) *fSettleFinanceOrderDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fSettleFinanceOrderDo) Preload(fields ...field.RelationField) *fSettleFinanceOrderDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fSettleFinanceOrderDo) FirstOrInit() (*db_model.FSettleFinanceOrder, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FSettleFinanceOrder), nil
	}
}

func (f fSettleFinanceOrderDo) FirstOrCreate() (*db_model.FSettleFinanceOrder, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FSettleFinanceOrder), nil
	}
}

func (f fSettleFinanceOrderDo) FindByPage(offset int, limit int) (result []*db_model.FSettleFinanceOrder, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fSettleFinanceOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fSettleFinanceOrderDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fSettleFinanceOrderDo) Delete(models ...*db_model.FSettleFinanceOrder) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fSettleFinanceOrderDo) withDO(do gen.Dao) *fSettleFinanceOrderDo {
	f.DO = *do.(*gen.DO)
	return f
}
