// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                   = new(Query)
	FEaIncome           *fEaIncome
	FFinanceOrder       *fFinanceOrder
	FOrderSplitInfo     *fOrderSplitInfo
	FRefundFinanceOrder *fRefundFinanceOrder
	FSettleFinanceOrder *fSettleFinanceOrder
	FweOrder            *fweOrder
	FweOrderContract    *fweOrderContract
	FweOrderDebugLog    *fweOrderDebugLog
	FweOrderLog         *fweOrderLog
	FweOrderSubject     *fweOrderSubject
	FweOrderTag         *fweOrderTag
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	FEaIncome = &Q.FEaIncome
	FFinanceOrder = &Q.FFinanceOrder
	FOrderSplitInfo = &Q.FOrderSplitInfo
	FRefundFinanceOrder = &Q.FRefundFinanceOrder
	FSettleFinanceOrder = &Q.FSettleFinanceOrder
	FweOrder = &Q.FweOrder
	FweOrderContract = &Q.FweOrderContract
	FweOrderDebugLog = &Q.FweOrderDebugLog
	FweOrderLog = &Q.FweOrderLog
	FweOrderSubject = &Q.FweOrderSubject
	FweOrderTag = &Q.FweOrderTag
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                  db,
		FEaIncome:           newFEaIncome(db, opts...),
		FFinanceOrder:       newFFinanceOrder(db, opts...),
		FOrderSplitInfo:     newFOrderSplitInfo(db, opts...),
		FRefundFinanceOrder: newFRefundFinanceOrder(db, opts...),
		FSettleFinanceOrder: newFSettleFinanceOrder(db, opts...),
		FweOrder:            newFweOrder(db, opts...),
		FweOrderContract:    newFweOrderContract(db, opts...),
		FweOrderDebugLog:    newFweOrderDebugLog(db, opts...),
		FweOrderLog:         newFweOrderLog(db, opts...),
		FweOrderSubject:     newFweOrderSubject(db, opts...),
		FweOrderTag:         newFweOrderTag(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	FEaIncome           fEaIncome
	FFinanceOrder       fFinanceOrder
	FOrderSplitInfo     fOrderSplitInfo
	FRefundFinanceOrder fRefundFinanceOrder
	FSettleFinanceOrder fSettleFinanceOrder
	FweOrder            fweOrder
	FweOrderContract    fweOrderContract
	FweOrderDebugLog    fweOrderDebugLog
	FweOrderLog         fweOrderLog
	FweOrderSubject     fweOrderSubject
	FweOrderTag         fweOrderTag
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                  db,
		FEaIncome:           q.FEaIncome.clone(db),
		FFinanceOrder:       q.FFinanceOrder.clone(db),
		FOrderSplitInfo:     q.FOrderSplitInfo.clone(db),
		FRefundFinanceOrder: q.FRefundFinanceOrder.clone(db),
		FSettleFinanceOrder: q.FSettleFinanceOrder.clone(db),
		FweOrder:            q.FweOrder.clone(db),
		FweOrderContract:    q.FweOrderContract.clone(db),
		FweOrderDebugLog:    q.FweOrderDebugLog.clone(db),
		FweOrderLog:         q.FweOrderLog.clone(db),
		FweOrderSubject:     q.FweOrderSubject.clone(db),
		FweOrderTag:         q.FweOrderTag.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                  db,
		FEaIncome:           q.FEaIncome.replaceDB(db),
		FFinanceOrder:       q.FFinanceOrder.replaceDB(db),
		FOrderSplitInfo:     q.FOrderSplitInfo.replaceDB(db),
		FRefundFinanceOrder: q.FRefundFinanceOrder.replaceDB(db),
		FSettleFinanceOrder: q.FSettleFinanceOrder.replaceDB(db),
		FweOrder:            q.FweOrder.replaceDB(db),
		FweOrderContract:    q.FweOrderContract.replaceDB(db),
		FweOrderDebugLog:    q.FweOrderDebugLog.replaceDB(db),
		FweOrderLog:         q.FweOrderLog.replaceDB(db),
		FweOrderSubject:     q.FweOrderSubject.replaceDB(db),
		FweOrderTag:         q.FweOrderTag.replaceDB(db),
	}
}

type queryCtx struct {
	FEaIncome           *fEaIncomeDo
	FFinanceOrder       *fFinanceOrderDo
	FOrderSplitInfo     *fOrderSplitInfoDo
	FRefundFinanceOrder *fRefundFinanceOrderDo
	FSettleFinanceOrder *fSettleFinanceOrderDo
	FweOrder            *fweOrderDo
	FweOrderContract    *fweOrderContractDo
	FweOrderDebugLog    *fweOrderDebugLogDo
	FweOrderLog         *fweOrderLogDo
	FweOrderSubject     *fweOrderSubjectDo
	FweOrderTag         *fweOrderTagDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		FEaIncome:           q.FEaIncome.WithContext(ctx),
		FFinanceOrder:       q.FFinanceOrder.WithContext(ctx),
		FOrderSplitInfo:     q.FOrderSplitInfo.WithContext(ctx),
		FRefundFinanceOrder: q.FRefundFinanceOrder.WithContext(ctx),
		FSettleFinanceOrder: q.FSettleFinanceOrder.WithContext(ctx),
		FweOrder:            q.FweOrder.WithContext(ctx),
		FweOrderContract:    q.FweOrderContract.WithContext(ctx),
		FweOrderDebugLog:    q.FweOrderDebugLog.WithContext(ctx),
		FweOrderLog:         q.FweOrderLog.WithContext(ctx),
		FweOrderSubject:     q.FweOrderSubject.WithContext(ctx),
		FweOrderTag:         q.FweOrderTag.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
