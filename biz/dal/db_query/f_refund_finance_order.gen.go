// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFRefundFinanceOrder(db *gorm.DB, opts ...gen.DOOption) fRefundFinanceOrder {
	_fRefundFinanceOrder := fRefundFinanceOrder{}

	_fRefundFinanceOrder.fRefundFinanceOrderDo.UseDB(db, opts...)
	_fRefundFinanceOrder.fRefundFinanceOrderDo.UseModel(&db_model.FRefundFinanceOrder{})

	tableName := _fRefundFinanceOrder.fRefundFinanceOrderDo.TableName()
	_fRefundFinanceOrder.ALL = field.NewAsterisk(tableName)
	_fRefundFinanceOrder.ID = field.NewInt64(tableName, "id")
	_fRefundFinanceOrder.TenantType = field.NewInt32(tableName, "tenant_type")
	_fRefundFinanceOrder.BizScene = field.NewInt32(tableName, "biz_scene")
	_fRefundFinanceOrder.RefundFinanceOrderID = field.NewString(tableName, "refund_finance_order_id")
	_fRefundFinanceOrder.RefundFinanceOrderType = field.NewInt32(tableName, "refund_finance_order_type")
	_fRefundFinanceOrder.OutID = field.NewString(tableName, "out_id")
	_fRefundFinanceOrder.OrderType = field.NewInt32(tableName, "order_type")
	_fRefundFinanceOrder.OrderID = field.NewString(tableName, "order_id")
	_fRefundFinanceOrder.OrderName = field.NewString(tableName, "order_name")
	_fRefundFinanceOrder.Reason = field.NewString(tableName, "reason")
	_fRefundFinanceOrder.RefundList = field.NewString(tableName, "refund_list")
	_fRefundFinanceOrder.Amount = field.NewInt64(tableName, "amount")
	_fRefundFinanceOrder.ProcessAmount = field.NewInt64(tableName, "process_amount")
	_fRefundFinanceOrder.Status = field.NewInt32(tableName, "status")
	_fRefundFinanceOrder.CallbackEvent = field.NewString(tableName, "callback_event")
	_fRefundFinanceOrder.CalllbackExtra = field.NewString(tableName, "calllback_extra")
	_fRefundFinanceOrder.FinishTime = field.NewTime(tableName, "finish_time")
	_fRefundFinanceOrder.CreateTime = field.NewTime(tableName, "create_time")
	_fRefundFinanceOrder.UpdateTime = field.NewTime(tableName, "update_time")

	_fRefundFinanceOrder.fillFieldMap()

	return _fRefundFinanceOrder
}

// fRefundFinanceOrder 退款资金单表
type fRefundFinanceOrder struct {
	fRefundFinanceOrderDo fRefundFinanceOrderDo

	ALL                    field.Asterisk
	ID                     field.Int64  // 主键id
	TenantType             field.Int32  // 租户类型
	BizScene               field.Int32  // 业务场景
	RefundFinanceOrderID   field.String // 退款资金单号
	RefundFinanceOrderType field.Int32  // 退款阶段，根绝bizScene自定义
	OutID                  field.String // 外部id，幂等用
	OrderType              field.Int32  // 订单类型，冗余存储
	OrderID                field.String // 外部订单号
	OrderName              field.String // 外部订单名称
	Reason                 field.String // 退款原因
	RefundList             field.String // 退款组成
	Amount                 field.Int64  // 退款金额，单位：分
	ProcessAmount          field.Int64  // 进行中的金额，单位：分
	Status                 field.Int32  // 状态
	CallbackEvent          field.String // 回调事件
	CalllbackExtra         field.String // 回调参数
	FinishTime             field.Time   // 完成时间
	CreateTime             field.Time   // 创建时间
	UpdateTime             field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (f fRefundFinanceOrder) Table(newTableName string) *fRefundFinanceOrder {
	f.fRefundFinanceOrderDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fRefundFinanceOrder) As(alias string) *fRefundFinanceOrder {
	f.fRefundFinanceOrderDo.DO = *(f.fRefundFinanceOrderDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fRefundFinanceOrder) updateTableName(table string) *fRefundFinanceOrder {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.TenantType = field.NewInt32(table, "tenant_type")
	f.BizScene = field.NewInt32(table, "biz_scene")
	f.RefundFinanceOrderID = field.NewString(table, "refund_finance_order_id")
	f.RefundFinanceOrderType = field.NewInt32(table, "refund_finance_order_type")
	f.OutID = field.NewString(table, "out_id")
	f.OrderType = field.NewInt32(table, "order_type")
	f.OrderID = field.NewString(table, "order_id")
	f.OrderName = field.NewString(table, "order_name")
	f.Reason = field.NewString(table, "reason")
	f.RefundList = field.NewString(table, "refund_list")
	f.Amount = field.NewInt64(table, "amount")
	f.ProcessAmount = field.NewInt64(table, "process_amount")
	f.Status = field.NewInt32(table, "status")
	f.CallbackEvent = field.NewString(table, "callback_event")
	f.CalllbackExtra = field.NewString(table, "calllback_extra")
	f.FinishTime = field.NewTime(table, "finish_time")
	f.CreateTime = field.NewTime(table, "create_time")
	f.UpdateTime = field.NewTime(table, "update_time")

	f.fillFieldMap()

	return f
}

func (f *fRefundFinanceOrder) WithContext(ctx context.Context) *fRefundFinanceOrderDo {
	return f.fRefundFinanceOrderDo.WithContext(ctx)
}

func (f fRefundFinanceOrder) TableName() string { return f.fRefundFinanceOrderDo.TableName() }

func (f fRefundFinanceOrder) Alias() string { return f.fRefundFinanceOrderDo.Alias() }

func (f fRefundFinanceOrder) Columns(cols ...field.Expr) gen.Columns {
	return f.fRefundFinanceOrderDo.Columns(cols...)
}

func (f *fRefundFinanceOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fRefundFinanceOrder) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 19)
	f.fieldMap["id"] = f.ID
	f.fieldMap["tenant_type"] = f.TenantType
	f.fieldMap["biz_scene"] = f.BizScene
	f.fieldMap["refund_finance_order_id"] = f.RefundFinanceOrderID
	f.fieldMap["refund_finance_order_type"] = f.RefundFinanceOrderType
	f.fieldMap["out_id"] = f.OutID
	f.fieldMap["order_type"] = f.OrderType
	f.fieldMap["order_id"] = f.OrderID
	f.fieldMap["order_name"] = f.OrderName
	f.fieldMap["reason"] = f.Reason
	f.fieldMap["refund_list"] = f.RefundList
	f.fieldMap["amount"] = f.Amount
	f.fieldMap["process_amount"] = f.ProcessAmount
	f.fieldMap["status"] = f.Status
	f.fieldMap["callback_event"] = f.CallbackEvent
	f.fieldMap["calllback_extra"] = f.CalllbackExtra
	f.fieldMap["finish_time"] = f.FinishTime
	f.fieldMap["create_time"] = f.CreateTime
	f.fieldMap["update_time"] = f.UpdateTime
}

func (f fRefundFinanceOrder) clone(db *gorm.DB) fRefundFinanceOrder {
	f.fRefundFinanceOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fRefundFinanceOrder) replaceDB(db *gorm.DB) fRefundFinanceOrder {
	f.fRefundFinanceOrderDo.ReplaceDB(db)
	return f
}

type fRefundFinanceOrderDo struct{ gen.DO }

func (f fRefundFinanceOrderDo) Debug() *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Debug())
}

func (f fRefundFinanceOrderDo) WithContext(ctx context.Context) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fRefundFinanceOrderDo) ReadDB() *fRefundFinanceOrderDo {
	return f.Clauses(dbresolver.Read)
}

func (f fRefundFinanceOrderDo) WriteDB() *fRefundFinanceOrderDo {
	return f.Clauses(dbresolver.Write)
}

func (f fRefundFinanceOrderDo) Session(config *gorm.Session) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Session(config))
}

func (f fRefundFinanceOrderDo) Clauses(conds ...clause.Expression) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fRefundFinanceOrderDo) Returning(value interface{}, columns ...string) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fRefundFinanceOrderDo) Not(conds ...gen.Condition) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fRefundFinanceOrderDo) Or(conds ...gen.Condition) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fRefundFinanceOrderDo) Select(conds ...field.Expr) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fRefundFinanceOrderDo) Where(conds ...gen.Condition) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fRefundFinanceOrderDo) Order(conds ...field.Expr) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fRefundFinanceOrderDo) Distinct(cols ...field.Expr) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fRefundFinanceOrderDo) Omit(cols ...field.Expr) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fRefundFinanceOrderDo) Join(table schema.Tabler, on ...field.Expr) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fRefundFinanceOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fRefundFinanceOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fRefundFinanceOrderDo) Group(cols ...field.Expr) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fRefundFinanceOrderDo) Having(conds ...gen.Condition) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fRefundFinanceOrderDo) Limit(limit int) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fRefundFinanceOrderDo) Offset(offset int) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fRefundFinanceOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fRefundFinanceOrderDo) Unscoped() *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fRefundFinanceOrderDo) Create(values ...*db_model.FRefundFinanceOrder) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fRefundFinanceOrderDo) CreateInBatches(values []*db_model.FRefundFinanceOrder, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fRefundFinanceOrderDo) Save(values ...*db_model.FRefundFinanceOrder) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fRefundFinanceOrderDo) First() (*db_model.FRefundFinanceOrder, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FRefundFinanceOrder), nil
	}
}

func (f fRefundFinanceOrderDo) Take() (*db_model.FRefundFinanceOrder, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FRefundFinanceOrder), nil
	}
}

func (f fRefundFinanceOrderDo) Last() (*db_model.FRefundFinanceOrder, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FRefundFinanceOrder), nil
	}
}

func (f fRefundFinanceOrderDo) Find() ([]*db_model.FRefundFinanceOrder, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FRefundFinanceOrder), err
}

func (f fRefundFinanceOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FRefundFinanceOrder, err error) {
	buf := make([]*db_model.FRefundFinanceOrder, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fRefundFinanceOrderDo) FindInBatches(result *[]*db_model.FRefundFinanceOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fRefundFinanceOrderDo) Attrs(attrs ...field.AssignExpr) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fRefundFinanceOrderDo) Assign(attrs ...field.AssignExpr) *fRefundFinanceOrderDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fRefundFinanceOrderDo) Joins(fields ...field.RelationField) *fRefundFinanceOrderDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fRefundFinanceOrderDo) Preload(fields ...field.RelationField) *fRefundFinanceOrderDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fRefundFinanceOrderDo) FirstOrInit() (*db_model.FRefundFinanceOrder, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FRefundFinanceOrder), nil
	}
}

func (f fRefundFinanceOrderDo) FirstOrCreate() (*db_model.FRefundFinanceOrder, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FRefundFinanceOrder), nil
	}
}

func (f fRefundFinanceOrderDo) FindByPage(offset int, limit int) (result []*db_model.FRefundFinanceOrder, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fRefundFinanceOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fRefundFinanceOrderDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fRefundFinanceOrderDo) Delete(models ...*db_model.FRefundFinanceOrder) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fRefundFinanceOrderDo) withDO(do gen.Dao) *fRefundFinanceOrderDo {
	f.DO = *do.(*gen.DO)
	return f
}
