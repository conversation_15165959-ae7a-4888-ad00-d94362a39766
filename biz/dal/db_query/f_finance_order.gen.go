// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFFinanceOrder(db *gorm.DB, opts ...gen.DOOption) fFinanceOrder {
	_fFinanceOrder := fFinanceOrder{}

	_fFinanceOrder.fFinanceOrderDo.UseDB(db, opts...)
	_fFinanceOrder.fFinanceOrderDo.UseModel(&db_model.FFinanceOrder{})

	tableName := _fFinanceOrder.fFinanceOrderDo.TableName()
	_fFinanceOrder.ALL = field.NewAsterisk(tableName)
	_fFinanceOrder.ID = field.NewInt64(tableName, "id")
	_fFinanceOrder.FinanceOrderID = field.NewString(tableName, "finance_order_id")
	_fFinanceOrder.FinanceOrderType = field.NewInt32(tableName, "finance_order_type")
	_fFinanceOrder.OrderID = field.NewString(tableName, "order_id")
	_fFinanceOrder.FulfillID = field.NewString(tableName, "fulfill_id")
	_fFinanceOrder.OrderName = field.NewString(tableName, "order_name")
	_fFinanceOrder.TradeType = field.NewString(tableName, "trade_type")
	_fFinanceOrder.TradeCategory = field.NewInt32(tableName, "trade_category")
	_fFinanceOrder.TenantType = field.NewInt32(tableName, "tenant_type")
	_fFinanceOrder.BizScene = field.NewInt32(tableName, "biz_scene")
	_fFinanceOrder.AppID = field.NewString(tableName, "app_id")
	_fFinanceOrder.MerchantID = field.NewString(tableName, "merchant_id")
	_fFinanceOrder.Mid = field.NewString(tableName, "mid")
	_fFinanceOrder.Amount = field.NewInt64(tableName, "amount")
	_fFinanceOrder.LoanAmount = field.NewInt64(tableName, "loan_amount")
	_fFinanceOrder.ProcessAmount = field.NewInt64(tableName, "process_amount")
	_fFinanceOrder.OfflineLoanAmount = field.NewInt64(tableName, "offline_loan_amount")
	_fFinanceOrder.PlatformPromotionAmount = field.NewInt64(tableName, "platform_promotion_amount")
	_fFinanceOrder.PlatformPromotionDetail = field.NewString(tableName, "platform_promotion_detail")
	_fFinanceOrder.Status = field.NewInt32(tableName, "status")
	_fFinanceOrder.FeeItemDetail = field.NewString(tableName, "fee_item_detail")
	_fFinanceOrder.DeductItemDetail = field.NewString(tableName, "deduct_item_detail")
	_fFinanceOrder.FeeRecordID = field.NewString(tableName, "fee_record_id")
	_fFinanceOrder.CallbackEvent = field.NewString(tableName, "callback_event")
	_fFinanceOrder.CallbackExtra = field.NewString(tableName, "callback_extra")
	_fFinanceOrder.FinishTime = field.NewTime(tableName, "finish_time")
	_fFinanceOrder.CreateTime = field.NewTime(tableName, "create_time")
	_fFinanceOrder.UpdateTime = field.NewTime(tableName, "update_time")

	_fFinanceOrder.fillFieldMap()

	return _fFinanceOrder
}

// fFinanceOrder 资金单表
type fFinanceOrder struct {
	fFinanceOrderDo fFinanceOrderDo

	ALL            field.Asterisk
	ID             field.Int64  // 主键id
	FinanceOrderID field.String // 资金单号
	/*
		资金单类型 根据biz_scene自定义
		新车门店：1返佣 2小订 3大定 4尾款
	*/
	FinanceOrderType        field.Int32
	OrderID                 field.String // 外部订单号
	FulfillID               field.String // 履约单号
	OrderName               field.String // 外部订单名称
	TradeType               field.String // 交易模式
	TradeCategory           field.Int32  // 交易分类： 1-支付 2-分账 3-退款 4-出款 6-转账
	TenantType              field.Int32  // 租户类型
	BizScene                field.Int32  // 业务场景
	AppID                   field.String // 财经app_id
	MerchantID              field.String // 一级商户id
	Mid                     field.String // 二级商户id
	Amount                  field.Int64  // 金额，单位分
	LoanAmount              field.Int64  // 贷款金额，单位分
	ProcessAmount           field.Int64  // 进行中的金额，单位分
	OfflineLoanAmount       field.Int64  // 线下贷款金额
	PlatformPromotionAmount field.Int64  // 平台优惠总金额，线上资金流
	PlatformPromotionDetail field.String // 平台优惠详情
	Status                  field.Int32  // 状态 1未处理 2处理中 3完成 4失败 5关闭
	FeeItemDetail           field.String // 支付计费详情
	DeductItemDetail        field.String // 扣除费项详情
	FeeRecordID             field.String // 支付计费记录id
	CallbackEvent           field.String // 回调事件
	CallbackExtra           field.String // 回调透传参数
	FinishTime              field.Time   // 资金单结束时间
	CreateTime              field.Time   // 创建时间
	UpdateTime              field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (f fFinanceOrder) Table(newTableName string) *fFinanceOrder {
	f.fFinanceOrderDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fFinanceOrder) As(alias string) *fFinanceOrder {
	f.fFinanceOrderDo.DO = *(f.fFinanceOrderDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fFinanceOrder) updateTableName(table string) *fFinanceOrder {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.FinanceOrderID = field.NewString(table, "finance_order_id")
	f.FinanceOrderType = field.NewInt32(table, "finance_order_type")
	f.OrderID = field.NewString(table, "order_id")
	f.FulfillID = field.NewString(table, "fulfill_id")
	f.OrderName = field.NewString(table, "order_name")
	f.TradeType = field.NewString(table, "trade_type")
	f.TradeCategory = field.NewInt32(table, "trade_category")
	f.TenantType = field.NewInt32(table, "tenant_type")
	f.BizScene = field.NewInt32(table, "biz_scene")
	f.AppID = field.NewString(table, "app_id")
	f.MerchantID = field.NewString(table, "merchant_id")
	f.Mid = field.NewString(table, "mid")
	f.Amount = field.NewInt64(table, "amount")
	f.LoanAmount = field.NewInt64(table, "loan_amount")
	f.ProcessAmount = field.NewInt64(table, "process_amount")
	f.OfflineLoanAmount = field.NewInt64(table, "offline_loan_amount")
	f.PlatformPromotionAmount = field.NewInt64(table, "platform_promotion_amount")
	f.PlatformPromotionDetail = field.NewString(table, "platform_promotion_detail")
	f.Status = field.NewInt32(table, "status")
	f.FeeItemDetail = field.NewString(table, "fee_item_detail")
	f.DeductItemDetail = field.NewString(table, "deduct_item_detail")
	f.FeeRecordID = field.NewString(table, "fee_record_id")
	f.CallbackEvent = field.NewString(table, "callback_event")
	f.CallbackExtra = field.NewString(table, "callback_extra")
	f.FinishTime = field.NewTime(table, "finish_time")
	f.CreateTime = field.NewTime(table, "create_time")
	f.UpdateTime = field.NewTime(table, "update_time")

	f.fillFieldMap()

	return f
}

func (f *fFinanceOrder) WithContext(ctx context.Context) *fFinanceOrderDo {
	return f.fFinanceOrderDo.WithContext(ctx)
}

func (f fFinanceOrder) TableName() string { return f.fFinanceOrderDo.TableName() }

func (f fFinanceOrder) Alias() string { return f.fFinanceOrderDo.Alias() }

func (f fFinanceOrder) Columns(cols ...field.Expr) gen.Columns {
	return f.fFinanceOrderDo.Columns(cols...)
}

func (f *fFinanceOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fFinanceOrder) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 28)
	f.fieldMap["id"] = f.ID
	f.fieldMap["finance_order_id"] = f.FinanceOrderID
	f.fieldMap["finance_order_type"] = f.FinanceOrderType
	f.fieldMap["order_id"] = f.OrderID
	f.fieldMap["fulfill_id"] = f.FulfillID
	f.fieldMap["order_name"] = f.OrderName
	f.fieldMap["trade_type"] = f.TradeType
	f.fieldMap["trade_category"] = f.TradeCategory
	f.fieldMap["tenant_type"] = f.TenantType
	f.fieldMap["biz_scene"] = f.BizScene
	f.fieldMap["app_id"] = f.AppID
	f.fieldMap["merchant_id"] = f.MerchantID
	f.fieldMap["mid"] = f.Mid
	f.fieldMap["amount"] = f.Amount
	f.fieldMap["loan_amount"] = f.LoanAmount
	f.fieldMap["process_amount"] = f.ProcessAmount
	f.fieldMap["offline_loan_amount"] = f.OfflineLoanAmount
	f.fieldMap["platform_promotion_amount"] = f.PlatformPromotionAmount
	f.fieldMap["platform_promotion_detail"] = f.PlatformPromotionDetail
	f.fieldMap["status"] = f.Status
	f.fieldMap["fee_item_detail"] = f.FeeItemDetail
	f.fieldMap["deduct_item_detail"] = f.DeductItemDetail
	f.fieldMap["fee_record_id"] = f.FeeRecordID
	f.fieldMap["callback_event"] = f.CallbackEvent
	f.fieldMap["callback_extra"] = f.CallbackExtra
	f.fieldMap["finish_time"] = f.FinishTime
	f.fieldMap["create_time"] = f.CreateTime
	f.fieldMap["update_time"] = f.UpdateTime
}

func (f fFinanceOrder) clone(db *gorm.DB) fFinanceOrder {
	f.fFinanceOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fFinanceOrder) replaceDB(db *gorm.DB) fFinanceOrder {
	f.fFinanceOrderDo.ReplaceDB(db)
	return f
}

type fFinanceOrderDo struct{ gen.DO }

func (f fFinanceOrderDo) Debug() *fFinanceOrderDo {
	return f.withDO(f.DO.Debug())
}

func (f fFinanceOrderDo) WithContext(ctx context.Context) *fFinanceOrderDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fFinanceOrderDo) ReadDB() *fFinanceOrderDo {
	return f.Clauses(dbresolver.Read)
}

func (f fFinanceOrderDo) WriteDB() *fFinanceOrderDo {
	return f.Clauses(dbresolver.Write)
}

func (f fFinanceOrderDo) Session(config *gorm.Session) *fFinanceOrderDo {
	return f.withDO(f.DO.Session(config))
}

func (f fFinanceOrderDo) Clauses(conds ...clause.Expression) *fFinanceOrderDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fFinanceOrderDo) Returning(value interface{}, columns ...string) *fFinanceOrderDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fFinanceOrderDo) Not(conds ...gen.Condition) *fFinanceOrderDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fFinanceOrderDo) Or(conds ...gen.Condition) *fFinanceOrderDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fFinanceOrderDo) Select(conds ...field.Expr) *fFinanceOrderDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fFinanceOrderDo) Where(conds ...gen.Condition) *fFinanceOrderDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fFinanceOrderDo) Order(conds ...field.Expr) *fFinanceOrderDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fFinanceOrderDo) Distinct(cols ...field.Expr) *fFinanceOrderDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fFinanceOrderDo) Omit(cols ...field.Expr) *fFinanceOrderDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fFinanceOrderDo) Join(table schema.Tabler, on ...field.Expr) *fFinanceOrderDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fFinanceOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fFinanceOrderDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fFinanceOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) *fFinanceOrderDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fFinanceOrderDo) Group(cols ...field.Expr) *fFinanceOrderDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fFinanceOrderDo) Having(conds ...gen.Condition) *fFinanceOrderDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fFinanceOrderDo) Limit(limit int) *fFinanceOrderDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fFinanceOrderDo) Offset(offset int) *fFinanceOrderDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fFinanceOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fFinanceOrderDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fFinanceOrderDo) Unscoped() *fFinanceOrderDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fFinanceOrderDo) Create(values ...*db_model.FFinanceOrder) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fFinanceOrderDo) CreateInBatches(values []*db_model.FFinanceOrder, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fFinanceOrderDo) Save(values ...*db_model.FFinanceOrder) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fFinanceOrderDo) First() (*db_model.FFinanceOrder, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FFinanceOrder), nil
	}
}

func (f fFinanceOrderDo) Take() (*db_model.FFinanceOrder, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FFinanceOrder), nil
	}
}

func (f fFinanceOrderDo) Last() (*db_model.FFinanceOrder, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FFinanceOrder), nil
	}
}

func (f fFinanceOrderDo) Find() ([]*db_model.FFinanceOrder, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FFinanceOrder), err
}

func (f fFinanceOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FFinanceOrder, err error) {
	buf := make([]*db_model.FFinanceOrder, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fFinanceOrderDo) FindInBatches(result *[]*db_model.FFinanceOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fFinanceOrderDo) Attrs(attrs ...field.AssignExpr) *fFinanceOrderDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fFinanceOrderDo) Assign(attrs ...field.AssignExpr) *fFinanceOrderDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fFinanceOrderDo) Joins(fields ...field.RelationField) *fFinanceOrderDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fFinanceOrderDo) Preload(fields ...field.RelationField) *fFinanceOrderDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fFinanceOrderDo) FirstOrInit() (*db_model.FFinanceOrder, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FFinanceOrder), nil
	}
}

func (f fFinanceOrderDo) FirstOrCreate() (*db_model.FFinanceOrder, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FFinanceOrder), nil
	}
}

func (f fFinanceOrderDo) FindByPage(offset int, limit int) (result []*db_model.FFinanceOrder, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fFinanceOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fFinanceOrderDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fFinanceOrderDo) Delete(models ...*db_model.FFinanceOrder) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fFinanceOrderDo) withDO(do gen.Dao) *fFinanceOrderDo {
	f.DO = *do.(*gen.DO)
	return f
}
