// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFweOrder(db *gorm.DB, opts ...gen.DOOption) fweOrder {
	_fweOrder := fweOrder{}

	_fweOrder.fweOrderDo.UseDB(db, opts...)
	_fweOrder.fweOrderDo.UseModel(&db_model.FweOrder{})

	tableName := _fweOrder.fweOrderDo.TableName()
	_fweOrder.ALL = field.NewAsterisk(tableName)
	_fweOrder.ID = field.NewInt64(tableName, "id")
	_fweOrder.TenantType = field.NewInt32(tableName, "tenant_type")
	_fweOrder.BizScene = field.NewInt32(tableName, "biz_scene")
	_fweOrder.OrderID = field.NewString(tableName, "order_id")
	_fweOrder.OrderStatus = field.NewInt32(tableName, "order_status")
	_fweOrder.OrderSubStatus = field.NewString(tableName, "order_sub_status")
	_fweOrder.BeforeStatus = field.NewInt32(tableName, "before_status")
	_fweOrder.OrderName = field.NewString(tableName, "order_name")
	_fweOrder.OrderDesc = field.NewString(tableName, "order_desc")
	_fweOrder.ProductID = field.NewString(tableName, "product_id")
	_fweOrder.ProductType = field.NewInt32(tableName, "product_type")
	_fweOrder.ProductName = field.NewString(tableName, "product_name")
	_fweOrder.ProductDetail = field.NewString(tableName, "product_detail")
	_fweOrder.ProductExtra = field.NewString(tableName, "product_extra")
	_fweOrder.ProductVersion = field.NewInt64(tableName, "product_version")
	_fweOrder.SkuID = field.NewString(tableName, "sku_id")
	_fweOrder.SkuVersion = field.NewInt64(tableName, "sku_version")
	_fweOrder.ProductQuantity = field.NewInt32(tableName, "product_quantity")
	_fweOrder.ProductUnitPrice = field.NewInt64(tableName, "product_unit_price")
	_fweOrder.TotalAmount = field.NewInt64(tableName, "total_amount")
	_fweOrder.TotalPayAmount = field.NewInt64(tableName, "total_pay_amount")
	_fweOrder.TotalSubsidyAmount = field.NewInt64(tableName, "total_subsidy_amount")
	_fweOrder.TradeType = field.NewInt32(tableName, "trade_type")
	_fweOrder.UID = field.NewInt64(tableName, "uid")
	_fweOrder.MobileID = field.NewInt64(tableName, "mobile_id")
	_fweOrder.BuyerID = field.NewString(tableName, "buyer_id")
	_fweOrder.BuyerExtra = field.NewString(tableName, "buyer_extra")
	_fweOrder.SellerID = field.NewString(tableName, "seller_id")
	_fweOrder.SellerExtra = field.NewString(tableName, "seller_extra")
	_fweOrder.ServiceProviderID = field.NewString(tableName, "service_provider_id")
	_fweOrder.ServiceProviderExtra = field.NewString(tableName, "service_provider_extra")
	_fweOrder.PurchasePlan = field.NewString(tableName, "purchase_plan")
	_fweOrder.TradeOption = field.NewString(tableName, "trade_option")
	_fweOrder.TalentID = field.NewString(tableName, "talent_id")
	_fweOrder.TalentExtra = field.NewString(tableName, "talent_extra")
	_fweOrder.IsTest = field.NewInt32(tableName, "is_test")
	_fweOrder.FinishTime = field.NewTime(tableName, "finish_time")
	_fweOrder.CreateTime = field.NewTime(tableName, "create_time")
	_fweOrder.Creator = field.NewString(tableName, "creator")
	_fweOrder.CreatorName = field.NewString(tableName, "creator_name")
	_fweOrder.UpdateTime = field.NewTime(tableName, "update_time")
	_fweOrder.Operator = field.NewString(tableName, "operator")
	_fweOrder.OperatorName = field.NewString(tableName, "operator_name")
	_fweOrder.IdempotentID = field.NewString(tableName, "idempotent_id")
	_fweOrder.SmVersion = field.NewInt32(tableName, "sm_version")
	_fweOrder.SnapshotContent = field.NewString(tableName, "snapshot_content")

	_fweOrder.fillFieldMap()

	return _fweOrder
}

// fweOrder 交易基建订单表
type fweOrder struct {
	fweOrderDo fweOrderDo

	ALL        field.Asterisk
	ID         field.Int64 // 主键id
	TenantType field.Int32 // 租户类型
	/*
		业务场景
		（新车门店业务）2201:大定 2202:小订 2102:返佣无水平 2101:返佣有水平 2301:采购单
	*/
	BizScene             field.Int32
	OrderID              field.String // 基建订单号
	OrderStatus          field.Int32  // 订单状态
	OrderSubStatus       field.String // 订单平行子状态
	BeforeStatus         field.Int32  // 订单前一个状态
	OrderName            field.String // 订单名
	OrderDesc            field.String // 订单描述
	ProductID            field.String // 商品id
	ProductType          field.Int32  // 商品类型
	ProductName          field.String // 商品名称
	ProductDetail        field.String // 商品详情
	ProductExtra         field.String // 商品扩展
	ProductVersion       field.Int64  // 商品版本
	SkuID                field.String // skucode
	SkuVersion           field.Int64  // sku版本
	ProductQuantity      field.Int32  // 商品数量
	ProductUnitPrice     field.Int64  // 商品单价 单位分
	TotalAmount          field.Int64  // 订单总金额，单位是分
	TotalPayAmount       field.Int64  // 总支付金额 ，单位分
	TotalSubsidyAmount   field.Int64  // 优惠总金额，单位分
	TradeType            field.Int32  // 交易类型，由bizScene 指定
	UID                  field.Int64  // uid
	MobileID             field.Int64  // mobile_id
	BuyerID              field.String // 买家id
	BuyerExtra           field.String // 买家冗余信息
	SellerID             field.String // 卖家id
	SellerExtra          field.String // 卖家冗余信息
	ServiceProviderID    field.String // 服务商id
	ServiceProviderExtra field.String // 服务商冗余信息
	PurchasePlan         field.String // 购车方案
	TradeOption          field.String // 交易策略可选配置
	TalentID             field.String // 达人id
	TalentExtra          field.String // 达人冗余信息
	IsTest               field.Int32  // 是否测试，1-是，0-不是
	FinishTime           field.Time   // 完成时间(终止态时间)
	CreateTime           field.Time   // 创建时间
	Creator              field.String // 创建人id
	CreatorName          field.String // 创建人名称
	UpdateTime           field.Time   // 更新时间
	Operator             field.String // 更新人id
	OperatorName         field.String // 更新人名称
	IdempotentID         field.String // 幂等号
	SmVersion            field.Int32  // 场景版本
	SnapshotContent      field.String // 订单快照

	fieldMap map[string]field.Expr
}

func (f fweOrder) Table(newTableName string) *fweOrder {
	f.fweOrderDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fweOrder) As(alias string) *fweOrder {
	f.fweOrderDo.DO = *(f.fweOrderDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fweOrder) updateTableName(table string) *fweOrder {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.TenantType = field.NewInt32(table, "tenant_type")
	f.BizScene = field.NewInt32(table, "biz_scene")
	f.OrderID = field.NewString(table, "order_id")
	f.OrderStatus = field.NewInt32(table, "order_status")
	f.OrderSubStatus = field.NewString(table, "order_sub_status")
	f.BeforeStatus = field.NewInt32(table, "before_status")
	f.OrderName = field.NewString(table, "order_name")
	f.OrderDesc = field.NewString(table, "order_desc")
	f.ProductID = field.NewString(table, "product_id")
	f.ProductType = field.NewInt32(table, "product_type")
	f.ProductName = field.NewString(table, "product_name")
	f.ProductDetail = field.NewString(table, "product_detail")
	f.ProductExtra = field.NewString(table, "product_extra")
	f.ProductVersion = field.NewInt64(table, "product_version")
	f.SkuID = field.NewString(table, "sku_id")
	f.SkuVersion = field.NewInt64(table, "sku_version")
	f.ProductQuantity = field.NewInt32(table, "product_quantity")
	f.ProductUnitPrice = field.NewInt64(table, "product_unit_price")
	f.TotalAmount = field.NewInt64(table, "total_amount")
	f.TotalPayAmount = field.NewInt64(table, "total_pay_amount")
	f.TotalSubsidyAmount = field.NewInt64(table, "total_subsidy_amount")
	f.TradeType = field.NewInt32(table, "trade_type")
	f.UID = field.NewInt64(table, "uid")
	f.MobileID = field.NewInt64(table, "mobile_id")
	f.BuyerID = field.NewString(table, "buyer_id")
	f.BuyerExtra = field.NewString(table, "buyer_extra")
	f.SellerID = field.NewString(table, "seller_id")
	f.SellerExtra = field.NewString(table, "seller_extra")
	f.ServiceProviderID = field.NewString(table, "service_provider_id")
	f.ServiceProviderExtra = field.NewString(table, "service_provider_extra")
	f.PurchasePlan = field.NewString(table, "purchase_plan")
	f.TradeOption = field.NewString(table, "trade_option")
	f.TalentID = field.NewString(table, "talent_id")
	f.TalentExtra = field.NewString(table, "talent_extra")
	f.IsTest = field.NewInt32(table, "is_test")
	f.FinishTime = field.NewTime(table, "finish_time")
	f.CreateTime = field.NewTime(table, "create_time")
	f.Creator = field.NewString(table, "creator")
	f.CreatorName = field.NewString(table, "creator_name")
	f.UpdateTime = field.NewTime(table, "update_time")
	f.Operator = field.NewString(table, "operator")
	f.OperatorName = field.NewString(table, "operator_name")
	f.IdempotentID = field.NewString(table, "idempotent_id")
	f.SmVersion = field.NewInt32(table, "sm_version")
	f.SnapshotContent = field.NewString(table, "snapshot_content")

	f.fillFieldMap()

	return f
}

func (f *fweOrder) WithContext(ctx context.Context) *fweOrderDo { return f.fweOrderDo.WithContext(ctx) }

func (f fweOrder) TableName() string { return f.fweOrderDo.TableName() }

func (f fweOrder) Alias() string { return f.fweOrderDo.Alias() }

func (f fweOrder) Columns(cols ...field.Expr) gen.Columns { return f.fweOrderDo.Columns(cols...) }

func (f *fweOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fweOrder) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 46)
	f.fieldMap["id"] = f.ID
	f.fieldMap["tenant_type"] = f.TenantType
	f.fieldMap["biz_scene"] = f.BizScene
	f.fieldMap["order_id"] = f.OrderID
	f.fieldMap["order_status"] = f.OrderStatus
	f.fieldMap["order_sub_status"] = f.OrderSubStatus
	f.fieldMap["before_status"] = f.BeforeStatus
	f.fieldMap["order_name"] = f.OrderName
	f.fieldMap["order_desc"] = f.OrderDesc
	f.fieldMap["product_id"] = f.ProductID
	f.fieldMap["product_type"] = f.ProductType
	f.fieldMap["product_name"] = f.ProductName
	f.fieldMap["product_detail"] = f.ProductDetail
	f.fieldMap["product_extra"] = f.ProductExtra
	f.fieldMap["product_version"] = f.ProductVersion
	f.fieldMap["sku_id"] = f.SkuID
	f.fieldMap["sku_version"] = f.SkuVersion
	f.fieldMap["product_quantity"] = f.ProductQuantity
	f.fieldMap["product_unit_price"] = f.ProductUnitPrice
	f.fieldMap["total_amount"] = f.TotalAmount
	f.fieldMap["total_pay_amount"] = f.TotalPayAmount
	f.fieldMap["total_subsidy_amount"] = f.TotalSubsidyAmount
	f.fieldMap["trade_type"] = f.TradeType
	f.fieldMap["uid"] = f.UID
	f.fieldMap["mobile_id"] = f.MobileID
	f.fieldMap["buyer_id"] = f.BuyerID
	f.fieldMap["buyer_extra"] = f.BuyerExtra
	f.fieldMap["seller_id"] = f.SellerID
	f.fieldMap["seller_extra"] = f.SellerExtra
	f.fieldMap["service_provider_id"] = f.ServiceProviderID
	f.fieldMap["service_provider_extra"] = f.ServiceProviderExtra
	f.fieldMap["purchase_plan"] = f.PurchasePlan
	f.fieldMap["trade_option"] = f.TradeOption
	f.fieldMap["talent_id"] = f.TalentID
	f.fieldMap["talent_extra"] = f.TalentExtra
	f.fieldMap["is_test"] = f.IsTest
	f.fieldMap["finish_time"] = f.FinishTime
	f.fieldMap["create_time"] = f.CreateTime
	f.fieldMap["creator"] = f.Creator
	f.fieldMap["creator_name"] = f.CreatorName
	f.fieldMap["update_time"] = f.UpdateTime
	f.fieldMap["operator"] = f.Operator
	f.fieldMap["operator_name"] = f.OperatorName
	f.fieldMap["idempotent_id"] = f.IdempotentID
	f.fieldMap["sm_version"] = f.SmVersion
	f.fieldMap["snapshot_content"] = f.SnapshotContent
}

func (f fweOrder) clone(db *gorm.DB) fweOrder {
	f.fweOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fweOrder) replaceDB(db *gorm.DB) fweOrder {
	f.fweOrderDo.ReplaceDB(db)
	return f
}

type fweOrderDo struct{ gen.DO }

func (f fweOrderDo) Debug() *fweOrderDo {
	return f.withDO(f.DO.Debug())
}

func (f fweOrderDo) WithContext(ctx context.Context) *fweOrderDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fweOrderDo) ReadDB() *fweOrderDo {
	return f.Clauses(dbresolver.Read)
}

func (f fweOrderDo) WriteDB() *fweOrderDo {
	return f.Clauses(dbresolver.Write)
}

func (f fweOrderDo) Session(config *gorm.Session) *fweOrderDo {
	return f.withDO(f.DO.Session(config))
}

func (f fweOrderDo) Clauses(conds ...clause.Expression) *fweOrderDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fweOrderDo) Returning(value interface{}, columns ...string) *fweOrderDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fweOrderDo) Not(conds ...gen.Condition) *fweOrderDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fweOrderDo) Or(conds ...gen.Condition) *fweOrderDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fweOrderDo) Select(conds ...field.Expr) *fweOrderDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fweOrderDo) Where(conds ...gen.Condition) *fweOrderDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fweOrderDo) Order(conds ...field.Expr) *fweOrderDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fweOrderDo) Distinct(cols ...field.Expr) *fweOrderDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fweOrderDo) Omit(cols ...field.Expr) *fweOrderDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fweOrderDo) Join(table schema.Tabler, on ...field.Expr) *fweOrderDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fweOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fweOrderDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fweOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) *fweOrderDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fweOrderDo) Group(cols ...field.Expr) *fweOrderDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fweOrderDo) Having(conds ...gen.Condition) *fweOrderDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fweOrderDo) Limit(limit int) *fweOrderDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fweOrderDo) Offset(offset int) *fweOrderDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fweOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fweOrderDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fweOrderDo) Unscoped() *fweOrderDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fweOrderDo) Create(values ...*db_model.FweOrder) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fweOrderDo) CreateInBatches(values []*db_model.FweOrder, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fweOrderDo) Save(values ...*db_model.FweOrder) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fweOrderDo) First() (*db_model.FweOrder, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrder), nil
	}
}

func (f fweOrderDo) Take() (*db_model.FweOrder, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrder), nil
	}
}

func (f fweOrderDo) Last() (*db_model.FweOrder, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrder), nil
	}
}

func (f fweOrderDo) Find() ([]*db_model.FweOrder, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FweOrder), err
}

func (f fweOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FweOrder, err error) {
	buf := make([]*db_model.FweOrder, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fweOrderDo) FindInBatches(result *[]*db_model.FweOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fweOrderDo) Attrs(attrs ...field.AssignExpr) *fweOrderDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fweOrderDo) Assign(attrs ...field.AssignExpr) *fweOrderDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fweOrderDo) Joins(fields ...field.RelationField) *fweOrderDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fweOrderDo) Preload(fields ...field.RelationField) *fweOrderDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fweOrderDo) FirstOrInit() (*db_model.FweOrder, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrder), nil
	}
}

func (f fweOrderDo) FirstOrCreate() (*db_model.FweOrder, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrder), nil
	}
}

func (f fweOrderDo) FindByPage(offset int, limit int) (result []*db_model.FweOrder, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fweOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fweOrderDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fweOrderDo) Delete(models ...*db_model.FweOrder) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fweOrderDo) withDO(do gen.Dao) *fweOrderDo {
	f.DO = *do.(*gen.DO)
	return f
}
