// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFweOrderContract(db *gorm.DB, opts ...gen.DOOption) fweOrderContract {
	_fweOrderContract := fweOrderContract{}

	_fweOrderContract.fweOrderContractDo.UseDB(db, opts...)
	_fweOrderContract.fweOrderContractDo.UseModel(&db_model.FweOrderContract{})

	tableName := _fweOrderContract.fweOrderContractDo.TableName()
	_fweOrderContract.ALL = field.NewAsterisk(tableName)
	_fweOrderContract.ID = field.NewInt64(tableName, "id")
	_fweOrderContract.OrderID = field.NewString(tableName, "order_id")
	_fweOrderContract.FulfillID = field.NewString(tableName, "fulfill_id")
	_fweOrderContract.AfterSaleID = field.NewString(tableName, "after_sale_id")
	_fweOrderContract.OrderType = field.NewInt32(tableName, "order_type")
	_fweOrderContract.ContractType = field.NewInt32(tableName, "contract_type")
	_fweOrderContract.ContractNo = field.NewString(tableName, "contract_no")
	_fweOrderContract.InfraContSerial = field.NewString(tableName, "infra_cont_serial")
	_fweOrderContract.InfraContName = field.NewString(tableName, "infra_cont_name")
	_fweOrderContract.Status = field.NewInt32(tableName, "status")
	_fweOrderContract.SignTime = field.NewTime(tableName, "sign_time")
	_fweOrderContract.Creator = field.NewInt64(tableName, "creator")
	_fweOrderContract.CreatorName = field.NewString(tableName, "creator_name")
	_fweOrderContract.CreateTime = field.NewTime(tableName, "create_time")
	_fweOrderContract.Operator = field.NewInt64(tableName, "operator")
	_fweOrderContract.OperatorName = field.NewString(tableName, "operator_name")
	_fweOrderContract.UpdateTime = field.NewTime(tableName, "update_time")

	_fweOrderContract.fillFieldMap()

	return _fweOrderContract
}

// fweOrderContract 订单-合同关联表
type fweOrderContract struct {
	fweOrderContractDo fweOrderContractDo

	ALL             field.Asterisk
	ID              field.Int64  // 主键
	OrderID         field.String // 订单id
	FulfillID       field.String // 履约订单号
	AfterSaleID     field.String // 售后订单号
	OrderType       field.Int32  // 关联订单类型
	ContractType    field.Int32  // 合同类型
	ContractNo      field.String // 合同编码,（订单id +合同类型）
	InfraContSerial field.String // 基建合同编码
	InfraContName   field.String // 基建合同名称
	Status          field.Int32  // 状态：0-待处理，1-处理中，2-处理完成
	SignTime        field.Time   // 签署时间
	Creator         field.Int64  // 创建人
	CreatorName     field.String // 创建人名称
	CreateTime      field.Time   // 创建时间
	Operator        field.Int64  // 操作人
	OperatorName    field.String // 操作人名称
	UpdateTime      field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (f fweOrderContract) Table(newTableName string) *fweOrderContract {
	f.fweOrderContractDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fweOrderContract) As(alias string) *fweOrderContract {
	f.fweOrderContractDo.DO = *(f.fweOrderContractDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fweOrderContract) updateTableName(table string) *fweOrderContract {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.OrderID = field.NewString(table, "order_id")
	f.FulfillID = field.NewString(table, "fulfill_id")
	f.AfterSaleID = field.NewString(table, "after_sale_id")
	f.OrderType = field.NewInt32(table, "order_type")
	f.ContractType = field.NewInt32(table, "contract_type")
	f.ContractNo = field.NewString(table, "contract_no")
	f.InfraContSerial = field.NewString(table, "infra_cont_serial")
	f.InfraContName = field.NewString(table, "infra_cont_name")
	f.Status = field.NewInt32(table, "status")
	f.SignTime = field.NewTime(table, "sign_time")
	f.Creator = field.NewInt64(table, "creator")
	f.CreatorName = field.NewString(table, "creator_name")
	f.CreateTime = field.NewTime(table, "create_time")
	f.Operator = field.NewInt64(table, "operator")
	f.OperatorName = field.NewString(table, "operator_name")
	f.UpdateTime = field.NewTime(table, "update_time")

	f.fillFieldMap()

	return f
}

func (f *fweOrderContract) WithContext(ctx context.Context) *fweOrderContractDo {
	return f.fweOrderContractDo.WithContext(ctx)
}

func (f fweOrderContract) TableName() string { return f.fweOrderContractDo.TableName() }

func (f fweOrderContract) Alias() string { return f.fweOrderContractDo.Alias() }

func (f fweOrderContract) Columns(cols ...field.Expr) gen.Columns {
	return f.fweOrderContractDo.Columns(cols...)
}

func (f *fweOrderContract) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fweOrderContract) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 17)
	f.fieldMap["id"] = f.ID
	f.fieldMap["order_id"] = f.OrderID
	f.fieldMap["fulfill_id"] = f.FulfillID
	f.fieldMap["after_sale_id"] = f.AfterSaleID
	f.fieldMap["order_type"] = f.OrderType
	f.fieldMap["contract_type"] = f.ContractType
	f.fieldMap["contract_no"] = f.ContractNo
	f.fieldMap["infra_cont_serial"] = f.InfraContSerial
	f.fieldMap["infra_cont_name"] = f.InfraContName
	f.fieldMap["status"] = f.Status
	f.fieldMap["sign_time"] = f.SignTime
	f.fieldMap["creator"] = f.Creator
	f.fieldMap["creator_name"] = f.CreatorName
	f.fieldMap["create_time"] = f.CreateTime
	f.fieldMap["operator"] = f.Operator
	f.fieldMap["operator_name"] = f.OperatorName
	f.fieldMap["update_time"] = f.UpdateTime
}

func (f fweOrderContract) clone(db *gorm.DB) fweOrderContract {
	f.fweOrderContractDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fweOrderContract) replaceDB(db *gorm.DB) fweOrderContract {
	f.fweOrderContractDo.ReplaceDB(db)
	return f
}

type fweOrderContractDo struct{ gen.DO }

func (f fweOrderContractDo) Debug() *fweOrderContractDo {
	return f.withDO(f.DO.Debug())
}

func (f fweOrderContractDo) WithContext(ctx context.Context) *fweOrderContractDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fweOrderContractDo) ReadDB() *fweOrderContractDo {
	return f.Clauses(dbresolver.Read)
}

func (f fweOrderContractDo) WriteDB() *fweOrderContractDo {
	return f.Clauses(dbresolver.Write)
}

func (f fweOrderContractDo) Session(config *gorm.Session) *fweOrderContractDo {
	return f.withDO(f.DO.Session(config))
}

func (f fweOrderContractDo) Clauses(conds ...clause.Expression) *fweOrderContractDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fweOrderContractDo) Returning(value interface{}, columns ...string) *fweOrderContractDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fweOrderContractDo) Not(conds ...gen.Condition) *fweOrderContractDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fweOrderContractDo) Or(conds ...gen.Condition) *fweOrderContractDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fweOrderContractDo) Select(conds ...field.Expr) *fweOrderContractDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fweOrderContractDo) Where(conds ...gen.Condition) *fweOrderContractDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fweOrderContractDo) Order(conds ...field.Expr) *fweOrderContractDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fweOrderContractDo) Distinct(cols ...field.Expr) *fweOrderContractDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fweOrderContractDo) Omit(cols ...field.Expr) *fweOrderContractDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fweOrderContractDo) Join(table schema.Tabler, on ...field.Expr) *fweOrderContractDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fweOrderContractDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fweOrderContractDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fweOrderContractDo) RightJoin(table schema.Tabler, on ...field.Expr) *fweOrderContractDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fweOrderContractDo) Group(cols ...field.Expr) *fweOrderContractDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fweOrderContractDo) Having(conds ...gen.Condition) *fweOrderContractDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fweOrderContractDo) Limit(limit int) *fweOrderContractDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fweOrderContractDo) Offset(offset int) *fweOrderContractDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fweOrderContractDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fweOrderContractDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fweOrderContractDo) Unscoped() *fweOrderContractDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fweOrderContractDo) Create(values ...*db_model.FweOrderContract) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fweOrderContractDo) CreateInBatches(values []*db_model.FweOrderContract, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fweOrderContractDo) Save(values ...*db_model.FweOrderContract) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fweOrderContractDo) First() (*db_model.FweOrderContract, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderContract), nil
	}
}

func (f fweOrderContractDo) Take() (*db_model.FweOrderContract, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderContract), nil
	}
}

func (f fweOrderContractDo) Last() (*db_model.FweOrderContract, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderContract), nil
	}
}

func (f fweOrderContractDo) Find() ([]*db_model.FweOrderContract, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FweOrderContract), err
}

func (f fweOrderContractDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FweOrderContract, err error) {
	buf := make([]*db_model.FweOrderContract, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fweOrderContractDo) FindInBatches(result *[]*db_model.FweOrderContract, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fweOrderContractDo) Attrs(attrs ...field.AssignExpr) *fweOrderContractDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fweOrderContractDo) Assign(attrs ...field.AssignExpr) *fweOrderContractDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fweOrderContractDo) Joins(fields ...field.RelationField) *fweOrderContractDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fweOrderContractDo) Preload(fields ...field.RelationField) *fweOrderContractDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fweOrderContractDo) FirstOrInit() (*db_model.FweOrderContract, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderContract), nil
	}
}

func (f fweOrderContractDo) FirstOrCreate() (*db_model.FweOrderContract, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderContract), nil
	}
}

func (f fweOrderContractDo) FindByPage(offset int, limit int) (result []*db_model.FweOrderContract, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fweOrderContractDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fweOrderContractDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fweOrderContractDo) Delete(models ...*db_model.FweOrderContract) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fweOrderContractDo) withDO(do gen.Dao) *fweOrderContractDo {
	f.DO = *do.(*gen.DO)
	return f
}
