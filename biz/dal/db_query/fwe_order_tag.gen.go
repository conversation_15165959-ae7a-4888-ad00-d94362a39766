// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFweOrderTag(db *gorm.DB, opts ...gen.DOOption) fweOrderTag {
	_fweOrderTag := fweOrderTag{}

	_fweOrderTag.fweOrderTagDo.UseDB(db, opts...)
	_fweOrderTag.fweOrderTagDo.UseModel(&db_model.FweOrderTag{})

	tableName := _fweOrderTag.fweOrderTagDo.TableName()
	_fweOrderTag.ALL = field.NewAsterisk(tableName)
	_fweOrderTag.ID = field.NewInt64(tableName, "id")
	_fweOrderTag.OrderID = field.NewString(tableName, "order_id")
	_fweOrderTag.Tag = field.NewString(tableName, "tag")
	_fweOrderTag.BizExtra = field.NewString(tableName, "biz_extra")

	_fweOrderTag.fillFieldMap()

	return _fweOrderTag
}

// fweOrderTag 交易基建订单扩展表
type fweOrderTag struct {
	fweOrderTagDo fweOrderTagDo

	ALL      field.Asterisk
	ID       field.Int64  // 主键id
	OrderID  field.String // 基建订单号
	Tag      field.String // 订单扩展字段
	BizExtra field.String // 业务订单透传字段

	fieldMap map[string]field.Expr
}

func (f fweOrderTag) Table(newTableName string) *fweOrderTag {
	f.fweOrderTagDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fweOrderTag) As(alias string) *fweOrderTag {
	f.fweOrderTagDo.DO = *(f.fweOrderTagDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fweOrderTag) updateTableName(table string) *fweOrderTag {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.OrderID = field.NewString(table, "order_id")
	f.Tag = field.NewString(table, "tag")
	f.BizExtra = field.NewString(table, "biz_extra")

	f.fillFieldMap()

	return f
}

func (f *fweOrderTag) WithContext(ctx context.Context) *fweOrderTagDo {
	return f.fweOrderTagDo.WithContext(ctx)
}

func (f fweOrderTag) TableName() string { return f.fweOrderTagDo.TableName() }

func (f fweOrderTag) Alias() string { return f.fweOrderTagDo.Alias() }

func (f fweOrderTag) Columns(cols ...field.Expr) gen.Columns { return f.fweOrderTagDo.Columns(cols...) }

func (f *fweOrderTag) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fweOrderTag) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 4)
	f.fieldMap["id"] = f.ID
	f.fieldMap["order_id"] = f.OrderID
	f.fieldMap["tag"] = f.Tag
	f.fieldMap["biz_extra"] = f.BizExtra
}

func (f fweOrderTag) clone(db *gorm.DB) fweOrderTag {
	f.fweOrderTagDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fweOrderTag) replaceDB(db *gorm.DB) fweOrderTag {
	f.fweOrderTagDo.ReplaceDB(db)
	return f
}

type fweOrderTagDo struct{ gen.DO }

func (f fweOrderTagDo) Debug() *fweOrderTagDo {
	return f.withDO(f.DO.Debug())
}

func (f fweOrderTagDo) WithContext(ctx context.Context) *fweOrderTagDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fweOrderTagDo) ReadDB() *fweOrderTagDo {
	return f.Clauses(dbresolver.Read)
}

func (f fweOrderTagDo) WriteDB() *fweOrderTagDo {
	return f.Clauses(dbresolver.Write)
}

func (f fweOrderTagDo) Session(config *gorm.Session) *fweOrderTagDo {
	return f.withDO(f.DO.Session(config))
}

func (f fweOrderTagDo) Clauses(conds ...clause.Expression) *fweOrderTagDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fweOrderTagDo) Returning(value interface{}, columns ...string) *fweOrderTagDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fweOrderTagDo) Not(conds ...gen.Condition) *fweOrderTagDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fweOrderTagDo) Or(conds ...gen.Condition) *fweOrderTagDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fweOrderTagDo) Select(conds ...field.Expr) *fweOrderTagDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fweOrderTagDo) Where(conds ...gen.Condition) *fweOrderTagDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fweOrderTagDo) Order(conds ...field.Expr) *fweOrderTagDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fweOrderTagDo) Distinct(cols ...field.Expr) *fweOrderTagDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fweOrderTagDo) Omit(cols ...field.Expr) *fweOrderTagDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fweOrderTagDo) Join(table schema.Tabler, on ...field.Expr) *fweOrderTagDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fweOrderTagDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fweOrderTagDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fweOrderTagDo) RightJoin(table schema.Tabler, on ...field.Expr) *fweOrderTagDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fweOrderTagDo) Group(cols ...field.Expr) *fweOrderTagDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fweOrderTagDo) Having(conds ...gen.Condition) *fweOrderTagDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fweOrderTagDo) Limit(limit int) *fweOrderTagDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fweOrderTagDo) Offset(offset int) *fweOrderTagDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fweOrderTagDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fweOrderTagDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fweOrderTagDo) Unscoped() *fweOrderTagDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fweOrderTagDo) Create(values ...*db_model.FweOrderTag) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fweOrderTagDo) CreateInBatches(values []*db_model.FweOrderTag, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fweOrderTagDo) Save(values ...*db_model.FweOrderTag) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fweOrderTagDo) First() (*db_model.FweOrderTag, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderTag), nil
	}
}

func (f fweOrderTagDo) Take() (*db_model.FweOrderTag, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderTag), nil
	}
}

func (f fweOrderTagDo) Last() (*db_model.FweOrderTag, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderTag), nil
	}
}

func (f fweOrderTagDo) Find() ([]*db_model.FweOrderTag, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FweOrderTag), err
}

func (f fweOrderTagDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FweOrderTag, err error) {
	buf := make([]*db_model.FweOrderTag, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fweOrderTagDo) FindInBatches(result *[]*db_model.FweOrderTag, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fweOrderTagDo) Attrs(attrs ...field.AssignExpr) *fweOrderTagDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fweOrderTagDo) Assign(attrs ...field.AssignExpr) *fweOrderTagDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fweOrderTagDo) Joins(fields ...field.RelationField) *fweOrderTagDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fweOrderTagDo) Preload(fields ...field.RelationField) *fweOrderTagDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fweOrderTagDo) FirstOrInit() (*db_model.FweOrderTag, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderTag), nil
	}
}

func (f fweOrderTagDo) FirstOrCreate() (*db_model.FweOrderTag, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderTag), nil
	}
}

func (f fweOrderTagDo) FindByPage(offset int, limit int) (result []*db_model.FweOrderTag, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fweOrderTagDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fweOrderTagDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fweOrderTagDo) Delete(models ...*db_model.FweOrderTag) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fweOrderTagDo) withDO(do gen.Dao) *fweOrderTagDo {
	f.DO = *do.(*gen.DO)
	return f
}
