// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFweOrderSubject(db *gorm.DB, opts ...gen.DOOption) fweOrderSubject {
	_fweOrderSubject := fweOrderSubject{}

	_fweOrderSubject.fweOrderSubjectDo.UseDB(db, opts...)
	_fweOrderSubject.fweOrderSubjectDo.UseModel(&db_model.FweOrderSubject{})

	tableName := _fweOrderSubject.fweOrderSubjectDo.TableName()
	_fweOrderSubject.ALL = field.NewAsterisk(tableName)
	_fweOrderSubject.ID = field.NewInt64(tableName, "id")
	_fweOrderSubject.SubjectID = field.NewString(tableName, "subject_id")
	_fweOrderSubject.SubjectKey = field.NewString(tableName, "subject_key")
	_fweOrderSubject.SubjectName = field.NewString(tableName, "subject_name")
	_fweOrderSubject.UID = field.NewString(tableName, "uid")
	_fweOrderSubject.EbsKey = field.NewString(tableName, "ebs_key")
	_fweOrderSubject.CreateTime = field.NewTime(tableName, "create_time")
	_fweOrderSubject.UpdateTime = field.NewTime(tableName, "update_time")

	_fweOrderSubject.fillFieldMap()

	return _fweOrderSubject
}

// fweOrderSubject 基建订单主体表
type fweOrderSubject struct {
	fweOrderSubjectDo fweOrderSubjectDo

	ALL         field.Asterisk
	ID          field.Int64  // 主键id
	SubjectID   field.String // 订单表上buyer_id/seller_id
	SubjectKey  field.String // 主体key最长30
	SubjectName field.String // 主体名称
	UID         field.String // 和财经交互的uid
	EbsKey      field.String // ebsKey， ATEG_前缀
	CreateTime  field.Time   // 创建时间
	UpdateTime  field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (f fweOrderSubject) Table(newTableName string) *fweOrderSubject {
	f.fweOrderSubjectDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fweOrderSubject) As(alias string) *fweOrderSubject {
	f.fweOrderSubjectDo.DO = *(f.fweOrderSubjectDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fweOrderSubject) updateTableName(table string) *fweOrderSubject {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.SubjectID = field.NewString(table, "subject_id")
	f.SubjectKey = field.NewString(table, "subject_key")
	f.SubjectName = field.NewString(table, "subject_name")
	f.UID = field.NewString(table, "uid")
	f.EbsKey = field.NewString(table, "ebs_key")
	f.CreateTime = field.NewTime(table, "create_time")
	f.UpdateTime = field.NewTime(table, "update_time")

	f.fillFieldMap()

	return f
}

func (f *fweOrderSubject) WithContext(ctx context.Context) *fweOrderSubjectDo {
	return f.fweOrderSubjectDo.WithContext(ctx)
}

func (f fweOrderSubject) TableName() string { return f.fweOrderSubjectDo.TableName() }

func (f fweOrderSubject) Alias() string { return f.fweOrderSubjectDo.Alias() }

func (f fweOrderSubject) Columns(cols ...field.Expr) gen.Columns {
	return f.fweOrderSubjectDo.Columns(cols...)
}

func (f *fweOrderSubject) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fweOrderSubject) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 8)
	f.fieldMap["id"] = f.ID
	f.fieldMap["subject_id"] = f.SubjectID
	f.fieldMap["subject_key"] = f.SubjectKey
	f.fieldMap["subject_name"] = f.SubjectName
	f.fieldMap["uid"] = f.UID
	f.fieldMap["ebs_key"] = f.EbsKey
	f.fieldMap["create_time"] = f.CreateTime
	f.fieldMap["update_time"] = f.UpdateTime
}

func (f fweOrderSubject) clone(db *gorm.DB) fweOrderSubject {
	f.fweOrderSubjectDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fweOrderSubject) replaceDB(db *gorm.DB) fweOrderSubject {
	f.fweOrderSubjectDo.ReplaceDB(db)
	return f
}

type fweOrderSubjectDo struct{ gen.DO }

func (f fweOrderSubjectDo) Debug() *fweOrderSubjectDo {
	return f.withDO(f.DO.Debug())
}

func (f fweOrderSubjectDo) WithContext(ctx context.Context) *fweOrderSubjectDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fweOrderSubjectDo) ReadDB() *fweOrderSubjectDo {
	return f.Clauses(dbresolver.Read)
}

func (f fweOrderSubjectDo) WriteDB() *fweOrderSubjectDo {
	return f.Clauses(dbresolver.Write)
}

func (f fweOrderSubjectDo) Session(config *gorm.Session) *fweOrderSubjectDo {
	return f.withDO(f.DO.Session(config))
}

func (f fweOrderSubjectDo) Clauses(conds ...clause.Expression) *fweOrderSubjectDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fweOrderSubjectDo) Returning(value interface{}, columns ...string) *fweOrderSubjectDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fweOrderSubjectDo) Not(conds ...gen.Condition) *fweOrderSubjectDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fweOrderSubjectDo) Or(conds ...gen.Condition) *fweOrderSubjectDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fweOrderSubjectDo) Select(conds ...field.Expr) *fweOrderSubjectDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fweOrderSubjectDo) Where(conds ...gen.Condition) *fweOrderSubjectDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fweOrderSubjectDo) Order(conds ...field.Expr) *fweOrderSubjectDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fweOrderSubjectDo) Distinct(cols ...field.Expr) *fweOrderSubjectDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fweOrderSubjectDo) Omit(cols ...field.Expr) *fweOrderSubjectDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fweOrderSubjectDo) Join(table schema.Tabler, on ...field.Expr) *fweOrderSubjectDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fweOrderSubjectDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fweOrderSubjectDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fweOrderSubjectDo) RightJoin(table schema.Tabler, on ...field.Expr) *fweOrderSubjectDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fweOrderSubjectDo) Group(cols ...field.Expr) *fweOrderSubjectDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fweOrderSubjectDo) Having(conds ...gen.Condition) *fweOrderSubjectDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fweOrderSubjectDo) Limit(limit int) *fweOrderSubjectDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fweOrderSubjectDo) Offset(offset int) *fweOrderSubjectDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fweOrderSubjectDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fweOrderSubjectDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fweOrderSubjectDo) Unscoped() *fweOrderSubjectDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fweOrderSubjectDo) Create(values ...*db_model.FweOrderSubject) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fweOrderSubjectDo) CreateInBatches(values []*db_model.FweOrderSubject, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fweOrderSubjectDo) Save(values ...*db_model.FweOrderSubject) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fweOrderSubjectDo) First() (*db_model.FweOrderSubject, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderSubject), nil
	}
}

func (f fweOrderSubjectDo) Take() (*db_model.FweOrderSubject, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderSubject), nil
	}
}

func (f fweOrderSubjectDo) Last() (*db_model.FweOrderSubject, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderSubject), nil
	}
}

func (f fweOrderSubjectDo) Find() ([]*db_model.FweOrderSubject, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FweOrderSubject), err
}

func (f fweOrderSubjectDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FweOrderSubject, err error) {
	buf := make([]*db_model.FweOrderSubject, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fweOrderSubjectDo) FindInBatches(result *[]*db_model.FweOrderSubject, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fweOrderSubjectDo) Attrs(attrs ...field.AssignExpr) *fweOrderSubjectDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fweOrderSubjectDo) Assign(attrs ...field.AssignExpr) *fweOrderSubjectDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fweOrderSubjectDo) Joins(fields ...field.RelationField) *fweOrderSubjectDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fweOrderSubjectDo) Preload(fields ...field.RelationField) *fweOrderSubjectDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fweOrderSubjectDo) FirstOrInit() (*db_model.FweOrderSubject, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderSubject), nil
	}
}

func (f fweOrderSubjectDo) FirstOrCreate() (*db_model.FweOrderSubject, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderSubject), nil
	}
}

func (f fweOrderSubjectDo) FindByPage(offset int, limit int) (result []*db_model.FweOrderSubject, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fweOrderSubjectDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fweOrderSubjectDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fweOrderSubjectDo) Delete(models ...*db_model.FweOrderSubject) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fweOrderSubjectDo) withDO(do gen.Dao) *fweOrderSubjectDo {
	f.DO = *do.(*gen.DO)
	return f
}
