// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFweOrderLog(db *gorm.DB, opts ...gen.DOOption) fweOrderLog {
	_fweOrderLog := fweOrderLog{}

	_fweOrderLog.fweOrderLogDo.UseDB(db, opts...)
	_fweOrderLog.fweOrderLogDo.UseModel(&db_model.FweOrderLog{})

	tableName := _fweOrderLog.fweOrderLogDo.TableName()
	_fweOrderLog.ALL = field.NewAsterisk(tableName)
	_fweOrderLog.ID = field.NewInt64(tableName, "id")
	_fweOrderLog.OrderID = field.NewString(tableName, "order_id")
	_fweOrderLog.Action = field.NewString(tableName, "action")
	_fweOrderLog.OperatorID = field.NewString(tableName, "operator_id")
	_fweOrderLog.OperatorName = field.NewString(tableName, "operator_name")
	_fweOrderLog.BeforeContent = field.NewString(tableName, "before_content")
	_fweOrderLog.AfterContent = field.NewString(tableName, "after_content")
	_fweOrderLog.BeforeStatus = field.NewInt32(tableName, "before_status")
	_fweOrderLog.AfterStatus = field.NewInt32(tableName, "after_status")
	_fweOrderLog.BeforeSubStatus = field.NewString(tableName, "before_sub_status")
	_fweOrderLog.AfterSubStatus = field.NewString(tableName, "after_sub_status")
	_fweOrderLog.LogID = field.NewString(tableName, "log_id")
	_fweOrderLog.OperateTime = field.NewInt64(tableName, "operate_time")
	_fweOrderLog.CreateTime = field.NewTime(tableName, "create_time")
	_fweOrderLog.OperateDesc = field.NewString(tableName, "operate_desc")

	_fweOrderLog.fillFieldMap()

	return _fweOrderLog
}

// fweOrderLog 订单日志存储
type fweOrderLog struct {
	fweOrderLogDo fweOrderLogDo

	ALL             field.Asterisk
	ID              field.Int64  // 自增id
	OrderID         field.String // 业务对象ID
	Action          field.String // 操作事件
	OperatorID      field.String // 操作人ID
	OperatorName    field.String // 操作人名
	BeforeContent   field.String // 操作前信息,JSON文本
	AfterContent    field.String // 操作后信息,JSON文本
	BeforeStatus    field.Int32  // 操作前状态
	AfterStatus     field.Int32  // 操作后状态
	BeforeSubStatus field.String // 操作前子状态
	AfterSubStatus  field.String // 操作后子状态
	LogID           field.String // log_id
	OperateTime     field.Int64  // 操作时间
	CreateTime      field.Time   // 创建时间
	OperateDesc     field.String // 操作描述

	fieldMap map[string]field.Expr
}

func (f fweOrderLog) Table(newTableName string) *fweOrderLog {
	f.fweOrderLogDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fweOrderLog) As(alias string) *fweOrderLog {
	f.fweOrderLogDo.DO = *(f.fweOrderLogDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fweOrderLog) updateTableName(table string) *fweOrderLog {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.OrderID = field.NewString(table, "order_id")
	f.Action = field.NewString(table, "action")
	f.OperatorID = field.NewString(table, "operator_id")
	f.OperatorName = field.NewString(table, "operator_name")
	f.BeforeContent = field.NewString(table, "before_content")
	f.AfterContent = field.NewString(table, "after_content")
	f.BeforeStatus = field.NewInt32(table, "before_status")
	f.AfterStatus = field.NewInt32(table, "after_status")
	f.BeforeSubStatus = field.NewString(table, "before_sub_status")
	f.AfterSubStatus = field.NewString(table, "after_sub_status")
	f.LogID = field.NewString(table, "log_id")
	f.OperateTime = field.NewInt64(table, "operate_time")
	f.CreateTime = field.NewTime(table, "create_time")
	f.OperateDesc = field.NewString(table, "operate_desc")

	f.fillFieldMap()

	return f
}

func (f *fweOrderLog) WithContext(ctx context.Context) *fweOrderLogDo {
	return f.fweOrderLogDo.WithContext(ctx)
}

func (f fweOrderLog) TableName() string { return f.fweOrderLogDo.TableName() }

func (f fweOrderLog) Alias() string { return f.fweOrderLogDo.Alias() }

func (f fweOrderLog) Columns(cols ...field.Expr) gen.Columns { return f.fweOrderLogDo.Columns(cols...) }

func (f *fweOrderLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fweOrderLog) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 15)
	f.fieldMap["id"] = f.ID
	f.fieldMap["order_id"] = f.OrderID
	f.fieldMap["action"] = f.Action
	f.fieldMap["operator_id"] = f.OperatorID
	f.fieldMap["operator_name"] = f.OperatorName
	f.fieldMap["before_content"] = f.BeforeContent
	f.fieldMap["after_content"] = f.AfterContent
	f.fieldMap["before_status"] = f.BeforeStatus
	f.fieldMap["after_status"] = f.AfterStatus
	f.fieldMap["before_sub_status"] = f.BeforeSubStatus
	f.fieldMap["after_sub_status"] = f.AfterSubStatus
	f.fieldMap["log_id"] = f.LogID
	f.fieldMap["operate_time"] = f.OperateTime
	f.fieldMap["create_time"] = f.CreateTime
	f.fieldMap["operate_desc"] = f.OperateDesc
}

func (f fweOrderLog) clone(db *gorm.DB) fweOrderLog {
	f.fweOrderLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fweOrderLog) replaceDB(db *gorm.DB) fweOrderLog {
	f.fweOrderLogDo.ReplaceDB(db)
	return f
}

type fweOrderLogDo struct{ gen.DO }

func (f fweOrderLogDo) Debug() *fweOrderLogDo {
	return f.withDO(f.DO.Debug())
}

func (f fweOrderLogDo) WithContext(ctx context.Context) *fweOrderLogDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fweOrderLogDo) ReadDB() *fweOrderLogDo {
	return f.Clauses(dbresolver.Read)
}

func (f fweOrderLogDo) WriteDB() *fweOrderLogDo {
	return f.Clauses(dbresolver.Write)
}

func (f fweOrderLogDo) Session(config *gorm.Session) *fweOrderLogDo {
	return f.withDO(f.DO.Session(config))
}

func (f fweOrderLogDo) Clauses(conds ...clause.Expression) *fweOrderLogDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fweOrderLogDo) Returning(value interface{}, columns ...string) *fweOrderLogDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fweOrderLogDo) Not(conds ...gen.Condition) *fweOrderLogDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fweOrderLogDo) Or(conds ...gen.Condition) *fweOrderLogDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fweOrderLogDo) Select(conds ...field.Expr) *fweOrderLogDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fweOrderLogDo) Where(conds ...gen.Condition) *fweOrderLogDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fweOrderLogDo) Order(conds ...field.Expr) *fweOrderLogDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fweOrderLogDo) Distinct(cols ...field.Expr) *fweOrderLogDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fweOrderLogDo) Omit(cols ...field.Expr) *fweOrderLogDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fweOrderLogDo) Join(table schema.Tabler, on ...field.Expr) *fweOrderLogDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fweOrderLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fweOrderLogDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fweOrderLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *fweOrderLogDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fweOrderLogDo) Group(cols ...field.Expr) *fweOrderLogDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fweOrderLogDo) Having(conds ...gen.Condition) *fweOrderLogDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fweOrderLogDo) Limit(limit int) *fweOrderLogDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fweOrderLogDo) Offset(offset int) *fweOrderLogDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fweOrderLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fweOrderLogDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fweOrderLogDo) Unscoped() *fweOrderLogDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fweOrderLogDo) Create(values ...*db_model.FweOrderLog) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fweOrderLogDo) CreateInBatches(values []*db_model.FweOrderLog, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fweOrderLogDo) Save(values ...*db_model.FweOrderLog) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fweOrderLogDo) First() (*db_model.FweOrderLog, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderLog), nil
	}
}

func (f fweOrderLogDo) Take() (*db_model.FweOrderLog, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderLog), nil
	}
}

func (f fweOrderLogDo) Last() (*db_model.FweOrderLog, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderLog), nil
	}
}

func (f fweOrderLogDo) Find() ([]*db_model.FweOrderLog, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FweOrderLog), err
}

func (f fweOrderLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FweOrderLog, err error) {
	buf := make([]*db_model.FweOrderLog, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fweOrderLogDo) FindInBatches(result *[]*db_model.FweOrderLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fweOrderLogDo) Attrs(attrs ...field.AssignExpr) *fweOrderLogDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fweOrderLogDo) Assign(attrs ...field.AssignExpr) *fweOrderLogDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fweOrderLogDo) Joins(fields ...field.RelationField) *fweOrderLogDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fweOrderLogDo) Preload(fields ...field.RelationField) *fweOrderLogDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fweOrderLogDo) FirstOrInit() (*db_model.FweOrderLog, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderLog), nil
	}
}

func (f fweOrderLogDo) FirstOrCreate() (*db_model.FweOrderLog, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FweOrderLog), nil
	}
}

func (f fweOrderLogDo) FindByPage(offset int, limit int) (result []*db_model.FweOrderLog, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fweOrderLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fweOrderLogDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fweOrderLogDo) Delete(models ...*db_model.FweOrderLog) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fweOrderLogDo) withDO(do gen.Dao) *fweOrderLogDo {
	f.DO = *do.(*gen.DO)
	return f
}
