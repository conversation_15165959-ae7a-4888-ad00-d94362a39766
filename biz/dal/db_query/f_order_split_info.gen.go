// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package db_query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
)

func newFOrderSplitInfo(db *gorm.DB, opts ...gen.DOOption) fOrderSplitInfo {
	_fOrderSplitInfo := fOrderSplitInfo{}

	_fOrderSplitInfo.fOrderSplitInfoDo.UseDB(db, opts...)
	_fOrderSplitInfo.fOrderSplitInfoDo.UseModel(&db_model.FOrderSplitInfo{})

	tableName := _fOrderSplitInfo.fOrderSplitInfoDo.TableName()
	_fOrderSplitInfo.ALL = field.NewAsterisk(tableName)
	_fOrderSplitInfo.ID = field.NewInt64(tableName, "id")
	_fOrderSplitInfo.OrderID = field.NewString(tableName, "order_id")
	_fOrderSplitInfo.SplitUID = field.NewString(tableName, "split_uid")
	_fOrderSplitInfo.SplitUIDType = field.NewInt32(tableName, "split_uid_type")
	_fOrderSplitInfo.Scale = field.NewInt64(tableName, "scale")
	_fOrderSplitInfo.Amount = field.NewInt64(tableName, "amount")
	_fOrderSplitInfo.SplitMethod = field.NewInt32(tableName, "split_method")
	_fOrderSplitInfo.RoleID = field.NewString(tableName, "role_id")
	_fOrderSplitInfo.RoleType = field.NewInt32(tableName, "role_type")
	_fOrderSplitInfo.SplitDesc = field.NewString(tableName, "split_desc")
	_fOrderSplitInfo.Extra = field.NewString(tableName, "extra")
	_fOrderSplitInfo.CreatedTime = field.NewTime(tableName, "created_time")
	_fOrderSplitInfo.UpdatedTime = field.NewTime(tableName, "updated_time")

	_fOrderSplitInfo.fillFieldMap()

	return _fOrderSplitInfo
}

// fOrderSplitInfo 交易基建 订单分账信息表
type fOrderSplitInfo struct {
	fOrderSplitInfoDo fOrderSplitInfoDo

	ALL          field.Asterisk
	ID           field.Int64  // 主键id
	OrderID      field.String // 基建订单号
	SplitUID     field.String // 商户传四轮ID 平台户不用传
	SplitUIDType field.Int32  // 商户类型 Platform = 1 Shop = 2
	Scale        field.Int64  // 比例-万分位 15 -> 0.15%
	Amount       field.Int64  // 金额
	SplitMethod  field.Int32  // 分账方式：1按比例 2按固定金额
	RoleID       field.String // 业务方ID
	RoleType     field.Int32  // 业务方自定义类型
	SplitDesc    field.String // 描述信息 分账描述
	Extra        field.String // 透传信息
	CreatedTime  field.Time   // 创建时间
	UpdatedTime  field.Time   // 修改时间

	fieldMap map[string]field.Expr
}

func (f fOrderSplitInfo) Table(newTableName string) *fOrderSplitInfo {
	f.fOrderSplitInfoDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fOrderSplitInfo) As(alias string) *fOrderSplitInfo {
	f.fOrderSplitInfoDo.DO = *(f.fOrderSplitInfoDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fOrderSplitInfo) updateTableName(table string) *fOrderSplitInfo {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.OrderID = field.NewString(table, "order_id")
	f.SplitUID = field.NewString(table, "split_uid")
	f.SplitUIDType = field.NewInt32(table, "split_uid_type")
	f.Scale = field.NewInt64(table, "scale")
	f.Amount = field.NewInt64(table, "amount")
	f.SplitMethod = field.NewInt32(table, "split_method")
	f.RoleID = field.NewString(table, "role_id")
	f.RoleType = field.NewInt32(table, "role_type")
	f.SplitDesc = field.NewString(table, "split_desc")
	f.Extra = field.NewString(table, "extra")
	f.CreatedTime = field.NewTime(table, "created_time")
	f.UpdatedTime = field.NewTime(table, "updated_time")

	f.fillFieldMap()

	return f
}

func (f *fOrderSplitInfo) WithContext(ctx context.Context) *fOrderSplitInfoDo {
	return f.fOrderSplitInfoDo.WithContext(ctx)
}

func (f fOrderSplitInfo) TableName() string { return f.fOrderSplitInfoDo.TableName() }

func (f fOrderSplitInfo) Alias() string { return f.fOrderSplitInfoDo.Alias() }

func (f fOrderSplitInfo) Columns(cols ...field.Expr) gen.Columns {
	return f.fOrderSplitInfoDo.Columns(cols...)
}

func (f *fOrderSplitInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fOrderSplitInfo) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 13)
	f.fieldMap["id"] = f.ID
	f.fieldMap["order_id"] = f.OrderID
	f.fieldMap["split_uid"] = f.SplitUID
	f.fieldMap["split_uid_type"] = f.SplitUIDType
	f.fieldMap["scale"] = f.Scale
	f.fieldMap["amount"] = f.Amount
	f.fieldMap["split_method"] = f.SplitMethod
	f.fieldMap["role_id"] = f.RoleID
	f.fieldMap["role_type"] = f.RoleType
	f.fieldMap["split_desc"] = f.SplitDesc
	f.fieldMap["extra"] = f.Extra
	f.fieldMap["created_time"] = f.CreatedTime
	f.fieldMap["updated_time"] = f.UpdatedTime
}

func (f fOrderSplitInfo) clone(db *gorm.DB) fOrderSplitInfo {
	f.fOrderSplitInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fOrderSplitInfo) replaceDB(db *gorm.DB) fOrderSplitInfo {
	f.fOrderSplitInfoDo.ReplaceDB(db)
	return f
}

type fOrderSplitInfoDo struct{ gen.DO }

func (f fOrderSplitInfoDo) Debug() *fOrderSplitInfoDo {
	return f.withDO(f.DO.Debug())
}

func (f fOrderSplitInfoDo) WithContext(ctx context.Context) *fOrderSplitInfoDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fOrderSplitInfoDo) ReadDB() *fOrderSplitInfoDo {
	return f.Clauses(dbresolver.Read)
}

func (f fOrderSplitInfoDo) WriteDB() *fOrderSplitInfoDo {
	return f.Clauses(dbresolver.Write)
}

func (f fOrderSplitInfoDo) Session(config *gorm.Session) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Session(config))
}

func (f fOrderSplitInfoDo) Clauses(conds ...clause.Expression) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fOrderSplitInfoDo) Returning(value interface{}, columns ...string) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fOrderSplitInfoDo) Not(conds ...gen.Condition) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fOrderSplitInfoDo) Or(conds ...gen.Condition) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fOrderSplitInfoDo) Select(conds ...field.Expr) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fOrderSplitInfoDo) Where(conds ...gen.Condition) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fOrderSplitInfoDo) Order(conds ...field.Expr) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fOrderSplitInfoDo) Distinct(cols ...field.Expr) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fOrderSplitInfoDo) Omit(cols ...field.Expr) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fOrderSplitInfoDo) Join(table schema.Tabler, on ...field.Expr) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fOrderSplitInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fOrderSplitInfoDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fOrderSplitInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) *fOrderSplitInfoDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fOrderSplitInfoDo) Group(cols ...field.Expr) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fOrderSplitInfoDo) Having(conds ...gen.Condition) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fOrderSplitInfoDo) Limit(limit int) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fOrderSplitInfoDo) Offset(offset int) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fOrderSplitInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fOrderSplitInfoDo) Unscoped() *fOrderSplitInfoDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fOrderSplitInfoDo) Create(values ...*db_model.FOrderSplitInfo) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fOrderSplitInfoDo) CreateInBatches(values []*db_model.FOrderSplitInfo, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fOrderSplitInfoDo) Save(values ...*db_model.FOrderSplitInfo) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fOrderSplitInfoDo) First() (*db_model.FOrderSplitInfo, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FOrderSplitInfo), nil
	}
}

func (f fOrderSplitInfoDo) Take() (*db_model.FOrderSplitInfo, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FOrderSplitInfo), nil
	}
}

func (f fOrderSplitInfoDo) Last() (*db_model.FOrderSplitInfo, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FOrderSplitInfo), nil
	}
}

func (f fOrderSplitInfoDo) Find() ([]*db_model.FOrderSplitInfo, error) {
	result, err := f.DO.Find()
	return result.([]*db_model.FOrderSplitInfo), err
}

func (f fOrderSplitInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*db_model.FOrderSplitInfo, err error) {
	buf := make([]*db_model.FOrderSplitInfo, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fOrderSplitInfoDo) FindInBatches(result *[]*db_model.FOrderSplitInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fOrderSplitInfoDo) Attrs(attrs ...field.AssignExpr) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fOrderSplitInfoDo) Assign(attrs ...field.AssignExpr) *fOrderSplitInfoDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fOrderSplitInfoDo) Joins(fields ...field.RelationField) *fOrderSplitInfoDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fOrderSplitInfoDo) Preload(fields ...field.RelationField) *fOrderSplitInfoDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fOrderSplitInfoDo) FirstOrInit() (*db_model.FOrderSplitInfo, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FOrderSplitInfo), nil
	}
}

func (f fOrderSplitInfoDo) FirstOrCreate() (*db_model.FOrderSplitInfo, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*db_model.FOrderSplitInfo), nil
	}
}

func (f fOrderSplitInfoDo) FindByPage(offset int, limit int) (result []*db_model.FOrderSplitInfo, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fOrderSplitInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fOrderSplitInfoDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fOrderSplitInfoDo) Delete(models ...*db_model.FOrderSplitInfo) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fOrderSplitInfoDo) withDO(do gen.Dao) *fOrderSplitInfoDo {
	f.DO = *do.(*gen.DO)
	return f
}
