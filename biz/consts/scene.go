package consts

import (
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"github.com/apaxa-go/helper/strconvh"

	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type BizScene int32

func (b BizScene) String() string {
	return strconvh.FormatInt32(int32(b))
}

func (b BizScene) Int32() int32 {
	return int32(b)
}

const (
	BizSceneIllegal BizScene = 0000 // 无效交易场景
	BizSceneTest    BizScene = 1000 // 测试

	BizSceneSHSellByEarnestFinal BizScene = 1101 // 二手车卖车-自营门店 订金-尾款|首付款-[贷款]
	BizSceneSHSellByFull         BizScene = 1102 // 二手车卖车-自营门店 全款|首付款-[贷款]
	BizSceneSHSellInsurance      BizScene = 1103 // 二手车卖车-自营门店 延保
	BizSceneSHConsignByEF        BizScene = 1104 // 二手车卖车-内网&寄售 订金-尾款|首付款-[贷款]
	BizSceneSHConsignByFull      BizScene = 1105 // 二手车卖车-内网&寄售 全款|首付款-[贷款]
	BizSceneSHACNByEF            BizScene = 1106 // 二手车卖车-ACN 订金-尾款|首付款-[贷款]
	BizSceneSHACNByFull          BizScene = 1107 // 二手车卖车-ACN 全款|首付款-[贷款]

	BizSceneSHBuyPersonCar        BizScene = 1201   // 二手车个人收车
	BizSceneSHBuyCompanyCar       BizScene = 1202   // 二手车库融企业收车
	BizSceneSHBackCompanyCar      BizScene = 1203   // 二手车企业回购
	BizSceneSHDirectBuyCompanyCar BizScene = 220400 // 二手车非库融企业收车
	BizSceneSHBuyBackPersonCar    BizScene = 220500 // 二手车门店个人回购
	BizSceneSHBuyBackCompanyCar   BizScene = 220600 // 二手车门店企业回购
	BizSceneSHBuyFranchiseesCar   BizScene = 220700 // 加盟商收车
	BizSceneNCBrokerage           BizScene = 2101   // 新车返佣模式
	BizSceneNCBrokerageNoHorizon  BizScene = 2102   // 新车返佣无水平模式
	BizSceneNCBigDeposit          BizScene = 2201   // 新车大订模式
	BizSceneNCSmallDeposit        BizScene = 2202   // 新车小订模式

	BizSceneNCFranchiseeBigDeposit         BizScene = 180100 // 新车加盟大订模式
	BizSceneNCFranchiseeSmallDeposit       BizScene = 180200 // 新车加盟小订模式
	BizSceneNCFranchiseeBrokerage          BizScene = 180300 // 新车加盟返佣模式
	BizSceneNCFranchiseeBrokerageNoHorizon BizScene = 180400 // 新车加盟返佣无水平模式

	BizSceneSHFinance BizScene = 3001 // 二手车金融

	BizSceneAfterMarketRetail BizScene = 4001 // 车品采购
)

var (
	BizSceneSHSellConsignRevoke                = BizScene(CommonConsts.BizSceneSHSellConsignRevoke.Value())             // 二手车卖车-内网&寄售 撤回
	BizSceneSHSellByEarnestFinalDeliveryCar    = BizScene(CommonConsts.BizSceneSHSellByEarnestFinalDeliveryCar.Value()) // 11
	BizSceneSHSellByFullDeliveryCar            = BizScene(CommonConsts.BizSceneSHSellByFullDeliveryCar.Value())         // 13
	BizSceneSHConsignByEarnestFinalDeliveryCar = BizScene(CommonConsts.BizSceneSHConsignByEFDeliveryCar.Value())        // 15
	BizSceneSHConsignByFullDeliveryCar         = BizScene(CommonConsts.BizSceneSHConsignByFullDeliveryCar.Value())      // 16
)

var BizSceneList = []BizScene{
	BizSceneTest,
	BizSceneSHSellByEarnestFinal,
	BizSceneSHSellByFull,
	BizSceneSHSellInsurance,
	BizSceneSHConsignByEF,
	BizSceneSHConsignByFull,
	BizSceneSHBuyPersonCar,
	BizSceneSHBuyCompanyCar,
	BizSceneSHBackCompanyCar,
	BizSceneSHDirectBuyCompanyCar,
	BizSceneNCBrokerage,
	BizSceneNCBrokerageNoHorizon,
	BizSceneNCBigDeposit,
	BizSceneNCSmallDeposit,
	BizSceneNCFranchiseeBigDeposit,
	BizSceneNCFranchiseeSmallDeposit,
	BizSceneNCFranchiseeBrokerage,
	BizSceneNCFranchiseeBrokerageNoHorizon,
	BizSceneSHFinance,
	BizSceneSHACNByEF,
	BizSceneSHACNByFull,
	BizSceneAfterMarketRetail,
	BizSceneSHSellConsignRevoke,
	BizSceneSHSellByEarnestFinalDeliveryCar,
	BizSceneSHSellByFullDeliveryCar,
	BizSceneSHConsignByEarnestFinalDeliveryCar,
	BizSceneSHConsignByFullDeliveryCar,
	BizSceneSHBuyBackPersonCar,
	BizSceneSHBuyBackCompanyCar,
	BizSceneSHBuyFranchiseesCar,
}

func GetBizScene(bizScene int32) BizScene {
	for _, v := range BizSceneList {
		if v.Int32() == bizScene {
			return v
		}
	}
	return BizSceneIllegal
}

func GetTradeTypeByBizScene(bizScene BizScene) fwe_trade_common.TradeType {
	switch bizScene {
	case BizSceneSHBuyPersonCar:
		return fwe_trade_common.TradeType_EarnestFinal
	case BizSceneSHBuyCompanyCar:
		return fwe_trade_common.TradeType_Full
	case BizSceneSHBackCompanyCar:
		return fwe_trade_common.TradeType_EarnestFinal
	case BizSceneSHDirectBuyCompanyCar:
		return fwe_trade_common.TradeType_EarnestAdvanceFinal
	case BizSceneNCBrokerage, BizSceneNCFranchiseeBrokerage:
		return fwe_trade_common.TradeType_Full
	case BizSceneNCBigDeposit, BizSceneNCFranchiseeBigDeposit:
		return fwe_trade_common.TradeType_EarnestFinal
	case BizSceneNCSmallDeposit, BizSceneNCFranchiseeSmallDeposit:
		return fwe_trade_common.TradeType_EarnestEarnestFinal
	case BizSceneSHBuyBackCompanyCar:
		return fwe_trade_common.TradeType_EarnestAdvanceFinal
	case BizSceneSHBuyBackPersonCar:
		return fwe_trade_common.TradeType_EarnestFinal
	default:
		return fwe_trade_common.TradeType(0)
	}
}
