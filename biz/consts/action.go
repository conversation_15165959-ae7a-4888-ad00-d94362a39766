package consts

type Action string

// 通用action

const (
	CreateAction = "create"
	UpdateExtra  = "UpdateExtra"
)

const (
	Pull  = "pull"
	Close = "close"
)

// 二手车-卖车 Action列表
const (
	// 二手车-自营 订金-尾款 / 订金-首付款-贷款

	ShSellEFSignIntentContractEvent     = "SignIntentContract"     // 签署意向合同
	ShSellEFSignIntentContractOverEvent = "SignIntentContractOver" // 意向合同签署完成
	ShSellEFPayEarnestEvent             = "PayEarnest"             // 支付订金
	ShSellEFPayEarnestOverEvent         = "PayEarnestOver"         // 订金支付完成
	ShSellEFRefundEarnestOverEvent      = "RefundEarnestOver"      // 退订金完成
	ShSellEFSignSellContractEvent       = "SignSellContract"       // 签署买卖合同
	ShSellEFSignSellContractOverEvent   = "SignSellContractOver"   // 买卖合同签署完成
	ShSellEFPayFinalEvent               = "PayFinal"               // 支付尾款
	ShSellEFPayFinalOverEvent           = "PayFinalOver"           // 尾款支付完成
	ShSellEFTransferOwnerEvent          = "TransferOwner"          // 过户
	ShSellEFSelectLoanEvent             = "SelectLoan"             // 确定金融贷款方式
	ShSellEFConfirmLoanEvent            = "ConfirmLoan"            // 确认贷款
	ShSellEFApproveLoanPassEvent        = "ApproveLoanPass"        // 审核贷款通过
	ShSellEFApproveLoanFailEvent        = "ApproveLoanFail"        // 审核贷款不通过
	ShSellEFLoanOverEvent               = "LoanOver"               // 金融贷款完成
	ShSellEFDeliveryEvent               = "DeliveryCar"            // 交车
	ShSellEFSettleOverEvent             = "SettleOver"             // 结算完成
	ShSellEFCancelEvent                 = "Cancel"                 // 作废
	ShSellEFCancelWithRefundEvent       = "CancelWithRefund"       // 作废并退款
	ShSellEFPayEarnestTimeoutEvent      = "PayEarnestTimeout"      // 支付订金超时
	ShSellEFPayPOSTimeoutEvent          = "PayPOSTimeout"          // 支付POS超时

	// 二手车-自营 全款 / 首付款-贷款

	ShSellFullSignSellContractEvent     = "SignSellContract"     // 签署买卖合同
	ShSellFullSignSellContractOverEvent = "SignSellContractOver" // 买卖合同签署完成
	ShSellFullPayMoneyEvent             = "PayMoney"             // 支付
	ShSellFullPayMoneyOverEvent         = "PayMoneyOver"         // 支付完成
	ShSellFullTransferOwnerEvent        = "TransferOwner"        // 过户
	ShSellFullSelectLoanEvent           = "SelectLoan"           // 确定金融贷款方式
	ShSellFullConfirmLoanEvent          = "ConfirmLoan"          // 确认贷款
	ShSellFullApproveLoanPassEvent      = "ApproveLoanPass"      // 审核贷款通过
	ShSellFullApproveLoanFailEvent      = "ApproveLoanFail"      // 审核贷款不通过
	ShSellFullLoanOverEvent             = "LoanOver"             // 金融贷款完成
	ShSellFullDeliveryEvent             = "DeliveryCar"          // 交车
	ShSellFullSettleOverEvent           = "SettleOver"           // 结算完成
	ShSellFullCancelEvent               = "Cancel"               // 作废
	ShSellFullPayPOSTimeoutEvent        = "PayPOSTimeout"        // 支付POS超时

	// 二手车-自营 延保事件

	ShSellWarrantySignContractEvent     = "SignContract"     // 签署合同
	ShSellWarrantySignContractOverEvent = "SignContractOver" // 合同签署完成
	ShSellWarrantyPayEvent              = "Pay"              // 支付
	ShSellWarrantyPayOverEvent          = "PayOver"          // 支付完成
	ShSellWarrantyCancelEvent           = "Cancel"           // 作废
	ShSellWarrantyPayTimeoutEvent       = "PayTimeout"       // 支付超时事件

	// 二手车 内网&寄售 订金-尾款|首付款-[贷款]

	ShConsignEFSignIntentContractEvent        = "SignIntentContract"        // 签署意向合同
	ShConsignEFSignIntentContractOverEvent    = "SignIntentContractOver"    // 意向合同签署完成
	ShConsignEFPayEarnestEvent                = "PayEarnest"                // 支付订金
	ShConsignEFPayEarnestOverEvent            = "PayEarnestOver"            // 订金支付完成
	ShConsignEFRefundEarnestOverEvent         = "RefundEarnestOver"         // 退订金完成
	ShConsignEFRefundEarnestOverAfterPOSEvent = "RefundEarnestOverAfterPOS" // POS支付后退订金完成
	ShConsignEFSignSellContractEvent          = "SignSellContract"          // 签署买卖合同
	ShConsignEFSignSellContractOverEvent      = "SignSellContractOver"      // 买卖合同签署完成
	ShConsignEFPayFinalEvent                  = "PayFinal"                  // 支付尾款
	ShConsignEFPayFinalOverEvent              = "PayFinalOver"              // 尾款支付完成
	ShConsignEFTransferOwnerEvent             = "TransferOwner"             // 过户
	ShConsignEFSelectLoanEvent                = "SelectLoan"                // 确定金融贷款方式
	ShConsignEFConfirmLoanEvent               = "ConfirmLoan"               // 确认贷款
	ShConsignEFApproveLoanPassEvent           = "ApproveLoanPass"           // 审核贷款通过
	ShConsignEFApproveLoanFailEvent           = "ApproveLoanFail"           // 审核贷款不通过
	ShConsignEFLoanOverEvent                  = "LoanOver"                  // 金融贷款完成
	ShConsignEFDeliveryEvent                  = "DeliveryCar"               // 交车
	ShConsignEFSettleOverEvent                = "SettleOver"                // 结算完成
	ShConsignEFCancelEvent                    = "Cancel"                    // 作废
	ShConsignEFCancelWithRefundEvent          = "CancelWithRefund"          // 作废并退款
	ShConsignEFPayEarnestTimeoutEvent         = "PayEarnestTimeout"         // 支付订金超时
	ShConsignEFPayPOSTimeoutEvent             = "PayPOSTimeout"             // 支付POS超时

	// 二手车 内网&寄售 全款|首付款-[贷款]

	ShConsignFullSignSellContractEvent     = "SignSellContract"     // 签署买卖合同
	ShConsignFullSignSellContractOverEvent = "SignSellContractOver" // 买卖合同签署完成
	ShConsignFullPayMoneyEvent             = "PayMoney"             // 支付
	ShConsignFullPayMoneyOverEvent         = "PayMoneyOver"         // 支付完成
	ShConsignFullTransferOwnerEvent        = "TransferOwner"        // 过户
	ShConsignFullSelectLoanEvent           = "SelectLoan"           // 确定金融贷款方式
	ShConsignFullConfirmLoanEvent          = "ConfirmLoan"          // 确认贷款
	ShConsignFullApproveLoanPassEvent      = "ApproveLoanPass"      // 审核贷款通过
	ShConsignFullApproveLoanFailEvent      = "ApproveLoanFail"      // 审核贷款不通过
	ShConsignFullLoanOverEvent             = "LoanOver"             // 金融贷款完成
	ShConsignFullDeliveryEvent             = "DeliveryCar"          // 交车
	ShConsignFullSettleOverEvent           = "SettleOver"           // 结算完成
	ShConsignFullCancelEvent               = "Cancel"               // 作废
	ShConsignFullPayPOSTimeoutEvent        = "PayPOSTimeout"        // 支付POS超时

	// 二手车 ACN 订金-尾款|首付款-[贷款]

	ShACNEFSignIntentContractEvent        = "SignIntentContract"        // 签署意向合同
	ShACNEFSignIntentContractOverEvent    = "SignIntentContractOver"    // 意向合同签署完成
	ShACNEFPayEarnestEvent                = "PayEarnest"                // 支付订金
	ShACNEFPayEarnestOverEvent            = "PayEarnestOver"            // 订金支付完成
	ShACNEFRefundEarnestOverEvent         = "RefundEarnestOver"         // 退订金完成
	ShACNEFRefundEarnestOverAfterPOSEvent = "RefundEarnestOverAfterPOS" // POS支付后退订金完成
	ShACNEFSignSellContractEvent          = "SignSellContract"          // 签署买卖合同
	ShACNEFSignSellContractOverEvent      = "SignSellContractOver"      // 买卖合同签署完成
	ShACNEFPayFinalEvent                  = "PayFinal"                  // 支付尾款
	ShACNEFPayFinalOverEvent              = "PayFinalOver"              // 尾款支付完成
	ShACNEFTransferOwnerEvent             = "TransferOwner"             // 过户
	ShACNEFSelectLoanEvent                = "SelectLoan"                // 确定金融贷款方式
	ShACNEFConfirmLoanEvent               = "ConfirmLoan"               // 确认贷款
	ShACNEFApproveLoanPassEvent           = "ApproveLoanPass"           // 审核贷款通过
	ShACNEFApproveLoanFailEvent           = "ApproveLoanFail"           // 审核贷款不通过
	ShACNEFLoanOverEvent                  = "LoanOver"                  // 金融贷款完成
	ShACNEFDeliveryEvent                  = "DeliveryCar"               // 交车
	ShACNEFSettleOverEvent                = "SettleOver"                // 结算完成
	ShACNEFCancelEvent                    = "Cancel"                    // 作废
	ShACNEFCancelWithRefundEvent          = "CancelWithRefund"          // 作废并退款
	ShACNEFPayPOSTimeoutEvent             = "PayPOSTimeout"             // 支付POS超时

	// 二手车 ACN 全款|首付款-[贷款]

	ShACNFullSignSellContractEvent     = "SignSellContract"     // 签署买卖合同
	ShACNFullSignSellContractOverEvent = "SignSellContractOver" // 买卖合同签署完成
	ShACNFullPayMoneyEvent             = "PayMoney"             // 支付
	ShACNFullPayMoneyOverEvent         = "PayMoneyOver"         // 支付完成
	ShACNFullTransferOwnerEvent        = "TransferOwner"        // 过户
	ShACNFullSelectLoanEvent           = "SelectLoan"           // 确定金融贷款方式
	ShACNFullConfirmLoanEvent          = "ConfirmLoan"          // 确认贷款
	ShACNFullApproveLoanPassEvent      = "ApproveLoanPass"      // 审核贷款通过
	ShACNFullApproveLoanFailEvent      = "ApproveLoanFail"      // 审核贷款不通过
	ShACNFullLoanOverEvent             = "LoanOver"             // 金融贷款完成
	ShACNFullDeliveryEvent             = "DeliveryCar"          // 交车
	ShACNFullSettleOverEvent           = "SettleOver"           // 结算完成
	ShACNFullCancelEvent               = "Cancel"               // 作废
	ShACNFullPayPOSTimeoutEvent        = "PayPOSTimeout"        // 支付POS超时
)

// 二手车-收车 Action列表

const (
	// 个人收车

	SHBuyPersonCarBusinessContStart  = "BusinessContStart"  // 商业合同签署
	SHBuyPersonCarBusinessContFinish = "BusinessContFinish" // 商业合同完成
	SHBuyPersonCarEarnestPayStart    = "EarnestPayStart"    // 订金支付发起
	SHBuyPersonCarEarnestPayFinish   = "EarnestFinish"      // 订金支付完成
	SHBuyPersonCarEarnestPayFailed   = "EarnestPayFailed"   // 订金支付失败
	SHBuyPersonCarTransOwnerFinish   = "TransOwnerFinish"   // 过户完成
	SHBuyPersonCarFinalPayStart      = "FinalPayStart"      // 尾款支付发起
	SHBuyPersonCarFinalPayFinish     = "FinalPayFinish"     // 尾款支付完成
	SHBuyPersonCarStorageInFinish    = "StorageInFinish"    // 入库完成
	SHBuyPersonCarCancelOrderFinish  = "CancelOrderFinish"  // 取消订单
	SHBuyPersonCarUpdateTotalAmount  = "UpdateTotalAmount"  // 更新总金额

	// 企业收车

	SHBuyCompanyCarBusinessContStart   = "BusinessContStart"   // 商业合同签署
	SHBuyCompanyCarBusinessContFinish  = "BusinessContFinish"  // 商业合同完成
	SHBuyCompanyCarGuaranteeContStart  = "GuaranteeContStart"  // 保证金合同签署
	SHBuyCompanyCarGuaranteeContFinish = "GuaranteeContFinish" // 保证金合同完成
	SHBuyCompanyCarTransOwnerFinish    = "TransOwnerFinish"    // 过户完成
	SHBuyCompanyCarStorageInFinish     = "StorageInFinish"     // 入库完成
	SHBuyCompanyCarTotalPayStart       = "TotalPayStart"       // 车款支付发起
	SHBuyCompanyCarTotalPayFinish      = "TotalPayFinish"      // 车款支付完成
	SHBuyCompanyCarCancelOrderFinish   = "CancelOrderFinish"   // 取消订单

	// 企业回购

	SHBackCompanyCarEarnestPayStart     = "EarnestPayStart"     // 订金支付发起
	SHBackCompanyCarEarnestPayFinish    = "EarnestFinish"       // 订金支付完成
	SHBackCompanyCarFinalPayStart       = "FinalPayStart"       // 尾款支付发起
	SHBackCompanyCarFinalPayFinish      = "FinalPayFinish"      // 尾款支付完成
	SHBackCompanyCarTransOwnerFinish    = "TransOwnerFinish"    // 过户完成
	SHBackCompanyCarEarnestRefundStart  = "EarnestRefundStart"  // 订金退款发起
	SHBackCompanyCarEarnestRefundFinish = "EarnestRefundFinish" // 订金退款完成
	SHBackCompanyCarCancelOrderFinish   = "CancelOrderFinish"   // 取消订单
)

const (
	// 新车返佣

	NCBrokerageReviewFailUpdateOrder       = "ReviewFailUpdateOrder"       // 审核失败修改订单
	NCBrokerageReviewPass                  = "ReviewPass"                  // 审核通过
	NCBrokerageReviewReject                = "ReviewReject"                // 审核驳回
	NCBrokerageCancelStart                 = "CancelStart"                 // 直接取消订单发起
	NCBrokerageServiceContStart            = "ServiceContStart"            // 发起签署服务合同
	NCBrokerageServiceContFinish           = "ServiceContFinish"           // 服务合同签署完成
	NCBrokerageTerminationContStart        = "TerminationContStart"        // 终止合同签署发起
	NCBrokerageTerminationContFinish       = "TerminationContFinish"       // 终止合同签署回调
	NCBrokerageRefundTerminationContFinish = "RefundTerminationContFinish" // 终止合同签署回调
	NCBrokerageTotalPayStart               = "TotalPayStart"               // 全款支付发起
	NCBrokerageTotalPayFinish              = "TotalPayFinish"              // 全款支付回调
	NCBrokerageTotalPayTimeoutFinish       = "TotalPayTimeoutFinish"       // 全款支付超时回调
	NCBrokerageCustomerBookCarFinish       = "CustomerBookCarFinish"       // 销售确认客户下订完成
	NCBrokerageTotalRefundFinish           = "TotalRefundFinish"           // 全款支付后取消订单退款回调
	NCBrokerageSettleStart                 = "SettleStart"                 // 交车完成分账发起
	NCBrokerageSettleFinish                = "SettleFinish"                // 分账回调

	// 新车大订

	NCBigDepositReviewFailUpdateOrder       = "ReviewFailUpdateOrder"       // 审核失败修改订单
	NCBigDepositReviewPass                  = "ReviewPass"                  // 审核通过
	NCBigDepositReviewReject                = "ReviewReject"                // 审核驳回
	NCBigDepositCancelStart                 = "CancelStart"                 // 直接取消发起审核
	NCBigDepositBusinessContStart           = "BusinessContStart"           // 买卖合同签署发起
	NCBigDepositBusinessContFinish          = "BusinessContFinish"          // 买卖合同签署回调
	NCBigDepositTerminationContStart        = "TerminationContStart"        // 终止合同发起签约
	NCBigDepositTerminationContFinish       = "TerminationContFinish"       // 终止合同发起签约回调
	NCBigDepositRefundTerminationContFinish = "RefundTerminationContFinish" // 终止合同发起签约回调
	NCBigDepositBigEarnestPayStart          = "BigEarnestPayStart"          // 大订支付发起
	NCBigDepositBigEarnestPayFinish         = "BigEarnestPayFinish"         // 大订支付回调
	NCBigDepositBigEarnestPayTimeoutFinish  = "BigEarnestPayTimeoutFinish"  // 大订支付超时回调
	NCBigDepositBigEarnestRefundFinish      = "BigEarnestRefundFinish"      // 大订支付后退款回调
	NCBigDepositCustomerCheckCarFinish      = "CustomerCheckCarFinish"      // 客户验车完成
	NCBigDepositCustomerGetCarFinish        = "CustomerGetCarFinish"        // 客户提车完成
	NCBigDepositCheckCarContStart           = "CheckCarContStart"           // 验车合同发起签约
	NCBigDepositCheckCarContFinish          = "CheckCarContFinish"          // 验车合同发起签约回调
	NCBigDepositFinalPayStart               = "FinalPayStart"               // 尾款支付发起
	NCBigDepositFinalPayFinish              = "FinalPayFinish"              // 尾款支付回调
	NCBigDepositFinalPayTimeoutFinish       = "FinalPayTimeoutFinish"       // 尾款支付超时回调
	NCBigDepositSettleStart                 = "SettleStart"                 // 交车完成分账发起
	NCBigDepositSettleFinish                = "SettleFinish"                // 分账回调

	// 新车大小订

	NCSmallDepositReviewFailUpdateOrder               = "ReviewFailUpdateOrder"               // 审核失败修改订单
	NCSmallDepositReviewPass                          = "ReviewPass"                          // 审核通过
	NCSmallDepositReviewReject                        = "ReviewReject"                        // 审核驳回
	NCSmallDepositCancelStart                         = "CancelStart"                         // 直接取消发起审核
	NCSmallDepositIntentionContStart                  = "IntentionContStart"                  // 意向合同发起
	NCSmallDepositIntentionContFinish                 = "IntentionContFinish"                 // 意向合同回调
	NCSmallDepositSmallEarnestPayStart                = "SmallEarnestPayStart"                // 小订支付发起
	NCSmallDepositSmallEarnestPayFinish               = "SmallEarnestPayFinish"               // 小订支付回调
	NCSmallDepositSmallEarnestPayTimeoutFinish        = "SmallEarnestPayTimeoutFinish"        // 小订支付超时回调
	NCSmallDepositBusinessContStart                   = "BusinessContStart"                   // 买卖合同签署发起
	NCSmallDepositBusinessContFinish                  = "BusinessContFinish"                  // 买卖合同签署回调
	NCSmallDepositTerminationContStart                = "TerminationContStart"                // 终止合同发起签约
	NCSmallDepositTerminationContFinish               = "TerminationContFinish"               // 终止合同发起签约回调
	NCSmallDepositRefundSmallTerminationContFinish    = "RefundSmallTerminationContFinish"    // 终止合同发起签约回调
	NCSmallDepositRefundSmallBigTerminationContFinish = "RefundSmallBigTerminationContFinish" // 终止合同发起签约回调
	NCSmallDepositSmallEarnestRefundFinish            = "SmallEarnestRefundFinish"            // 小订支付后退款回调
	NCSmallDepositBigEarnestPayStart                  = "BigEarnestPayStart"                  // 大订支付发起
	NCSmallDepositBigEarnestPayFinish                 = "BigEarnestPayFinish"                 // 大订支付回调
	NCSmallDepositBigEarnestPayTimeoutFinish          = "BigEarnestPayTimeoutFinish"          // 大订支付超时回调
	NCSmallDepositEarnestRefundFinish                 = "EarnestRefundFinish"                 // 大订支付后退款回调
	NCSmallDepositShopBookCarFinish                   = "ShopBookCarFinish"                   // 门店订车完成
	NCSmallDepositCustomerCheckCarFinish              = "CustomerCheckCarFinish"              // 客户验车完成
	NCSmallDepositCustomerGetCarFinish                = "CustomerGetCarFinish"                // 客户提车完成
	NCSmallDepositCheckCarContStart                   = "CheckCarContStart"                   // 验车合同发起签约
	NCSmallDepositCheckCarContFinish                  = "CheckCarContFinish"                  // 验车合同发起签约回调
	NCSmallDepositFinalPayStart                       = "FinalPayStart"                       // 尾款支付发起
	NCSmallDepositFinalPayFinish                      = "FinalPayFinish"                      // 尾款支付回调
	NCSmallDepositFinalPayTimeoutFinish               = "FinalPayTimeoutFinish"               // 尾款支付超时回调
	NCSmallDepositSettleStart                         = "SettleStart"                         // 交车完成分账发起
	NCSmallDepositSettleFinish                        = "SettleFinish"                        // 分账完成
)

const (
	SHFinanceFirstReviewResult     = "FirstReview"           // 审核初审结果
	SHFinanceReviewApprove         = "ReviewApprove"         // 审核通过
	SHFinanceReviewReject          = "ReviewReject"          // 审核拒绝
	SHFinanceBeginSignContract     = "BeginSignContract"     // 开始签署合同
	SHFinanceSignSucAndTransferSuc = "SignSucAndTransferSuc" // 签署成功并且转账成功
	SHFinanceVerifyApprove         = "VerifyApprove"         // 核销通过
	SHFinanceFirstVerifyResult     = "FirstVerify"           // 核销初审结果
	SHFinanceBeginVerify           = "BeginVerify"           // 发起核销
)
