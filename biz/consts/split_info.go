package consts

import (
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type PaymentUIDType int32

const (
	ShopUIDType     PaymentUIDType = 1002
	PlatformUIDType PaymentUIDType = 1005
)

func (s PaymentUIDType) ToInt32() int32 {
	return int32(s)
}

var splitMap = map[fwe_trade_common.SplitUIDType]int32{
	fwe_trade_common.SplitUIDType_Platform: PlatformUIDType.ToInt32(),
	fwe_trade_common.SplitUIDType_Shop:     ShopUIDType.ToInt32(),
}

func GetSplitUIDType(uidType fwe_trade_common.SplitUIDType) int32 {
	return splitMap[uidType]
}
