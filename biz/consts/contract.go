package consts

type ContractType int32

const (
	// 二手车-卖车 合同类型

	SHSellIntentCont    ContractType = 1 // 意向合同
	SHSellSaleCont      ContractType = 2 // 买卖合同
	SHSellInsuranceCont ContractType = 3 // 延保合同
	SHSellAfterSaleCont ContractType = 4 // 售后合同

	SHSellMerchantNotificationCont ContractType = 21 // 车商告知书
	SHSellTransferCommitmentCont   ContractType = 22 // 过户承诺书
	SHSellDeliveryCarCont          ContractType = 23 // 交车单

	// 二手车-收车 合同类型

	SHBuyBusinessCont  ContractType = 1 // 买卖合同
	SHBuyGuaranteeCont ContractType = 2 // 保证金合同

	// 新车门店 合同类型

	ServiceCont     ContractType = 1
	BusinessCont    ContractType = 2
	CheckCarCont    ContractType = 3
	IntentionCont   ContractType = 4
	TerminationCont ContractType = 5
)

func (cont ContractType) Int32() int32 {
	return int32(cont)
}

func (cont ContractType) Name() string {
	switch cont {
	case SHSellIntentCont:
		return "intent"
	case SHSellSaleCont:
		return "sale"
	case SHSellInsuranceCont:
		return "insurance"
	case SHSellAfterSaleCont:
		return "after_sale"
	case SHSellMerchantNotificationCont:
		return "merchantNotification"
	case SHSellTransferCommitmentCont:
		return "transferCommitment"
	case SHSellDeliveryCarCont:
		return "deliveryCar"
	}
	return ""
}
