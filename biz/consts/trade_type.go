package consts

type TradeType string

func (t TradeType) String() string {
	return string(t)
}

// deprecated: 请使用 sdkConsts ："code.byted.org/motor/fwe_trade_common/consts"
const (
	TradeTypePayCashier            TradeType = "pay_cashier"
	TradeTypePayGuarantee          TradeType = "pay_guarantee"
	TradeTypePayPos                TradeType = "pay_pos"
	TradeTypeTransferHz            TradeType = "transfer_hz"
	TradeTypeWithdrawBank          TradeType = "withdraw_bank"
	TradeTypePayOffline            TradeType = "pay_offline"
	TradeTypeYZT                   TradeType = "pay_yzt"
	TradeTypePayYztQr              TradeType = "pay_yzt_qr"
	TradeTypePayYztOfflineCash     TradeType = "pay_yzt_offline_cash"
	TradeTypePayYztOfflineTransfer TradeType = "pay_yzt_offline_transfer"
)

var (
	YztOfflineTradeType = []string{TradeTypePayYztOfflineCash.String(), TradeTypePayYztOfflineTransfer.String()}
)
