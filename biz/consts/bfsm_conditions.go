package consts

import "fmt"

type Condition string

func (c Condition) Val() string {
	return string(c)
}

func (c Condition) Not() Condition {
	return Condition(fmt.Sprintf("!%s", c))
}

func (c Condition) And(another Condition) Condition {
	return Condition(fmt.Sprintf("%s && %s", c, another))
}

const (
	CondShSellIsSelfEmployed Condition = "isSelfEmployed" // 是否是自营业务
	CondShSellHasLoan        Condition = "hasLoan"        // 是否有金融节点
	CondShSellIsLoanFirst    Condition = "isLoanFirst"    // 是否先贷款
	CondShSellHasOrderLoan   Condition = "hasOrderLoan"   // 是否有订单贷
	CondShSellOrderLoanOver  Condition = "orderLoanOver"  // 订单贷是否已经结束
	CondShSellFinalPrice     Condition = "finalPrice"     // 尾款金额
)

const (
	CondParamRefundTransferGuaranteeAmount = "refundTransferGuaranteeAmount"
	CondParamRefundRemainingAmount         = "remainingAmount"
	CondParamIsSkipPartSettle              = "isSkipPartSettle"
)

const (
	CondAfterMarketNeedLogistics Condition = "needLogistics" // 是否需要物流
)

const (
	CondShAuctionRefundOrPayoutAmount = "amount"
	CondShAuctionNeedUnfreeze         = "need_unfreeze" // 是否需要解抵押
)
