package consts

type FinanceOrderType int32

const (
	// 二手车-收车 资金单类型

	SHBuyCarTotalPay   FinanceOrderType = 1
	SHBuyCarEarnestPay FinanceOrderType = 2
	SHBuyCarFinalPay   FinanceOrderType = 3

	// 二手车-自营卖车 资金单类型

	SHSellCarTotalPay   FinanceOrderType = 1
	SHSellCarEarnestPay FinanceOrderType = 2
	SHSellCarFinalPay   FinanceOrderType = 3
	SHSellCarLoan       FinanceOrderType = 4

	SHSellCarTransferGuaranteePay FinanceOrderType = 6
	// 二手车-金融

	// 新车门店

	NCShopBrokerageFull FinanceOrderType = 1
	NCShopSmallDeposit  FinanceOrderType = 2
	NCShopBigDeposit    FinanceOrderType = 3
	NCShopFinal         FinanceOrderType = 4
)

func (ft FinanceOrderType) Int32() int32 {
	return int32(ft)
}
