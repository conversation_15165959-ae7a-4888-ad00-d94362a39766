package test

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type RefundExecution struct {
	*executor.ActionBaseExecution
	req *engine.ActionOrderReq
}

func NewRefundExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	return &RefundExecution{
		ActionBaseExecution: executor.NewActionBaseExecution(ctx, req, nil, nil),
		req:                 req,
	}
}

func (e *RefundExecution) Process(ctx context.Context) error {
	logs.CtxInfo(ctx, "refund order process")
	return nil
}
