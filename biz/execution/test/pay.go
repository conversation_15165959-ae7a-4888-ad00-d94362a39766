package test

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/gopkg/tools"
)

type PayExecution struct {
	*executor.ActionBaseExecution
	req    *engine.ActionOrderReq
	config model.BaseConfig
	input  PayInput
}

type PayInput struct {
	IP string `json:"ip"`
}

func NewPayExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	e := &PayExecution{}
	e.req = req
	e.config = model.BaseConfig{}
	e.input = PayInput{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, &e.config, &e.input)
	return e
}

func (e *PayExecution) CheckParams(ctx context.Context) error {
	if e.input.IP == "" {
		return errdef.NewRawErr(errdef.ParamErr, "ip params error")
	}
	return nil
}

func (e *PayExecution) Process(ctx context.Context) error {
	logs.CtxInfo(ctx, "[PayExecution] execution info=%v", tools.GetLogStr(e))
	logs.CtxInfo(ctx, "pay order process")
	return nil
}
