package test

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type CreatExecution struct {
	*executor.CreateBaseExecution
}

func NewCreatExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.CreateOrderReq)
	return &CreatExecution{
		CreateBaseExecution: executor.NewCreateBaseExecution(ctx, req, nil),
	}
}

func (e *CreatExecution) Process(ctx context.Context) error {
	logs.CtxInfo(ctx, "caret order process")
	return nil
}
