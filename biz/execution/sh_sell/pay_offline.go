package sh_sell

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"context"
)

type OfflinePayExecution struct {
	*executor.ActionBaseExecution
	conf   model.CommonConf
	bizReq *sh_sell_model.CreateOfflinePayModel
	bizRsp execution_common.OfflinePayRsp
}

func NewOfflinePayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	e := &OfflinePayExecution{
		bizReq: &sh_sell_model.CreateOfflinePayModel{},
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq, options...)
	return e
}

func (e *OfflinePayExecution) CheckParams(ctx context.Context) error {
	if e.bizReq.FinanceType == 0 || e.bizReq.Amount <= 0 || e.bizReq.CheckData == nil {
		return errdef.NewParamsErr("必传参数为空")
	}
	checkData := e.bizReq.CheckData
	if checkData.ReceiveAccountNo == "" || checkData.RecevieAccountName == "" || checkData.BuyerName == "" {
		return errdef.NewParamsErr("线下转账校验参数为空")
	}
	return nil
}

func (e *OfflinePayExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	// 资金安全校验
	if e.conf.CheckContStructField {
		req := e.GetActionOrderReq()
		param := &service.CheckAmountByContParam{
			OrderID:                   req.OrderID,
			FinanceOrderType:          e.bizReq.FinanceType,
			Amount:                    e.bizReq.Amount,
			AllowIncomeAmountOverflow: e.conf.CheckContOptionAllowIncomeAmountOverflow,
		}
		pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
		if bizErr != nil {
			logs.CtxError(ctx, "[PreProcess] CheckAmountByCont failed, err=%s", bizErr.Error())
			return bizErr
		}
		if !pass {
			logs.CtxError(ctx, "[PreProcess] safe check not pass, blockMsg=%s", blockMsg)
			return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "支付合同校验未通过")
		}
	}

	return nil
}

func (e *OfflinePayExecution) Process(ctx context.Context) error {
	serviceReq, bizErr := e.buildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[OfflinePayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	payNo, bizErr := service.NewTradePayment().CreateOfflinePay(ctx, serviceReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[OfflinePayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.OfflinePayRsp{
		PayOrderNo: payNo,
	}
	return nil
}

func (e *OfflinePayExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *OfflinePayExecution) buildReq(ctx context.Context) (*payment.CreateOfflinePayReq, *errdef.BizErr) {
	var (
		payModel            = e.bizReq
		req                 = e.GetActionOrderReq()
		order               = e.GetOrder()
		fweOrder            = order.FweOrder
		bizErr              *errdef.BizErr
		callbackAction      string
		closeCallbackAction string
	)

	financeOrder := packer.FinanceGetByType(order.FinanceList, payModel.FinanceType)
	if financeOrder == nil {
		bizErr = errdef.NewParamsErr("no finance order")
		return nil, bizErr
	}
	if financeOrder.Amount != e.bizReq.Amount {
		bizErr = errdef.NewParamsErr("支付金额与资金单不一致")
		return nil, bizErr
	}

	switch consts.BizScene(req.Identity.BizScene) {
	case consts.BizSceneSHSellByFullDeliveryCar:
		callbackAction = sh_state.ShSellFullDCPayMoneyOverEvent.Value()
		closeCallbackAction = sh_state.ShSellFullDCPayPOSTimeoutEvent.Value()
	default:
	}

	serviceReq := &payment.CreateOfflinePayReq{
		Identity:           e.GetBizIdentity(),
		OrderID:            fweOrder.OrderID,
		FinanceType:        payModel.FinanceType,
		Currency:           nil,
		Amount:             payModel.Amount,
		CheckData:          payModel.CheckData,
		Extra:              nil,
		CallbackEvent:      utils.MakeCallbackEvent(callbackAction),
		CallbackExtra:      "",
		CloseCallbackEvent: utils.MakeCallbackEvent(closeCallbackAction),
		Base:               base.NewBase(),
	}

	return serviceReq, nil
}
