package sh_sell

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"fmt"
)

type unionPayExecution struct {
	*executor.ActionBaseExecution
	payModel     *sh_sell_model.CreateUnionPayModel
	conf         *sh_sell_model.Conf
	execution    executor.IExecution
	validPayList []*payment.FinancePay
	sourceReq    interface{}
}

func NewPayUnionExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &unionPayExecution{
		payModel:  new(sh_sell_model.CreateUnionPayModel),
		conf:      new(sh_sell_model.Conf),
		sourceReq: sourceReq,
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, exe.conf, exe.payModel)
	return exe
}

func (e *unionPayExecution) Init(ctx context.Context) error {
	if err := e.ActionBaseExecution.Init(ctx); err != nil {
		return err
	}

	tradeType := e.payModel.TradeType
	switch tradeType {
	case CommonConsts.FinancePayCashier.Value():
		e.execution = NewPayCashierExecution(ctx, e.sourceReq)
	case CommonConsts.FinancePayPos.Value():
		e.execution = NewPayPOSExecution(ctx, e.sourceReq)
	case CommonConsts.FinancePayOffline.Value():
		e.execution = NewOfflinePayExecution(ctx, e.sourceReq)
	default:
		logs.CtxError(ctx, "[unionPayExecution.Init] unsupported trade type %v", tradeType)
		return errdef.NewParamsErr(fmt.Sprintf("unsupported trade type %v", tradeType))
	}
	return e.execution.Init(ctx)
}

func (e *unionPayExecution) CheckParams(ctx context.Context) error {
	if bizErr := e.execution.CheckParams(ctx); bizErr != nil {
		return bizErr
	}
	// 查询有效支付单
	var (
		payModel         = e.payModel
		validPayList     = make([]*payment.FinancePay, 0)
		activeStatusList = []fwe_trade_common.CommonStatus{fwe_trade_common.CommonStatus_ToHandle, fwe_trade_common.CommonStatus_Handling, fwe_trade_common.CommonStatus_Success}
	)
	queryReq := &payment.QueryFinancePayListReq{
		Identity:    payModel.Identity,
		OrderID:     payModel.OrderId,
		FinanceType: payModel.FinanceType,
		Base:        base.NewBase(),
	}
	payList, bizErr := service.NewTradePayment().QueryFinancePayList(ctx, queryReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[unionPayExecution.CheckParams] query pay list failed, err=%s", bizErr.Error())
		return bizErr
	}
	for _, pay := range payList {
		if slices.Contains(activeStatusList, pay.Status) {
			validPayList = append(validPayList, pay)
		}
	}
	e.validPayList = validPayList

	// trade_type不允许替换
	if bizErr = e.checkPayListTradeType(ctx, validPayList); bizErr != nil {
		logs.CtxWarn(ctx, "[unionPayExecution.CheckParams] checkPayListTradeType not pass, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *unionPayExecution) checkPayListTradeType(ctx context.Context, validPayList []*payment.FinancePay) *errdef.BizErr {
	if len(validPayList) == 0 {
		return nil
	}
	var (
		tradeType      = e.payModel.TradeType
		existTradeType = validPayList[0].TradeType
	)

	if existTradeType != "" && existTradeType != tradeType {
		logs.CtxError(ctx, "[checkPayListTradeType] trade_type is not consist with exist_trade_type, "+
			"existTradeType=%s, tradeType=%s", existTradeType, tradeType)
		return errdef.NewRawErr(errdef.ExistDifferentPayTradeTypeErr, "支付方式不允许更换")
	}
	return nil
}

func (e *unionPayExecution) PreProcess(ctx context.Context) error {
	return e.execution.PreProcess(ctx)
}

func (e *unionPayExecution) Process(ctx context.Context) error {
	// 修改资金单类型，每次调用都改，避免调用下游超时
	if bizErr := e.updateFinanceOrder(ctx); bizErr != nil {
		logs.CtxWarn(ctx, "[unionPayExecution.Process] updateFinanceOrder failed, err=%+v", bizErr.Error())
		return bizErr
	}
	return e.execution.Process(ctx)
}

func (e *unionPayExecution) updateFinanceOrder(ctx context.Context) *errdef.BizErr {
	var (
		order              *service_model.Order
		normalMerchantInfo = e.conf.NormalPayMerchant
		posMerchantInfo    = e.conf.POSPayMerchant
		financeType        = e.payModel.FinanceType
		reqTradeType       = e.payModel.TradeType
	)
	switch reqTradeType {
	case CommonConsts.FinancePayCashier.Value():
		order = (e.execution).(*payCashierExecution).GetOrder()
	case CommonConsts.FinancePayPos.Value():
		order = (e.execution).(*payPOSExecution).GetOrder()
	case CommonConsts.FinancePayOffline.Value():
		order = (e.execution).(*OfflinePayExecution).GetOrder()
	default:
		logs.CtxError(ctx, "[unionPayExecution.updateFinanceOrder] unsupported trade type %v", reqTradeType)
		return errdef.NewParamsErr(fmt.Sprintf("unsupported trade type %v", reqTradeType))
	}

	financeOrder := packer.FinanceGetByType(order.FinanceList, financeType)
	if financeOrder.TradeType == reqTradeType {
		logs.CtxInfo(ctx, "[unionPayExecution.updateFinanceOrder] no need update trade_type")
		return nil
	}

	logs.CtxInfo(ctx, "[unionPayExecution.updateFinanceOrder] need update tradeType")
	updateFinanceParam := &service_model.UpdateFinanceParams{
		UpdateTradeType: &reqTradeType,
	}

	if reqTradeType == CommonConsts.FinancePayPos.Value() {
		updateFinanceParam.UpdateMerchantID = &posMerchantInfo.MerchantID
		updateFinanceParam.UpdateAppID = &posMerchantInfo.AppID
	} else if reqTradeType == CommonConsts.FinancePayOffline.Value() {
		updateFinanceParam.UpdateMerchantID = conv.StringPtr("")
		updateFinanceParam.UpdateAppID = conv.StringPtr("")
		updateFinanceParam.UpdateMid = conv.StringPtr("")
	} else {
		updateFinanceParam.UpdateMerchantID = &normalMerchantInfo.MerchantID
		updateFinanceParam.UpdateAppID = &normalMerchantInfo.AppID
	}
	bizErr := service.NewFinanceOrderService().UpdateOrderFinance(ctx, financeOrder.FinanceOrderID, updateFinanceParam)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[unionPayExecution.updateFinanceOrder] update financeOrder failed, err=%+v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *unionPayExecution) PostProcess(ctx context.Context) error {
	return e.execution.PostProcess(ctx)
}

func (e *unionPayExecution) Result() interface{} {
	if e.execution == nil {
		return nil
	}
	return e.execution.Result()
}

func (e *unionPayExecution) DebugLog(ctx context.Context) *model.OrderDebugLog {
	return e.execution.DebugLog(ctx)
}
