package sh_sell

import (
	"context"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type loanOverExecution struct {
	*executor.ActionBaseExecution
	param *sh_sell_model.LoanOverModel
	conf  *sh_sell_model.Conf
}

func NewLoanOverExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &loanOverExecution{
		param: new(sh_sell_model.LoanOverModel),
		conf:  new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.param)
	return e
}

func (e *loanOverExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	return nil
}

func (e *loanOverExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

// Process 贷款完成  先过户后贷款，做结算； 先贷款后过户，不做结算
func (e *loanOverExecution) Process(ctx context.Context) error {
	var (
		order = e.GetOrder()
	)

	if IsLoanFirst(order) {
		logs.CtxInfo(ctx, "[loanOverExecution.Process] no need settle")
		return nil
	}

	if e.conf.POSPayMerchant == nil || e.conf.POSPayMerchant.ReceiveUID == "" {
		logs.CtxWarn(ctx, "[loanOverExecution.Process] no receive uid")
		return errdef.NewRawErr(errdef.LackConfigErr, "缺少收款方uid")
	}
	bizErr := CreateSettle(ctx, &CreateSettleReq{
		order:        order,
		receiveUID:   e.conf.POSPayMerchant.ReceiveUID,
		autoWithdraw: slices.ContainsInt32([]int32{int32(consts.BizSceneSHConsignByEF), int32(consts.BizSceneSHConsignByFull), int32(consts.BizSceneSHACNByEF), int32(consts.BizSceneSHACNByFull)}, order.FweOrder.BizScene),
		fcType:       e.conf.POSPayMerchant.FcType,
		fcSceneCode:  e.conf.POSPayMerchant.FcSceneCode,
	})
	if bizErr != nil {
		logs.CtxWarn(ctx, "[loanOverExecution.Process] settle failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
