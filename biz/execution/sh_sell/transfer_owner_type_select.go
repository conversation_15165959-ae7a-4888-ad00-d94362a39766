package sh_sell

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type selectTransferOwnerTypeExecution struct {
	*executor.ActionBaseExecution
	conf  *sh_sell_model.Conf
	param *sh_sell_model.TransferOwnerTypeSelectModel
}

func NewSelectTransferOwnerTypeExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &selectTransferOwnerTypeExecution{
		param: new(sh_sell_model.TransferOwnerTypeSelectModel),
		conf:  new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.param)
	return e
}

func (e *selectTransferOwnerTypeExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	param := e.param
	if param == nil || (param.TransferOwnerType != TransferOwnerByPlatformStr && param.TransferOwnerType != TransferOwnerBySelfStr) {
		logs.CtxWarn(ctx, "[selectTransferOwnerTypeExecution.CheckParams] param error. param=%+v", param)
		return errdef.NewParamsErr("缺少平台过户方式参数")
	}
	if param.TransferOwnerType == TransferOwnerByPlatformStr && param.TransferOwnerGuaranteeAmount != 0 {
		return errdef.NewParamsErr("平台过户时过户保证金金额应当为0")
	}
	if param.TransferOwnerType == TransferOwnerBySelfStr && param.TransferOwnerGuaranteeAmount <= 0 {
		return errdef.NewParamsErr("自主过户时过户保证金金额应当大于0")
	}
	return nil
}

func (e *selectTransferOwnerTypeExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	param := &ShSellConditionParam{TransferOwnerType: e.param.TransferOwnerType}
	conditions := GetShSellCondition(ctx, e.GetOrder(), param)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})

	return nil
}

// Process 设置贷款类型
func (e *selectTransferOwnerTypeExecution) Process(ctx context.Context) error {
	var (
		param = e.param
		order = e.GetOrder()
	)

	order.TagMap[TransferOwnerType] = param.TransferOwnerType
	if bizErr := service.NewOrderService().UpdateOrderTag(ctx, order.FweOrder.OrderID, order.TagMap); bizErr != nil {
		logs.CtxWarn(ctx, "[selectTransferOwnerTypeExecution.Process] set tag failed, err=%s", bizErr.Error())
		return bizErr
	}

	if param.TransferOwnerType == TransferOwnerBySelfStr {
		financeList := []*fwe_trade_common.FinanceInfo{
			{
				FinanceOrderType: int32(consts.SHSellCarTransferGuaranteePay),
				Amount:           param.TransferOwnerGuaranteeAmount,
			},
		}
		dbFinanceList, bizErr := packer.BuildFinanceList(ctx, e.GetOrder().FweOrder, e.conf.ConfigWithFinance, financeList)
		if bizErr != nil {
			logs.CtxError(ctx, "[selectTransferOwnerTypeExecution.Process] err=%s", bizErr.Error())
			return bizErr
		}
		if bizErr := service.NewFinanceOrderService().CreateFinanceOrderList(ctx, dbFinanceList); bizErr != nil {
			logs.CtxError(ctx, "[selectTransferOwnerTypeExecution.Process] err=%s", bizErr.Error())
			return bizErr
		}
	}

	return nil
}
