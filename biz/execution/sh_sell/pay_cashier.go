package sh_sell

import (
	CommonSHSell "code.byted.org/motor/fwe_trade_common/statemachine/sh_sell"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"context"

	"github.com/bytedance/sonic"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
)

type payCashierExecution struct {
	*executor.ActionBaseExecution
	conf     model.CommonConf
	payModel *sh_sell_model.CreateCashPayModel
	result   string
}

func NewPayCashierExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &payCashierExecution{
		payModel: new(sh_sell_model.CreateCashPayModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, &exe.conf, exe.payModel, options...)
	return exe
}

func (e *payCashierExecution) CheckParams(ctx context.Context) error {
	payModel := e.payModel
	if payModel.Identity == nil || payModel.Identity.BizScene == 0 || payModel.Identity.TenantType == 0 ||
		payModel.FinanceType == 0 || payModel.OrderId == "" {
		logs.CtxWarn(ctx, "[payCashierExecution.CheckParams] params empty")
		return errdef.NewParamsErr("param error")
	}
	return nil
}

func (e *payCashierExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	// 资金安全校验
	if e.conf.CheckContStructField {
		req := e.GetActionOrderReq()
		order := e.GetOrder()
		finance := packer.FinanceGetByType(order.FinanceList, e.payModel.FinanceType)
		if finance == nil {
			return errdef.NewRawErr(errdef.DataErr, "未找到对应的资金单")
		}

		param := &service.CheckAmountByContParam{
			OrderID:                   req.OrderID,
			FinanceOrderType:          e.payModel.FinanceType,
			Amount:                    finance.Amount,
			AllowIncomeAmountOverflow: e.conf.CheckContOptionAllowIncomeAmountOverflow,
		}
		pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
		if bizErr != nil {
			logs.CtxError(ctx, "[PreProcess] CheckAmountByCont failed, err=%s", bizErr.Error())
			return bizErr
		}
		if !pass {
			logs.CtxError(ctx, "[PreProcess] safe check not pass, blockMsg=%s", blockMsg)
			return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "支付合同校验未通过")
		}
	}

	return nil
}

func (e *payCashierExecution) Process(ctx context.Context) error {
	rpcReq, err := e.buildPayReq(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[payCashierExecution.Process] build pay req failed, err=%+v", err)
		return err
	}

	payOrderNo, payData, bizErr := service.NewTradePayment().CreateCashPay(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[payCashierExecution.Process] pay failed, err=%s", bizErr.Error())
		return bizErr
	}

	res := &sh_sell_model.CreateCashPayResult{
		PayData:    payData,
		PayOrderNo: payOrderNo,
	}

	e.result, err = sonic.MarshalString(res)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		return bizErr
	}
	return nil
}

func (e *payCashierExecution) Result() interface{} {
	return e.result
}

func (e *payCashierExecution) buildPayReq(ctx context.Context) (*payment.CreateCashPayReq, error) {
	var (
		req             = e.GetActionOrderReq()
		payModel        = e.payModel
		userId          = "user_id"
		currency        = payment.CurrencyType_CNY
		order           = e.GetOrder()
		finance         = packer.FinanceGetByType(order.FinanceList, payModel.FinanceType)
		callbackAction  string
		timeoutCBAction string
	)
	if finance == nil {
		return nil, errdef.NewRawErr(errdef.DataErr, "未找到对应的资金单")
	}
	if payModel.Operator != nil {
		userId = payModel.Operator.OperatorID
	}

	switch consts.BizScene(req.Identity.BizScene) {
	case consts.BizSceneSHSellByEarnestFinal:
		callbackAction = consts.ShSellEFPayEarnestOverEvent
		timeoutCBAction = consts.ShSellEFPayEarnestTimeoutEvent
	case consts.BizSceneSHSellInsurance:
		callbackAction = consts.ShSellWarrantyPayOverEvent
		timeoutCBAction = consts.ShSellWarrantyPayTimeoutEvent
	case consts.BizSceneSHConsignByEF:
		callbackAction = consts.ShSellEFPayEarnestOverEvent
	case consts.BizSceneSHACNByEF:
		callbackAction = consts.ShACNEFPayEarnestOverEvent
	case consts.BizSceneSHSellConsignRevoke:
		callbackAction = CommonSHSell.SHSellConsignRevokeCashPayFinishEt.Value()
		timeoutCBAction = CommonSHSell.SHSellConsignRevokeCashPayExpiredEt.Value()
	case consts.BizSceneSHSellByEarnestFinalDeliveryCar:
		if payModel.FinanceType == int32(consts.SHSellCarTransferGuaranteePay) {
			callbackAction = sh_state.ShSellEFDCTransferGuaranteePayOverEvent.Value()
			timeoutCBAction = sh_state.ShSellEFDCTransferGuaranteePayTimeoutEvent.Value()
		} else {
			callbackAction = sh_state.ShSellEFDCPayEarnestOverEvent.Value()
			timeoutCBAction = sh_state.ShSellEFDCPayEarnestTimeoutEvent.Value()
		}
	case consts.BizSceneSHSellByFullDeliveryCar:
		if payModel.FinanceType == int32(consts.SHSellCarTransferGuaranteePay) {
			callbackAction = sh_state.ShSellFullDCTransferGuaranteePayOverEvent.Value()
			timeoutCBAction = sh_state.ShSellFullDCTransferGuaranteePayTimeoutEvent.Value()
		}
	case consts.BizSceneSHConsignByEarnestFinalDeliveryCar:
		if payModel.FinanceType == int32(consts.SHSellCarTransferGuaranteePay) {
			callbackAction = sh_state.ShSellEFDCTransferGuaranteePayOverEvent.Value()
			timeoutCBAction = sh_state.ShSellEFDCTransferGuaranteePayTimeoutEvent.Value()
		} else {
			callbackAction = sh_state.ShSellEFDCPayEarnestOverEvent.Value()
		}
	case consts.BizSceneSHConsignByFullDeliveryCar:
		if payModel.FinanceType == int32(consts.SHSellCarTransferGuaranteePay) {
			callbackAction = sh_state.ShConsignFullDCTransferGuaranteePayOverEvent.Value()
			timeoutCBAction = sh_state.ShConsignFullDCTransferGuaranteePayTimeoutEvent.Value()
		}
	}

	rpcReq := &payment.CreateCashPayReq{
		Identity:        req.Identity,
		UserID:          userId,
		OrderID:         payModel.OrderId,
		FinanceType:     payModel.FinanceType,
		PayOrderNo:      payModel.PayOrderNo,
		CashierDeskType: payModel.CashierDeskType,
		OsType:          payModel.OsType,
		Currency:        &currency,
		TotalAmount:     finance.Amount,
		ExpireTime:      payModel.ExpireTime,
		RedirectURL:     payModel.RedirectUrl,
		IPAddress:       payModel.IpAddress,
		PayLimitList:    payModel.PayLimitList,
		CallbackEvent:   utils.MakeCallbackEvent(callbackAction),
		CallbackExtra:   "",
		Extra:           nil,
		Base:            base.NewBase(),
	}

	if timeoutCBAction != "" {
		rpcReq.TimeoutCallbackEvent = utils.MakeCallbackEvent(timeoutCBAction)
	}

	return rpcReq, nil
}
