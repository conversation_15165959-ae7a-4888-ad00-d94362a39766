package sh_sell

import (
	"context"

	"github.com/bytedance/sonic"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
)

type payPOSExecution struct {
	*executor.ActionBaseExecution
	posPayModel *sh_sell_model.CreatePOSPayModel
	conf        *sh_sell_model.Conf
	result      interface{}
}

func NewPayPOSExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &payPOSExecution{
		posPayModel: new(sh_sell_model.CreatePOSPayModel),
		conf:        new(sh_sell_model.Conf),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, exe.conf, exe.posPayModel, options...)
	return exe
}

func (e *payPOSExecution) CheckParams(ctx context.Context) error {
	if e.conf == nil || e.conf.POSPayMerchant == nil {
		logs.CtxWarn(ctx, "[payPOSExecution.CheckParams] no pos_pay_merchant conf")
		return errdef.NewRawErr(errdef.ParamErr, "缺少POS配置")
	}

	return nil
}

func (e *payPOSExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}
	if e.GetOrder().TagMap[DeliveryCarType] == DeliveryCarTypeRemote {
		return errdef.NewParamsErr("异地交车不允许使用 POS 支付")
	}

	// 资金安全校验
	if e.conf.CheckContStructField {
		req := e.GetActionOrderReq()
		param := &service.CheckAmountByContParam{
			OrderID:                   req.OrderID,
			FinanceOrderType:          e.posPayModel.FinanceType,
			Amount:                    e.posPayModel.Amount,
			AllowIncomeAmountOverflow: e.conf.CheckContOptionAllowIncomeAmountOverflow,
		}
		pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
		if bizErr != nil {
			logs.CtxError(ctx, "[PreProcess] CheckAmountByCont failed, err=%s", bizErr.Error())
			return bizErr
		}
		if !pass {
			logs.CtxError(ctx, "[PreProcess] safe check not pass, blockMsg=%s", blockMsg)
			return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "支付合同校验未通过")
		}
	}

	return nil
}

func (e *payPOSExecution) Process(ctx context.Context) error {
	// 收款账户信息(配置写死)
	sellerUID := e.conf.POSPayMerchant.ReceiveUID

	// 请求POS
	rpcReq, err := e.buildReq(ctx, sellerUID)
	if err != nil {
		logs.CtxWarn(ctx, "[payPOSExecution.Process] build pay req failed, err=%+v", err)
		return err
	}

	payPosRsp, bizErr := service.NewTradePayment().CreatePOSPay(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[payPOSExecution.Process] pay failed, err=%s", bizErr.Error())
		return bizErr
	}

	res := &sh_sell_model.CreateCashPayResult{
		PayData:    payPosRsp.PayData,
		PayOrderNo: payPosRsp.PayNo,
	}

	e.result, err = sonic.MarshalString(res)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		return bizErr
	}
	return nil
}

func (e *payPOSExecution) buildReq(ctx context.Context, sellerUID string) (*payment.CreatePOSPayReq, error) {
	var (
		req                   = e.GetActionOrderReq()
		payModel              = e.posPayModel
		userId                = "user_id"
		currency              = payment.CurrencyType_CNY
		callbackAction        string
		timeoutCallbackAction string
	)

	ipAddress := "*************"
	if payModel.IpAddress != nil {
		ipAddress = *(payModel.IpAddress)
	}

	switch consts.BizScene(req.Identity.BizScene) {
	case consts.BizSceneSHSellByEarnestFinal:
		callbackAction = consts.ShSellEFPayFinalOverEvent
		timeoutCallbackAction = consts.ShSellEFPayPOSTimeoutEvent
	case consts.BizSceneSHSellByFull:
		callbackAction = consts.ShSellFullPayMoneyOverEvent
		timeoutCallbackAction = consts.ShSellFullPayPOSTimeoutEvent
	case consts.BizSceneSHConsignByEF:
		callbackAction = consts.ShConsignEFPayFinalOverEvent
		timeoutCallbackAction = consts.ShConsignEFPayPOSTimeoutEvent
	case consts.BizSceneSHConsignByFull:
		callbackAction = consts.ShConsignFullPayMoneyOverEvent
		timeoutCallbackAction = consts.ShConsignFullPayPOSTimeoutEvent
	case consts.BizSceneSHACNByEF:
		callbackAction = consts.ShACNEFPayFinalOverEvent
		timeoutCallbackAction = consts.ShACNEFPayPOSTimeoutEvent
	case consts.BizSceneSHACNByFull:
		callbackAction = consts.ShACNFullPayMoneyOverEvent
		timeoutCallbackAction = consts.ShACNFullPayPOSTimeoutEvent
	case consts.BizSceneSHSellByEarnestFinalDeliveryCar:
		callbackAction = sh_state.ShSellEFDCPayFinalOverEvent.Value()
		timeoutCallbackAction = sh_state.ShSellEFDCPayPOSTimeoutEvent.Value()
	case consts.BizSceneSHSellByFullDeliveryCar:
		callbackAction = sh_state.ShSellFullDCPayMoneyOverEvent.Value()
		timeoutCallbackAction = sh_state.ShSellFullDCPayPOSTimeoutEvent.Value()
	case consts.BizSceneSHConsignByEarnestFinalDeliveryCar:
		callbackAction = sh_state.ShConsignEFDCPayFinalOverEvent.Value()
		timeoutCallbackAction = sh_state.ShConsignEFDCPayPOSTimeoutEvent.Value()
	case consts.BizSceneSHConsignByFullDeliveryCar:
		callbackAction = sh_state.ShConsignFullDCPayMoneyOverEvent.Value()
		timeoutCallbackAction = sh_state.ShConsignFullDCPayPOSTimeoutEvent.Value()
	}

	rpcReq := &payment.CreatePOSPayReq{
		Identity:             req.Identity,
		UserID:               userId,
		OrderID:              req.OrderID,
		FinanceOrderType:     payModel.FinanceType,
		PayOrderNo:           payModel.PayOrderNo,
		Currency:             &currency,
		TotalAmount:          payModel.Amount,
		ExpireTime:           payModel.ExpireTime,
		Extra:                nil,
		IPAddress:            ipAddress,
		CallbackEvent:        utils.MakeCallbackEvent(callbackAction),
		CallbackExtra:        "",
		SellerUID:            sellerUID,
		SellerUIDType:        consts.ShopUidType,
		TimeoutCallbackEvent: utils.MakeCallbackEvent(timeoutCallbackAction),
		Base:                 base.NewBase(),
	}

	return rpcReq, nil
}

func (e *payPOSExecution) Result() interface{} {
	return e.result
}
