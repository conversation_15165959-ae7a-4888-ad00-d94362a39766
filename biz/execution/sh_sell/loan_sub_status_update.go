package sh_sell

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type updateLoanSubStatusExecution struct {
	*executor.ActionBaseExecution
	conf  *sh_sell_model.Conf
	param *sh_sell_model.UpdateLoanSubStatusModel
}

func NewUpdateLoanSubStatusExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &updateLoanSubStatusExecution{
		conf:  new(sh_sell_model.Conf),
		param: new(sh_sell_model.UpdateLoanSubStatusModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.param)
	return e
}

func (e *updateLoanSubStatusExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	loanSubStatus := e.getLoanSubStatus(ctx)
	if loanSubStatus == "" {
		logs.CtxWarn(ctx, "[updateLoanSubStatusExecution.CheckParams] param error. param=%+v", e.param)
		return errdef.NewParamsErr("贷款子状态参数错误")
	}

	// TODO add checks
	return nil
}

func (e *updateLoanSubStatusExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})

	return nil
}

// Process 设置贷款类型
func (e *updateLoanSubStatusExecution) Process(ctx context.Context) error {
	var order = e.GetOrder()

	order.TagMap[LoanSubStatus] = e.getLoanSubStatus(ctx)
	if bizErr := service.NewOrderService().UpdateOrderTag(ctx, order.FweOrder.OrderID, order.TagMap); bizErr != nil {
		logs.CtxWarn(ctx, "[updateLoanSubStatusExecution.Process] set tag failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

// 获取贷款子状态
func (e *updateLoanSubStatusExecution) getLoanSubStatus(ctx context.Context) string {
	switch e.GetActionOrderReq().Action {
	case sh_state.ShSellEFDCLoanSubProcessStartEvent.Value(), sh_state.ShSellFullDCLoanSubProcessStartEvent.Value(), sh_state.ShConsignEFDCLoanSubProcessStartEvent.Value(), sh_state.ShConsignFullDCLoanSubProcessStartEvent.Value():
		return LoanSubStatusStartStr
	case sh_state.ShSellEFDCLoanSubProcessOverEvent.Value(), sh_state.ShSellFullDCLoanSubProcessOverEvent.Value(), sh_state.ShConsignEFDCLoanSubProcessOverEvent.Value(), sh_state.ShConsignFullDCLoanSubProcessOverEvent.Value():
		return LoanSubStatusOverStr
	}
	return ""
}
