package sh_sell

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type selectLoanTypeExecution struct {
	*executor.ActionBaseExecution
	param *sh_sell_model.LoanTypeSelectModel
}

func NewSelectLoanTypeExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &selectLoanTypeExecution{
		param: new(sh_sell_model.LoanTypeSelectModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param)
	return e
}

func (e *selectLoanTypeExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	param := e.param
	if param == nil || (param.LoanType != LoanFirst && param.LoanType != LoanAfter) {
		logs.CtxWarn(ctx, "[selectLoanTypeExecution.CheckParams] param error")
		return errdef.NewParamsErr("缺少贷款方式参数")
	}
	return nil
}

func (e *selectLoanTypeExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	param := &ShSellConditionParam{LoanType: e.param.LoanType}
	conditions := GetShSellCondition(ctx, e.GetOrder(), param)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

// Process 设置贷款类型
func (e *selectLoanTypeExecution) Process(ctx context.Context) error {
	var (
		param = e.param
		order = e.GetOrder()
	)

	loanType := LoanFirstStr
	if param.LoanType == LoanAfter {
		loanType = LoanAfterStr
	}
	order.TagMap[LoanType] = loanType

	bizErr := service.NewOrderService().UpdateOrderTag(ctx, order.FweOrder.OrderID, order.TagMap)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[selectLoanTypeExecution.Process] set tag failed, err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
