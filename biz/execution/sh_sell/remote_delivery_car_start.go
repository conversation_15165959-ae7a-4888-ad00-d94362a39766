package sh_sell

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type startRemoteDeliveryCarExecution struct {
	*executor.ActionBaseExecution
	conf  *sh_sell_model.Conf
	param *sh_sell_model.StartRemoteDeliveryCarModel
}

func NewStartRemoteDeliveryCarExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &startRemoteDeliveryCarExecution{
		param: new(sh_sell_model.StartRemoteDeliveryCarModel),
		conf:  new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.param)
	return e
}

func (e *startRemoteDeliveryCarExecution) CheckParams(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	if e.GetOrder().TagMap[DeliveryCarType] != DeliveryCarTypeRemote {
		return errdef.NewParamsErr("当前订单不是异地交车，非法调用")
	}
	return nil
}

func (e *startRemoteDeliveryCarExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	param := &ShSellConditionParam{}
	conditions := GetShSellCondition(ctx, e.GetOrder(), param)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})

	return nil
}

func (e *startRemoteDeliveryCarExecution) Process(ctx context.Context) error {
	return nil
}
