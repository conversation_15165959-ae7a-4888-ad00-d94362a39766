package sh_sell

import (
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
)

type refundExecution struct {
	*executor.ActionBaseExecution
	cancelModel *sh_sell_model.CancelModel
}

func NewCancelRefundExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &refundExecution{
		cancelModel: new(sh_sell_model.CancelModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req,
		nil, exe.cancelModel, options...)
	return exe
}

func (e *refundExecution) CheckParams(ctx context.Context) error {
	return nil
}

func (e *refundExecution) Process(ctx context.Context) error {
	// 退款
	rpcReq, err := e.buildRefundReq(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[refundExecution.Process] build refund req failed, err=%+v", err)
		return err
	}
	refundNo, bizErr := service.NewTradePayment().CreateCashRefund(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[refundExecution.Process] payment refund rpc failed, err=%+v", bizErr.Error())
		return bizErr
	}
	logs.CtxInfo(ctx, "[refundExecution.Process] refund req success, refundNo=%s", refundNo)

	return nil
}

func (e *refundExecution) buildRefundReq(ctx context.Context) (*payment.CreateCashRefundReq, error) {
	req := e.GetActionOrderReq()

	// 查资金单，找到对应退款信息
	financeOrder, err := db_query.FFinanceOrder.WithContext(ctx).
		Where(db_query.FFinanceOrder.OrderID.Eq(req.OrderID)).
		Where(db_query.FFinanceOrder.FinanceOrderType.Eq(int32(consts.SHSellCarEarnestPay))).
		First()
	if err != nil {
		logs.CtxError(ctx, "[refundExecution.buildRefundReq] query finance_order failed, err=%+v", err)
		return nil, err
	}

	// 拼req
	currency := payment.CurrencyType_CNY
	rpcReq := &payment.CreateCashRefundReq{
		Identity:      req.Identity,
		OrderID:       req.OrderID,
		FinanceType:   financeOrder.FinanceOrderType,
		Currency:      &currency,
		Amount:        financeOrder.Amount,
		Reason:        "",
		IPAddress:     nil,
		Extra:         nil,
		CallbackEvent: utils.MakeCallbackEvent(consts.ShSellEFRefundEarnestOverEvent),
		CallbackExtra: "",
		Base:          base.NewBase(),
	}
	return rpcReq, nil
}
