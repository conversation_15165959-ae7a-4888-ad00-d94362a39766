package sh_sell

import (
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"context"
	"strconv"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"github.com/bytedance/sonic"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

const (
	PlatformCreditKey = "platform"
)

type createContractExecution struct {
	*executor.ActionBaseExecution
	contractReq *sh_sell_model.SignContractModel
	conf        *sh_sell_model.Conf
	contSerial  string
}

func NewCreateContractExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	e := &createContractExecution{
		contractReq: new(sh_sell_model.SignContractModel),
		conf:        new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, e.conf, e.contractReq, options...)
	return e
}

func (e *createContractExecution) CheckParams(ctx context.Context) error {
	contractType := e.contractReq.ContractType
	contTypeName := consts.ContractType(contractType).Name()
	if contTypeName == "" {
		logs.CtxWarn(ctx, "[createContractExecution.CheckParams] contType is unknown, contType=%d", contractType)
		return errdef.NewRawErr(errdef.UnknownContractType, "合同类型错误")
	}

	contInfo := e.conf.ContInfoMap[contTypeName]
	if contInfo == nil || contInfo.TmplID == 0 {
		logs.CtxWarn(ctx, "[createContractExecution.CheckParams] contInfo not config, contTypeName=%s", contTypeName)
		return errdef.NewRawErr(errdef.LackConfigErr, "合同模板未配置")
	}
	return nil
}

func (e *createContractExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[createContractExecution] PreProcess failed, err=%+v", err)
		return err
	}

	if !e.conf.CheckContStructField || e.contractReq.TmplId == 0 {
		return nil
	}

	// 校验
	param := &service.CheckContTotalAmountParam{
		TmplID:           e.contractReq.TmplId,
		TmplParams:       e.contractReq.ContParams,
		OrderTotalAmount: e.GetOrder().FweOrder.TotalAmount,
		BizScene:         e.GetBizIdentity().BizScene,
		ContType:         e.contractReq.ContractType,
		OrderID:          e.GetActionOrderReq().OrderID,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckContTotalAmount(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[createContractExecution] CheckContTotalAmount failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[createContractExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "合同总金额和订单不一致")
	}

	return nil

}

func (e *createContractExecution) Process(ctx context.Context) error {

	// 构建参数
	createParam, bizErr := e.buildCreateContParam(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createContractExecution] buildCreateContParam failed, err=%s", bizErr.Error())
		return bizErr
	}

	// service
	contSerial, bizErr := service.NewContractService().CreateContract(ctx, createParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[BaseContStartExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.contSerial = contSerial
	return nil
}

func (e *createContractExecution) Result() interface{} {
	res := &sh_sell_model.SignContractResult{
		ContSerial: e.contSerial,
	}

	resJson, _ := sonic.MarshalString(res)
	return resJson
}

func (e *createContractExecution) buildCreateContParam(ctx context.Context) (*service_model.ContractCreateParam, *errdef.BizErr) {
	var (
		req          = e.GetActionOrderReq()
		contractReq  = e.contractReq
		conf         = e.conf
		fweOrder     = e.GetOrder().FweOrder
		financeOrder *db_model.FFinanceOrder
		operator     = e.GetActionOrderReq().GetOperator()
		contTypeName = consts.ContractType(contractReq.ContractType).Name()
		contInfo     = conf.ContInfoMap[contTypeName]
		bizScene     = req.Identity.BizScene
	)

	financeType, callbackAction := e.getConfigByScene(contractReq.ContractType, bizScene)
	financeOrder = packer.FinanceGetByType(e.GetOrder().FinanceList, financeType)
	if financeOrder == nil {
		logs.CtxWarn(ctx, "[createContractExecution.buildCreateContParam] no finance order, contractType=%d", contractReq.ContractType)
		return nil, errdef.NewRawErr(errdef.DirtyDataException, "缺少资金单数据")
	}

	operatorId, _ := strconv.ParseInt(operator.OperatorID, 10, 64)

	signParties, bizErr := e.buildSignParties(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[buildCreateContParam] build sign parties failed, err=%s", bizErr.Error())
		return nil, bizErr
	}
	var tmplId = contInfo.TmplID
	if contractReq.TmplId != 0 {
		tmplId = contractReq.TmplId
	}
	param := &service_model.ContractCreateParam{
		OrderID:        fweOrder.OrderID,
		TenantType:     fweOrder.TenantType,
		BizScene:       fweOrder.BizScene,
		ContType:       contractReq.ContractType,
		OperatorID:     operatorId,
		OperatorName:   operator.OperatorName,
		TmplID:         tmplId,
		NeedSignNoCert: false,
		SmsTmplID:      contInfo.SmsTmplID,
		SmsChannelID:   contInfo.SmsChannel,
		TmplParams:     contractReq.ContParams,
		SignPartList:   signParties,
		InOutData: &service_model.InOutData{
			Currency: 1,
			TotalOut: 0,
			TotalIn:  financeOrder.Amount,
		},
		CallbackEvent: utils.MakeCallbackEvent(callbackAction),
		CallbackExtra: utils.MakeContractCallbackExtra(fweOrder.OrderID),
	}

	return param, nil
}

func (e *createContractExecution) getConfigByScene(contractType int32, bizScene int32) (int32, string) {
	type config struct {
		FinanceType    consts.FinanceOrderType
		CallbackAction string
	}

	configMap := map[consts.BizScene]map[consts.ContractType]config{
		// 自营卖车-订金尾款
		consts.BizSceneSHSellByEarnestFinal: {
			consts.SHSellIntentCont: {
				FinanceType:    consts.SHSellCarEarnestPay,
				CallbackAction: consts.ShSellEFSignIntentContractOverEvent,
			},
			consts.SHSellSaleCont: {
				FinanceType:    consts.SHSellCarFinalPay,
				CallbackAction: consts.ShSellEFSignSellContractOverEvent,
			},
		},
		// 自营卖车-全款
		consts.BizSceneSHSellByFull: {
			consts.SHSellSaleCont: {
				FinanceType:    consts.SHSellCarTotalPay,
				CallbackAction: consts.ShSellFullSignSellContractOverEvent,
			},
		},
		// 自营卖车-延保
		consts.BizSceneSHSellInsurance: {
			consts.SHSellInsuranceCont: {
				FinanceType:    consts.SHSellCarTotalPay,
				CallbackAction: consts.ShSellWarrantySignContractOverEvent,
			},
		},
		// 自营内网寄售-订金尾款
		consts.BizSceneSHConsignByEF: {
			consts.SHSellIntentCont: {
				FinanceType:    consts.SHSellCarEarnestPay,
				CallbackAction: consts.ShConsignEFSignIntentContractOverEvent,
			},
			consts.SHSellSaleCont: {
				FinanceType:    consts.SHSellCarFinalPay,
				CallbackAction: consts.ShConsignEFSignSellContractOverEvent,
			},
		},
		// 自营内网寄售-全款
		consts.BizSceneSHConsignByFull: {
			consts.SHSellSaleCont: {
				FinanceType:    consts.SHSellCarTotalPay,
				CallbackAction: consts.ShConsignFullSignSellContractOverEvent,
			},
		},
		// ACN-订金尾款
		consts.BizSceneSHACNByEF: {
			consts.SHSellIntentCont: {
				FinanceType:    consts.SHSellCarEarnestPay,
				CallbackAction: consts.ShACNEFSignIntentContractOverEvent,
			},
			consts.SHSellSaleCont: {
				FinanceType:    consts.SHSellCarFinalPay,
				CallbackAction: consts.ShACNEFSignSellContractOverEvent,
			},
		},
		// ACN-全款
		consts.BizSceneSHACNByFull: {
			consts.SHSellSaleCont: {
				FinanceType:    consts.SHSellCarTotalPay,
				CallbackAction: consts.ShACNFullSignSellContractOverEvent,
			},
		},
		// 自营卖车-订金尾款
		consts.BizSceneSHSellByEarnestFinalDeliveryCar: {
			consts.SHSellIntentCont: {
				FinanceType:    consts.SHSellCarEarnestPay,
				CallbackAction: sh_state.ShSellEFSignIntentContractOverEvent.Value(),
			},
			consts.SHSellSaleCont: {
				FinanceType:    consts.SHSellCarFinalPay,
				CallbackAction: sh_state.ShSellEFSignSellContractOverEvent.Value(),
			},
		},
		// 自营卖车-全款 交车
		consts.BizSceneSHSellByFullDeliveryCar: {
			consts.SHSellSaleCont: {
				FinanceType:    consts.SHSellCarTotalPay,
				CallbackAction: sh_state.ShSellFullDCSignSellContractOverEvent.Value(),
			},
		},
		// 自营内网寄售-订金尾款
		consts.BizSceneSHConsignByEarnestFinalDeliveryCar: {
			consts.SHSellIntentCont: {
				FinanceType:    consts.SHSellCarEarnestPay,
				CallbackAction: consts.ShConsignEFSignIntentContractOverEvent,
			},
			consts.SHSellSaleCont: {
				FinanceType:    consts.SHSellCarFinalPay,
				CallbackAction: consts.ShConsignEFSignSellContractOverEvent,
			},
		},
		// 自营内网寄售-全款 交车
		consts.BizSceneSHConsignByFullDeliveryCar: {
			consts.SHSellSaleCont: {
				FinanceType:    consts.SHSellCarTotalPay,
				CallbackAction: sh_state.ShConsignFullDCSignSellContractOverEvent.Value(),
			},
		},
	}

	if contConfig, exist := configMap[consts.BizScene(bizScene)]; exist {
		if c, exist := contConfig[consts.ContractType(contractType)]; exist {
			return c.FinanceType.Int32(), c.CallbackAction
		}
	}
	return 0, ""
}

func (e *createContractExecution) buildSignParties(ctx context.Context) ([]*service_model.SignPart, *errdef.BizErr) {
	var (
		contType = e.contractReq.ContractType
		fweOrder = e.GetOrder().FweOrder

		buyerSignParty  = new(service_model.SignPart)
		sellerSignParty = new(service_model.SignPart)

		res []*service_model.SignPart
	)

	buyer, bizErr := packer.CommonTradeSubjectDeserialize(fweOrder.BuyerID, *fweOrder.BuyerExtra)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[buildSignParties] deserialize buyer info failed, err=%s", bizErr.Error())
		return nil, bizErr
	}
	seller, bizErr := packer.CommonTradeSubjectDeserialize(fweOrder.SellerID, *fweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[buildSignParties] deserialize seller info failed, err=%s", bizErr.Error())
		return nil, bizErr
	}

	bizErr = e.assignSubjectInfo(ctx, buyerSignParty, buyer)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[buildSignParties] assign buyer info failed, err=%s", bizErr.Error())
		return nil, bizErr
	}
	bizErr = e.assignSubjectInfo(ctx, sellerSignParty, seller)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[buildSignParties] assign seller info failed, err=%s", bizErr.Error())
		return nil, bizErr
	}

	switch consts.ContractType(contType) {
	case consts.SHSellIntentCont, consts.SHSellSaleCont, consts.SHSellInsuranceCont: // 订金、买卖合同，甲方是买方，乙方是卖方; 延保合同待定
		buyerSignParty.SignPosition = int32(core.SignPosition_PA)
		buyerSignParty.IsInner = false
		sellerSignParty.SignPosition = int32(core.SignPosition_PB)
		sellerSignParty.IsInner = true
		res = []*service_model.SignPart{buyerSignParty, sellerSignParty}
	}
	return res, nil
}

func (e *createContractExecution) assignSubjectInfo(ctx context.Context, signPart *service_model.SignPart,
	subjectInfo *fwe_trade_common.TradeSubjectInfo) *errdef.BizErr {

	var (
		req         = e.GetActionOrderReq()
		bizScene    = req.Identity.BizScene
		contractReq = e.contractReq
		creditInfo  = e.conf.CreditInfo
	)

	signPart.SignerName = contractReq.SignerName
	signPart.SignerPhone = contractReq.SignerPhone

	switch subjectInfo.SubjectType {
	case fwe_trade_common.TradeSubjectType_Person:
		person := subjectInfo.PersonInfo
		signPart.CardType = int32(core.CardType_Identity)
		signPart.IdentID = person.IDCard
		signPart.IdentName = person.PersonName
	case fwe_trade_common.TradeSubjectType_Company:
		company := subjectInfo.CompanyInfo
		signPart.CardType = int32(core.CardType_CreditCode)
		signPart.IdentID = company.CreditCode
		signPart.IdentName = company.CompanyName
	case fwe_trade_common.TradeSubjectType_FweMerchant:
		signPart.CardType = int32(core.CardType_CreditCode)

		// 内网&寄售和平台签合同, 自营卖车和门店签合同
		var creditKey string

		if slices.Contains([]int32{int32(consts.BizSceneSHConsignByEF), int32(consts.BizSceneSHConsignByFull), int32(consts.BizSceneSHConsignByEarnestFinalDeliveryCar), int32(consts.BizSceneSHConsignByFullDeliveryCar), int32(consts.BizSceneSHACNByEF), int32(consts.BizSceneSHACNByFull)}, bizScene) {
			creditKey = PlatformCreditKey
		} else {
			creditKey = subjectInfo.FweMerchant.FweAccountID
		}

		if _, exist := creditInfo[creditKey]; !exist {
			logs.CtxWarn(ctx, "[assignSubjectInfo] credit info not exist, creditKey=%s", creditKey)
			return errdef.NewRawErr(errdef.LackConfigErr, "信用代码未配置")
		}

		signPart.IdentID = creditInfo[creditKey].CreditCode
		signPart.IdentName = creditInfo[creditKey].Name
	}

	return nil
}
