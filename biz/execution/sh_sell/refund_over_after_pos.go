package sh_sell

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type refundOverAfterPOSExecution struct {
	*callback.RefundCallbackBaseExecution
	confPtr *sh_sell_model.Conf
}

func NewRefundOverAfterPOSExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)

	e := &refundOverAfterPOSExecution{
		confPtr: new(sh_sell_model.Conf),
	}
	e.RefundCallbackBaseExecution = callback.NewRefundCallbackBaseExecution(ctx, req, e.confPtr)
	return e
}

func (e *refundOverAfterPOSExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})

	return nil
}

func (e *refundOverAfterPOSExecution) CheckParams(ctx context.Context) error {
	return nil
}

// Process ...
func (e *refundOverAfterPOSExecution) Process(ctx context.Context) error {
	return nil
}
