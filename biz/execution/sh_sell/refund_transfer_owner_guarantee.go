package sh_sell

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"context"
)

type refundTransferOwnerGuaranteeExecution struct {
	*executor.ActionBaseExecution
	confPtr *sh_sell_model.Conf
	param   *sh_sell_model.RefundTransferOwnerGuaranteeModel
}

func NewRefundTransferOwnerGuaranteeExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)

	e := &refundTransferOwnerGuaranteeExecution{
		confPtr: new(sh_sell_model.Conf),
		param:   new(sh_sell_model.RefundTransferOwnerGuaranteeModel),
	}

	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, e.confPtr, e.param)
	return e
}

func (e *refundTransferOwnerGuaranteeExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	param := e.param
	if param == nil {
		logs.CtxWarn(ctx, "[refundTransferOwnerGuaranteeExecution.CheckParams] param error. param=%+v", param)
		return errdef.NewParamsErr("缺少退还过户保证金参数")
	}
	if param.RefundAmount < 0 {
		logs.CtxWarn(ctx, "[refundTransferOwnerGuaranteeExecution.CheckParams] param error. param=%+v", param)
		return errdef.NewParamsErr("退还过户保证金金额不能小于0")
	}
	return nil
}

func (e *refundTransferOwnerGuaranteeExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	param := &ShSellConditionParam{RefundTransferGuaranteeAmount: e.param.RefundAmount}
	conditions := GetShSellCondition(ctx, e.GetOrder(), param)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

func (e *refundTransferOwnerGuaranteeExecution) Process(ctx context.Context) error {
	if e.param.RefundAmount <= 0 {
		return nil
	}
	// 退款
	rpcReq, err := e.buildRefundReq()
	if err != nil {
		logs.CtxWarn(ctx, "[refundTransferOwnerGuaranteeExecution.Process] build refund req failed, err=%+v", err)
		return err
	}

	refundNo, bizErr := service.NewTradePayment().MergeRefund(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[refundTransferOwnerGuaranteeExecution.Process] payment refund rpc failed, err=%+v", bizErr.Error())
		return bizErr
	}
	logs.CtxInfo(ctx, "[refundTransferOwnerGuaranteeExecution.Process] refund req success, refundNo=%s", refundNo)
	return nil
}

func (e *refundTransferOwnerGuaranteeExecution) buildRefundReq() (*payment.MergeRefundReq, *errdef.BizErr) {
	// 查资金单，找到对应退款信息
	var (
		order    = e.GetOrder()
		orderID  = e.GetOrder().FweOrder.OrderID
		fweOrder = order.FweOrder
	)
	refundList := e.buildSingleRefund(order.FinanceList, e.param.RefundAmount, consts.SHSellCarTransferGuaranteePay)
	if len(refundList) == 0 {
		return nil, errdef.NewRawErr(errdef.DataErr, "refund list is empty")
	}
	for _, refund := range refundList {
		if refund.Amount < e.param.RefundAmount {
			return nil, errdef.NewParamsErr("refund amount is large than finance order amount")
		}
	}
	serviceReq := &payment.MergeRefundReq{
		Identity:          e.GetBizIdentity(),
		RefundList:        refundList,
		OrderID:           orderID,
		RefundFinanceType: 1,
		OrderName:         fweOrder.OrderName,
		Reason:            "过户保证金退款",
		Extra:             nil,
		CallbackEvent:     utils.MakeCallbackEvent(e.getTransferGuaranteeRefundOverEvent()),
		CallbackExtra:     "",
		IPAddress:         "*************",
	}

	return serviceReq, nil
}

func (e *refundTransferOwnerGuaranteeExecution) buildSingleRefund(input []*db_model.FFinanceOrder, amount int64, financeOrderType consts.FinanceOrderType) (output []*payment.SingleRefund) {
	for _, v := range input {
		if v == nil ||
			v.TradeType != consts.TradeTypePayCashier.String() ||
			v.FinanceOrderType != financeOrderType.Int32() {
			continue
		}
		output = append(output, &payment.SingleRefund{
			FinanceOrderID: v.FinanceOrderID,
			Amount:         amount,
		})
		return // 找到一个就返回
	}
	return
}

func (e *refundTransferOwnerGuaranteeExecution) getTransferGuaranteeRefundOverEvent() string {
	switch e.GetBizIdentity().BizScene {
	case consts.BizSceneSHSellByEarnestFinalDeliveryCar.Int32():
		return sh_state.ShSellEFDCTransferGuaranteeRefundOverEvent.Value()
	case consts.BizSceneSHSellByFullDeliveryCar.Int32():
		return sh_state.ShSellFullDCTransferGuaranteeRefundOverEvent.Value()
	case consts.BizSceneSHConsignByEarnestFinalDeliveryCar.Int32():
		return sh_state.ShConsignEFDCTransferGuaranteeRefundOverEvent.Value()
	case consts.BizSceneSHConsignByFullDeliveryCar.Int32():
		return sh_state.ShConsignFullDCTransferGuaranteeRefundOverEvent.Value()
	}
	return ""
}
