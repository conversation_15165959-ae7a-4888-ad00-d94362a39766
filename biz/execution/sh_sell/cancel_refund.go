package sh_sell

import (
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"context"
	"github.com/bytedance/sonic"
)

type unionRefundExecution struct {
	*executor.ActionBaseExecution
	bizReq sh_sell_model.RefundReq
	conf   sh_sell_model.Conf
	result interface{}
}

// NewUnionRefundExecution 逆向取消订单并退款
func NewUnionRefundExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	e := &unionRefundExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq, opts...)
	return e
}

func (e *unionRefundExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.bizReq
	if bizReq.RefundList == nil || len(bizReq.RefundList) == 0 {
		bizErr := errdef.NewParamsErr("RefundList 参数错误")
		return bizErr
	}
	for _, refund := range bizReq.RefundList {
		if refund.Amount <= 0 {
			bizErr := errdef.NewParamsErr("RefundList.amount 参数错误")
			return bizErr
		}
	}
	return nil
}

func (e *unionRefundExecution) Process(ctx context.Context) error {

	var (
		cashRefundReq *payment.MergeRefundReq
		mergeRefundNo string
		bizErr        *errdef.BizErr
		err           error
		order         = e.GetOrder()
		subStatusMap  = e.GetStateMachine().GetOriginalSubStates()
	)
	if !AllowCancelRefund(subStatusMap) {
		// 存在在途贷款资金，不允许取消
		return errdef.NewRawErr(errdef.FinanceFlowErr, "存在审批中/在途贷款资金，不允许取消")
	}
	// refund req
	cashRefundReq, bizErr = e.buildRefundReq()
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr = service.NewTradePayment().MergeRefund(ctx, cashRefundReq)
	if bizErr != nil {
		return bizErr
	}

	rpcRsp := &action_model.CreateRefundRsp{
		MergeRefundID: mergeRefundNo,
	}
	e.result, err = sonic.MarshalString(rpcRsp)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		return bizErr
	}

	// 2。更新tag
	if e.bizReq.Tag == nil || len(e.bizReq.Tag) == 0 {
		return nil
	}
	bizErr = service.NewOrderService().UpdateOrderTag(ctx, order.FweOrder.OrderID, e.bizReq.Tag)
	if bizErr != nil {
		return bizErr
	}
	return nil
}

func (e *unionRefundExecution) buildRefundReq() (*payment.MergeRefundReq, *errdef.BizErr) {
	var (
		order            = e.GetOrder()
		orderID          = e.GetOrder().FweOrder.OrderID
		fweOrder         = order.FweOrder
		bizReq           = e.bizReq
		refundSettleInfo = new(payment.RefundSettleInfo)
	)

	refundList := make([]*payment.SingleRefund, 0, len(bizReq.RefundList))
	hasPos := false
	for _, refund := range bizReq.RefundList {
		financeOrder := packer.FinanceGetByType(order.FinanceList, refund.FinanceOrderType)
		if financeOrder == nil {
			return nil, errdef.NewRawErr(errdef.DataErr, "找不到对应的资金单")
		}
		if financeOrder.TradeType == consts.TradeTypePayPos.String() {
			hasPos = true
		}
		refundList = append(refundList, &payment.SingleRefund{
			FinanceOrderID: financeOrder.FinanceOrderID,
			Amount:         refund.Amount,
		})
	}
	// 补充 pos 分账和补贴uid
	if hasPos {
		posMerchant := e.conf.POSPayMerchant
		if posMerchant == nil || posMerchant.SettleUID == "" || posMerchant.ReceiveUID == "" {
			return nil, errdef.NewRawErr(errdef.LackConfigErr, "缺少配置")
		}
		refundSettleInfo = &payment.RefundSettleInfo{
			SettleUID:      posMerchant.SettleUID,
			SettleUIDType:  posMerchant.SettleUIDType,
			SubsidyUID:     posMerchant.ReceiveUID,
			SubsidyUIDType: int32(consts.ShopUIDType),
		}
	}

	serviceReq := &payment.MergeRefundReq{
		Identity:          e.GetBizIdentity(),
		RefundList:        refundList,
		OrderID:           orderID,
		RefundFinanceType: bizReq.RefundType,
		OrderName:         fweOrder.OrderName,
		Reason:            bizReq.Reason,
		Extra:             nil,
		CallbackEvent:     utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:     "",
		IPAddress:         bizReq.IpAddress,
		RefundSettleInfo:  refundSettleInfo,
	}
	return serviceReq, nil
}
