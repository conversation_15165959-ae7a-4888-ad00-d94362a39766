package sh_sell

import (
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"context"
	"sort"
	"strings"

	"github.com/bytedance/sonic"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
)

func CheckActionReq(ctx context.Context, actionReq *engine.ActionOrderReq) *errdef.BizErr {
	if actionReq == nil || actionReq.Identity == nil || actionReq.Identity.TenantType == 0 ||
		actionReq.Identity.BizScene == 0 || actionReq.OrderID == "" || actionReq.Action == "" {
		logs.CtxWarn(ctx, "[CheckActionReq] param empty")
		return errdef.NewParamsErr("有必传参数为空，请检查")
	}
	return nil
}

type ShSellConditionParam struct {
	LoanType                      int32
	RefundTransferGuaranteeAmount int64
	TransferOwnerType             string
	HasOfflinePay                 bool
}

// GetShSellCondition 获取状态机条件
func GetShSellCondition(ctx context.Context, order *service_model.Order, param *ShSellConditionParam) map[string]interface{} {
	var (
		hasLoan, isLoanFirst                         bool
		IsPlatformTransferOwner, IsSelfTransferOwner bool
		IsLoanOver, isLoanNotStart                   bool
		fweOrder                                     = order.FweOrder
		tagMap                                       = order.TagMap
	)

	if fweOrder.TradeType == int32(fwe_trade_common.TradeType_EarnestFinalLoan) ||
		fweOrder.TradeType == int32(fwe_trade_common.TradeType_FinalLoan) {
		hasLoan = true
	}

	if loanType, exist := tagMap[LoanType]; exist && loanType == LoanFirstStr {
		isLoanFirst = true
	}

	if param.LoanType == LoanFirst {
		isLoanFirst = true
	}

	if param.TransferOwnerType != "" {
		IsPlatformTransferOwner = param.TransferOwnerType == TransferOwnerByPlatformStr
		IsSelfTransferOwner = param.TransferOwnerType == TransferOwnerBySelfStr
	} else {
		if transferOwnerType, exist := tagMap[TransferOwnerType]; exist {
			IsPlatformTransferOwner = transferOwnerType == TransferOwnerByPlatformStr
			IsSelfTransferOwner = transferOwnerType == TransferOwnerBySelfStr
		}
	}
	isLoanNotStart = true
	if loanSbbStatus, exist := tagMap[LoanSubStatus]; exist {
		isLoanNotStart = false
		IsLoanOver = loanSbbStatus == LoanSubStatusOverStr
	}
	return map[string]interface{}{
		consts.CondShSellHasLoan.Val():                   hasLoan,
		consts.CondShSellIsLoanFirst.Val():               isLoanFirst,
		sh_state.CondShSellIsPlatformTransferOwner.Val(): IsPlatformTransferOwner,             // 是否平台过户
		sh_state.CondShSellIsSelfTransferOwner.Val():     IsSelfTransferOwner,                 // 是否自主过户
		consts.CondParamRefundTransferGuaranteeAmount:    param.RefundTransferGuaranteeAmount, // 退还过户保证金金额
		sh_state.CondShSellIsLoanNotStart.Val():          isLoanNotStart,                      // 是否未发起放贷
		sh_state.CondShSellIsLoanOver.Val():              IsLoanOver,                          // 是否已完成放贷
		sh_state.CondShSellIsOrderCancelled.Val():        IsOrderCanceled(order),              // 是否已取消订单
		sh_state.CondShSellIsNeedAgreementPay.Val():      !param.HasOfflinePay,                // 是否需要协议代付
	}
}

const (
	DeliveryCarCancelRefundEndSt     = 101
	DeliveryCarCancelRefundProcessSt = 198
	DeliveryCarCanceling             = 199
	DeliveryCarCancellation          = 200
)

func IsOrderCanceled(order *service_model.Order) bool {
	return slices.Contains([]int32{DeliveryCarCancelRefundEndSt, DeliveryCarCancelRefundProcessSt, DeliveryCarCanceling, DeliveryCarCancellation}, order.FweOrder.OrderStatus)
}

func HasLoan(order *service_model.Order) bool {
	var fweOrder = order.FweOrder
	if fweOrder.TradeType == int32(fwe_trade_common.TradeType_EarnestFinalLoan) ||
		fweOrder.TradeType == int32(fwe_trade_common.TradeType_FinalLoan) {
		return true
	}
	return false
}

func IsLoanFirst(order *service_model.Order) bool {
	var tagMap = order.TagMap
	if loanType, exist := tagMap[LoanType]; exist && loanType == LoanFirstStr {
		return true
	}
	return false
}

func IsLoanSubStatusOver(order *service_model.Order) bool {
	var tagMap = order.TagMap
	if loanType, exist := tagMap[LoanSubStatus]; exist && loanType == LoanSubStatusOverStr {
		return true
	}
	return false
}

func HasProcessLoan(order *service_model.Order) bool {
	value, exist := order.TagMap[LoanSubStatus]
	if exist && value == LoanSubStatusStartStr {
		return true
	}
	return false
}

func HasLoanOver(order *service_model.Order) bool {
	value, exist := order.TagMap[LoanSubStatus]
	if exist && value == LoanSubStatusOverStr {
		return true
	}
	return false
}

const (
	subStatusHead    int = 30
	loanApprovalWait int = 31
)

func AllowCancelRefund(subStatus map[int]int) bool {
	var value int
	if len(subStatus) == 0 {
		return true
	}
	value, exist := subStatus[subStatusHead]
	if exist && value != loanApprovalWait {
		return false
	}
	return true
}

type CreateSettleReq struct {
	order        *service_model.Order
	receiveUID   string
	autoWithdraw bool
	fcType       string
	fcSceneCode  int64
}

func CreateSettle(ctx context.Context, req *CreateSettleReq) *errdef.BizErr {
	var (
		financeOrders = req.order.FinanceList
		order         = req.order
		orderID       = req.order.FweOrder.OrderID

		//callbackAction string
	)
	// 做结算
	//switch consts.BizScene(bizScene) {
	//case consts.BizSceneSHSellByEarnestFinal:
	//	callbackAction = consts.ShSellEFSettleOverEvent
	//case consts.BizSceneSHSellByFull:
	//	callbackAction = consts.ShSellFullSettleOverEvent
	//case consts.BizSceneSHConsignByEF:
	//	callbackAction = consts.ShConsignEFSettleOverEvent
	//case consts.BizSceneSHConsignByFull:
	//	callbackAction = consts.ShConsignFullSettleOverEvent
	//}

	// 读分账明细
	orderSplitInfo, err := service.NewSplitInfoService().GetSplitInfosByOrderID(ctx, orderID)
	if err != nil {
		logs.CtxWarn(ctx, "[CreateSettle] read split info failed, err=%+v", err)
		return errdef.NewBizErr(errdef.AccountRpcErr, err, "")
	}
	splitInfo, bizErr := packSplitInfo(ctx, order, orderSplitInfo)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CreateSettle] read split info failed, err=%+v", bizErr.Error())
		return bizErr
	}

	settleFinanceOrderID, financeIDList := SettleGroupFinanceOrderIDs(financeOrders)
	if len(financeIDList) == 0 {
		logs.CtxWarn(ctx, "[CreateSettle] no finance list to settle")
		return errdef.NewRawErr(errdef.DataErr, "无可结算的资金单")
	}

	// 如果需要自动提现 将提现fc参数填入extra payment将会解析
	bizExtra := ""
	if req.autoWithdraw {
		extra := struct {
			FcType      string `json:"FcType"`
			FcSceneCode int64  `json:"FcSceneCode"`
		}{
			FcType:      req.fcType,
			FcSceneCode: req.fcSceneCode,
		}
		bizExtra, _ = sonic.MarshalString(extra)
	}

	// 请求分账
	settleReq := &payment.MergeSettleReq{
		Identity: &fwe_trade_common.BizIdentity{
			TenantType: tenant_base.TenantType(order.FweOrder.TenantType),
			BizScene:   order.FweOrder.BizScene,
		},
		FinanceList:          financeIDList,
		SplitList:            splitInfo,
		SettleFinanceOrderID: settleFinanceOrderID,
		OrderID:              orderID,
		OrderName:            "",
		ReceiveUID:           req.receiveUID,
		ReceiveUIDType:       consts.ShopUidType,
		CallbackEvent:        "",
		CallbackExtra:        "",
		IsAutoWithdraw:       conv.BoolPtr(req.autoWithdraw),
		BizExtra:             conv.StringPtr(bizExtra),
		Base:                 base.NewBase(),
	}
	bizErr = service.NewTradePayment().MergeSettle(ctx, settleReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CreateSettle] rpc failed, err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func SettleGroupFinanceOrderIDs(financeOrders []*db_model.FFinanceOrder) (string, []string) {
	if len(financeOrders) == 0 {
		return "", nil
	}
	list := make([]string, 0)
	for _, v := range financeOrders {
		if v.TradeType != consts.TradeTypePayPos.String() {
			continue
		}
		list = append(list, v.FinanceOrderID)
	}

	sort.Strings(list)

	return strings.Join(list, "-"), list
}

// packSplitInfo 如果没有splitInfo信息，则100%分账给二级商户
func packSplitInfo(ctx context.Context, order *service_model.Order, splitInfo *fwe_trade_common.TradeSpiltInfo) ([]*payment.SplitInfo, *errdef.BizErr) {
	var (
		fweOrder = order.FweOrder

		res = make([]*payment.SplitInfo, 0)
	)

	splitMethod := splitInfo.GetSplitMethod()
	splitUnits := splitInfo.Detail

	// 如果没有splitInfo信息，取卖家，并100%分账给卖家
	if len(splitUnits) == 0 {
		seller, bizErr := packer.CommonTradeSubjectDeserialize(fweOrder.SellerID, *fweOrder.SellerExtra)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[packSplitInfo] deserialize seller info failed, err=%s", bizErr.Error())
			return nil, bizErr
		}
		if seller.SubjectType != fwe_trade_common.TradeSubjectType_FweMerchant {
			logs.CtxWarn(ctx, "[packSplitInfo] seller's subject type is not fwe_merchant")
			return nil, errdef.NewRawErr(errdef.DataErr, "卖家不是四轮商户")
		}
		splitUnits = append(splitUnits, &fwe_trade_common.TradeSplitUnit{
			SplitUID:     seller.FweMerchant.FweAccountID,
			SplitUIDType: fwe_trade_common.SplitUIDType_Shop,
			Scale:        OneHundredScale,
		})
	}
	if splitMethod == 0 {
		splitMethod = fwe_trade_common.SplitMethod_ByScale
	}

	// 获取四轮商户对应的分账真实uid
	accountMapping, bizErr := getSplitUIDs(ctx, order, splitUnits)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[packSplitInfo] getSplitUIDs failed, err=%s", bizErr.Error())
		return nil, bizErr
	}

	// pack payment split_info
	for _, v := range splitUnits {
		var info *payment.SplitInfo
		if v.SplitUIDType == fwe_trade_common.SplitUIDType_Shop {
			info = &payment.SplitInfo{
				UID:         accountMapping[v.SplitUID],
				UIDType:     consts.ShopUidType,
				Scale:       int32(v.Scale),
				RoleType:    payment.RoleType_Shop,
				Amount:      v.Amount,
				SplitMethod: splitMethod,
			}
		} else if v.SplitUIDType == fwe_trade_common.SplitUIDType_Platform {
			info = &payment.SplitInfo{
				UIDType:     consts.PlatformUidType,
				Scale:       int32(v.Scale),
				RoleType:    payment.RoleType_Platform,
				Amount:      v.Amount,
				SplitMethod: splitMethod,
			}
		}
		res = append(res, info)
	}

	return res, nil
}

// getSplitUIDs 获取分账方的真实uid
func getSplitUIDs(ctx context.Context, order *service_model.Order, splitUnits []*fwe_trade_common.TradeSplitUnit) (map[string]string, *errdef.BizErr) {
	var (
		fweAccountIDs  = make([]string, 0)
		bizScene       = order.FweOrder.BizScene
		posFinanceType consts.FinanceOrderType
		res            = make(map[string]string, 0) // key: 四轮商户id  val: 真实分账uid
	)

	// 过滤四轮商户id
	for _, v := range splitUnits {
		if v.SplitUIDType == fwe_trade_common.SplitUIDType_Shop {
			fweAccountIDs = append(fweAccountIDs, v.SplitUID)
		}
	}

	if len(fweAccountIDs) == 0 {
		logs.CtxInfo(ctx, "[getSplitUIDs] no shop in splitInfo")
		return nil, nil
	}

	// 查资金单
	if slices.Contains([]int32{int32(consts.BizSceneSHSellByFull), int32(consts.BizSceneSHConsignByFull), int32(consts.BizSceneSHSellByFullDeliveryCar), int32(consts.BizSceneSHConsignByFullDeliveryCar), int32(consts.BizSceneSHACNByFull)}, bizScene) {
		posFinanceType = consts.SHSellCarTotalPay
	} else {
		posFinanceType = consts.SHSellCarFinalPay
	}
	financeOrder := packer.FinanceGetByType(order.FinanceList, int32(posFinanceType))
	if financeOrder == nil {
		logs.CtxError(ctx, "[getSplitUIDs] finance_order not exist, orderID=%s, posFinanceType=%d",
			order.FweOrder.OrderID, posFinanceType)
		return nil, errdef.NewRawErr(errdef.DataErr, "资金单不存在")
	}

	// 查account服务，获取四轮id对应的uid
	financeAccountMap, missFweAccountIds, bizErr := service.NewAccountShop().MGetFinanceAccount(ctx, fweAccountIDs, financeOrder.MerchantID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[getSplitUIDs] MGetFinanceAccount failed, err=%+v", bizErr.Error())
		return nil, bizErr
	}

	if len(missFweAccountIds) > 0 {
		logs.CtxWarn(ctx, "[getSplitUIDs] some fweAccountIDs not exist, miss_ids=%+v", missFweAccountIds)
		return nil, errdef.NewRawErr(errdef.DataErr, "四轮商户id未注册")
	}

	for fweAccountID, account := range financeAccountMap {
		res[fweAccountID] = account.GetUID()
	}

	return res, nil
}
