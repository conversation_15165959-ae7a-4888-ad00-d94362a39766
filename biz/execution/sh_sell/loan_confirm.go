package sh_sell

import (
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metrics"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/base"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/motor/trade/audit"
	"context"
	"fmt"
)

type loanConfirmExecution struct {
	*executor.ActionBaseExecution
	param *sh_sell_model.LoanConfirmModel
	conf  *sh_sell_model.Conf
}

func NewLoanConfirmExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &loanConfirmExecution{
		param: new(sh_sell_model.LoanConfirmModel),
		conf:  new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.param)
	return e
}

func (e *loanConfirmExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	if e.conf.ApproveStarter == nil || e.conf.ApproveStarter.OpenID == "" {
		logs.CtxWarn(ctx, "[loanConfirmExecution.CheckParams] approve_starter not config")
		return errdef.NewParamsErr("approve_starter not config")
	}
	return nil
}

func (e *loanConfirmExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})

	return nil
}

// Process ...
func (e *loanConfirmExecution) Process(ctx context.Context) error {
	var (
		param           = e.param
		order           = e.GetOrder()
		tenantType      = e.GetActionOrderReq().Identity.TenantType
		bizScene        = e.GetActionOrderReq().Identity.BizScene
		posFinanceOrder *db_model.FFinanceOrder
	)

	// 0. 校验认款金额是否和创单时一致
	switch consts.BizScene(bizScene) {
	case consts.BizSceneSHSellByEarnestFinal, consts.BizSceneSHConsignByEF, consts.BizSceneSHACNByEF, consts.BizSceneSHSellByEarnestFinalDeliveryCar, consts.BizSceneSHConsignByEarnestFinalDeliveryCar:
		posFinanceOrder = packer.FinanceGetByType(order.FinanceList, consts.SHSellCarFinalPay.Int32())
	case consts.BizSceneSHSellByFull, consts.BizSceneSHConsignByFull, consts.BizSceneSHACNByFull, consts.BizSceneSHSellByFullDeliveryCar, consts.BizSceneSHConsignByFullDeliveryCar:
		posFinanceOrder = packer.FinanceGetByType(order.FinanceList, consts.SHSellCarTotalPay.Int32())
	}
	if posFinanceOrder.LoanAmount != param.Amount {
		logs.CtxWarn(ctx, "[loanConfirmExecution.Process] amount is not consist with loan_amount")
		return errdef.NewParamsErr("金额和创单时不一致")
	}

	// 1. 设置贷款明细
	loanDetail, err := utils.Marshal(param)
	if err != nil {
		logs.CtxWarn(ctx, "[loanConfirmExecution.Process] marshal failed, err=%+v", err)
		return err
	}

	order.TagMap[LoanDetail] = loanDetail

	bizErr := service.NewOrderService().UpdateOrderTag(ctx, order.FweOrder.OrderID, order.TagMap)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[loanConfirmExecution.Process] set tag failed, err=%s", bizErr.Error())
		return bizErr
	}

	// 2. 发送lark审批通知
	bizErr = e.SendAuditLark(ctx, loanDetail)
	if bizErr != nil {
		tags := []metrics.T{
			{Name: consts.TagTenantType, Value: fmt.Sprintf("%d", tenantType)},
			{Name: consts.TagBizScene, Value: fmt.Sprintf("%d", bizScene)},
		}
		utils.EmitCounter(consts.MetricsStartAuditFail, 1, tags...)
		logs.CtxWarn(ctx, "[loanConfirmExecution.Process] send audit lark failed, orderId=%s, err=%s",
			order.FweOrder.OrderID, bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *loanConfirmExecution) SendAuditLark(ctx context.Context, loanDetail string) *errdef.BizErr {
	req := e.GetActionOrderReq()
	param := e.param

	formMap := e.packAuditParamForLarkForm()
	formStr, _ := utils.Marshal(formMap)

	var (
		successAction, failAction string
	)
	switch req.GetIdentity().BizScene {
	case consts.BizSceneSHSellByEarnestFinal.Int32():
		successAction = consts.ShSellEFApproveLoanPassEvent
		failAction = consts.ShSellEFApproveLoanFailEvent
	case consts.BizSceneSHSellByFull.Int32():
		successAction = consts.ShSellFullApproveLoanPassEvent
		failAction = consts.ShSellFullApproveLoanFailEvent
	case consts.BizSceneSHConsignByEF.Int32():
		successAction = consts.ShConsignEFApproveLoanPassEvent
		failAction = consts.ShConsignEFApproveLoanFailEvent
	case consts.BizSceneSHConsignByFull.Int32():
		successAction = consts.ShConsignFullApproveLoanPassEvent
		failAction = consts.ShConsignFullApproveLoanFailEvent
	case consts.BizSceneSHACNByEF.Int32():
		successAction = consts.ShACNEFApproveLoanPassEvent
		failAction = consts.ShACNEFApproveLoanFailEvent
	case consts.BizSceneSHACNByFull.Int32():
		successAction = consts.ShACNFullApproveLoanPassEvent
		failAction = consts.ShACNFullApproveLoanFailEvent
	case consts.BizSceneSHSellByEarnestFinalDeliveryCar.Int32():
		successAction = sh_state.ShSellEFDCApproveLoanPassEvent.Value()
		failAction = sh_state.ShSellEFDCApproveLoanFailEvent.Value()
	case consts.BizSceneSHSellByFullDeliveryCar.Int32():
		successAction = sh_state.ShSellFullDCApproveLoanPassEvent.Value()
		failAction = sh_state.ShSellFullDCApproveLoanFailEvent.Value()
	case consts.BizSceneSHConsignByEarnestFinalDeliveryCar.Int32():
		successAction = sh_state.ShConsignEFDCApproveLoanPassEvent.Value()
		failAction = sh_state.ShConsignEFDCApproveLoanFailEvent.Value()
	case consts.BizSceneSHConsignByFullDeliveryCar.Int32():
		successAction = sh_state.ShConsignFullDCApproveLoanPassEvent.Value()
		failAction = sh_state.ShConsignFullDCApproveLoanFailEvent.Value()
	}

	auditReq := &audit.ApplyAuditReq{
		Tenant:    int64(audit.AuditServiceTenant_FWE_TRADE),
		CreatorId: 1,
		BizType:   req.Identity.BizScene,
		OuterId:   e.makeAuditOuterID(),
		AuditName: fmt.Sprintf("%s-%s", param.FinanceName, ApproveName),
		AuditParams: []*audit.AuditParams{
			{Name: LoanDetail, BeforeValue: loanDetail},
		},
		Steps: []*audit.ApplyAuditStepInfo{
			{
				StepType:     int32(audit.StepTypeEnum_Or),
				StepName:     ApproveName,
				TargetSystem: audit.StepTargetSystemTypeEnum_Lark,
				LarkOuterAuditInfo: &audit.LarkOuterAuditInfo{
					ApprovalCode:      e.conf.ApproveStarter.ApproveCode,
					Form:              formStr,
					OpenId:            e.conf.ApproveStarter.OpenID,
					Appid:             LarkAppID,
					AppSecret:         LarkAppSecret,
					VerificationToken: LarkAppVerificationToken,
					EncryptKey:        LarkEncryptKey,
				},
			},
		},
		BizIndex1: req.OrderID,
		BizIndex2: fmt.Sprintf("%d", req.Identity.TenantType),
		BizIndex3: utils.MakeCallbackEvent(successAction),
		BizIndex4: utils.MakeCallbackEvent(failAction),
		Base:      base.NewBase(),
	}
	bizErr := service.NewAuditService().ApplyAudit(ctx, auditReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[loanConfirmExecution.SendAuditLark] apply audit failed, err=%+v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *loanConfirmExecution) makeAuditOuterID() string {
	req := e.GetActionOrderReq()
	// 返回order_id$$event_name
	return fmt.Sprintf("%s$$%s", req.OrderID, consts.ShSellEFConfirmLoanEvent)
}

func (e *loanConfirmExecution) packAuditParamForLarkForm() []*sh_sell_model.AuditForm {
	var (
		order  = e.GetOrder()
		tagMap = order.TagMap
		param  = e.param
	)

	tradeStructure := LoanFirstName
	if loanType, exist := tagMap[LoanType]; exist && loanType == LoanAfterStr {
		tradeStructure = LoanAfterName
	}

	amount := param.Amount

	res := []*sh_sell_model.AuditForm{
		{ID: CarVin, Value: param.CarVin},
		{ID: BorrowerName, Value: param.BorrowerName},
		{ID: LoanAmount, Value: fmt.Sprintf("%.2f元", float64(amount)/1e2)},
		{ID: ShopName, Value: param.ShopName},
		//{ID: FinanceName, Value: param.FinanceName},
		{ID: BizOrderId, Value: param.BizOrderID},
	}

	deliveryCarBizScenes := []int32{
		consts.BizSceneSHSellByEarnestFinalDeliveryCar.Int32(),
		consts.BizSceneSHSellByFullDeliveryCar.Int32(),
		consts.BizSceneSHConsignByEarnestFinalDeliveryCar.Int32(),
		consts.BizSceneSHConsignByFullDeliveryCar.Int32(),
	}
	if !slices.Contains(deliveryCarBizScenes, order.FweOrder.BizScene) {
		res = append(res, &sh_sell_model.AuditForm{ID: TradeStructure, Value: tradeStructure})
	}

	//if param.OutBankInfo != nil {
	//	res = append(res, []*sh_sell_model.AuditForm{
	//		{ID: OutBankAccountName, Value: param.OutBankInfo.AccountName},
	//		{ID: OutBankBranch, Value: param.OutBankInfo.BranchName},
	//		{ID: OutBankAccountNo, Value: param.OutBankInfo.AccountNo},
	//	}...)
	//}

	for _, m := range res {
		m.Type = "input" // 固定值
	}

	return res
}
