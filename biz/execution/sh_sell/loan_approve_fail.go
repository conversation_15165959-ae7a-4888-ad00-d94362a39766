package sh_sell

import (
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type loanApproveFailExecution struct {
	*executor.ActionBaseExecution
	param *sh_sell_model.LoanApproveFailModel
}

func NewLoanApproveFailExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	e := &loanApproveFailExecution{
		param: new(sh_sell_model.LoanApproveFailModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq),
		nil, e.param, options...)
	return e
}

func (e *loanApproveFailExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	return nil
}

// Process ...
func (e *loanApproveFailExecution) Process(ctx context.Context) error {
	return nil
}
