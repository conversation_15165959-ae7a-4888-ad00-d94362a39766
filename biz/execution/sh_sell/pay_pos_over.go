package sh_sell

import (
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"context"
)

type payPOSOverExecution struct {
	*callback.PayCallbackBaseExecution
	confPtr *sh_sell_model.Conf
}

func NewPayPOSOverExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)

	e := &payPOSOverExecution{
		confPtr: new(sh_sell_model.Conf),
	}
	e.PayCallbackBaseExecution = callback.NewPayCallbackBaseExecution(ctx, req, e.confPtr)
	return e
}

func (e *payPOSOverExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})

	return nil
}

func (e *payPOSOverExecution) CheckParams(ctx context.Context) error {
	return nil
}

// Process 如果是内网&&寄售||ACN 有订金的交易类型，需退订金
func (e *payPOSOverExecution) Process(ctx context.Context) error {
	var (
		bizScene = e.GetActionOrderReq().Identity.BizScene
	)

	if !slices.ContainsInt32([]int32{int32(consts.BizSceneSHACNByEF), int32(consts.BizSceneSHConsignByEF), int32(consts.BizSceneSHConsignByEarnestFinalDeliveryCar)}, bizScene) {
		logs.CtxWarn(ctx, "[payPOSOverExecution.Process] cur bizScene no need refund earnest, bizScene=%d", bizScene)
		return nil
	}

	// 退款
	rpcReq, err := e.buildRefundReq(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[payPOSOverExecution.Process] build refund req failed, err=%+v", err)
		return err
	}
	refundNo, bizErr := service.NewTradePayment().CreateCashRefund(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[payPOSOverExecution.Process] payment refund rpc failed, err=%+v", bizErr.Error())
		return bizErr
	}
	logs.CtxInfo(ctx, "[payPOSOverExecution.Process] refund req success, refundNo=%s", refundNo)

	return nil
}

func (e *payPOSOverExecution) buildRefundReq(ctx context.Context) (*payment.CreateCashRefundReq, error) {
	req := e.GetActionOrderReq()

	// 查资金单，找到对应退款信息
	financeOrder, err := db_query.FFinanceOrder.WithContext(ctx).
		Where(db_query.FFinanceOrder.OrderID.Eq(req.OrderID)).
		Where(db_query.FFinanceOrder.FinanceOrderType.Eq(int32(consts.SHSellCarEarnestPay))).
		First()
	if err != nil {
		logs.CtxError(ctx, "[payPOSOverExecution.buildRefundReq] query finance_order failed, err=%+v", err)
		return nil, err
	}

	// 拼req
	currency := payment.CurrencyType_CNY
	rpcReq := &payment.CreateCashRefundReq{
		Identity:      req.Identity,
		OrderID:       req.OrderID,
		FinanceType:   financeOrder.FinanceOrderType,
		Currency:      &currency,
		Amount:        financeOrder.Amount,
		Reason:        "",
		IPAddress:     nil,
		Extra:         nil,
		CallbackEvent: utils.MakeCallbackEvent(consts.ShConsignEFRefundEarnestOverAfterPOSEvent),
		CallbackExtra: "",
		Base:          base.NewBase(),
	}
	return rpcReq, nil
}
