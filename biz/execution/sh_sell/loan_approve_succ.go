package sh_sell

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type loanApproveSuccessExecution struct {
	*executor.ActionBaseExecution
	param *sh_sell_model.LoanApproveSuccModel
	conf  *sh_sell_model.Conf
}

func NewLoanApproveSuccExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &loanApproveSuccessExecution{
		param: new(sh_sell_model.LoanApproveSuccModel),
		conf:  new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.param)
	return e
}

func (e *loanApproveSuccessExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	if e.conf == nil || e.conf.AgreementMerchant == nil {
		logs.CtxWarn(ctx, "[loanApproveSuccessExecution.CheckParams] conf is empty")
		return errdef.NewRawErr(errdef.LackConfigErr, "缺少协议扣款配置")
	}

	return nil
}

func (e *loanApproveSuccessExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}
	param := ShSellConditionParam{HasOfflinePay: e.hasOfflinePay(ctx)}
	conditions := GetShSellCondition(ctx, e.GetOrder(), &param)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})

	return nil
}

// Process ...
func (e *loanApproveSuccessExecution) Process(ctx context.Context) error {
	if e.GetBizIdentity().BizScene == consts.BizSceneSHSellByFullDeliveryCar.Int32() && e.hasOfflinePay(ctx) {
		var order = e.GetOrder()
		order.TagMap[LoanSubStatus] = LoanSubStatusOverStr
		if bizErr := service.NewOrderService().UpdateOrderTag(ctx, order.FweOrder.OrderID, order.TagMap); bizErr != nil {
			logs.CtxWarn(ctx, "[updateLoanSubStatusExecution.Process] set tag failed, err=%s", bizErr.Error())
			return bizErr
		}
		return nil
	}

	var (
		req            = e.GetActionOrderReq()
		order          = e.GetOrder()
		agreeMerchant  = e.conf.AgreementMerchant
		callbackAction string
	)

	switch consts.BizScene(req.GetIdentity().BizScene) {
	case consts.BizSceneSHSellByEarnestFinal:
		callbackAction = consts.ShSellEFLoanOverEvent
	case consts.BizSceneSHSellByFull:
		callbackAction = consts.ShSellFullLoanOverEvent
	case consts.BizSceneSHConsignByEF:
		callbackAction = consts.ShConsignEFLoanOverEvent
	case consts.BizSceneSHConsignByFull:
		callbackAction = consts.ShConsignFullLoanOverEvent
	case consts.BizSceneSHACNByEF:
		callbackAction = consts.ShACNEFLoanOverEvent
	case consts.BizSceneSHACNByFull:
		callbackAction = consts.ShACNFullLoanOverEvent
	case consts.BizSceneSHSellByEarnestFinalDeliveryCar:
		callbackAction = sh_state.ShSellEFDCLoanSubProcessOverEvent.Value()
	case consts.BizSceneSHSellByFullDeliveryCar:
		callbackAction = sh_state.ShSellFullDCLoanSubProcessOverEvent.Value()
	case consts.BizSceneSHConsignByEarnestFinalDeliveryCar:
		callbackAction = sh_state.ShConsignEFDCLoanSubProcessOverEvent.Value()
	case consts.BizSceneSHConsignByFullDeliveryCar:
		callbackAction = sh_state.ShConsignFullDCLoanSubProcessOverEvent.Value()
	}

	loanDetailStr, exist := order.TagMap[LoanDetail]
	if !exist {
		logs.CtxWarn(ctx, "[loanApproveSuccessExecution.Process] order_tag loan_detail is empty, orderID=%s", req.OrderID)
		return errdef.NewRawErr(errdef.DataErr, "贷款详情不存在")
	}

	var loanDetail sh_sell_model.LoanConfirmModel
	err := utils.Unmarshal(loanDetailStr, &loanDetail)
	if err != nil {
		logs.CtxWarn(ctx, "[loanApproveSuccessExecution.Process] unmarshal loan_detail failed, err=%+v", err)
		return errdef.NewBizErrWithCode(errdef.DataErr, err)
	}

	// 资金安全校验
	if e.conf.CheckContStructField {
		req := e.GetActionOrderReq()
		param := &service.CheckAmountByContParam{
			OrderID:                   req.OrderID,
			FinanceOrderType:          consts.SHSellCarLoan.Int32(),
			Amount:                    loanDetail.Amount,
			AllowIncomeAmountOverflow: e.conf.CheckContOptionAllowIncomeAmountOverflow,
		}
		pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
		if bizErr != nil {
			logs.CtxError(ctx, "[loanApproveSuccessExecution.Process] CheckAmountByCont failed, err=%s", bizErr.Error())
			return bizErr
		}
		if !pass {
			logs.CtxError(ctx, "[loanApproveSuccessExecution.Process] safe check not pass, blockMsg=%s", blockMsg)
			return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "合同资金安全校验未通过")
		}
	}

	// 发起协议代扣
	financeReq := &payment.AgreementPayReq{
		Identity: &fwe_trade_common.BizIdentity{
			TenantType: req.Identity.TenantType,
			BizScene:   req.Identity.BizScene,
		},
		OrderID:          req.OrderID,
		MerchantID:       agreeMerchant.MerchantID,
		MerchantName:     agreeMerchant.MerchantName,
		AppID:            agreeMerchant.AppID,
		AppName:          agreeMerchant.AppName,
		UID:              agreeMerchant.Uid,
		UIDType:          agreeMerchant.UidType,
		AgreeOrderNo:     req.OrderID,
		AgreeOrderName:   fmt.Sprintf("订单-%s-协议扣款", req.OrderID),
		AgreeOrderDesc:   fmt.Sprintf("订单-%s-协议扣款", req.OrderID),
		Amount:           loanDetail.Amount,
		CardNo:           agreeMerchant.CardNo,
		InnerAgreementNo: agreeMerchant.InnerAgreementNo,
		AccountProp:      agreeMerchant.AccountProp,
		IPAddress:        "*************",
		CallbackEvent:    utils.MakeCallbackEvent(callbackAction),
		Base:             base.NewBase(),
	}

	bizErr := service.NewTradePayment().AgreementPay(ctx, financeReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[loanApproveSuccessExecution.Process] agreement pay failed, err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *loanApproveSuccessExecution) hasOfflinePay(ctx context.Context) bool {
	for _, financeOrder := range e.GetOrder().FinanceList {
		if financeOrder.TradeType == CommonConsts.FinancePayOffline.Value() {
			return true
		}
	}
	return false
}
