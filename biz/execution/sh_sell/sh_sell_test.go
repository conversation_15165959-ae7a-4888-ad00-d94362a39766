package sh_sell

import (
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/base"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/motor/trade/audit"
	"context"
	"fmt"
	"testing"
)

// doas -p motor.fwe_trade.engine go test ./biz/execution/sh_sell -v -count=1 -run="TestSendAuditLark$"
func TestSendAuditLark(t *testing.T) {
	bizscene := int32(1101)
	orderID := "1234567"
	financeName := "贷款测试"

	formMap := []map[string]string{
		{
			"id":    "borrower_name",
			"type":  "input",
			"value": "赵腾测试",
		},
		{
			"id":    "loan_amount",
			"type":  "input",
			"value": "100",
		},
		{
			"id":    "shop_name",
			"type":  "input",
			"value": "测试门店",
		},
	}
	formStr, _ := utils.Marshal(formMap)

	auditReq := &audit.ApplyAuditReq{
		Tenant:      int64(audit.AuditServiceTenant_FWE_TRADE),
		SystemId:    0,
		CreatorId:   1,
		BizType:     bizscene,
		OuterId:     orderID,
		AuditName:   fmt.Sprintf("%s-审批", financeName),
		AuditParams: []*audit.AuditParams{},
		CallbackUrl: nil,
		Steps: []*audit.ApplyAuditStepInfo{
			{
				StepType:     int32(audit.StepTypeEnum_Or),
				StepName:     "审批",
				TargetSystem: audit.StepTargetSystemTypeEnum_Lark,
				LarkOuterAuditInfo: &audit.LarkOuterAuditInfo{
					ApprovalCode:      AuditApproveCode,
					Form:              formStr,
					UserId:            "",
					OpenId:            "ou_7c2a1af349e60996f8422d9b489114b3",
					Appid:             LarkAppID,
					AppSecret:         LarkAppSecret,
					VerificationToken: LarkAppVerificationToken,
					EncryptKey:        LarkEncryptKey,
				},
			},
		},
		BizIndex1: orderID,
		Base:      base.NewBase(),
	}
	bizErr := service.NewAuditService().ApplyAudit(context.Background(), auditReq)
	if bizErr != nil {
		panic(bizErr.Error())
	}

	fmt.Println("success")
}
