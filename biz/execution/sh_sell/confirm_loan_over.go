package sh_sell

import (
	"context"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type confirmLoanOverExecution struct {
	*executor.ActionBaseExecution
	conf *sh_sell_model.Conf
}

func NewConfirmLoanOverExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &confirmLoanOverExecution{
		conf: new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, nil)
	return e
}

func (e *confirmLoanOverExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

// Process 主流程确认贷款完成后进行分账
func (e *confirmLoanOverExecution) Process(ctx context.Context) error {
	var (
		order = e.GetOrder()
	)

	if HasLoan(order) && !IsLoanSubStatusOver(order) {
		logs.CtxInfo(ctx, "[confirmLoanOverExecution.Process] no need settle")
		return nil
	}
	_, financeIDList := SettleGroupFinanceOrderIDs(order.FinanceList)
	if len(financeIDList) == 0 {
		logs.CtxInfo(ctx, "[confirmLoanOverExecution.Process] no need settle")
		return nil
	}
	if e.conf.POSPayMerchant == nil || e.conf.POSPayMerchant.ReceiveUID == "" {
		logs.CtxWarn(ctx, "[confirmLoanOverExecution.Process] no receive uid")
		return errdef.NewRawErr(errdef.LackConfigErr, "缺少收款方uid")
	}
	bizErr := CreateSettle(ctx, &CreateSettleReq{
		order:        order,
		receiveUID:   e.conf.POSPayMerchant.ReceiveUID,
		autoWithdraw: slices.ContainsInt32([]int32{int32(consts.BizSceneSHConsignByEarnestFinalDeliveryCar), int32(consts.BizSceneSHConsignByFullDeliveryCar)}, order.FweOrder.BizScene),
		fcType:       e.conf.POSPayMerchant.FcType,
		fcSceneCode:  e.conf.POSPayMerchant.FcSceneCode,
	})
	if bizErr != nil {
		logs.CtxWarn(ctx, "[confirmLoanOverExecution.Process] rpc failed, err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
