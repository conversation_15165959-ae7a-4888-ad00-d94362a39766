package sh_sell

import (
	"context"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type transferOwnerExecution struct {
	*executor.ActionBaseExecution
	conf *sh_sell_model.Conf
}

func NewTransferOwnerExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &transferOwnerExecution{
		conf: new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, nil)
	return e
}

func (e *transferOwnerExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

// Process 过户时，（没有有金融节点）||（有金融节点&&先贷款后过户）做结算； （有金融节点 && 先过户后贷款）不做结算 ||1112，1113，1114，1115（有金融节点 && 贷款已完成）做结算；（有金融节点 && 贷款未完成）不做结算
func (e *transferOwnerExecution) Process(ctx context.Context) error {
	var (
		order = e.GetOrder()
	)

	if slices.Contains([]int32{int32(consts.BizSceneSHSellByEarnestFinalDeliveryCar), int32(consts.BizSceneSHSellByFullDeliveryCar), int32(consts.BizSceneSHConsignByEarnestFinalDeliveryCar), int32(consts.BizSceneSHConsignByFullDeliveryCar)}, order.FweOrder.BizScene) {
		if HasLoan(order) && !IsLoanSubStatusOver(order) {
			logs.CtxInfo(ctx, "[transferOwnerExecution.Process] no need settle")
			return nil
		}
		_, financeIDList := SettleGroupFinanceOrderIDs(order.FinanceList)
		if len(financeIDList) == 0 {
			logs.CtxInfo(ctx, "[transferOwnerExecution.Process] no need settle")
			return nil
		}
	} else {
		if HasLoan(order) && !IsLoanFirst(order) {
			logs.CtxInfo(ctx, "[transferOwnerExecution.Process] no need settle")
			return nil
		}
	}

	if e.conf.POSPayMerchant == nil || e.conf.POSPayMerchant.ReceiveUID == "" {
		logs.CtxWarn(ctx, "[transferOwnerExecution.Process] no receive uid")
		return errdef.NewRawErr(errdef.LackConfigErr, "缺少收款方uid")
	}
	bizErr := CreateSettle(ctx, &CreateSettleReq{
		order:        order,
		receiveUID:   e.conf.POSPayMerchant.ReceiveUID,
		autoWithdraw: slices.ContainsInt32([]int32{int32(consts.BizSceneSHConsignByEF), int32(consts.BizSceneSHConsignByFull), int32(consts.BizSceneSHConsignByEarnestFinalDeliveryCar), int32(consts.BizSceneSHConsignByFullDeliveryCar), int32(consts.BizSceneSHACNByEF), int32(consts.BizSceneSHACNByFull)}, order.FweOrder.BizScene),
		fcType:       e.conf.POSPayMerchant.FcType,
		fcSceneCode:  e.conf.POSPayMerchant.FcSceneCode,
	})
	if bizErr != nil {
		logs.CtxWarn(ctx, "[transferOwnerExecution.Process] rpc failed, err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
