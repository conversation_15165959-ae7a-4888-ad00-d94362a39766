package sh_sell

import (
	"context"
	"unicode/utf8"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/fwe_trade_common/scene/sh_sell_ef/common"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type createOrderExecution struct {
	*executor.CreateBaseExecution
	conf       sh_sell_model.Conf
	AccountMap map[string]*shop.FinanceAccount
}

func NewCreateOrderExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.CreateOrderReq)
	e := &createOrderExecution{}
	e.conf = sh_sell_model.Conf{}
	e.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, req, &e.conf)
	return e
}

func (e *createOrderExecution) CheckParams(ctx context.Context) error {
	req := e.GetCreateOrderReq()

	if req.ProductInfo == nil {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack product_info")
		return errdef.NewParamsErr("lack product_info")
	}

	if req.BuyerInfo == nil || (req.BuyerInfo.PersonInfo == nil && req.BuyerInfo.CompanyInfo == nil && req.BuyerInfo.FweMerchant == nil) {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack buyer_info")
		return errdef.NewParamsErr("lack buyer_info")
	}
	if req.SellerInfo == nil || req.SellerInfo.FweMerchant == nil || req.SellerInfo.FweMerchant.FweAccountID == "" {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack seller_info")
		return errdef.NewParamsErr("lack seller_info")
	}
	if req.ProductInfo.ProductDetail != nil {
		if utf8.RuneCountInString(req.ProductInfo.GetProductDetail().GetProductHeadImageURI()) > 128 {
			logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] product_head_image_url illegal")
			return errdef.NewParamsErr("product_head_image_url length over 128")
		}
		if utf8.RuneCountInString(req.ProductInfo.GetProductDetail().GetProductDesc()) > 128 {
			logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] product_desc illegal")
			return errdef.NewParamsErr("product_desc length over 128")
		}
	}

	// 校验配置
	bizScene := req.Identity.BizScene
	normalMerchant := e.conf.NormalPayMerchant
	posMerchant := e.conf.POSPayMerchant
	if (bizScene == int32(consts.BizSceneSHSellByEarnestFinal) && (normalMerchant == nil || posMerchant == nil)) ||
		(bizScene == int32(consts.BizSceneSHConsignByEF) && (normalMerchant == nil || posMerchant == nil)) ||
		(bizScene == int32(consts.BizSceneSHSellByFull) && posMerchant == nil) ||
		(bizScene == int32(consts.BizSceneSHConsignByFull) && posMerchant == nil) ||
		(bizScene == int32(consts.BizSceneSHSellInsurance) && normalMerchant == nil) ||
		(bizScene == int32(consts.BizSceneSHSellByEarnestFinalDeliveryCar) && (normalMerchant == nil || posMerchant == nil)) ||
		(bizScene == int32(consts.BizSceneSHConsignByEarnestFinalDeliveryCar) && (normalMerchant == nil || posMerchant == nil)) ||
		(bizScene == int32(consts.BizSceneSHSellByFullDeliveryCar) && posMerchant == nil) ||
		(bizScene == int32(consts.BizSceneSHConsignByFullDeliveryCar) && posMerchant == nil) ||
		(bizScene == int32(consts.BizSceneSHACNByEF) && (normalMerchant == nil || posMerchant == nil)) ||
		(bizScene == int32(consts.BizSceneSHACNByFull) && posMerchant == nil) ||
		(bizScene == int32(consts.BizSceneSHSellConsignRevoke) && normalMerchant == nil) {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack conf")
		return errdef.NewRawErr(errdef.LackConfigErr, "lack conf")
	}

	// 获取fweAccountId对应的mid
	fweAccountID := req.SellerInfo.FweMerchant.FweAccountID
	e.AccountMap = make(map[string]*shop.FinanceAccount)
	// if normalMerchant != nil {
	//	account, bizErr := service.NewAccountService().GetFinanceAccount(ctx, fweAccountID, normalMerchant.MerchantID)
	//	if bizErr != nil {
	//		logs.CtxWarn(ctx, "[CheckParams] get financeAccount failed, fweId=%s, err=%+v, accountResp=%s", fweAccountID, bizErr.Error())
	//		return errors.New("get account info failed")
	//	}
	//
	//	e.AccountMap[normalMerchant.MerchantID] = account
	// }
	if posMerchant != nil {
		// 查四轮商户的财经账户
		account, bizErr := service.NewAccountShop().GetFinanceAccountOne(ctx, fweAccountID, posMerchant.MerchantID)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[CheckParams] get financeAccount failed, fweId=%s, err=%+v, accountResp=%s", fweAccountID, bizErr.Error())
			return bizErr
		}

		e.AccountMap[posMerchant.MerchantID] = account

		// 校验分账方都开通了财经账户
		if req.IsSetSplitInfo() {
			splitAccountIDs := make([]string, 0)
			for _, splitUnit := range req.GetSplitInfo().Detail {
				if splitUnit.SplitUID != "" {
					splitAccountIDs = append(splitAccountIDs, splitUnit.SplitUID)
				}
			}
			_, missIDs, bizErr := service.NewAccountShop().MGetFinanceAccount(ctx, splitAccountIDs, posMerchant.MerchantID)
			if bizErr != nil {
				logs.CtxWarn(ctx, "[CheckParams] get financeAccount failed, fweIds=%s, err=%+v", splitAccountIDs, bizErr.Error())
				return bizErr
			}
			if len(missIDs) > 0 {
				logs.CtxWarn(ctx, "[CheckParams] get financeAccount, missIDs > 0, missIDs=%v", missIDs)
				return errdef.NewRawErr(errdef.FinanceAccountNotExist, "miss finance account")
			}
		}
		// 如果没有传分账信息， 需要100%分账给二级商户
		if req.SplitInfo == nil || len(req.SplitInfo.Detail) == 0 {
			req.SplitInfo = &fwe_trade_common.TradeSpiltInfo{
				SplitMethod: fwe_trade_common.SplitMethod_ByScale,
				Detail: []*fwe_trade_common.TradeSplitUnit{
					{
						SplitUID:     fweAccountID,
						SplitUIDType: fwe_trade_common.SplitUIDType_Shop,
						Scale:        OneHundredScale,
					},
				},
			}
		}
	}

	return nil
}

func (e *createOrderExecution) Process(ctx context.Context) error {
	req := e.GetCreateOrderReq()
	bizScene := req.Identity.BizScene

	// 取配置
	conf := e.conf
	normalMerchant := conf.NormalPayMerchant
	posMerchant := conf.POSPayMerchant

	// 驱动状态
	fsmConditionMap := make(map[string]interface{})
	if req.OrderTag[common.CondShSellNoIntentionContract.Val()] == "true" {
		fsmConditionMap[common.CondShSellNoIntentionContract.Val()] = true
	} else {
		fsmConditionMap[common.CondShSellNoIntentionContract.Val()] = false
	}

	// 驱动状态
	err := e.GetStateMachine().Fire(ctx, consts.CreateAction, fsmConditionMap)
	if err != nil {
		logs.CtxError(ctx, "[sh_sell.createOrderExecution] fire create event failed, err=%+v", err)
		return err
	}

	// 拼数据
	var (
		fweOrder    *db_model.FweOrder
		financeList []*db_model.FFinanceOrder
	)

	// common val
	totalAmount := req.TotalAmount
	buyerID := packer.CommonTradeSubjectIDGet(req.BuyerInfo)
	buyerExtra := packer.CommonTradeSubjectSerialize(req.BuyerInfo)
	sellerID := packer.CommonTradeSubjectIDGet(req.SellerInfo)
	sellerExtra := packer.CommonTradeSubjectSerialize(req.SellerInfo)

	var isTest int32
	if req.IsTest {
		isTest = 1
	}
	fweOrder = &db_model.FweOrder{
		TenantType:         int32(req.GetIdentity().GetTenantType()),
		BizScene:           req.GetIdentity().BizScene,
		SmVersion:          req.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(e.GetStateMachine().CurState()), // 初始态
		OrderName:          req.GetOrderName(),
		OrderDesc:          req.GetOrderDesc(),
		ProductID:          req.ProductInfo.ProductID,
		ProductType:        int32(req.ProductInfo.ProductType),
		ProductName:        req.ProductInfo.ProductName,
		ProductExtra:       req.ProductInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(req.ProductInfo.ProductDetail)),
		SkuID:              req.ProductInfo.SkuID,
		ProductQuantity:    int32(req.ProductInfo.ProductQuantity),
		ProductUnitPrice:   req.ProductInfo.ProductUnitPrice,
		TotalAmount:        totalAmount,
		TotalPayAmount:     totalAmount,
		TotalSubsidyAmount: 0,
		TradeType:          int32(req.TradeType),
		BuyerID:            buyerID,
		BuyerExtra:         &buyerExtra,
		SellerID:           sellerID,
		SellerExtra:        &sellerExtra,
		IsTest:             isTest,
		IdempotentID:       req.GetIdemID(),
	}

	loanAmount := int64(0)
	for _, financeInfo := range req.FinanceList {
		merchant := normalMerchant
		tradeType := consts.TradeTypePayCashier
		switch consts.FinanceOrderType(financeInfo.FinanceOrderType) {
		case consts.SHSellCarTotalPay:
			if slices.ContainsInt32([]int32{int32(consts.BizSceneSHSellByFull), int32(consts.BizSceneSHConsignByFull), int32(consts.BizSceneSHSellByFullDeliveryCar), int32(consts.BizSceneSHConsignByFullDeliveryCar), int32(consts.BizSceneSHACNByFull)}, bizScene) {
				tradeType = consts.TradeTypePayPos
			} else if bizScene == int32(consts.BizSceneSHSellInsurance) || bizScene == int32(consts.BizSceneSHSellConsignRevoke) {
				tradeType = consts.TradeTypePayCashier
			}
		case consts.SHSellCarEarnestPay:
			tradeType = consts.TradeTypePayCashier
		case consts.SHSellCarFinalPay:
			tradeType = consts.TradeTypePayPos
		case consts.SHSellCarLoan:
			loanAmount = financeInfo.Amount
			continue
		}

		if tradeType == consts.TradeTypePayPos {
			merchant = posMerchant
		}

		financeOrderID, err := utils.TryGenId(3)
		if err != nil {
			logs.CtxError(ctx, "[createOrderExecution] gen id error, err=%+v", err)
			return err
		}

		var mid string
		if account, exist := e.AccountMap[merchant.MerchantID]; !exist {
			logs.CtxInfo(ctx, "[createOrderExecution] account is not found")
		} else {
			mid = account.Mid
		}

		financeList = append(financeList, &db_model.FFinanceOrder{
			TenantType:       int32(req.GetIdentity().GetTenantType()),
			BizScene:         req.GetIdentity().BizScene,
			AppID:            merchant.AppID,
			MerchantID:       merchant.MerchantID,
			Mid:              mid,
			OrderID:          e.GetOrderID(),
			OrderName:        req.OrderName,
			TradeType:        tradeType.String(),
			FinanceOrderID:   utils.MakeFinanceOrderID(financeOrderID, financeInfo.FinanceOrderType),
			FinanceOrderType: financeInfo.FinanceOrderType,
			Amount:           financeInfo.Amount,
			ProcessAmount:    0,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
		})
	}

	for _, financeInfo := range financeList {
		// 只有一个POS资金单放上贷款金额
		if financeInfo.TradeType == consts.TradeTypePayPos.String() {
			financeInfo.LoanAmount = loanAmount
			break
		}
	}

	order := &service_model.Order{
		FweOrder:       fweOrder,
		FinanceList:    financeList,
		TagMap:         req.OrderTag,
		BizExtra:       req.Extra,
		TradeSplitInfo: req.SplitInfo,
	}
	bizErr := service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[createOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *createOrderExecution) Result() interface{} {
	return e.GetOrderID()
}
