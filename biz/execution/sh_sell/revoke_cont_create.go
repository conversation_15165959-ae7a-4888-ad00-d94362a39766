package sh_sell

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_sell"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
	"github.com/bytedance/sonic"
	"strconv"
)

type revokeContCreateExecution struct {
	*executor.ActionBaseExecution
	contractReq *sh_sell_model.RevokeContSignParam
	conf        *sh_sell_model.Conf
	contSerial  string
}

func NewRevokeContCreateExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	e := &revokeContCreateExecution{
		contractReq: new(sh_sell_model.RevokeContSignParam),
		conf:        new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, e.conf, e.contractReq, options...)
	return e
}

func (e *revokeContCreateExecution) CheckParams(ctx context.Context) error {

	var (
		config = e.conf
	)

	contTypeName := "revoke"
	contInfo := config.ContInfoMap[contTypeName]
	if contInfo == nil || contInfo.TmplID == 0 {
		logs.CtxWarn(ctx, "[revokeContCreateExecution.CheckParams] contInfo not config, contTypeName=%s", contTypeName)
		return errdef.NewRawErr(errdef.LackConfigErr, "合同模板未配置")
	}

	if config.CreditInfo == nil || config.CreditInfo[PlatformCreditKey] == nil {
		logs.CtxWarn(ctx, "[revokeContCreateExecution.CheckParams] contInfo not config, contTypeName=%s", contTypeName)
		return errdef.NewRawErr(errdef.LackConfigErr, "我司企业信息未配置")
	}

	return nil
}

func (e *revokeContCreateExecution) Process(ctx context.Context) error {

	// 构建参数
	createParam, bizErr := e.buildCreateContParam(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[revokeContCreateExecution] buildCreateContParam failed, err=%s", bizErr.Error())
		return bizErr
	}

	// service
	contSerial, bizErr := service.NewContractService().CreateContract(ctx, createParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[BaseContStartExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.contSerial = contSerial
	return nil
}

func (e *revokeContCreateExecution) Result() interface{} {
	res := &sh_sell_model.SignContractResult{
		ContSerial: e.contSerial,
	}

	resJson, _ := sonic.MarshalString(res)
	return resJson
}

func (e *revokeContCreateExecution) buildCreateContParam(ctx context.Context) (*service_model.ContractCreateParam, *errdef.BizErr) {
	var (
		fweOrder      = e.GetOrder().FweOrder
		req           = e.contractReq
		contConfig    = e.conf.ContInfoMap["revoke"]
		companyConfig = e.conf.CreditInfo[PlatformCreditKey]
		operator      = e.GetActionOrderReq().Operator
	)

	id, err := strconv.ParseInt(operator.OperatorID, 10, 64)
	if err != nil {
		return nil, errdef.NewBizErr(errdef.ParamErr, err, "operator error")
	}

	param := &service_model.ContractCreateParam{
		OrderID:        fweOrder.OrderID,
		TenantType:     fweOrder.TenantType,
		BizScene:       fweOrder.BizScene,
		ContType:       req.ContractType,
		OperatorID:     id,
		OperatorName:   operator.OperatorName,
		TmplID:         contConfig.TmplID,
		NeedSignNoCert: false,
		SmsTmplID:      contConfig.SmsTmplID,
		SmsChannelID:   contConfig.SmsChannel,
		TmplParams:     req.ContParams,
		SignPartList: []*service_model.SignPart{
			{
				SignPosition: int32(core.SignPosition_PA),
				IsInner:      true,
				CardType:     int32(core.CardType_CreditCode),
				IdentName:    companyConfig.Name,
				IdentID:      companyConfig.CreditCode,
			},
			{
				SignPosition: int32(core.SignPosition_PB),
				IsInner:      false,
				CardType:     int32(core.CardType_CreditCode),
				IdentName:    req.CompanyName,
				IdentID:      req.CreditCode,
				SignerName:   req.SignerName,
				SignerPhone:  req.SignerPhone,
			},
		},
		InOutData: &service_model.InOutData{
			Currency: int32(core.Currency_CNY),
			TotalOut: 0,
			TotalIn:  fweOrder.TotalAmount,
		},
		CallbackEvent: utils.MakeCallbackEvent(sh_sell.SHSellConsignRevokeContFinishEt.Value()),
		CallbackExtra: utils.MakeContractCallbackExtra(fweOrder.OrderID),
		ReturnUrl:     req.RedirectUrl,
	}
	return param, nil
}
