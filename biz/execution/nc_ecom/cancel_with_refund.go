package nc_ecom

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
)

type CancelWithRefundExecution struct {
	*common.UnionRefundExecution
}

// NewCancelWithRefundExecution 标准退款
func NewCancelWithRefundExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &CancelWithRefundExecution{}
	e.UnionRefundExecution = common.NewUnionRefundBaseExecution(ctx, actionReq)
	return e
}

func (e *CancelWithRefundExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.UnionRefundExecution.BizReq
	if bizReq.RefundList == nil || len(bizReq.RefundList) == 0 {
		bizErr := errdef.NewParamsErr("RefundList 参数错误")
		return bizErr
	}
	for _, refund := range bizReq.RefundList {
		if refund.Amount <= 0 {
			bizErr := errdef.NewParamsErr("RefundList.amount 参数错误")
			return bizErr
		}
	}
	return nil
}

func (e *CancelWithRefundExecution) PostProcess(ctx context.Context) error {
	// 归还库存
	if bizErr := restoreStock(ctx, e.GetOrder().FweOrder, e.GetOrder().TagMap); bizErr != nil {
		return bizErr
	}

	return e.UnionRefundExecution.PostProcess(ctx)
}
