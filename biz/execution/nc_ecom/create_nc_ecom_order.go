package nc_ecom

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/statemachine"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type CreateOrderExecution struct {
	*executor.CreateBaseExecution
	result interface{}
}

func NewCreateOrderExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &CreateOrderExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), nil)
	return t
}

func (e *CreateOrderExecution) CheckParams(ctx context.Context) error {
	req := e.GetCreateOrderReq()
	// 新车电商只需校验意向金、服务费和订金
	for _, financeInfo := range req.FinanceList {
		if slices.Contains([]int32{CommonConsts.FinanceIntention.Value(), CommonConsts.FinanceServiceFee.Value(), CommonConsts.FinanceEarnest.Value()}, financeInfo.FinanceOrderType) {
			// 资金风控检查
			if bizErr := utils.CheckFundRiskOfAmount(ctx, req.IsTest, financeInfo.Amount, conv.Int64Ptr(consts.TestAmount)); bizErr != nil {
				logs.CtxWarn(ctx, "[createOrderExecution-CheckParams] CheckFundRiskOfAmount error, err = %v. financeInfo = %v ", bizErr.Error(), tools.GetLogStr(financeInfo))
				return bizErr
			}
		}
	}
	return nil
}

func (e *CreateOrderExecution) Process(ctx context.Context) error {
	var (
		err            error
		intentionMoney int64
		conditionMap   map[string]interface{}
		bizErr         *errdef.BizErr
		stateMachine   = e.GetStateMachine()
		createReq      = e.GetCreateOrderReq()
		bizScene       = createReq.GetIdentity().GetBizScene()
		productInfo    = createReq.GetProductInfo()
	)

	for _, financeInfo := range createReq.FinanceList {
		if financeInfo.FinanceOrderType == CommonConsts.FinanceIntention.Value() {
			intentionMoney = financeInfo.Amount
			break
		}
	}
	// 驱动状态
	conditionMap = map[string]interface{}{
		"intention_money": intentionMoney,
	}
	if err = stateMachine.Fire(ctx, statemachine.OrderCreateEt.Value(), conditionMap); err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "CreateOrderExecution")
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 兼容逻辑，指定是新订单
	if createReq.OrderTag == nil {
		createReq.OrderTag = make(map[string]string)
	}
	createReq.OrderTag[InfraUseUnionPay] = "1"

	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene,
		SmVersion:          createReq.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createReq.GetOrderName(),
		OrderDesc:          createReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductExtra:       productInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		ProductVersion:     productInfo.ProductVersion,
		TotalAmount:        createReq.TotalAmount,
		TotalPayAmount:     packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList()),
		TotalSubsidyAmount: 0,
		TradeType:          int32(createReq.TradeType),
		BuyerID:            packer.CommonTradeSubjectIDGet(createReq.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(createReq.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createReq.IsTest),
		Creator:            createReq.GetOperator().GetOperatorID(),
		CreatorName:        createReq.GetOperator().GetOperatorName(),
		Operator:           createReq.GetOperator().GetOperatorID(),
		OperatorName:       createReq.GetOperator().GetOperatorName(),
		IdempotentID:       createReq.GetIdemID(),
	}

	order := &service_model.Order{
		FweOrder:    fweOrder,
		FinanceList: packer.FinanceCommonList2DBList(ctx, createReq.FinanceList, fweOrder.OrderID, fweOrder.OrderName, fweOrder.TenantType, fweOrder.BizScene),
		TagMap:      createReq.OrderTag,
		BizExtra:    createReq.Extra,
	}

	// 扣库存
	if bizErr = decrStock(ctx, fweOrder, false, conv.BoolDefault(order.TagMap[InfraDecrStock], true)); bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] decrStock err=%s", bizErr.Error())
		return bizErr
	}

	bizErr = service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] CreateOrder err=%s", bizErr.Error())
		return bizErr
	}

	e.result = e.GetOrderID()
	return nil
}

func (e *CreateOrderExecution) Result() interface{} {
	return e.result
}
