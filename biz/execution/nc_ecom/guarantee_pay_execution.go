package nc_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

type GuaranteePayExecution struct {
	*common.GuaranteePayExecution
}

func NewGuaranteePayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	return &GuaranteePayExecution{
		GuaranteePayExecution: common.NewGuaranteePayExecution(ctx, actionReq).(*common.GuaranteePayExecution),
	}
}

func (e *GuaranteePayExecution) Process(ctx context.Context) error {
	// 没有扣过库存，需要扣库存
	var (
		alreadyDecrStock = e.GetOrder().TagMap[InfraDecrStock] == "true"
		needDecrStock    = conv.BoolDefault(e.GuaranteePayExecution.GetBizReq().Tag[InfraDecrStock], false)
	)
	if bizErr := decrStock(ctx, e.GetOrder().FweOrder, alreadyDecrStock, needDecrStock); bizErr != nil {
		logs.CtxError(ctx, "[DecrStockOne] err=%s", bizErr.Error())
		return bizErr
	}
	if err := e.GuaranteePayExecution.Process(ctx); err != nil {
		return err
	}
	return nil
}
