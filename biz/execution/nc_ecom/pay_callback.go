package nc_ecom

import (
	"context"

	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
)

type PayCallbackBaseExecution struct {
	*callback.UnionPayCallbackBaseExecution
}

func NewPayCallbackCommonExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &PayCallbackBaseExecution{}
	exe.UnionPayCallbackBaseExecution = callback.NewUnionPayCallbackCommonBaseExecution(ctx, sourceReq)
	return exe
}

func (e *PayCallbackBaseExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[PayAdvanceMoneyCallbackExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}

	bizErr := e.FireWithCondition(ctx, buildCondition(e.GetOrder().TagMap, nil))
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
