package nc_ecom

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
)

type UnionPayExecution struct {
	*common.UnionPayExecution
}

func NewUnionPayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	return &UnionPayExecution{
		UnionPayExecution: common.NewUnionPayExecution(ctx, actionReq).(*common.UnionPayExecution),
	}
}

func (e *UnionPayExecution) Process(ctx context.Context) error {
	// 没有扣过库存，需要扣库存
	var (
		alreadyDecrStock = e.GetOrder().TagMap[InfraDecrStock] == "true"
		needDecrStock    = conv.BoolDefault(e.UnionPayExecution.GetBizReq().Tag[InfraDecrStock], false)
	)
	if bizErr := decrStock(ctx, e.GetOrder().FweOrder, alreadyDecrStock, needDecrStock); bizErr != nil {
		logs.CtxError(ctx, "[DecrStockOne] err=%s", bizErr.Error())
		return bizErr
	}
	if err := e.UnionPayExecution.Process(ctx); err != nil {
		return err
	}
	return nil
}
