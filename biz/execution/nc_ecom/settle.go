package nc_ecom

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

const (
	accountId                 = "0044_0001"
	normalGuaranteeSplitUid   = "0018_0"
	normalGuaranteeMerchantId = "**********"
)

type SettleExecution struct {
	*common.UnionSettleExecution
}

func NewSettleExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &SettleExecution{}
	exe.UnionSettleExecution = common.NewUnionSettleBaseExecution(ctx, sourceReq)
	return exe
}

func (e *SettleExecution) PreProcess(ctx context.Context) error {
	if err := e.UnionSettleExecution.PreProcess(ctx); err != nil {
		return err
	}

	// 临时解决: 一个account_id对应多个uid，分账时获取的uid不正确
	var (
		useGuaranteeUid bool
	)
	for _, v := range e.GetOrder().FinanceList {
		if (v.TradeCategory == int32(fwe_trade_common.TradeCategory_Pay) || v.TradeCategory == 0) &&
			v.MerchantID == normalGuaranteeMerchantId {
			useGuaranteeUid = true
			break
		}
	}
	for _, split := range e.CommonSpiltInfo.Detail {
		if useGuaranteeUid && split.SplitUID == accountId {
			split.SplitUID = normalGuaranteeSplitUid
		}
	}
	return nil
}

func (e *SettleExecution) Process(ctx context.Context) error {
	var (
		bizErr *errdef.BizErr
	)

	// 驱动
	bizErr = e.FireWithCondition(ctx, e.GetFSMCondition())
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	// 分账,默认分账需要消费贷金额
	settleParam, bizErr := e.BuildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	mergeSettleNo, bizErr := service.NewUnionSettleService().UnionSettle(ctx, settleParam)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.BizRsp = execution_common.SettleRsp{
		MergeSettleNo: mergeSettleNo,
	}
	return nil
}

func (e *SettleExecution) BuildReq(ctx context.Context) (*service_model.UnionSettleParam, *errdef.BizErr) {
	var (
		bizReq  = e.BizReq
		order   = e.GetOrder()
		orderID = order.FweOrder.OrderID
	)

	if len(bizReq.FinanceTypeList) > 0 {
		return e.UnionSettleExecution.BuildReq(ctx)
	}

	if len(bizReq.FinanceIDList) == 0 {
		logs.CtxError(ctx, "[SettleExecution] no FinanceTypeList nor FinanceIDList")
		return nil, errdef.NewParamsErr("no FinanceTypeList nor FinanceIDList")
	}

	splits, bizErr := service.NewFinanceAccountService().ConvertCommonSplitInfo(ctx, e.GetBizIdentity().TenantType, e.CommonSpiltInfo)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionSettleExecution-BuildReq] ConvertCommonSplitInfo error, err = %v", bizErr.Error())
		return nil, bizErr
	}

	serviceReq := &payment.MergeSettleV2Req{
		Identity:          e.GetBizIdentity(),
		OrderID:           orderID,
		OrderType:         fwe_trade_common.OrderType_Trade,
		OutID:             utils.MakeSettleFinanceOutID(orderID, bizReq.SettleType),
		SettleFinanceType: bizReq.SettleType,
		FinanceList:       bizReq.FinanceIDList,
		SettleDesc:        bizReq.Reason,
		SplitList:         splits,
		SubsidyList:       nil,
		IPAddress:         conv.StringPtr(bizReq.IPAddress),
		BizExtra:          nil,
		IsOmitPosFee:      conv.BoolPtr(false),
		IsAutoWithdraw:    conv.BoolPtr(false),
		CallbackEvent:     utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:     "",
	}

	settleParam := &service_model.UnionSettleParam{
		OrderID:        orderID,
		OrderName:      order.FweOrder.OrderName,
		SettleType:     bizReq.SettleType,
		SettleAmount:   e.SettleAmount,
		MergeSettleReq: serviceReq,
	}
	return settleParam, nil
}
