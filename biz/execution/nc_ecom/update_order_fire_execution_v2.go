package nc_ecom

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model/new_car_ecom_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type UpdateOrderFireExecutionV2 struct {
	*executor.ActionBaseExecution
	updateOrder new_car_ecom_model.UpdateOrderParam
}

func NewUpdateOrderFireExecutionV2(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &UpdateOrderFireExecutionV2{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.updateOrder)
	return t
}

func (e *UpdateOrderFireExecutionV2) Process(ctx context.Context) error {
	var (
		bizErr              *errdef.BizErr
		updateOrder         = e.updateOrder
		order               = e.GetOrder().FweOrder
		actionReq           = e.GetActionOrderReq()
		orderID             = actionReq.GetOrderID()
		orderService        = service.NewOrderService()
		financeOrderService = service.NewFinanceOrderService()
		stateMachine        = e.GetStateMachine()
		updateParams        = &service_model.UpdateOrderParams{
			UpdateOrderName:   updateOrder.OrderName,
			UpdateOrderDesc:   updateOrder.OrderDesc,
			UpdateTotalAmount: updateOrder.TotalAmount,
			Operator:          actionReq.GetOperator(),
		}
	)

	bizErr = e.FireWithCondition(ctx, buildCondition(e.GetOrder().TagMap, updateOrder.Tag))
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}

	if len(updateOrder.Extra) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.Extra)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(updateOrder.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, updateOrder.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if updateOrder.BuyerInfo != nil {
		buyerID := packer.CommonTradeSubjectIDGet(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerID = &buyerID
		buyerInfo := packer.CommonTradeSubjectSerialize(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerExtra = &buyerInfo
	}

	if updateOrder.SellerInfo != nil {
		sellerID := packer.CommonTradeSubjectIDGet(updateOrder.SellerInfo)
		updateParams.UpdateSellerID = &sellerID
		sellerInfo := packer.CommonTradeSubjectSerialize(updateOrder.SellerInfo)
		updateParams.UpdateSellerExtra = &sellerInfo
	}

	if updateOrder.ProductInfo != nil {
		p := updateOrder.ProductInfo
		if p.ProductID != "" {
			updateParams.UpdateProductID = &p.ProductID
		}
		if p.SkuID != "" {
			updateParams.UpdateSkuID = &p.SkuID
		}
		if p.ProductName != "" {
			updateParams.UpdateProductName = &p.ProductName
		}
		if p.ProductType != 0 {
			updateParams.UpdateProductType = conv.Int32Ptr(int32(p.ProductType))
		}
		if p.ProductUnitPrice != 0 {
			updateParams.UpdateProductUnitPrice = &p.ProductUnitPrice
		}
		if p.ProductQuantity != 0 {
			updateParams.UpdateProductQuantity = &p.ProductQuantity
		}
		if p.ProductExtra != nil {
			updateParams.UpdateProductExtra = p.ProductExtra
		}
	}

	if updateOrder.Operator != nil {
		updateParams.Operator = updateOrder.Operator
	}

	// 更新订单
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 更新资金单
	if len(updateOrder.FinanceList) > 0 {
		createFinanceList := make([]*fwe_trade_common.FinanceInfo, 0, 0)
		for _, financeOrder := range updateOrder.FinanceList {
			finance := packer.FinanceGetByType(e.GetOrder().FinanceList, financeOrder.FinanceOrderType)
			if finance != nil {
				// updateFinance
				bizErr = financeOrderService.UpdateOrderFinance(ctx, finance.FinanceOrderID, packUpdateFinanceParam(ctx, financeOrder))
				if bizErr != nil {
					logs.CtxError(ctx, "[UpdateOrderFireExecution] UpdateOrderFinance failed, err is %+v", bizErr.Error())
					return bizErr
				}
			} else {
				// createFinance
				createFinanceList = append(createFinanceList, financeOrder)
			}
		}

		// batch create finance
		bizErr = financeOrderService.CreateFinanceOrderList(ctx, packer.FinanceCommonList2DBList(ctx, createFinanceList, order.OrderID, order.OrderName, order.TenantType, order.BizScene))
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] CreateFinanceOrderList failed, err is %+v", bizErr.Error())
			return bizErr
		}
	}

	return nil
}

func packUpdateFinanceParam(ctx context.Context, financeOrder *fwe_trade_common.FinanceInfo) *service_model.UpdateFinanceParams {
	if financeOrder.PayStatus == fwe_trade_common.FinanceStatus_Handling || financeOrder.PayStatus == fwe_trade_common.FinanceStatus_Complete {
		logs.CtxWarn(ctx, "[UpdateOrderFireExecutionV2.packUpdateFinanceParam] financeOrder %d, 状态正在进行中或已完成 不允许变更", financeOrder.FinanceOrderID)
		return nil
	}

	updateParams := &service_model.UpdateFinanceParams{
		UpdateAmount:        conv.Int64Ptr(financeOrder.Amount),
		UpdateTradeType:     conv.StringPtr(financeOrder.TradeType),
		UpdateMerchantID:    conv.StringPtr(financeOrder.MerchantID),
		UpdateFeeItemDetail: conv.StringPtr(tools.GetLogStr(financeOrder.FeeItemList)),
	}

	return updateParams
}
