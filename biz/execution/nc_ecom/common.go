package nc_ecom

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

const (
	InfraOrderCondition = "infra_order_condition"
	InfraDecrStock      = "infra_decr_stock"
	InfraUseUnionPay    = "infra_use_union_pay"
)

const (
	RefundTotalType     = 1
	RefundIntentionType = 2
)

func restoreStock(ctx context.Context, order *db_model.FweOrder, tagMap map[string]string) *errdef.BizErr {
	if tagMap[InfraDecrStock] == "false" {
		return nil
	}
	if bizErr := service.NewProductStockService().IncrStockOne(ctx, buildIncrStockReq(ctx, order)); bizErr != nil {
		logs.CtxError(ctx, "[restoreStock] IncrStock err: %+v", bizErr)
		return bizErr
	}
	return nil
}

func decrStock(ctx context.Context, order *db_model.FweOrder, alreadyDecrStock, needDecrStock bool) *errdef.BizErr {
	if alreadyDecrStock || !needDecrStock {
		logs.CtxInfo(ctx, "[decrStock]: order=%v, alreadyDecrStock=%v, needDecrStock=%v", tools.GetLogStr(order), alreadyDecrStock, needDecrStock)
		return nil
	}
	if bizErr := service.NewProductStockService().DecrStockOne(ctx, buildDecrStockReq(ctx, order)); bizErr != nil {
		logs.CtxError(ctx, "[decrStock] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func buildDecrStockReq(_ context.Context, order *db_model.FweOrder) *product_stock.DecrStockForOneReq {
	return &product_stock.DecrStockForOneReq{
		BizOrder: &product_stock.BizOrder{
			BizType: 44,
			OrderId: order.OrderID,
		},
		Item: &product_stock.ChangeStockItem{
			StockUnit: &product_stock.StockUnit{
				SkuId:          conv.StrToInt64(order.SkuID, 0),
				StockType:      product_stock.StockType_NORMAL,
				ProductId:      conv.Int64Ptr(conv.StrToInt64(order.ProductID, 0)),
				ProductVersion: conv.Int64Ptr(order.ProductVersion),
			},
			StockNum: conv.Int64Ptr(int64(order.ProductQuantity)),
		},
	}
}

func buildIncrStockReq(_ context.Context, order *db_model.FweOrder) *product_stock.IncrStockForOneReq {
	return &product_stock.IncrStockForOneReq{
		BizOrder: &product_stock.BizOrder{
			BizType: 44,
			OrderId: order.OrderID,
		},
		Item: &product_stock.ChangeStockItem{
			StockUnit: &product_stock.StockUnit{
				SkuId:          conv.StrToInt64(order.SkuID, 0),
				StockType:      product_stock.StockType_NORMAL,
				ProductId:      conv.Int64Ptr(conv.StrToInt64(order.ProductID, 0)),
				ProductVersion: conv.Int64Ptr(order.ProductVersion),
			},
			StockNum: conv.Int64Ptr(int64(order.ProductQuantity)),
		},
	}
}

func buildCondition(orderTag map[string]string, updateTag map[string]string) map[string]interface{} {
	var conditionStr string
	if len(updateTag) > 0 && updateTag[InfraOrderCondition] != "" {
		conditionStr = updateTag[InfraOrderCondition]
	} else if len(orderTag) > 0 {
		conditionStr = orderTag[InfraOrderCondition]
	}

	condition := make(map[string]interface{}, 0)
	_ = utils.SonicUnmarshal(conditionStr, &condition)

	return condition
}

func buildRefundReq(ctx context.Context, identity *fwe_trade_common.BizIdentity, order *service_model.Order, bizReq execution_common.RefundReq) (*payment.MergeRefundReq, *errdef.BizErr) {
	var (
		refundList             = make([]*payment.SingleRefund, 0)
		refundListV2           = make([]*payment.SingleRefundV2, 0)
		intentionRefundFinance *db_model.FFinanceOrder
		//useUnionPay            string
	)
	for _, financeOrder := range order.RefundFinanceList {
		if financeOrder.FinanceOrderType == RefundIntentionType {
			intentionRefundFinance = financeOrder
			break
		}
	}

	//if order.TagMap != nil {
	//	useUnionPay = order.TagMap[InfraUseUnionPay]
	//}

	for _, refund := range bizReq.RefundList {
		financeOrder := packer.FinanceGetByType(order.FinanceList, refund.FinanceOrderType)
		if financeOrder == nil {
			return nil, errdef.NewRawErr(errdef.DataErr, "找不到对应的资金单")
		}
		if financeOrder.FinanceOrderType == CommonConsts.FinanceIntention.Value() && intentionRefundFinance != nil && financeOrder.Amount == intentionRefundFinance.Amount {
			// 检查退款支付单是否已经退款成功
			logs.CtxInfo(ctx, "[buildRefundReq] financeOrder=%v already refunded.", tools.GetLogStr(financeOrder))
			continue
		}
		//if useUnionPay == "1" {
		// 使用聚合支付，则使用SingleRefundV2
		refundListV2 = append(refundListV2, &payment.SingleRefundV2{
			OutPayUnionNo: financeOrder.FinanceOrderID,
			Amount:        refund.Amount,
		})
		//} else {
		//	refundList = append(refundList, &payment.SingleRefund{
		//		FinanceOrderID: financeOrder.FinanceOrderID,
		//		Amount:         refund.Amount,
		//	})
		//}
	}

	serviceReq := &payment.MergeRefundReq{
		Identity:          identity,
		RefundList:        refundList,
		RefundListV2:      refundListV2,
		OrderID:           order.FweOrder.OrderID,
		OutID:             utils.MakeRefundFinanceOutID(order.FweOrder.OrderID, bizReq.GetRefundType()),
		RefundFinanceType: bizReq.RefundType,
		OrderName:         order.FweOrder.OrderName,
		Reason:            bizReq.Reason,
		Extra:             nil,
		CallbackEvent:     utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:     "",
		IPAddress:         bizReq.IPAddress,
	}
	return serviceReq, nil
}
