package nc_ecom

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/statemachine/nc_state/nc_ecom"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	OrderRpc "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type PayTimeoutCallbackBaseExecution struct {
	*executor.ActionBaseExecution
	cbModel      *callback_model.PayCallbackModel
	bizRefundReq execution_common.RefundReq
	tag          map[string]string
}

func NewPayTimeoutCallbackCommonExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req, ok := sourceReq.(*engine.ActionOrderReq)
	if !ok {
		logs.CtxError(ctx, "[NewPayTimeoutCallbackCommonExecution] req is not ActionOrderReq, req = %v", tools.GetLogStr(sourceReq))
	}
	exe := &PayTimeoutCallbackBaseExecution{
		cbModel: new(callback_model.PayCallbackModel),
		tag:     make(map[string]string, 0),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel)
	return exe
}

func (e *PayTimeoutCallbackBaseExecution) PreProcess(ctx context.Context) error {
	if err := e.ActionBaseExecution.PreProcess(ctx); err != nil {
		return nil
	}
	if err := e.buildBizReq(); err != nil {
		return err
	}
	return nil
}

func (e *PayTimeoutCallbackBaseExecution) Process(ctx context.Context) error {

	var (
		bizErr         *errdef.BizErr
		intentionMoney int64
		refundList     []*payment.SingleRefundV2
	)

	for _, financeInfo := range e.GetOrder().FinanceList {
		if financeInfo.FinanceOrderType == CommonConsts.FinanceIntention.Value() {
			intentionMoney = financeInfo.Amount
			break
		}
	}

	conditionMap := map[string]interface{}{
		"intention_money": intentionMoney,
	}
	bizErr = e.FireWithCondition(ctx, conditionMap)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 查询退款信息
	req := &OrderRpc.QueryFinanceModelByOrderIDReq{
		OrderID:      e.GetOrder().FweOrder.OrderID,
		ReadStrategy: OrderRpc.ReadStrategyPtr(OrderRpc.ReadStrategy_ReadMaster),
		NeedRefund:   conv.BoolPtr(true),
	}
	fModel, bizErr := service.NewOrderService().QueryFinanceModel(ctx, req)
	if bizErr != nil {
		logs.CtxError(ctx, "[PayTimeoutCallbackBaseExecution] QueryFinanceModelByOrderID failed, err=%s", bizErr.Error())
		return bizErr
	}
	unRefundFinanceList := e.findUnRefundFinanceInfo(ctx, fModel)

	gslice.ForEach(unRefundFinanceList, func(financeInfo *fwe_trade_common.FinanceInfo) {
		// 目前没有分笔支付，后续如果有需要看支付单
		if financeInfo.PayStatus == fwe_trade_common.FinanceStatus_Complete && financeInfo.Amount > 0 {
			refundList = append(refundList, &payment.SingleRefundV2{
				OutPayUnionNo: financeInfo.FinanceOrderID,
				Amount:        financeInfo.Amount,
			})
		}
	})

	if len(refundList) == 0 {
		logs.CtxInfo(ctx, "[PayTimeoutCallbackBaseExecution] no need refund")
		return nil
	}

	refundReq := &payment.MergeRefundReq{
		Identity:          e.GetActionOrderReq().Identity,
		RefundListV2:      refundList,
		OrderID:           e.GetOrder().FweOrder.OrderID,
		OutID:             utils.MakeRefundFinanceOutID(e.GetOrder().FweOrder.OrderID, 1),
		RefundFinanceType: 1,
		OrderName:         e.GetOrder().FweOrder.OrderName,
		Reason:            "支付超时退款",
		Extra:             nil,
		CallbackEvent:     utils.MakeCallbackEvent(nc_ecom.RefundFinishEt.Value()),
		CallbackExtra:     "",
		IPAddress:         "127.0.0.1",
	}

	refundParam := &service_model.UnionRefundParam{
		OrderID:        e.GetOrder().FweOrder.OrderID,
		OrderName:      e.GetOrder().FweOrder.OrderName,
		RefundType:     refundReq.RefundFinanceType,
		RefundAmount:   intentionMoney,
		MergeRefundReq: refundReq,
	}

	if _, bizErr = service.NewUnionRefundService().UnionRefund(ctx, refundParam); bizErr != nil {
		return bizErr
	}
	return nil
}

func (e *PayTimeoutCallbackBaseExecution) findUnRefundFinanceInfo(ctx context.Context, fModel *fwe_trade_common.OrderFinanceModel) (res []*fwe_trade_common.FinanceInfo) {
	var (
		payFinanceList  []*fwe_trade_common.FinanceInfo
		unRefundPayList []string
	)

	payFinanceList = gslice.Filter(fModel.FinanceList, func(info *fwe_trade_common.FinanceInfo) bool {
		return info.TradeCategory == 0 || info.TradeCategory == fwe_trade_common.TradeCategory_Pay
	})

	refundPayMap := gslice.ToMap(fModel.RefundList, func(t *fwe_trade_common.FinanceRefund) (string, bool) {
		return t.PayOrderNo, true
	})

	for _, pay := range fModel.PayList {
		if !refundPayMap[pay.PayOrderNo] {
			unRefundPayList = append(unRefundPayList, pay.PayOrderNo)
		}
	}

	// 一个资金单只有一笔支付， 找到退款单对应的支付单的资金单，过滤掉
	for _, finance := range payFinanceList {
		if len(finance.PayList) > 0 && slices.ContainsString(unRefundPayList, finance.PayList[0].PayOrderNo) {
			res = append(res, finance)
		}
	}

	return
}

func (e *PayTimeoutCallbackBaseExecution) PostProcess(ctx context.Context) error {
	var (
		orderID      = e.GetOrder().FweOrder.OrderID
		stateMachine = e.GetStateMachine()
	)
	// 更新订单tag
	if bizErr := service.NewOrderService().UpdateOrderTag(ctx, orderID, e.bizRefundReq.Tag); bizErr != nil {
		return bizErr
	}

	// 更新状态
	updateParams := &service_model.UpdateOrderParams{}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	updateParams.Operator = e.GetActionOrderReq().GetOperator()
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		return bizErr
	}

	// 归还库存
	if bizErr := restoreStock(ctx, e.GetOrder().FweOrder, e.GetOrder().TagMap); bizErr != nil {
		return bizErr
	}

	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *PayTimeoutCallbackBaseExecution) buildBizReq() error {
	var (
		refundList []*execution_common.SingleRefund
	)

	e.tag["infra_cancel_source"] = "2"
	e.tag["biz_refund_reason"] = "支付超时退款"
	for _, financeInfo := range e.GetOrder().FinanceList {
		if financeInfo.TradeCategory != int32(fwe_trade_common.TradeCategory_Pay) && financeInfo.TradeCategory != 0 {
			continue
		}
		if financeInfo.Status == int32(fwe_trade_common.FinanceStatus_Complete) && financeInfo.Amount > 0 {
			refundList = append(refundList, &execution_common.SingleRefund{
				FinanceOrderType: financeInfo.FinanceOrderType,
				Amount:           financeInfo.Amount,
			})
		}
	}

	// 不需要退款直接取消
	if len(refundList) == 0 {
		return nil
	}

	e.bizRefundReq = execution_common.RefundReq{
		RefundList:     refundList,
		RefundType:     1,
		Reason:         "支付超时退款",
		IPAddress:      "127.0.0.1",
		CallbackAction: nc_ecom.RefundFinishEt.Value(),
		Tag:            e.tag,
	}
	return nil
}
