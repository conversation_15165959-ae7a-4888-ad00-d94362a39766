package sh_buy

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/utils"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_buy/sh_buy_common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_buy_model"
)

type PersonBusinessContStartExecution struct {
	*sh_buy_common.BaseContStartExecution
	conf sh_buy_model.Conf
}

func NewPersonBusinessContStartExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	t := &PersonBusinessContStartExecution{}
	t.BaseContStartExecution = sh_buy_common.NewBaseContStartExecution(ctx, rpcReq, &t.conf)
	return t
}

func (e *PersonBusinessContStartExecution) Process(ctx context.Context) error {
	return e.BaseContStartExecution.Do(ctx, e.buildBaseContParams())
}

func (e *PersonBusinessContStartExecution) buildBaseContParams() *sh_buy_common.BaseContStartParams {
	output := &sh_buy_common.BaseContStartParams{
		CallbackEvent:    utils.MakeCallbackEvent(consts.SHBuyPersonCarBusinessContFinish),
		FinanceOrderType: consts.SHBuyCarEarnestPay,
		ContType:         consts.SHBuyBusinessCont.Int32(),
		SmsID:            e.conf.BusinessContSmsID,
		SmsChannelID:     e.conf.SMSChannel,
		CompanyName:      e.conf.CompanyInfo.Name,
		CompanyCreditID:  e.conf.CompanyInfo.CreditCode,
	}
	return output
}
