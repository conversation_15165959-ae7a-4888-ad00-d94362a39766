package sh_buy

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/utils"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_buy/sh_buy_common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
)

type FinalWithdrawStartExecution struct {
	*sh_buy_common.BaseWithdrawStartExecution
}

func NewFinalWithdrawStartExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	return &FinalWithdrawStartExecution{
		BaseWithdrawStartExecution: sh_buy_common.NewBaseWithdrawStartExecution(ctx, rpcReq),
	}
}

func (e *FinalWithdrawStartExecution) Process(ctx context.Context) error {
	return e.BaseWithdrawStartExecution.Do(ctx, e.buildBaseWithdrawParams())
}

func (e *FinalWithdrawStartExecution) buildBaseWithdrawParams() *sh_buy_common.BaseWithdrawStartParams {
	output := &sh_buy_common.BaseWithdrawStartParams{
		CallbackEvent:    utils.MakeCallbackEvent(consts.SHBuyPersonCarFinalPayFinish),
		FinanceOrderType: consts.SHBuyCarFinalPay,
	}
	return output
}
