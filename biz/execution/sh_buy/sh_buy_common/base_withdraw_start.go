package sh_buy_common

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_buy_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"github.com/bytedance/sonic"
)

type BaseWithdrawStartParams struct {
	FinanceOrderType  consts.FinanceOrderType
	CallbackEvent     string
	FailCallbackEvent string
}

type BaseWithdrawStartExecution struct {
	*executor.ActionBaseExecution
	conf        sh_buy_model.Conf
	withdrawReq sh_buy_model.WithdrawReq
	result      interface{}
}

func NewBaseWithdrawStartExecution(ctx context.Context, rpcReq interface{}) *BaseWithdrawStartExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	t := &BaseWithdrawStartExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), &t.conf, &t.withdrawReq, options...)
	return t
}

func (execution *BaseWithdrawStartExecution) Do(ctx context.Context, input *BaseWithdrawStartParams) error {
	var paymentRpc = service.NewTradePayment()

	// 出款
	withdrawReq, bizErr := execution.buildWithdrawReq(ctx, input)
	if bizErr != nil {
		logs.CtxError(ctx, "[BaseWithdrawStartExecution] err=%s", bizErr.Error())
		return bizErr
	}
	withdrawNo, bizErr := paymentRpc.WithdrawDeposit(ctx, withdrawReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[BaseWithdrawStartExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 打包
	rpcRsp := &sh_buy_model.WithdrawRsp{
		WithdrawNo: withdrawNo,
	}
	execution.result, _ = sonic.MarshalString(rpcRsp)
	return nil
}

func (execution *BaseWithdrawStartExecution) Result() interface{} {
	return execution.result
}

func (execution *BaseWithdrawStartExecution) buildWithdrawReq(ctx context.Context, input *BaseWithdrawStartParams) (*payment.WithdrawDepositReq, *errdef.BizErr) {
	var (
		bizErr       *errdef.BizErr
		conf         = execution.conf
		rpcReq       = execution.GetActionOrderReq()
		order        = execution.GetOrder()
		fweOrder     = order.FweOrder
		financeOrder = packer.FinanceGetByType(order.FinanceList, input.FinanceOrderType.Int32())
	)

	if input.CallbackEvent == "" {
		bizErr = errdef.NewParamsErr("callback is nil")
		logs.CtxError(ctx, "[BaseWithdrawStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}
	if financeOrder == nil {
		bizErr = errdef.NewParamsErr("no earnest pay finance order")
		logs.CtxError(ctx, "[BaseContStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}

	bankInfo, bizErr := execution.buildBankInfo(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[BaseWithdrawStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}

	withdrawReq := &payment.WithdrawDepositReq{
		OrderID:           fweOrder.OrderID,
		FinanceOrderID:    financeOrder.FinanceOrderID,
		MerchantID:        conf.MerchantID,
		MerchantName:      conf.MerchantName,
		AppID:             conf.AppID,
		Amount:            financeOrder.Amount,
		WithdrawType:      payment.WithdrawType_TEMP_WITHDRAW,
		IPAddress:         "*************",
		Mid:               nil,
		Currency:          payment.CurrencyTypePtr(payment.CurrencyType_CNY),
		BankCardInfo:      bankInfo,
		Operator:          rpcReq.GetOperator(),
		Identity:          rpcReq.GetIdentity(),
		CallbackEvent:     input.CallbackEvent,
		CallbackExtra:     "",
		FailCallbackEvent: input.FailCallbackEvent,
	}

	return withdrawReq, nil
}

func (execution *BaseWithdrawStartExecution) buildBankInfo(ctx context.Context) (bankInfo *fwe_trade_common.BankInfo, bizErr *errdef.BizErr) {
	var (
		order      = execution.GetOrder()
		sellerInfo *fwe_trade_common.TradeSubjectInfo
	)

	sellerInfo, bizErr = packer.CommonTradeSubjectDeserialize(order.FweOrder.SellerID, *order.FweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxError(ctx, "[BaseWithdrawStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}

	switch sellerInfo.SubjectType {
	case fwe_trade_common.TradeSubjectType_Person:
		return sellerInfo.PersonInfo.BankInfo, nil
	case fwe_trade_common.TradeSubjectType_Company:
		return sellerInfo.CompanyInfo.BankInfo, nil
	default:
		bizErr := errdef.NewParamsErr("type is err")
		logs.CtxError(ctx, "[BaseWithdrawStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}
}
