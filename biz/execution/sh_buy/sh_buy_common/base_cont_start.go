package sh_buy_common

import (
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"context"
	"fmt"
	"strconv"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_buy_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"github.com/bytedance/sonic"
)

type BaseContStartParams struct {
	FinanceOrderType consts.FinanceOrderType
	SmsID            int64
	SmsChannelID     int32
	ContType         int32
	CompanyName      string
	CompanyCreditID  string
	CallbackEvent    string
}

type BaseContStartExecution struct {
	*executor.ActionBaseExecution
	*service.ContractService
	contReq sh_buy_model.ContSignReq
	result  interface{}
}

func NewBaseContStartExecution(ctx context.Context, actionReq, confPtr interface{}) *BaseContStartExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	t := &BaseContStartExecution{
		ContractService: service.NewContractService(),
	}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), confPtr, &t.contReq, options...)
	return t
}

func (e *BaseContStartExecution) Do(ctx context.Context, input *BaseContStartParams) error {
	var (
		bizErr            *errdef.BizErr
		contNo            string
		createContractReq *service_model.ContractCreateParam
	)

	createContractReq, bizErr = e.buildCreateContractReq(ctx, input)
	if bizErr != nil {
		logs.CtxError(ctx, "[BaseContStartExecution] err=%s", bizErr.Error())
		return bizErr
	}
	contNo, bizErr = e.CreateContract(ctx, createContractReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[BaseContStartExecution] err=%s", bizErr.Error())
		return bizErr
	}
	actionRsp := &sh_buy_model.ContSignRsp{ContSerial: contNo}
	e.result, _ = sonic.MarshalString(actionRsp)
	return nil
}

func (e *BaseContStartExecution) Result() interface{} {
	return e.result
}

func (e *BaseContStartExecution) buildCreateContractReq(ctx context.Context, input *BaseContStartParams) (createContractReq *service_model.ContractCreateParam, bizErr *errdef.BizErr) {

	if input == nil || e.GetOrder() == nil {
		bizErr = errdef.NewParamsErr("cont params is nil")
		logs.CtxError(ctx, "[BaseContStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}

	var (
		order         = e.GetOrder()
		fweOrder      = order.FweOrder
		actionReq     = e.GetActionOrderReq()
		contSignReq   = e.contReq
		operator      = actionReq.GetOperator()
		operatorID, _ = strconv.ParseInt(operator.GetOperatorID(), 10, 64)
		financeOrder  = packer.FinanceGetByType(order.FinanceList, input.FinanceOrderType.Int32())
	)

	if input.CallbackEvent == "" {
		bizErr = errdef.NewParamsErr("callback is nil")
		logs.CtxError(ctx, "[BaseContStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}
	if fweOrder.SellerExtra == nil {
		bizErr = errdef.NewParamsErr("seller extra is nil")
		logs.CtxError(ctx, "[BaseContStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}
	if financeOrder == nil {
		bizErr = errdef.NewParamsErr(fmt.Sprintf("no finance order (type=%d)", input.FinanceOrderType))
		logs.CtxError(ctx, "[BaseContStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}
	signParty, bizErr := e.buildSignPartyData(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[BaseContStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}

	inOutData := &service_model.InOutData{
		Currency: 1,
	}
	switch input.ContType {
	case consts.SHBuyBusinessCont.Int32():
		inOutData.TotalOut = financeOrder.Amount
	case consts.SHBuyGuaranteeCont.Int32():
		inOutData.TotalIn = financeOrder.Amount
	default:
		bizErr = errdef.NewParamsErr(fmt.Sprintf("cont type = %d err", input.ContType))
		return
	}

	createContractReq = &service_model.ContractCreateParam{
		OrderID:        order.FweOrder.OrderID,
		TenantType:     int32(actionReq.GetIdentity().GetTenantType()),
		BizScene:       actionReq.GetIdentity().GetBizScene(),
		ContType:       input.ContType,
		OperatorID:     operatorID,
		OperatorName:   operator.GetOperatorName(),
		TmplID:         contSignReq.ContTmplID,
		NeedSignNoCert: false,
		SmsTmplID:      input.SmsID,
		SmsChannelID:   input.SmsChannelID,
		TmplParams:     contSignReq.ContParams,
		SignPartList: []*service_model.SignPart{
			// 买方懂懂分公司
			{
				SignPosition: 1,
				IsInner:      true,
				CardType:     2,
				IdentName:    input.CompanyName,
				IdentID:      input.CompanyCreditID,
			},
			// 卖方客户
			signParty,
		},
		InOutData:     inOutData,
		CallbackEvent: utils.MakeCallbackEvent(input.CallbackEvent),
		CallbackExtra: utils.MakeContractCallbackExtra(fweOrder.OrderID),
	}
	return createContractReq, nil
}

func (e *BaseContStartExecution) buildSignPartyData(ctx context.Context) (data *service_model.SignPart, bizErr *errdef.BizErr) {
	var (
		order      = e.GetOrder()
		sellerInfo *fwe_trade_common.TradeSubjectInfo
	)

	data = &service_model.SignPart{
		SignPosition: 2,
		IsInner:      false,
	}

	sellerInfo, bizErr = packer.CommonTradeSubjectDeserialize(order.FweOrder.SellerID, *order.FweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxError(ctx, "[GuaranteeContStartExecution] err=%s", bizErr.Error())
		return
	}
	switch sellerInfo.SubjectType {
	case fwe_trade_common.TradeSubjectType_Person:
		if sellerInfo.PersonInfo == nil {
			bizErr = errdef.NewParamsErr("seller subject person info err")
			return
		}
		data.CardType = 1
		data.IdentName = sellerInfo.PersonInfo.PersonName
		data.IdentID = sellerInfo.PersonInfo.IDCard
		data.SignerName = sellerInfo.PersonInfo.PersonName
		data.SignerPhone = sellerInfo.PersonInfo.PersonPhone
	case fwe_trade_common.TradeSubjectType_Company:
		if sellerInfo.CompanyInfo == nil {
			bizErr = errdef.NewParamsErr("seller subject company info err")
			return
		}
		data.CardType = 2
		data.IdentName = sellerInfo.CompanyInfo.CompanyName
		data.IdentID = sellerInfo.CompanyInfo.CreditCode
		data.SignerName = sellerInfo.CompanyInfo.OwnerName
		data.SignerPhone = sellerInfo.CompanyInfo.OwnerPhone
	default:
		bizErr = errdef.NewParamsErr("seller subject type err")
		logs.CtxError(ctx, "[GuaranteeContStartExecution] err=%s", bizErr.Error())
		return
	}
	return
}
