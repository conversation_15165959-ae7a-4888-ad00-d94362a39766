package sh_buy_common

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"

	"code.byted.org/gopkg/logs"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_buy_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"github.com/bytedance/sonic"
)

type BaseCashPayStartParams struct {
	CallbackEvent    string
	FinanceOrderType consts.FinanceOrderType
}

type BaseCashPayStartExecution struct {
	*executor.ActionBaseExecution
	conf       sh_buy_model.ConfV2
	cashPayReq sh_buy_model.CashPayReq
	result     interface{}
}

func NewBaseCashPayStartExecution(ctx context.Context, rpcReq interface{}) *BaseCashPayStartExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	t := &BaseCashPayStartExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq),
		&t.conf, &t.cashPayReq, options...)
	return t
}

func (execution *BaseCashPayStartExecution) Do(ctx context.Context, input *BaseCashPayStartParams) error {
	var (
		paymentRpc = service.NewTradePayment()
		err        error
	)

	cashPayReq, bizErr := execution.buildCashPayReq(ctx, input)
	if bizErr != nil {
		return bizErr
	}
	cashPayNo, cashPayData, bizErr := paymentRpc.CreateCashPay(ctx, cashPayReq)
	if bizErr != nil {
		return bizErr
	}

	rpcRsp := &sh_buy_model.CashPayRsp{
		PayNo:   cashPayNo,
		PayData: cashPayData,
	}
	execution.result, err = sonic.MarshalString(rpcRsp)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		return bizErr
	}
	return nil
}

func (execution *BaseCashPayStartExecution) Result() interface{} {
	return execution.result
}

func (execution *BaseCashPayStartExecution) buildCashPayReq(ctx context.Context, input *BaseCashPayStartParams) (cashPayReq *payment.CreateCashPayReq, bizErr *errdef.BizErr) {
	var (
		bizRequest      = execution.cashPayReq
		rpcReq          = execution.GetActionOrderReq()
		order           = execution.GetOrder()
		fweOrder        = order.FweOrder
		finance         = packer.FinanceGetByType(order.FinanceList, input.FinanceOrderType.Int32())
		conf            = execution.conf
		cashierDeskType = fwe_trade_common.CashierDeskType_PC // 这里暂时写死，需要业务方配合修改参数
	)

	if input.CallbackEvent == "" {
		bizErr = errdef.NewParamsErr("callback is nil")
		logs.CtxError(ctx, "[BaseCashPayStartExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}

	merchantBase, bizErr := conf.GetMerchantBase(cashierDeskType)
	if bizErr != nil {
		logs.CtxError(ctx, "[BaseCashPayStartExecution] GetMerchantBase error, err = %+v", bizErr)
		return nil, bizErr
	}

	cashPayReq = &payment.CreateCashPayReq{
		Identity:        rpcReq.GetIdentity(),
		UserID:          rpcReq.GetOperator().GetOperatorID(),
		OrderID:         fweOrder.OrderID,
		FinanceType:     bizRequest.FinanceOrderType.Int32(),
		PayOrderNo:      nil,
		CashierDeskType: cashierDeskType,
		OsType:          nil,
		Currency:        payment.CurrencyTypePtr(payment.CurrencyType_CNY),
		TotalAmount:     finance.Amount,
		ExpireTime:      conv.Int64Ptr(bizRequest.ExpireTime),
		RedirectURL:     conv.StringPtr(bizRequest.RedirectUrl),
		IPAddress:       conv.StringPtr(bizRequest.Ip),
		PayLimitList: []fwe_trade_common.PayLimitType{
			fwe_trade_common.PayLimitType_CNetBank,
			fwe_trade_common.PayLimitType_BNetBank,
		},
		Extra:         nil,
		CallbackEvent: input.CallbackEvent,
		CallbackExtra: "",
		MerchantInfo: &payment.MerchantInfo{
			MerchantID: merchantBase.MerchantID,
			AppID:      merchantBase.AppID,
		},
	}

	return cashPayReq, nil
}
