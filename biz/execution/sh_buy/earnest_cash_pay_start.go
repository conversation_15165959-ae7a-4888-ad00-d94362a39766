package sh_buy

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_buy/sh_buy_common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
)

type EarnestCashPayStartExecution struct {
	*sh_buy_common.BaseCashPayStartExecution
}

func NewEarnestCashPayStartExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	t := &EarnestCashPayStartExecution{}
	t.BaseCashPayStartExecution = sh_buy_common.NewBaseCashPayStartExecution(ctx, rpcReq)
	return t
}

func (e *EarnestCashPayStartExecution) Process(ctx context.Context) error {
	return e.BaseCashPayStartExecution.Do(ctx, e.buildCashPayStartParams())
}

func (e *EarnestCashPayStartExecution) buildCashPayStartParams() *sh_buy_common.BaseCashPayStartParams {
	output := &sh_buy_common.BaseCashPayStartParams{
		CallbackEvent:    utils.MakeCallbackEvent(consts.SHBackCompanyCarEarnestPayFinish),
		FinanceOrderType: consts.SHBuyCarEarnestPay,
	}
	return output
}
