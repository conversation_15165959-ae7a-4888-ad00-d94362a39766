package sh_buy

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_buy/sh_buy_common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
)

type FinalCashPayStartExecution struct {
	*sh_buy_common.BaseCashPayStartExecution
}

func NewFinalCashPayStartExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	t := &FinalCashPayStartExecution{}
	t.BaseCashPayStartExecution = sh_buy_common.NewBaseCashPayStartExecution(ctx, rpcReq)
	return t
}

func (e *FinalCashPayStartExecution) Process(ctx context.Context) error {
	return e.BaseCashPayStartExecution.Do(ctx, e.buildCashPayStartParams())
}

func (e *FinalCashPayStartExecution) buildCashPayStartParams() *sh_buy_common.BaseCashPayStartParams {
	output := &sh_buy_common.BaseCashPayStartParams{
		CallbackEvent:    utils.MakeCallbackEvent(consts.SHBackCompanyCarFinalPayFinish),
		FinanceOrderType: consts.SHBuyCarFinalPay,
	}
	return output
}
