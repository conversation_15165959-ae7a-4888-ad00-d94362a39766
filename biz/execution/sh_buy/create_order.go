package sh_buy

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/packer"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_buy_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type CreateOrderExecution struct {
	*executor.CreateBaseExecution
	conf   sh_buy_model.Conf
	result interface{}
}

func NewCreateOrderExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	t := &CreateOrderExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, rpcReq.(*engine.CreateOrderReq), &t.conf)
	return t
}

func (execution *CreateOrderExecution) Process(ctx context.Context) error {
	var (
		err            error
		bizErr         *errdef.BizErr
		conf           = execution.conf
		createOrderReq = execution.GetCreateOrderReq()
		bizScene       = consts.GetBizScene(createOrderReq.GetIdentity().GetBizScene())
		tradeType      = int32(consts.GetTradeTypeByBizScene(bizScene))
		orderService   = service.NewOrderService()
		productInfo    = createOrderReq.GetProductInfo()
		stateMachine   = execution.GetStateMachine()
		totalAmount    = createOrderReq.TotalAmount
	)

	// 驱动状态
	err = stateMachine.Fire(ctx, consts.CreateAction, nil)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "CreateOrderExecution")
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 创建订单生成对应订单号
	buyerID := packer.CommonTradeSubjectIDGet(createOrderReq.BuyerInfo)
	sellerID := packer.CommonTradeSubjectIDGet(createOrderReq.SellerInfo)

	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createOrderReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene.Int32(),
		SmVersion:          createOrderReq.GetIdentity().GetSmVersion(),
		OrderID:            execution.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createOrderReq.GetOrderName(),
		OrderDesc:          createOrderReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductExtra:       productInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		TotalAmount:        totalAmount,
		TotalPayAmount:     totalAmount,
		TotalSubsidyAmount: 0,
		TradeType:          tradeType,
		BuyerID:            buyerID,
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createOrderReq.BuyerInfo)),
		SellerID:           sellerID,
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createOrderReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createOrderReq.IsTest),
		IdempotentID:       createOrderReq.GetIdemID(),
	}
	financeList, bizErr := execution.buildFinanceList(ctx, fweOrder, conf.BaseConfig, createOrderReq.GetFinanceList())
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}
	order := &service_model.Order{
		FweOrder:    fweOrder,
		FinanceList: financeList,
		BizExtra:    createOrderReq.Extra,
	}

	bizErr = orderService.CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	execution.result = execution.GetOrderID()
	return nil
}

func (execution *CreateOrderExecution) Result() interface{} {
	return execution.result
}

func (execution *CreateOrderExecution) buildFinanceList(ctx context.Context, order *db_model.FweOrder, conf *model.BaseConfig, input []*fwe_trade_common.FinanceInfo) (output []*db_model.FFinanceOrder, bizErr *errdef.BizErr) {
	if order == nil {
		bizErr = errdef.NewParamsErr("order is nil")
		logs.CtxError(ctx, "[CommonService] err=%v", bizErr.Error())
		return
	}
	for _, v := range input {
		if v == nil {
			logs.CtxError(ctx, "[CommonService] v is nil")
			continue
		}

		// 创建资金单号
		financeOrderID, err := utils.MustGenIDInt64()
		if err != nil {
			bizErr = errdef.NewBizErr(errdef.ServerException, err, "CreateOrderExecution")
			logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
			return nil, bizErr
		}

		output = append(output, &db_model.FFinanceOrder{
			TenantType:       order.TenantType,
			BizScene:         order.BizScene,
			AppID:            "", // 这里空着也不能填错的
			MerchantID:       conf.MerchantID,
			Mid:              conf.MID,
			OrderID:          order.OrderID,
			OrderName:        order.OrderName,
			TradeType:        execution.buildFinanceTradeType(),
			FinanceOrderID:   utils.MakeFinanceOrderID(financeOrderID, v.FinanceOrderType),
			FinanceOrderType: v.FinanceOrderType,
			Amount:           v.Amount,
			ProcessAmount:    0,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
		})
	}
	return
}

func (execution *CreateOrderExecution) buildFinanceTradeType() string {
	switch execution.GetBizIdentity().GetBizScene() {
	case consts.BizSceneSHBuyPersonCar.Int32():
		return consts.TradeTypeWithdrawBank.String()
	case consts.BizSceneSHBuyCompanyCar.Int32():
		return consts.TradeTypeWithdrawBank.String()
	case consts.BizSceneSHBackCompanyCar.Int32():
		return consts.TradeTypePayCashier.String()
	case consts.BizSceneSHDirectBuyCompanyCar.Int32():
		return consts.TradeTypeWithdrawBank.String()
	case consts.BizSceneSHBuyBackPersonCar.Int32():
		return consts.TradeTypeWithdrawBank.String()
	case consts.BizSceneSHBuyBackCompanyCar.Int32():
		return consts.TradeTypeWithdrawBank.String()
	default:
		return ""
	}
}
