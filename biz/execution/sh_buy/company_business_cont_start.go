package sh_buy

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/utils"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_buy/sh_buy_common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_buy_model"
)

type CompanyBusinessContStartExecution struct {
	*sh_buy_common.BaseContStartExecution
	conf sh_buy_model.Conf
}

func NewCompanyBusinessContStartExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	t := &CompanyBusinessContStartExecution{}
	t.BaseContStartExecution = sh_buy_common.NewBaseContStartExecution(ctx, rpcReq, &t.conf)
	return t
}

func (e *CompanyBusinessContStartExecution) Process(ctx context.Context) error {
	return e.BaseContStartExecution.Do(ctx, e.buildBaseContParams())
}

func (e *CompanyBusinessContStartExecution) buildBaseContParams() *sh_buy_common.BaseContStartParams {
	var (
		companyName     string
		companyCreditID string
	)
	if e.conf.CompanyInfo != nil {
		companyName = e.conf.CompanyInfo.Name
		companyCreditID = e.conf.CompanyInfo.CreditCode
	}
	output := &sh_buy_common.BaseContStartParams{
		CallbackEvent:    utils.MakeCallbackEvent(consts.SHBuyCompanyCarBusinessContFinish),
		FinanceOrderType: consts.SHBuyCarTotalPay,
		ContType:         consts.SHBuyBusinessCont.Int32(),
		SmsID:            e.conf.BusinessContSmsID,
		SmsChannelID:     e.conf.SMSChannel,
		CompanyName:      companyName,
		CompanyCreditID:  companyCreditID,
	}
	return output
}
