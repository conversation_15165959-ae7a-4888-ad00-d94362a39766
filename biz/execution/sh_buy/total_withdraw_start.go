package sh_buy

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/utils"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_buy/sh_buy_common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
)

type TotalWithdrawStartExecution struct {
	*sh_buy_common.BaseWithdrawStartExecution
}

func NewTotalWithdrawStartExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	return &TotalWithdrawStartExecution{
		BaseWithdrawStartExecution: sh_buy_common.NewBaseWithdrawStartExecution(ctx, rpcReq),
	}
}

func (e *TotalWithdrawStartExecution) Process(ctx context.Context) error {
	return e.BaseWithdrawStartExecution.Do(ctx, e.buildBaseWithdrawParams())
}

func (e *TotalWithdrawStartExecution) buildBaseWithdrawParams() *sh_buy_common.BaseWithdrawStartParams {
	output := &sh_buy_common.BaseWithdrawStartParams{
		CallbackEvent:    utils.MakeCallbackEvent(consts.SHBuyCompanyCarTotalPayFinish),
		FinanceOrderType: consts.SHBuyCarTotalPay,
	}
	return output
}
