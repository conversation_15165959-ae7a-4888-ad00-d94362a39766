package sh_buy

import (
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"fmt"
)

type WithdrawCallbackBaseExecution struct {
	*executor.ActionBaseExecution
	cbModel *callback_model.WithdrawCallbackModel
	success bool
}

func NewWithdrawFailCallbackExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := NewWithdrawCallbackBaseExecution(ctx, sourceReq, nil, false)
	return exe
}

func NewWithdrawCallbackBaseExecution(ctx context.Context, sourceReq interface{}, confPtr interface{}, success bool) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &WithdrawCallbackBaseExecution{
		cbModel: new(callback_model.WithdrawCallbackModel),
		success: success,
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, confPtr, exe.cbModel, options...)
	return exe
}

func (e *WithdrawCallbackBaseExecution) CheckParams(ctx context.Context) error {
	if !e.success && e.cbModel.Status != int32(fwe_trade_common.CommonStatus_Fail) {
		errMsg := fmt.Sprintf("[WithdrawCallbackBaseExecution.CheckParams] status(%v) != fail(%v)", e.cbModel.Status, fwe_trade_common.CommonStatus_Fail)
		return errdef.NewParamsErr(errMsg)
	}
	return nil
}

func (e *WithdrawCallbackBaseExecution) Process(ctx context.Context) error {
	return nil
}
