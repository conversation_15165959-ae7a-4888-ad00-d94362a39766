package sh_buy

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/utils"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_buy/sh_buy_common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_buy_model"
)

type GuaranteeContStartExecution struct {
	*sh_buy_common.BaseContStartExecution
	conf sh_buy_model.Conf
}

func NewGuaranteeContStartExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	t := &GuaranteeContStartExecution{}
	t.BaseContStartExecution = sh_buy_common.NewBaseContStartExecution(ctx, rpcReq, &t.conf)
	return t
}

func (e *GuaranteeContStartExecution) Process(ctx context.Context) error {
	return e.BaseContStartExecution.Do(ctx, e.buildBaseContParams())
}

func (e *GuaranteeContStartExecution) buildBaseContParams() *sh_buy_common.BaseContStartParams {
	output := &sh_buy_common.BaseContStartParams{
		CallbackEvent:    utils.MakeCallbackEvent(consts.SHBuyCompanyCarGuaranteeContFinish),
		FinanceOrderType: consts.SHBuyCarTotalPay,
		ContType:         consts.SHBuyGuaranteeCont.Int32(),
		SmsID:            e.conf.GuaranteeContSmsID,
		SmsChannelID:     e.conf.SMSChannel,
		CompanyName:      e.conf.CompanyInfo.Name,
		CompanyCreditID:  e.conf.CompanyInfo.CreditCode,
	}
	return output
}
