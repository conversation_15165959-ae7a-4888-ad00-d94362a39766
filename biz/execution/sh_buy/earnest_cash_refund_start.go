package sh_buy

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/utils"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_buy_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"github.com/bytedance/sonic"
)

type EarnestCashRefundStartExecution struct {
	*executor.ActionBaseExecution
	conf          sh_buy_model.Conf
	cashRefundReq sh_buy_model.CashRefundReq
	result        interface{}
}

func NewEarnestCashRefundStartExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	t := &EarnestCashRefundStartExecution{}
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq),
		&t.conf, &t.cashRefundReq, options...)
	return t
}

func (execution *EarnestCashRefundStartExecution) Process(ctx context.Context) error {
	var (
		paymentRpc = service.NewTradePayment()
		err        error
	)

	cashRefundReq, bizErr := execution.buildCashRefundReq(ctx)
	if bizErr != nil {
		return bizErr
	}
	refundNo, bizErr := paymentRpc.CreateCashRefund(ctx, cashRefundReq)
	if bizErr != nil {
		return bizErr
	}

	rpcRsp := &sh_buy_model.CashRefundRsp{
		RefundNo: refundNo,
	}
	execution.result, err = sonic.MarshalString(rpcRsp)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		return bizErr
	}
	return nil
}

func (execution *EarnestCashRefundStartExecution) Result() interface{} {
	return execution.result
}

func (execution *EarnestCashRefundStartExecution) buildCashRefundReq(ctx context.Context) (*payment.CreateCashRefundReq, *errdef.BizErr) {
	var (
		bizRequest = execution.cashRefundReq
		rpcReq     = execution.GetActionOrderReq()
		order      = execution.GetOrder()
		fweOrder   = order.FweOrder
		finance    = packer.FinanceGetByType(order.FinanceList, consts.SHBuyCarEarnestPay.Int32())
	)

	cashRefundReq := &payment.CreateCashRefundReq{
		CallbackEvent: utils.MakeCallbackEvent(consts.SHBackCompanyCarEarnestRefundFinish),
		Identity:      rpcReq.GetIdentity(),
		OrderID:       fweOrder.OrderID,
		FinanceType:   bizRequest.FinanceOrderType.Int32(),
		RefundOrderNo: nil,
		Currency:      payment.CurrencyTypePtr(payment.CurrencyType_CNY),
		Amount:        finance.Amount,
		Reason:        bizRequest.Reason,
		IPAddress:     nil,
		Extra:         nil,
		CallbackExtra: "",
	}

	return cashRefundReq, nil
}
