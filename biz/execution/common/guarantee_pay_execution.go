package common

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type GuaranteePayExecution struct {
	*executor.ActionBaseExecution
	bizReq execution_common.GuaranteePayReq
	bizRsp execution_common.GuaranteePayRsp
	conf   model.CommonConf
}

func NewGuaranteePayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &GuaranteePayExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq)
	return e
}

func (e *GuaranteePayExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.bizReq
	if bizReq.MerchantID == "" || bizReq.AppID == "" {
		return errdef.NewParamsErr("Merchant参数错误")
	}
	return nil
}

func (e *GuaranteePayExecution) PreProcess(ctx context.Context) error {
	var (
		bizReq = e.bizReq
	)

	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}

	// 判断是否做合同金额校验
	if !e.conf.CheckContStructField {
		return nil
	}
	// amount 取资金单金额

	// 校验
	param := &service.CheckAmountByContParam{
		OrderID:          e.GetActionOrderReq().OrderID,
		FinanceOrderType: bizReq.FinanceOrderType,
		Amount:           bizReq.Amount,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[WithdrawExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[WithdrawExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "支付校验不通过")
	}

	return nil
}

func (e *GuaranteePayExecution) Process(ctx context.Context) error {
	var (
		bizErr         *errdef.BizErr
		serviceReq     *payment.CreateGuaranteePayReq
		payData, payNo string
		orderID        = e.GetActionOrderReq().GetOrderID()
		orderService   = service.NewOrderService()
		updateParams   = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	account, bizErr := e.GetFweFinanceAccount(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[GuaranteePayExecution-Process] GetFweFinanceAccount error , err =%+v", bizErr)
		return bizErr
	}

	serviceReq, bizErr = e.buildReq(ctx, account)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[GuaranteePayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	payData, payNo, bizErr = service.NewTradePayment().CreateGuaranteePay(ctx, serviceReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[GuaranteePayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.GuaranteePayRsp{
		PayData:    payData,
		PayOrderNo: payNo,
	}
	return nil
}

func (e *GuaranteePayExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *GuaranteePayExecution) buildReq(ctx context.Context, account *shop.FinanceAccount) (*payment.CreateGuaranteePayReq, *errdef.BizErr) {
	var (
		productReq = e.bizReq
		order      = e.GetOrder()
		fweOrder   = order.FweOrder
		bizErr     *errdef.BizErr
	)

	financeOrder := packer.FinanceGetByType(order.FinanceList, productReq.FinanceOrderType)
	if financeOrder == nil {
		bizErr = errdef.NewParamsErr("no finance order")
		return nil, bizErr
	}

	// h5 支付 和 pc 支付 是否同一个 appid，可以配置同一个

	PayLimitList := productReq.PayLimitList
	// pc 支付指定收银台
	if productReq.CashierDeskType == fwe_trade_common.CashierDeskType_PC {
		PayLimitList = []fwe_trade_common.PayLimitType{
			fwe_trade_common.PayLimitType_CNetBank,
			fwe_trade_common.PayLimitType_BNetBank,
		}
	}
	serviceReq := &payment.CreateGuaranteePayReq{
		Identity:             e.GetBizIdentity(),
		CallbackEvent:        utils.MakeCallbackEvent(productReq.CallbackAction),
		TimeoutCallbackEvent: utils.MakeCallbackEvent(productReq.TimeoutAction),
		CashierDeskType:      productReq.CashierDeskType,
		OrderID:              fweOrder.OrderID,
		FinanceType:          productReq.FinanceOrderType,
		OsType:               nil,
		UID:                  account.GetUID(),
		UserID:               "user_id",
		TotalAmount:          productReq.Amount,
		SubsidyAmount:        nil,
		RedirectURL:          productReq.RedirectURL,
		PayLimitList:         PayLimitList,
		ExpireTime:           productReq.ExpireTime,
		Extra:                nil,
		IPAddress:            conv.StringPtr(productReq.IPAddress),
		CallbackExtra:        "",
		PayOrderNo:           nil,
		Currency:             nil,
		MerchantInfo: &payment.MerchantInfo{
			MerchantID: productReq.MerchantID,
			AppID:      productReq.AppID,
		},
	}
	return serviceReq, nil
}

func (e *GuaranteePayExecution) GetFweFinanceAccount(ctx context.Context) (*shop.FinanceAccount, *errdef.BizErr) {
	var (
		fweOrder = e.GetOrder().FweOrder
		bizReq   = e.bizReq
	)

	if fweOrder.SellerExtra == nil {
		bizErr := errdef.NewParamsErr("seller info nil")
		logs.CtxWarn(ctx, "[GetSellAccountShop] err=%s", bizErr.Error())
		return nil, bizErr
	}
	sellerInfo, bizErr := packer.CommonTradeSubjectDeserialize(fweOrder.SellerID, *fweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[GetSellAccountShop] err=%s", bizErr.Error())
		return nil, bizErr
	}
	if sellerInfo == nil || sellerInfo.FweMerchant == nil || sellerInfo.FweMerchant.FweAccountID == "" {
		bizErr = errdef.NewParamsErr("fwe account id is nil")
		logs.CtxWarn(ctx, "[GetSellAccountShop] err=%s", bizErr.Error())
		return nil, bizErr
	}

	account, bizErr := service.NewAccountShop().GetFinanceAccountOne(ctx, sellerInfo.FweMerchant.FweAccountID, bizReq.MerchantID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[GetSellAccountShop] GetFinanceAccountOne failed  err=%s", bizErr.Error())
		return nil, bizErr
	}

	return account, nil

}

func (e *GuaranteePayExecution) GetBizReq() execution_common.GuaranteePayReq {
	return e.bizReq
}
