package common

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type ActionExecution struct {
	*executor.ActionBaseExecution
}

// Deprecated:  please use NewUpdateOrderFireExecutionV2
func NewActionExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	return &ActionExecution{
		ActionBaseExecution: executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq),
			nil, nil, options...),
	}
}

func (execution *ActionExecution) Process(ctx context.Context) error {
	return nil
}
