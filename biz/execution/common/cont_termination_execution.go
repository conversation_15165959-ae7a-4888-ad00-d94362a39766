package common

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
)

type ContTerminationExecution struct {
	*executor.ActionBaseExecution
	bizReq execution_common.ContTerminationReq
	bizRsp execution_common.ContTerminationRsp
}

func NewContTerminationExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &ContTerminationExecution{}
	req, ok := actionReq.(*engine.ActionOrderReq)
	if !ok {
		logs.CtxError(ctx, "req is not ActionOrderReq")
	}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, &t.bizReq)
	return t
}

func (e *ContTerminationExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		req          = e.GetActionOrderReq()
		bizReq       = e.bizReq
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	// 合同
	bizErr = service.NewContractService().CancelContract(ctx, &service_model.ContractCancelParam{
		BizScene:   req.Identity.BizScene,
		TenantType: int32(req.Identity.TenantType),
		OrderID:    orderID,
		ContType:   bizReq.ContType,
		Operator: &core.Operator{
			OperatorID:   conv.StrToInt64(req.Operator.OperatorID, 0),
			OperatorName: req.Operator.OperatorName,
		},
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.ContTerminationRsp{}

	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *ContTerminationExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}
