package common

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"context"
)

type OfflinePayExecution struct {
	*executor.ActionBaseExecution
	bizReq execution_common.OfflinePayReq
	bizRsp execution_common.OfflinePayRsp
}

func NewOfflinePayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &OfflinePayExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.bizReq)
	return e
}

func (e *OfflinePayExecution) CheckParams(ctx context.Context) error {
	if e.bizReq.FinanceOrderType == 0 || e.bizReq.Amount <= 0 || e.bizReq.CheckData == nil {
		return errdef.NewParamsErr("必传参数为空")
	}
	checkData := e.bizReq.CheckData
	if checkData.ReceiveAccountNo == "" || checkData.RecevieAccountName == "" || checkData.BuyerName == "" {
		return errdef.NewParamsErr("线下转账校验参数为空")
	}

	return nil
}

func (e *OfflinePayExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		serviceReq   *payment.CreateOfflinePayReq
		payNo        string
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	serviceReq, bizErr = e.buildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[GuaranteePayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	payNo, bizErr = service.NewTradePayment().CreateOfflinePay(ctx, serviceReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[GuaranteePayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.OfflinePayRsp{
		PayOrderNo: payNo,
	}
	return nil
}

func (e *OfflinePayExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *OfflinePayExecution) buildReq(ctx context.Context) (*payment.CreateOfflinePayReq, *errdef.BizErr) {
	var (
		productReq = e.bizReq
		order      = e.GetOrder()
		fweOrder   = order.FweOrder
		bizErr     *errdef.BizErr
	)

	financeOrder := packer.FinanceGetByType(order.FinanceList, productReq.FinanceOrderType)
	if financeOrder == nil {
		bizErr = errdef.NewParamsErr("no finance order")
		return nil, bizErr
	}

	serviceReq := &payment.CreateOfflinePayReq{
		Identity:           e.GetBizIdentity(),
		OrderID:            fweOrder.OrderID,
		FinanceType:        productReq.FinanceOrderType,
		Currency:           nil,
		Amount:             productReq.Amount,
		CheckData:          productReq.CheckData,
		Extra:              nil,
		CallbackEvent:      utils.MakeCallbackEvent(productReq.CallbackAction),
		CallbackExtra:      "",
		CloseCallbackEvent: utils.MakeCallbackEvent(productReq.TimeoutAction),
		Base:               base.NewBase(),
	}

	return serviceReq, nil
}
