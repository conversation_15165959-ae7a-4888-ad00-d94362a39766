package common

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"errors"
)

type UpdateFailFinanceOrderExecution struct {
	*executor.StaticBaseExecution
	financeList execution_common.CreateFinanceReq
}

func NewUpdateFailFinanceOrderExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &UpdateFailFinanceOrderExecution{}
	t.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.financeList)
	return t
}

func (e *UpdateFailFinanceOrderExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.StaticBaseExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}
	return nil
}

func (e *UpdateFailFinanceOrderExecution) Process(ctx context.Context) error {
	var (
		bizErr      *errdef.BizErr
		financeList = e.financeList
		actionReq   = e.GetActionOrderReq()
		orderID     = actionReq.GetOrderID()
		tagService  = service.NewTagService()
	)
	// 更新资金单
	if len(financeList.FinanceList) != 1 {
		bizErr = errdef.NewBizErr(errdef.ParamErr, errors.New("只能更新一个资金单"), "只能更新一个资金单")
		return bizErr
	}
	financeInfo := financeList.FinanceList[0]
	financeOrderType := financeInfo.FinanceOrderType
	orderId := financeInfo.OrderID
	if orderId == "" || financeOrderType <= 0 {
		bizErr = errdef.NewBizErr(errdef.ParamErr, errors.New("资金单类型错误"), "资金单类型错误")
		return bizErr
	}
	// 校验状态
	var matchFinanceInfo *db_model.FFinanceOrder
	// 目前只能更新出款单
	for _, dbFinanceInfo := range e.GetOrder().WithdrawFinanceList {
		if dbFinanceInfo.FinanceOrderType == financeOrderType && dbFinanceInfo.OrderID == orderId {
			matchFinanceInfo = dbFinanceInfo
			break
		}
	}
	if matchFinanceInfo == nil || matchFinanceInfo.Status != int32(fwe_trade_common.FinanceStatus_Fail) {
		bizErr = errdef.NewBizErr(errdef.ParamErr, errors.New("没有匹配的资金单"), "没有匹配的资金单")
		return bizErr
	}
	params := &service_model.UpdateFinanceParams{
		UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_NotHandle)),
	}
	bizErr = service.NewFinanceOrderService().UpdateV2(ctx, matchFinanceInfo.FinanceOrderID, params)
	if bizErr != nil {
		return bizErr
	}

	if len(financeList.Tag) > 0 || len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(financeList.Tag) > 0 {
			for key, value := range financeList.Tag {
				tagMap[key] = value
			}
		}
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		_, bizErr = tagService.UpdateTag(ctx, orderID, e.GetOrder().FweOrder.BizScene, tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}
	return nil
}

func (e *UpdateFailFinanceOrderExecution) PostProcess(ctx context.Context) error {
	if err := e.StaticBaseExecution.PostProcess(ctx); err != nil {
		return err
	}
	return nil
}
