package common

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
)

type RefundExecution struct {
	*executor.ActionBaseExecution
	bizReq execution_common.RefundReq
	bizRsp execution_common.RefundRsp
}

// NewRefundExecution 标准退款
func NewRefundExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &RefundExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.bizReq)
	return e
}

func (e *RefundExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.bizReq
	if bizReq.RefundList == nil || len(bizReq.RefundList) == 0 {
		bizErr := errdef.NewParamsErr("RefundList 参数错误")
		return bizErr
	}
	for _, refund := range bizReq.RefundList {
		if refund.Amount <= 0 {
			bizErr := errdef.NewParamsErr("RefundList.amount 参数错误")
			return bizErr
		}
	}
	return nil
}

func (e *RefundExecution) Process(ctx context.Context) error {

	var (
		cashRefundReq *payment.MergeRefundReq
		mergeRefundNo string
		bizErr        *errdef.BizErr
	)

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// refund req
	cashRefundReq, bizErr = e.buildRefundReq()
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr = service.NewTradePayment().MergeRefund(ctx, cashRefundReq)
	if bizErr != nil {
		return bizErr
	}

	e.bizRsp = execution_common.RefundRsp{
		MergeRefundNo: mergeRefundNo,
	}
	return nil
}

func (e *RefundExecution) PostProcess(ctx context.Context) error {
	var (
		orderID      = e.GetOrder().FweOrder.OrderID
		stateMachine = e.GetStateMachine()
	)
	// 更新订单tag
	if bizErr := service.NewOrderService().UpdateOrderTag(ctx, orderID, e.bizReq.Tag); bizErr != nil {
		return bizErr
	}
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	updateParams.Operator = e.GetActionOrderReq().GetOperator()
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *RefundExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *RefundExecution) buildRefundReq() (*payment.MergeRefundReq, *errdef.BizErr) {
	var (
		order    = e.GetOrder()
		orderID  = e.GetOrder().FweOrder.OrderID
		fweOrder = order.FweOrder
		bizReq   = e.bizReq
	)

	refundList := make([]*payment.SingleRefund, 0, len(bizReq.RefundList))

	for _, refund := range bizReq.RefundList {
		financeOrder := packer.FinanceGetByType(order.FinanceList, refund.FinanceOrderType)
		if financeOrder == nil {
			return nil, errdef.NewRawErr(errdef.DataErr, "找不到对应的资金单")
		}
		refundList = append(refundList, &payment.SingleRefund{
			FinanceOrderID: financeOrder.FinanceOrderID,
			Amount:         refund.Amount,
		})
	}

	serviceReq := &payment.MergeRefundReq{
		Identity:                 e.GetBizIdentity(),
		RefundList:               refundList,
		OrderID:                  orderID,
		RefundFinanceType:        bizReq.RefundType,
		OrderName:                fweOrder.OrderName,
		Reason:                   bizReq.Reason,
		Extra:                    nil,
		CallbackEvent:            utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:            "",
		IPAddress:                bizReq.IPAddress,
		NeedConfirmOfflineRefund: conv.BoolDefault(bizReq.NeedConfirmOfflineRefund, false),
	}
	return serviceReq, nil
}
