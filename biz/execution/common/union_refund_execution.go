package common

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metrics"
	"code.byted.org/motor/bfsm"
	sdkUtils "code.byted.org/motor/fwe_trade_common/statemachine/utils"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/fee"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
)

// UnionRefundExecution 云直通（with rule）
type UnionRefundExecution struct {
	*executor.ActionBaseExecution
	BizReq execution_common.RefundReq
	BizRsp execution_common.RefundRsp
	conf   *model.CommonConf

	FeeRecordId  string
	RefundAmount int64
	RefundList   []*fee.SingleRefund
	RefundMode   fwe_trade_common.RefundMode
}

// NewUnionRefundExecution 标准退款
func NewUnionRefundExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &UnionRefundExecution{conf: new(model.CommonConf)}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), e.conf, &e.BizReq)
	return e
}

func NewUnionRefundBaseExecution(ctx context.Context, actionReq interface{}) *UnionRefundExecution {
	e := &UnionRefundExecution{conf: new(model.CommonConf)}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), e.conf, &e.BizReq)
	return e
}

func packRefundMode(feeRule *fee.FeeRule) fwe_trade_common.RefundMode {
	var refundMode = fwe_trade_common.RefundMode_Normal
	if feeRule != nil && feeRule.RuleAttribute != nil && feeRule.RuleAttribute.AfterSettleRefund {
		refundMode = fwe_trade_common.RefundMode_Settled
	}
	return refundMode
}

func (e *UnionRefundExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}
	var (
		bizReq          = e.BizReq
		order           = e.GetOrder()
		orderId         = order.FweOrder.OrderID
		operator        = e.GetActionOrderReq().Operator
		tradeFeeService = service.NewTradeFeeService()
	)
	// 幂等判断
	refundFinanceOrder := packer.FinanceGetByType(order.RefundFinanceList, bizReq.GetRefundType())
	if refundFinanceOrder != nil && refundFinanceOrder.FeeRecordID != "" {
		feeRecord, bizErr := tradeFeeService.GetOneTradeFeeRecord(ctx, refundFinanceOrder.FeeRecordID)
		if bizErr != nil {
			logs.CtxError(ctx, "[UnionRefundExecution-PreProcess] GetOneTradeFeeRecord", bizErr.Error())
			return bizErr
		}
		e.setRefundData(feeRecord.RecordID, feeRecord.RefundList, packRefundMode(feeRecord.Rule))
		return nil
	}
	// 直接退款
	refundList := make([]*fee.SingleRefund, 0, len(bizReq.RefundList))
	for _, refund := range bizReq.RefundList {
		financeOrder := packer.FinanceGetByType(order.FinanceList, refund.FinanceOrderType)
		if financeOrder == nil {
			return errdef.NewRawErr(errdef.DataErr, "找不到对应的资金单")
		}
		refundList = append(refundList, &fee.SingleRefund{
			FinanceOrderID:   financeOrder.FinanceOrderID,
			Amount:           refund.Amount,
			YztOfflineAmount: refund.YztOfflineAmount,
			JstRefundList:    refund.JstRefundList,
		})
	}
	if bizReq.GetRuleID() == int64(0) {
		e.setRefundData("", refundList, fwe_trade_common.RefundMode_Normal)
		return nil
	}
	// 使用规则退款
	chargeRefundResp, bizErr := tradeFeeService.ChargeRefund(ctx, &fee.ChargeRefundReq{
		OrderID:    orderId,
		RuleID:     bizReq.GetRuleID(),
		RefundList: refundList,
		Operator:   operator.OperatorName,
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionRefundExecution-PreProcess] ChargeRefund error, err = %v", bizErr.Error())
		return bizErr
	}
	e.setRefundData(chargeRefundResp.RecordID, chargeRefundResp.RefundList, chargeRefundResp.RefundMode)
	// 退款费项校验
	if len(bizReq.FeeItemList) != 0 {
		feeAmount := int64(0)
		for _, feeItem := range bizReq.FeeItemList {
			feeAmount += feeItem.Amount
		}
		if feeAmount > int64(0) && feeAmount != e.RefundAmount {
			bizErr := errdef.NewParamsErr("退款费项参数错误")
			logs.CtxError(ctx, "[UnionRefundExecution-PreProcess] feeAmount != RefundAmount, err = %v", bizErr.Error())
			return bizErr
		}
	}
	return nil
}

func (e *UnionRefundExecution) Process(ctx context.Context) error {

	var (
		mergeRefundNo  string
		bizErr         *errdef.BizErr
		refundAmount   = e.RefundAmount
		actionOrderReq = e.GetActionOrderReq()
	)
	// 跳转状态机
	conditions := sdkUtils.BuildRefundCondition(refundAmount)
	if actionOrderReq.ActionCondition != "" {
		conditions, _ = sdkUtils.AppendBizReqParams(ctx, conditions, actionOrderReq.ActionCondition)
	}
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[UnionRefundExecution-process] fire fsm failed, err=%+v", err.Error())
		return err
	}
	// 退款金额为0，直接结束
	if refundAmount == int64(0) {
		logs.CtxInfo(ctx, "[UnionRefundExecution-process] refund amount is 0")
		tags := []metrics.T{
			{Name: consts.TagBizScene, Value: fmt.Sprintf("%d", e.GetBizIdentity().BizScene)},
			{Name: consts.TagStatus, Value: fmt.Sprintf("%d", e.GetStateMachine().CurState())},
		}
		utils.EmitCounter(consts.MetricRefundAmountIsZero, 1, tags...)
		return nil
	}
	// refund req
	refundParam, bizErr := e.BuildRefundReq(ctx)
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr = service.NewUnionRefundService().UnionRefund(ctx, refundParam)
	if bizErr != nil {
		return bizErr
	}

	e.BizRsp = execution_common.RefundRsp{
		MergeRefundNo: mergeRefundNo,
	}
	return nil
}

func (e *UnionRefundExecution) PostProcess(ctx context.Context) error {
	var (
		orderID      = e.GetOrder().FweOrder.OrderID
		stateMachine = e.GetStateMachine()
	)
	// 更新订单tag
	if _, bizErr := service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), e.BizReq.Tag); bizErr != nil {
		return bizErr
	}
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{}
	if stateMachine.GetOriginalState() != stateMachine.CurState() { // 如果主流程有状态流转
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	}

	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	updateParams.Operator = e.GetActionOrderReq().GetOperator()
	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *UnionRefundExecution) Result() interface{} {
	str, _ := utils.Marshal(e.BizRsp)
	return str
}

func (e *UnionRefundExecution) BuildRefundReq(ctx context.Context) (*service_model.UnionRefundParam, *errdef.BizErr) {
	var (
		order        = e.GetOrder()
		fweOrder     = order.FweOrder
		orderId      = fweOrder.OrderID
		bizReq       = e.BizReq
		refundList   = packer.FeeRefunds2Payment(ctx, e.RefundList)
		refundAmount = e.RefundAmount
		recordId     = e.FeeRecordId
	)
	if len(refundList) == 0 {
		return nil, errdef.NewParamsErr("the length of refundList is 0")
	}
	serviceReq := &payment.MergeRefundReq{
		Identity:                   e.GetBizIdentity(),
		OrderID:                    fweOrder.OrderID,
		RefundFinanceType:          bizReq.RefundType,
		OrderName:                  fweOrder.OrderName,
		NeedConfirmOfflineRefund:   conv.BoolDefault(bizReq.NeedConfirmOfflineRefund, false),
		NeedWithdraw:               bizReq.NeedWithdraw,
		Reason:                     bizReq.Reason,
		IPAddress:                  bizReq.IPAddress,
		Extra:                      nil,
		RefundSettleInfo:           bizReq.RefundSettleInfo,
		YztOfflineRefundSettleInfo: bizReq.YztOfflineRefundSettleInfo,
		LifeRefundParam:            bizReq.LifeRefundParam,
		RefundMode:                 e.RefundMode,
		OutID:                      utils.MakeRefundFinanceOutID(orderId, bizReq.GetRefundType()),
		RefundListV2:               refundList,
		OrderType:                  fwe_trade_common.OrderType_Trade,
		CallbackEvent:              utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:              "",
	}
	refundParam := &service_model.UnionRefundParam{
		OrderID:        orderId,
		OrderName:      fweOrder.OrderName,
		RefundType:     bizReq.RefundType,
		RefundAmount:   refundAmount,
		MergeRefundReq: serviceReq,
		FeeRecordID:    recordId,
		FeeItemList:    bizReq.FeeItemList,
		DeductItemList: bizReq.DeductItemList,
	}
	return refundParam, nil
}

func (e *UnionRefundExecution) getRefundAmount(singleRefunds []*fee.SingleRefund) int64 {
	res := int64(0)
	if len(singleRefunds) == 0 {
		return 0
	}
	for _, refund := range singleRefunds {
		res += refund.Amount
		res += refund.YztOfflineAmount
	}
	return res
}

func (e *UnionRefundExecution) setRefundData(recordID string, refundList []*fee.SingleRefund, refundMode fwe_trade_common.RefundMode) {
	e.FeeRecordId = recordID
	e.RefundList = refundList
	e.RefundAmount = e.getRefundAmount(refundList)
	e.RefundMode = refundMode
}
