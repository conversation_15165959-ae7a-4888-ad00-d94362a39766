package common

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
)

type ContSignExecution struct {
	*executor.ActionBaseExecution
	conf   model.CommonConf
	bizReq execution_common.ContSignReq
	bizRsp execution_common.ContSignRsp
}

func NewContSignExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &ContSignExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &t.conf, &t.bizReq)
	return t
}

func (e *ContSignExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[ContSignExecution] PreProcess failed, err=%+v", err)
		return err
	}
	if !e.conf.CheckContStructField {
		return nil
	}
	// 校验
	param := &service.CheckContTotalAmountParam{
		TmplID:           e.bizReq.ContTmplID,
		TmplParams:       e.bizReq.ContTmplParams,
		OrderTotalAmount: e.GetOrder().FweOrder.TotalAmount, // 后续改成费项 + 规则校验
		Params:           "",
		BizScene:         e.GetBizIdentity().BizScene,
		ContType:         e.bizReq.ContType,
		OrderID:          e.GetActionOrderReq().OrderID,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckContTotalAmount(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[ContSignExecution] CheckContTotalAmount failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[ContSignExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "合同总金额和订单不一致")
	}
	return nil
}

func (e *ContSignExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		signLink     string
		contSerial   string
		sReq         *service_model.ContractCreateParam
		orderID      = e.GetActionOrderReq().GetOrderID()
		contService  = service.NewContractService()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	// 合同
	sReq, bizErr = e.buildReq(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	if len(e.bizReq.SmsConfigMap) > 0 {
		contSerial, bizErr = contService.CreateContract(ctx, sReq)
	} else {
		signLink, bizErr = contService.CreateContractWithApply(ctx, sReq)
	}
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.ContSignRsp{
		ContSerial: contSerial,
		SignLink:   signLink,
	}

	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *ContSignExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *ContSignExecution) buildReq(ctx context.Context) (sReq *service_model.ContractCreateParam, bizErr *errdef.BizErr) {

	var (
		req       = e.bizReq
		fweOrder  = e.GetOrder().FweOrder
		actionReq = e.GetActionOrderReq()
	)

	sReq = &service_model.ContractCreateParam{
		OrderID:           fweOrder.OrderID,
		TenantType:        int32(actionReq.GetIdentity().GetTenantType()),
		BizScene:          actionReq.GetIdentity().GetBizScene(),
		ContType:          req.ContType,
		OperatorID:        conv.StrToInt64(actionReq.GetOperator().GetOperatorID(), 0),
		OperatorName:      actionReq.GetOperator().GetOperatorName(),
		TmplID:            req.ContTmplID,
		NeedSignNoCert:    false,
		SmsConfigMap:      req.SmsConfigMap,
		TmplParams:        req.ContTmplParams,
		SignPartyDataList: req.SignPartyList,
		ReturnUrl:         conv.StringPtrToVal(req.RedirectURL, ""),
		InOutData: &service_model.InOutData{
			Currency: int32(core.Currency_CNY),
			TotalIn:  fweOrder.TotalAmount,
			TotalOut: fweOrder.TotalAmount,
		},
		CallbackEvent: utils.MakeCallbackEvent(req.CallbackAction),
		CallbackExtra: utils.MakeContractCallbackExtra(fweOrder.OrderID),
	}
	return
}
