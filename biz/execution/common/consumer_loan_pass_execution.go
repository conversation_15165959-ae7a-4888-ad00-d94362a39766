package common

import (
	"code.byted.org/gopkg/lang/maps"
	"context"
	"fmt"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	commonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

const (
	TagLoanDetail = "infra_loan_detail"
)

type ConsumerLoanPassExecution struct {
	*executor.ActionBaseExecution
	bizReq *execution_common.AgreementPayReq
	conf   *model.CommonConf
}

func NewConsumerLoanPassExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &ConsumerLoanPassExecution{
		bizReq: new(execution_common.AgreementPayReq),
		conf:   new(model.CommonConf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.bizReq)
	return e
}

func NewAgreementPayBaseExecution(ctx context.Context, rpcReq interface{}) *ConsumerLoanPassExecution {
	e := &ConsumerLoanPassExecution{
		bizReq: new(execution_common.AgreementPayReq),
		conf:   new(model.CommonConf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.bizReq)
	return e
}

func (e *ConsumerLoanPassExecution) SetCallbackAction(callbackAction string) error {
	if callbackAction == "" {
		return nil
	}
	e.bizReq.CallbackAction = callbackAction
	return nil
}

func (e *ConsumerLoanPassExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditionMap, err := e.parseCondition(ctx)
	if err != nil {
		return err
	}
	bizErr := e.FireWithCondition(ctx, conditionMap)
	if bizErr != nil {
		return bizErr
	}
	return nil
}

func (e *ConsumerLoanPassExecution) Process(ctx context.Context) error {
	var (
		req            = e.GetActionOrderReq()
		order          = e.GetOrder()
		agreeMerchant  *model.AgreementMerchantInfo
		tagNewMap      map[string]string
		callbackAction = e.bizReq.CallbackAction
		bizErr         *errdef.BizErr
	)

	// 合并tag
	if e.bizReq.Tag == nil {
		e.bizReq.Tag = make(map[string]string)
	}
	tagNewMap = maps.MergeStrStrCopy(e.GetOrder().TagMap, e.bizReq.Tag)
	loanDetailStr, exist := tagNewMap[TagLoanDetail]
	if !exist {
		logs.CtxWarn(ctx, "[ConsumerLoanPassExecution.Process] order_tag infra_loan_detail is empty, orderID=%s", req.OrderID)
		return errdef.NewRawErr(errdef.DataErr, "贷款详情不存在")
	}

	var loanDetail *execution_common.ConfirmLoanData
	err := utils.Unmarshal(loanDetailStr, &loanDetail)
	if err != nil {
		logs.CtxWarn(ctx, "[ConsumerLoanPassExecution.Process] unmarshal loan_detail failed, err=%+v", err)
		return errdef.NewBizErrWithCode(errdef.DataErr, err)
	}
	// 测试订单资金风险检查
	if bizErr = utils.CheckFundRiskOfAmount(ctx, order.FweOrder.IsTest == int32(1), loanDetail.GetAmount(), e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[ConsumerLoanPassExecution.Process] CheckFundRiskOfAmount failed, err=%+v", bizErr.Error())
		return bizErr
	}

	// 资金安全校验
	if e.conf.CheckContStructField {
		req := e.GetActionOrderReq()
		param := &service.CheckAmountByContParam{
			OrderID:                   req.OrderID,
			FinanceOrderType:          commonConsts.FinanceLoan.Value(),
			Amount:                    loanDetail.Amount,
			AllowIncomeAmountOverflow: e.conf.CheckContOptionAllowIncomeAmountOverflow,
		}
		pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
		if bizErr != nil {
			logs.CtxError(ctx, "[ConsumerLoanPassExecution.Process] CheckAmountByCont failed, err=%s", bizErr.Error())
			return bizErr
		}
		if !pass {
			logs.CtxError(ctx, "[ConsumerLoanPassExecution.Process] safe check not pass, blockMsg=%s", blockMsg)
			return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "合同资金安全校验未通过")
		}
	}

	// 先取配置中的，兼容历史订单
	agreeMerchant = e.conf.AgreementMerchant
	// 后取tag中的，新订单逻辑
	str, exist := tagNewMap[WithholdingInfo]
	if exist {
		err = utils.Unmarshal(str, &agreeMerchant)
		if err != nil {
			logs.CtxWarn(ctx, "[ConsumerLoanPassExecution] unmarshal withholding_info failed, err=%+v", err)
			return errdef.NewBizErrWithCode(errdef.DataErr, err)
		}
	}
	if agreeMerchant == nil || agreeMerchant.CardNo == "" {
		bizErr = errdef.NewRawErr(errdef.ParamErr, "agreeMerchant param error")
		logs.CtxWarn(ctx, "[ConsumerLoanPassExecution] agreeMerchant param error, err = %v", bizErr.Error())
		return bizErr
	}
	financeReq := &payment.AgreementPayReq{
		Identity: &fwe_trade_common.BizIdentity{
			TenantType: req.Identity.TenantType,
			BizScene:   req.Identity.BizScene,
		},
		OrderID:          req.OrderID,
		MerchantID:       agreeMerchant.MerchantID,
		MerchantName:     agreeMerchant.MerchantName,
		AppID:            agreeMerchant.AppID,
		AppName:          agreeMerchant.AppName,
		UID:              agreeMerchant.Uid,
		UIDType:          agreeMerchant.UidType,
		AgreeOrderNo:     req.OrderID,
		AgreeOrderName:   fmt.Sprintf("订单-%s-协议扣款", req.OrderID),
		AgreeOrderDesc:   fmt.Sprintf("订单-%s-协议扣款", req.OrderID),
		Amount:           loanDetail.Amount,
		CardNo:           agreeMerchant.CardNo,
		InnerAgreementNo: agreeMerchant.InnerAgreementNo,
		AccountProp:      agreeMerchant.AccountProp,
		IPAddress:        "*************",
		CallbackEvent:    utils.MakeCallbackEvent(callbackAction),
		Base:             base.NewBase(),
	}
	bizErr = service.NewTradePayment().AgreementPay(ctx, financeReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[ConsumerLoanPassExecution.Process] agreement pay failed, err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *ConsumerLoanPassExecution) PostProcess(ctx context.Context) error {
	var (
		orderID      = e.GetOrder().FweOrder.OrderID
		stateMachine = e.GetStateMachine()
	)

	orderTag := e.bizReq.Tag
	if orderTag == nil {
		orderTag = make(map[string]string)
	}
	orderTag[consts.InfraConsumerLoanStatusKey] = consts.InfraConsumerLoanStatusSuc
	// 更新订单tag
	if _, bizErr := service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), orderTag); bizErr != nil {
		return bizErr
	}
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{
		WhereOrderStatus:     []int32{int32(stateMachine.GetOriginalState())},
		UpdateBeforeStatus:   conv.Int32Ptr(int32(stateMachine.GetOriginalState())),
		UpdateOrderStatus:    conv.Int32Ptr(int32(stateMachine.CurState())),
		UpdateOrderSubStatus: conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates())),
		Operator:             e.GetActionOrderReq().GetOperator(),
	}
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *ConsumerLoanPassExecution) parseCondition(ctx context.Context) (map[string]interface{}, error) {
	var (
		fireCondition = make(map[string]interface{})
	)
	if e.bizReq.GetFireCondition() == "" {
		return fireCondition, nil
	}

	err := utils.Unmarshal(e.bizReq.GetFireCondition(), &fireCondition)
	if err != nil {
		logs.CtxError(ctx, "[ConsumerLoanPassExecution] unmarshal fire_condition str failed, err=%+v", err)
		return nil, errdef.NewRawErr(errdef.ParamErr, "fireCondition格式错误")
	}
	return fireCondition, nil
}
