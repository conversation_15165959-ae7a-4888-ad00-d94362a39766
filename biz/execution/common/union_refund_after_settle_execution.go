package common

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metrics"
	"code.byted.org/motor/bfsm"
	sdkUtils "code.byted.org/motor/fwe_trade_common/statemachine/utils"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/fee"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

// UnionRefundAfterSettleExecution 易宝分账归还并退款
type UnionRefundAfterSettleExecution struct {
	*executor.ActionBaseExecution
	BizReq execution_common.RefundReq
	BizRsp execution_common.RefundRsp

	FeeRecordId  string
	RefundAmount int64
	RefundList   []*fee.SingleRefund
}

// NewUnionRefundAfterSettleExecution 标准退款
func NewUnionRefundAfterSettleExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &UnionRefundAfterSettleExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.BizReq)
	return e
}

func (e *UnionRefundAfterSettleExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}
	var (
		bizReq          = e.BizReq
		order           = e.GetOrder()
		orderId         = order.FweOrder.OrderID
		operator        = e.GetActionOrderReq().Operator
		tradeFeeService = service.NewTradeFeeService()
	)

	// 幂等判断
	refundFinanceOrder := packer.FinanceGetByType(order.RefundFinanceList, bizReq.GetRefundType())
	if refundFinanceOrder != nil && refundFinanceOrder.FeeRecordID != "" {
		feeRecord, bizErr := tradeFeeService.GetOneTradeFeeRecord(ctx, refundFinanceOrder.FeeRecordID)
		if bizErr != nil {
			logs.CtxError(ctx, "[UnionRefundAfterSettleExecution-PreProcess] GetOneTradeFeeRecord", bizErr.Error())
			return bizErr
		}
		e.setRefundData(feeRecord.RecordID, feeRecord.RefundList)
		return nil
	}

	// todo 校验分账单是否存在

	// 直接退款
	refundList := make([]*fee.SingleRefund, 0, len(bizReq.RefundList))
	for _, refund := range bizReq.RefundList {
		financeOrder := packer.FinanceGetByType(order.FinanceList, refund.FinanceOrderType)
		if financeOrder == nil {
			return errdef.NewRawErr(errdef.DataErr, "找不到对应的资金单")
		}
		refundList = append(refundList, &fee.SingleRefund{
			FinanceOrderID: financeOrder.FinanceOrderID,
			Amount:         refund.Amount,
			JstRefundList:  refund.JstRefundList,
		})
	}
	if bizReq.GetRuleID() == int64(0) {
		e.setRefundData("", refundList)
		return nil
	}

	// 使用规则退款
	chargeRefundResp, bizErr := tradeFeeService.ChargeRefund(ctx, &fee.ChargeRefundReq{
		OrderID:    orderId,
		RuleID:     bizReq.GetRuleID(),
		RefundList: refundList,
		Operator:   operator.OperatorName,
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionRefundAfterSettleExecution-PreProcess] ChargeRefund error, err = %v", bizErr.Error())
		return bizErr
	}
	e.setRefundData(chargeRefundResp.RecordID, chargeRefundResp.RefundList)
	// 退款费项校验
	if len(bizReq.FeeItemList) != 0 {
		feeAmount := int64(0)
		for _, feeItem := range bizReq.FeeItemList {
			feeAmount += feeItem.Amount
		}
		if feeAmount > int64(0) && feeAmount != e.RefundAmount {
			bizErr := errdef.NewParamsErr("退款费项参数错误")
			logs.CtxError(ctx, "[UnionRefundAfterSettleExecution-PreProcess] feeAmount != RefundAmount, err = %v", bizErr.Error())
			return bizErr
		}
	}
	return nil
}

func (e *UnionRefundAfterSettleExecution) Process(ctx context.Context) error {

	var (
		mergeRefundNo string
		bizErr        *errdef.BizErr
		refundAmount  = e.RefundAmount
	)
	// 跳转状态机
	conditions := sdkUtils.BuildRefundCondition(refundAmount)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[UnionRefundAfterSettleExecution-process] fire fsm failed, err=%+v", err.Error())
		return err
	}
	// 退款金额为0，直接结束
	if refundAmount == int64(0) {
		logs.CtxInfo(ctx, "[UnionRefundAfterSettleExecution-process] refund amount is 0")
		tags := []metrics.T{
			{Name: consts.TagBizScene, Value: fmt.Sprintf("%d", e.GetBizIdentity().BizScene)},
			{Name: consts.TagStatus, Value: fmt.Sprintf("%d", e.GetStateMachine().CurState())},
		}
		utils.EmitCounter(consts.MetricRefundAmountIsZero, 1, tags...)
		return nil
	}

	// refund req
	refundParam, bizErr := e.BuildRefundReq(ctx)
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr = service.NewUnionRefundAfterSettleService().UnionRefundAfterSettle(ctx, refundParam)
	if bizErr != nil {
		return bizErr
	}

	e.BizRsp = execution_common.RefundRsp{
		MergeRefundNo: mergeRefundNo,
	}
	return nil
}

func (e *UnionRefundAfterSettleExecution) PostProcess(ctx context.Context) error {
	var (
		orderID      = e.GetOrder().FweOrder.OrderID
		stateMachine = e.GetStateMachine()
	)
	// 更新订单tag
	if _, bizErr := service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), e.BizReq.Tag); bizErr != nil {
		return bizErr
	}
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{}
	if stateMachine.GetOriginalState() != stateMachine.CurState() { // 如果主流程有状态流转
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	}

	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	updateParams.Operator = e.GetActionOrderReq().GetOperator()
	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *UnionRefundAfterSettleExecution) Result() interface{} {
	str, _ := utils.Marshal(e.BizRsp)
	return str
}

func (e *UnionRefundAfterSettleExecution) BuildRefundReq(ctx context.Context) (*service_model.UnionRefundAfterSettleParam, *errdef.BizErr) {
	var (
		order        = e.GetOrder()
		fweOrder     = order.FweOrder
		orderId      = fweOrder.OrderID
		bizReq       = e.BizReq
		refundList   = packer.FeeRefunds2Payment(ctx, e.RefundList)
		refundAmount = e.RefundAmount
		recordId     = e.FeeRecordId
	)
	if len(refundList) == 0 {
		return nil, errdef.NewParamsErr("the length of refundList is 0")
	}
	serviceReq := &payment.MergeRefundAfterSettleReq{
		Identity:      e.GetBizIdentity(),
		OrderID:       fweOrder.OrderID,
		OrderName:     fweOrder.OrderName,
		Reason:        bizReq.Reason,
		IPAddress:     bizReq.IPAddress,
		Extra:         nil,
		OutID:         utils.MakeRefundFinanceOutID(orderId, bizReq.GetRefundType()),
		RefundListV2:  refundList,
		OrderType:     fwe_trade_common.OrderType_Trade,
		CallbackEvent: utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra: "",
		Base:          nil,
	}
	refundParam := &service_model.UnionRefundAfterSettleParam{
		OrderID:        orderId,
		OrderName:      fweOrder.OrderName,
		RefundType:     bizReq.RefundType,
		RefundAmount:   refundAmount,
		Req:            serviceReq,
		FeeRecordID:    recordId,
		FeeItemList:    bizReq.FeeItemList,
		DeductItemList: bizReq.DeductItemList,
	}
	return refundParam, nil
}

func (e *UnionRefundAfterSettleExecution) getRefundAmount(singleRefunds []*fee.SingleRefund) int64 {
	res := int64(0)
	if len(singleRefunds) == 0 {
		return 0
	}
	for _, refund := range singleRefunds {
		res += refund.Amount
	}
	return res
}

func (e *UnionRefundAfterSettleExecution) setRefundData(recordID string, refundList []*fee.SingleRefund) {
	e.FeeRecordId = recordID
	e.RefundList = refundList
	e.RefundAmount = e.getRefundAmount(refundList)
}
