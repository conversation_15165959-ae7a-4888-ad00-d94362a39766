package common

import (
	"context"
	"sort"
	"strings"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type settleExecution struct {
	*executor.ActionBaseExecution
	bizReq execution_common.SettleReq
	bizRsp execution_common.SettleRsp
}

func NewSettleExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &settleExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.bizReq)
	return e
}

func (e *settleExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		fweOrder     = e.GetOrder().FweOrder
		splitInfo    = e.bizReq.SplitInfo
		merchantID   = e.GetOrder().FinanceList[0].MerchantID
		shopService  = service.NewAccountShop()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	if int32(e.GetBizIdentity().TenantType) != fweOrder.TenantType || e.GetBizIdentity().BizScene != fweOrder.BizScene {
		bizErr = errdef.NewParamsErr("Identity 与db不一致")
		return bizErr
	}
	if splitInfo == nil || len(splitInfo.Detail) == 0 {
		logs.CtxWarn(ctx, "[settleExecution] data error orderID = %+v", orderID)
		return errdef.NewRawErr(errdef.DataErr, "splitInfo error ")
	}
	fweAccountIDs := make([]string, 0)
	for _, info := range splitInfo.Detail {
		// 找四轮id
		if info.SplitUIDType == fwe_trade_common.SplitUIDType_Shop {
			fweAccountIDs = append(fweAccountIDs, info.SplitUID)
		}
	}
	shopAccountMap, _, bizErr := shopService.MGetFinanceAccount(ctx, fweAccountIDs, merchantID)
	if bizErr != nil {
		logs.CtxError(ctx, "[settleExecution] MGetFinanceAccount error ,err = %+v", bizErr)
		return bizErr
	}
	if len(shopAccountMap) != len(fweAccountIDs) {
		logs.CtxError(ctx, "[settleExecution] MGetFinanceAccount error ,fweAccountIDs = %+v", fweAccountIDs)
		return errdef.NewRawErr(errdef.AccountShopErr, "data error")
	}
	// 转化uid
	paymentSplit, bizErr := packer.SplitInfoCommonList2PaymentList(splitInfo, shopAccountMap)
	if bizErr != nil {
		logs.CtxError(ctx, "[settleExecution] splitInfo packer error ,err = %+v", bizErr)
		return bizErr
	}
	settleV2Req, bizErr := e.buildReq(paymentSplit, e.bizReq.FinanceIDList)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[settleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	_, bizErr = service.NewTradePayment().MergeSettleV2(ctx, settleV2Req)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[settleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.SettleRsp{
		MergeSettleNo: "",
	}
	return nil
}

func (e *settleExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *settleExecution) buildReq(splitInfo []*payment.SplitInfo, financeList []string) (*payment.MergeSettleV2Req, *errdef.BizErr) {
	var (
		productReq = e.bizReq
		actionReq  = e.GetActionOrderReq()
		orderID    = actionReq.GetOrderID()
		order      = e.GetOrder()
	)
	if len(financeList) == 0 {
		_, financeList = e.groupFinanceOrderIDs(order.FinanceList)
	}

	serviceReq := &payment.MergeSettleV2Req{
		Identity:          e.GetBizIdentity(),
		OrderID:           orderID,
		SettleFinanceType: productReq.SettleType,
		FinanceList:       financeList,
		SettleDesc:        productReq.Reason,
		SplitList:         splitInfo,
		SubsidyList:       nil,
		IPAddress:         conv.StringPtr(productReq.IPAddress),
		BizExtra:          nil,
		IsOmitPosFee:      conv.BoolPtr(false),
		IsAutoWithdraw:    conv.BoolPtr(false),
		CallbackEvent:     utils.MakeCallbackEvent(productReq.CallbackAction),
		CallbackExtra:     "",
	}
	return serviceReq, nil
}

func (e *settleExecution) groupFinanceOrderIDs(financeOrders []*db_model.FFinanceOrder) (string, []string) {
	if len(financeOrders) == 0 {
		return "", nil
	}
	list := make([]string, 0)
	for _, v := range financeOrders {
		if v.Amount <= 0 {
			continue
		}
		list = append(list, v.FinanceOrderID)
	}

	sort.Strings(list)

	return strings.Join(list, "-"), list
}
