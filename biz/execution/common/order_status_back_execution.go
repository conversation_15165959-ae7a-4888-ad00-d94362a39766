package common

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/gopkg/tools"
	"context"
)

type OrderStatusRollbackExecution struct {
	*executor.ActionBaseExecution
}

func NewOrderStatusRollbackExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &OrderStatusRollbackExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, nil)
	return t
}

func NewOrderStatusRollbackBaseExecution(ctx context.Context, sourceReq interface{}) *OrderStatusRollbackExecution {
	exe := &OrderStatusRollbackExecution{}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, sourceReq.(*engine.ActionOrderReq), nil, nil)
	return exe
}

func (e *OrderStatusRollbackExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		return err
	}

	// 解析状态机条件
	var fireCondition = make(map[string]interface{})

	fireCondition["beforeStatus"] = e.GetOrder().FweOrder.BeforeStatus
	bizErr := e.FireWithCondition(ctx, fireCondition)
	if bizErr != nil {
		logs.CtxError(ctx, "[OrderStatusRollbackExecution] fire failed, err=%s, fireCondition=%s",
			bizErr.Error(), tools.GetLogStr(fireCondition))
		return bizErr
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}
