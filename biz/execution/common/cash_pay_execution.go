package common

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
)

type CashierPayExecution struct {
	*executor.ActionBaseExecution
	bizReq execution_common.CashPayReq
	bizRsp execution_common.CashPayRsp
	conf   model.CommonConf
}

func NewCashierPayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &CashierPayExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &t.conf, &t.bizReq)
	return t
}

func (e *CashierPayExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.bizReq
	if bizReq.MerchantID == "" || bizReq.AppID == "" {
		return errdef.NewParamsErr("Merchant参数错误")
	}
	return nil
}

func (e *CashierPayExecution) PreProcess(ctx context.Context) error {
	var (
		bizReq = e.bizReq
	)

	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}
	// 判断是否做合同金额校验
	if !e.conf.CheckContStructField {
		return nil
	}
	// 校验
	param := &service.CheckAmountByContParam{
		OrderID:          e.GetActionOrderReq().OrderID,
		FinanceOrderType: bizReq.FinanceOrderType,
		Amount:           bizReq.Amount,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[WithdrawExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[WithdrawExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "支付校验不通过")
	}

	return nil
}

func (e *CashierPayExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	rpcReq, err := e.buildPayReq(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[CashierPayExecution.Process] build pay req failed, err=%+v", err)
		return err
	}

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	payOrderNo, payData, bizErr := service.NewTradePayment().CreateCashPay(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CashierPayExecution.Process] pay failed, err=%s", bizErr.Error())
		return bizErr
	}

	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.CashPayRsp{
		PayData:    payData,
		PayOrderNo: payOrderNo,
	}

	return nil
}

func (e *CashierPayExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *CashierPayExecution) buildPayReq(_ context.Context) (*payment.CreateCashPayReq, *errdef.BizErr) {
	var (
		req      = e.GetActionOrderReq()
		bizReq   = e.bizReq
		currency = payment.CurrencyType_CNY
		order    = e.GetOrder()
		finance  = packer.FinanceGetByType(order.FinanceList, bizReq.FinanceOrderType)
	)

	userId := "user_id"
	if bizReq.UserID != nil {
		userId = *bizReq.UserID
	}

	// 普通收银台支付 不支持拆单
	if finance == nil {
		return nil, errdef.NewParamsErr("未找到资金单")
	}

	if finance.Amount != bizReq.Amount {
		return nil, errdef.NewParamsErr("金额与资金单不等")
	}

	if finance.TradeType != CommonConsts.FinancePayCashier.Value() {
		return nil, errdef.NewParamsErr("不支持的资金单类型")
	}

	rpcReq := &payment.CreateCashPayReq{
		Identity:             req.Identity,
		UserID:               userId,
		OrderID:              e.GetOrder().FweOrder.OrderID,
		FinanceType:          bizReq.FinanceOrderType,
		PayOrderNo:           bizReq.PayOrderNo,
		CashierDeskType:      bizReq.CashierDeskType,
		Currency:             &currency,
		TotalAmount:          bizReq.Amount,
		ExpireTime:           bizReq.ExpireTime,
		RedirectURL:          bizReq.RedirectURL,
		IPAddress:            &bizReq.IPAddress,
		PayLimitList:         bizReq.PayLimitList,
		CallbackEvent:        utils.MakeCallbackEvent(bizReq.CallbackAction),
		TimeoutCallbackEvent: utils.MakeCallbackEvent(bizReq.TimeoutAction),
		MerchantInfo: &payment.MerchantInfo{
			MerchantID: bizReq.MerchantID,
			AppID:      bizReq.AppID,
		},
		Extra: nil,
	}

	return rpcReq, nil
}
