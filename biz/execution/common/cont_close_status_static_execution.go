package common

import (
	"context"

	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type ContCloseStatusStaticExecution struct {
	*executor.StaticBaseExecution
	req      execution_common.CloseContStatusReq
	needFire bool
}

func NewContCloseStatusStaticExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &ContCloseStatusStaticExecution{
		needFire: false,
	}
	t.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.req)
	return t
}

func (e *ContCloseStatusStaticExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		req          = e.req
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		orderService = service.NewOrderService()
		contService  = service.NewContractService()
		updateParams = &service_model.UpdateOrderParams{}
		db           = caller.DB(ctx)
	)

	// 更新合同状态
	bizErr = contService.UpdateOrderContStatus(ctx, db, orderID, req.ContType,
		fwe_trade_common.CommonStatus_Success, fwe_trade_common.CommonStatus_Closed)
	if bizErr != nil {
		logs.CtxError(ctx, "[ContCloseStatusStaticExecution] update order_cont status failed, err=%s", bizErr.Error())
		return bizErr
	}

	if len(req.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, req.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[ContCloseStatusStaticExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}
	if actionReq.Operator != nil {
		updateParams.Operator = actionReq.Operator
	}
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[ContCloseStatusStaticExecution] UpdateOrder err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
