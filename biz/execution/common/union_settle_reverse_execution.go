package common

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/fee"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"time"
)

type UnionSettleReverseExecution struct {
	*executor.ActionBaseExecution
	BizReq execution_common.SettleReq
	BizRsp execution_common.SettleRsp
	conf   *model.CommonConf

	FeeRecordID     string
	OutPayUnionNos  []string
	CommonSpiltInfo *fwe_trade_common.TradeSpiltInfo
	SettleAmount    int64
}

// NewUnionSettleReverseExecution : 退款后分账标准执行器，去除了贷款金额
func NewUnionSettleReverseExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &UnionSettleReverseExecution{conf: new(model.CommonConf)}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), e.conf, &e.BizReq)
	return e
}

func NewUnionSettleReverseBaseExecution(ctx context.Context, actionReq interface{}) *UnionSettleReverseExecution {
	e := &UnionSettleReverseExecution{conf: new(model.CommonConf)}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), e.conf, &e.BizReq)
	return e
}

func (e *UnionSettleReverseExecution) CheckParams(ctx context.Context) error {
	var (
		bizReq = e.BizReq
	)
	if bizReq.GetRuleID() == int64(0) {
		if bizReq.GetSplitInfo() == nil || len(bizReq.GetSplitInfo().Detail) == 0 {
			return errdef.NewParamsErr("GetSplitInfo error")
		}
	}

	return nil
}

func (e *UnionSettleReverseExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}
	var (
		order           = e.GetOrder()
		fweOrder        = e.GetOrder().FweOrder
		bizReq          = e.BizReq
		outPayUnionNos  []string
		operator        = e.GetActionOrderReq().Operator
		tradeFeeService = service.NewTradeFeeService()
	)
	// pre 阶段查出来
	if int32(e.GetBizIdentity().TenantType) != fweOrder.TenantType || e.GetBizIdentity().BizScene != fweOrder.BizScene {
		bizErr := errdef.NewParamsErr("Identity 与db不一致")
		return bizErr
	}

	// 幂等判断
	refundFinanceOrder := packer.FinanceGetByType(order.SettleFinanceList, bizReq.GetSettleType())
	if refundFinanceOrder != nil && refundFinanceOrder.FeeRecordID != "" {
		feeRecord, bizErr := tradeFeeService.GetOneTradeFeeRecord(ctx, refundFinanceOrder.FeeRecordID)
		if bizErr != nil {
			logs.CtxError(ctx, "[UnionRefundExecution-PreProcess] GetOneTradeFeeRecord", bizErr.Error())
			return bizErr
		}

		spiltInfo, bizErr := packer.FeeSplits2PaymentSplits(ctx, feeRecord.ChargeResultList, fweOrder, e.conf.PlatformSplitUID, e.conf.RentSplitUID, e.conf.AdvanceAccountUID)
		if bizErr != nil {
			logs.CtxError(ctx, "[UnionSettleReverseExecution-PreProcess] FeeSplits2PaymentSplits error, err = %v", bizErr.Error())
			return bizErr
		}
		e.SetSettleData(feeRecord.RecordID, spiltInfo, feeRecord.SettleFinanceIDList)
		return nil
	}

	// 准备参数
	if bizReq.GetRuleID() == int64(0) {
		financeOrders := packer.FinanceGetByTypes(e.GetOrder().FinanceList, bizReq.FinanceTypeList)
		for _, order := range financeOrders {
			outPayUnionNos = append(outPayUnionNos, order.FinanceOrderID)
		}
		// 分账信息
		e.SetSettleData("", bizReq.GetSplitInfo(), outPayUnionNos)
		return nil
	}
	// 咨询计费中心
	chargeSettleResp, bizErr := tradeFeeService.ChargeSettle(ctx, &fee.ChargeSettleReq{
		OrderID:  fweOrder.OrderID,
		RuleID:   bizReq.GetRuleID(),
		Params:   conv.StringPtr(bizReq.Params),
		Operator: operator.OperatorName,
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionSettleReverseExecution-PreProcess] ChargeSettle error, err = %v", bizErr.Error())
		return bizErr
	}
	spiltInfo, bizErr := packer.FeeSplits2PaymentSplits(ctx, chargeSettleResp.ChargeResultList, fweOrder, e.conf.PlatformSplitUID, e.conf.RentSplitUID, e.conf.AdvanceAccountUID)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionSettleReverseExecution-PreProcess] FeeSplits2PaymentSplits error, err = %v", bizErr.Error())
		return bizErr
	}
	e.SetSettleData(chargeSettleResp.RecordID, spiltInfo, chargeSettleResp.FinanceIDList)
	return nil
}

func (e *UnionSettleReverseExecution) Process(ctx context.Context) error {
	var (
		bizErr *errdef.BizErr
	)

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	// 分账
	settleParam, bizErr := e.BuildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionSettleReverseExecution] err=%s", bizErr.Error())
		return bizErr
	}

	mergeSettleNo, bizErr := service.NewUnionSettleService().UnionSettle(ctx, settleParam)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionSettleReverseExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.BizRsp = execution_common.SettleRsp{
		MergeSettleNo: mergeSettleNo,
	}
	return nil
}

func (e *UnionSettleReverseExecution) PostProcess(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		fweOrder     = e.GetOrder().FweOrder
		orderID      = fweOrder.OrderID
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
		stateMachine = e.GetStateMachine()
	)

	// 更新order
	if stateMachine.GetOriginalState() != stateMachine.CurState() {
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	}

	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))

	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	bizErr = service.NewOrderService().UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 更新tag
	_, bizErr = service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), e.BizReq.Tag)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *UnionSettleReverseExecution) Result() interface{} {
	str, _ := utils.Marshal(e.BizRsp)
	return str
}

func (e *UnionSettleReverseExecution) BuildReq(ctx context.Context) (*service_model.UnionSettleParam, *errdef.BizErr) {
	var (
		productReq     = e.BizReq
		fweOrder       = e.GetOrder().FweOrder
		outPayUnionNos = e.OutPayUnionNos
		settleAmount   = e.SettleAmount
		recordID       = e.FeeRecordID
		subsidyList    = make([]*payment.SubsidyInfo, 0)
	)
	if len(outPayUnionNos) == 0 {
		return nil, errdef.NewParamsErr("the length of outPayUnionNos is 0")
	}
	splits, bizErr := service.NewFinanceAccountService().ConvertCommonSplitInfo(ctx, e.GetBizIdentity().TenantType, e.CommonSpiltInfo)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionSettleReverseExecution-BuildReq] ConvertCommonSplitInfo error, err = %v", bizErr.Error())
		return nil, bizErr
	}

	serviceReq := &payment.MergeSettleV2Req{
		Identity:       e.GetBizIdentity(),
		OrderID:        fweOrder.OrderID,
		OrderType:      fwe_trade_common.OrderType_Trade,
		OutID:          utils.MakeSettleFinanceOutID(fweOrder.OrderID, productReq.SettleType),
		OutPayUnionNos: outPayUnionNos,
		SettleDesc:     productReq.Reason,
		SplitList:      splits,
		SubsidyList:    subsidyList,
		IPAddress:      conv.StringPtr(productReq.IPAddress),
		BizExtra:       nil,
		IsOmitPosFee:   conv.BoolPtr(false),
		IsAutoWithdraw: productReq.IsAutoWithdraw,
		CallbackEvent:  utils.MakeCallbackEvent(productReq.CallbackAction),
		CallbackExtra:  "",
	}
	settleParam := &service_model.UnionSettleParam{
		OrderID:        fweOrder.OrderID,
		OrderName:      fweOrder.OrderName,
		SettleType:     productReq.SettleType,
		SettleAmount:   settleAmount,
		MergeSettleReq: serviceReq,
		FeeRecordID:    recordID,
	}
	return settleParam, nil
}

func (e *UnionSettleReverseExecution) getSettleAmount(info *fwe_trade_common.TradeSpiltInfo) int64 {
	res := int64(0)
	if info == nil || len(info.Detail) == 0 || info.SplitMethod == fwe_trade_common.SplitMethod_ByScale {
		return res
	}
	for _, splitUnit := range info.Detail {
		res += splitUnit.Amount
	}
	return res
}

func (e *UnionSettleReverseExecution) SetSettleData(recordID string, spiltInfo *fwe_trade_common.TradeSpiltInfo, idList []string) {
	e.FeeRecordID = recordID
	e.CommonSpiltInfo = spiltInfo
	e.OutPayUnionNos = idList
	e.SettleAmount = e.getSettleAmount(spiltInfo)
}
