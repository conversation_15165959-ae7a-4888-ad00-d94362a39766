package common

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/overpass/motor_dealer_douyin_open_proxy/kitex_gen/motor/dealer/douyin_open_proxy"
	"context"
	"strconv"
)

type LifeOrderVerifyExecution struct {
	*executor.ActionBaseExecution
	conf   model.CommonConf
	bizReq execution_common.LifeVerifyReq
	bizRsp execution_common.LifeVerifyResp
}

func NewLifeOrderVerifyExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &LifeOrderVerifyExecution{}
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq, opts...)
	return e
}

// PreProcess 前置处理,其他的继承子类必须使用本类的PreProcess方法
func (e *LifeOrderVerifyExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[LifeOrderVerifyExecution] PreProcess failed, err=%+v", err)
		return err
	}
	return nil
}

func (e *LifeOrderVerifyExecution) Process(ctx context.Context) error {
	var (
		bizErr     *errdef.BizErr
		orderID    = e.GetActionOrderReq().GetOrderID()
		tagService = service.NewTagService()
		bizTag     = e.GetActionOrderReq().TagMap
		bizReq     = e.bizReq
	)
	// 构建请求
	rpcReq := &douyin_open_proxy.DouyinLifePushDeliveryReq{
		OutOrderNo:    orderID,
		ItemOrderList: packer.PackItemOrderList(bizReq.ItemOrderList),
		UseAll:        bizReq.UseAll,
		PoiInfo:       bizReq.PoiInfo,
		AppId:         bizReq.AppID,
	}
	// 请求生服核销
	results, bizErr := service.NewDouyinLifeService().PushDelivery(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[LifeOrderVerifyExecution][Process] PushDelivery err=%s", bizErr.Error())
		return bizErr
	}
	if len(bizTag) == 0 {
		bizTag = make(map[string]string)
	}
	bizTag["infra_life_verify_result"] = tools.GetLogStr(results)                   // 核销结果
	bizTag["infra_life_verify_time"] = strconv.FormatInt(results[0].VerifyTime, 10) // 毫米时间戳
	bizTag["infra_life_verify_status"] = "SUCCESS"                                  // 核销状态
	// 保存核销结果
	_, bizErr = tagService.UpdateTag(ctx, orderID, e.GetBizIdentity().BizScene, bizTag)
	if bizErr != nil {
		logs.CtxError(ctx, "[LifeOrderVerifyExecution][Process] UpdateTag err=%s", bizErr.Error())
		return bizErr
	}
	e.bizRsp = execution_common.LifeVerifyResp{
		VerifyResults: packer.PackVerifyResults(results),
	}
	return nil
}

func (e *LifeOrderVerifyExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}
