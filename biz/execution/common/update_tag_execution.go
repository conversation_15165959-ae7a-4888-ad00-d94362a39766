package common

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"context"
	"errors"
)

type UpdateOrderTagExecution struct {
	*executor.StaticBaseExecution
	updateOrder execution_common.UpdateOrderReq
}

func NewUpdateOrderTagExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &UpdateOrderTagExecution{}
	t.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.updateOrder)
	return t
}

func NewUpdateOrderTagExecutionBase(ctx context.Context, actionReq interface{}) *UpdateOrderTagExecution {
	t := &UpdateOrderTagExecution{}
	t.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.updateOrder)
	return t
}

func (e *UpdateOrderTagExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.StaticBaseExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}
	if len(e.updateOrder.Tag) <= 0 {
		return errors.New("tag数据为空")
	}
	return nil
}

func (e *UpdateOrderTagExecution) Process(ctx context.Context) error {
	var (
		bizErr      *errdef.BizErr
		updateOrder = e.updateOrder
		actionReq   = e.GetActionOrderReq()
		orderID     = actionReq.GetOrderID()
		tagService  = service.NewTagService()
	)

	if len(updateOrder.Tag) > 0 || len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(updateOrder.Tag) > 0 {
			for key, value := range updateOrder.Tag {
				tagMap[key] = value
			}
		}
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		_, bizErr = tagService.UpdateTag(ctx, orderID, e.GetOrder().FweOrder.BizScene, tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}
	return nil
}

func (e *UpdateOrderTagExecution) PostProcess(ctx context.Context) error {
	if err := e.BaseExecution.PostProcess(ctx); err != nil {
		return err
	}
	return nil
}
