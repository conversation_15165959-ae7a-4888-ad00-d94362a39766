package common

import (
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

//TagExecution tag创建或更新并更新状态
type TagExecution struct {
	*executor.ActionBaseExecution
	bizParam map[string]string
}

// Deprecated:  please use NewUpdateOrderFireExecutionV2
func NewTagExecution(ctx context.Context, req interface{}) executor.IExecution {
	t := &TagExecution{}
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req.(*engine.ActionOrderReq), nil, &t.bizParam, options...)
	return t
}

func (e *TagExecution) Process(ctx context.Context) error {
	//1.取订单
	order := e.GetOrder()

	//2。更新tag
	for key, value := range e.bizParam {
		order.TagMap[key] = value
	}
	bizErr := service.NewOrderService().UpdateOrderTag(ctx, order.FweOrder.OrderID, order.TagMap)
	if bizErr != nil {
		return bizErr
	}
	return nil
}
