package common

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
)

type WithdrawExecution struct {
	*executor.ActionBaseExecution
	conf   model.CommonConf
	bizReq execution_common.WithdrawReq
	bizRsp execution_common.WithdrawRsp
}

func NewWithdrawExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &WithdrawExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq)
	return e
}

func NewWithdrawExecutionBase(ctx context.Context, actionReq interface{}) *WithdrawExecution {
	e := &WithdrawExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq)
	return e
}

func (e *WithdrawExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[WithdrawExecution] PreProcess failed, err=%+v", err)
		return err
	}

	// 判断是否做合同金额校验
	if !e.conf.CheckContStructField {
		return nil
	}

	// 校验
	param := &service.CheckAmountByContParam{
		OrderID:          e.GetActionOrderReq().OrderID,
		FinanceOrderType: e.bizReq.FinanceOrderType,
		Amount:           e.bizReq.Amount,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[WithdrawExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[WithdrawExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "出款合同校验未通过")
	}

	return nil
}

func (e *WithdrawExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	rpcReq, err := e.buildPayReq(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[WithdrawExecution.Process] build pay req failed, err=%+v", err)
		return err
	}

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	withdrawNo, bizErr := service.NewTradePayment().WithdrawDeposit(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[WithdrawExecution.Process] pay failed, err=%s", bizErr.Error())
		return bizErr
	}

	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.WithdrawRsp{
		WithdrawOrderNo: withdrawNo,
	}

	return nil
}

func (e *WithdrawExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *WithdrawExecution) buildPayReq(_ context.Context) (*payment.WithdrawDepositReq, *errdef.BizErr) {
	var (
		req      = e.GetActionOrderReq()
		bizReq   = e.bizReq
		currency = payment.CurrencyType_CNY
		finance  = packer.FinanceGetByType(e.GetOrder().WithdrawFinanceList, bizReq.FinanceOrderType)
	)

	// 兼容资金单trade_category为0或1-pay的场景
	if finance == nil {
		finance = packer.FinanceGetByType(e.GetOrder().FinanceList, bizReq.FinanceOrderType)
	}

	if finance == nil {
		return nil, errdef.NewParamsErr("未找到资金单")
	}

	if finance.Amount != bizReq.Amount {
		return nil, errdef.NewParamsErr("金额与资金单不等")
	}

	if finance.TradeType != CommonConsts.FinanceWithdrawBank.Value() {
		return nil, errdef.NewParamsErr("不支持的资金单类型")
	}

	rpcReq := &payment.WithdrawDepositReq{
		OrderID:           req.OrderID,
		FinanceOrderID:    finance.FinanceOrderID,
		MerchantID:        bizReq.MerchantID,
		MerchantName:      bizReq.MerchantName,
		AppID:             bizReq.AppID,
		Amount:            finance.Amount,
		WithdrawType:      bizReq.WithdrawType,
		IPAddress:         bizReq.IPAddress,
		WitdhrawDesc:      bizReq.Reason,
		Mid:               nil,
		Currency:          &currency,
		BankCardInfo:      bizReq.BankCardInfo,
		CallbackEvent:     utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:     "",
		FailCallbackEvent: utils.MakeCallbackEvent(bizReq.FailCallbackAction),
		FweAccountID:      bizReq.FweAccountID,
		Extra:             nil,
		Operator:          req.GetOperator(),
		Identity:          req.GetIdentity(),
	}

	return rpcReq, nil
}

func (e *WithdrawExecution) GetBizReq() execution_common.WithdrawReq {
	return e.bizReq
}

func (e *WithdrawExecution) SetBizRsp(rsp execution_common.WithdrawRsp) {
	e.bizRsp = rsp
}
