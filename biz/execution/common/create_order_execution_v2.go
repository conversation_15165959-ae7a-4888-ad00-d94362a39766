package common

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"
	"context"
	"github.com/fatih/structs"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
)

type CreateOrderExecutionV2 struct {
	*executor.CreateBaseExecution
	result interface{}
}

// NewCreateOrderExecutionV2 创建订单并创建资金单
func NewCreateOrderExecutionV2(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &CreateOrderExecutionV2{}
	conf := new(model.CommonConf)
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), conf)
	return t
}

func (e *CreateOrderExecutionV2) Process(ctx context.Context) error {
	var (
		err          error
		bizErr       *errdef.BizErr
		stateMachine = e.GetStateMachine()
		createReq    = e.GetCreateOrderReq()
		bizScene     = createReq.GetIdentity().GetBizScene()
		productInfo  = createReq.GetProductInfo()
		financeList  []*db_model.FFinanceOrder
	)

	// 驱动状态
	err = stateMachine.Fire(ctx, statemachine.OrderCreateEt.Value(), nil)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "CreateOrderExecutionV2")
		logs.CtxError(ctx, "[CreateOrderExecutionV2] err=%s", bizErr.Error())
		return bizErr
	}

	// 扣减库存
	if createReq.GetTradeOption() != nil &&
		createReq.GetTradeOption().UseInfraProductStockStrategy {
		bizErr = service.NewProductStockService().DecrStockOne(ctx, &product_stock.DecrStockForOneReq{
			BizOrder: &product_stock.BizOrder{
				OrderId: e.GetOrderID(),
			},
			Item: &product_stock.ChangeStockItem{
				StockUnit: &product_stock.StockUnit{
					SkuId: e.GetSkuDetail().GetSkuId(),
				},
				StockNum: conv.Int64Ptr(createReq.ProductInfo.ProductQuantity),
			},
		})
		if bizErr != nil {
			logs.CtxError(ctx, "[CreateOrderExecutionV2] DecrStockOne err=%s", bizErr.Error())
			return bizErr
		}
	}

	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene,
		SmVersion:          createReq.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createReq.GetOrderName(),
		OrderDesc:          createReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductExtra:       productInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		ProductVersion:     productInfo.ProductVersion,
		TotalAmount:        createReq.TotalAmount,
		TotalPayAmount:     createReq.TotalPayAmount,
		TotalSubsidyAmount: createReq.TotalSubsidyAmount,
		TradeType:          int32(createReq.TradeType),
		TradeOption:        conv.StringPtr(utils.MarshalToStr(createReq.TradeOption)),
		BuyerID:            packer.CommonTradeSubjectIDGet(createReq.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(createReq.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createReq.IsTest),
		Creator:            createReq.GetOperator().GetOperatorID(),
		CreatorName:        createReq.GetOperator().GetOperatorName(),
		Operator:           createReq.GetOperator().GetOperatorID(),
		OperatorName:       createReq.GetOperator().GetOperatorName(),
		IdempotentID:       createReq.GetIdemID(),
	}
	if createReq.ServiceProviderInfo != nil && !structs.IsZero(createReq.ServiceProviderInfo) {
		fweOrder.ServiceProviderID = packer.CommonTradeSubjectIDGet(createReq.ServiceProviderInfo)
		fweOrder.ServiceProviderExtra = conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.ServiceProviderInfo))
	}
	if createReq.TalentInfo != nil && !structs.IsZero(createReq.TalentInfo) {
		fweOrder.TalentID = packer.CommonTradeSubjectIDGet(createReq.TalentInfo)
		fweOrder.TalentExtra = conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.TalentInfo))
	}
	// 资金单列表
	for _, financeInfo := range createReq.FinanceList {
		financeOrderID, err := utils.TryGenId(3)
		if err != nil {
			logs.CtxError(ctx, "[createOrderExecution] gen id error, err=%+v", err)
			return err
		}
		financeOrder := &db_model.FFinanceOrder{
			TenantType:       int32(e.GetBizIdentity().TenantType),
			BizScene:         e.GetBizIdentity().BizScene,
			AppID:            "",
			MerchantID:       "",
			Mid:              "",
			OrderID:          e.GetOrderID(),
			OrderName:        fweOrder.OrderName,
			TradeType:        financeInfo.TradeType,
			TradeCategory:    int32(financeInfo.TradeCategory),
			FinanceOrderID:   utils.MakeFinanceOrderID(financeOrderID, financeInfo.FinanceOrderType),
			FinanceOrderType: financeInfo.FinanceOrderType,
			Amount:           financeInfo.Amount,
			ProcessAmount:    0,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
			FeeItemDetail:    conv.StringPtr(tools.GetLogStr(financeInfo.FeeItemList)),
			LoanAmount:       financeInfo.LoanAmount,
		}
		if financeOrder.TradeCategory == 0 {
			financeOrder.TradeCategory = int32(fwe_trade_common.TradeCategory_Pay)
		}
		financeList = append(financeList, financeOrder)
	}

	order := &service_model.Order{
		FweOrder:    fweOrder,
		TagMap:      createReq.OrderTag,
		BizExtra:    createReq.Extra,
		FinanceList: financeList,
	}

	bizErr = service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecutionV2] err=%s", bizErr.Error())
		return bizErr
	}

	e.result = e.GetOrderID()
	return nil
}
