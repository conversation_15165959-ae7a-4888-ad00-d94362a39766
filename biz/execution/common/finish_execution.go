package common

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type FinishExecution struct {
	*executor.ActionBaseExecution
}

// Deprecated:  please use NewUpdateOrderFireExecutionV2
func NewFinishExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFinish})
	return &FinishExecution{
		ActionBaseExecution: executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq),
			nil, nil, opts...),
	}
}

func (execution *FinishExecution) Process(ctx context.Context) error {
	return nil
}
