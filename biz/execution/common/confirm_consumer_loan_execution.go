package common

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	commonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/base"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/motor/trade/audit"
	"context"
	"fmt"
)

const (
	// 飞书相关
	LarkAppID                = "********************"
	LarkAppSecret            = "Vd0GipsjBmNbv7DQhmeY8eBSF3s8ZmwB"
	LarkAppVerificationToken = "f19MAp2kHpCGVh1XkYlfEdQ20i3v65SL"
	LarkEncryptKey           = "PYawhWlfpv0Chf8MtlQdqLnB2Smh1kc2"

	ApproveCode   = "infra_approve_code"
	ApproveOpenId = "infra_approve_open_id"

	// 审批单字段id
	ApproveName    = "审批"
	CarVin         = "car_vin"
	TradeStructure = "trade_structure"
	BorrowerName   = "borrower_name"
	LoanAmount     = "loan_amount"
	ShopName       = "shop_name"
	BizOrderId     = "biz_order_id"
	LoanFirstName  = "先贷款后过户"
	LoanAfterName  = "先过户后贷款"
	LoanType       = "infra_loan_type" // 贷款类型 值为loan_first和loan_after
	LoanFirstStr   = "loan_first"
	LoanAfterStr   = "loan_after"

	WithholdingInfo = "infra_withholding_info"
)

type ConfirmConsumerLoanExecution struct {
	*executor.ActionBaseExecution
	bizReq *execution_common.ConfirmLoanReq
	conf   *model.CommonConf
}

func NewConfirmConsumerLoanExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &ConfirmConsumerLoanExecution{
		bizReq: new(execution_common.ConfirmLoanReq),
		conf:   new(model.CommonConf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.bizReq)
	return e
}

func (e *ConfirmConsumerLoanExecution) CheckParams(ctx context.Context) error {
	if e.conf.ApproveStarter == nil || e.conf.ApproveStarter.OpenID == "" {
		logs.CtxWarn(ctx, "[ConfirmConsumerLoanExecution.CheckParams] approve_starter not config")
		return errdef.NewParamsErr("approve_starter not config")
	}
	if e.bizReq.ConfirmLoanData == nil {
		logs.CtxWarn(ctx, "[ConfirmConsumerLoanExecution.CheckParams] approve_starter not config")
		return errdef.NewParamsErr("BizReq param is empty")
	}
	return nil
}

func (e *ConfirmConsumerLoanExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}
	if e.GetStateMachine().CurState() == statemachine.GetAfterSaleBaseState() {
		return errdef.NewRawErr(errdef.ActionNotRegister, "售后流程中不可操作确认贷款")
	}
	var bizReq = e.bizReq
	// 设置默认tag
	if bizReq.Tag == nil {
		bizReq.Tag = make(map[string]string)
	}

	conditionMap, err := e.parseCondition(ctx)
	if err != nil {
		return err
	}
	bizErr := e.FireWithCondition(ctx, conditionMap)
	if bizErr != nil {
		return bizErr
	}
	return nil
}

func (e *ConfirmConsumerLoanExecution) Process(ctx context.Context) error {
	var (
		order      = e.GetOrder()
		bizReq     = e.bizReq
		param      = e.bizReq.ConfirmLoanData
		loanAmount int64
	)
	// 1. 校验认款金额是否和创单时一致
	for _, finance := range order.FinanceList {
		loanAmount += finance.LoanAmount
	}
	if param.Amount != loanAmount {
		logs.CtxWarn(ctx, "[ConfirmConsumerLoanExecution.Process] amount is not consist with loan_amount")
		return errdef.NewParamsErr("金额和创单时不一致")
	}

	// 资金安全校验
	if e.conf.CheckContStructField {
		req := e.GetActionOrderReq()
		param := &service.CheckAmountByContParam{
			OrderID:                   req.OrderID,
			FinanceOrderType:          commonConsts.FinanceLoan.Value(),
			Amount:                    loanAmount,
			AllowIncomeAmountOverflow: e.conf.CheckContOptionAllowIncomeAmountOverflow,
		}
		pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
		if bizErr != nil {
			logs.CtxError(ctx, "[ConfirmConsumerLoanExecution.Process] CheckAmountByCont failed, err=%s", bizErr.Error())
			return bizErr
		}
		if !pass {
			logs.CtxError(ctx, "[ConfirmConsumerLoanExecution.Process] safe check not pass, blockMsg=%s", blockMsg)
			return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "合同资金安全校验未通过")
		}
	}

	// 2. 设置贷款明细
	loanDetail, err := utils.Marshal(param)
	if err != nil {
		logs.CtxWarn(ctx, "[ConfirmConsumerLoanExecution.Process] marshal failed, err=%+v", err)
		return err
	}
	bizReq.Tag[TagLoanDetail] = loanDetail

	// 3. 发送lark审批通知
	bizErr := e.SendAuditLark(ctx, loanDetail)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[ConfirmConsumerLoanExecution.Process] send audit lark failed, orderId=%s, err=%s",
			order.FweOrder.OrderID, bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *ConfirmConsumerLoanExecution) PostProcess(ctx context.Context) error {
	var (
		orderID      = e.GetOrder().FweOrder.OrderID
		stateMachine = e.GetStateMachine()
	)
	// 更新订单tag
	if _, bizErr := service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), e.bizReq.Tag); bizErr != nil {
		return bizErr
	}
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{
		WhereOrderStatus:     []int32{int32(stateMachine.GetOriginalState())},
		UpdateBeforeStatus:   conv.Int32Ptr(int32(stateMachine.GetOriginalState())),
		UpdateOrderStatus:    conv.Int32Ptr(int32(stateMachine.CurState())),
		UpdateOrderSubStatus: conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates())),
		Operator:             e.GetActionOrderReq().GetOperator(),
	}
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *ConfirmConsumerLoanExecution) parseCondition(ctx context.Context) (map[string]interface{}, error) {
	var (
		fireCondition = make(map[string]interface{})
	)
	if e.bizReq.GetFireCondition() == "" {
		return fireCondition, nil
	}

	err := utils.Unmarshal(e.bizReq.GetFireCondition(), &fireCondition)
	if err != nil {
		logs.CtxError(ctx, "[ConfirmConsumerLoanExecution] unmarshal fire_condition str failed, err=%+v", err)
		return nil, errdef.NewRawErr(errdef.ParamErr, "fireCondition格式错误")
	}
	return fireCondition, nil
}

func (e *ConfirmConsumerLoanExecution) SendAuditLark(ctx context.Context, loanDetail string) *errdef.BizErr {
	var (
		req                = e.GetActionOrderReq()
		bizReq             = e.bizReq
		param              = bizReq.ConfirmLoanData
		succCallbackAction = bizReq.SuccCallbackAction
		failCallbackAction = bizReq.FailCallbackAction
	)

	formMap := e.packAuditParamForLarkForm()
	formStr, _ := utils.Marshal(formMap)
	approveCode := e.conf.ApproveStarter.ApproveCode
	approveOpenId := e.conf.ApproveStarter.OpenID

	_, approveCodeExist := e.bizReq.Tag[ApproveCode]
	_, openExist := e.bizReq.Tag[ApproveOpenId]
	if approveCodeExist && openExist {
		approveCode = e.bizReq.Tag[ApproveCode]
		approveOpenId = e.bizReq.Tag[ApproveOpenId]
	}
	auditReq := &audit.ApplyAuditReq{
		Tenant:    int64(audit.AuditServiceTenant_FWE_TRADE),
		CreatorId: 1,
		BizType:   req.Identity.BizScene,
		OuterId:   e.makeAuditOuterID(),
		AuditName: fmt.Sprintf("%s-%s", param.FinanceName, ApproveName),
		AuditParams: []*audit.AuditParams{
			{Name: TagLoanDetail, BeforeValue: loanDetail},
		},
		Steps: []*audit.ApplyAuditStepInfo{
			{
				StepType:     int32(audit.StepTypeEnum_Or),
				StepName:     ApproveName,
				TargetSystem: audit.StepTargetSystemTypeEnum_Lark,
				LarkOuterAuditInfo: &audit.LarkOuterAuditInfo{
					ApprovalCode:      approveCode,
					Form:              formStr,
					OpenId:            approveOpenId,
					Appid:             LarkAppID,
					AppSecret:         LarkAppSecret,
					VerificationToken: LarkAppVerificationToken,
					EncryptKey:        LarkEncryptKey,
				},
			},
		},
		BizIndex1: req.OrderID,
		BizIndex2: fmt.Sprintf("%d", req.Identity.TenantType),
		BizIndex3: utils.MakeCallbackEvent(succCallbackAction),
		BizIndex4: utils.MakeCallbackEvent(failCallbackAction),
		Base:      base.NewBase(),
	}
	bizErr := service.NewAuditService().ApplyAudit(ctx, auditReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[ConfirmConsumerLoanExecution.SendAuditLark] apply audit failed, err=%+v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *ConfirmConsumerLoanExecution) makeAuditOuterID() string {
	req := e.GetActionOrderReq()
	// 返回order_id$$event_name
	return fmt.Sprintf("%s$$%s", req.OrderID, "ConfirmConsumerLoanExecution")
}

func (e *ConfirmConsumerLoanExecution) packAuditParamForLarkForm() []*sh_sell_model.AuditForm {
	var (
		order  = e.GetOrder()
		tagMap = order.TagMap
		bizReq = e.bizReq
		param  = bizReq.ConfirmLoanData
	)

	tradeStructure := LoanFirstName
	if loanType, exist := tagMap[LoanType]; exist && loanType == LoanAfterStr {
		tradeStructure = LoanAfterName
	}

	amount := param.Amount

	res := []*sh_sell_model.AuditForm{
		{ID: CarVin, Value: param.CarVin},
		{ID: BorrowerName, Value: param.BorrowerName},
		{ID: LoanAmount, Value: fmt.Sprintf("%.2f元", float64(amount)/1e2)},
		{ID: ShopName, Value: param.ShopName},
		{ID: BizOrderId, Value: param.BizOrderID},
		{ID: TradeStructure, Value: tradeStructure},
	}

	for _, m := range res {
		m.Type = "input" // 固定值
	}
	return res
}
