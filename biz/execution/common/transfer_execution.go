package common

import (
	"code.byted.org/gopkg/lang/maths"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"time"
)

type TransferExecution struct {
	*executor.ActionBaseExecution
	conf   model.CommonConf
	bizReq execution_common.TransferReq
	bizRsp execution_common.TransferResp
}

func NewTransferExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &TransferExecution{}
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq, opts...)
	return e
}

// PreProcess 前置处理,其他的继承子类必须使用本类的PreProcess方法
func (e *TransferExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[TransferExecution] PreProcess failed, err=%+v", err)
		return err
	}
	return nil
}

func (e *TransferExecution) Process(ctx context.Context) error {
	var (
		bizErr     *errdef.BizErr
		orderID    = e.GetActionOrderReq().GetOrderID()
		tagService = service.NewTagService()
		bizTag     = e.GetActionOrderReq().TagMap
		bizReq     = e.bizReq
		finance    = packer.FinanceGetByType(e.GetOrder().FinanceList, bizReq.GetFinanceOrderType())
	)
	if finance == nil {
		finance = packer.FinanceGetByType(e.GetOrder().TransferFinanceList, bizReq.GetFinanceOrderType())
	}
	// 检查资金单
	if finance == nil {
		return errdef.NewParamsErr("未找到资金单")
	}
	if bizReq.Amount > 0 && finance.Amount != bizReq.Amount {
		return errdef.NewParamsErr("金额与资金单不等")
	}
	// 测试订单资金风控检查
	bizErr = utils.CheckFundRiskOfAmount(ctx, e.GetOrder().FweOrder.IsTest == int32(1), maths.MaxInt64(bizReq.Amount, finance.Amount), e.conf.TestOrderLimitAmount)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[TransferExecution.PreProcess] CheckFundRiskOfAmount failed, err=%+v", bizErr.Error())
		return bizErr
	}
	supportTradeTypeList := []string{CommonConsts.FinanceTransferHz.Value()}
	if !slices.ContainsString(supportTradeTypeList, finance.TradeType) {
		return errdef.NewParamsErr("不支持的资金单类型")
	}
	// 金额大于0元，执行转账
	if finance.Amount > 0 {
		rpcReq, bizErr := e.buildTransferReq(ctx, finance)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[TransferExecution][withDraw] build pay transfer failed, err=%+v", bizErr)
			return bizErr
		}
		bizErr = service.NewTradePayment().Transfer(ctx, rpcReq)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[TransferExecution][withDraw] Transfer failed, err=%s", bizErr.Error())
			return bizErr
		}
	}
	_, bizErr = tagService.UpdateTag(ctx, orderID, e.GetBizIdentity().BizScene, bizTag)
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferExecution][Process] UpdateTag err=%s", bizErr.Error())
		return bizErr
	}
	e.bizRsp = execution_common.TransferResp{}
	return nil
}

func (e *TransferExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *TransferExecution) buildTransferReq(ctx context.Context, finance *db_model.FFinanceOrder) (*payment.TransferReq, *errdef.BizErr) {
	var (
		proReq = e.bizReq
		order  = e.GetOrder().FweOrder
	)

	orderName := order.OrderName
	rpcReq := &payment.TransferReq{
		Identity:          e.GetBizIdentity(),
		MerchantID:        proReq.PayerInfo.MerchantID,     //
		AppID:             proReq.PayerInfo.AppID,          //
		MerchantName:      proReq.PayerInfo.MerchantName,   //
		UID:               proReq.PayerInfo.UID,            //
		UIDType:           int64(proReq.PayerInfo.UIDType), //
		FcType:            proReq.FcType,                   // 产品提供
		FcSceneCode:       proReq.FcSceneCode,              // 产品提供
		FinanceOrderID:    finance.FinanceOrderID,
		TransferOrderNo:   finance.FinanceOrderID,
		TransferOrderName: orderName,
		TransferOrderDesc: proReq.TransferDesc, // 转账描述
		TradeTime:         time.Now().Unix(),
		Currency:          payment.CurrencyTypePtr(payment.CurrencyType_CNY),
		Amount:            finance.Amount,
		PayerInfo:         e.packParticipant(proReq.PayerInfo),
		PayeeInfo:         e.packParticipant(proReq.PayeeInfo),
		Extra:             nil,
		Operator:          e.GetActionOrderReq().GetOperator(),
	}
	return rpcReq, nil
}

func (e *TransferExecution) packParticipant(conf *fwe_trade_common.Participant) *payment.Participant {
	return &payment.Participant{
		IdentifyType: conf.IdentifyType, // 固定值
		Aid:          nil,
		MerchantID:   conf.MerchantID, // 固定值
		UID:          conf.UID,        // fweAccountId
		UIDType:      conf.UIDType,    // g固定值
		AppID:        conf.AppID,      //
	}
}
