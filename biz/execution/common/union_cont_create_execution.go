package common

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"encoding/json"
	"time"
)

type UnionContCreateExecution struct {
	*executor.ActionBaseExecution
	conf   model.CommonConf
	bizReq execution_common.UnionContCreateReq
	BizRsp execution_common.UnionContCreateRsp
}

func NewUnionContCreateExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &UnionContCreateExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &t.conf, &t.bizReq)
	return t
}

func NewUnionContCreateBaseExecution(ctx context.Context, actionReq interface{}) *UnionContCreateExecution {
	t := &UnionContCreateExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &t.conf, &t.bizReq)
	return t
}

func (e *UnionContCreateExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[UnionContCreateExecution] PreProcess failed, err=%+v", err)
		return err
	}

	if !e.conf.CheckContStructField {
		logs.CtxInfo(ctx, "[UnionContCreateExecution] CheckContStructField is false")
		return nil
	}

	// 校验
	param := &service.CheckContTotalAmountParam{
		TmplID:           e.bizReq.ContTmplID,
		TmplParams:       e.bizReq.ContTmplParams,
		OrderTotalAmount: e.GetOrder().FweOrder.TotalAmount,
		BizScene:         e.GetBizIdentity().BizScene,
		ContType:         e.bizReq.ContType,
		OrderID:          e.GetActionOrderReq().OrderID,
		Params:           e.makeParam(ctx),
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckContTotalAmount(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionContCreateExecution] CheckContTotalAmount failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[UnionContCreateExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "合同总金额和订单不一致")
	}

	return nil

}

func (e *UnionContCreateExecution) Process(ctx context.Context) error {
	var (
		bizErr     *errdef.BizErr
		bizReq     = e.bizReq
		actionReq  = e.GetActionOrderReq()
		operator   = actionReq.GetOperator()
		orderID    = actionReq.GetOrderID()
		tagMap     = actionReq.TagMap
		bizScene   = actionReq.GetIdentity().BizScene
		serviceReq *service_model.UnionContCreateReq
		serviceRsp *service_model.UnionContCreateRsp
		fweOrder   = e.GetOrder().FweOrder
	)

	relatedContSerials, err := service.NewContractService().QueryRelatedContract(ctx, caller.WriteDB(ctx), fweOrder.OrderID)
	if err != nil {
		logs.CtxError(ctx, "[ContSignExecution] signContract err=%s", err.Error())
		return err
	}

	// 驱动
	bizErr = e.FireWithCondition(ctx, e.GetFSMCondition())
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams := &service_model.UpdateOrderParams{
		Operator:             e.GetActionOrderReq().GetOperator(),
		UpdateOrderSubStatus: conv.StringPtr(tools.GetLogStr(e.GetStateMachine().CurSubStates())),
	}

	if e.GetStateMachine().GetOriginalState() != e.GetStateMachine().CurState() { // 主流程有发生流转
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	}

	// 合同请求
	serviceReq = &service_model.UnionContCreateReq{
		TenantType:   int32(actionReq.GetIdentity().GetTenantType()),
		BizScene:     actionReq.GetIdentity().GetBizScene(),
		OrderID:      orderID,
		ContType:     bizReq.ContType,
		TmplID:       bizReq.ContTmplID,
		TmplParams:   bizReq.ContTmplParams,
		SignPartyMap: bizReq.SignPartyMap,
		InOutData: &core.InOutData{
			Currency: core.Currency_CNY,
			TotalIn:  bizReq.ContInAmount,
			TotalOut: bizReq.ContOutAmount,
		},
		CallbackAction: utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:  utils.MakeContractCallbackExtra(orderID),
		Operator: &core.Operator{
			OperatorID:   conv.StrToInt64(operator.GetOperatorID(), 0),
			OperatorName: operator.GetOperatorName(),
		},
		IsTest:             fweOrder.IsTest == int32(1),
		RelatedContSerials: relatedContSerials,
	}

	// 请求
	serviceRsp, bizErr = service.NewContractService().UnionCreateContract(ctx, caller.WriteDB(ctx), serviceReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	e.BizRsp = execution_common.UnionContCreateRsp{
		ContNo:    serviceRsp.ContSerial,
		SignLinks: serviceRsp.SignLinks,
	}

	// 更新拓展字段
	_, bizErr = service.NewTagService().UpdateTag(ctx, orderID, bizScene, tagMap)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	// 更新订单
	bizErr = service.NewOrderService().UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *UnionContCreateExecution) LockTimeout() time.Duration {
	// 创建合同接口比较慢，调整超时时间为 20s
	return 20 * time.Second
}

func (e *UnionContCreateExecution) Result() interface{} {
	str, _ := utils.Marshal(e.BizRsp)
	return str
}

func (e *UnionContCreateExecution) makeParam(ctx context.Context) string {
	var (
		fweOrder = e.GetOrder().FweOrder
	)
	params := make(map[string]interface{})
	params[consts.TradeTypeKey] = fweOrder.TradeType
	bytes, _ := json.Marshal(params)
	return string(bytes)
}
