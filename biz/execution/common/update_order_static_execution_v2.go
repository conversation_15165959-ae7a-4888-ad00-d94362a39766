package common

import (
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"context"
	"github.com/aws/smithy-go/ptr"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UpdateOrderStaticExecutionV2 struct {
	*executor.StaticBaseExecution
	updateOrder execution_common.UpdateOrderReq
	needFire    bool
}

func NewUpdateOrderStaticExecutionV2(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &UpdateOrderStaticExecutionV2{
		needFire: false,
	}
	t.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.updateOrder)
	return t
}

func (e *UpdateOrderStaticExecutionV2) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		updateOrder  = e.updateOrder
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			UpdateOrderName:   updateOrder.OrderName,
			UpdateOrderDesc:   updateOrder.OrderDesc,
			UpdateTotalAmount: updateOrder.TotalAmount,
			Operator:          actionReq.GetOperator(),
		}
	)

	if len(updateOrder.Extra) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.Extra)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(updateOrder.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, updateOrder.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if updateOrder.BuyerInfo != nil {
		buyerID := packer.CommonTradeSubjectIDGet(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerID = &buyerID
		buyerInfo := packer.CommonTradeSubjectSerialize(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerExtra = &buyerInfo
	}

	if updateOrder.SellerInfo != nil {
		sellerID := packer.CommonTradeSubjectIDGet(updateOrder.SellerInfo)
		updateParams.UpdateSellerID = &sellerID
		sellerInfo := packer.CommonTradeSubjectSerialize(updateOrder.SellerInfo)
		updateParams.UpdateSellerExtra = &sellerInfo
	}

	if updateOrder.ServiceProviderInfo != nil {
		serviceProviderID := packer.CommonTradeSubjectIDGet(updateOrder.ServiceProviderInfo)
		updateParams.UpdateSProviderID = &serviceProviderID
		serviceProviderInfo := packer.CommonTradeSubjectSerialize(updateOrder.ServiceProviderInfo)
		updateParams.UpdateSProviderExtra = &serviceProviderInfo
	}

	if updateOrder.TalentInfo != nil {
		talentID := packer.CommonTradeSubjectIDGet(updateOrder.TalentInfo)
		updateParams.UpdateTalentID = &talentID
		talentInfo := packer.CommonTradeSubjectSerialize(updateOrder.TalentInfo)
		updateParams.UpdateTalentExtra = &talentInfo
	}

	if updateOrder.ProductInfo != nil {
		p := updateOrder.ProductInfo
		if p.ProductID != "" {
			updateParams.UpdateProductID = &p.ProductID
		}
		if p.SkuID != "" {
			updateParams.UpdateSkuID = &p.SkuID
		}
		if p.ProductName != "" {
			updateParams.UpdateProductName = &p.ProductName
		}
		if p.ProductType != 0 {
			updateParams.UpdateProductType = conv.Int32Ptr(int32(p.ProductType))
		}
		if p.ProductUnitPrice != 0 {
			updateParams.UpdateProductUnitPrice = &p.ProductUnitPrice
		}
		if p.ProductQuantity != 0 {
			updateParams.UpdateProductQuantity = &p.ProductQuantity
		}
		if p.ProductExtra != nil {
			updateParams.UpdateProductExtra = p.ProductExtra
		}
		if p.ProductVersion != 0 {
			updateParams.UpdateProductVersion = conv.Int64Ptr(p.ProductVersion)
		}
	}

	if updateOrder.Operator != nil {
		updateParams.Operator = updateOrder.Operator
	}

	if len(updateOrder.UpsertFinanceList) > 0 {
		var addFinanceInfoList []*fwe_trade_common.FinanceInfo
		baseOrder := &service_model.OrderBaseParam{
			Identity:  e.GetBizIdentity(),
			OrderID:   orderID,
			OrderName: e.GetOrder().FweOrder.OrderName,
		}
		for _, info := range updateOrder.UpsertFinanceList {
			if info.FinanceOrderID != "" {
				bizErr := service.NewFinanceOrderService().UpdateV2(ctx, info.FinanceOrderID, &service_model.UpdateFinanceParams{
					UpdateAmount:                  ptr.Int64(info.Amount),
					UpdateLoanAmount:              ptr.Int64(info.LoanAmount),
					UpdatePlatformPromotionAmount: ptr.Int64(info.PlatformPromotionAmount),
					UpdateFeeItemDetail:           ptr.String(utils.MarshalToStr(info.FeeItemList)),
					UpdateFinanceStatus:           gptr.Of(int32(info.PayStatus)),
				})
				if bizErr != nil {
					logs.CtxError(ctx, "[UpdateOrderFireExecution] FinanceOrderService UpdateV2 err=%s", bizErr.Error())
					return bizErr
				}
			} else {
				addFinanceInfoList = append(addFinanceInfoList, &fwe_trade_common.FinanceInfo{
					FinanceOrderID:   utils.MakeFinanceOrderIDTool(orderID, info.FinanceOrderType),
					FinanceOrderType: info.FinanceOrderType,
					PayStatus:        fwe_trade_common.FinanceStatus_NotHandle,
					Amount:           info.Amount,
					LoanAmount:       info.LoanAmount,
					TradeCategory:    info.TradeCategory,
					FeeItemList:      info.FeeItemList,
					TradeType:        info.TradeType,
					OrderID:          info.OrderID,
				})
			}
			if len(addFinanceInfoList) > 0 {
				bizErr := service.NewFinanceOrderService().CreateV2(ctx, baseOrder, addFinanceInfoList)
				if bizErr != nil {
					logs.CtxError(ctx, "[UpdateOrderFireExecution] FinanceOrderService CreateV2 err=%s", bizErr.Error())
					return bizErr
				}
			}
		}
	}

	if len(updateOrder.DeleteFinanceList) > 0 {
		for _, deleteId := range updateOrder.DeleteFinanceList {
			bizErr := service.NewFinanceOrderService().UpdateV2(ctx, deleteId, &service_model.UpdateFinanceParams{
				UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Closed)),
			})
			if bizErr != nil {
				logs.CtxError(ctx, "[UpdateOrderFireExecution] FinanceOrderService UpdateV2 err=%s", bizErr.Error())
				return bizErr
			}
		}
	}

	if updateOrder.PurchasePlan != nil {
		updateParams.UpdatePurchasePlan = conv.StringPtr(utils.MarshalToStr(updateOrder.PurchasePlan))
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] UpdateOrder err=%s", bizErr.Error())
		return bizErr
	}

	subjects := []*fwe_trade_common.TradeSubjectInfo{updateOrder.BuyerInfo, updateOrder.SellerInfo,
		updateOrder.ServiceProviderInfo, updateOrder.TalentInfo}
	bizErr = service.NewOrderService().CreateOrUpdateOrderSubject(ctx, subjects)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] CreateOrUpdateOrderSubject err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
