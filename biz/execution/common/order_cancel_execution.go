package common

import (
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type OrderCancelExecution struct {
	*executor.ActionBaseExecution
}

func NewOrderCancelExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &OrderCancelExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, nil)
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return e
}

func (e *OrderCancelExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		return err
	}
	if len(e.GetOrder().FinanceList) > 0 {
		for _, financeOrder := range e.GetOrder().FinanceList {
			if financeOrder.Status == int32(fwe_trade_common.FinanceStatus_Handling) ||
				financeOrder.Status == int32(fwe_trade_common.FinanceStatus_Complete) {
				return errdef.NewParamsErr("订单存在处理中或已完成的资金单, 不可直接取消")
			}
		}
	}
	return nil
}
