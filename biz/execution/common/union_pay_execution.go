package common

import (
	"context"
	"encoding/json"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UnionPayExecution struct {
	*executor.ActionBaseExecution
	conf                 model.CommonConf
	bizReq               execution_common.UnionPayReq
	bizRsp               execution_common.UnionPayResp
	intersectPayTypeList []fwe_trade_common.UnionPayType // 用户传入支付方式和已开通渠道的并集
	shopUID              string
}

func NewUnionPayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &UnionPayExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &t.conf, &t.bizReq)
	return t
}

func (e *UnionPayExecution) CheckParams(_ context.Context) error {
	var bizReq = e.bizReq
	if bizReq.FinanceOrderType == 0 || bizReq.TotalAmount == 0 || len(bizReq.PayTypeList) == 0 || (bizReq.FweAccountID == "" && bizReq.FinanceAccountID == "") {
		return errdef.NewParamsErr("参数错误")
	}
	if slices.Contains(bizReq.PayTypeList, fwe_trade_common.UnionPayType_POS) && len(bizReq.PosDeviceList) == 0 {
		return errdef.NewParamsErr("POS设备列表为空")
	}
	if slices.Contains(bizReq.PayTypeList, fwe_trade_common.UnionPayType_OFFLINE) && len(bizReq.PayTypeList) != 1 {
		return errdef.NewParamsErr("线下支付时，不允许使用其他方式")
	}
	if slices.Contains(bizReq.PayTypeList, fwe_trade_common.UnionPayType_JST) && len(bizReq.PayTypeList) != 1 {
		return errdef.NewParamsErr("结算通支付时，不允许使用其他方式")
	}
	if slices.Contains(bizReq.PayTypeList, fwe_trade_common.UnionPayType_YB_NORMAL) && len(bizReq.PayTypeList) != 1 {
		return errdef.NewParamsErr("易宝支付时，不允许使用其他方式")
	}
	for _, unionPayType := range bizReq.PayTypeList {
		if !e.checkUnionPayType(unionPayType, bizReq.MerchantInfoMap) {
			return errdef.NewParamsErr("缺少与支付类型对应的财经商户信息")
		}
	}
	return nil
}

func (e *UnionPayExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		return err
	}

	var (
		tenantType       = e.GetBizIdentity().GetTenantType()
		bizReq           = e.bizReq
		fweOrder         = e.GetOrder().FweOrder
		totalAmount      = bizReq.TotalAmount
		financeOrders    = e.GetOrder().FinanceList
		financeAccountID = packer.GetFinanceAccountId(bizReq.GetFinanceAccountID(), bizReq.GetFweAccountID())
	)
	// 校验金额
	financeOrder := packer.FinanceGetByType(financeOrders, bizReq.FinanceOrderType)
	if financeOrder == nil {
		logs.CtxError(ctx, "[UnionPayExecution-PreProcess] FinanceGetByType error, orderId = %v", fweOrder.OrderID)
		return errdef.NewRawErr(errdef.DataErr, "not found financeOrder")
	}
	if financeOrder.Amount != totalAmount {
		logs.CtxError(ctx, "[UnionPayExecution-PreProcess] union-pay amount error, orderId = %v", fweOrder.OrderID)
		return errdef.NewParamsErr("union-pay amount not equal finance amount")
	}
	// 合同结构化校验
	if e.conf.CheckContStructField {
		// 校验
		param := &service.CheckAmountByContParam{
			OrderID:                   e.GetActionOrderReq().OrderID,
			FinanceOrderType:          bizReq.FinanceOrderType,
			Amount:                    bizReq.TotalAmount,
			AllowIncomeAmountOverflow: e.conf.CheckContOptionAllowIncomeAmountOverflow,
			Params:                    e.makeSafeParams(ctx),
		}
		pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
		if bizErr != nil {
			logs.CtxError(ctx, "[UnionPayExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
			return bizErr
		}
		if !pass {
			logs.CtxError(ctx, "[UnionPayExecution] safe check not pass, blockMsg=%s", blockMsg)
			return errdef.NewRawErr(errdef.SafeCheckNotPassErr, blockMsg)
		}
	}

	if slices.Contains(bizReq.PayTypeList, fwe_trade_common.UnionPayType_YB_NORMAL) {
		e.intersectPayTypeList = bizReq.PayTypeList
		e.shopUID = financeAccountID
		return nil
	}

	if slices.Contains(bizReq.PayTypeList, fwe_trade_common.UnionPayType_PAY_LIFE) {
		e.intersectPayTypeList = bizReq.PayTypeList
		e.shopUID = financeAccountID
		return nil
	}
	// 查四轮商户对应的资金账户，以及已开通的渠道，将参数中支付方式和已开通的支付方式取交集
	subMerchantMap, bizErr := service.NewFinanceAccountService().GetFweSubMerchantInfo(ctx, tenantType, financeAccountID)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionPayExecution] GetFweSubMerchantInfo failed, err=%s", bizErr.Error())
		return bizErr
	}
	// 按序遍历，结果中保持传入顺序
	for _, unionPayType := range bizReq.PayTypeList {
		// 生服支付，直接加
		if unionPayType == fwe_trade_common.UnionPayType_PAY_LIFE || unionPayType == fwe_trade_common.UnionPayType_PAY_LIFE_SUBSIDY {
			e.intersectPayTypeList = append(e.intersectPayTypeList, unionPayType)
			continue
		}
		if unionPayType == fwe_trade_common.UnionPayType_OFFLINE {
			e.intersectPayTypeList = append(e.intersectPayTypeList, unionPayType)
			continue
		}
		if unionPayType == fwe_trade_common.UnionPayType_GT {
			e.intersectPayTypeList = append(e.intersectPayTypeList, unionPayType)
			continue
		}
		if unionPayType == fwe_trade_common.UnionPayType_NORMAL {
			e.intersectPayTypeList = append(e.intersectPayTypeList, unionPayType)
			continue
		}
		if e.checkUnionPayTypeStatus(subMerchantMap, bizReq.MerchantInfoMap[unionPayType].MerchantID, unionPayType) {
			e.intersectPayTypeList = append(e.intersectPayTypeList, unionPayType)
		}
	}

	// 获取shopUID
	if slices.Contains(bizReq.PayTypeList, fwe_trade_common.UnionPayType_OFFLINE) {
		e.shopUID = financeAccountID
	} else {
		e.shopUID, bizErr = e.getShopUID(subMerchantMap, bizReq.MerchantInfoMap, financeAccountID)
		if bizErr != nil {
			logs.CtxError(ctx, "[UnionPayExecution] getShopUID failed, err=%s", bizErr.Error())
			return bizErr
		}
	}

	return nil
}

func (e *UnionPayExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		serviceReq   *payment.CreateUnionPayReq
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionPayExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))

	// 请求支付
	serviceReq, bizErr = e.buildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionPayExecution] err=%s", bizErr.Error())
		return bizErr
	}
	paymentResp, bizErr := service.NewTradePayment().CreateUnionPay(ctx, serviceReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionPayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 更新tag

	_, bizErr = service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().BizScene, e.bizReq.Tag)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionPayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 更新资金单pay_union_no

	// 更新订单
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	e.bizRsp = execution_common.UnionPayResp{
		PayUnionNo:   paymentResp.PayUnionNo,
		PayUnionLink: paymentResp.PayUnionLink,
		PayData:      paymentResp.PayData,
	}
	return nil
}

func (e *UnionPayExecution) buildReq(ctx context.Context) (*payment.CreateUnionPayReq, *errdef.BizErr) {
	var (
		productReq           = e.bizReq
		intersectPayTypeList = e.intersectPayTypeList
		shopUID              = e.shopUID
		order                = e.GetOrder()
		fweOrder             = order.FweOrder
		bizErr               *errdef.BizErr
	)

	financeOrder := packer.FinanceGetByType(order.FinanceList, productReq.FinanceOrderType)
	if financeOrder == nil {
		bizErr = errdef.NewParamsErr("no finance order")
		return nil, bizErr
	}

	orderType := fwe_trade_common.OrderType_Trade
	var isMock bool
	bossFlag, bizErr := service.NewConfigService().GetBoss(ctx, *productReq.UserID)
	if bizErr == nil && bossFlag && order.FweOrder.IsTest == 1 {
		isMock = true
	}

	serviceReq := &payment.CreateUnionPayReq{
		Identity:              e.GetBizIdentity(),
		OutID:                 financeOrder.FinanceOrderID,
		OutName:               financeOrder.OrderName,
		UserID:                productReq.UserID,
		Currency:              productReq.Currency,
		TotalAmount:           productReq.TotalAmount,
		EstimateSubsidyAmount: productReq.EstimateSubsidyAmount,
		PayTypeList:           intersectPayTypeList,
		MerchantInfoMap:       productReq.MerchantInfoMap,
		PosDeviceList:         productReq.PosDeviceList,
		PayLimitList:          productReq.PayLimitList,
		CashierDeskType:       productReq.CashierDeskType,
		OsType:                productReq.OsType,
		ExpireTime:            productReq.ExpireTime,
		RedirectURL:           productReq.RedirectURL,
		ShopUID:               shopUID,
		OrderType:             &orderType,
		OrderID:               conv.StringPtr(fweOrder.OrderID),
		Extra:                 productReq.Extra,
		CallbackEvent:         utils.MakeCallbackEvent(productReq.CallbackEvent),
		CallbackExtra:         productReq.CallbackExtra,
		CloseCallbackEvent:    utils.MakeCallbackEvent(productReq.CloseCallbackEvent),
		CheckData:             productReq.GetCheckData(),
		RiskInfo:              productReq.GetRiskInfo(),
		JstPayParam:           productReq.GetJstPayParam(),
		OneStepPay:            productReq.OneStepPay,
		PayUnionParam:         productReq.PayUnionParam,
		SecondPayParam:        productReq.SecondPayParam,
		IsMock:                isMock,
		Base:                  base.NewBase(),
	}

	return serviceReq, nil
}

func (e *UnionPayExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *UnionPayExecution) checkUnionPayTypeStatus(subMerchantMap map[string][]*finance_account.SubMerchantInfo,
	merchantID string, unionPayType fwe_trade_common.UnionPayType) bool {
	var (
		wontCheckTypes = []fwe_trade_common.UnionPayType{
			fwe_trade_common.UnionPayType_NORMAL, fwe_trade_common.UnionPayType_OFFLINE,
		}
		unionPayTypeMapping = e.unionPayTradeChannelMap()
	)
	if slices.Contains(wontCheckTypes, unionPayType) {
		return true
	}
	// 担保交易，没有开户信息
	if len(subMerchantMap) == 0 || len(subMerchantMap[merchantID]) == 0 {
		return false
	}
	// 担保交易，看对应渠道是否就绪
	for _, subMerchantInfo := range subMerchantMap[merchantID] {
		if slices.Contains(unionPayTypeMapping[unionPayType], subMerchantInfo.TradeChannel) && subMerchantInfo.ChannelStatus == finance_account.ChannelStatus_Ready {
			return true
		}
	}
	return false
}

func (e *UnionPayExecution) unionPayTradeChannelMap() map[fwe_trade_common.UnionPayType][]finance_account.TradeChannel {
	return map[fwe_trade_common.UnionPayType][]finance_account.TradeChannel{
		fwe_trade_common.UnionPayType_YZT: {
			finance_account.TradeChannel_yzt_hz, finance_account.TradeChannel_yzt_alipay, finance_account.TradeChannel_syt_wx,
		},
		fwe_trade_common.UnionPayType_POS: {
			finance_account.TradeChannel_hz, finance_account.TradeChannel_lkl,
		},
		fwe_trade_common.UnionPayType_GT: {
			finance_account.TradeChannel_alipay, finance_account.TradeChannel_wx, finance_account.TradeChannel_hz,
		},
		fwe_trade_common.UnionPayType_YZT_QR: {
			finance_account.TradeChannel_yzt_hz, finance_account.TradeChannel_yzt_alipay, finance_account.TradeChannel_syt_wx,
		},
		fwe_trade_common.UnionPayType_JST: {
			finance_account.TradeChannel_jst_tob_out,
		},
		fwe_trade_common.UnionPayType_YZT_OFFLINE_CASH: {
			finance_account.TradeChannel_yzt_hz, finance_account.TradeChannel_yzt_alipay, finance_account.TradeChannel_syt_wx,
		},
		fwe_trade_common.UnionPayType_YZT_OFFLINE_TRANSFER: {
			finance_account.TradeChannel_yzt_hz, finance_account.TradeChannel_yzt_alipay, finance_account.TradeChannel_syt_wx,
		},
	}
}

// getShopUID 获取普通担保和云直通的商户uid
func (e *UnionPayExecution) getShopUID(subMerchantMap map[string][]*finance_account.SubMerchantInfo,
	payTypeMerchantMap map[fwe_trade_common.UnionPayType]*payment.MerchantInfo, defaultShopId string) (string, *errdef.BizErr) {
	var (
		filterList = []fwe_trade_common.UnionPayType{
			fwe_trade_common.UnionPayType_YZT,
			fwe_trade_common.UnionPayType_GT,
			fwe_trade_common.UnionPayType_POS,
			fwe_trade_common.UnionPayType_JST,
		}
		unionPayTypeMapping = e.unionPayTradeChannelMap()
		shopUID             = defaultShopId
		shopUIDMap          = make(map[string]struct{})
	)
	// 遍历支付列表，uid 不同不能聚合支付
	for unionPayType, merchantInfo := range payTypeMerchantMap {
		if slices.Contains(filterList, unionPayType) {
			for _, subMerchantInfo := range subMerchantMap[merchantInfo.MerchantID] {
				if slices.Contains(unionPayTypeMapping[unionPayType], subMerchantInfo.TradeChannel) && subMerchantInfo.ChannelStatus == finance_account.ChannelStatus_Ready {
					shopUIDMap[subMerchantInfo.UID] = struct{}{}
					shopUID = subMerchantInfo.UID
				}
			}
		}
	}
	if len(shopUIDMap) > 1 {
		return "", errdef.NewRawErr(errdef.ShopUIDNotUniqueErr, "shop's uid is not unique")
	}
	return shopUID, nil
}

func (e *UnionPayExecution) checkUnionPayType(payType fwe_trade_common.UnionPayType, merchantInfoMap map[fwe_trade_common.UnionPayType]*payment.MerchantInfo) bool {
	if payType == fwe_trade_common.UnionPayType_OFFLINE {
		return true
	}
	if (payType == fwe_trade_common.UnionPayType_YZT_OFFLINE_CASH || payType == fwe_trade_common.UnionPayType_YZT_OFFLINE_TRANSFER) && merchantInfoMap[payType] == nil {
		merchantInfoMap[payType] = merchantInfoMap[fwe_trade_common.UnionPayType_YZT]
	}
	info, exist := merchantInfoMap[payType]
	if !exist {
		return false
	}
	if info.MerchantID == "" {
		return false
	}
	return true
}

func (e *UnionPayExecution) makeSafeParams(ctx context.Context) string {
	var (
		fweOrder = e.GetOrder().FweOrder
	)
	params := make(map[string]interface{})
	params[consts.TradeTypeKey] = fweOrder.TradeType
	bytes, _ := json.Marshal(params)
	return string(bytes)
}

func (e *UnionPayExecution) GetBizReq() execution_common.UnionPayReq {
	return e.bizReq
}

func (e *UnionPayExecution) GetBizRsp() execution_common.UnionPayResp {
	return e.bizRsp
}
