package common

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type CreateOrderExecution struct {
	*executor.CreateBaseExecution
	result interface{}
}

// Deprecated:  please use NewCreateOrderExecutionV2
func NewCreateOrderExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &CreateOrderExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), nil)
	return t
}

func (e *CreateOrderExecution) Process(ctx context.Context) error {
	var (
		err          error
		bizErr       *errdef.BizErr
		stateMachine = e.GetStateMachine()
		createReq    = e.GetCreateOrderReq()
		bizScene     = createReq.GetIdentity().GetBizScene()
		productInfo  = createReq.GetProductInfo()
	)

	// 驱动状态
	err = stateMachine.Fire(ctx, statemachine.OrderCreateEt.Value(), nil)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "CreateOrderExecution")
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene,
		SmVersion:          createReq.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createReq.GetOrderName(),
		OrderDesc:          createReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductExtra:       productInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		ProductVersion:     productInfo.ProductVersion,
		TotalAmount:        createReq.TotalAmount,
		TotalPayAmount:     createReq.TotalAmount,
		TotalSubsidyAmount: 0,
		TradeType:          int32(createReq.TradeType),
		BuyerID:            packer.CommonTradeSubjectIDGet(createReq.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(createReq.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createReq.IsTest),
		Creator:            createReq.GetOperator().GetOperatorID(),
		CreatorName:        createReq.GetOperator().GetOperatorName(),
		Operator:           createReq.GetOperator().GetOperatorID(),
		OperatorName:       createReq.GetOperator().GetOperatorName(),
		IdempotentID:       createReq.GetIdemID(),
	}

	order := &service_model.Order{
		FweOrder: fweOrder,
		TagMap:   createReq.OrderTag,
		BizExtra: createReq.Extra,
	}

	bizErr = service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.result = e.GetOrderID()
	return nil
}
