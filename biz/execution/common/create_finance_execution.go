package common

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/gopkg/tools"
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_auction_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type CreateFinanceOrderExecution struct {
	*executor.ActionBaseExecution
	bizReq execution_common.CreateFinanceReq
	bizRsp execution_common.CreateFinanceRsp
	conf   sh_auction_model.ShAuctionConfig
}

func NewCreateFinanceOrderExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &CreateFinanceOrderExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &t.conf, &t.bizReq)
	return t
}

func (e *CreateFinanceOrderExecution) Process(ctx context.Context) error {
	var (
		fweOrder     = e.GetOrder().FweOrder
		orderService = service.NewFinanceOrderService()
		orderID      = e.GetActionOrderReq().GetOrderID()
	)

	if len(e.bizReq.Tag) > 0 {
		bizErr := service.NewOrderService().UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	financeList, bizErr := e.buildFinanceList(ctx, fweOrder)
	if bizErr != nil {
		logs.CtxInfo(ctx, "[CreateFinanceOrderExecution-Process] buildFinanceList error, err = %+v", bizErr)
		return bizErr
	}

	var isFind bool
	for _, v := range e.GetOrder().FinanceList {
		if v.FinanceOrderType == financeList[0].FinanceOrderType {
			isFind = true
			if v.Amount != financeList[0].Amount {
				bizErr = errdef.NewParamsErr("can not update amount")
				logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
				return bizErr
			}
		}
	}
	if isFind {
		logs.CtxInfo(ctx, "[CreateFinanceExecution] has find finances")
		return nil
	}

	// 新建资金单
	bizErr = orderService.CreateFinanceOrderList(ctx, financeList)
	if bizErr != nil {
		logs.CtxInfo(ctx, "[CreateFinanceOrderExecution-Process] CreateFinanceOrderList error, err = %+v", bizErr)
		return bizErr
	}
	// todo 更新订单行支付金额吗？
	return nil
}

func (e *CreateFinanceOrderExecution) buildFinanceList(ctx context.Context, order *db_model.FweOrder) ([]*db_model.FFinanceOrder, *errdef.BizErr) {
	var (
		merchant = e.conf.NormalWithdrawMerchant
		req      = e.bizReq
	)

	if merchant == nil {
		merchant = &sh_auction_model.MerchantInfo{
			MerchantID: e.bizReq.MerchantID,
		}
	}

	// 一个资金单
	if len(req.FinanceList) != 1 {
		bizErr := errdef.NewParamsErr("only can create one")
		logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
		return nil, bizErr
	}

	output := make([]*db_model.FFinanceOrder, 0)

	for _, info := range req.FinanceList {

		// 兼容逻辑
		merchantID := info.MerchantID
		if merchantID == "" {
			merchantID = merchant.MerchantID
		}
		if merchantID == "" {
			return nil, errdef.NewRawErr(errdef.LackConfigErr, "merchantID is blank")
		}

		tradeType := info.TradeType
		if tradeType == "" {
			tradeType = consts.TradeTypeWithdrawBank.String()
		}
		output = append(output, &db_model.FFinanceOrder{
			TenantType:       order.TenantType,
			BizScene:         order.BizScene,
			AppID:            "",
			MerchantID:       merchantID,
			Mid:              "",
			OrderID:          order.OrderID,
			OrderName:        order.OrderName,
			TradeType:        tradeType,
			FinanceOrderID:   utils.MakeFinanceOrderIDTool(order.OrderID, info.FinanceOrderType),
			FinanceOrderType: info.FinanceOrderType,
			Amount:           info.Amount,
			ProcessAmount:    0,
			FeeItemDetail:    conv.StringPtr(tools.GetLogStr(info.FeeItemList)),
			Status:           getStatusBool(info.Amount == 0, fwe_trade_common.FinanceStatus_Complete, fwe_trade_common.FinanceStatus_NotHandle),
		})
	}
	return output, nil
}

func getStatusBool(cond bool, a, b fwe_trade_common.FinanceStatus) int32 {
	if cond {
		return int32(a)
	} else {
		return int32(b)
	}
}
