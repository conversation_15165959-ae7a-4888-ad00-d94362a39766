package common

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	sdkUtils "code.byted.org/motor/fwe_trade_common/statemachine/utils"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"time"
)

// UnionWithdrawExecution Compare this snippet from biz/execution/common/withdraw_execution.go: 封装了创建资金单的逻辑
type UnionWithdrawExecution struct {
	*executor.ActionBaseExecution
	conf   model.CommonConf
	bizReq execution_common.WithdrawReq
	bizRsp execution_common.WithdrawRsp
}

func NewUnionWithdrawExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &UnionWithdrawExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq)
	return e
}

func (e *UnionWithdrawExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[UnionWithdrawExecution] PreProcess failed, err=%+v", err)
		return err
	}

	// 判断是否做合同金额校验
	if !e.conf.CheckContStructField {
		return nil
	}

	// 校验
	param := &service.CheckAmountByContParam{
		OrderID:          e.GetActionOrderReq().OrderID,
		FinanceOrderType: e.bizReq.FinanceOrderType,
		Amount:           e.bizReq.Amount,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionWithdrawExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[UnionWithdrawExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "出款合同校验未通过")
	}
	return nil
}

func (e *UnionWithdrawExecution) Process(ctx context.Context) error {
	var (
		bizErr         *errdef.BizErr
		withdrawAmount = e.bizReq.GetAmount()
	)

	// 跳转状态机
	conditions := sdkUtils.BuildWithdrawCondition(withdrawAmount)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[UnionWithdrawExecution-process] fire fsm failed, err=%+v", err.Error())
		return err
	}
	// 出款金额为 0，结束
	if withdrawAmount == int64(0) {
		logs.CtxInfo(ctx, "[UnionWithdrawExecution-process] withdraw amount is 0")
		return nil
	}
	// 出款
	withdrawNo, bizErr := service.NewUnionWithdrawService().WithdrawDeposit(ctx, e.BuildWithdrawReq(ctx))
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionWithdrawExecution-process] WithdrawDeposit failed, err=%s", bizErr.Error())
		return bizErr
	}
	e.bizRsp = execution_common.WithdrawRsp{
		WithdrawOrderNo: withdrawNo,
	}
	return nil
}

func (e *UnionWithdrawExecution) PostProcess(ctx context.Context) error {
	var (
		orderID      = e.GetOrder().FweOrder.OrderID
		stateMachine = e.GetStateMachine()
	)
	// 更新订单tag
	if _, bizErr := service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), e.bizReq.Tag); bizErr != nil {
		return bizErr
	}
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{}
	if stateMachine.GetOriginalState() != stateMachine.CurState() { // 如果主流程有状态流转
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	}

	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	updateParams.Operator = e.GetActionOrderReq().GetOperator()
	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *UnionWithdrawExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *UnionWithdrawExecution) GetBizReq() execution_common.WithdrawReq {
	return e.bizReq
}

func (e *UnionWithdrawExecution) BuildWithdrawReq(_ context.Context) *service_model.UnionWithdrawParam {
	var (
		req            = e.GetActionOrderReq()
		fweOrder       = e.GetOrder().FweOrder
		bizReq         = e.bizReq
		currency       = payment.CurrencyType_CNY
		financeOrderId = utils.MakeFinanceOrderIDTool(fweOrder.OrderID, bizReq.FinanceOrderType)
	)
	rpcReq := &payment.WithdrawDepositReq{
		OrderID:           fweOrder.OrderID,
		FinanceOrderID:    financeOrderId,
		MerchantID:        bizReq.MerchantID,
		MerchantName:      bizReq.MerchantName,
		AppID:             bizReq.AppID,
		Amount:            bizReq.Amount,
		WithdrawType:      bizReq.WithdrawType,
		IPAddress:         bizReq.IPAddress,
		WitdhrawDesc:      bizReq.Reason,
		Mid:               nil,
		Currency:          &currency,
		BankCardInfo:      bizReq.BankCardInfo,
		CallbackEvent:     utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:     "",
		FailCallbackEvent: utils.MakeCallbackEvent(bizReq.FailCallbackAction),
		FweAccountID:      bizReq.FweAccountID,
		Extra:             nil,
		Operator:          req.GetOperator(),
		Identity:          req.GetIdentity(),
	}
	return &service_model.UnionWithdrawParam{
		OrderID:        fweOrder.OrderID,
		OrderName:      fweOrder.OrderName,
		FinanceType:    bizReq.FinanceOrderType,
		WithdrawAmount: bizReq.Amount,
		FeeItemList:    bizReq.FeeItemList,
		WithdrawReq:    rpcReq,
	}
}
