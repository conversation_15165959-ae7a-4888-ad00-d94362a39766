package common

import (
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"context"
	"time"

	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type LifePaySettleExecution struct {
	*executor.ActionBaseExecution
	BizReq execution_common.SettleReq
	BizRsp execution_common.SettleRsp
	conf   *model.CommonConf

	OutPayUnionNos []string
	SettleAmount   int64
}

func NewLifePaySettleExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &LifePaySettleExecution{conf: new(model.CommonConf)}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), e.conf, &e.BizReq)
	return e
}

func (e *LifePaySettleExecution) CheckParams(ctx context.Context) error {
	return nil
}

func (e *LifePaySettleExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}
	var (
		fweOrder = e.GetOrder().FweOrder
		bizReq   = e.BizReq
	)
	// pre 阶段查出来
	if int32(e.GetBizIdentity().TenantType) != fweOrder.TenantType || e.GetBizIdentity().BizScene != fweOrder.BizScene {
		bizErr := errdef.NewParamsErr("Identity 与db不一致")
		return bizErr
	}
	var (
		outPayUnionNos []string
		settleAmount   int64
	)

	financeOrders := packer.FinanceGetByTypes(e.GetOrder().FinanceList, bizReq.FinanceTypeList)
	for _, order := range financeOrders {
		outPayUnionNos = append(outPayUnionNos, order.FinanceOrderID)
		settleAmount += order.Amount
	}
	e.OutPayUnionNos = outPayUnionNos
	e.SettleAmount = settleAmount
	return nil
}

func (e *LifePaySettleExecution) Process(ctx context.Context) error {
	var (
		bizErr *errdef.BizErr
	)
	bizErr = e.FireWithCondition(ctx, nil)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	// 分账
	settleParam, bizErr := e.BuildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[LifePaySettleExecution] err=%s", bizErr.Error())
		return bizErr
	}
	mergeSettleNo, bizErr := service.NewUnionSettleService().UnionSettle(ctx, settleParam)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[LifePaySettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.BizRsp = execution_common.SettleRsp{
		MergeSettleNo: mergeSettleNo,
	}
	return nil
}

func (e *LifePaySettleExecution) PostProcess(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		fweOrder     = e.GetOrder().FweOrder
		orderID      = fweOrder.OrderID
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
		stateMachine = e.GetStateMachine()
	)

	// 更新order
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	bizErr = service.NewOrderService().UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 更新tag
	_, bizErr = service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), e.BizReq.Tag)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *LifePaySettleExecution) Result() interface{} {
	str, _ := utils.Marshal(e.BizRsp)
	return str
}

func (e *LifePaySettleExecution) BuildReq(ctx context.Context) (*service_model.UnionSettleParam, *errdef.BizErr) {
	var (
		productReq     = e.BizReq
		fweOrder       = e.GetOrder().FweOrder
		outPayUnionNos = e.OutPayUnionNos
	)
	if len(outPayUnionNos) == 0 {
		return nil, errdef.NewParamsErr("the length of outPayUnionNos is 0")
	}
	serviceReq := &payment.MergeSettleV2Req{
		Identity:       e.GetBizIdentity(),
		OrderID:        fweOrder.OrderID,
		OrderType:      fwe_trade_common.OrderType_Trade,
		OutID:          utils.MakeSettleFinanceOutID(fweOrder.OrderID, productReq.SettleType),
		OutPayUnionNos: outPayUnionNos,
		SettleDesc:     productReq.Reason,
		IPAddress:      conv.StringPtr(productReq.IPAddress),
		BizExtra:       conv.StringPtr(productReq.BizExtra),
		IsOmitPosFee:   conv.BoolPtr(false),
		IsAutoWithdraw: productReq.IsAutoWithdraw,
		CallbackEvent:  utils.MakeCallbackEvent(productReq.CallbackAction),
		CallbackExtra:  "",
	}
	settleParam := &service_model.UnionSettleParam{
		OrderID:        fweOrder.OrderID,
		OrderName:      fweOrder.OrderName,
		SettleType:     productReq.SettleType,
		SettleAmount:   e.SettleAmount,
		MergeSettleReq: serviceReq,
	}
	return settleParam, nil
}
