package common

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/sh_sell_ef"
	"github.com/tidwall/sjson"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_invoice/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
)

type invoiceExecution struct {
	*executor.ActionBaseExecution
	bizReq execution_common.InvoiceReq
	bizRsp execution_common.InvoiceResp
}

func NewInvoiceExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &invoiceExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.bizReq)
	return e
}

func (e *invoiceExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		fweOrder     = e.GetOrder().FweOrder
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
		invoiceOrderID string
	)

	// 驱动
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[invoiceExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	if int32(e.GetBizIdentity().TenantType) != fweOrder.TenantType || e.GetBizIdentity().BizScene != fweOrder.BizScene {
		bizErr = errdef.NewParamsErr("Identity 与db不一致")
		return bizErr
	}

	// 请求发票service
	rpcReq, bizErr := e.buildReq(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[invoiceExecution] buildReq failed, err=%s", bizErr.Error())
		return bizErr
	}
	invoiceOrderID, bizErr = service.NewInvoiceService().Invoice(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[invoiceExecution] Invoice failed, err=%s", bizErr.Error())
		return bizErr
	}

	// 更新tag
	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[invoiceExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	// 更新订单
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[invoiceExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.InvoiceResp{
		InvoiceOrderID: invoiceOrderID,
	}
	return nil
}

func (e *invoiceExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *invoiceExecution) buildReq(ctx context.Context) (*core.InvoiceReq, *errdef.BizErr) {
	var (
		bizReq          = e.bizReq
		successCallback = utils.MakeCallbackEvent(bizReq.SuccCallbackAction)
		failCallback    = utils.MakeCallbackEvent(bizReq.FailCallbackAction)
		extra           = bizReq.Extra
		err             error
	)

	if extra == "" {
		extra = "{}"
	}

	extra, err = sjson.Set(extra, sh_sell_ef.ShSellEFDCInvoiceSuccessEvent.Value(), successCallback)
	if err != nil {
		logs.CtxError(ctx, "[invoiceExecution.buildReq] set json succCallbackAction failed, err=%+v", err)
		return nil, errdef.NewBizErrWithCode(errdef.DataErr, err)
	}

	extra, err = sjson.Set(extra, sh_sell_ef.ShSellEFDCInvoiceFailEvent.Value(), failCallback)
	if err != nil {
		logs.CtxError(ctx, "[invoiceExecution.buildReq] set json failCallbackAction failed, err=%+v", err)
		return nil, errdef.NewBizErrWithCode(errdef.DataErr, err)
	}

	rpcReq := &core.InvoiceReq{
		InvoiceBizScene:   10001,
		InvoiceMode:       bizReq.InvoiceMode,
		InvoiceType:       bizReq.InvoiceType,
		InvoiceFuncType:   bizReq.InvoiceFuncType,
		RelatedOutID:      bizReq.RelatedOutID,
		SubjectID:         bizReq.SubjectID,
		CustomerInfo:      bizReq.CustomerInfo,
		SalesInfo:         bizReq.SalesInfo,
		InvoiceItem:       bizReq.InvoiceItem,
		ActualInvoiceItem: bizReq.ActualInvoiceItem,
		Amount:            bizReq.Amount,
		IncludeTax:        bizReq.IncludeTax,
		TaxRate:           bizReq.TaxRate,
		ApplyRemark:       bizReq.ApplyRemark,
		InvoiceRemark:     bizReq.InvoiceRemark,
		InvoiceContent:    bizReq.InvoiceContent,
		ContactEmail:      bizReq.ContactEmail,
		ContactNumber:     bizReq.ContactNumber,
		Receiver:          bizReq.Receiver,
		ReceiveAddress:    bizReq.ReceiveAddress,
		BusinessExt1:      bizReq.BusinessExt1,
		BusinessTimeExt1:  bizReq.BusinessTimeExt1,
		VehicleInfo:       bizReq.VehicleInfo,
		Extra:             extra,
		IsTest:            &(e.GetOrder().FweOrder.IsTest),
		OperatorID:        bizReq.OperatorID,
		OperatorName:      bizReq.OperatorName,
		Base:              base.NewBase(),
	}
	return rpcReq, nil
}
