package common

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type RefundWithSafeCheckExecution struct {
	*UnionRefundExecution
}

// NewRefundWithSafeCheckExecution 带合同结构化校验退款
func NewRefundWithSafeCheckExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &RefundWithSafeCheckExecution{}
	e.UnionRefundExecution = NewUnionRefundBaseExecution(ctx, actionReq)
	return e
}

func (e *RefundWithSafeCheckExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.BizReq
	if bizReq.GetRuleID() != int64(0) {
		return nil
	}
	if bizReq.RefundList == nil || len(bizReq.RefundList) == 0 {
		bizErr := errdef.NewParamsErr("RefundList 参数错误")
		return bizErr
	}
	for _, refund := range bizReq.RefundList {
		if refund.Amount <= 0 {
			bizErr := errdef.NewParamsErr("RefundList.amount 参数错误")
			return bizErr
		}
	}
	return nil
}

func (e *RefundWithSafeCheckExecution) PreProcess(ctx context.Context) error {

	err := e.UnionRefundExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[cancelRefundExecution-PreProcess] UnionRefundExecution.PreProcess error, err = %v", err.Error())
		return err
	}
	// 如果退款金额为0，则不用结构化校验
	if e.RefundAmount == 0 {
		return nil
	}

	// 合同结构化校验
	param := &service.CheckAmountByContParam{
		OrderID:          e.GetActionOrderReq().OrderID,
		FinanceOrderType: e.BizReq.RefundType,
		Amount:           e.RefundAmount,
		TradeCategory:    fwe_trade_common.TradeCategory_Refund,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionPayExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[UnionPayExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, blockMsg)
	}

	return nil
}
