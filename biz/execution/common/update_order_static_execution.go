package common

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools/tools_recover_kite"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type UpdateOrderStaticExecution struct {
	*executor.StaticBaseExecution
	UpdateOrder action_model.UpdateOrder
	needFire    bool
}

// Deprecated:  please use NewUpdateOrderStaticExecutionV2
func NewUpdateOrderStaticExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &UpdateOrderStaticExecution{
		needFire: false,
	}
	t.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.UpdateOrder)
	return t
}

func (e *UpdateOrderStaticExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		updateOrder  = e.UpdateOrder
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			UpdateOrderName:   updateOrder.OrderName,
			UpdateOrderDesc:   updateOrder.OrderDesc,
			UpdateTotalAmount: updateOrder.TotalAmount,
			Operator:          actionReq.GetOperator(),
		}
	)

	if len(updateOrder.Extra) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.Extra)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(updateOrder.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, updateOrder.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if updateOrder.BuyerInfo != nil {
		buyerID := packer.CommonTradeSubjectIDGet(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerID = &buyerID
		buyerInfo := packer.CommonTradeSubjectSerialize(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerExtra = &buyerInfo
	}

	if updateOrder.SellerInfo != nil {
		sellerID := packer.CommonTradeSubjectIDGet(updateOrder.SellerInfo)
		updateParams.UpdateSellerID = &sellerID
		sellerInfo := packer.CommonTradeSubjectSerialize(updateOrder.SellerInfo)
		updateParams.UpdateSellerExtra = &sellerInfo
	}

	if updateOrder.ProductInfo != nil {
		p := updateOrder.ProductInfo
		if p.ProductID != "" {
			updateParams.UpdateProductID = &p.ProductID
		}
		if p.SkuID != "" {
			updateParams.UpdateSkuID = &p.SkuID
		}
		if p.ProductName != "" {
			updateParams.UpdateProductName = &p.ProductName
		}
		if p.ProductType != 0 {
			updateParams.UpdateProductType = conv.Int32Ptr(int32(p.ProductType))
		}
		if p.ProductUnitPrice != 0 {
			updateParams.UpdateProductUnitPrice = &p.ProductUnitPrice
		}
		if p.ProductQuantity != 0 {
			updateParams.UpdateProductQuantity = &p.ProductQuantity
		}
		if p.ProductExtra != nil {
			updateParams.UpdateProductExtra = p.ProductExtra
		}
		if p.ProductVersion != 0 {
			updateParams.UpdateProductVersion = &p.ProductVersion
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	go func() {
		defer func() {
			tools_recover_kite.CheckRecover(ctx, recover(), nil)
		}()

		subjects := []*fwe_trade_common.TradeSubjectInfo{updateOrder.BuyerInfo, updateOrder.SellerInfo}
		bizErr = service.NewOrderService().CreateOrUpdateOrderSubject(ctx, subjects)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] CreateOrUpdateOrderSubject err=%s", bizErr.Error())
		}
	}()

	return nil
}
