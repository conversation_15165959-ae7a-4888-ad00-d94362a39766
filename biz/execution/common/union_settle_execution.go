package common

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/gopkg/metrics"
	sdkConsts "code.byted.org/motor/fwe_trade_common/consts"
	sdkUtils "code.byted.org/motor/fwe_trade_common/statemachine/utils"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/fee"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UnionSettleExecution struct {
	*executor.ActionBaseExecution
	BizReq execution_common.SettleReq
	BizRsp execution_common.SettleRsp
	conf   *model.CommonConf

	FeeRecordID     string
	OutPayUnionNos  []string
	CommonSpiltInfo *fwe_trade_common.TradeSpiltInfo
	SettleAmount    int64
}

func NewUnionSettleExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &UnionSettleExecution{conf: new(model.CommonConf)}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), e.conf, &e.BizReq)
	return e
}

func NewUnionSettleBaseExecution(ctx context.Context, actionReq interface{}) *UnionSettleExecution {
	e := &UnionSettleExecution{conf: new(model.CommonConf)}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), e.conf, &e.BizReq)
	return e
}

func (e *UnionSettleExecution) CheckParams(ctx context.Context) error {
	var (
		bizReq = e.BizReq
	)
	if bizReq.GetRuleID() == int64(0) {
		if bizReq.GetSplitInfo() == nil || len(bizReq.GetSplitInfo().Detail) == 0 {
			return errdef.NewParamsErr("GetSplitInfo error")
		}
	}

	return nil
}

func (e *UnionSettleExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}
	var (
		order           = e.GetOrder()
		fweOrder        = e.GetOrder().FweOrder
		bizReq          = e.BizReq
		outPayUnionNos  []string
		operator        = e.GetActionOrderReq().Operator
		tradeFeeService = service.NewTradeFeeService()
	)
	// pre 阶段查出来
	if int32(e.GetBizIdentity().TenantType) != fweOrder.TenantType || e.GetBizIdentity().BizScene != fweOrder.BizScene {
		bizErr := errdef.NewParamsErr("Identity 与db不一致")
		return bizErr
	}

	// 幂等判断
	refundFinanceOrder := packer.FinanceGetByType(order.SettleFinanceList, bizReq.GetSettleType())
	if refundFinanceOrder != nil && refundFinanceOrder.FeeRecordID != "" {
		feeRecord, bizErr := tradeFeeService.GetOneTradeFeeRecord(ctx, refundFinanceOrder.FeeRecordID)
		if bizErr != nil {
			logs.CtxError(ctx, "[UnionRefundExecution-PreProcess] GetOneTradeFeeRecord", bizErr.Error())
			return bizErr
		}

		spiltInfo, bizErr := packer.FeeSplits2PaymentSplits(ctx, feeRecord.ChargeResultList, fweOrder, e.conf.PlatformSplitUID, e.conf.RentSplitUID, e.conf.AdvanceAccountUID)
		if bizErr != nil {
			logs.CtxError(ctx, "[UnionSettleExecution-PreProcess] FeeSplits2PaymentSplits error, err = %v", bizErr.Error())
			return bizErr
		}
		e.SetSettleData(feeRecord.RecordID, spiltInfo, feeRecord.SettleFinanceIDList)
		return nil
	}

	// 准备参数
	if bizReq.GetRuleID() == int64(0) {
		financeOrders := packer.FinanceGetByTypes(e.GetOrder().FinanceList, bizReq.FinanceTypeList)
		for _, order := range financeOrders {
			outPayUnionNos = append(outPayUnionNos, order.FinanceOrderID)
		}
		// 分账信息
		e.SetSettleData("", bizReq.GetSplitInfo(), outPayUnionNos)
		return nil
	}
	// 咨询计费中心
	chargeSettleResp, bizErr := tradeFeeService.ChargeSettle(ctx, &fee.ChargeSettleReq{
		OrderID:  fweOrder.OrderID,
		RuleID:   bizReq.GetRuleID(),
		Params:   conv.StringPtr(bizReq.Params),
		Operator: operator.OperatorName,
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionSettleExecution-PreProcess] ChargeSettle error, err = %v", bizErr.Error())
		return bizErr
	}
	spiltInfo, bizErr := packer.FeeSplits2PaymentSplits(ctx, chargeSettleResp.ChargeResultList, fweOrder, e.conf.PlatformSplitUID, e.conf.RentSplitUID, e.conf.AdvanceAccountUID)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionSettleExecution-PreProcess] FeeSplits2PaymentSplits error, err = %v", bizErr.Error())
		return bizErr
	}
	// 同时传了资金单类型，取业务的资金单
	var outPayUnionNos2 = chargeSettleResp.FinanceIDList
	if len(bizReq.FinanceTypeList) > 0 {
		financeOrders := packer.FinanceGetByTypes(e.GetOrder().FinanceList, bizReq.FinanceTypeList)
		for _, order := range financeOrders {
			outPayUnionNos = append(outPayUnionNos, order.FinanceOrderID)
		}
		outPayUnionNos2 = outPayUnionNos
	}
	e.SetSettleData(chargeSettleResp.RecordID, spiltInfo, outPayUnionNos2)
	return nil
}

func (e *UnionSettleExecution) Process(ctx context.Context) error {
	var (
		bizErr         *errdef.BizErr
		settleAmount   = e.SettleAmount
		actionOrderReq = e.GetActionOrderReq()
	)
	// 驱动
	conditions := sdkUtils.BuildSettleCondition(settleAmount)
	if actionOrderReq.ActionCondition != "" {
		conditions, _ = sdkUtils.AppendBizReqParams(ctx, conditions, actionOrderReq.ActionCondition)
	}
	bizErr = e.FireWithCondition(ctx, conditions)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	// 金额为0，结束
	if settleAmount == int64(0) {
		logs.CtxInfo(ctx, "[UnionSettleExecution] settleAmount is 0")
		tags := []metrics.T{
			{Name: consts.TagBizScene, Value: fmt.Sprintf("%d", e.GetBizIdentity().BizScene)},
			{Name: consts.TagStatus, Value: fmt.Sprintf("%d", e.GetStateMachine().CurState())},
		}
		utils.EmitCounter(consts.MetricSettleAmountIsZero, 1, tags...)
		return nil
	}
	// 分账,默认分账需要消费贷金额
	settleParam, bizErr := e.BuildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	mergeSettleNo, bizErr := service.NewUnionSettleService().UnionSettle(ctx, settleParam)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.BizRsp = execution_common.SettleRsp{
		MergeSettleNo: mergeSettleNo,
	}
	return nil
}

func (e *UnionSettleExecution) PostProcess(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		fweOrder     = e.GetOrder().FweOrder
		orderID      = fweOrder.OrderID
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
		stateMachine = e.GetStateMachine()
	)

	// 更新order
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	bizErr = service.NewOrderService().UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 更新tag
	_, bizErr = service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), e.BizReq.Tag)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *UnionSettleExecution) Result() interface{} {
	str, _ := utils.Marshal(e.BizRsp)
	return str
}

func (e *UnionSettleExecution) BuildReq(ctx context.Context) (*service_model.UnionSettleParam, *errdef.BizErr) {
	var (
		productReq     = e.BizReq
		fweOrder       = e.GetOrder().FweOrder
		outPayUnionNos = e.OutPayUnionNos
		settleAmount   = e.SettleAmount
		recordID       = e.FeeRecordID
		subsidyList    = make([]*payment.SubsidyInfo, 0)
	)
	if len(outPayUnionNos) == 0 {
		return nil, errdef.NewParamsErr("the length of outPayUnionNos is 0")
	}
	splits, bizErr := service.NewFinanceAccountService().ConvertCommonSplitInfo(ctx, e.GetBizIdentity().TenantType, e.CommonSpiltInfo)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionSettleExecution-BuildReq] ConvertCommonSplitInfo error, err = %v", bizErr.Error())
		return nil, bizErr
	}

	subsidyList, bizErr = e.getSubsidyList(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionSettleExecution-BuildReq] getSubsidyList error, err = %v", bizErr.Error())
		return nil, bizErr
	}

	serviceReq := &payment.MergeSettleV2Req{
		Identity:       e.GetBizIdentity(),
		OrderID:        fweOrder.OrderID,
		OrderType:      fwe_trade_common.OrderType_Trade,
		OutID:          utils.MakeSettleFinanceOutID(fweOrder.OrderID, productReq.SettleType),
		OutPayUnionNos: outPayUnionNos,
		SettleDesc:     productReq.Reason,
		SplitList:      splits,
		SubsidyList:    subsidyList,
		IPAddress:      conv.StringPtr(productReq.IPAddress),
		BizExtra:       conv.StringPtr(productReq.BizExtra),
		IsOmitPosFee:   conv.BoolPtr(false),
		IsAutoWithdraw: productReq.IsAutoWithdraw,
		CallbackEvent:  utils.MakeCallbackEvent(productReq.CallbackAction),
		CallbackExtra:  "",
	}
	if !strings.IsPtrBlank(e.BizReq.SubsidyOutUID) && e.BizReq.SubsidyOutUIDType != nil {
		serviceReq.YztOfflineSubsidyInfo = &payment.SubsidyConfig{
			OutUID:     *e.BizReq.SubsidyOutUID,
			OutUIDType: *e.BizReq.SubsidyOutUIDType,
		}
	}
	settleParam := &service_model.UnionSettleParam{
		OrderID:        fweOrder.OrderID,
		OrderName:      fweOrder.OrderName,
		SettleType:     productReq.SettleType,
		SettleAmount:   settleAmount,
		MergeSettleReq: serviceReq,
		FeeRecordID:    recordID,
	}
	return settleParam, nil
}

// getSubsidyList 将贷款金额和平台优惠作为补贴
// 有贷款时优先使用协议扣款时记录的merchantID，如果不存在取第一笔支付单的merchantID，
// 如果已经在第一次分账阶段补贴过了，第二次进行分账时不需要补贴
func (e *UnionSettleExecution) getSubsidyList(ctx context.Context) ([]*payment.SubsidyInfo, *errdef.BizErr) {
	var (
		subsidyAmount           int64
		inputTotalSubsidyAmount int64
		inputTargetMerchantID   string
		targetFinanceID         string
		res                     = make([]*payment.SubsidyInfo, 0)
		order                   = e.GetOrder()
		financeList             = e.GetOrder().FinanceList
		inputSubsidyUnit        = e.BizReq.SubsidyInfoList
		validStatusList         = []fwe_trade_common.FinanceStatus{fwe_trade_common.FinanceStatus_Complete}
		tradeTypeList           = []string{consts.TradeTypeYZT.String(), consts.TradeTypePayPos.String(), consts.TradeTypePayYztQr.String(),
			consts.TradeTypePayYztOfflineCash.String(), consts.TradeTypePayYztOfflineTransfer.String(), sdkConsts.TradeTypePayLifeSubsidy.Value()}
		bizErr          *errdef.BizErr
		financeOrderIds = e.OutPayUnionNos
	)
	if e.BizReq.OmitInfraSubsidy {
		logs.CtxInfo(ctx, "[UnionSettleExecution] omitInfraSubsidy is true, no need subsidy")
		return nil, nil
	}
	logs.CtxInfo(ctx, "[UnionSettleExecution] bizReq = %s", tools.GetLogStr(e.BizReq))
	// 计算loan_amount 和 platformPromotionAmount
	for _, financeOrder := range financeList {
		if !slices.Contains(financeOrderIds, financeOrder.FinanceOrderID) {
			continue
		}
		if financeOrder.TradeCategory != int32(fwe_trade_common.TradeCategory_Pay) {
			continue
		}
		if !slices.Contains(validStatusList, fwe_trade_common.FinanceStatus(financeOrder.Status)) {
			continue
		}
		if financeOrder.LoanAmount+financeOrder.PlatformPromotionAmount == int64(0) {
			continue
		}
		subsidyAmount += financeOrder.LoanAmount + financeOrder.PlatformPromotionAmount
		targetFinanceID = financeOrder.FinanceOrderID
	}
	if len(inputSubsidyUnit) > 0 {
		logs.CtxInfo(ctx, "[UnionSettleExecution] inputSubsidyUnit is not empty, %s", tools.GetLogStr(inputSubsidyUnit))
		inputTargetMerchantID = inputSubsidyUnit[0].MerchantID
		for _, subsidyUnit := range inputSubsidyUnit {
			if subsidyUnit.MerchantID != inputTargetMerchantID {
				bizErr = errdef.NewParamsErr("补贴户一级户需相同")
				logs.CtxError(ctx, "[UnionSettleExecution-getSubsidyList] %+v", bizErr)
				return nil, bizErr
			}
			inputTotalSubsidyAmount += subsidyUnit.Amount
		}
		if inputTotalSubsidyAmount != subsidyAmount {
			logs.CtxError(ctx, "[UnionSettleExecution-getSubsidyList] inputTotalSubsidyAmount != subsidyAmount, inputTotalSubsidyAmount = %v, subsidyAmount = %v", inputTotalSubsidyAmount, subsidyAmount)
			return nil, errdef.NewParamsErr("传入补贴金额与实际金额不一致")
		}
	}
	if subsidyAmount == 0 {
		logs.CtxInfo(ctx, "[UnionSettleExecution] subsidy amount is 0, no need subsidy")
		return nil, nil
	}
	if e.BizReq.TotalSubsidyAmount != nil && *e.BizReq.TotalSubsidyAmount != subsidyAmount {
		bizErr = errdef.NewParamsErr("传入补贴金额与实际金额不一致")
		logs.CtxError(ctx, "[UnionSettleExecution-getSubsidyList] %+v", bizErr)
		return nil, bizErr
	}

	// 查询资金模型
	successPay, bizErr := service.NewOrderService().QueryOneSuccessPay(ctx, order.FweOrder.OrderID, tradeTypeList, subsidyAmount, targetFinanceID, inputTargetMerchantID)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionSettleExecution-getSubsidyList] QueryOneSuccessPay error, err = %v", bizErr)
		return nil, bizErr
	}
	if successPay == nil {
		logs.CtxInfo(ctx, "[UnionSettleExecution] successPay is nil, dont need subsidy")
		return nil, nil
	}
	// 业务传入补贴列表优先
	if len(inputSubsidyUnit) > 0 {
		for _, unit := range inputSubsidyUnit {
			if strings.IsBlank(unit.OutUID) || unit.OutUIDType == 0 {
				bizErr = errdef.NewParamsErr("传入补贴户不能为空")
				logs.CtxError(ctx, "[UnionSettleExecution-getSubsidyList] %+v", bizErr)
				return nil, bizErr
			}
			subsidy := &payment.SubsidyInfo{
				MerchantID: inputTargetMerchantID,
				OutUID:     unit.OutUID,
				OutUIDType: unit.OutUIDType,
				Amount:     unit.Amount,
				Extra:      unit.Extra,
			}
			res = append(res, subsidy)
		}
	} else {
		subsidy := &payment.SubsidyInfo{
			MerchantID: successPay.MerchantID,
			Amount:     subsidyAmount,
		}
		// 正式
		if !strings.IsBlank(e.conf.PlatformSplitUID) {
			subsidy.OutUID = e.conf.PlatformSplitUID
			subsidy.OutUIDType = consts.PlatformUidType
		}
		// 测试
		if !strings.IsBlank(e.conf.PlatformSplitUIDTest) && order.FweOrder.IsTest == 1 {
			subsidy.OutUID = e.conf.PlatformSplitUIDTest
			subsidy.OutUIDType = consts.PlatformUidType
		}
		res = append(res, subsidy)
	}
	return res, nil
}

func (e *UnionSettleExecution) getSettleAmount(info *fwe_trade_common.TradeSpiltInfo) int64 {
	res := int64(0)
	if info == nil || len(info.Detail) == 0 || info.SplitMethod == fwe_trade_common.SplitMethod_ByScale {
		return res
	}
	for _, splitUnit := range info.Detail {
		res += splitUnit.Amount
	}
	return res
}

func (e *UnionSettleExecution) SetSettleData(recordID string, spiltInfo *fwe_trade_common.TradeSpiltInfo, idList []string) {
	e.FeeRecordID = recordID
	e.CommonSpiltInfo = spiltInfo
	e.OutPayUnionNos = idList
	e.SettleAmount = e.getSettleAmount(spiltInfo)
}
