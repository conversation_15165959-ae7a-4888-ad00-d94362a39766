package common

import (
	"code.byted.org/lang/gg/gptr"
	"context"
	"github.com/aws/smithy-go/ptr"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UpdateOrderFireExecutionV2 struct {
	*executor.ActionBaseExecution
	updateOrder execution_common.UpdateOrderReq
}

func NewUpdateOrderFireExecutionV2(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &UpdateOrderFireExecutionV2{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.updateOrder)
	return t
}

func NewUpdateOrderFireExecutionV2Base(ctx context.Context, actionReq interface{}) *UpdateOrderFireExecutionV2 {
	t := &UpdateOrderFireExecutionV2{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.updateOrder)
	return t
}

func (e *UpdateOrderFireExecutionV2) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		return err
	}

	// 解析状态机条件
	var (
		fireConditionStr = conv.StringDefault(e.updateOrder.FireCondition, "{}")
		fireCondition    = make(map[string]interface{})
	)
	if fireConditionStr == "" {
		fireConditionStr = "{}"
	}

	err = utils.SonicUnmarshal(fireConditionStr, &fireCondition)
	if err != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] unmarshal fire_condition str failed, err=%+v", err)
		return errdef.NewRawErr(errdef.ParamErr, "fireCondition格式错误")
	}

	if len(e.GetFSMCondition()) > 0 {
		fireCondition = e.MergeCondition(fireCondition, e.GetFSMCondition())
	}

	bizErr := e.FireWithCondition(ctx, fireCondition)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] fire failed, err=%s, fireCondition=%s",
			bizErr.Error(), tools.GetLogStr(fireCondition))
		return bizErr
	}

	return nil
}

func (e *UpdateOrderFireExecutionV2) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		updateOrder  = e.updateOrder
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		orderService = service.NewOrderService()
		stateMachine = e.GetStateMachine()
		updateParams = &service_model.UpdateOrderParams{
			UpdateOrderName:   updateOrder.OrderName,
			UpdateOrderDesc:   updateOrder.OrderDesc,
			UpdateTotalAmount: updateOrder.TotalAmount,
			Operator:          actionReq.GetOperator(),
		}
	)
	if e.GetStateMachine().GetOriginalState() != e.GetStateMachine().CurState() {
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	}
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))

	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}

	if len(updateOrder.GetExtra()) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.GetExtra())
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(updateOrder.Tag) > 0 || len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(updateOrder.Tag) > 0 {
			for key, value := range updateOrder.Tag {
				tagMap[key] = value
			}
		}
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		bizErr = orderService.UpdateOrderTag(ctx, orderID, tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if updateOrder.BuyerInfo != nil {
		buyerID := packer.CommonTradeSubjectIDGet(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerID = &buyerID
		buyerInfo := packer.CommonTradeSubjectSerialize(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerExtra = &buyerInfo
	}

	if updateOrder.SellerInfo != nil {
		sellerID := packer.CommonTradeSubjectIDGet(updateOrder.SellerInfo)
		updateParams.UpdateSellerID = &sellerID
		sellerInfo := packer.CommonTradeSubjectSerialize(updateOrder.SellerInfo)
		updateParams.UpdateSellerExtra = &sellerInfo
	}

	if updateOrder.ServiceProviderInfo != nil {
		serviceProviderID := packer.CommonTradeSubjectIDGet(updateOrder.ServiceProviderInfo)
		updateParams.UpdateSProviderID = &serviceProviderID
		serviceProviderInfo := packer.CommonTradeSubjectSerialize(updateOrder.ServiceProviderInfo)
		updateParams.UpdateSProviderExtra = &serviceProviderInfo
	}

	if updateOrder.ProductInfo != nil {
		p := updateOrder.ProductInfo
		if p.ProductID != "" {
			updateParams.UpdateProductID = &p.ProductID
		}
		if p.SkuID != "" {
			updateParams.UpdateSkuID = &p.SkuID
		}
		if p.ProductName != "" {
			updateParams.UpdateProductName = &p.ProductName
		}
		if p.ProductType != 0 {
			updateParams.UpdateProductType = conv.Int32Ptr(int32(p.ProductType))
		}
		if p.ProductUnitPrice != 0 {
			updateParams.UpdateProductUnitPrice = &p.ProductUnitPrice
		}
		if p.ProductQuantity != 0 {
			updateParams.UpdateProductQuantity = &p.ProductQuantity
		}
		if p.ProductExtra != nil {
			updateParams.UpdateProductExtra = p.ProductExtra
		}
		if p.ProductVersion != 0 {
			updateParams.UpdateProductVersion = conv.Int64Ptr(p.ProductVersion)
		}
	}

	if updateOrder.Operator != nil {
		updateParams.Operator = updateOrder.Operator
	}

	if updateOrder.PurchasePlan != nil {
		updateParams.UpdatePurchasePlan = conv.StringPtr(utils.MarshalToStr(updateOrder.PurchasePlan))
	}

	if len(updateOrder.UpsertFinanceList) > 0 {
		var addFinanceInfoList []*fwe_trade_common.FinanceInfo
		baseOrder := &service_model.OrderBaseParam{
			Identity:  e.GetBizIdentity(),
			OrderID:   orderID,
			OrderName: e.GetOrder().FweOrder.OrderName,
		}
		for _, info := range updateOrder.UpsertFinanceList {
			if info.FinanceOrderID != "" {
				bizErr := service.NewFinanceOrderService().UpdateV2(ctx, info.FinanceOrderID, &service_model.UpdateFinanceParams{
					UpdateAmount:        ptr.Int64(info.Amount),
					UpdateFeeItemDetail: ptr.String(utils.MarshalToStr(info.FeeItemList)),
					UpdateFinanceStatus: gptr.Of(int32(info.PayStatus)),
					UpdateLoanAmount:    gptr.Of(info.LoanAmount),
				})
				if bizErr != nil {
					logs.CtxError(ctx, "[UpdateOrderFireExecution] FinanceOrderService UpdateV2 err=%s", bizErr.Error())
					return bizErr
				}
			} else {
				addFinanceInfoList = append(addFinanceInfoList, &fwe_trade_common.FinanceInfo{
					FinanceOrderID:   utils.MakeFinanceOrderIDTool(orderID, info.FinanceOrderType),
					FinanceOrderType: info.FinanceOrderType,
					PayStatus:        fwe_trade_common.FinanceStatus_NotHandle,
					Amount:           info.Amount,
					TradeCategory:    info.TradeCategory,
					FeeItemList:      info.FeeItemList,
					TradeType:        info.TradeType,
					OrderID:          info.OrderID,
					LoanAmount:       info.LoanAmount,
				})
			}
			if len(addFinanceInfoList) > 0 {
				bizErr := service.NewFinanceOrderService().CreateV2(ctx, baseOrder, addFinanceInfoList)
				if bizErr != nil {
					logs.CtxError(ctx, "[UpdateOrderFireExecution] FinanceOrderService CreateV2 err=%s", bizErr.Error())
					return bizErr
				}
			}
		}
	}

	if len(updateOrder.DeleteFinanceList) > 0 {
		for _, deleteId := range updateOrder.DeleteFinanceList {
			bizErr := service.NewFinanceOrderService().UpdateV2(ctx, deleteId, &service_model.UpdateFinanceParams{
				UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Closed)),
			})
			if bizErr != nil {
				logs.CtxError(ctx, "[UpdateOrderFireExecution] FinanceOrderService UpdateV2 err=%s", bizErr.Error())
				return bizErr
			}
		}
	}

	// 更新订单信息
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// upsert ebs信息
	subjects := []*fwe_trade_common.TradeSubjectInfo{updateOrder.BuyerInfo, updateOrder.SellerInfo,
		updateOrder.ServiceProviderInfo, updateOrder.TalentInfo}
	bizErr = service.NewOrderService().CreateOrUpdateOrderSubject(ctx, subjects)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] CreateOrUpdateOrderSubject err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
