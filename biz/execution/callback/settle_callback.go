package callback

import (
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type SettleCallbackBaseExecution struct {
	*executor.ActionBaseExecution
	cbModel *callback_model.SettleCallbackModel
}

func NewSettleCallbackCommonExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &SettleCallbackBaseExecution{
		cbModel: new(callback_model.SettleCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel, options...)
	return exe
}

func NewSettleCallbackBaseExecution(ctx context.Context, sourceReq interface{}, confPtr interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &SettleCallbackBaseExecution{
		cbModel: new(callback_model.SettleCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, confPtr, exe.cbModel, options...)
	return exe
}

func (e *SettleCallbackBaseExecution) CheckParams(ctx context.Context) error {
	return nil
}

func (e *SettleCallbackBaseExecution) Process(ctx context.Context) error {
	return nil
}
