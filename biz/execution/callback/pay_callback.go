package callback

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type PayCallbackBaseExecution struct {
	*executor.ActionBaseExecution
	cbModel *callback_model.PayCallbackModel
}

// Deprecated: 后续请使用：NewUnionPayCallbackBaseExecution
func NewPayCallbackCommonExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &PayCallbackBaseExecution{
		cbModel: new(callback_model.PayCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel, options...)
	return exe
}

// Deprecated: 后续请使用：NewUnionPayCallbackBaseExecution
func NewPayCallbackCommonExecutionBase(ctx context.Context, sourceReq interface{}) *PayCallbackBaseExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &PayCallbackBaseExecution{
		cbModel: new(callback_model.PayCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel, options...)
	return exe
}

// Deprecated: 后续请使用：NewUnionPayCallbackBaseExecution
func NewPayCallbackBaseExecution(ctx context.Context, sourceReq interface{}, confPtr interface{}, options ...*executor.Option) *PayCallbackBaseExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &PayCallbackBaseExecution{
		cbModel: new(callback_model.PayCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, confPtr, exe.cbModel, options...)
	return exe
}

// Deprecated: 后续请使用：NewUnionPayCallbackBaseExecution
func NewPayCallbackWithFinishExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}, {OptionID: executor.OptionAutoFinish}}
	exe := &PayCallbackBaseExecution{
		cbModel: new(callback_model.PayCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel, options...)
	return exe
}

func (e *PayCallbackBaseExecution) CheckParams(ctx context.Context) error {
	return nil
}

func (e *PayCallbackBaseExecution) Process(ctx context.Context) error {
	return nil
}
