package callback

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type WithdrawCallbackBaseExecution struct {
	*executor.ActionBaseExecution
	cbModel *callback_model.WithdrawCallbackModel
}

func NewWithdrawCallbackCommonExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &WithdrawCallbackBaseExecution{
		cbModel: new(callback_model.WithdrawCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel, options...)
	return exe
}

func NewWithdrawCallbackBaseExecutionWithOpt(ctx context.Context, sourceReq interface{}, opt ...*executor.Option) *WithdrawCallbackBaseExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &WithdrawCallbackBaseExecution{
		cbModel: new(callback_model.WithdrawCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel, opt...)
	return exe
}

func NewWithdrawCallbackBaseExecution(ctx context.Context, sourceReq interface{}, confPtr interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &WithdrawCallbackBaseExecution{
		cbModel: new(callback_model.WithdrawCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, confPtr, exe.cbModel, options...)
	return exe
}

func NewWithdrawCallbackWithFinishExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFinish})
	exe := &WithdrawCallbackBaseExecution{
		cbModel: new(callback_model.WithdrawCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel, opts...)
	return exe
}

func (e *WithdrawCallbackBaseExecution) CheckParams(ctx context.Context) error {
	return nil
}

func (e *WithdrawCallbackBaseExecution) Process(ctx context.Context) error {
	return nil
}
