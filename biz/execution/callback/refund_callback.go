package callback

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type RefundCallbackBaseExecution struct {
	*executor.ActionBaseExecution
	CbModel *callback_model.RefundCallbackModel
}

func NewRefundCallbackCommonExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &RefundCallbackBaseExecution{
		CbModel: new(callback_model.RefundCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.CbModel, options...)
	return exe
}

func NewRefundCallbackBaseExecution(ctx context.Context, sourceReq interface{}, confPtr interface{}, options ...*executor.Option) *RefundCallbackBaseExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &RefundCallbackBaseExecution{
		CbModel: new(callback_model.RefundCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, confPtr, exe.CbModel, options...)
	return exe
}

func NewRefundCallbackWithFinishExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFinish})
	exe := &RefundCallbackBaseExecution{
		CbModel: new(callback_model.RefundCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.CbModel, opts...)
	return exe
}

func (e *RefundCallbackBaseExecution) CheckParams(ctx context.Context) error {
	return nil
}

func (e *RefundCallbackBaseExecution) Process(ctx context.Context) error {
	return nil
}
