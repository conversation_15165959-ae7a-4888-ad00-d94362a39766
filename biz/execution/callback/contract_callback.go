package callback

import (
	"context"
	"errors"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
)

type ContCallbackBaseExecution struct {
	*executor.ActionBaseExecution
	cbModel *callback_model.ContractCallbackModel
}

func NewContCallbackCommonExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &ContCallbackBaseExecution{
		cbModel: new(callback_model.ContractCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel, options...)
	return exe
}

func NewContCallbackBaseExecution(ctx context.Context, sourceReq interface{}, confPtr interface{}) *ContCallbackBaseExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &ContCallbackBaseExecution{
		cbModel: new(callback_model.ContractCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, confPtr, exe.cbModel, options...)
	return exe
}

func NewContCallbackBaseExecutionWithOpt(ctx context.Context, sourceReq interface{}, opt ...*executor.Option) *ContCallbackBaseExecution {
	e := &ContCallbackBaseExecution{
		cbModel: new(callback_model.ContractCallbackModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, sourceReq.(*engine.ActionOrderReq), nil, e.cbModel, opt...)
	return e
}

func NewContCallbackWithOpt(ctx context.Context, actionReq interface{}, confPtr interface{}, opt ...*executor.Option) *ContCallbackBaseExecution {
	e := &ContCallbackBaseExecution{
		cbModel: new(callback_model.ContractCallbackModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), confPtr, e.cbModel, opt...)
	return e
}

func (e *ContCallbackBaseExecution) CheckParams(ctx context.Context) error {
	cbModel := e.cbModel
	if cbModel.TenantType == 0 || cbModel.BizScene == 0 || cbModel.ContSerial == "" || cbModel.OutContID == "" {
		logs.CtxWarn(ctx, "[ContCallbackBaseExecution.CheckParams] param error, cbModel=%s", tools.GetLogStr(cbModel))
		return errors.New("param error")
	}
	if cbModel.Status != int32(fwe_trade_common.CommonStatus_Success) {
		logs.CtxWarn(ctx, "[ContCallbackBaseExecution.CheckParams] status is not success")
		return errors.New("status is not success")
	}
	if cbModel.SignTime == int64(0) {
		logs.CtxWarn(ctx, "[ContCallbackBaseExecution.CheckParams] signTime is 0")
		return errors.New("signTime is 0")
	}
	return nil
}

func (e *ContCallbackBaseExecution) PreProcess(ctx context.Context) error {
	var (
		cbModel = e.cbModel
		orderId = e.GetActionOrderReq().GetOrderID()
	)
	// 有正确的order_id
	if orderId != "" && orderId != callback_model.ContractOrderID {
		return e.ActionBaseExecution.PreProcess(ctx)
	}
	// 没有order_id,使用外键号查找,兼容逻辑，后续删除
	orderContract, err := db_query.FweOrderContract.WithContext(ctx).
		Where(db_query.FweOrderContract.ContractNo.Eq(cbModel.OutContID)).
		Where(db_query.FweOrderContract.InfraContSerial.Eq(cbModel.ContSerial)).
		First()
	if err != nil {
		logs.CtxError(ctx, "[contractCallback.Process] query fwe_order_contract failed, err=%+v", err)
		return err
	}

	// 查主订单
	order, err := db_query.FweOrder.WithContext(ctx).
		Where(db_query.FweOrder.OrderID.Eq(orderContract.OrderID)).
		First()
	if err != nil {
		logs.CtxError(ctx, "[contractCallback.Process] query fwe_order failed, err=%+v", err)
		return err
	}

	// 赋值orderID
	e.ActionBaseExecution.SetOrderID(order.OrderID)

	return e.ActionBaseExecution.PreProcess(ctx)
}

func (e *ContCallbackBaseExecution) GetContCallbackModel() *callback_model.ContractCallbackModel {
	return e.cbModel
}

func (e *ContCallbackBaseExecution) PostProcess(ctx context.Context) error {
	cbModel := e.cbModel
	// 在这里更新 合同关联表
	bizErr := service.NewContractService().CompleteCont(ctx, cbModel)
	if bizErr != nil {
		logs.CtxError(ctx, "[contractCallback.Process] CompleteCont failed, err=%+v", bizErr)
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}
