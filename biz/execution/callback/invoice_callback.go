package callback

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_invoice/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type InvoiceCallbackExecution struct {
	*executor.ActionBaseExecution
	cbModel *callback_model.InvoiceResult
}

func NewInvoiceCallbackExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &InvoiceCallbackExecution{
		cbModel: new(callback_model.InvoiceResult),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel)
	return exe
}

func NewInvoiceCallbackBaseExecution(ctx context.Context, sourceReq interface{}) *InvoiceCallbackExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &InvoiceCallbackExecution{
		cbModel: new(callback_model.InvoiceResult),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel)
	return exe
}

func (e *InvoiceCallbackExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[UnionPayCallbackBaseExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}
	bizErr := e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionPayCallbackBaseExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *InvoiceCallbackExecution) Process(ctx context.Context) error {
	var (
		cbModel = e.cbModel
	)

	// 校验状态
	if cbModel.InvoiceStatus != int32(core.InvoiceStatus_INVOICE_SUCCESS) &&
		cbModel.InvoiceStatus != int32(core.InvoiceStatus_INVOICE_FAIL) {
		logs.CtxError(ctx, "[InvoiceCallbackExecution] status is not success nor fail")
		return errdef.NewRawErr(errdef.ServerException, "callback status is not success nor fail")
	}

	return nil
}

func (e *InvoiceCallbackExecution) PostProcess(ctx context.Context) error {
	var (
		order        = e.GetOrder()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)
	// 更新状态
	updateParams.WhereOrderStatus = []int32{int32(e.GetStateMachine().GetOriginalState())}
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	bizErr := service.NewOrderService().UpdateOrder(ctx, order.FweOrder.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionPayCallbackBaseExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}
