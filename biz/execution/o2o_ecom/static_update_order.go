package o2o_ecom

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/consts"

	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type StaticUpdateOrderExecution struct {
	*common.UpdateOrderStaticExecution
}

func NewStaticUpdateOrderExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &StaticUpdateOrderExecution{}
	t.UpdateOrderStaticExecution = common.NewUpdateOrderStaticExecution(ctx, actionReq).(*common.UpdateOrderStaticExecution)
	return t
}

func (e *StaticUpdateOrderExecution) Process(ctx context.Context) error {
	if err := e.UpdateOrderStaticExecution.Process(ctx); err != nil {
		return err
	}
	// 更新尾款资金单
	var (
		finalFinanceInfo    *fwe_trade_common.FinanceInfo
		finalFinanceOrderID string
	)
	for _, v := range e.GetOrder().FinanceList {
		if v.FinanceOrderType == consts.FinanceFinal.Value() {
			finalFinanceOrderID = v.FinanceOrderID
			break
		}
	}
	for _, v := range e.UpdateOrder.FinanceList {
		if v.FinanceOrderType == consts.FinanceFinal.Value() {
			finalFinanceInfo = v
			break
		}
	}
	if finalFinanceInfo != nil {
		updateFinanceParams := &service_model.UpdateFinanceParams{
			UpdateAmount:                  &finalFinanceInfo.Amount,
			UpdateFeeItemDetail:           conv.StringPtr(utils.MarshalToStr(finalFinanceInfo.FeeItemList)),
			UpdateOfflineLoanAmount:       &finalFinanceInfo.OfflineLoanAmount,
			UpdatePlatformPromotionAmount: &finalFinanceInfo.PlatformPromotionAmount,
			UpdatePlatformPromotionDetail: finalFinanceInfo.PlatformPromotionDetail,
		}

		bizErr := service.NewFinanceOrderService().UpdateV2(ctx, finalFinanceOrderID, updateFinanceParams)
		if bizErr != nil {
			logs.CtxError(ctx, "[StaticUpdateOrderExecution] update financeOrder failed, err=%s", bizErr.Error())
			return bizErr
		}
	}

	return nil
}
