package o2o_ecom

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/consts"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/o2o_ecom_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type updateInstallmentExecution struct {
	*executor.ActionBaseExecution
	bizReq *o2o_ecom_model.UpdateInstallmentModel
}

// NewUpdateInstallmentExecution 修改分期方案， 即修改首付款资金单
func NewUpdateInstallmentExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &updateInstallmentExecution{}
	e.bizReq = new(o2o_ecom_model.UpdateInstallmentModel)
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, e.bizReq)
	return e
}

func (e *updateInstallmentExecution) CheckParams(ctx context.Context) error {
	var (
		bizReq           = e.bizReq
		finalFinanceInfo = bizReq.FinalFinanceInfo
	)

	if finalFinanceInfo == nil {
		logs.CtxError(ctx, "[updateInstallmentExecution] finance info is empty")
		return errdef.NewParamsErr("finance info is empty")
	}

	return nil
}

func (e *updateInstallmentExecution) Process(ctx context.Context) error {
	var (
		req                = e.GetActionOrderReq()
		orderID            = req.OrderID
		bizReq             = e.bizReq
		finalFinanceInfo   = bizReq.FinalFinanceInfo
		order              = e.GetOrder().FweOrder
		financeList        = e.GetOrder().FinanceList
		earnestFinanceInfo *db_model.FFinanceOrder

		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: req.GetOperator(),
		}

		bizErr *errdef.BizErr
	)

	// 金额校验
	for _, v := range financeList {
		if v.FinanceOrderType == consts.FinanceEarnest.Value() {
			earnestFinanceInfo = v
			break
		}
	}
	calc := earnestFinanceInfo.Amount + finalFinanceInfo.Amount + finalFinanceInfo.OfflineLoanAmount + finalFinanceInfo.PlatformPromotionAmount
	if order.TotalAmount != calc {
		logs.CtxError(ctx, "[updateInstallmentExecution] total amount is not consistent, order's totalAmount=%d, calc=%d", order.TotalAmount, calc)
		return errdef.NewRawErr(errdef.AmountNotConsistErr, "total amount is not consistent")
	}

	// 状态机流转
	bizErr = e.FireDefault(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[updateInstallmentExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	updateParams.UpdateProductExtra = bizReq.ProductExtra

	// 更新
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[updateInstallmentExecution] err=%s", bizErr.Error())
		return bizErr
	}

	if len(bizReq.Extra) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, bizReq.Extra)
		if bizErr != nil {
			logs.CtxError(ctx, "[updateInstallmentExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(bizReq.OrderTag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, bizReq.OrderTag)
		if bizErr != nil {
			logs.CtxError(ctx, "[updateInstallmentExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	// 修改资金单信息
	updateFinanceParams := &service_model.UpdateFinanceParams{
		UpdateAmount:                  &finalFinanceInfo.Amount,
		UpdateFeeItemDetail:           conv.StringPtr(utils.MarshalToStr(finalFinanceInfo.FeeItemList)),
		UpdateOfflineLoanAmount:       &finalFinanceInfo.OfflineLoanAmount,
		UpdatePlatformPromotionAmount: &finalFinanceInfo.PlatformPromotionAmount,
		UpdatePlatformPromotionDetail: finalFinanceInfo.PlatformPromotionDetail,
	}
	bizErr = service.NewFinanceOrderService().UpdateV2(ctx, finalFinanceInfo.FinanceOrderID, updateFinanceParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[updateInstallmentExecution] update financeOrder failed, err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
