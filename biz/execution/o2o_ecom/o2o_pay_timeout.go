package o2o_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

type O2OPayTimeoutExecution struct {
	// 基于支付超时 额外添加还库存操作
	*callback.PayCallbackBaseExecution
}

func NewO2OPayTimeoutExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &O2OPayTimeoutExecution{}
	exe.PayCallbackBaseExecution = callback.NewPayCallbackCommonExecutionBase(ctx, sourceReq)
	return exe
}

func (e *O2OPayTimeoutExecution) PostProcess(ctx context.Context) error {
	fweOrder := e.GetOrder().FweOrder
	IncrStock(ctx, fweOrder.OrderID, conv.Int64Default(fweOrder.SkuID, 0))

	// 父类型执行
	if err := e.PayCallbackBaseExecution.PostProcess(ctx); err != nil {
		return err
	}

	return nil
}
