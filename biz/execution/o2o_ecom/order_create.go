package o2o_ecom

import (
	"context"
	"strconv"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/platform_ecom"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/base"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/tenant_base"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/o2o_ecom_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	base2 "code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	engine2 "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_activity/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type CreateOrderExecution struct {
	*executor.CreateBaseExecution
	conf                o2o_ecom_model.Conf
	yztSubMerchantInfos []*finance_account.SubMerchantInfo
}

func NewCreateOrderExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &CreateOrderExecution{}
	t.conf = o2o_ecom_model.Conf{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), &t.conf)
	return t
}

func (e *CreateOrderExecution) CheckParams(ctx context.Context) error {
	var (
		req = e.GetCreateOrderReq()
	)
	//if err := e.CreateBaseExecution.CheckParams(ctx); err != nil {
	//	return err
	//}

	// O2O电商不校验
	//amount := req.GetTotalAmount()
	//if req.Identity.BizScene == platform_ecom.PlatformEcomEarnestScene.Value() {
	//	for _, v := range req.FinanceList {
	//		if v.FinanceOrderType == consts2.FinanceEarnest.Value() {
	//			amount = v.Amount
	//			break
	//		}
	//	}
	//}
	//if bizErr := utils.CheckFundRiskOfAmount(ctx, req.IsTest, amount, conv.Int64Ptr(consts.TestAmount)); bizErr != nil {
	//	logs.CtxWarn(ctx, "[createOrderExecution-CheckParams] CheckFundRiskOfAmount error, err = %v ", bizErr.Error())
	//	return bizErr
	//}

	// 校验费项
	for _, financeInfo := range req.GetFinanceList() {
		if len(financeInfo.FeeItemList) == 0 {
			return errdef.NewParamsErr("FeeItemList 不能为空")
		}
		tmpAmount := int64(0)
		for _, item := range financeInfo.FeeItemList {
			tmpAmount += item.Amount
		}
		if tmpAmount != financeInfo.Amount+financeInfo.LoanAmount+financeInfo.OfflineLoanAmount+financeInfo.PlatformPromotionAmount {
			return errdef.NewParamsErr("FeeItemList 费项金额之和跟资金单金额不等")
		}
	}

	// 校验资金账户开通
	bizErr := e.validFinanceAccount(ctx)
	if bizErr != nil {
		return bizErr
	}

	return nil
}

func (e *CreateOrderExecution) Process(ctx context.Context) error {
	var (
		req                      = e.GetCreateOrderReq()
		stateMachine             = e.GetStateMachine()
		fweOrder                 *db_model.FweOrder
		financeList              []*db_model.FFinanceOrder
		isTest                   int32
		uid, mobileID            int64
		promotionAmount          int64
		cashbackAmount           int64
		activityOrders           []*fwe_trade_common.ActivityOrder
		operatorID, operatorName string
		err                      error
		bizErr                   *errdef.BizErr
	)
	if req.IsTest {
		isTest = 1
	}

	defer func() {
		if bizErr != nil {
			// 如果有错误，发送订单取消消息
			cancelMessage := e.packCancelEvent(ctx, req)
			_ = ProduceOrderCancelMessage(ctx, cancelMessage)
		}
	}()

	if req.GetBuyerInfo().GetPersonInfo() != nil {
		uid = req.GetBuyerInfo().GetPersonInfo().GetUID()
	}
	if req.GetBuyerInfo().GetPersonInfo() != nil {
		mobileID = req.GetBuyerInfo().GetPersonInfo().GetMobileID()
	}
	if req.GetOperator() != nil {
		operatorID, operatorName = req.GetOperator().OperatorID, req.GetOperator().OperatorName
	}

	for _, finance := range req.FinanceList {
		promotionAmount += finance.PlatformPromotionAmount
	}

	if couponStr := req.GetOrderTag()[InfraCashbackCoupon]; couponStr != "" {
		if err = utils.Unmarshal(couponStr, &activityOrders); err != nil {
			bizErr = errdef.NewBizErrWithCode(errdef.DataErr, err)
			logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
			return bizErr
		}
		for _, actOrder := range activityOrders {
			cashbackAmount += actOrder.CouponAmount
		}
	}

	if err := stateMachine.Fire(ctx, platform_ecom.EcomOrderCreateEt.Value(), nil); err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "CreateOrderExecution")
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	fweOrder = &db_model.FweOrder{
		TenantType:         int32(req.GetIdentity().GetTenantType()),
		BizScene:           req.GetIdentity().BizScene,
		SmVersion:          req.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()), // 初始态
		OrderName:          req.GetOrderName(),
		OrderDesc:          req.GetOrderDesc(),
		ProductID:          req.ProductInfo.ProductID,
		ProductType:        int32(req.ProductInfo.ProductType),
		ProductName:        req.ProductInfo.ProductName,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(req.ProductInfo.ProductDetail)),
		ProductExtra:       req.ProductInfo.ProductExtra,
		SkuID:              req.ProductInfo.SkuID,
		SkuVersion:         req.ProductInfo.SkuVersion,
		UID:                uid,
		MobileID:           mobileID,
		ProductVersion:     req.ProductInfo.ProductVersion,
		ProductQuantity:    int32(req.ProductInfo.ProductQuantity),
		ProductUnitPrice:   req.ProductInfo.ProductUnitPrice,
		TotalAmount:        req.TotalAmount,
		TotalPayAmount:     0,
		TotalSubsidyAmount: promotionAmount + cashbackAmount,
		TradeType:          int32(req.TradeType),
		BuyerID:            packer.CommonTradeSubjectIDGet(req.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(req.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(req.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(req.SellerInfo)),
		IsTest:             isTest,
		Creator:            operatorID,
		CreatorName:        operatorName,
		Operator:           operatorID,
		OperatorName:       operatorName,
	}

	for _, finance := range req.FinanceList {
		financeList = append(financeList, &db_model.FFinanceOrder{
			TenantType:              int32(req.GetIdentity().GetTenantType()),
			BizScene:                req.GetIdentity().BizScene,
			OrderID:                 e.GetOrderID(),
			OrderName:               req.OrderName,
			TradeCategory:           int32(fwe_trade_common.TradeCategory_Pay),
			FinanceOrderID:          utils.MakeFinanceOrderIDTool(fweOrder.OrderID, finance.FinanceOrderType),
			FinanceOrderType:        finance.FinanceOrderType,
			Amount:                  finance.Amount,                  // 用户需要实际支付金额
			ProcessAmount:           0,                               // 用户实际支付过程金额
			LoanAmount:              0,                               // 线上贷款金额
			OfflineLoanAmount:       finance.OfflineLoanAmount,       // 线下贷款金额
			PlatformPromotionAmount: finance.PlatformPromotionAmount, // 平台优惠总金额
			PlatformPromotionDetail: conv.StringPtr(utils.MarshalToStr(finance.PlatformPromotionDetail)),
			Status:                  int32(fwe_trade_common.FinanceStatus_NotHandle),
			FeeItemDetail:           conv.StringPtr(utils.MarshalToStr(finance.FeeItemList)),
		})
	}

	// 扣减库存 todo: 区分超时， 需要补偿回补
	bizErr = e.decrStock(ctx, e.GetOrderID(), conv.Int64Default(req.ProductInfo.SkuID, 0))
	if bizErr != nil {
		logs.CtxError(ctx, "[createOrderExecution] decrStock err: %+v", bizErr)
		return bizErr
	}

	defer func() {
		// 如果存在error， 将库存回补
		if bizErr != nil {
			logs.CtxInfo(ctx, "[createOrderExecution] start incr stock")
			if tmpErr := IncrStock(ctx, e.GetOrderID(), conv.Int64Default(req.ProductInfo.SkuID, 0)); tmpErr != nil {
				logs.CtxError(ctx, "[createOrderExecution] IncrStock err: %+v", tmpErr)
				utils.EmitCounter("o2o_ecom_incr_stock_failed", 1)
				return
			}
		}
	}()

	// 下单
	order := &service_model.Order{
		FweOrder:    fweOrder,
		FinanceList: financeList,
		TagMap:      req.OrderTag,
		BizExtra:    req.Extra,
	}
	bizErr = service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[createOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *CreateOrderExecution) Result() interface{} {
	return e.GetOrderID()
}

func (e *CreateOrderExecution) validFinanceAccount(ctx context.Context) *errdef.BizErr {
	var (
		req          = e.GetCreateOrderReq()
		fweAccountID = e.GetCreateOrderReq().SellerInfo.GetFweMerchant().FweAccountID
		yztConfig    = e.conf.YZTPayMerchant
		yztChannels  = []finance_account.TradeChannel{
			finance_account.TradeChannel_yzt_hz,
			finance_account.TradeChannel_yzt_alipay,
			finance_account.TradeChannel_syt_wx,
		}
	)
	if yztConfig == nil || yztConfig.MerchantID == "" {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] config error, config = %v", tools.GetLogStr(e.conf))
		return errdef.NewRawErr(errdef.LackConfigErr, "lack yztConfig config")
	}
	fweSubMerchantInfo, bizErr := service.NewFinanceAccountService().GetFweSubMerchantInfo(ctx, req.Identity.TenantType, fweAccountID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] GetFweSubMerchantInfo error, err = %v", bizErr.Error())
		return bizErr
	}
	// 验证 yzt
	yztSubMerchantInfos, bizErr := e.checkFinanceAccount(yztConfig.MerchantID, fweSubMerchantInfo, yztChannels)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] checkFinanceAccount yzt-account error, err = %v", bizErr.Error())
		return bizErr
	}
	e.yztSubMerchantInfos = yztSubMerchantInfos

	return nil
}

func (e *CreateOrderExecution) checkFinanceAccount(merchantId string, fweSubMerchantInfo map[string][]*finance_account.SubMerchantInfo, validChannels []finance_account.TradeChannel) ([]*finance_account.SubMerchantInfo, *errdef.BizErr) {
	channels, exist := fweSubMerchantInfo[merchantId]
	if !exist {
		return nil, errdef.NewParamsErr("this seller dont own channels")
	}
	validRes := slices.Filter(channels, func(dto *finance_account.SubMerchantInfo) bool {
		if slices.Contains(validChannels, dto.TradeChannel) && dto.ChannelStatus == finance_account.ChannelStatus_Ready {
			return true
		}
		return false
	}).([]*finance_account.SubMerchantInfo)
	if len(validRes) == 0 {
		return nil, errdef.NewParamsErr("this seller`s all channels is not ready")
	}
	return validRes, nil
}

func (e *CreateOrderExecution) decrStock(ctx context.Context, orderID string, skuID int64) *errdef.BizErr {
	req := &product_stock.DecrStockForOneReq{
		BizOrder: &product_stock.BizOrder{
			BizType: tenant_base.TenantType_Platform,
			OrderId: orderID,
		},
		Item: &product_stock.ChangeStockItem{
			StockUnit: &product_stock.StockUnit{
				SkuId:     skuID,
				StockType: product_stock.StockType_NORMAL,
			},
			StockNum: conv.Int64Ptr(1),
		},
		BizExtra: nil,
		Base:     base.NewBase(),
	}
	bizErr := service.NewProductStockService().DecrStockOne(ctx, req)
	if bizErr != nil {
		logs.CtxError(ctx, "[decrStock] decr stock failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *CreateOrderExecution) approveActivityOrder(ctx context.Context, orderID string, list []*fwe_trade_common.ActivityOrder,
	operator *fwe_trade_common.OperatorInfo) *errdef.BizErr {
	var (
		errWg, errCtx = utils.WithContext(ctx)
	)

	if len(list) == 0 {
		return nil
	}

	for _, v := range list {
		order := v
		errWg.Go(errCtx, func(ctx context.Context) error {
			req := &engine2.ApproveOrderReq{
				ActOrderID: order.ActOrderID,
				OutID:      orderID,
				RealAmount: order.RealAmount,
				Operator:   operator,
				Base:       base2.NewBase(),
			}
			bizErr := service.NewActivityService().ApproveActivityOrder(ctx, req)
			if bizErr != nil {
				logs.CtxError(ctx, "[approveActivityOrder] approve failed, err=%s, actOrderID=%s", bizErr.Error(), order.ActOrderID)
				return bizErr
			}
			return nil
		})
	}

	if err := errWg.Wait(); err != nil {
		logs.CtxError(ctx, "[approveActivityOrder] approve failed, err=%s", err.Error())
		if bizErr, ok := err.(*errdef.BizErr); ok {
			return bizErr
		} else {
			return errdef.NewBizErrWithCode(errdef.ActivityRpcErr, err)
		}
	}
	return nil
}

func (e *CreateOrderExecution) freezeActivityOrders(ctx context.Context, req *engine.CreateOrderReq, orderID string, actOrders []*fwe_trade_common.ActivityOrder) *errdef.BizErr {
	var (
		product     = req.GetProductInfo()
		productID   int64
		accID       string
		orderSource = new(o2o_ecom_model.OrderSourceInfo)
		err         error
	)
	if product == nil {
		return nil
	}
	if productID, err = strconv.ParseInt(product.ProductID, 10, 64); err != nil {
		logs.CtxError(ctx, "[freezeActivityOrders] parse int failed, err=%+v", err)
		return errdef.NewBizErrWithCode(errdef.DataErr, err)
	}
	if req.GetExtra()[InfraOrderSourceKey] != "" {
		if err = utils.Unmarshal(req.GetExtra()[InfraOrderSourceKey], orderSource); err != nil {
			logs.CtxError(ctx, "[freezeActivityOrders] parse order source failed, err=%+v", err)
			return errdef.NewBizErrWithCode(errdef.DataErr, err)
		}
	}
	if req.GetOrderTag() == nil || req.GetOrderTag()[InfraManufactureAccountID] == "" {
		logs.CtxError(ctx, "[freezeActivityOrders] manufactID is empty")
		return errdef.NewRawErr(errdef.ParamErr, "manufactID is empty")
	}
	accID = req.GetOrderTag()[InfraManufactureAccountID]

	param := &service.BatchActivityParam{
		OrderID:    orderID,
		ProductID:  productID,
		FweAccID:   accID,
		List:       actOrders,
		LinkSource: orderSource.LinkSource,
		Operator:   req.GetOperator(),
	}
	bizErr := service.NewActivityService().BatchFreezeOrder(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[freezeActivityOrders] BatchFreezeOrder failed, err=%+v", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *CreateOrderExecution) packCancelEvent(ctx context.Context, req *engine.CreateOrderReq) *fwe_trade_common.OrderCancelMessage {
	return &fwe_trade_common.OrderCancelMessage{
		TenantType:  req.GetIdentity().GetTenantType(),
		BizScene:    req.GetIdentity().BizScene,
		SmVersion:   req.GetIdentity().SmVersion,
		Version:     "0",
		OrderID:     e.GetOrderID(),
		OrderEvent:  platform_ecom.EcomOrderCreateEt.Event,
		OrderStatus: -1,
		OrderSource: nil,
	}
}
