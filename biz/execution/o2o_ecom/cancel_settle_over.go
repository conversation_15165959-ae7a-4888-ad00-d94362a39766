package o2o_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"context"

	"code.byted.org/motor/fwe_trade_common/scene/platform_ecom"

	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
)

type CancelSettleOverExecution struct {
	*callback.UnionSettleCallbackExecution
}

func NewCancelSettleOverExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &CancelSettleOverExecution{}
	exe.UnionSettleCallbackExecution = callback.NewUnionSettleCallbackBaseExecution(ctx, sourceReq)
	return exe
}

func (e *CancelSettleOverExecution) PostProcess(ctx context.Context) error {
	if err := e.UnionSettleCallbackExecution.PostProcess(ctx); err != nil {
		return err
	}

	fweOrder := e.GetOrder().FweOrder
	orderInfo, _ := packer.OrderService2Common(e.GetOrder())

	// 发送订单取消消息
	cancelOrderMsg := &fwe_trade_common.OrderCancelMessage{
		TenantType:  tenant_base.TenantType(fweOrder.TenantType),
		BizScene:    fweOrder.BizScene,
		SmVersion:   fweOrder.SmVersion,
		Version:     "0",
		OrderID:     fweOrder.OrderID,
		OrderEvent:  platform_ecom.EcomCancelSettleFinishEt.Event,
		OrderStatus: int32(e.GetStateMachine().CurState()),
		OrderSource: orderInfo,
	}
	_ = ProduceOrderCancelMessage(ctx, cancelOrderMsg)

	IncrStock(ctx, fweOrder.OrderID, conv.Int64Default(fweOrder.SkuID, 0))

	return nil
}
