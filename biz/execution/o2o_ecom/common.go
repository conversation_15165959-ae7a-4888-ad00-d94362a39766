package o2o_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_ecom_product_common/lark_robot"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"
	overpass_tenant_base "code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/tenant_base"
	"context"

	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"encoding/json"

	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/types"

	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
)

const (
	InfraManufactureAccountID = "infra_manufacture_account_id"
	InfraDealerAccountID      = "infra_dealer_account_id"
	InfraO2OReceiveRole       = "infra_o2o_receive_role" // 收款角色
	O2OReceiveRoleManufacture = "manufacture"
	O2OReceiveRoleDealer      = "dealer"
	InfraCashbackCoupon       = "infra_cashback_coupon" // 返现优惠券
	InfraOrderSourceKey       = "infra_order_source"    // 订单来源
)

func ProduceOrderCancelMessage(ctx context.Context, cancelMessage *fwe_trade_common.OrderCancelMessage) *errdef.BizErr {
	cancelMessageBytes, _ := json.Marshal(cancelMessage)
	msg := types.NewDefaultMessage(consts.OrderCancelTopic, cancelMessageBytes)
	sendResp, err := caller.OrderCancelEventProducer.Send(ctx, msg)
	if err != nil {
		logs.CtxError(ctx, "[ProduceOrderCancelMessage] send message failed, err=%v, msg=%s", err, string(cancelMessageBytes))
		utils.EmitCounter("produce_order_cancel_msg.fail", 1)
		return errdef.NewBizErrWithCode(errdef.ProduceMsgErr, err)
	}
	logs.CtxInfo(ctx, "[ProduceOrderCancelMessage] produce msg: %s, res: %s", string(cancelMessageBytes), tools.GetLogStr(sendResp))
	return nil
}

func IncrStock(ctx context.Context, orderID string, skuID int64) *errdef.BizErr {
	req := &product_stock.IncrStockForOneReq{
		BizOrder: &product_stock.BizOrder{
			BizType: overpass_tenant_base.TenantType_Platform,
			OrderId: orderID,
		},
		Item: &product_stock.ChangeStockItem{
			StockUnit: &product_stock.StockUnit{
				SkuId:     skuID,
				StockType: product_stock.StockType_NORMAL,
			},
			StockNum: conv.Int64Ptr(1),
		},
		BizExtra: nil,
	}
	bizErr := service.NewProductStockService().IncrStockOne(ctx, req)
	if bizErr != nil {
		lark_robot.DefaultDiyLarkAlertBot(ctx).
			Level(lark_robot.AlertLevel_Error).
			Title("【O2O】关闭订单-库存回补失败").
			Content(map[string]string{
				"err":     bizErr.Error(),
				"orderID": orderID,
				"skuID":   conv.StringDefault(skuID, ""),
				"req":     tools.GetLogStr(req),
				"处理方案":    "手动调用【motor.fwe_ecom.product_stock】的【IncrStockOne】接口, req传参【req】中的内容",
			}).
			NotifyUserMap(map[string]string{
				"111": "姚文振",
				"222": "李沛洋",
				"333": "陈晨",
			}).
			SendCardMsg()

		logs.CtxError(ctx, "[IncrStock] IncrStockOne err: %+v, orderID: %+v, skuID: %+v", bizErr, orderID, skuID)
		return bizErr
	}
	return nil
}
