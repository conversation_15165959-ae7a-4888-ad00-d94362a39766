package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"context"
)

type afterRefundSettleExecution struct {
	*common.UnionSettleReverseExecution
	hasOnlinePay bool
}

func NewAfterRefundSettleExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &afterRefundSettleExecution{}
	e.UnionSettleReverseExecution = common.NewUnionSettleReverseBaseExecution(ctx, actionReq)
	return e
}

func (e *afterRefundSettleExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.UnionSettleReverseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxWarn(ctx, "[afterRefundSettleExecution-PreProcess] base PreProcess error", bizErr.Error())
		return bizErr
	}
	var (
		settleAmount = e.SettleAmount
	)
	param := &ShSellConditionParam{RemainAmount: settleAmount}
	conditions := GetShSellCondition(ctx, e.GetOrder(), param)
	hasOnlinePay, bizErr := checkHasOnlinePay(ctx, e.GetOrder().FweOrder)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[afterRefundSettleExecution-PreProcess] checkHasOnlinePay error, err = %v", bizErr.Error())
		return bizErr
	}
	e.hasOnlinePay = hasOnlinePay
	conditions[HasOnlinePay] = hasOnlinePay
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[afterRefundSettleExecution-PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	return nil
}

func (e *afterRefundSettleExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		settleAmount = e.SettleAmount
	)
	// 没有线上支付，不需要分账，直接结束
	if !e.hasOnlinePay {
		return nil
	}

	// 金额为0，直接结束
	if settleAmount == int64(0) {
		logs.CtxWarn(ctx, "[afterRefundSettleExecution-Process] settleAmount is 0, direct over", settleAmount)
		return nil
	}
	// 分账
	settleParam, bizErr := e.BuildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[afterRefundSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	mergeSettleNo, bizErr := service.NewUnionSettleService().UnionSettle(ctx, settleParam)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[afterRefundSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.BizRsp = execution_common.SettleRsp{
		MergeSettleNo: mergeSettleNo,
	}
	return nil
}
