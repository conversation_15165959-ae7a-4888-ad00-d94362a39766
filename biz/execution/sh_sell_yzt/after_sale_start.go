package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"context"
)

type AfterSaleStartExecution struct {
	*executor.ActionBaseExecution
	AfterSaleStartReq execution_common.AfterSaleStartReq
}

func NewAfterSaleStartExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &AfterSaleStartExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.AfterSaleStartReq)
	return e
}

func (e *AfterSaleStartExecution) Process(ctx context.Context) error {
	subStatusMap := e.GetStateMachine().GetOriginalSubStates()
	if !AllowCancelRefund(subStatusMap) {
		// 存在在途贷款资金，不允许发起售后
		return errdef.NewRawErr(errdef.FinanceFlowErr, "存在审批中/在途贷款资金，不允许发起售后")
	}
	// 是否需要售后检测
	conditionMap := map[string]interface{}{
		"needAfterSaleCheck": e.AfterSaleStartReq.NeedAfterSaleCheck,
	}
	if err := e.FireWithCondition(ctx, conditionMap); err != nil {
		logs.CtxWarn(ctx, "[AfterSaleStartExecution-Process] fire fsm failed, err=%+v", err.Error())
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}
