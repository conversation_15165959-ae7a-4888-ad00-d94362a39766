package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

type PayFinalOverExecution struct {
	*callback.UnionPayCallbackBaseExecution
}

// NewPayFinalOverExecution 尾款支付完成回调
func NewPayFinalOverExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &PayFinalOverExecution{}
	e.UnionPayCallbackBaseExecution = callback.NewUnionPayCallbackCommonBaseExecution(ctx, actionReq)
	return e
}

func (e *PayFinalOverExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[PayFinalOverExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}
	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PayFinalOverExecution-PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	return nil
}
