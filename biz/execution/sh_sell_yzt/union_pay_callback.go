package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/condition"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

// 二手车专用支付回调

type ShUnionPayCallbackBaseExecution struct {
	*callback.UnionPayCallbackBaseExecution
}

func NewShUnionPayCallbackBaseExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &ShUnionPayCallbackBaseExecution{}
	exe.UnionPayCallbackBaseExecution = callback.NewUnionPayCallbackCommonBaseExecution(ctx, sourceReq)
	return exe
}

func (e *ShUnionPayCallbackBaseExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[ShUnionPayCallbackBaseExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}
	var calCondition map[string]interface{}
	conditionFunc := condition.NewOrderConditionFuncFactory().GetActionOrderConditionFunc(ctx, e.GetActionOrderReq())
	if conditionFunc != nil {
		calCondition = conditionFunc(e.BuildConditionParam())
	}

	bizErr := e.FireWithCondition(ctx, calCondition)
	if bizErr != nil {
		logs.CtxError(ctx, "[ShUnionPayCallbackBaseExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
