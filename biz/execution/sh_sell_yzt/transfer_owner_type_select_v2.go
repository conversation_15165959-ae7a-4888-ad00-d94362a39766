package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/gopkg/tools"
	"context"
)

type selectTransferOwnerTypeV2Execution struct {
	*executor.ActionBaseExecution
	param *sh_sell_model.TransferOwnerTypeSelectModel
}

func NewSelectTransferOwnerTypeV2Execution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &selectTransferOwnerTypeV2Execution{
		param: new(sh_sell_model.TransferOwnerTypeSelectModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param)
	return e
}

func (e *selectTransferOwnerTypeV2Execution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	param := e.param
	if param == nil || (param.TransferOwnerType != TransferOwnerByPlatformStr && param.TransferOwnerType != TransferOwnerBySelfStr) {
		logs.CtxWarn(ctx, "[selectTransferOwnerTypeExecution.CheckParams] param error. param=%+v", tools.GetLogStr(param))
		return errdef.NewParamsErr("缺少平台过户方式参数")
	}
	return nil
}

func (e *selectTransferOwnerTypeV2Execution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}
	var (
		bizReq = e.param
		order  = e.GetOrder()
	)
	// 如果是异地交车订单，只能选择平台过户
	if order.TagMap[DeliveryCarType] == DeliveryCarTypeRemote && bizReq.TransferOwnerType != TransferOwnerByPlatformStr {
		return errdef.NewParamsErr("异地交车订单只能选择平台过户方式")
	}
	param := &ShSellConditionParam{TransferOwnerType: e.param.TransferOwnerType}
	conditions := GetShSellCondition(ctx, e.GetOrder(), param)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})

	return nil
}

// Process 设置贷款类型
func (e *selectTransferOwnerTypeV2Execution) Process(ctx context.Context) error {
	var (
		param    = e.param
		order    = e.GetOrder()
		fweOrder = order.FweOrder
		bizScene = e.GetBizIdentity().BizScene
	)
	order.TagMap[TransferOwnerType] = param.TransferOwnerType
	_, bizErr := service.NewTagService().UpdateTag(ctx, fweOrder.OrderID, bizScene, order.TagMap)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[selectTransferOwnerTypeExecution.Process] set tag failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
