package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	sdkUtils "code.byted.org/motor/fwe_trade_common/statemachine/utils"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"context"
)

type ShUnionRefundBaseExecution struct {
	*common.UnionRefundExecution
}

func NewShUnionRefundBaseExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &ShUnionRefundBaseExecution{}
	exe.UnionRefundExecution = common.NewUnionRefundBaseExecution(ctx, sourceReq)
	return exe
}

func (e *ShUnionRefundBaseExecution) buildCondition(ctx context.Context) (condition map[string]interface{}, bizErr *errdef.BizErr) {
	result := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	conditions := sdkUtils.BuildRefundCondition(e.RefundAmount)
	if len(conditions) > 0 {
		for key, inCondition := range conditions {
			result[key] = inCondition
		}
	}
	return result, nil
}

func (e *ShUnionRefundBaseExecution) Process(ctx context.Context) error {

	var (
		mergeRefundNo string
		bizErr        *errdef.BizErr
		refundAmount  = e.RefundAmount
	)
	// 跳转状态机
	conditions, _ := e.buildCondition(ctx)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[UnionRefundExecution-process] fire fsm failed, err=%+v", err.Error())
		return err
	}
	// 退款金额为0，直接结束
	if refundAmount == int64(0) {
		logs.CtxInfo(ctx, "[UnionRefundExecution-process] refund amount is 0")
		return nil
	}
	// refund req
	refundParam, bizErr := e.BuildRefundReq(ctx)
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr = service.NewUnionRefundService().UnionRefund(ctx, refundParam)
	if bizErr != nil {
		return bizErr
	}

	e.BizRsp = execution_common.RefundRsp{
		MergeRefundNo: mergeRefundNo,
	}
	return nil
}
