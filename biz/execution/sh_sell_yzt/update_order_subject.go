package sh_sell_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/motor/gopkg/tools/tools_recover"
	"context"
	"errors"
)

type simpleUpdateFweTradeOrderSubject struct {
	*executor.StaticBaseExecution
	conf  sh_sell_model.Conf
	param *engine.CreateOrderReq
}

func NewUpdateFweShTradeOrderSubjectExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &simpleUpdateFweTradeOrderSubject{
		param: new(engine.CreateOrderReq),
	}
	e.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param)
	return e
}

func (e *simpleUpdateFweTradeOrderSubject) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}
	if e.param == nil {
		return errdef.NewParamsErr("缺少更新订单参数")
	}
	var req = e.param
	if (req.BuyerInfo == nil || (req.BuyerInfo.PersonInfo == nil && req.BuyerInfo.CompanyInfo == nil && req.BuyerInfo.FweMerchant == nil)) &&
		(req.SellerInfo == nil || req.SellerInfo.FweMerchant == nil || req.SellerInfo.FweMerchant.FweAccountID == "") &&
		(req.ServiceProviderInfo == nil || req.ServiceProviderInfo.FweMerchant == nil || req.ServiceProviderInfo.FweMerchant.FweAccountID == "") &&
		(req.TalentInfo == nil || req.TalentInfo.FweMerchant == nil || req.TalentInfo.FweMerchant.FweAccountID == "") {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack buyer_info")
		return errors.New("缺少主体信息")
	}
	return nil
}

func (e *simpleUpdateFweTradeOrderSubject) Process(ctx context.Context) error {
	var (
		orderService        = service.NewOrderService()
		tagService          = service.NewTagService()
		buyerInfo           = e.param.BuyerInfo
		sellerInfo          = e.param.SellerInfo
		serviceProviderInfo = e.param.ServiceProviderInfo
		talentInfo          = e.param.TalentInfo
		order               = e.GetOrder()
		bizErr              *errdef.BizErr
		updateOrder         = e.param
		actionReq           = e.GetActionOrderReq()
		financeOrderList    = e.param.FinanceList
		financeOrderService = service.NewFinanceOrderService()
		orderID             = actionReq.GetOrderID()
		updateParams        = &service_model.UpdateOrderParams{
			Operator: actionReq.GetOperator(),
		}
	)
	if len(updateOrder.GetExtra()) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.GetExtra())
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}
	var (
		oldFinanceMap = make(map[int32]*db_model.FFinanceOrder, 0)
	)
	// 不修改 订单资金信息直接返回 不作预处理
	if len(e.param.FinanceList) > 0 {
		// 查询资金模型
		financeList := e.GetOrder().FinanceList
		for _, financeInfo := range financeList {
			oldFinanceMap[financeInfo.FinanceOrderType] = financeInfo
		}
	}

	if len(financeOrderList) > 0 {
		// 更新 资金单
		for _, financeInfo := range financeOrderList {
			oldFinance, exist := oldFinanceMap[financeInfo.FinanceOrderType]
			// 新增
			if !exist {
				//createFinanceOrderList = append(createFinanceOrderList, e.buildFinanceInfo(financeInfo))
				continue
			}
			err1 := financeOrderService.UpdateV2(ctx, financeInfo.FinanceOrderID, e.buildUpdateParam(financeInfo, oldFinance))
			if err1 != nil {
				logs.CtxError(ctx, "[NewUpdateFweShTradeOrderSubjectExecution-createOrUpdateFinanceOrder] UpdateV2 error, err = %v", err1.Error())
				return err1
			}
		}
	}

	if len(updateOrder.OrderTag) > 0 || len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(updateOrder.OrderTag) > 0 {
			for key, value := range updateOrder.OrderTag {
				tagMap[key] = value
			}
		}
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		_, bizErr = tagService.UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if updateOrder.Operator != nil {
		updateParams.Operator = updateOrder.Operator
	}

	if e.param.TotalAmount != int64(0) {
		updateParams.UpdateTotalAmount = conv.Int64Ptr(e.param.TotalAmount)
	}
	if int64(e.param.TradeType) != int64(0) {
		updateParams.UpdateTradeType = conv.Int32Ptr(int32(e.param.TradeType))
	}
	if buyerInfo != nil && buyerInfo.SubjectType > 0 {
		// 如果创建订单为个人类型且没有传mid 没有传mobile_id + 传了手机号
		if buyerInfo.SubjectType == fwe_trade_common.TradeSubjectType_Person &&
			buyerInfo.PersonInfo != nil && buyerInfo.PersonInfo.GetMobileID() == 0 && buyerInfo.PersonInfo.GetPersonPhone() != "" {

			mobileID, bizErrInner := service.NewToutiaoUserService().GetMobileIdByPhone(ctx, buyerInfo.GetPersonInfo().GetPersonPhone())
			if bizErrInner != nil {
				return bizErrInner
			}
			buyerInfo.PersonInfo.SetMobileID(&mobileID)
		}
		buyerID := packer.CommonTradeSubjectIDGet(buyerInfo)
		serialBuyerInfo := packer.CommonTradeSubjectSerialize(buyerInfo)
		updateParams.UpdateBuyerID = &buyerID
		updateParams.UpdateBuyerExtra = &serialBuyerInfo
	}
	if sellerInfo != nil && sellerInfo.SubjectType > 0 {
		buyerID := packer.CommonTradeSubjectIDGet(sellerInfo)
		serialBuyerInfo := packer.CommonTradeSubjectSerialize(sellerInfo)
		updateParams.UpdateSellerID = &buyerID
		updateParams.UpdateSellerExtra = &serialBuyerInfo
	}
	if serviceProviderInfo != nil && serviceProviderInfo.SubjectType > 0 {
		buyerID := packer.CommonTradeSubjectIDGet(serviceProviderInfo)
		serialBuyerInfo := packer.CommonTradeSubjectSerialize(serviceProviderInfo)
		updateParams.UpdateSProviderID = &buyerID
		updateParams.UpdateSProviderExtra = &serialBuyerInfo
	}
	if talentInfo != nil && talentInfo.SubjectType > 0 {
		buyerID := packer.CommonTradeSubjectIDGet(talentInfo)
		serialBuyerInfo := packer.CommonTradeSubjectSerialize(talentInfo)
		updateParams.UpdateTalentID = &buyerID
		updateParams.UpdateTalentExtra = &serialBuyerInfo
	}
	bizErr = orderService.UpdateOrder(ctx, order.FweOrder.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[simpleUpdateFweTradeOrder-Process] UpdateOrder error, err=%s", bizErr.Error())
		return bizErr
	}
	// 更新 ebs 数据
	go func() {
		defer func() {
			tools_recover.CheckRecover(ctx, recover())
		}()
		err := orderService.CreateOrUpdateOrderSubject(ctx, []*fwe_trade_common.TradeSubjectInfo{buyerInfo, serviceProviderInfo, talentInfo})
		if err != nil {
			logs.CtxError(ctx, "[CreateOrUpdateOrderSubject] error, err = %v", err.Error())
		}
	}()
	return nil
}
func (e *simpleUpdateFweTradeOrderSubject) buildUpdateParam(info *fwe_trade_common.FinanceInfo, oldFinance *db_model.FFinanceOrder) *service_model.UpdateFinanceParams {

	feeItemDetail := ""
	if len(info.FeeItemList) > 0 {
		feeItemDetail = tools.GetLogStr(info.FeeItemList)
	}
	res := &service_model.UpdateFinanceParams{
		// UpdateAmount:                  conv.Int64Ptr(info.Amount),
		// UpdateLoanAmount:              conv.Int64Ptr(info.LoanAmount),
		UpdatePlatformPromotionAmount: conv.Int64Ptr(info.PlatformPromotionAmount), // 暂时只更新平台补贴字段和费项字段
		//UpdateFinanceStatus:           conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_NotHandle)),
		UpdateFeeItemDetail: conv.StringPtr(feeItemDetail),
	}
	return res
}
