package sh_sell_yzt

const (
	LoanFirst = 1 // 先贷款后过户
	LoanAfter = 2 // 先过户后贷款

	LoanFirstStr = "loan_first"
	LoanAfterStr = "loan_after"

	LoanFirstName = "先贷款后过户"
	LoanAfterName = "先过户后贷款"

	TransferOwnerByPlatformStr = "Platform" // 平台过户
	TransferOwnerBySelfStr     = "Self"     // 自主过户

	LoanSubStatusStartStr = "Start" // 开始
	LoanSubStatusOverStr  = "Over"  // 结束

	DeliveryCarTypeRemote = "Remote" // 结束
)

const (
	OneHundredScale = 1 * 1e4
)

// tag 名称
const (
	LoanType   = "loan_type"   // 贷款类型 值为loan_first和loan_after
	LoanDetail = "loan_detail" // 贷款详情 值为LoanConfirmModel序列化后的值

	LoanSubStatus = "loan_sub_status" // 贷款子流程状态

	TransferOwnerType = "transfer_owner_type" // 过户类型 值为 Platform, Self

	DeliveryCarType = "delivery_car_type" // 交车类型 值为Local, Remote

)

// 飞书相关
const (
	LarkAppID                = "********************"
	LarkAppSecret            = "Vd0GipsjBmNbv7DQhmeY8eBSF3s8ZmwB"
	LarkAppVerificationToken = "f19MAp2kHpCGVh1XkYlfEdQ20i3v65SL"
	LarkEncryptKey           = "PYawhWlfpv0Chf8MtlQdqLnB2Smh1kc2"
)

// 审批单字段id
const (
	ApproveName        = "审批"
	OutBankAccountName = "out_bank_account_name"
	OutBankBranch      = "out_bank_branch"
	OutBankAccountNo   = "out_bank_account_no"
	CarVin             = "car_vin"
	TradeStructure     = "trade_structure"
	BorrowerName       = "borrower_name"
	LoanAmount         = "loan_amount"
	ShopName           = "shop_name"
	FinanceName        = "finance_name"
	BizOrderId         = "biz_order_id"
)
