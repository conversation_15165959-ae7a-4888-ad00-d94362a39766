package sh_sell_yzt

import (
	"context"

	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type confirmLoanOverExecution struct {
	*executor.ActionBaseExecution
	conf *sh_sell_model.Conf
}

func NewConfirmLoanOverExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &confirmLoanOverExecution{
		conf: new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, nil)
	return e
}

func (e *confirmLoanOverExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	hasOnlinePay, bizErr := checkHasOnlinePay(ctx, e.GetOrder().FweOrder)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[loanApproveSuccessExecution-PreProcess] checkHasOnlinePay error, err = %v", bizErr.Error())
		return bizErr
	}
	// 根据是否有线上支付跳转
	conditionMap := map[string]interface{}{
		HasOnlinePay: hasOnlinePay,
	}
	if err := e.FireWithCondition(ctx, conditionMap); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

// Process 主流程确认贷款完成后进行分账
func (e *confirmLoanOverExecution) Process(ctx context.Context) error {
	var (
		order = e.GetOrder()
	)
	if HasLoan(order) && !IsLoanSubStatusOver(order) {
		logs.CtxInfo(ctx, "[confirmLoanOverExecution.Process] no need settle")
		return errdef.NewRawErr(errdef.DataErr, "subStatus not over")
	}
	return nil
}
