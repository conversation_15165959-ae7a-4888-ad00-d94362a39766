package sh_sell_yzt

import (
	"context"

	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
)

type InvoiceSuccessExecution struct {
	*callback.InvoiceCallbackExecution
}

// NewInvoiceSuccessExecution 开票成功
func NewInvoiceSuccessExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &InvoiceSuccessExecution{}
	e.InvoiceCallbackExecution = callback.NewInvoiceCallbackBaseExecution(ctx, actionReq)
	return e
}

func (e *InvoiceSuccessExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[InvoiceSuccessExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}
	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[InvoiceSuccessExecution-PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	return nil
}
