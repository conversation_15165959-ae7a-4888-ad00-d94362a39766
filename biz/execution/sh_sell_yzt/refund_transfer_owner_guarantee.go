package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"context"
)

type refundTransferOwnerGuaranteeExecution struct {
	*common.UnionRefundExecution
}

func NewRefundTransferOwnerGuaranteeExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	e := &refundTransferOwnerGuaranteeExecution{}
	e.UnionRefundExecution = common.NewUnionRefundBaseExecution(ctx, sourceReq)
	return e
}

func (e *refundTransferOwnerGuaranteeExecution) PreProcess(ctx context.Context) error {
	bizErr := e.UnionRefundExecution.PreProcess(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", bizErr.Error())
		return bizErr
	}
	var (
		refundAmount = e.RefundAmount
	)
	param := &ShSellConditionParam{RefundTransferGuaranteeAmount: refundAmount}
	conditions := GetShSellCondition(ctx, e.GetOrder(), param)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

func (e *refundTransferOwnerGuaranteeExecution) Process(ctx context.Context) error {
	var (
		refundAmount = e.RefundAmount
		orderId      = e.GetOrder().FweOrder.OrderID
	)
	// 退款金额为0，结束
	if refundAmount == int64(0) {
		logs.CtxInfo(ctx, "[refundTransferOwnerGuaranteeExecution-Process] refundAmount is 0, dont handler", orderId)
		return nil
	}
	// refund req
	refundParam, bizErr := e.BuildRefundReq(ctx)
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr := service.NewUnionRefundService().UnionRefund(ctx, refundParam)
	if bizErr != nil {
		return bizErr
	}
	logs.CtxInfo(ctx, "[refundTransferOwnerGuaranteeExecution.Process] refund req success, refundNo=%s", mergeRefundNo)
	e.BizRsp = execution_common.RefundRsp{MergeRefundNo: mergeRefundNo}
	return nil
}
