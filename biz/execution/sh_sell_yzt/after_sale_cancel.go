package sh_sell_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"context"
)

type AfterSaleCancelExecution struct {
	*common.OrderStatusRollbackExecution
}

func NewAfterSaleCancelExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	exe := &AfterSaleCancelExecution{}
	exe.OrderStatusRollbackExecution = common.NewOrderStatusRollbackBaseExecution(ctx, actionReq)
	return exe
}

func (e *AfterSaleCancelExecution) Process(ctx context.Context) error {
	// 若存在生效中合同进行取消
	req := e.GetActionOrderReq()
	bizErr := service.NewContractService().CancelContract(ctx, &service_model.ContractCancelParam{
		BizScene:   e.GetOrder().FweOrder.BizScene,
		TenantType: e.GetOrder().FweOrder.TenantType,
		OrderID:    e.GetOrder().FweOrder.OrderID,
		ContType:   int32(consts.TerminationContType),
		Operator: &core.Operator{
			OperatorID:   conv.StrToInt64(req.Operator.OperatorID, 0),
			OperatorName: req.Operator.OperatorName,
		},
	})
	if bizErr != nil {
		if bizErr.Code() == int32(errdef.DataNotFound) { // 无合同 无需处理
			return nil
		}
		logs.CtxError(ctx, "[AfterSaleCancelExecution] CancelContract error err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
