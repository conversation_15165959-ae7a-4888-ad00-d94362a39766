package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/sh_sell_ef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"context"
)

type loanApproveSuccessExecution struct {
	*common.ConsumerLoanPassExecution
	hasOnlinePay bool
}

func NewLoanApproveSuccExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &loanApproveSuccessExecution{}
	e.ConsumerLoanPassExecution = common.NewAgreementPayBaseExecution(ctx, rpcReq)
	return e
}

func (e *loanApproveSuccessExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}
	hasOnlinePay, bizErr := checkHasOnlinePay(ctx, e.GetOrder().FweOrder)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[loanApproveSuccessExecution-PreProcess] checkHasOnlinePay error, err = %v", bizErr.Error())
		return bizErr
	}
	e.hasOnlinePay = hasOnlinePay
	// 设置条件：
	conditionMap := map[string]interface{}{
		HasOnlinePay: hasOnlinePay,
	}
	bizErr = e.FireWithCondition(ctx, conditionMap)
	if bizErr != nil {
		return bizErr
	}
	err = e.SetCallbackAction(sh_sell_ef.ShSellEFDCLoanSubProcessOverEvent.Value())
	if err != nil {
		logs.CtxWarn(ctx, "[loanApproveSuccessExecution-PreProcess] SetCallbackAction error, err = %v", err)
		return err
	}
	return nil
}

func (e *loanApproveSuccessExecution) Process(ctx context.Context) error {
	var (
		order    = e.GetOrder()
		fweOrder = e.GetOrder().FweOrder
	)
	// 没有在线支付，直接结束
	if !e.hasOnlinePay {
		logs.CtxWarn(ctx, "[loanApproveSuccessExecution-Process] hasNoOnlinePay, dont agreement", fweOrder.OrderID)
		// 更新tag
		order.TagMap[LoanSubStatus] = LoanSubStatusOverStr
		_, bizErr := service.NewTagService().UpdateTag(ctx, order.FweOrder.OrderID, e.GetBizIdentity().BizScene, order.TagMap)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[loanApproveSuccessExecution.Process] set tag failed, err=%s", bizErr.Error())
			return bizErr
		}
		return nil
	}
	return e.ConsumerLoanPassExecution.Process(ctx)
}
