package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/sh_sell_ef"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/gopkg/tools"
	"context"
)

type updateLoanSubStatusExecution struct {
	*executor.ActionBaseExecution
	conf  *sh_sell_model.Conf
	param *sh_sell_model.UpdateLoanSubStatusModel
}

func NewUpdateLoanSubStatusExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &updateLoanSubStatusExecution{
		conf:  new(sh_sell_model.Conf),
		param: new(sh_sell_model.UpdateLoanSubStatusModel),
	}

	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.param, opts...)
	return e
}

func (e *updateLoanSubStatusExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	loanSubStatus := e.getLoanSubStatus(ctx)
	if loanSubStatus == "" {
		logs.CtxWarn(ctx, "[updateLoanSubStatusExecution.CheckParams] param error. param=%+v", tools.GetLogStr(e.param))
		return errdef.NewParamsErr("贷款子状态参数错误")
	}
	return nil
}

// Process 设置贷款类型
func (e *updateLoanSubStatusExecution) Process(ctx context.Context) error {
	var order = e.GetOrder()
	order.TagMap[LoanSubStatus] = e.getLoanSubStatus(ctx)
	_, bizErr := service.NewTagService().UpdateTag(ctx, order.FweOrder.OrderID, e.GetBizIdentity().BizScene, order.TagMap)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[updateLoanSubStatusExecution.Process] set tag failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

var mapping = map[string]string{
	sh_sell_ef.ShSellEFDCLoanSubProcessStartEvent.Value(): LoanSubStatusStartStr,
	sh_sell_ef.ShSellEFDCLoanSubProcessOverEvent.Value():  LoanSubStatusOverStr,
}

// 获取贷款子状态
func (e *updateLoanSubStatusExecution) getLoanSubStatus(ctx context.Context) string {
	return mapping[e.GetActionOrderReq().Action]
}
