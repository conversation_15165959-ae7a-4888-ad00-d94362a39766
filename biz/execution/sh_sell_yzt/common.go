package sh_sell_yzt

import (
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/sh_consign_ef/common"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"context"
	"strconv"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

func CheckActionReq(ctx context.Context, actionReq *engine.ActionOrderReq) *errdef.BizErr {
	if actionReq == nil || actionReq.Identity == nil || actionReq.Identity.TenantType == 0 ||
		actionReq.Identity.BizScene == 0 || actionReq.OrderID == "" || actionReq.Action == "" {
		logs.CtxWarn(ctx, "[CheckActionReq] param empty")
		return errdef.NewParamsErr("有必传参数为空，请检查")
	}
	return nil
}

type ShSellConditionParam struct {
	LoanType                      int32
	RefundTransferGuaranteeAmount int64
	TransferOwnerType             string
	HasOfflinePay                 bool
	RemainAmount                  interface{}
	TagMap                        map[string]string
}

// GetShSellCondition 获取状态机条件
func GetShSellCondition(ctx context.Context, order *service_model.Order, param *ShSellConditionParam) map[string]interface{} {
	var (
		hasLoan, isLoanFirst                         bool
		IsPlatformTransferOwner, IsSelfTransferOwner bool
		IsLoanOver, isLoanNotStart, isSkipPartSettle bool
		fweOrder                                     = order.FweOrder
		tagMap                                       = order.TagMap
	)

	if fweOrder.TradeType == int32(fwe_trade_common.TradeType_EarnestFinalLoan) ||
		fweOrder.TradeType == int32(fwe_trade_common.TradeType_FinalLoan) {
		hasLoan = true
	}

	if loanType, exist := tagMap[LoanType]; exist && loanType == LoanFirstStr {
		isLoanFirst = true
	}
	if isSkipPartSettleStr, exist := param.TagMap[consts.CondParamIsSkipPartSettle]; exist {
		isSkipPartSettle, _ = strconv.ParseBool(isSkipPartSettleStr)
	}

	if param.LoanType == LoanFirst {
		isLoanFirst = true
	}

	if param.TransferOwnerType != "" {
		IsPlatformTransferOwner = param.TransferOwnerType == TransferOwnerByPlatformStr
		IsSelfTransferOwner = param.TransferOwnerType == TransferOwnerBySelfStr
	} else {
		if transferOwnerType, exist := tagMap[TransferOwnerType]; exist {
			IsPlatformTransferOwner = transferOwnerType == TransferOwnerByPlatformStr
			IsSelfTransferOwner = transferOwnerType == TransferOwnerBySelfStr
		}
	}
	isLoanNotStart = true
	if loanSbbStatus, exist := tagMap[LoanSubStatus]; exist {
		isLoanNotStart = false
		IsLoanOver = loanSbbStatus == LoanSubStatusOverStr
	}
	return map[string]interface{}{
		consts.CondShSellHasLoan.Val():                   hasLoan,
		consts.CondShSellIsLoanFirst.Val():               isLoanFirst,
		sh_state.CondShSellIsPlatformTransferOwner.Val(): IsPlatformTransferOwner,                                     // 是否平台过户
		sh_state.CondShSellIsSelfTransferOwner.Val():     IsSelfTransferOwner,                                         // 是否自主过户
		consts.CondParamRefundTransferGuaranteeAmount:    param.RefundTransferGuaranteeAmount,                         // 退还过户保证金金额
		sh_state.CondShSellIsLoanNotStart.Val():          isLoanNotStart,                                              // 是否未发起放贷
		sh_state.CondShSellIsLoanOver.Val():              IsLoanOver,                                                  // 是否已完成放贷
		sh_state.CondShSellIsOrderCancelled.Val():        IsOrderCanceled(order),                                      // 是否已取消订单
		sh_state.CondShSellIsNeedAgreementPay.Val():      !param.HasOfflinePay,                                        // 是否需要协议代付
		consts.CondParamRefundRemainingAmount:            param.RemainAmount,                                          // 退款剩余金额
		consts.CondParamIsSkipPartSettle:                 isSkipPartSettle,                                            // 是否部分分账
		common.CondShConsignIsRemoteDelivery.Val():       tagMap[DeliveryCarType] == DeliveryCarTypeRemote,            // 是否异地
		common.CondShConsignHasBigDeposit.Val():          tagMap[common.CondShConsignHasBigDeposit.Val()] == "true",   // 是否含大定
		common.CondShOrderIsJoinMerchant.Val():           tagMap[common.CondShOrderIsJoinMerchant.Val()] == "true",    // 是否加盟商订单
		common.CondShConsignIsLocalDispatch.Val():        tagMap[common.CondShConsignIsLocalDispatch.Val()] == "true", // 是否本地调度
	}
}

var OrderCancelStatus = []int32{
	101,   // 退款完成
	198,   // 取消退款中
	200,   // 已作废
	61101, // 取消退款后待分账
	61102, // 取消退款后分钟中
}

var tradeTypeList = []string{consts.TradeTypeYZT.String(), consts.TradeTypePayPos.String(), consts.TradeTypePayYztQr.String(),
	consts.TradeTypePayYztOfflineCash.String(), consts.TradeTypePayYztOfflineTransfer.String()}

func IsOrderCanceled(order *service_model.Order) bool {
	return slices.Contains(OrderCancelStatus, order.FweOrder.OrderStatus)
}

func HasLoan(order *service_model.Order) bool {
	var fweOrder = order.FweOrder
	if fweOrder.TradeType == int32(fwe_trade_common.TradeType_EarnestFinalLoan) ||
		fweOrder.TradeType == int32(fwe_trade_common.TradeType_FinalLoan) {
		return true
	}
	return false
}

func IsLoanSubStatusOver(order *service_model.Order) bool {
	var tagMap = order.TagMap
	if loanType, exist := tagMap[LoanSubStatus]; exist && loanType == LoanSubStatusOverStr {
		return true
	}
	return false
}

const (
	subStatusHead    int = 30
	loanApprovalWait int = 31
)

const (
	HasOnlinePay = "hasOnlinePay"
)

func AllowCancelRefund(subStatus map[int]int) bool {
	var value int
	if len(subStatus) == 0 {
		return true
	}
	value, exist := subStatus[subStatusHead]
	if exist && value != loanApprovalWait {
		return false
	}
	return true
}

func getLoadAmount(ctx context.Context, orderTag map[string]string) (int64, *errdef.BizErr) {
	valStr := orderTag[sh_sell_model.LoanAmountTag]
	if valStr == "" {
		return int64(0), nil
	}
	num, err := strconv.ParseInt(valStr, 10, 64)
	if err != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-getLoadAmount] ParseInt error, err = %v", err)
		return 0, errdef.NewParamsErr("orderTag loanAmount error")
	}
	return num, nil
}

// checkHasOnlinePay 线上支付 包括云直通离线支付
func checkHasOnlinePay(ctx context.Context, fweOrder *db_model.FweOrder) (bool, *errdef.BizErr) {
	// 查订单的第一笔支付单（线上支付或pos)
	firstPay, bizErr := service.NewTradePayment().QueryOneSuccessPay(ctx, fweOrder.OrderID, tradeTypeList)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[checkHasOnlinePay] QueryOneSuccessPay error, err = %v", bizErr.Error())
		return false, bizErr
	}
	return firstPay != nil, nil
}

func hasEffectivePay(_ context.Context, financeOrder *fwe_trade_common.FinanceInfo) bool {
	return len(financeOrder.PayList) > 0
}
