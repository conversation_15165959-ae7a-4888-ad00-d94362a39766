package sh_sell_yzt

import (
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"context"

	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type transferOwnerExecution struct {
	*executor.ActionBaseExecution
	conf   *sh_sell_model.Conf
	bizReq *execution_common.UpdateOrderReq
}

func NewTransferOwnerExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &transferOwnerExecution{
		conf:   new(sh_sell_model.Conf),
		bizReq: new(execution_common.UpdateOrderReq),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.bizReq)
	return e
}

func (e *transferOwnerExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

// Process 过户
func (e *transferOwnerExecution) Process(ctx context.Context) error {
	var (
		bizReq  = e.bizReq
		orderId = e.GetOrder().FweOrder.OrderID
	)
	_, bizErr := service.NewTagService().UpdateTag(ctx, orderId, e.GetActionOrderReq().Identity.GetBizScene(), bizReq.Tag)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[transferOwnerExecution-Process] UpdateTag error, err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}
