package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type ThirdPartyInvoiceExecution struct {
	*executor.ActionBaseExecution
}

// NewThirdPartyInvoiceExecution 第三方开票
func NewThirdPartyInvoiceExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &ThirdPartyInvoiceExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, nil)
	return e
}

func (e *ThirdPartyInvoiceExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[InvoiceSuccessExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}
	conditions := GetShSellCondition(ctx, e.GetOrder(), &ShSellConditionParam{})
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[InvoiceSuccessExecution-PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}
