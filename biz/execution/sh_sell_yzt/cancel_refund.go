package sh_sell_yzt

import (
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

type cancelRefundExecution struct {
	*common.UnionRefundExecution
}

// NewCancelRefundExecution 逆向取消订单并退款
func NewCancelRefundExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &cancelRefundExecution{}
	e.UnionRefundExecution = common.NewUnionRefundBaseExecution(ctx, actionReq)
	return e
}

func (e *cancelRefundExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.BizReq
	if bizReq.GetRuleID() != int64(0) {
		return nil
	}
	if bizReq.RefundList == nil || len(bizReq.RefundList) == 0 {
		bizErr := errdef.NewParamsErr("RefundList 参数错误")
		return bizErr
	}
	for _, refund := range bizReq.RefundList {
		if refund.Amount <= 0 {
			bizErr := errdef.NewParamsErr("RefundList.amount 参数错误")
			return bizErr
		}
	}
	return nil
}

func (e *cancelRefundExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.UnionRefundExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}
	var (
		subStatusMap = e.GetStateMachine().GetOriginalSubStates()
	)
	if !AllowCancelRefund(subStatusMap) {
		// 存在在途贷款资金，不允许取消
		return errdef.NewRawErr(errdef.FinanceFlowErr, "存在审批中/在途贷款资金，不允许取消")
	}
	return nil
}
