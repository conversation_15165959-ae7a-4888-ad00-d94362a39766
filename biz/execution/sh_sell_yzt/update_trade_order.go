package sh_sell_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	sdkConst "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/scene/sh_sell_ef/common"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	OrderRpc "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/motor/gopkg/tools/tools_recover_kite"
	"context"
	"errors"
	"strconv"
	"time"
	"unicode/utf8"
)

type simpleUpdateFweTradeOrder struct {
	*executor.ActionBaseExecution
	conf  sh_sell_model.Conf
	param *engine.CreateOrderReq

	oldFinanceMap     map[int32]*fwe_trade_common.FinanceInfo
	newFinanceInfoMap map[int32]*fwe_trade_common.FinanceInfo
}

func NewUpdateFweShTradeOrderExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &simpleUpdateFweTradeOrder{
		param: new(engine.CreateOrderReq),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param)
	return e
}

func (e *simpleUpdateFweTradeOrder) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}
	if e.param == nil {
		return errdef.NewParamsErr("缺少更新订单参数")
	}
	var req = e.param
	// 资金风控检查
	if bizErr = utils.CheckFundRiskOfAmount(ctx, req.IsTest, req.GetTotalAmount(), e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-CheckParams] CheckFundRiskOfAmount error, err = %v ", bizErr.Error())
		return bizErr
	}
	if req.ProductInfo == nil {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack product_info")
		return errors.New("lack product_info")
	}

	if req.BuyerInfo == nil || (req.BuyerInfo.PersonInfo == nil && req.BuyerInfo.CompanyInfo == nil && req.BuyerInfo.FweMerchant == nil) {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack buyer_info")
		return errors.New("lack buyer_info")
	}
	if req.SellerInfo == nil || req.SellerInfo.FweMerchant == nil || req.SellerInfo.FweMerchant.FweAccountID == "" {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack seller_info")
		return errors.New("lack seller_info")
	}
	if req.ProductInfo != nil && req.ProductInfo.ProductDetail != nil {
		if utf8.RuneCountInString(req.ProductInfo.GetProductDetail().GetProductHeadImageURI()) > 128 {
			logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] product_head_image_url illegal")
			return errdef.NewParamsErr("product_head_image_url length over 128")
		}
		if utf8.RuneCountInString(req.ProductInfo.GetProductDetail().GetProductDesc()) > 128 {
			logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] product_desc illegal")
			return errdef.NewParamsErr("product_desc length over 128")
		}
	}
	// 检查费项
	if len(e.param.FinanceList) > 0 {
		for _, financeInfo := range e.param.FinanceList {
			if len(financeInfo.FeeItemList) == 0 {
				return errdef.NewParamsErr("FeeItemList 不能为空")
			}
			tmpAmount := int64(0)
			for _, item := range financeInfo.FeeItemList {
				tmpAmount += item.Amount
			}
			if tmpAmount != financeInfo.Amount {
				return errdef.NewParamsErr("FeeItemList 费项金额之和跟资金单金额不等")
			}
		}
	}
	return nil
}

func (e *simpleUpdateFweTradeOrder) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}
	var (
		order             = e.GetOrder()
		oldFinanceMap     = make(map[int32]*fwe_trade_common.FinanceInfo, 0)
		newFinanceInfoMap = make(map[int32]*fwe_trade_common.FinanceInfo, 0)
	)
	// 不修改 订单资金信息直接返回 不作预处理
	if e.param.FinanceList == nil {
		return nil
	}
	// 查询资金模型
	req := &OrderRpc.QueryFinanceModelByOrderIDReq{
		OrderID:      order.FweOrder.OrderID,
		OrderType:    fwe_trade_common.OrderTypePtr(fwe_trade_common.OrderType_Trade),
		ReadStrategy: OrderRpc.ReadStrategyPtr(OrderRpc.ReadStrategy_ReadMaster),
	}
	financeModel, bizErr := service.NewOrderService().QueryFinanceModel(ctx, req)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[simpleUpdateFweTradeOrder-PreProcess] QueryFinanceModel error, err = %v", bizErr.Error())
		return bizErr
	}
	for _, financeInfo := range financeModel.FinanceList {
		oldFinanceMap[financeInfo.FinanceOrderType] = financeInfo
	}

	// getLoanData
	loanAmount := int64(0)
	feeItems := make([]*fwe_trade_common.FeeItem, 0)
	platformSubsidyAmount := int64(0)
	platformSubsidyFeeItems := make([]*fwe_trade_common.FeeItem, 0)
	for _, newFinance := range e.param.FinanceList {
		if newFinance.FinanceOrderType == sdkConst.FinanceLoan.Value() {
			loanAmount = newFinance.Amount
			feeItems = newFinance.FeeItemList
			continue
		} else if newFinance.FinanceOrderType == sdkConst.FinancePlatformSubsidy.Value() {
			platformSubsidyAmount = newFinance.Amount
			platformSubsidyFeeItems = newFinance.FeeItemList
			continue
		}
	}
	// newFinanceInfo
	for _, newFinance := range e.param.FinanceList {
		// 跳过贷款资金单
		if newFinance.FinanceOrderType == sdkConst.FinanceLoan.Value() || newFinance.FinanceOrderType == sdkConst.FinancePlatformSubsidy.Value() {
			continue
		}
		newFinanceInfoMap[newFinance.FinanceOrderType] = &fwe_trade_common.FinanceInfo{
			FinanceOrderType: newFinance.FinanceOrderType,
			Amount:           newFinance.Amount,
			FeeItemList:      newFinance.FeeItemList,
		}
	}
	// append loanAmount
	targetFinanceOrderType := sdkConst.FinanceFinal.Value()
	financeInfo := newFinanceInfoMap[targetFinanceOrderType]
	if financeInfo == nil {
		targetFinanceOrderType = sdkConst.FinanceTotal.Value()
		financeInfo = newFinanceInfoMap[sdkConst.FinanceTotal.Value()]
	}
	if financeInfo == nil {
		bizErr = errdef.NewParamsErr("缺少 final/total 类型的资金单")
		logs.CtxWarn(ctx, "[PreProcess] finance order type error", bizErr.Error())
		return bizErr
	}
	newItems, _ := mergeFeeItem(ctx, financeInfo.FeeItemList, feeItems)
	newItems2, _ := mergeFeeItem(ctx, newItems, platformSubsidyFeeItems)
	financeInfo.LoanAmount = loanAmount
	financeInfo.FeeItemList = newItems2
	financeInfo.PlatformPromotionAmount = platformSubsidyAmount
	newFinanceInfoMap[targetFinanceOrderType] = financeInfo

	e.oldFinanceMap = oldFinanceMap
	e.newFinanceInfoMap = newFinanceInfoMap
	return nil
}

func (e *simpleUpdateFweTradeOrder) Process(ctx context.Context) error {
	var (
		orderService           = service.NewOrderService()
		tagService             = service.NewTagService()
		buyerInfo              = e.param.BuyerInfo
		sellerInfo             = e.param.SellerInfo
		serviceProviderInfo    = e.param.ServiceProviderInfo
		talentInfo             = e.param.TalentInfo
		order                  = e.GetOrder()
		oldFinanceMap          = e.oldFinanceMap
		newFinanceInfoMap      = e.newFinanceInfoMap
		createFinanceOrderList = make([]*fwe_trade_common.FinanceInfo, 0)
		financeOrderService    = service.NewFinanceOrderService()
		bizErr                 *errdef.BizErr
		updateOrder            = e.param
		actionReq              = e.GetActionOrderReq()
		orderID                = actionReq.GetOrderID()
		stateMachine           = e.GetStateMachine()
		updateParams           = &service_model.UpdateOrderParams{
			UpdateOrderName:   &updateOrder.OrderName,
			UpdateOrderDesc:   &updateOrder.OrderDesc,
			UpdateTotalAmount: &updateOrder.TotalAmount,
			Operator:          actionReq.GetOperator(),
		}
	)
	// 驱动状态机流转
	// 解析状态机条件
	var fireCondition = make(map[string]interface{})
	auditBeforeStatus, _ := strconv.ParseInt(e.param.OrderTag["auditBeforeStatus"], 10, 64)
	fireCondition["auditBeforeStatus"] = auditBeforeStatus
	fireCondition[common.CondShSellIsOrderChangeEarnestType.Val()] = e.param.OrderTag[common.CondShSellIsOrderChangeEarnestType.Val()] == "true"
	fireCondition[common.CondShSellIsSkipEarnest.Val()] = e.param.OrderTag[common.CondShSellIsSkipEarnest.Val()] == "true"
	fireCondition[common.CondShSellIsSocialSale.Val()] = e.param.OrderTag[common.CondShSellIsSocialSale.Val()] == "true"
	bizErr = e.FireWithCondition(ctx, fireCondition)
	if bizErr != nil {
		logs.CtxError(ctx, "[OrderStatusRollbackExecution] fire failed, err=%s, fireCondition=%s", bizErr.Error(), tools.GetLogStr(fireCondition))
		return bizErr
	}

	if e.GetStateMachine().GetOriginalState() != e.GetStateMachine().CurState() {
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	}
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	updateParams.UpdateTradeType = conv.Int32Ptr(int32(e.param.TradeType))

	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}

	if len(updateOrder.GetExtra()) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.GetExtra())
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(updateOrder.OrderTag) > 0 || len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(updateOrder.OrderTag) > 0 {
			for key, value := range updateOrder.OrderTag {
				tagMap[key] = value
			}
		}
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		_, bizErr = tagService.UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if updateOrder.Operator != nil {
		updateParams.Operator = updateOrder.Operator
	}

	if e.param.TotalAmount != int64(0) {
		updateParams.UpdateTotalAmount = conv.Int64Ptr(e.param.TotalAmount)
	}
	if int64(e.param.TradeType) != int64(0) {
		updateParams.UpdateTradeType = conv.Int32Ptr(int32(e.param.TradeType))
	}
	if buyerInfo != nil {
		// 如果创建订单为个人类型且没有传mid 没有传mobile_id + 传了手机号
		if buyerInfo.SubjectType == fwe_trade_common.TradeSubjectType_Person &&
			buyerInfo.PersonInfo != nil && buyerInfo.PersonInfo.GetMobileID() == 0 && buyerInfo.PersonInfo.GetPersonPhone() != "" {

			mobileID, bizErrInner := service.NewToutiaoUserService().GetMobileIdByPhone(ctx, buyerInfo.GetPersonInfo().GetPersonPhone())
			if bizErrInner != nil {
				return bizErrInner
			}
			buyerInfo.PersonInfo.SetMobileID(&mobileID)
		}
		buyerID := packer.CommonTradeSubjectIDGet(buyerInfo)
		serialBuyerInfo := packer.CommonTradeSubjectSerialize(buyerInfo)
		updateParams.UpdateBuyerID = &buyerID
		updateParams.UpdateBuyerExtra = &serialBuyerInfo
	}
	if sellerInfo != nil {
		buyerID := packer.CommonTradeSubjectIDGet(sellerInfo)
		serialBuyerInfo := packer.CommonTradeSubjectSerialize(sellerInfo)
		updateParams.UpdateSellerID = &buyerID
		updateParams.UpdateSellerExtra = &serialBuyerInfo
	}
	if serviceProviderInfo != nil {
		buyerID := packer.CommonTradeSubjectIDGet(serviceProviderInfo)
		serialBuyerInfo := packer.CommonTradeSubjectSerialize(serviceProviderInfo)
		updateParams.UpdateSProviderID = &buyerID
		updateParams.UpdateSProviderExtra = &serialBuyerInfo
	}
	if talentInfo != nil {
		buyerID := packer.CommonTradeSubjectIDGet(talentInfo)
		serialBuyerInfo := packer.CommonTradeSubjectSerialize(talentInfo)
		updateParams.UpdateTalentID = &buyerID
		updateParams.UpdateTalentExtra = &serialBuyerInfo
	}
	bizErr = orderService.UpdateOrder(ctx, order.FweOrder.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[simpleUpdateFweTradeOrder-Process] UpdateOrder error, err=%s", bizErr.Error())
		return bizErr
	}
	// 更新 资金单
	for _, financeInfo := range newFinanceInfoMap {
		oldFinance, exist := oldFinanceMap[financeInfo.FinanceOrderType]
		// 新增
		if !exist {
			createFinanceOrderList = append(createFinanceOrderList, e.buildFinanceInfo(financeInfo))
			continue
		}
		// 存在有效支付，不允许更新
		if hasEffectivePay(ctx, oldFinance) {
			continue
		}
		err1 := financeOrderService.UpdateV2(ctx, oldFinance.FinanceOrderID, e.buildUpdateParam(financeInfo, oldFinance))
		if err1 != nil {
			logs.CtxError(ctx, "[confirmOrderExecution-createOrUpdateFinanceOrder] UpdateV2 error, err = %v", err1.Error())
			return err1
		}
	}
	// 关闭资金单
	for _, financeOrder := range oldFinanceMap {
		_, exist := newFinanceInfoMap[financeOrder.FinanceOrderType]
		// 存在新数据，跳过
		if exist {
			continue
		}
		// 具有有效支付了，不允许关闭
		if hasEffectivePay(ctx, financeOrder) {
			continue
		}
		err1 := financeOrderService.UpdateV2(ctx, financeOrder.FinanceOrderID, e.buildUpdateParamWithClose())
		if err1 != nil {
			logs.CtxError(ctx, "[confirmOrderExecution-createOrUpdateFinanceOrder] UpdateV2 error, err = %v", err1.Error())
			return err1
		}
	}
	// 新增资金单
	if len(createFinanceOrderList) != 0 {
		baseOrder := &service_model.OrderBaseParam{
			Identity:  e.GetBizIdentity(),
			OrderID:   order.FweOrder.OrderID,
			OrderName: order.FweOrder.OrderName,
		}
		bizErr = service.NewFinanceOrderService().CreateV2(ctx, baseOrder, createFinanceOrderList)
		if bizErr != nil {
			logs.CtxError(ctx, "[confirmOrderExecution-createOrUpdateFinanceOrder] CreateV2 error, err = %v", bizErr.Error())
			return bizErr
		}
	}
	// 更新 ebs 数据
	go func() {
		defer func() {
			tools_recover_kite.CheckRecover(ctx, recover(), nil)
		}()
		err := orderService.CreateOrUpdateOrderSubject(ctx, []*fwe_trade_common.TradeSubjectInfo{buyerInfo})
		if err != nil {
			logs.CtxError(ctx, "[CreateOrUpdateOrderSubject] error, err = %v", err.Error())
		}
	}()
	return nil
}

func (e *simpleUpdateFweTradeOrder) buildFinanceInfo(info *fwe_trade_common.FinanceInfo) *fwe_trade_common.FinanceInfo {
	var (
		fweOrder = e.GetOrder().FweOrder
	)
	res := &fwe_trade_common.FinanceInfo{
		FinanceOrderID:          utils.MakeFinanceOrderIDTool(fweOrder.OrderID, info.FinanceOrderType),
		FinanceOrderType:        info.FinanceOrderType,
		PayStatus:               fwe_trade_common.FinanceStatus_NotHandle,
		Amount:                  info.Amount,
		TradeCategory:           fwe_trade_common.TradeCategory_Pay,
		FeeItemList:             info.FeeItemList,
		LoanAmount:              info.LoanAmount,
		PlatformPromotionAmount: info.PlatformPromotionAmount,
	}
	return res
}

func (e *simpleUpdateFweTradeOrder) buildUpdateParam(info *fwe_trade_common.FinanceInfo, oldFinance *fwe_trade_common.FinanceInfo) *service_model.UpdateFinanceParams {

	feeItemDetail := ""
	if len(info.FeeItemList) > 0 {
		feeItemDetail = tools.GetLogStr(info.FeeItemList)
	}
	res := &service_model.UpdateFinanceParams{
		UpdateAmount:                  conv.Int64Ptr(info.Amount),
		UpdateLoanAmount:              conv.Int64Ptr(info.LoanAmount),
		UpdatePlatformPromotionAmount: conv.Int64Ptr(info.PlatformPromotionAmount),
		UpdateFinanceStatus:           conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_NotHandle)),
		UpdateFeeItemDetail:           conv.StringPtr(feeItemDetail),
	}
	return res
}

func (e *simpleUpdateFweTradeOrder) buildUpdateParamWithClose() *service_model.UpdateFinanceParams {
	res := &service_model.UpdateFinanceParams{
		UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Closed)),
	}
	return res
}
