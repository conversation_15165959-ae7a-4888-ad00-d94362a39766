package sh_sell_yzt

import (
	"code.byted.org/motor/fwe_trade_common/scene/sh_consign_ef"
	"code.byted.org/motor/gopkg/tools/tools_recover"
	"context"
	"errors"
	"strconv"
	"sync"
	"unicode/utf8"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/maps"
	"code.byted.org/gopkg/lang/slices"
	sdkConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/scene/sh_sell_ef/common"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type createOrderExecution struct {
	*executor.CreateBaseExecution
	conf                sh_sell_model.Conf
	yztSubMerchantInfos []*finance_account.SubMerchantInfo
}

func NewCreateOrderExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.CreateOrderReq)
	e := &createOrderExecution{}
	e.conf = sh_sell_model.Conf{}
	e.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, req, &e.conf)
	return e
}

func (e *createOrderExecution) CheckParams(ctx context.Context) error {
	var (
		req = e.GetCreateOrderReq()
	)
	// 资金风控检查
	if bizErr := utils.CheckFundRiskOfAmount(ctx, req.IsTest, req.GetTotalAmount(), e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-CheckParams] CheckFundRiskOfAmount error, err = %v ", bizErr.Error())
		return bizErr
	}
	if req.ProductInfo == nil {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack product_info")
		return errors.New("lack product_info")
	}

	if req.BuyerInfo == nil || (req.BuyerInfo.PersonInfo == nil && req.BuyerInfo.CompanyInfo == nil && req.BuyerInfo.FweMerchant == nil) {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack buyer_info")
		return errors.New("缺少买方信息")
	}
	// 寄售场景，一定要有场景方(并且订单版本号>=11)
	if slices.ContainsInt32([]int32{sh_consign_ef.SHConsignEFScene.Value()}, req.Identity.BizScene) && req.Identity.SmVersion >= 11 {
		if req.ServiceProviderInfo == nil || req.ServiceProviderInfo.FweMerchant == nil || req.ServiceProviderInfo.FweMerchant.FweAccountID == "" {
			logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack ServiceProviderInfo")
			return errors.New("缺少场景方信息")
		}
	}
	if req.SellerInfo == nil || req.SellerInfo.FweMerchant == nil || req.SellerInfo.FweMerchant.FweAccountID == "" {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack seller_info")
		return errors.New("缺少卖方信息")
	}
	if req.ProductInfo.ProductDetail != nil {
		if utf8.RuneCountInString(req.ProductInfo.GetProductDetail().GetProductHeadImageURI()) > 128 {
			logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] product_head_image_url illegal")
			return errdef.NewParamsErr("product_head_image_url length over 128")
		}
		if utf8.RuneCountInString(req.ProductInfo.GetProductDetail().GetProductDesc()) > 128 {
			logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] product_desc illegal")
			return errdef.NewParamsErr("product_desc length over 128")
		}
	}
	// 验证 账户
	bizErr := e.validFinanceAccount(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-CheckParams] validFinanceAccount error , err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *createOrderExecution) PreProcess(ctx context.Context) error {
	err := e.CreateBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] PreProcess error, err = %v", err)
		return err
	}
	var createReq = e.GetCreateOrderReq()
	condMap := make(map[string]interface{})
	// 是否需要支付订金
	value, exist := createReq.OrderTag[common.CondShSellIsSkipEarnest.Val()]
	if exist {
		boolValue, err := strconv.ParseBool(value)
		if err != nil {
			logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] ParseOrderTag error, err = %v", err)
			return errdef.NewParamsErr("orderTag ParseBool error, err = %v")
		}
		condMap[common.CondShSellIsSkipEarnest.Val()] = boolValue
	}
	// 是否跳过订金合同
	value, exist = createReq.OrderTag[common.CondShSellNoIntentionContract.Val()]
	if exist {
		boolValue, inErr := strconv.ParseBool(value)
		if inErr != nil {
			logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] ParseOrderTag error, err = %v", inErr)
			return errdef.NewParamsErr("orderTag ParseBool error, err = %v")
		}
		condMap[common.CondShSellNoIntentionContract.Val()] = boolValue
	}
	// 是否是社会化销售创建订单
	value, exist = createReq.OrderTag[common.CondShSellIsSocialSale.Val()]
	if exist {
		boolValue, inErr := strconv.ParseBool(value)
		if inErr != nil {
			logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] ParseOrderTag error, err = %v", inErr)
			return errdef.NewParamsErr("orderTag ParseBool error, err = %v")
		}
		condMap[common.CondShSellIsSocialSale.Val()] = boolValue
	} else {
		condMap[common.CondShSellIsSocialSale.Val()] = false
	}
	// 是否有大定
	value, exist = createReq.OrderTag[common.CondShConsignHasBigDeposit.Val()]
	if exist {
		boolValue, inErr := strconv.ParseBool(value)
		if inErr != nil {
			logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] ParseOrderTag error, err = %v", inErr)
			return errdef.NewParamsErr("orderTag ParseBool error, err = %v")
		}
		condMap[common.CondShConsignHasBigDeposit.Val()] = boolValue
	} else {
		condMap[common.CondShConsignHasBigDeposit.Val()] = false
	}
	bizErr := e.FireWithCondition(ctx, condMap)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] FireWithCondition error, err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *createOrderExecution) Process(ctx context.Context) error {

	// 拼数据
	var (
		req                  = e.GetCreateOrderReq()
		fweOrder             *db_model.FweOrder
		financeList          []*db_model.FFinanceOrder
		totalAmount          = req.TotalAmount
		buyerID              = packer.CommonTradeSubjectIDGet(req.BuyerInfo)
		buyerExtra           = packer.CommonTradeSubjectSerialize(req.BuyerInfo)
		sellerID             = packer.CommonTradeSubjectIDGet(req.SellerInfo)
		sellerExtra          = packer.CommonTradeSubjectSerialize(req.SellerInfo)
		serviceProviderID    = packer.CommonTradeSubjectIDGet(req.ServiceProviderInfo)
		serviceProviderExtra = packer.CommonTradeSubjectSerialize(req.ServiceProviderInfo)
		talentID             = packer.CommonTradeSubjectIDGet(req.TalentInfo)
		talentExtra          = packer.CommonTradeSubjectSerialize(req.TalentInfo)
	)

	var isTest int32
	if req.IsTest {
		isTest = 1
	}
	fweOrder = &db_model.FweOrder{
		TenantType:         int32(req.GetIdentity().GetTenantType()),
		BizScene:           req.GetIdentity().BizScene,
		SmVersion:          req.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(e.GetStateMachine().CurState()), // 初始态
		OrderName:          req.GetOrderName(),
		OrderDesc:          req.GetOrderDesc(),
		ProductID:          req.ProductInfo.ProductID,
		ProductType:        int32(req.ProductInfo.ProductType),
		ProductName:        req.ProductInfo.ProductName,
		ProductExtra:       req.ProductInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(req.ProductInfo.ProductDetail)),
		SkuID:              req.ProductInfo.SkuID,
		ProductQuantity:    int32(req.ProductInfo.ProductQuantity),
		ProductUnitPrice:   req.ProductInfo.ProductUnitPrice,
		TotalAmount:        totalAmount,
		TotalPayAmount:     totalAmount,
		TotalSubsidyAmount: 0,
		TradeType:          int32(req.TradeType),
		BuyerID:            buyerID,
		BuyerExtra:         &buyerExtra,
		SellerID:           sellerID,
		SellerExtra:        &sellerExtra,
		IsTest:             isTest,
		IdempotentID:       req.GetIdemID(),
		ServiceProviderID:  serviceProviderID,
		TalentID:           talentID,
	}
	if serviceProviderExtra != "" {
		fweOrder.ServiceProviderExtra = &serviceProviderExtra
	}
	if talentExtra != "" {
		fweOrder.TalentExtra = &talentExtra
	}

	// 获取贷款信息
	loanAmount, loanFeeItems, bizErr := getLoanData(ctx, req.FinanceList, req.OrderTag)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-Process] getLoanData error, err = %v", bizErr.Error())
		return bizErr
	}
	platformSubsidyAmount, platformSubsidyFeeItems, bizErr := getPlatformSubsidyData(ctx, req.FinanceList)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-Process] getPlatformSubsidyData error, err = %v", bizErr.Error())
		return bizErr
	}
	if bizErr := utils.CheckFundRiskOfAmount(ctx, req.IsTest, loanAmount, e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-Process] CheckFundRiskOfAmount error, err = %v", bizErr.Error())
		return bizErr
	}

	var addedLoan = false
	for _, financeInfo := range req.FinanceList {
		// 跳过贷款
		if financeInfo.FinanceOrderType == sdkConsts.FinanceLoan.Value() || financeInfo.FinanceOrderType == sdkConsts.FinancePlatformSubsidy.Value() {
			continue
		}
		financeOrderID, err := utils.TryGenId(3)
		if err != nil {
			logs.CtxError(ctx, "[createOrderExecution] gen id error, err=%+v", err)
			return err
		}
		if (financeInfo.FinanceOrderType == sdkConsts.FinanceFinal.Value() || financeInfo.FinanceOrderType == sdkConsts.FinanceTotal.Value()) && !addedLoan {
			feeItems, err := mergeFeeItem(ctx, financeInfo.FeeItemList, loanFeeItems)
			if err != nil {
				logs.CtxError(ctx, "[createOrderExecution] mergeFeeItem error, err=%+v", err.Error())
				return err
			}
			feeItems, err = mergeFeeItem(ctx, feeItems, platformSubsidyFeeItems)
			if err != nil {
				logs.CtxError(ctx, "[createOrderExecution] mergeFeeItem error, err=%+v", err.Error())
				return err
			}
			financeInfo.FeeItemList = feeItems
			financeInfo.LoanAmount = loanAmount
			financeInfo.PlatformPromotionAmount = platformSubsidyAmount
			addedLoan = true
		}
		financeOrder := &db_model.FFinanceOrder{
			TenantType:              int32(req.GetIdentity().GetTenantType()),
			BizScene:                req.GetIdentity().BizScene,
			AppID:                   "",
			MerchantID:              "",
			Mid:                     "",
			OrderID:                 e.GetOrderID(),
			OrderName:               req.OrderName,
			TradeType:               financeInfo.TradeType,
			TradeCategory:           int32(fwe_trade_common.TradeCategory_Pay),
			FinanceOrderID:          utils.MakeFinanceOrderID(financeOrderID, financeInfo.FinanceOrderType),
			FinanceOrderType:        financeInfo.FinanceOrderType,
			Amount:                  financeInfo.Amount,
			ProcessAmount:           0,
			Status:                  int32(fwe_trade_common.FinanceStatus_NotHandle),
			FeeItemDetail:           conv.StringPtr(tools.GetLogStr(financeInfo.FeeItemList)),
			LoanAmount:              financeInfo.LoanAmount,
			PlatformPromotionAmount: financeInfo.PlatformPromotionAmount,
		}
		financeList = append(financeList, financeOrder)
	}

	order := &service_model.Order{
		FweOrder:       fweOrder,
		FinanceList:    financeList,
		TagMap:         req.OrderTag,
		BizExtra:       req.Extra,
		TradeSplitInfo: req.SplitInfo,
	}
	bizErr = service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[createOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *createOrderExecution) Result() interface{} {
	return e.GetOrderID()
}

var (
	yztChannels = []finance_account.TradeChannel{
		finance_account.TradeChannel_yzt_hz,
		finance_account.TradeChannel_yzt_alipay,
		finance_account.TradeChannel_syt_wx,
	}

	posChannels = []finance_account.TradeChannel{
		finance_account.TradeChannel_hz,
	}
)

func (e *createOrderExecution) validFinanceAccount(ctx context.Context) *errdef.BizErr {
	var (
		req                         = e.GetCreateOrderReq()
		sellerInfo                  = req.GetSellerInfo().GetFweMerchant()
		fweAccountID                = packer.GetFinanceAccountId(sellerInfo.GetFinanceAccountID(), sellerInfo.GetFweAccountID())
		yztConfig                   = e.conf.YZTPayMerchant
		posConfig                   = e.conf.POSPayMerchant
		serviceProviderFweAccountID = ""
		talentFweAccountID          = ""
	)
	if e.GetCreateOrderReq().ServiceProviderInfo != nil && e.GetCreateOrderReq().ServiceProviderInfo.FweMerchant != nil {
		serviceProviderFweAccountID = packer.GetFinanceAccountId(e.GetCreateOrderReq().ServiceProviderInfo.FweMerchant.GetFinanceAccountID(), e.GetCreateOrderReq().ServiceProviderInfo.FweMerchant.GetFweAccountID())
	}
	if e.GetCreateOrderReq().TalentInfo != nil && e.GetCreateOrderReq().TalentInfo.FweMerchant != nil {
		talentFweAccountID = packer.GetFinanceAccountId(e.GetCreateOrderReq().TalentInfo.FweMerchant.GetFinanceAccountID(), e.GetCreateOrderReq().TalentInfo.FweMerchant.GetFweAccountID())
	}
	if yztConfig == nil || yztConfig.MerchantID == "" || posConfig == nil || posConfig.MerchantID == "" {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] config error, config = %v", tools.GetLogStr(e.conf))
		return errdef.NewRawErr(errdef.LackConfigErr, "yztConfig/posConfig config error")
	}
	var err1, err2, err3 *errdef.BizErr
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer func() {
			tools_recover.CheckRecover(ctx, recover())
			wg.Done()
		}()
		fweSubMerchantInfo, bizErr := service.NewFinanceAccountService().GetFweSubMerchantInfo(ctx, req.Identity.TenantType, fweAccountID)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] GetFweSubMerchantInfo error, err = %v", bizErr.Error())
			err1 = bizErr
			return
		}
		// 验证 yzt
		yztSubMerchantChannels, bizErr := checkFinanceAccount(yztConfig.MerchantID, fweSubMerchantInfo, yztChannels)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] checkFinanceAccount yzt-account error, err = %v", bizErr.Error())
			err1 = bizErr
			return
		}
		e.yztSubMerchantInfos = yztSubMerchantChannels
		isPersonConsign, _ := strconv.ParseBool(req.OrderTag["isPersonalConsign"])
		// 验证 pos，只有在非个人寄售的情况下才验证
		if !isPersonConsign {
			_, bizErr = checkFinanceAccount(posConfig.MerchantID, fweSubMerchantInfo, posChannels)
			if bizErr != nil {
				logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] checkFinanceAccount pos(hz)-account error, err = %v", bizErr.Error())
				err1 = bizErr
				return
			}
		}
	}()
	// 验证场景方云直通账户
	if serviceProviderFweAccountID != "" {
		wg.Add(1)
		go func() {
			defer func() {
				tools_recover.CheckRecover(ctx, recover())
				wg.Done()
			}()
			fweSubMerchantInfo, bizErr := service.NewFinanceAccountService().GetFweSubMerchantInfo(ctx, req.Identity.TenantType, serviceProviderFweAccountID)
			if bizErr != nil {
				logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] GetFweSubMerchantInfo error, err = %v", bizErr.Error())
				err2 = bizErr
				return
			}
			// 验证 yzt
			yztSubMerchantChannels, bizErr := checkFinanceAccount(yztConfig.MerchantID, fweSubMerchantInfo, yztChannels)
			if bizErr != nil {
				logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] checkFinanceAccount yzt-account error, err = %v", bizErr.Error())
				err2 = bizErr
				return
			}
			e.yztSubMerchantInfos = yztSubMerchantChannels
			// 验证pos
			_, bizErr = checkFinanceAccount(posConfig.MerchantID, fweSubMerchantInfo, posChannels)
			if bizErr != nil {
				logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] checkFinanceAccount pos(hz)-account error, err = %v", bizErr.Error())
				err2 = bizErr
				return
			}
		}()
	}
	// 验证销售方云直通账户
	if talentFweAccountID != "" {
		wg.Add(1)
		go func() {
			defer func() {
				tools_recover.CheckRecover(ctx, recover())
				wg.Done()
			}()
			fweSubMerchantInfo, bizErr := service.NewFinanceAccountService().GetFweSubMerchantInfo(ctx, req.Identity.TenantType, talentFweAccountID)
			if bizErr != nil {
				logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] GetFweSubMerchantInfo error, err = %v", bizErr.Error())
				err3 = bizErr
				return
			}
			// 验证 yzt
			yztSubMerchantChannels, bizErr := checkFinanceAccount(yztConfig.MerchantID, fweSubMerchantInfo, yztChannels)
			if bizErr != nil {
				logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] checkFinanceAccount yzt-account error, err = %v", bizErr.Error())
				err3 = bizErr
				return
			}
			e.yztSubMerchantInfos = yztSubMerchantChannels
			// 验证pos
			_, bizErr = checkFinanceAccount(posConfig.MerchantID, fweSubMerchantInfo, posChannels)
			if bizErr != nil {
				logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] checkFinanceAccount pos(hz)-account error, err = %v", bizErr.Error())
				err3 = bizErr
				return
			}
		}()
	}
	wg.Wait()
	if err1 != nil || err2 != nil || err3 != nil {
		logs.CtxError(ctx, "[validFinanceAccount] err1:%+v,err2:%+v,err3:%+v", err1, err2, err3)
		return errdef.NewBizErr(errdef.FinanceAccountRpcErr, errors.New("未开通云直通或pos账户"), "未开通云直通或pos账户")
	}
	return nil
}

func checkFinanceAccount(merchantId string, fweSubMerchantInfo map[string][]*finance_account.SubMerchantInfo, validChannels []finance_account.TradeChannel) ([]*finance_account.SubMerchantInfo, *errdef.BizErr) {
	channels, exist := fweSubMerchantInfo[merchantId]
	if !exist {
		return nil, errdef.NewParamsErr("this seller dont owner channels")
	}
	validRes := slices.Filter(channels, func(dto *finance_account.SubMerchantInfo) bool {
		if slices.Contains(validChannels, dto.TradeChannel) && dto.ChannelStatus == finance_account.ChannelStatus_Ready {
			return true
		}
		return false
	}).([]*finance_account.SubMerchantInfo)
	if len(validRes) == 0 {
		return nil, errdef.NewParamsErr("this seller`s all channels is not ready")
	}
	return validRes, nil
}

func getLoanData(ctx context.Context, infos []*fwe_trade_common.FinanceInfo, orderTag map[string]string) (int64, []*fwe_trade_common.FeeItem, *errdef.BizErr) {
	valStr := orderTag[sh_sell_model.LoanAmountTag]
	if valStr != "" {
		num, err := strconv.ParseInt(valStr, 10, 64)
		if err != nil {
			logs.CtxWarn(ctx, "[createOrderExecution-getLoadAmount] ParseInt error, err = %v", err)
			return 0, nil, errdef.NewParamsErr("orderTag loanAmount error")
		}
		return num, nil, nil
	}

	loanAmount := int64(0)
	feeItems := make([]*fwe_trade_common.FeeItem, 0)
	for _, financeInfo := range infos {
		if financeInfo.FinanceOrderType == sdkConsts.FinanceLoan.Value() {
			loanAmount = financeInfo.Amount
			feeItems = append(feeItems, financeInfo.FeeItemList...)
			break
		}
	}
	return loanAmount, feeItems, nil
}

func getPlatformSubsidyData(ctx context.Context, infos []*fwe_trade_common.FinanceInfo) (int64, []*fwe_trade_common.FeeItem, *errdef.BizErr) {
	loanAmount := int64(0)
	feeItems := make([]*fwe_trade_common.FeeItem, 0)
	for _, financeInfo := range infos {
		if financeInfo.FinanceOrderType == sdkConsts.FinancePlatformSubsidy.Value() {
			loanAmount = financeInfo.Amount
			feeItems = append(feeItems, financeInfo.FeeItemList...)
			break
		}
	}
	return loanAmount, feeItems, nil
}

func mergeFeeItem(ctx context.Context, oldFeeItems, addFeeItems []*fwe_trade_common.FeeItem) ([]*fwe_trade_common.FeeItem, *errdef.BizErr) {
	if len(addFeeItems) == 0 {
		return oldFeeItems, nil
	}
	itemMap := make(map[string]*fwe_trade_common.FeeItem)
	for _, oldFeeItem := range oldFeeItems {
		itemMap[oldFeeItem.FeeItemName] = oldFeeItem
	}
	for _, feeItem := range addFeeItems {
		if itemMap[feeItem.FeeItemName] != nil {
			// 合并已有费项金额
			itemMap[feeItem.FeeItemName].Amount += feeItem.Amount
		} else {
			// 新增费项，追加到 map 中
			itemMap[feeItem.FeeItemName] = feeItem
		}
	}
	res, ok := maps.Values(itemMap).([]*fwe_trade_common.FeeItem)
	if !ok {
		logs.CtxError(ctx, "[createOrderExecution-mergeFeeItem] FeeItem param transfer error")
		return nil, errdef.NewRawErr(errdef.DataErr, "FeeItem param transfer error")
	}
	return res, nil
}
