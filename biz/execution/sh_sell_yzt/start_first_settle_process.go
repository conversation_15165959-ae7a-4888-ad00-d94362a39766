package sh_sell_yzt

import (
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type settleFirstSubExecution struct {
	*executor.ActionBaseExecution
}

func NewSettleFirstSubExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &settleFirstSubExecution{}

	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, nil, opts...)
	return e
}

func (e *settleFirstSubExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}
	return nil
}
