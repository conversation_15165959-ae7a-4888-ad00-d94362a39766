package sh_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	sdkConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
)

type selectTransferOwnerTypeExecution struct {
	*executor.ActionBaseExecution
	param *sh_sell_model.TransferOwnerTypeSelectModel
}

func NewSelectTransferOwnerTypeExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &selectTransferOwnerTypeExecution{
		param: new(sh_sell_model.TransferOwnerTypeSelectModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param)
	return e
}

func (e *selectTransferOwnerTypeExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	param := e.param
	if param == nil || (param.TransferOwnerType != TransferOwnerByPlatformStr && param.TransferOwnerType != TransferOwnerBySelfStr) {
		logs.CtxWarn(ctx, "[selectTransferOwnerTypeExecution.CheckParams] param error. param=%+v", tools.GetLogStr(param))
		return errdef.NewParamsErr("缺少平台过户方式参数")
	}
	if param.TransferOwnerType == TransferOwnerByPlatformStr && param.TransferOwnerGuaranteeAmount != 0 {
		return errdef.NewParamsErr("平台过户时过户保证金金额应当为0")
	}
	if param.TransferOwnerType == TransferOwnerBySelfStr && param.TransferOwnerGuaranteeAmount <= 0 {
		return errdef.NewParamsErr("自主过户时过户保证金金额应当大于0")
	}
	return nil
}

func (e *selectTransferOwnerTypeExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}
	var (
		bizReq = e.param
		order  = e.GetOrder()
	)
	// 如果是异地交车订单，只能选择平台过户
	if order.TagMap[DeliveryCarType] == DeliveryCarTypeRemote && bizReq.TransferOwnerType != TransferOwnerByPlatformStr {
		return errdef.NewParamsErr("异地交车订单只能选择平台过户方式")
	}
	param := &ShSellConditionParam{TransferOwnerType: e.param.TransferOwnerType}
	conditions := GetShSellCondition(ctx, e.GetOrder(), param)
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})

	return nil
}

// Process 设置贷款类型
func (e *selectTransferOwnerTypeExecution) Process(ctx context.Context) error {
	var (
		param                = e.param
		order                = e.GetOrder()
		fweOrder             = order.FweOrder
		bizScene             = e.GetBizIdentity().BizScene
		guaranteeFinanceType = sdkConsts.FinanceGuarantee
	)
	order.TagMap[TransferOwnerType] = param.TransferOwnerType
	_, bizErr := service.NewTagService().UpdateTag(ctx, fweOrder.OrderID, bizScene, order.TagMap)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[selectTransferOwnerTypeExecution.Process] set tag failed, err=%s", bizErr.Error())
		return bizErr
	}
	if param.TransferOwnerType == TransferOwnerBySelfStr {
		var commonFinanceInfos []*fwe_trade_common.FinanceInfo
		commonFinanceInfos = append(commonFinanceInfos, &fwe_trade_common.FinanceInfo{
			FinanceOrderID:   utils.MakeFinanceOrderIDTool(fweOrder.OrderID, int32(guaranteeFinanceType)),
			FinanceOrderType: int32(guaranteeFinanceType),
			PayStatus:        fwe_trade_common.FinanceStatus_NotHandle,
			Amount:           param.TransferOwnerGuaranteeAmount,
			TradeCategory:    fwe_trade_common.TradeCategory_Pay,
			FeeItemList:      nil,
		})

		baseOrder := &service_model.OrderBaseParam{
			Identity:  e.GetBizIdentity(),
			OrderID:   fweOrder.OrderID,
			OrderName: fweOrder.OrderName,
		}
		if bizErr := service.NewFinanceOrderService().CreateV2(ctx, baseOrder, commonFinanceInfos); bizErr != nil {
			logs.CtxError(ctx, "[selectTransferOwnerTypeExecution.Process] err=%s", bizErr.Error())
			return bizErr
		}
	}
	return nil
}
