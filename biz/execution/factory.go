package execution

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type Factory struct{}

func NewExecutionFactory() *Factory {
	return &Factory{}
}

func (e *Factory) GetActionOrderExecution(ctx context.Context, req *engine.ActionOrderReq) (executor.IExecution, error) {
	executionConstructor, err := GetExecution(ctx, req.Identity.GetBizScene(), req.Identity.GetSmVersion(), req.GetAction())
	if err != nil {
		return nil, err
	}
	return executionConstructor(ctx, req), nil
}

func (e *Factory) GetCreateOrderExecution(ctx context.Context, req *engine.CreateOrderReq) (executor.IExecution, error) {
	executionConstructor, err := GetExecution(ctx, req.Identity.GetBizScene(), req.Identity.GetSmVersion(), consts.CreateAction)
	if err != nil {
		return nil, err
	}
	return executionConstructor(ctx, req), nil
}
