package sh_auction_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

// ShAuctionUnionPayCallbackExecution 为 UnionPayCallbackBaseExecution注入二手车拍卖的condition
type ShAuctionUnionPayCallbackExecution struct {
	*callback.UnionPayCallbackBaseExecution
}

func NewShAuctionUnionPayCallbackBaseExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &ShAuctionUnionPayCallbackExecution{}
	exe.UnionPayCallbackBaseExecution = callback.NewUnionPayCallbackBaseExecutionWithOpt(ctx, sourceReq, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: exe.buildCondition,
	})
	return exe
}

func (e *ShAuctionUnionPayCallbackExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[ShAuctionUnionPayCallbackExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}
	logs.CtxInfo(ctx, "[ShAuctionUnionPayCallbackExecution-PreProcess] PreProcess done")
	return nil
}

func (e *ShAuctionUnionPayCallbackExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	// mp = make(map[string]interface{})
	// mp[ShAuctionIsMortgageCar] = e.GetOrder().TagMap[ShAuctionIsMortgageCar] == "true"
	// mp[ShAuctionIsPlatformCheck] = e.GetOrder().TagMap[ShAuctionIsPlatformCheck] == "true"
	// mp[ShAuctionIsC1] = e.GetOrder().TagMap[ShAuctionIsC1] == "true"
	mp = GetShAuctionCondition(ctx, e.GetOrder(), nil)
	return mp, nil
}
