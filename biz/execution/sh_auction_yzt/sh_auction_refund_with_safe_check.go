package sh_auction_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	sdkUtils "code.byted.org/motor/fwe_trade_common/statemachine/utils"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"errors"
)

type RefundWithSafeCheckExecution struct {
	*common.UnionRefundExecution
}

// NewShAuctionRefundWithSafeCheckExecution 带合同结构化校验退款
func NewShAuctionRefundWithSafeCheckExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &RefundWithSafeCheckExecution{}
	e.UnionRefundExecution = common.NewUnionRefundBaseExecution(ctx, actionReq)
	return e
}

func (e *RefundWithSafeCheckExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.BizReq
	if bizReq.GetRuleID() != int64(0) {
		return nil
	}
	if bizReq.RefundList == nil || len(bizReq.RefundList) == 0 {
		bizErr := errdef.NewParamsErr("RefundList 参数错误")
		return bizErr
	}
	for _, refund := range bizReq.RefundList {
		if refund.Amount <= 0 {
			bizErr := errdef.NewParamsErr("RefundList.amount 参数错误")
			return bizErr
		}
	}
	return nil
}

func (e *RefundWithSafeCheckExecution) PreProcess(ctx context.Context) error {

	err := e.UnionRefundExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[cancelRefundExecution-PreProcess] UnionRefundExecution.PreProcess error, err = %v", err.Error())
		return err
	}
	// 如果退款金额为0，则不用结构化校验
	if e.RefundAmount == 0 {
		return nil
	}

	// 合同结构化校验
	param := &service.CheckAmountByContParam{
		OrderID:          e.GetActionOrderReq().OrderID,
		FinanceOrderType: e.BizReq.RefundType,
		Amount:           e.RefundAmount,
		TradeCategory:    fwe_trade_common.TradeCategory_Refund,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionPayExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[UnionPayExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, blockMsg)
	}

	return nil
}

func (e *RefundWithSafeCheckExecution) Process(ctx context.Context) error {

	var (
		mergeRefundNo  string
		bizErr         *errdef.BizErr
		refundAmount   = e.RefundAmount
		actionOrderReq = e.GetActionOrderReq()
	)
	// 跳转状态机
	conditions := sdkUtils.BuildRefundCondition(refundAmount)
	if actionOrderReq.ActionCondition != "" {
		conditions, _ = sdkUtils.AppendBizReqParams(ctx, conditions, actionOrderReq.ActionCondition)
	}
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[UnionRefundExecution-process] fire fsm failed, err=%+v", err.Error())
		return err
	}
	// refund req
	refundParam, bizErr := e.BuildRefundReq(ctx)
	if bizErr != nil {
		return bizErr
	}
	if refundAmount > 0 { // 如果退款金额不为空，则触发退款
		if refundParam == nil || refundParam.MergeRefundReq == nil || len(refundParam.MergeRefundReq.RefundListV2) <= 0 {
			return errors.New("退款资金单为空")
		}
		mergeRefundNo, bizErr = service.NewUnionRefundService().UnionRefund(ctx, refundParam)
		if bizErr != nil {
			return bizErr
		}
	} else { // 如果退款金额为空，直接只创建一个资金单(本质是为了写入费项)
		// 生成资金单
		baseOrder := &service_model.OrderBaseParam{
			Identity:  refundParam.MergeRefundReq.Identity,
			OrderID:   refundParam.OrderID,
			OrderName: refundParam.OrderName,
		}
		financeInfo := &fwe_trade_common.FinanceInfo{
			FinanceOrderID:   refundParam.MergeRefundReq.OutID,
			FinanceOrderType: refundParam.RefundType,
			PayStatus:        fwe_trade_common.FinanceStatus_Complete,
			Amount:           refundParam.RefundAmount,
			TradeCategory:    fwe_trade_common.TradeCategory_Refund,
			FeeRecordID:      refundParam.FeeRecordID,
			FeeItemList:      refundParam.FeeItemList,
			DeductItemList:   refundParam.DeductItemList,
		}

		bizErr = service.NewFinanceOrderService().CreateV2(ctx, baseOrder, []*fwe_trade_common.FinanceInfo{financeInfo})
		if bizErr != nil {
			logs.CtxWarn(ctx, "[UnionRefundService-UnionRefund] CreateV2 error, err = %v", bizErr.Error())
			return bizErr
		}
	}

	e.BizRsp = execution_common.RefundRsp{
		MergeRefundNo: mergeRefundNo,
	}
	return nil
}

func (e *RefundWithSafeCheckExecution) BuildRefundReq(ctx context.Context) (*service_model.UnionRefundParam, *errdef.BizErr) {
	var (
		order        = e.GetOrder()
		fweOrder     = order.FweOrder
		orderId      = fweOrder.OrderID
		bizReq       = e.BizReq
		refundList   = packer.FeeRefunds2Payment(ctx, e.RefundList)
		refundAmount = e.RefundAmount
		recordId     = e.FeeRecordId
	)
	serviceReq := &payment.MergeRefundReq{
		Identity:                   e.GetBizIdentity(),
		RefundListV2:               refundList,
		OrderID:                    fweOrder.OrderID,
		OrderType:                  fwe_trade_common.OrderType_Trade,
		OutID:                      utils.MakeRefundFinanceOutID(orderId, bizReq.GetRefundType()),
		OrderName:                  fweOrder.OrderName,
		Reason:                     bizReq.Reason,
		Extra:                      nil,
		CallbackEvent:              utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:              "",
		IPAddress:                  bizReq.IPAddress,
		NeedConfirmOfflineRefund:   conv.BoolDefault(bizReq.NeedConfirmOfflineRefund, false),
		NeedWithdraw:               bizReq.NeedWithdraw,
		RefundSettleInfo:           bizReq.RefundSettleInfo,
		RefundFinanceType:          bizReq.RefundType,
		YztOfflineRefundSettleInfo: bizReq.YztOfflineRefundSettleInfo,
		LifeRefundParam:            bizReq.LifeRefundParam,
	}
	refundParam := &service_model.UnionRefundParam{
		OrderID:        orderId,
		OrderName:      fweOrder.OrderName,
		RefundType:     bizReq.RefundType,
		RefundAmount:   refundAmount,
		MergeRefundReq: serviceReq,
		FeeRecordID:    recordId,
		FeeItemList:    bizReq.FeeItemList,
		DeductItemList: bizReq.DeductItemList,
	}
	return refundParam, nil
}
