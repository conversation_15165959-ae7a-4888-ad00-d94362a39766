package sh_auction_yzt

import (
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

// ShAuctionWithdrawCallbackExecution 为 UnionPayCallbackBaseExecution注入二手车拍卖的condition
type ShAuctionWithdrawCallbackExecution struct {
	*callback.WithdrawCallbackBaseExecution
}

func NewShAuctionWithdrawCallbackExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &ShAuctionWithdrawCallbackExecution{}
	exe.WithdrawCallbackBaseExecution = callback.NewWithdrawCallbackBaseExecutionWithOpt(ctx, sourceReq, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: exe.buildCondition,
	})
	return exe
}

func (e *ShAuctionWithdrawCallbackExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	// mp = make(map[string]interface{})
	// mp[ShAuctionIsMortgageCar] = e.GetOrder().TagMap[ShAuctionIsMortgageCar] == "true"
	// mp[ShAuctionIsPlatformCheck] = e.GetOrder().TagMap[ShAuctionIsPlatformCheck] == "true"
	// mp[ShAuctionIsC1] = e.GetOrder().TagMap[ShAuctionIsC1] == "true"
	mp = GetShAuctionCondition(ctx, e.GetOrder(), nil)
	return mp, nil
}
