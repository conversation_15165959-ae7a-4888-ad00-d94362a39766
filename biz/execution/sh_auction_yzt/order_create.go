package sh_auction_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	sdkConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/scene/sh_auction"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_auction_yzt_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"errors"
	"unicode/utf8"
)

type createOrderExecution struct {
	*executor.CreateBaseExecution
	conf                sh_auction_yzt_model.Conf
	yztSubMerchantInfos []*finance_account.SubMerchantInfo
}

func NewCreateOrderExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.CreateOrderReq)
	e := &createOrderExecution{}
	e.conf = sh_auction_yzt_model.Conf{}
	e.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, req, &e.conf)
	return e
}

func (e *createOrderExecution) CheckParams(ctx context.Context) error {
	var (
		req = e.GetCreateOrderReq()
	)
	// 资金风控检查
	if bizErr := utils.CheckFundRiskOfAmount(ctx, req.IsTest, req.GetTotalAmount(), e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-CheckParams] CheckFundRiskOfAmount error, err = %v ", bizErr.Error())
		return bizErr
	}
	if req.ProductInfo == nil {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack product_info")
		return errors.New("lack product_info")
	}

	if req.BuyerInfo == nil || (req.BuyerInfo.PersonInfo == nil && req.BuyerInfo.CompanyInfo == nil && req.BuyerInfo.FweMerchant == nil) {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack buyer_info")
		return errors.New("lack buyer_info")
	}
	if req.SellerInfo == nil || req.SellerInfo.FweMerchant == nil || req.SellerInfo.FweMerchant.FweAccountID == "" {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack seller_info")
		return errors.New("lack seller_info")
	}
	if req.Identity.BizScene == sh_auction.SHChannelAuctionScene.Value() {
		if req.ServiceProviderInfo == nil || req.ServiceProviderInfo.FweMerchant == nil || req.ServiceProviderInfo.FweMerchant.FweAccountID == "" {
			logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack service provider info")
			return errors.New("lack service provider info")
		}
	}
	if req.ProductInfo.ProductDetail != nil {
		if utf8.RuneCountInString(req.ProductInfo.GetProductDetail().GetProductHeadImageURI()) > 128 {
			logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] product_head_image_url illegal")
			return errdef.NewParamsErr("product_head_image_url length over 128")
		}
		if utf8.RuneCountInString(req.ProductInfo.GetProductDetail().GetProductDesc()) > 128 {
			logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] product_desc illegal")
			return errdef.NewParamsErr("product_desc length over 128")
		}
	}
	// 验证 账户，目前只有拍卖主订单需要验证
	if req.GetIdentity().BizScene == sh_auction.SHAuctionScene.Value() || req.GetIdentity().BizScene == sh_auction.SHChannelAuctionScene.Value() {
		bizErr := e.validFinanceAccount(ctx)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[createOrderExecution-CheckParams] validFinanceAccount error , err = %v", bizErr.Error())
			return bizErr
		}
	}
	return nil
}

func (e *createOrderExecution) PreProcess(ctx context.Context) error {
	err := e.CreateBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] PreProcess error, err = %v", err)
		return err
	}
	condMap := make(map[string]interface{})
	bizErr := e.FireWithCondition(ctx, condMap)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] FireWithCondition error, err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *createOrderExecution) Process(ctx context.Context) error {
	// 拼数据
	var (
		req                  = e.GetCreateOrderReq()
		fweOrder             *db_model.FweOrder
		financeList          []*db_model.FFinanceOrder
		totalAmount          = req.TotalAmount
		buyerID              = packer.CommonTradeSubjectIDGet(req.BuyerInfo)
		buyerExtra           = packer.CommonTradeSubjectSerialize(req.BuyerInfo)
		sellerID             = packer.CommonTradeSubjectIDGet(req.SellerInfo)
		sellerExtra          = packer.CommonTradeSubjectSerialize(req.SellerInfo)
		serviceProviderID    = packer.CommonTradeSubjectIDGet(req.ServiceProviderInfo)
		serviceProviderExtra = packer.CommonTradeSubjectSerialize(req.ServiceProviderInfo)
		talentID             = packer.CommonTradeSubjectIDGet(req.TalentInfo)
		talentExtra          = packer.CommonTradeSubjectSerialize(req.TalentInfo)
	)

	var isTest int32
	if req.IsTest {
		isTest = 1
	}
	fweOrder = &db_model.FweOrder{
		TenantType:         int32(req.GetIdentity().GetTenantType()),
		BizScene:           req.GetIdentity().BizScene,
		SmVersion:          req.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(e.GetStateMachine().CurState()), // 初始态
		OrderName:          req.GetOrderName(),
		OrderDesc:          req.GetOrderDesc(),
		ProductID:          req.ProductInfo.ProductID,
		ProductType:        int32(req.ProductInfo.ProductType),
		ProductName:        req.ProductInfo.ProductName,
		ProductExtra:       req.ProductInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(req.ProductInfo.ProductDetail)),
		SkuID:              req.ProductInfo.SkuID,
		ProductQuantity:    int32(req.ProductInfo.ProductQuantity),
		ProductUnitPrice:   req.ProductInfo.ProductUnitPrice,
		TotalAmount:        totalAmount,
		TotalPayAmount:     totalAmount,
		TotalSubsidyAmount: 0,
		TradeType:          int32(req.TradeType),
		BuyerID:            buyerID,
		BuyerExtra:         &buyerExtra,
		SellerID:           sellerID,
		SellerExtra:        &sellerExtra,
		ServiceProviderID:  serviceProviderID,
		TalentID:           talentID,
		IsTest:             isTest,
		IdempotentID:       req.GetIdemID(),
	}
	if serviceProviderExtra != "" {
		fweOrder.ServiceProviderExtra = &serviceProviderExtra
	}
	if talentExtra != "" {
		fweOrder.TalentExtra = &talentExtra
	}

	for _, financeInfo := range req.FinanceList {
		// 跳过贷款
		if financeInfo.FinanceOrderType == sdkConsts.FinanceLoan.Value() {
			continue
		}
		financeOrderID, err := utils.TryGenId(3)
		if err != nil {
			logs.CtxError(ctx, "[createOrderExecution] gen id error, err=%+v", err)
			return err
		}
		financeOrder := &db_model.FFinanceOrder{
			TenantType:       int32(req.GetIdentity().GetTenantType()),
			BizScene:         req.GetIdentity().BizScene,
			AppID:            "",
			MerchantID:       "",
			Mid:              "",
			OrderID:          e.GetOrderID(),
			OrderName:        req.OrderName,
			TradeType:        financeInfo.TradeType,
			TradeCategory:    int32(fwe_trade_common.TradeCategory_Pay),
			FinanceOrderID:   utils.MakeFinanceOrderID(financeOrderID, financeInfo.FinanceOrderType),
			FinanceOrderType: financeInfo.FinanceOrderType,
			Amount:           financeInfo.Amount,
			ProcessAmount:    0,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
			FeeItemDetail:    conv.StringPtr(tools.GetLogStr(financeInfo.FeeItemList)),
			LoanAmount:       financeInfo.LoanAmount,
		}
		financeList = append(financeList, financeOrder)
	}

	order := &service_model.Order{
		FweOrder:       fweOrder,
		FinanceList:    financeList,
		TagMap:         req.OrderTag,
		BizExtra:       req.Extra,
		TradeSplitInfo: req.SplitInfo,
	}
	bizErr := service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[createOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *createOrderExecution) Result() interface{} {
	return e.GetOrderID()
}

var (
	yztChannels = []finance_account.TradeChannel{
		finance_account.TradeChannel_yzt_hz,
		finance_account.TradeChannel_yzt_alipay,
		finance_account.TradeChannel_syt_wx,
	}
)

func (e *createOrderExecution) validFinanceAccount(ctx context.Context) *errdef.BizErr {
	var (
		req          = e.GetCreateOrderReq()
		fweAccountID = e.GetCreateOrderReq().SellerInfo.GetFweMerchant().FweAccountID
		yztConfig    = e.conf.YZTPayMerchant
	)
	if yztConfig == nil || yztConfig.MerchantID == "" {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] config error, config = %v", tools.GetLogStr(e.conf))
		return errdef.NewRawErr(errdef.LackConfigErr, "yztConfig/posConfig config error")
	}
	fweSubMerchantInfo, bizErr := service.NewFinanceAccountService().GetFweSubMerchantInfo(ctx, req.Identity.TenantType, fweAccountID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] GetFweSubMerchantInfo error, err = %v", bizErr.Error())
		return bizErr
	}
	// 验证 yzt
	yztSubMerchantChannels, bizErr := checkFinanceAccount(yztConfig.MerchantID, fweSubMerchantInfo, yztChannels)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] checkFinanceAccount yzt-account error, err = %v", bizErr.Error())
		return bizErr
	}
	e.yztSubMerchantInfos = yztSubMerchantChannels
	return nil
}

func checkFinanceAccount(merchantId string, fweSubMerchantInfo map[string][]*finance_account.SubMerchantInfo, validChannels []finance_account.TradeChannel) ([]*finance_account.SubMerchantInfo, *errdef.BizErr) {
	channels, exist := fweSubMerchantInfo[merchantId]
	if !exist {
		return nil, errdef.NewParamsErr("this seller dont owner channels")
	}
	validRes := slices.Filter(channels, func(dto *finance_account.SubMerchantInfo) bool {
		if slices.Contains(validChannels, dto.TradeChannel) && dto.ChannelStatus == finance_account.ChannelStatus_Ready {
			return true
		}
		return false
	}).([]*finance_account.SubMerchantInfo)
	if len(validRes) == 0 {
		return nil, errdef.NewParamsErr("this seller`s all channels is not ready")
	}
	return validRes, nil
}
