package sh_auction_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type CreateFinanceExecution struct {
	*executor.StaticBaseExecution
	proReq execution_common.CreateFinanceReq
}

func NewCreateFinanceExecution(ctx context.Context, req interface{}) executor.IExecution {
	e := &CreateFinanceExecution{}
	e.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, req.(*engine.ActionOrderReq), nil, &e.proReq)
	return e
}

func (e *CreateFinanceExecution) Process(ctx context.Context) error {
	var (
		bizErr   *errdef.BizErr
		proReq   = e.proReq
		order    = e.GetOrder().FweOrder
		finances = e.GetOrder().FinanceList
	)
	e.proReq.Tag = e.GetActionOrderReq().TagMap
	// 一个资金单
	if len(proReq.FinanceList) != 1 {
		bizErr = errdef.NewParamsErr("only can create one")
		logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
		return bizErr
	}
	finance := proReq.FinanceList[0]
	finance.OrderID = order.OrderID

	// 更新tag
	if len(proReq.Tag) > 0 {
		bizErr = service.NewOrderService().UpdateOrderTag(ctx, order.OrderID, proReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	createFinanceOrderList := make([]*fwe_trade_common.FinanceInfo, 0)
	var isFind bool
	for _, v := range finances {
		if v.FinanceOrderType == finance.FinanceOrderType {
			isFind = true
			if v.Amount != finance.Amount {
				bizErr = errdef.NewParamsErr("can not update amount")
				logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
				return bizErr
			}
		}
	}
	if isFind {
		logs.CtxInfo(ctx, "[CreateFinanceExecution] has find finances")
		return nil
	}
	createFinanceOrderList = append(createFinanceOrderList, e.buildFinanceInfo(finance))

	// 创建资金单
	baseOrder := &service_model.OrderBaseParam{
		Identity:  e.GetBizIdentity(),
		OrderID:   order.OrderID,
		OrderName: order.OrderName,
	}
	bizErr = service.NewFinanceOrderService().CreateV2(ctx, baseOrder, createFinanceOrderList)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *CreateFinanceExecution) buildFinanceInfo(info *fwe_trade_common.FinanceInfo) *fwe_trade_common.FinanceInfo {
	var (
		fweOrder = e.GetOrder().FweOrder
	)
	res := &fwe_trade_common.FinanceInfo{
		FinanceOrderID:   utils.MakeFinanceOrderIDTool(fweOrder.OrderID, info.FinanceOrderType),
		FinanceOrderType: info.FinanceOrderType,
		PayStatus:        fwe_trade_common.FinanceStatus_NotHandle,
		Amount:           info.Amount,
		TradeCategory:    info.TradeCategory,
		FeeItemList:      info.FeeItemList,
		TradeType:        info.TradeType,
		OrderID:          info.OrderID,
	}
	return res
}
