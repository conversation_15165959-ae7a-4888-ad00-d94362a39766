package sh_auction_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
	"strconv"
)

func CheckActionReq(ctx context.Context, actionReq *engine.ActionOrderReq) *errdef.BizErr {
	if actionReq == nil || actionReq.Identity == nil || actionReq.Identity.TenantType == 0 ||
		actionReq.Identity.BizScene == 0 || actionReq.OrderID == "" || actionReq.Action == "" {
		logs.CtxWarn(ctx, "[CheckActionReq] param empty")
		return errdef.NewParamsErr("有必传参数为空，请检查")
	}
	return nil
}

// GetShAuctionCondition 获取状态机条件
func GetShAuctionCondition(ctx context.Context, orderInfo *service_model.Order, newTag map[string]string) map[string]interface{} {
	result := make(map[string]interface{})
	if isPlatformCheck, exist := orderInfo.TagMap[ShAuctionIsPlatformCheck]; exist {
		isPlatformCheckBool, _ := strconv.ParseBool(isPlatformCheck)
		result[ShAuctionIsPlatformCheck] = isPlatformCheckBool
	} else {
		result[ShAuctionIsPlatformCheck] = false
	}
	if isMortgageCar, exist := orderInfo.TagMap[ShAuctionIsMortgageCar]; exist {
		isMortgageCarBool, _ := strconv.ParseBool(isMortgageCar)
		result[ShAuctionIsMortgageCar] = isMortgageCarBool
	} else {
		result[ShAuctionIsMortgageCar] = false
	}
	if isC1, exist := orderInfo.TagMap[ShAuctionIsC1]; exist {
		isC1Bool, _ := strconv.ParseBool(isC1)
		result[ShAuctionIsC1] = isC1Bool
	} else {
		result[ShAuctionIsC1] = false
	}
	if buyerIsSelf, exist := orderInfo.TagMap[ShAuctionBuyerIsSelf]; exist {
		buyerIsSelfBool, _ := strconv.ParseBool(buyerIsSelf)
		result[ShAuctionBuyerIsSelf] = buyerIsSelfBool
	} else {
		result[ShAuctionBuyerIsSelf] = false
	}
	if needPayFull, exist := orderInfo.TagMap[ShAuctionNeedPayFull]; exist {
		needPayFullBool, _ := strconv.ParseBool(needPayFull)
		result[ShAuctionNeedPayFull] = needPayFullBool
	} else {
		result[ShAuctionNeedPayFull] = false
	}
	if needOnlineWithdraw, exist := orderInfo.TagMap[ShAuctionNeedOnlineWithdraw]; exist {
		needOnlineWithdrawBool, _ := strconv.ParseBool(needOnlineWithdraw)
		result[ShAuctionNeedOnlineWithdraw] = needOnlineWithdrawBool
	} else {
		result[ShAuctionNeedOnlineWithdraw] = false
	}
	if cancelWithdrawAmount, exist := orderInfo.TagMap[ShAuctionCancelWithdrawAmount]; exist {
		cancelWithdrawAmountInt, _ := strconv.ParseInt(cancelWithdrawAmount, 10, 64)
		result[ShAuctionCancelWithdrawAmount] = cancelWithdrawAmountInt
	} else {
		result[ShAuctionCancelWithdrawAmount] = 0
	}

	if len(newTag) > 0 {
		if isPlatformCheck, exist := newTag[ShAuctionIsPlatformCheck]; exist && isPlatformCheck != "" {
			isPlatformCheckBool, _ := strconv.ParseBool(isPlatformCheck)
			result[ShAuctionIsPlatformCheck] = isPlatformCheckBool
		}
		if isMortgageCar, exist := newTag[ShAuctionIsMortgageCar]; exist && isMortgageCar != "" {
			isMortgageCarBool, _ := strconv.ParseBool(isMortgageCar)
			result[ShAuctionIsMortgageCar] = isMortgageCarBool
		}
		if isC1, exist := newTag[ShAuctionIsC1]; exist && isC1 != "" {
			isC1Bool, _ := strconv.ParseBool(isC1)
			result[ShAuctionIsC1] = isC1Bool
		}
		if buyerIsSelf, exist := newTag[ShAuctionBuyerIsSelf]; exist && buyerIsSelf != "" {
			buyerIsSelfBool, _ := strconv.ParseBool(buyerIsSelf)
			result[ShAuctionBuyerIsSelf] = buyerIsSelfBool
		}
		if needPayFull, exist := newTag[ShAuctionNeedPayFull]; exist && needPayFull != "" {
			needPayFullBool, _ := strconv.ParseBool(needPayFull)
			result[ShAuctionNeedPayFull] = needPayFullBool
		}
		if needOnlineWithdraw, exist := newTag[ShAuctionNeedOnlineWithdraw]; exist && needOnlineWithdraw != "" {
			needOnlineWithdrawBool, _ := strconv.ParseBool(needOnlineWithdraw)
			result[ShAuctionNeedOnlineWithdraw] = needOnlineWithdrawBool
		}
		if cancelWithdrawAmount, exist := newTag[ShAuctionCancelWithdrawAmount]; exist && cancelWithdrawAmount != "" {
			cancelWithdrawAmountInt, _ := strconv.ParseInt(cancelWithdrawAmount, 10, 64)
			result[ShAuctionCancelWithdrawAmount] = cancelWithdrawAmountInt
		}
	}
	return result
}

func GetShAuctionCompensateCondition(ctx context.Context, orderInfo *service_model.Order, newTag map[string]string) map[string]interface{} {
	result := make(map[string]interface{})
	if orderInfo != nil && len(orderInfo.TagMap) > 0 {
		if isPlatformCheck, exist := orderInfo.TagMap[ShAuctionCompensateBizType]; exist {
			isPlatformCheckInt, _ := strconv.ParseInt(isPlatformCheck, 10, 64)
			result[ShAuctionCompensateBizType] = isPlatformCheckInt
		} else {
			result[ShAuctionCompensateBizType] = 0
		}
	}
	if len(newTag) > 0 {
		if isPlatformCheck, exist := newTag[ShAuctionCompensateBizType]; exist && isPlatformCheck != "" {
			isPlatformCheckInt, _ := strconv.ParseInt(isPlatformCheck, 10, 64)
			result[ShAuctionCompensateBizType] = isPlatformCheckInt
		} else {
			result[ShAuctionCompensateBizType] = 0
		}
	}
	return result
}
