package sh_auction_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	OrderRpc "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"errors"
	"time"
)

type CompensateOrderOperationConfirmExecution struct {
	*executor.ActionBaseExecution
	updateOrder execution_common.CreateFinanceReq

	oldFinanceMap map[int32]*fwe_trade_common.FinanceInfo
}

func NewCompensateOrderOperationConfirmExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &CompensateOrderOperationConfirmExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.updateOrder)
	return t
}

func (e *CompensateOrderOperationConfirmExecution) PreProcess(ctx context.Context) error {
	var oldFinanceMap = make(map[int32]*fwe_trade_common.FinanceInfo)
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		return err
	}
	// 查询资金模型
	req := &OrderRpc.QueryFinanceModelByOrderIDReq{
		OrderID:      e.GetActionOrderReq().OrderID,
		OrderType:    fwe_trade_common.OrderTypePtr(fwe_trade_common.OrderType_Trade),
		ReadStrategy: OrderRpc.ReadStrategyPtr(OrderRpc.ReadStrategy_ReadMaster),
	}
	financeModel, bizErr := service.NewOrderService().QueryFinanceModel(ctx, req)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[simpleUpdateFweTradeOrder-PreProcess] QueryFinanceModel error, err = %v", bizErr.Error())
		return bizErr
	}
	for _, financeInfo := range financeModel.FinanceList {
		oldFinanceMap[financeInfo.FinanceOrderType] = financeInfo
	}
	e.oldFinanceMap = oldFinanceMap
	// e.updateOrder.Tag = e.GetActionOrderReq().TagMap
	// 解析状态机条件
	var (
		fireConditionStr = "{}"
		fireCondition    = make(map[string]interface{})
	)
	err = utils.SonicUnmarshal(fireConditionStr, &fireCondition)
	if err != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] unmarshal fire_condition str failed, err=%+v", err)
		return errdef.NewRawErr(errdef.ParamErr, "fireCondition格式错误")
	}

	if len(e.GetFSMCondition()) > 0 {
		fireCondition = e.MergeCondition(fireCondition, e.GetFSMCondition())
	}
	// 获取默认状态机跳转条件
	fireCondition2 := GetShAuctionCompensateCondition(ctx, e.GetOrder(), e.GetActionOrderReq().TagMap)
	if len(fireCondition2) > 0 {
		fireCondition = e.MergeConditionV2(fireCondition, fireCondition2)
	}

	bizErr = e.FireWithCondition(ctx, fireCondition)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] fire failed, err=%s, fireCondition=%s",
			bizErr.Error(), tools.GetLogStr(fireCondition))
		return bizErr
	}

	return nil
}

func (e *CompensateOrderOperationConfirmExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		updateOrder  = e.updateOrder
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		orderService = service.NewOrderService()
		stateMachine = e.GetStateMachine()
		updateParams = &service_model.UpdateOrderParams{
			Operator: actionReq.GetOperator(),
		}
		financeOrderService = service.NewFinanceOrderService()
	)
	if e.GetStateMachine().GetOriginalState() != e.GetStateMachine().CurState() {
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	}
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))

	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}

	if len(updateOrder.Tag) > 0 || len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(updateOrder.Tag) > 0 {
			for key, value := range updateOrder.Tag {
				tagMap[key] = value
			}
		}
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		bizErr = orderService.UpdateOrderTag(ctx, orderID, tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	// 更新订单信息
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 创建资金单，目前只让创建一个
	if len(e.updateOrder.FinanceList) == 1 {
		info := e.updateOrder.FinanceList[0]
		financeOrderType := info.FinanceOrderType
		if e.oldFinanceMap != nil && e.oldFinanceMap[financeOrderType] != nil { // 修改
			oldFinance := e.oldFinanceMap[financeOrderType]
			if oldFinance.PayStatus == fwe_trade_common.FinanceStatus_Complete {
				return errors.New("资金单已完成，不可编辑")
			}
			err1 := financeOrderService.UpdateV2(ctx, oldFinance.FinanceOrderID, e.buildUpdateParam(info, oldFinance))
			if err1 != nil {
				logs.CtxError(ctx, "[confirmOrderExecution-createOrUpdateFinanceOrder] UpdateV2 error, err = %v", err1.Error())
				return err1
			}
		} else { // 新增
			output := make([]*db_model.FFinanceOrder, 0)
			tempFinanceInfo := &db_model.FFinanceOrder{
				TenantType:       e.GetOrder().FweOrder.TenantType,
				BizScene:         e.GetOrder().FweOrder.BizScene,
				AppID:            "",
				MerchantID:       info.MerchantID,
				Mid:              "",
				OrderID:          e.GetOrder().FweOrder.OrderID,
				OrderName:        e.GetOrder().FweOrder.OrderName,
				TradeType:        info.TradeType,
				TradeCategory:    int32(info.TradeCategory),
				FinanceOrderID:   utils.MakeFinanceOrderIDTool(e.GetOrder().FweOrder.OrderID, info.FinanceOrderType),
				FinanceOrderType: info.FinanceOrderType,
				Amount:           info.Amount,
				ProcessAmount:    0,
				FeeItemDetail:    conv.StringPtr(tools.GetLogStr(info.FeeItemList)),
				Status:           getStatusBool(info.Amount == 0, fwe_trade_common.FinanceStatus_Complete, fwe_trade_common.FinanceStatus_NotHandle),
				FeeRecordID:      info.FeeRecordID,
			}
			if tempFinanceInfo.Status == int32(fwe_trade_common.FinanceStatus_Complete) {
				tempFinanceInfo.FinishTime = time.Now()
			}
			output = append(output, tempFinanceInfo)
			bizErr = service.NewFinanceOrderService().CreateFinanceOrderList(ctx, output)
			if bizErr != nil {
				logs.CtxError(ctx, "[confirmOrderExecution-createOrUpdateFinanceOrder] CreateV2 error, err = %v", bizErr.Error())
				return bizErr
			}
		}
	}
	return nil
}

func (e *CompensateOrderOperationConfirmExecution) MergeConditionV2(paramMap map[string]interface{}, calMap map[string]interface{}) map[string]interface{} {
	for key, value := range calMap {
		paramMap[key] = value
	}
	return paramMap
}

func (e *CompensateOrderOperationConfirmExecution) buildUpdateParam(info *fwe_trade_common.FinanceInfo, oldFinance *fwe_trade_common.FinanceInfo) *service_model.UpdateFinanceParams {
	feeItemDetail := "[]"
	if len(info.FeeItemList) > 0 {
		feeItemDetail = tools.GetLogStr(info.FeeItemList)
	}
	res := &service_model.UpdateFinanceParams{
		UpdateAmount:        conv.Int64Ptr(info.Amount),
		UpdateLoanAmount:    conv.Int64Ptr(info.LoanAmount),
		UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_NotHandle)),
		UpdateFeeItemDetail: conv.StringPtr(feeItemDetail),
	}
	return res
}
