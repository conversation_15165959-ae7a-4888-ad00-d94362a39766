package sh_auction_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"errors"
	"time"
)

type DirectCancelExecution struct {
	*executor.ActionBaseExecution
	updateOrder DirectCancelOrderReq
}

type DirectCancelOrderReq struct {
	FinanceList []*fwe_trade_common.FinanceInfo
}

func NewDirectCancelExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &DirectCancelExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.updateOrder)
	return t
}

func (e *DirectCancelExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		return err
	}
	// 解析状态机条件
	var (
		fireConditionStr = "{}"
		fireCondition    = make(map[string]interface{})
	)
	err = utils.SonicUnmarshal(fireConditionStr, &fireCondition)
	if err != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] unmarshal fire_condition str failed, err=%+v", err)
		return errdef.NewRawErr(errdef.ParamErr, "fireCondition格式错误")
	}

	if len(e.GetFSMCondition()) > 0 {
		fireCondition = e.MergeCondition(fireCondition, e.GetFSMCondition())
	}
	// 获取默认状态机跳转条件
	fireCondition2 := GetShAuctionCondition(ctx, e.GetOrder(), e.GetActionOrderReq().TagMap)
	if len(fireCondition2) > 0 {
		fireCondition = e.MergeConditionV2(fireCondition, fireCondition2)
	}

	bizErr := e.FireWithCondition(ctx, fireCondition)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] fire failed, err=%s, fireCondition=%s",
			bizErr.Error(), tools.GetLogStr(fireCondition))
		return bizErr
	}
	if len(e.updateOrder.FinanceList) > 0 && len(e.updateOrder.FinanceList) != 1 {
		bizErr = errdef.NewBizErr(errdef.ServerException, errors.New("最多只能创建一个资金单"), "")
		return bizErr
	}

	return nil
}

func (e *DirectCancelExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		orderService = service.NewOrderService()
		tagService   = service.NewTagService()
		stateMachine = e.GetStateMachine()
		updateParams = &service_model.UpdateOrderParams{
			Operator: actionReq.GetOperator(),
		}
		finances         = e.GetOrder().FinanceList
		withdrawFinances = e.GetOrder().WithdrawFinanceList
	)
	if e.GetStateMachine().GetOriginalState() != e.GetStateMachine().CurState() {
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	}
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))

	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}

	if len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		_, bizErr = tagService.UpdateTag(ctx, orderID, e.GetOrder().FweOrder.BizScene, tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[DirectCancelExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if actionReq.Operator != nil {
		updateParams.Operator = actionReq.Operator
	}

	// 写入资金单
	if len(e.updateOrder.FinanceList) == 1 {
		finance := e.updateOrder.FinanceList[0]
		var isFind bool
		if len(finances) > 0 {
			for _, v := range finances {
				if v.FinanceOrderType == finance.FinanceOrderType {
					isFind = true
					if v.Amount != finance.Amount {
						bizErr = errdef.NewParamsErr("can not update amount")
						logs.CtxError(ctx, "[DirectCancelExecution] err=%s", bizErr.Error())
						return bizErr
					}
				}
			}
		}
		if len(withdrawFinances) > 0 {
			for _, v := range withdrawFinances {
				if v.FinanceOrderType == finance.FinanceOrderType {
					isFind = true
					if v.Amount != finance.Amount {
						bizErr = errdef.NewParamsErr("can not update amount")
						logs.CtxError(ctx, "[DirectCancelExecution] err=%s", bizErr.Error())
						return bizErr
					}
				}
			}
		}
		if !isFind && len(e.updateOrder.FinanceList) > 0 {
			output := make([]*db_model.FFinanceOrder, 0)
			for _, info := range e.updateOrder.FinanceList {
				tempFinanceInfo := &db_model.FFinanceOrder{
					TenantType:       e.GetOrder().FweOrder.TenantType,
					BizScene:         e.GetOrder().FweOrder.BizScene,
					AppID:            "",
					MerchantID:       info.MerchantID,
					OrderID:          e.GetOrder().FweOrder.OrderID,
					OrderName:        e.GetOrder().FweOrder.OrderName,
					TradeType:        info.TradeType,
					TradeCategory:    int32(info.TradeCategory),
					FinanceOrderID:   utils.MakeFinanceOrderIDTool(e.GetOrder().FweOrder.OrderID, info.FinanceOrderType),
					FinanceOrderType: info.FinanceOrderType,
					Amount:           info.Amount,
					ProcessAmount:    0,
					FeeItemDetail:    conv.StringPtr(tools.GetLogStr(info.FeeItemList)),
					Status:           getStatusBool(info.Amount == 0, fwe_trade_common.FinanceStatus_Complete, fwe_trade_common.FinanceStatus_NotHandle),
					FeeRecordID:      info.FeeRecordID,
				}
				if tempFinanceInfo.Status == int32(fwe_trade_common.FinanceStatus_Complete) {
					tempFinanceInfo.FinishTime = time.Now()
				}
				output = append(output, tempFinanceInfo)
			}
			bizErr = service.NewFinanceOrderService().CreateFinanceOrderList(ctx, output)
			if bizErr != nil {
				logs.CtxError(ctx, "[confirmOrderExecution-createOrUpdateFinanceOrder] CreateV2 error, err = %v", bizErr.Error())
				return bizErr
			}
		}
	}

	// 更新订单信息
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *DirectCancelExecution) MergeConditionV2(paramMap map[string]interface{}, calMap map[string]interface{}) map[string]interface{} {
	for key, value := range calMap {
		paramMap[key] = value
	}
	return paramMap
}
