package sh_auction_yzt

import (
	"code.byted.org/gopkg/logs"
	sdkUtils "code.byted.org/motor/fwe_trade_common/statemachine/utils"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"context"
)

type ShAuctionRefundExecution struct {
	*common.UnionRefundExecution
}

func NewShAuctionRefundExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &ShAuctionRefundExecution{}
	exe.UnionRefundExecution = common.NewUnionRefundBaseExecution(ctx, sourceReq)
	return exe
}

func (e *ShAuctionRefundExecution) Process(ctx context.Context) error {

	var (
		mergeRefundNo string
		bizErr        *errdef.BizErr
		refundAmount  = e.RefundAmount
	)
	// 跳转状态机
	conditions := sdkUtils.BuildRefundCondition(refundAmount)
	conditions2, _ := e.buildCondition(ctx)
	if len(conditions2) > 0 {
		for key, value := range conditions2 {
			conditions[key] = value
		}
	}
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[UnionRefundExecution-process] fire fsm failed, err=%+v", err.Error())
		return err
	}
	// 退款金额为0，直接结束
	if refundAmount == int64(0) {
		logs.CtxInfo(ctx, "[UnionRefundExecution-process] refund amount is 0")
		return nil
	}
	// refund req
	refundParam, bizErr := e.BuildRefundReq(ctx)
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr = service.NewUnionRefundService().UnionRefund(ctx, refundParam)
	if bizErr != nil {
		return bizErr
	}

	e.BizRsp = execution_common.RefundRsp{
		MergeRefundNo: mergeRefundNo,
	}
	return nil
}

func (e *ShAuctionRefundExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	// mp = make(map[string]interface{})
	// mp[ShAuctionIsMortgageCar] = e.GetOrder().TagMap[ShAuctionIsMortgageCar] == "true"
	// mp[ShAuctionIsPlatformCheck] = e.GetOrder().TagMap[ShAuctionIsPlatformCheck] == "true"
	// mp[ShAuctionIsC1] = e.GetOrder().TagMap[ShAuctionIsC1] == "true"
	mp = GetShAuctionCondition(ctx, e.GetOrder(), nil)
	return mp, nil
}
