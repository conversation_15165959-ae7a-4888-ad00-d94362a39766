package sh_auction_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/overpass/motor_service_rpc_idl_common/kitex_gen/motor/fwe_trade_common"
	"context"
	"time"
)

type ShAuctionRefundCallbackExecution struct {
	*callback.UnionRefundCallbackExecution
}

func NewShAuctionRefundCallbackExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &ShAuctionRefundCallbackExecution{}
	exe.UnionRefundCallbackExecution = callback.NewUnionRefundCallbackBaseExecution(ctx, sourceReq, nil, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: exe.buildCondition,
	})
	return exe
}

func (e *ShAuctionRefundCallbackExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	// mp = make(map[string]interface{})
	// mp[ShAuctionIsMortgageCar] = e.GetOrder().TagMap[ShAuctionIsMortgageCar] == "true"
	// mp[ShAuctionIsPlatformCheck] = e.GetOrder().TagMap[ShAuctionIsPlatformCheck] == "true"
	// mp[ShAuctionIsC1] = e.GetOrder().TagMap[ShAuctionIsC1] == "true"
	mp = GetShAuctionCondition(ctx, e.GetOrder(), nil)
	return mp, nil
}

func (e *ShAuctionRefundCallbackExecution) Process(ctx context.Context) error {
	var (
		order          = e.GetOrder()
		cbModel        = e.CbModel
		financeOrderID = cbModel.OutID
		status         = cbModel.Status
		amount         = cbModel.RefundAmount
	)
	// 校验状态和金额
	if status != int32(fwe_trade_common.CommonStatus_Success) {
		logs.CtxError(ctx, "[UnionRefundCallbackExecution] callback status is not success")
		return errdef.NewRawErr(errdef.PaymentRPCErr, "callback status is not success")
	}
	financeOrder := packer.FinanceGetByID(order.RefundFinanceList, financeOrderID)
	if financeOrder == nil {
		logs.CtxError(ctx, "[UnionRefundCallbackExecution] FinanceGetByID error, err = %v", tools.GetLogStr(cbModel))
		return errdef.NewRawErr(errdef.DataErr, "can not find financeOrder")
	}
	if financeOrder.Amount != amount {
		logs.CtxError(ctx, "[UnionRefundCallbackExecution] callback amount is not equal with financeOrder's amount")
		return errdef.NewRawErr(errdef.PaymentRPCErr, "callback amount is not equal with financeOrder's amount")
	}

	if financeOrder.Status == int32(fwe_trade_common.FinanceStatus_Complete) {
		logs.CtxInfo(ctx, "[UnionRefundCallbackExecution] already complete, won't handle")
		return nil
	}

	// 修改资金单状态
	updateStatus := int32(fwe_trade_common.FinanceStatus_Complete)
	updateFinanceOrderParams := &service_model.UpdateFinanceParams{
		UpdateFinanceStatus: &updateStatus,
		UpdateProcessAmount: conv.Int64Ptr(financeOrder.Amount),
		UpdateFinishTime:    conv.Int64Ptr(time.Now().Unix()),
	}
	bizErr := service.NewFinanceOrderService().UpdateV2(ctx, financeOrderID, updateFinanceOrderParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionRefundCallbackExecution] update failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *ShAuctionRefundCallbackExecution) PostProcess(ctx context.Context) error {
	return e.ActionBaseExecution.PostProcess(ctx)
}
