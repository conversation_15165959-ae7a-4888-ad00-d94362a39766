package sh_auction_yzt

import (
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

// ShAuctionUnionContCallbackExecution 为 UnionPayCallbackBaseExecution注入二手车拍卖的condition
type ShAuctionUnionContCallbackExecution struct {
	*callback.ContCallbackBaseExecution
}

func NewShAuctionUnionContCallbackExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &ShAuctionUnionContCallbackExecution{}
	exe.ContCallbackBaseExecution = callback.NewContCallbackBaseExecutionWithOpt(ctx, sourceReq, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: exe.buildCondition,
	})
	return exe
}

func (e *ShAuctionUnionContCallbackExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	mp = GetShAuctionCondition(ctx, e.GetOrder(), nil)
	return mp, nil
}
