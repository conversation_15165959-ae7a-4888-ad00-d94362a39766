package sh_auction_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	sdkUtils "code.byted.org/motor/fwe_trade_common/statemachine/utils"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"context"
	"errors"
)

// ShAuctionWithdrawExecution 为 UnionPayCallbackBaseExecution注入二手车拍卖的condition
type ShAuctionWithdrawExecution struct {
	*common.WithdrawExecution
}

func NewShAuctionWithdrawExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &ShAuctionWithdrawExecution{}
	exe.WithdrawExecution = common.NewWithdrawExecutionBase(ctx, sourceReq)
	return exe
}

func (e *ShAuctionWithdrawExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
		withdrawAmount = e.GetBizReq().Amount
		rpcReq         *payment.WithdrawDepositReq
		withdrawNo     string
		// err            error
	)

	// 会真实出款
	if withdrawAmount > 0 {
		rpcReq, bizErr = e.buildPayReq(ctx)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[WithdrawExecution.Process] build pay req failed, err=%+v", bizErr)
			return errors.New("构建出款请求失败")
		}
	}

	// 驱动
	conditions := sdkUtils.BuildWithdrawCondition(withdrawAmount)
	conditions2, _ := e.buildCondition(ctx)
	if len(conditions2) > 0 {
		for key, value := range conditions2 {
			conditions[key] = value
		}
	}
	if bizErr = e.FireWithCondition(ctx, conditions); bizErr != nil {
		logs.CtxWarn(ctx, "[UpdateOrderFireExecution] fire fsm failed, err=%+v", bizErr.Error())
		return bizErr
	}

	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	// 会真实出款
	if withdrawAmount > 0 {
		withdrawNo, bizErr = service.NewTradePayment().WithdrawDeposit(ctx, rpcReq)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[WithdrawExecution.Process] pay failed, err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(e.GetBizReq().Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.GetBizReq().Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.SetBizRsp(execution_common.WithdrawRsp{
		WithdrawOrderNo: withdrawNo,
	})
	return nil
}

func (e *ShAuctionWithdrawExecution) buildPayReq(_ context.Context) (*payment.WithdrawDepositReq, *errdef.BizErr) {
	var (
		req      = e.GetActionOrderReq()
		bizReq   = e.GetBizReq()
		currency = payment.CurrencyType_CNY
		finance  = packer.FinanceGetByType(e.GetOrder().WithdrawFinanceList, bizReq.FinanceOrderType)
	)

	// 兼容资金单trade_category为0或1-pay的场景
	if finance == nil {
		finance = packer.FinanceGetByType(e.GetOrder().FinanceList, bizReq.FinanceOrderType)
	}

	if finance == nil {
		return nil, errdef.NewParamsErr("未找到资金单")
	}

	if finance.Amount != bizReq.Amount {
		return nil, errdef.NewParamsErr("金额与资金单不等")
	}

	if finance.TradeType != CommonConsts.FinanceWithdrawBank.Value() {
		return nil, errdef.NewParamsErr("不支持的资金单类型")
	}

	rpcReq := &payment.WithdrawDepositReq{
		OrderID:           req.OrderID,
		FinanceOrderID:    finance.FinanceOrderID,
		MerchantID:        bizReq.MerchantID,
		MerchantName:      bizReq.MerchantName,
		AppID:             bizReq.AppID,
		Amount:            finance.Amount,
		WithdrawType:      bizReq.WithdrawType,
		IPAddress:         bizReq.IPAddress,
		WitdhrawDesc:      bizReq.Reason,
		Mid:               nil,
		Currency:          &currency,
		BankCardInfo:      bizReq.BankCardInfo,
		CallbackEvent:     utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:     "",
		FailCallbackEvent: utils.MakeCallbackEvent(bizReq.FailCallbackAction),
		Extra:             nil,
		Operator:          req.GetOperator(),
		Identity:          req.GetIdentity(),
	}

	return rpcReq, nil
}

func (e *ShAuctionWithdrawExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	// mp = make(map[string]interface{})
	// mp[ShAuctionIsMortgageCar] = e.GetOrder().TagMap[ShAuctionIsMortgageCar] == "true"
	// mp[ShAuctionIsPlatformCheck] = e.GetOrder().TagMap[ShAuctionIsPlatformCheck] == "true"
	// mp[ShAuctionIsC1] = e.GetOrder().TagMap[ShAuctionIsC1] == "true"
	mp = GetShAuctionCondition(ctx, e.GetOrder(), nil)
	return mp, nil
}
