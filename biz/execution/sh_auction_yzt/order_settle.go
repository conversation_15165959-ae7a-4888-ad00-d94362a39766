package sh_auction_yzt

import (
	"code.byted.org/gopkg/lang/maps"
	"code.byted.org/gopkg/logs"
	sdkUtils "code.byted.org/motor/fwe_trade_common/statemachine/utils"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"context"
)

type orderSettleExecution struct {
	*common.UnionSettleExecution
	hasOnlinePay bool
}

var tradeTypeList = []string{consts.TradeTypeYZT.String(), consts.TradeTypePayPos.String(), consts.TradeTypePayYztQr.String()}

func NewOrderSettleExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &orderSettleExecution{}
	e.UnionSettleExecution = common.NewUnionSettleBaseExecution(ctx, actionReq)
	return e
}

func (e *orderSettleExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.UnionSettleExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxWarn(ctx, "[orderSettleExecution-PreProcess] base PreProcess error", bizErr.Error())
		return bizErr
	}
	hasOnlinePay, bizErr := checkHasOnlinePay(ctx, e.GetOrder().FweOrder)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[orderSettleExecution-PreProcess] checkHasOnlinePay error, err = %v", bizErr.Error())
		return bizErr
	}
	e.hasOnlinePay = hasOnlinePay
	var settleAmount = e.SettleAmount
	e.BizReq.Tag = e.GetActionOrderReq().TagMap
	conditions := GetShAuctionCondition(ctx, e.GetOrder(), e.BizReq.Tag)
	// 合并
	settleCondition := sdkUtils.BuildSettleCondition(settleAmount)
	mergeCondition := maps.MergeStrIFaceCopy(conditions, settleCondition)
	if err := e.FireWithCondition(ctx, mergeCondition); err != nil {
		logs.CtxWarn(ctx, "[orderSettleExecution-PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	return nil
}

func (e *orderSettleExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		settleAmount = e.SettleAmount
	)
	// 没有线上支付，不需要分账，直接结束
	if !e.hasOnlinePay {
		return nil
	}

	// 金额为0，直接结束
	if settleAmount == int64(0) {
		logs.CtxWarn(ctx, "[orderSettleExecution-Process] settleAmount is 0, direct over", settleAmount)
		return nil
	}
	// 分账
	settleParam, bizErr := e.BuildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[orderSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	mergeSettleNo, bizErr := service.NewUnionSettleService().UnionSettle(ctx, settleParam)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[orderSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.BizRsp = execution_common.SettleRsp{
		MergeSettleNo: mergeSettleNo,
	}
	return nil
}

func checkHasOnlinePay(ctx context.Context, fweOrder *db_model.FweOrder) (bool, *errdef.BizErr) {
	// 查订单的第一笔支付单（线上支付或pos)
	firstPay, bizErr := service.NewTradePayment().QueryOneSuccessPay(ctx, fweOrder.OrderID, tradeTypeList)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[checkHasOnlinePay] QueryOneSuccessPay error, err = %v", bizErr.Error())
		return false, bizErr
	}
	return firstPay != nil, nil
}
