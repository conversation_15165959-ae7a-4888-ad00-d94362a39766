package sh_logistic

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"time"
)

type LogisticOrderCommonTransferExecutionV2 struct {
	*executor.ActionBaseExecution
	updateOrder execution_common.UpdateOrderReq
}

func NewUpdateOrderFireExecutionV2(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &LogisticOrderCommonTransferExecutionV2{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.updateOrder)
	return t
}

func NewUpdateOrderFireExecutionV2Base(ctx context.Context, actionReq interface{}) *LogisticOrderCommonTransferExecutionV2 {
	t := &LogisticOrderCommonTransferExecutionV2{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.updateOrder)
	return t
}

func (e *LogisticOrderCommonTransferExecutionV2) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		return err
	}
	e.updateOrder.Tag = e.GetActionOrderReq().TagMap
	// 解析状态机条件
	var (
		fireConditionStr = conv.StringDefault(e.updateOrder.FireCondition, "{}")
		fireCondition    = make(map[string]interface{})
	)
	if fireConditionStr == "" {
		fireConditionStr = "{}"
	}
	err = utils.SonicUnmarshal(fireConditionStr, &fireCondition)
	if err != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] unmarshal fire_condition str failed, err=%+v", err)
		return errdef.NewRawErr(errdef.ParamErr, "fireCondition格式错误")
	}

	if len(e.GetFSMCondition()) > 0 {
		fireCondition = e.MergeCondition(fireCondition, e.GetFSMCondition())
	}
	// 获取默认状态机跳转条件
	fireCondition2 := GetShLogisticCondition(ctx, e.GetOrder(), e.GetActionOrderReq().TagMap)
	if len(fireCondition2) > 0 {
		fireCondition = e.MergeConditionV2(fireCondition, fireCondition2)
	}

	bizErr := e.FireWithCondition(ctx, fireCondition)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] fire failed, err=%s, fireCondition=%s",
			bizErr.Error(), tools.GetLogStr(fireCondition))
		return bizErr
	}

	return nil
}

func (e *LogisticOrderCommonTransferExecutionV2) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		updateOrder  = e.updateOrder
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		orderService = service.NewOrderService()
		stateMachine = e.GetStateMachine()
		updateParams = &service_model.UpdateOrderParams{
			UpdateOrderName:   updateOrder.OrderName,
			UpdateOrderDesc:   updateOrder.OrderDesc,
			UpdateTotalAmount: updateOrder.TotalAmount,
			Operator:          actionReq.GetOperator(),
		}
	)
	if e.GetStateMachine().GetOriginalState() != e.GetStateMachine().CurState() {
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	}
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))

	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}

	if len(updateOrder.GetExtra()) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.GetExtra())
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(updateOrder.Tag) > 0 || len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(updateOrder.Tag) > 0 {
			for key, value := range updateOrder.Tag {
				tagMap[key] = value
			}
		}
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		bizErr = orderService.UpdateOrderTag(ctx, orderID, tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if updateOrder.BuyerInfo != nil {
		buyerID := packer.CommonTradeSubjectIDGet(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerID = &buyerID
		buyerInfo := packer.CommonTradeSubjectSerialize(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerExtra = &buyerInfo
	}

	if updateOrder.SellerInfo != nil {
		sellerID := packer.CommonTradeSubjectIDGet(updateOrder.SellerInfo)
		updateParams.UpdateSellerID = &sellerID
		sellerInfo := packer.CommonTradeSubjectSerialize(updateOrder.SellerInfo)
		updateParams.UpdateSellerExtra = &sellerInfo
	}

	if updateOrder.ProductInfo != nil {
		p := updateOrder.ProductInfo
		if p.ProductID != "" {
			updateParams.UpdateProductID = &p.ProductID
		}
		if p.SkuID != "" {
			updateParams.UpdateSkuID = &p.SkuID
		}
		if p.ProductName != "" {
			updateParams.UpdateProductName = &p.ProductName
		}
		if p.ProductType != 0 {
			updateParams.UpdateProductType = conv.Int32Ptr(int32(p.ProductType))
		}
		if p.ProductUnitPrice != 0 {
			updateParams.UpdateProductUnitPrice = &p.ProductUnitPrice
		}
		if p.ProductQuantity != 0 {
			updateParams.UpdateProductQuantity = &p.ProductQuantity
		}
		if p.ProductExtra != nil {
			updateParams.UpdateProductExtra = p.ProductExtra
		}
		if p.ProductVersion != 0 {
			updateParams.UpdateProductVersion = conv.Int64Ptr(p.ProductVersion)
		}
	}

	if updateOrder.Operator != nil {
		updateParams.Operator = updateOrder.Operator
	}

	// 更新订单信息
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *LogisticOrderCommonTransferExecutionV2) MergeConditionV2(paramMap map[string]interface{}, calMap map[string]interface{}) map[string]interface{} {
	for key, value := range calMap {
		paramMap[key] = value
	}
	return paramMap
}
