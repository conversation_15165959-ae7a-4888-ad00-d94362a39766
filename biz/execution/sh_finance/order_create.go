package sh_finance

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_finance_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type shFinanceOrderCreateExecution struct {
	confPtr sh_finance_model.ShFinanceConfig
	*executor.CreateBaseExecution
}

func NewShFinanceOrderCreateExecution(ctx context.Context, req interface{}) executor.IExecution {
	t := &shFinanceOrderCreateExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, req.(*engine.CreateOrderReq), &t.confPtr)
	return t
}

func (s *shFinanceOrderCreateExecution) Process(ctx context.Context) error {
	req := s.GetCreateOrderReq()

	//1.1 构造主订单
	order, err := s.buildShFinanceOrder(ctx, req)
	if err != nil {
		return err
	}
	//1.2 构造资金单
	fundsOrder := s.buildFundsOrder(ctx, req, order.OrderID)
	//2.保存
	OrderCreateParam := &service_model.Order{
		FweOrder:    order,
		FinanceList: []*db_model.FFinanceOrder{fundsOrder},
		TagMap:      req.OrderTag,
	}
	if len(req.Extra) > 0 {
		OrderCreateParam.BizExtra = req.Extra
	}
	bizErr := service.NewOrderService().CreateOrder(ctx, OrderCreateParam)
	if bizErr != nil {
		return bizErr
	}
	return nil
}

func (s *shFinanceOrderCreateExecution) Result() interface{} {
	return s.GetOrderID()
}

func (s *shFinanceOrderCreateExecution) buildShFinanceOrder(ctx context.Context, req *engine.CreateOrderReq) (*db_model.FweOrder, error) {
	buyerID := packer.CommonTradeSubjectIDGet(req.BuyerInfo)
	buyerExtra := packer.CommonTradeSubjectSerialize(req.BuyerInfo)
	sellerID := packer.CommonTradeSubjectIDGet(req.SellerInfo)
	sellerExtra := packer.CommonTradeSubjectSerialize(req.SellerInfo)
	operator := req.Operator

	if operator == nil {
		return nil, errdef.NewParamsErr("operator error")
	}

	totalAmount := packer.CommonFinanceGetTotalAmount(req.GetFinanceList())

	productInfo := req.ProductInfo
	OrderEntity := &db_model.FweOrder{
		TenantType:         int32(req.Identity.TenantType),
		BizScene:           req.Identity.BizScene,
		SmVersion:          req.Identity.SmVersion,
		OrderID:            s.GetOrderID(),
		OrderStatus:        int32(statemachine.ShFinanceWaitingReview),
		OrderName:          req.OrderName,
		OrderDesc:          req.OrderDesc,
		ProductID:          productInfo.ProductID,
		ProductType:        int32(productInfo.ProductType),
		ProductName:        productInfo.ProductName,
		ProductExtra:       productInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(req.ProductInfo.ProductDetail)),
		SkuID:              productInfo.SkuID,
		ProductQuantity:    int32(productInfo.ProductQuantity),
		ProductUnitPrice:   productInfo.ProductUnitPrice,
		TotalAmount:        totalAmount,
		TotalPayAmount:     totalAmount,
		TotalSubsidyAmount: 0,
		TradeType:          int32(fwe_trade_common.TradeType_Full),
		BuyerID:            buyerID,
		BuyerExtra:         &buyerExtra,
		SellerID:           sellerID,
		SellerExtra:        &sellerExtra,
		IsTest:             conv.BoolToInt32(req.IsTest),
		Creator:            operator.OperatorID,
		CreatorName:        operator.OperatorName,
		Operator:           operator.OperatorID,
		OperatorName:       operator.OperatorName,
		IdempotentID:       req.GetIdemID(),
	}
	return OrderEntity, nil
}

func (s *shFinanceOrderCreateExecution) buildFundsOrder(ctx context.Context, req *engine.CreateOrderReq, orderId string) *db_model.FFinanceOrder {

	financeConfig := s.confPtr
	financeInfo := req.FinanceList[0]
	fundsOrderId, _ := utils.TryGenId(3)
	fundsOrderDO := &db_model.FFinanceOrder{
		TenantType:       int32(s.GetBizIdentity().TenantType),
		BizScene:         s.GetBizIdentity().BizScene,
		AppID:            financeConfig.AppID,
		MerchantID:       financeConfig.MerchantID,
		Mid:              financeConfig.Mid,
		OrderID:          orderId,
		OrderName:        req.OrderName,
		TradeType:        string(consts.TradeTypeTransferHz),
		FinanceOrderID:   utils.MakeFinanceOrderID(fundsOrderId, financeInfo.FinanceOrderType),
		FinanceOrderType: financeInfo.FinanceOrderType,
		Amount:           financeInfo.Amount,
		ProcessAmount:    0,
		Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
	}
	return fundsOrderDO
}
