package sh_finance

import (
	"code.byted.org/gopkg/metrics"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"context"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_finance_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type SignAndTransferExecution struct {
	*callback.ContCallbackBaseExecution
	confPtr sh_finance_model.ShFinanceConfig
}

func NewSignAndTransferSucExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)

	t := &SignAndTransferExecution{}
	t.ContCallbackBaseExecution = callback.NewContCallbackBaseExecution(ctx, req, &t.confPtr)
	return t
}

func (e *SignAndTransferExecution) Process(ctx context.Context) error {
	//1.取订单
	order := e.GetOrder()
	if len(order.FinanceList) != 1 {
		logs.CtxError(ctx, "[SignAndTransferExecution] FinanceOrder data err,orderID = %v", order.FweOrder.OrderID)
		return errdef.NewRawErr(errdef.DirtyDataException, "FinanceList data error")
	}
	financeOrder := order.FinanceList[0]
	//2. 取 配置
	config := e.confPtr
	// 构造rpcParam
	rpcParam := e.buildTransferParam(order.FweOrder, financeOrder, &config)
	//3. 同步转账
	bizErr := service.NewTradePayment().Transfer(ctx, rpcParam)
	if bizErr != nil {
		// 加 metrics 打点
		tag := metrics.Tag("code", string(bizErr.Code()))
		utils.EmitCounter(consts.MetricsTransferFail, 1, tag)
		logs.CtxError(ctx, "[SignAndTransferExecution] transfer rpc error,err = %+v", bizErr)
		return bizErr
	}
	//4. 更新主订单状态,父类自动执行
	return nil
}

func (e *SignAndTransferExecution) buildTransferParam(order *db_model.FweOrder, financeOrder *db_model.FFinanceOrder, config *sh_finance_model.ShFinanceConfig) *payment.TransferReq {

	operator := e.GetActionOrderReq().Operator

	rpcParam := &payment.TransferReq{
		Identity:          e.GetBizIdentity(),
		MerchantID:        config.MerchantID,
		AppID:             config.AppID,
		MerchantName:      config.MerchantName,
		UID:               config.Uid,
		UIDType:           config.UidType,
		FinanceOrderID:    financeOrder.FinanceOrderID,
		TransferOrderNo:   financeOrder.FinanceOrderID,
		TransferOrderName: order.OrderName,
		TransferOrderDesc: order.OrderDesc,
		TradeTime:         time.Now().Unix(),
		Currency:          nil,
		Amount:            financeOrder.Amount,
		PayerInfo: &payment.Participant{
			IdentifyType: config.Payer.IdentifyType,
			Aid:          nil,
			MerchantID:   config.Payer.MerchantId,
			UID:          config.Payer.Uid,
			UIDType:      config.Payer.UidType,
			AppID:        config.Payer.AppId,
		},
		PayeeInfo: &payment.Participant{
			IdentifyType: config.Payee.IdentifyType,
			Aid:          nil,
			MerchantID:   config.Payee.MerchantId,
			UID:          config.Payee.Uid,
			UIDType:      config.Payee.UidType,
			AppID:        config.Payee.AppId,
		},
		Extra:    nil,
		Operator: operator,
		Base:     base.NewBase(),
	}
	return rpcParam
}
