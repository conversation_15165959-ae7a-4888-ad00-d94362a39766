package sh_finance

import (
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"context"
	"encoding/json"
	"strconv"
	"time"

	"code.byted.org/motor/fwe_trade_engine/biz/packer"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_finance_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/overpass/motor_fwe_contract_core/kitex_gen/motor/fwe_contract/core"
)

type ContSignExecution struct {
	*executor.ActionBaseExecution
	confPtr sh_finance_model.ShFinanceConfig
	contNo  string
}

func NewContSignExecution(ctx context.Context, req interface{}) executor.IExecution {
	t := &ContSignExecution{}
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req.(*engine.ActionOrderReq), &t.confPtr, nil, options...)
	return t
}

func (e *ContSignExecution) LockTimeout() time.Duration {
	// 创建合同接口比较慢，调整超时时间为 20s
	return 20 * time.Second
}

func (e *ContSignExecution) Process(ctx context.Context) error {
	//1。取订单
	order := e.GetOrder()
	//2。取配置
	config := e.confPtr
	//3。创建合同所需要参数
	contParam, err := e.buildCreateContParam(ctx, order, &config)
	if err != nil {
		logs.CtxError(ctx, "[ContSignExecution] buildCreateContParam err , error = %+v", err)
		return err
	}
	contractNo, bizErr := service.NewContractService().CreateContract(ctx, contParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[ContSignExecution] CreateContract err , error = %+v", bizErr)
		return bizErr
	}
	e.contNo = contractNo
	return nil
}

func (e *ContSignExecution) buildCreateContParam(ctx context.Context, orderBO *service_model.Order, config *sh_finance_model.ShFinanceConfig) (*service_model.ContractCreateParam, error) {

	operator := e.GetActionOrderReq().Operator
	fweOrder := orderBO.FweOrder
	id, err := strconv.ParseInt(operator.OperatorID, 10, 64)
	if err != nil {
		return nil, errdef.NewBizErr(errdef.ParamErr, err, "operator error")
	}

	param := &service_model.ContractCreateParam{
		OrderID:        fweOrder.OrderID,
		TenantType:     fweOrder.TenantType,
		BizScene:       fweOrder.BizScene,
		ContType:       sh_finance_model.DefaultContType,
		OperatorID:     id,
		OperatorName:   operator.OperatorName,
		TmplID:         config.ContTmplID,
		NeedSignNoCert: false,
		TmplParams:     nil,
		SignPartList:   nil,
		InOutData: &service_model.InOutData{
			Currency: int32(core.Currency_CNY),
			TotalOut: fweOrder.TotalAmount,
			TotalIn:  0,
		},
		CallbackEvent: utils.MakeCallbackEvent(consts.SHFinanceSignSucAndTransferSuc),
		CallbackExtra: utils.MakeContractCallbackExtra(fweOrder.OrderID),
	}
	buyer, bizErr := packer.CommonTradeSubjectDeserialize(orderBO.FweOrder.BuyerID, *orderBO.FweOrder.BuyerExtra)
	if bizErr != nil {
		logs.CtxError(ctx, "[ContSignExecution] buildCreateContParam error ,err = %+v", bizErr)
		return nil, bizErr
	}
	seller, bizErr := packer.CommonTradeSubjectDeserialize(orderBO.FweOrder.SellerID, *orderBO.FweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxError(ctx, "[ContSignExecution] buildCreateContParam error ,err = %+v", bizErr)
		return nil, bizErr
	}
	// 从订单解析出模板动态变量
	contParams, bizErr := e.buildContParams(ctx, orderBO, config, buyer, seller)
	if bizErr != nil {
		return nil, bizErr
	}
	param.TmplParams = contParams
	// 从订单买卖双方解析出合同签约人数据
	param.SignPartList = e.buildSignParts(config, buyer, seller)

	return param, nil
}

func (e *ContSignExecution) Result() interface{} {
	return e.contNo
}

func (e *ContSignExecution) buildContParams(ctx context.Context, order *service_model.Order, config *sh_finance_model.ShFinanceConfig, buyer, seller *fwe_trade_common.TradeSubjectInfo) (map[string]string, *errdef.BizErr) {

	paramMap := make(map[string]string)

	var financeProduct sh_finance_model.FinanceProductInfo
	fweOrder := order.FweOrder
	if fweOrder.ProductExtra == nil || *(fweOrder.ProductExtra) == "" {
		return nil, errdef.NewRawErr(errdef.DirtyDataException, "ProductExtra is blank")
	}
	err := json.Unmarshal([]byte(*fweOrder.ProductExtra), &financeProduct)
	if err != nil {
		logs.CtxError(ctx, "[ContSignExecution] buildContParams err,orderID=%v", fweOrder.OrderID)
		return nil, errdef.NewBizErr(errdef.DirtyDataException, err, "parse tag error")
	}

	paramMap["motorcycle_type"] = financeProduct.CarsModelType
	paramMap["plate_number"] = financeProduct.CarsPlateNumber
	paramMap["Vehicle_identification_number"] = financeProduct.VinCode

	paramMap["telephone"] = buyer.CompanyInfo.OwnerPhone
	paramMap["car_count_money"] = strconv.FormatFloat(float64(fweOrder.TotalAmount)/100.00, 'f', 2, 64)

	paramMap["car_lease_money"] = strconv.FormatFloat(float64(financeProduct.LoanAmount)/100.00, 'f', 2, 64)
	paramMap["year_interest"] = strconv.FormatFloat(financeProduct.InterestRate*100, 'f', 4, 64)
	paramMap["car_lease_date"] = strconv.FormatInt(financeProduct.LoanDays, 10)
	paramMap["car_bond"] = strconv.FormatFloat(float64(financeProduct.CarsBond)/100.00, 'f', 2, 64)

	now := time.Now()
	paramMap["first_party_Signing_date"] = now.Format("2006-01-02")
	paramMap["second_party_Signing_date"] = now.Format("2006-01-02")

	// email 取不到
	paramMap["e_mail"] = "-"
	return paramMap, nil
}

func (e *ContSignExecution) buildSignParts(config *sh_finance_model.ShFinanceConfig, buyer, seller *fwe_trade_common.TradeSubjectInfo) []*service_model.SignPart {

	res := make([]*service_model.SignPart, 0)
	//pa 甲方 = 付钱方 = 四轮（厦门）融资租赁有限公司 = seller
	pa := &service_model.SignPart{
		SignPosition: int32(core.SignPosition_PA),
		IsInner:      true,
		CardType:     int32(core.CardType_CreditCode),
		IdentName:    seller.CompanyInfo.CompanyName,
		IdentID:      seller.CompanyInfo.CreditCode,
		//SignerName:   seller.CompanyInfo.OwnerName,
		//SignerPhone:  seller.CompanyInfo.OwnerPhone,
	}
	//pb 乙方 = 购买方 = 重庆空间变换科技有限公司懂懂分公司 = buyer
	pb := &service_model.SignPart{
		SignPosition: int32(core.SignPosition_PB),
		IsInner:      true,
		CardType:     int32(core.CardType_CreditCode),
		IdentName:    buyer.CompanyInfo.CompanyName,
		IdentID:      buyer.CompanyInfo.CreditCode,
		//SignerName:   buyer.CompanyInfo.OwnerName,
		//SignerPhone:  buyer.CompanyInfo.OwnerPhone,
	}
	res = append(res, pa, pb)
	return res
}
