package sh_finance

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_finance_model"
	"encoding/json"
	"testing"
	"time"
)

func TestStruct2Json(t *testing.T) {
	config := sh_finance_model.ShFinanceConfig{
		MerchantID:   "",
		MerchantName: "",
		AppID:        "",
		Uid:          "",
		UidType:      0,
		ContTmplID:   0,
		Payer: &sh_finance_model.Participant{
			IdentifyType: "",
			Aid:          0,
			MerchantId:   "",
			Uid:          "",
			UidType:      0,
			AppId:        "",
		},
		Payee: &sh_finance_model.Participant{
			IdentifyType: "",
			Aid:          0,
			MerchantId:   "",
			Uid:          "",
			UidType:      0,
			AppId:        "",
		},
	}

	bytes, err := json.Marshal(config)
	if err != nil {
		return
	}
	t.Log(bytes)
}

func TestJson(t *testing.T) {

	source := "test"

	a := ""
	err := json.Unmarshal([]byte(source), &a)
	t.Log(err)
	t.Log(a)
}

func TestZero(t *testing.T) {

	zero := time.Unix(0, 0)

	t.Log(zero.Format("2006-01-02 15:04:05"))

}
