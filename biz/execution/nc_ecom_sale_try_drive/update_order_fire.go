package nc_ecom_sale_try_drive

import (
	"context"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/motor/fwe_trade_common/scene/nc_ecom_sale_try_drive"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
)

type UpdateTryDriveOrderExecution struct {
	*executor.ActionBaseExecution
	bizReq *execution_common.UpdateOrderReq
}

func NewUpdateTryDriveOrderExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &UpdateTryDriveOrderExecution{}
	e.bizReq = new(execution_common.UpdateOrderReq)
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.bizReq)
	return e
}

func (e *UpdateTryDriveOrderExecution) CheckParams(ctx context.Context) error {
	return nil
}

func (e *UpdateTryDriveOrderExecution) Process(ctx context.Context) error {
	var (
		req          = e.GetActionOrderReq()
		updateParams = &service_model.UpdateOrderParams{
			Operator: req.GetOperator(),
		}
		orderID = req.OrderID
		bizReq  = e.bizReq
	)

	if bizErr := e.FireDefault(ctx); bizErr != nil {
		logs.CtxError(ctx, "[UpdateTryDriveOrderExecution.Process] err: %+v", bizErr.Error())
		return bizErr
	}

	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	if conv.Int32PtrToVal(updateParams.UpdateOrderStatus, 0) == int32(nc_ecom_sale_try_drive.NewCarEcomSaleFinishTryDriveSt.Value()) {
		updateParams.UpdateFinishTime = gptr.Of(time.Now())
	}
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		logs.CtxError(ctx, "[UpdateTryDriveOrderExecution.Process] UpdateOrder err: %+v", bizErr.Error())
		return bizErr
	}

	if _, bizErr := service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().BizScene, bizReq.GetTag()); bizErr != nil {
		logs.CtxError(ctx, "[UpdateTryDriveOrderExecution.Process] UpdateTag err: %+v", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *UpdateTryDriveOrderExecution) Result() interface{} {
	return nil
}
