package nc_ecom_sale_try_drive

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/ternary"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/nc_ecom_sale_try_drive"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type CreateTryDriveOrderExecution struct {
	*executor.CreateBaseExecution
}

func NewCreateTryDriveOrderExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &CreateTryDriveOrderExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), nil)
	return t
}

func (e *CreateTryDriveOrderExecution) CheckParams(ctx context.Context) error {

	return nil
}

func (e *CreateTryDriveOrderExecution) Process(ctx context.Context) error {
	var (
		req                      = e.GetCreateOrderReq()
		stateMachine             = e.GetStateMachine()
		operatorID, operatorName string
		bizErr                   *errdef.BizErr
	)

	if req.GetOperator() != nil {
		operatorID, operatorName = req.GetOperator().OperatorID, req.GetOperator().OperatorName
	}

	if err := stateMachine.Fire(ctx, nc_ecom_sale_try_drive.NewCarEcomSaleCreateTryDriveOrderEt.Value(), nil); err != nil {
		bizErr = errdef.NewBizErrWithCode(errdef.ServerException, err)
		logs.CtxError(ctx, "[CreateTryDriveOrderExecution.Process] err: %+v", bizErr.Error())
		return bizErr
	}

	if bizErr = service.NewOrderService().CreateOrder(ctx, &service_model.Order{
		FweOrder: &db_model.FweOrder{
			TenantType:         int32(req.GetIdentity().GetTenantType()),
			BizScene:           req.GetIdentity().BizScene,
			SmVersion:          req.GetIdentity().GetSmVersion(),
			OrderID:            e.GetOrderID(),
			OrderStatus:        int32(stateMachine.CurState()), // 初始态
			OrderName:          req.GetOrderName(),
			OrderDesc:          req.GetOrderDesc(),
			ProductID:          "",
			ProductType:        0,
			ProductName:        "",
			ProductDetail:      nil,
			ProductExtra:       nil,
			SkuID:              "",
			SkuVersion:         0,
			UID:                0,
			MobileID:           0,
			ProductVersion:     0,
			ProductQuantity:    0,
			ProductUnitPrice:   0,
			TotalAmount:        0,
			TotalPayAmount:     0,
			TotalSubsidyAmount: 0,
			TradeType:          int32(req.TradeType),
			BuyerID:            packer.CommonTradeSubjectIDGet(req.BuyerInfo),
			BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(req.BuyerInfo)),
			SellerID:           packer.CommonTradeSubjectIDGet(req.SellerInfo),
			SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(req.SellerInfo)),
			IsTest:             ternary.Int32(req.IsTest, 1, 0),
			Creator:            operatorID,
			CreatorName:        operatorName,
			Operator:           operatorID,
			OperatorName:       operatorName,
		},
		TagMap:   req.OrderTag,
		BizExtra: req.Extra,
	}); bizErr != nil {
		logs.CtxError(ctx, "[CreateTryDriveOrderExecution.Process] CreateOrder err: %+v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *CreateTryDriveOrderExecution) Result() interface{} {
	return e.GetOrderID()
}
