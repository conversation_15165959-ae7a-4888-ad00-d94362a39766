package aftermarket_retail

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type AuditRejectExecution struct {
	*executor.ActionBaseExecution
}

func NewAuditRejectExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFinish})

	req := rpcReq.(*engine.ActionOrderReq)
	e := &AuditRejectExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, nil, opts...)

	return e
}

func (e *AuditRejectExecution) OpenTX() bool {
	return true
}

func (e *AuditRejectExecution) CheckParams(ctx context.Context) error {
	if bizErr := checkActionReq(ctx, e.GetActionOrderReq()); bizErr != nil {
		return bizErr
	}

	return nil
}

func (e *AuditRejectExecution) Process(ctx context.Context) error {
	if bizErr := restoreStock(ctx, e.GetOrder().FweOrder); bizErr != nil {
		return bizErr
	}

	return nil
}
