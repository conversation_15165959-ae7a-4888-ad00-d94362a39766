package aftermarket_retail

import (
	"context"
	"encoding/json"
	"fmt"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/model/aftermarket_retail_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"
	"github.com/tidwall/gjson"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/statemachine/ams_state"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

func checkActionReq(ctx context.Context, actionReq *engine.ActionOrderReq) *errdef.BizErr {
	if actionReq == nil || actionReq.Identity == nil || actionReq.Identity.TenantType == 0 ||
		actionReq.Identity.BizScene == 0 || actionReq.OrderID == "" || actionReq.Action == "" {
		logs.CtxWarn(ctx, "[checkActionReq] param empty")
		return errdef.NewParamsErr("有必传参数为空，请检查")
	}
	return nil
}

func getAfterMarketRetailCondition(order *db_model.FweOrder) map[string]interface{} {
	var (
		needLogistics bool
		productExtra  = *order.ProductExtra
	)

	needLogistics = gjson.Get(productExtra, "need_logistics").Bool()

	return map[string]interface{}{
		ams_state.CondAfterMarketNeedLogistics.Val(): needLogistics,
	}
}

func restoreStock(ctx context.Context, order *db_model.FweOrder) *errdef.BizErr {
	incrStockReq, err := buildIncrStockReq(ctx, order)
	if err != nil {
		logs.CtxError(ctx, "[restoreStock] buildIncrStockReq err: %+v", err)
		return errdef.NewRawErr(errdef.ProductStockRpcErr, "buildIncrStockReq err")
	}

	if bizErr := service.NewProductStockService().IncrStock(ctx, incrStockReq); bizErr != nil {
		logs.CtxError(ctx, "[restoreStock] IncrStock err: %+v", err)
		return bizErr
	}

	return nil
}

func buildIncrStockReq(ctx context.Context, order *db_model.FweOrder) (*product_stock.IncrStockReq, error) {
	incrStockReq := product_stock.NewIncrStockReq()
	var productExtra aftermarket_retail_model.ProductExtra
	if order.ProductExtra == nil {
		return nil, fmt.Errorf("order ProductExtra is nil")
	}

	if err := json.Unmarshal([]byte(*order.ProductExtra), &productExtra); err != nil {
		logs.CtxError(ctx, "[buildIncrStockReq] json unmarshal err: %+v", err)
		return nil, err
	}

	items := make([]*product_stock.ChangeStockItem, 0)
	for _, spec := range productExtra.Specs {
		items = append(items, &product_stock.ChangeStockItem{
			StockUnit: &product_stock.StockUnit{
				SkuId:     spec.GetSkuId(),
				StockType: product_stock.StockType_NORMAL,
			},
			StockNum: conv.Int64Ptr(spec.GetStockNum()),
		})
	}

	incrStockReq.SetItems(items)
	incrStockReq.SetToken(fmt.Sprintf("order_center_%s", order.OrderID))
	incrStockReq.SetToken(consts.PSM)

	return incrStockReq, nil
}
