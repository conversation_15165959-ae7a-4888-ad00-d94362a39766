package aftermarket_retail

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type AuditPassExecution struct {
	*executor.ActionBaseExecution
}

func NewAuditPassExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &AuditPassExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, nil)

	return e
}

func (e *AuditPassExecution) CheckParams(ctx context.Context) error {
	bizErr := checkActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	return nil
}

func (e *AuditPassExecution) PreProcess(ctx context.Context) error {
	var (
		err    error
		bizErr *errdef.BizErr
	)

	err = e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err: %+v", err)
		return bizErr
	}

	bizErr = e.FireWithCondition(ctx, getAfterMarketRetailCondition(e.GetOrder().FweOrder))
	if bizErr != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err: %+v", bizErr)
		return bizErr
	}

	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})

	return nil
}

func (e *AuditPassExecution) Process(ctx context.Context) error {
	return nil
}
