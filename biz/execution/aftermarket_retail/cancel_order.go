package aftermarket_retail

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type CancelExecution struct {
	*executor.ActionBaseExecution
	param *sh_nation_sell_model.CancelModel
}

func NewCancelExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFinish})
	return &CancelExecution{
		ActionBaseExecution: executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, nil, opts...),
	}
}

func (e *CancelExecution) OpenTX() bool {
	return true
}

func (e *CancelExecution) CheckParams(ctx context.Context) error {
	if bizErr := checkActionReq(ctx, e.GetActionOrderReq()); bizErr != nil {
		return bizErr
	}

	return nil
}

func (e *CancelExecution) Process(ctx context.Context) error {
	if bizErr := restoreStock(ctx, e.GetOrder().FweOrder); bizErr != nil {
		return bizErr
	}

	return nil
}
