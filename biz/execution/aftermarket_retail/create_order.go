package aftermarket_retail

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/aftermarket_retail_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"
	"code.byted.org/overpass/motor_fwe_trade_product_am_supply/kitex_gen/motor/fwe_trade/product_am_supply"
	"github.com/tidwall/gjson"
)

type CreateOrderExecution struct {
	*executor.CreateBaseExecution
	createOrderResult *model.CreateOrderResult
}

func NewCreateOrderExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &CreateOrderExecution{}
	e.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, rpcReq.(*engine.CreateOrderReq), nil)
	e.createOrderResult = &model.CreateOrderResult{}
	return e
}

func (e *CreateOrderExecution) Result() interface{} {
	e.createOrderResult.OrderID = e.GetOrderID()
	return e.createOrderResult
}

func (e *CreateOrderExecution) CheckParams(ctx context.Context) error {
	req := e.GetCreateOrderReq()

	if req.GetProductInfo() == nil {
		logs.CtxWarn(ctx, "[afterMarketRetailExecution.CheckParams] product_info empty")
		return errors.New("product_info empty")
	}

	if req.BuyerInfo == nil || (req.BuyerInfo.PersonInfo == nil && req.BuyerInfo.CompanyInfo == nil) {
		logs.CtxWarn(ctx, "[afterMarketRetailExecution.CheckParams] lack buyer_info")
		return errors.New("buyer_info empty")
	}
	if req.SellerInfo == nil || req.SellerInfo.FweMerchant == nil || req.SellerInfo.FweMerchant.FweAccountID == "" {
		logs.CtxWarn(ctx, "[afterMarketRetailExecution.CheckParams] lack seller_info")
		return errors.New("seller_info empty")
	}

	return nil
}

func (e *CreateOrderExecution) Process(ctx context.Context) error {
	var (
		ownerOrder    bool // 是否商家owner下单
		needLogistics bool // 是否需要物流邮寄
	)

	req := e.GetCreateOrderReq()

	if req.Extra != nil {
		ownerOrderStr := req.GetExtra()["owner_order"]
		ownerOrder, _ = strconv.ParseBool(ownerOrderStr)
	}

	if req.ProductInfo != nil && req.ProductInfo.ProductExtra != nil {
		needLogistics = gjson.Get(req.GetProductInfo().GetProductExtra(), "need_logistics").Bool()
	}

	conditionMap := map[string]interface{}{
		"needLogistics": needLogistics,
		"ownerOrder":    ownerOrder,
	}

	// 驱动状态
	err := e.GetStateMachine().Fire(ctx, consts.CreateAction, conditionMap)
	if err != nil {
		bizErr := errdef.NewBizErr(errdef.ServerException, err, "CreateOrderExecution")
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 构造主订单
	order := e.buildAfterMarketRetailOrder(req)

	// 构造订单model
	OrderCreateParam := &service_model.Order{
		FweOrder: order,
		TagMap:   req.GetOrderTag(),
	}
	if len(req.GetExtra()) > 0 {
		OrderCreateParam.BizExtra = req.GetExtra()
	}

	// 创建订单
	bizErr := service.NewOrderService().CreateOrder(ctx, OrderCreateParam)
	if bizErr != nil {
		return bizErr
	}

	// 构造扣减库存请求
	decrStockReq, err := e.buildDecrStockReq(ctx, order.OrderID, req.GetProductInfo())
	if err != nil {
		return errdef.NewBizErr(errdef.ServerException, err, "")
	}

	// 扣减库存
	failedSkuIds, bizErr := service.NewProductStockService().DecrStock(ctx, decrStockReq)
	if bizErr != nil {
		logs.Error("[CreateOrderExecution] DecrStock err=%+v", bizErr.Error())
		marshal, _ := json.Marshal(product_am_supply.AftermarketRetailBizResp{
			FailedSkuIds: failedSkuIds,
		})
		e.createOrderResult.BizResponse = string(marshal)
		return bizErr
	}

	return nil
}

func (e *CreateOrderExecution) buildDecrStockReq(ctx context.Context, orderID string, productInfo *fwe_trade_common.ProductInfo) (
	*product_stock.DecrStockReq, error) {
	decrStockReq := product_stock.NewDecrStockReq()

	var productExtra aftermarket_retail_model.ProductExtra
	if err := json.Unmarshal([]byte(productInfo.GetProductExtra()), &productExtra); err != nil {
		logs.CtxError(ctx, "[buildDecrStockReq] json unmarshal err: %+v", err)
		return nil, fmt.Errorf("buildDecrStockReq err")
	}

	items := make([]*product_stock.ChangeStockItem, 0)
	for _, spec := range productExtra.Specs {
		items = append(items, &product_stock.ChangeStockItem{
			StockUnit: &product_stock.StockUnit{
				SkuId:     spec.GetSkuId(),
				StockType: product_stock.StockType_NORMAL,
			},
			StockNum: conv.Int64Ptr(spec.GetStockNum()),
		})
	}

	decrStockReq.SetItems(items)
	decrStockReq.SetToken(fmt.Sprintf("order_center_%s", orderID))
	decrStockReq.SetSource(consts.PSM)
	decrStockReq.SetNeedFailedSkus(conv.BoolPtr(true))
	return decrStockReq, nil
}

func (e *CreateOrderExecution) buildAfterMarketRetailOrder(req *engine.CreateOrderReq) *db_model.FweOrder {
	var (
		bizScene    = consts.GetBizScene(req.GetIdentity().GetBizScene())
		tradeType   = int32(consts.GetTradeTypeByBizScene(bizScene))
		productInfo = req.GetProductInfo()
		totalAmount = packer.CommonProductGetTotalAmount(req.GetProductInfo())
	)

	orderEntity := &db_model.FweOrder{
		TenantType:         int32(req.GetIdentity().GetTenantType()),
		BizScene:           int32(bizScene),
		SmVersion:          req.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(e.GetStateMachine().CurState()),
		OrderName:          req.GetOrderName(),
		OrderDesc:          req.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		ProductExtra:       productInfo.ProductExtra,
		ProductVersion:     productInfo.GetProductVersion(),
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		TotalAmount:        totalAmount,
		TotalPayAmount:     totalAmount,
		TotalSubsidyAmount: 0,
		TradeType:          tradeType,
		BuyerID:            packer.CommonTradeSubjectIDGet(req.GetBuyerInfo()),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(req.GetBuyerInfo())),
		SellerID:           packer.CommonTradeSubjectIDGet(req.GetSellerInfo()),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(req.GetSellerInfo())),
		IsTest:             conv.BoolToInt32(req.GetIsTest()),
		Creator:            req.GetOperator().GetOperatorID(),
		CreatorName:        req.GetOperator().GetOperatorName(),
		Operator:           req.GetOperator().GetOperatorID(),
		OperatorName:       req.GetOperator().GetOperatorName(),
		IdempotentID:       req.GetIdemID(),
	}

	return orderEntity
}
