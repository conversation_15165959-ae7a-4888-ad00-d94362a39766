package sh_nation_sell_yzt

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	sdkConst "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
)

type confirmOrderExecution struct {
	*executor.ActionBaseExecution
	confirmOrder *sh_nation_sell_model.ConfirmOrderModel
	conf         *sh_nation_sell_model.ConfV2

	oldFinanceMap     map[int32]*db_model.FFinanceOrder
	newFinanceInfoMap map[int32]*fwe_trade_common.FinanceInfo
}

func NewConfirmOrderExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	e := &confirmOrderExecution{
		confirmOrder: new(sh_nation_sell_model.ConfirmOrderModel),
		conf:         new(sh_nation_sell_model.ConfV2),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), e.conf, e.confirmOrder, options...)
	return e
}

func (e *confirmOrderExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	if e.confirmOrder == nil {
		return errdef.NewParamsErr("缺少确认订单参数")
	}
	// 检查资金单
	if e.confirmOrder.OrderFinanceInfo == nil {
		return nil
	}
	// tradeType 和 financeInfo 映射校验
	mapping := map[fwe_trade_common.TradeType][]sdkConst.FinanceOrderType{
		fwe_trade_common.TradeType_EarnestFinalLoan:    {sdkConst.FinanceEarnest, sdkConst.FinanceAdvance, sdkConst.FinanceLoan},
		fwe_trade_common.TradeType_FinalLoan:           {sdkConst.FinanceAdvance, sdkConst.FinanceLoan},
		fwe_trade_common.TradeType_EarnestAdvanceFinal: {sdkConst.FinanceEarnest, sdkConst.FinanceAdvance, sdkConst.FinanceFinal},
		fwe_trade_common.TradeType_AdvanceFinal:        {sdkConst.FinanceAdvance, sdkConst.FinanceFinal},
	}
	if !checkFinanceType(e.confirmOrder.OrderFinanceInfo.FinanceList, mapping[e.confirmOrder.OrderFinanceInfo.TradeType]) {
		bizErr = errdef.NewParamsErr("资金列表和TradeType不匹配")
		logs.CtxWarn(ctx, "[CheckReq] err=%s", bizErr.Error())
		return bizErr
	}
	for _, financeInfo := range e.confirmOrder.OrderFinanceInfo.FinanceList {
		if len(financeInfo.FeeItemList) == 0 {
			return errdef.NewParamsErr("FeeItemList 不能为空")
		}
		tmpAmount := int64(0)
		for _, item := range financeInfo.FeeItemList {
			tmpAmount += item.Amount
		}
		if tmpAmount != financeInfo.Amount {
			return errdef.NewParamsErr("FeeItemList 费项金额之和跟资金单金额不等")
		}
	}
	return nil
}

func (e *confirmOrderExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	var (
		order             = e.GetOrder()
		oldFinanceMap     = make(map[int32]*db_model.FFinanceOrder, 0)
		newFinanceInfoMap = make(map[int32]*fwe_trade_common.FinanceInfo, 0)
	)

	// 不修改 订单资金信息直接返回 不作预处理
	if e.confirmOrder.OrderFinanceInfo == nil {
		return nil
	}

	for _, oldFinance := range order.FinanceList {
		oldFinanceMap[oldFinance.FinanceOrderType] = oldFinance
	}

	// getLoanData
	loanAmount := int64(0)
	feeItems := make([]*fwe_trade_common.FeeItem, 0)
	for _, newFinance := range e.confirmOrder.OrderFinanceInfo.FinanceList {
		if newFinance.FinanceType == sdkConst.FinanceLoan.Value() {
			loanAmount = newFinance.Amount
			feeItems = newFinance.FeeItemList
			break
		}
	}
	// newFinanceInfo
	for _, newFinance := range e.confirmOrder.OrderFinanceInfo.FinanceList {
		// 跳过贷款资金单
		if newFinance.FinanceType == sdkConst.FinanceLoan.Value() {
			continue
		}
		newFinanceInfoMap[newFinance.FinanceType] = &fwe_trade_common.FinanceInfo{
			FinanceOrderType: newFinance.FinanceType,
			Amount:           newFinance.Amount,
			FeeItemList:      newFinance.FeeItemList,
		}
	}
	// 订金金额不能修改
	if changedFinance(newFinanceInfoMap[sdkConst.FinanceEarnest.Value()], oldFinanceMap[sdkConst.FinanceEarnest.Value()]) {
		return errdef.NewParamsErr("订金金额不允许修改")
	}
	// 全量更新逻辑，一定有首付款资金单,mock 首付款资金单修改
	advanceType := int32(sdkConst.FinanceAdvance)
	financeInfo, exist := newFinanceInfoMap[int32(sdkConst.FinanceAdvance)]
	if !exist {
		return errdef.NewParamsErr("缺少首付款资金单")
	}
	financeInfo.LoanAmount = loanAmount
	financeInfo.FeeItemList = mergeFeeItem(financeInfo.FeeItemList, feeItems)
	newFinanceInfoMap[advanceType] = financeInfo

	e.oldFinanceMap = oldFinanceMap
	e.newFinanceInfoMap = newFinanceInfoMap

	return nil
}

//是否修改金额
func changedFinance(new *fwe_trade_common.FinanceInfo, old *db_model.FFinanceOrder) bool {
	if old == nil && new == nil {
		return false
	}
	if old == nil && new != nil {
		return true
	}
	if old != nil && new == nil {
		return true
	}
	return old.Amount != new.Amount
}

func (e *confirmOrderExecution) Process(ctx context.Context) error {
	var (
		bizErr           *errdef.BizErr
		orderService     = service.NewOrderService()
		order            = e.GetOrder()
		orderFinanceInfo = e.confirmOrder.OrderFinanceInfo
		productInfo      = e.confirmOrder.ProductInfo
		buyerInfo        = e.confirmOrder.BuyerInfo
		updateParams     = &service_model.UpdateOrderParams{}
	)

	// 0. 无需修改 直接退出
	if productInfo == nil && orderFinanceInfo == nil && buyerInfo == nil {
		return nil
	}

	// 1. 更新资金单
	if bizErr = e.createOrUpdateFinanceOrder(ctx, orderFinanceInfo); bizErr != nil {
		logs.CtxError(ctx, "[confirmOrderExecution] createOrUpdateFinanceOrder failed, err=%s", bizErr.Error())
		return bizErr
	}

	// 2. 修改订单
	if productInfo != nil {
		updateParams.UpdateProductID = conv.StringPtr(productInfo.ProductID)
		updateParams.UpdateProductType = conv.Int32Ptr(int32(productInfo.ProductType))
		updateParams.UpdateProductName = conv.StringPtr(productInfo.ProductName)
		updateParams.UpdateProductExtra = productInfo.ProductExtra
		updateParams.UpdateSkuID = conv.StringPtr(productInfo.SkuID)
		updateParams.UpdateProductQuantity = conv.Int64Ptr(productInfo.ProductQuantity)
		updateParams.UpdateProductUnitPrice = conv.Int64Ptr(productInfo.ProductUnitPrice)
		updateParams.UpdateSkuVersion = conv.Int64Ptr(productInfo.SkuVersion)
		updateParams.UpdateProductVersion = conv.Int64Ptr(productInfo.ProductVersion)
	}
	if orderFinanceInfo != nil {
		updateParams.UpdateTradeType = conv.Int32Ptr(int32(orderFinanceInfo.TradeType))
		if orderFinanceInfo.TotalAmount != nil && *orderFinanceInfo.TotalAmount != int64(0) {
			updateParams.UpdateTotalAmount = orderFinanceInfo.TotalAmount
		}
	}
	if buyerInfo != nil {
		buyerID := packer.CommonTradeSubjectIDGet(buyerInfo)
		updateParams.UpdateBuyerID = &buyerID
		serialBuyerInfo := packer.CommonTradeSubjectSerialize(buyerInfo)
		updateParams.UpdateBuyerExtra = &serialBuyerInfo
	}

	bizErr = orderService.UpdateOrder(ctx, order.FweOrder.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[confirmOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 更新ebs
	err := orderService.CreateOrUpdateOrderSubject(ctx, []*fwe_trade_common.TradeSubjectInfo{buyerInfo})
	if err != nil {
		logs.CtxError(ctx, "[CreateOrUpdateOrderSubject] error, err = %v", err.Error())
		return err
	}
	return nil
}

func (e *confirmOrderExecution) PostProcess(ctx context.Context) error {
	var (
		fweOrder = e.GetOrder().FweOrder
		bizReq   = e.confirmOrder
	)
	// 更新订单tag
	_, bizErr := service.NewTagService().UpdateTag(ctx, fweOrder.OrderID, e.GetBizIdentity().BizScene, bizReq.OrderTag)
	if bizErr != nil {
		logs.CtxError(ctx, "[confirmOrderExecution-PostProcess] update tag error, err = %v", bizErr.Error())
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *confirmOrderExecution) createOrUpdateFinanceOrder(ctx context.Context, orderFinanceInfo *sh_nation_sell_model.OrderFinanceInfo) *errdef.BizErr {
	var (
		fweOrder               = e.GetOrder().FweOrder
		bizErr                 *errdef.BizErr
		oldFinanceMap          = e.oldFinanceMap
		newFinanceInfoMap      = e.newFinanceInfoMap
		createFinanceOrderList = make([]*fwe_trade_common.FinanceInfo, 0)
		financeOrderService    = service.NewFinanceOrderService()
	)
	// 订单资金信息为空 不修改直接退出
	if orderFinanceInfo == nil {
		return nil
	}
	for _, financeInfo := range newFinanceInfoMap {
		// 定金不能修改
		if financeInfo.FinanceOrderType == sdkConst.FinanceEarnest.Value() {
			continue
		}
		oldFinance, exist := oldFinanceMap[financeInfo.FinanceOrderType]
		// 新增
		if !exist {
			createFinanceOrderList = append(createFinanceOrderList, e.buildFinanceInfo(ctx, financeInfo))
			continue
		}
		// 更新
		if oldFinance.Status != int32(fwe_trade_common.FinanceStatus_NotHandle) {
			return errdef.NewParamsErr("当前阶段不允许修改资金单")
		}
		err1 := financeOrderService.UpdateV2(ctx, oldFinance.FinanceOrderID, e.buildUpdateParam(financeInfo))
		if err1 != nil {
			logs.CtxError(ctx, "[confirmOrderExecution-createOrUpdateFinanceOrder] UpdateV2 error, err = %v", err1.Error())
			return err1
		}
	}
	// 新增资金单为空
	if len(createFinanceOrderList) == 0 {
		return nil
	}
	baseOrder := &service_model.OrderBaseParam{
		Identity:  e.GetBizIdentity(),
		OrderID:   fweOrder.OrderID,
		OrderName: fweOrder.OrderName,
	}
	bizErr = service.NewFinanceOrderService().CreateV2(ctx, baseOrder, createFinanceOrderList)
	if bizErr != nil {
		logs.CtxError(ctx, "[confirmOrderExecution-createOrUpdateFinanceOrder] CreateV2 error, err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *confirmOrderExecution) buildFinanceInfo(ctx context.Context, info *fwe_trade_common.FinanceInfo) *fwe_trade_common.FinanceInfo {
	var (
		fweOrder = e.GetOrder().FweOrder
	)
	res := &fwe_trade_common.FinanceInfo{
		FinanceOrderID:   utils.MakeFinanceOrderIDTool(fweOrder.OrderID, info.FinanceOrderType),
		FinanceOrderType: info.FinanceOrderType,
		PayStatus:        fwe_trade_common.FinanceStatus_NotHandle,
		Amount:           info.Amount,
		TradeCategory:    fwe_trade_common.TradeCategory_Pay,
		FeeItemList:      info.FeeItemList,
	}
	return res
}

func (e *confirmOrderExecution) buildUpdateParam(info *fwe_trade_common.FinanceInfo) *service_model.UpdateFinanceParams {

	feeItemDetail := ""
	if len(info.FeeItemList) > 0 {
		feeItemDetail = tools.GetLogStr(info.FeeItemList)
	}
	res := &service_model.UpdateFinanceParams{
		UpdateAmount:        conv.Int64Ptr(info.Amount),
		UpdateLoanAmount:    conv.Int64Ptr(info.LoanAmount),
		UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_NotHandle)),
		UpdateFeeItemDetail: conv.StringPtr(feeItemDetail),
	}
	return res
}

func checkFinanceType(financeList []*sh_nation_sell_model.FinanceInfo, requireTypes []sdkConst.FinanceOrderType) bool {
	if len(financeList) != len(requireTypes) {
		return false
	}

	existMap := make(map[sdkConst.FinanceOrderType]bool)
	for _, v := range financeList {
		existMap[sdkConst.FinanceOrderType(v.FinanceType)] = true
	}

	for _, financeType := range requireTypes {
		if _, exist := existMap[financeType]; !exist {
			return false
		}
	}

	return true
}
