package sh_nation_sell_yzt

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/sh_nation_sell"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type cancelRefundExecution struct {
	*common.UnionRefundExecution
}

func NewCancelRefundExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &cancelRefundExecution{}
	e.UnionRefundExecution = common.NewUnionRefundBaseExecution(ctx, actionReq)
	return e
}

func (e *cancelRefundExecution) PreProcess(ctx context.Context) error {

	err := e.UnionRefundExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[cancelRefundExecution-PreProcess] UnionRefundExecution.PreProcess error, err = %v", err.Error())
		return err
	}
	var (
		fweOrder = e.GetOrder().FweOrder
	)
	// 存在有效消费贷，或者存在在途订单贷，不允许发起退款
	if !AllowCancelRefund(e.GetOrder()) {
		logs.CtxWarn(ctx, "[cancelRefundExecution-PreProcess] can not cancel, orderid = %v", fweOrder.OrderID)
		return errdef.NewParamsErr("存在有效消费贷，不能取消")
	}

	// 合同结构化校验
	orderStatus := e.GetOrder().FweOrder.OrderStatus
	// 处于终止后同后带退款才需要校验
	if orderStatus == int32(sh_nation_sell.SHNationSellV3CancelRefundWaitSt.Value()) {
		// 校验
		param := &service.CheckAmountByContParam{
			OrderID:          e.GetActionOrderReq().OrderID,
			FinanceOrderType: e.BizReq.RefundType,
			Amount:           e.RefundAmount,
			TradeCategory:    fwe_trade_common.TradeCategory_Refund,
		}
		pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
		if bizErr != nil {
			logs.CtxError(ctx, "[UnionPayExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
			return bizErr
		}
		if !pass {
			logs.CtxError(ctx, "[UnionPayExecution] safe check not pass, blockMsg=%s", blockMsg)
			return errdef.NewRawErr(errdef.SafeCheckNotPassErr, blockMsg)
		}
	}

	return nil
}
