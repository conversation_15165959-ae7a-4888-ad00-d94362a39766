package sh_nation_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

type PayAdvanceMoneyCallbackExecution struct {
	*callback.UnionPayCallbackBaseExecution
}

func NewPayAdvanceMoneyCallbackExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &PayAdvanceMoneyCallbackExecution{}
	exe.UnionPayCallbackBaseExecution = callback.NewUnionPayCallbackCommonBaseExecution(ctx, sourceReq)
	return exe
}

func (e *PayAdvanceMoneyCallbackExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}
	return nil
}

func (e *PayAdvanceMoneyCallbackExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[PayAdvanceMoneyCallbackExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}
	// 条件跳转
	conditions := GetShSellCondition(ctx, e.GetOrder(), 0)
	bizErr := e.FireWithCondition(ctx, conditions)
	if bizErr != nil {
		logs.CtxError(ctx, "[PayAdvanceMoneyCallbackExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
