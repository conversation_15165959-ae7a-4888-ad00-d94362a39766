package sh_nation_sell_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/gopkg/tools"
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type DeliverCarExecution struct {
	*executor.ActionBaseExecution
	param *sh_nation_sell_model.DeliveryCarModel
}

func NewDeliverCarExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &DeliverCarExecution{
		param: new(sh_nation_sell_model.DeliveryCarModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param)
	return e
}

func (e *DeliverCarExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	if e.param == nil {
		logs.CtxWarn(ctx, "[DeliverCarExecution.CheckParams] param error")
		return errdef.NewParamsErr("缺少交车参数")
	}

	return nil
}

func (e *DeliverCarExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}
	// 条件跳转
	conditions := GetShSellCondition(ctx, e.GetOrder(), 0)
	bizErr := e.FireWithCondition(ctx, conditions)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[DeliverCarExecution-PreProcess] FireWithCondition error, err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *DeliverCarExecution) PostProcess(ctx context.Context) error {
	var (
		fweOrder     = e.GetOrder().FweOrder
		bizScene     = e.GetBizIdentity().BizScene
		bizReq       = e.param
		stateMachine = e.GetStateMachine()
	)
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{
		WhereOrderStatus:     []int32{int32(stateMachine.GetOriginalState())},
		UpdateOrderStatus:    conv.Int32Ptr(int32(stateMachine.CurState())),
		UpdateBeforeStatus:   conv.Int32Ptr(int32(stateMachine.GetOriginalState())),
		UpdateOrderSubStatus: conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates())),
		Operator:             e.GetActionOrderReq().GetOperator(),
	}
	if bizErr := service.NewOrderService().UpdateOrder(ctx, fweOrder.OrderID, updateParams); bizErr != nil {
		logs.CtxError(ctx, "[DeliverCarExecution.PostProcess] update order_status failed, err=%+v", bizErr.Error())
		return bizErr
	}
	// 更新tag
	_, bizErr := service.NewTagService().UpdateTag(ctx, fweOrder.OrderID, bizScene, bizReq.OrderTag)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[DeliverCarExecution-PostProcess] UpdateTag error, err = %v", bizErr.Error())
		return bizErr
	}

	return e.ActionBaseExecution.PostProcess(ctx)
}
