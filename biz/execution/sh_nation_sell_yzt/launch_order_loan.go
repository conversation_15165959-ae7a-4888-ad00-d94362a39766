package sh_nation_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type LaunchOrderLoanExecution struct {
	*executor.ActionBaseExecution
	param *sh_nation_sell_model.LaunchOrderLoanModel
}

func NewLaunchOrderLoanExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &LaunchOrderLoanExecution{
		param: new(sh_nation_sell_model.LaunchOrderLoanModel),
	}
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param, options...)
	return e
}

func (e *LaunchOrderLoanExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}
	return nil
}

func (e *LaunchOrderLoanExecution) PreProcess(ctx context.Context) error {
	if err := e.ActionBaseExecution.PreProcess(ctx); err != nil {
		return err
	}

	if OrderLoanExist(e.GetOrder()) && !OrderLoanIsFail(e.GetOrder()) {
		return errdef.NewRawErr(errdef.DataErr, "已经发起过订单贷 请勿重发")
	}

	return nil
}

func (e *LaunchOrderLoanExecution) Process(ctx context.Context) error {
	var (
		fweOrder    = e.GetOrder().FweOrder
		bizScene    = e.GetBizIdentity().BizScene
		orderTagMap = e.param.GetOrderTag()
	)
	// 更新是否存在订单贷为true，表示已存在
	orderTagMap[HasOrderLoan] = HasOrderLoanExistStr
	// 如果是失败后，重复发起，则将tag中的订单贷状态(infra_order_loan_status)清空, 否则在分账时不会卡住
	if OrderLoanIsFail(e.GetOrder()) {
		orderTagMap[OrderLoanStatus] = ""
	}
	// 更新tag
	_, bizErr := service.NewTagService().UpdateTag(ctx, fweOrder.OrderID, bizScene, orderTagMap)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[LaunchOrderLoanExecution-PostProcess] UpdateTag error, err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}
