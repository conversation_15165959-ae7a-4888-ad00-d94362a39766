package sh_nation_sell_yzt

const (
	LoanFirst = 1 // 先贷款后过户
	LoanAfter = 2 // 先过户后贷款

	LoanFirstStr = "loan_first"
	LoanAfterStr = "loan_after"

	HasOrderLoanExistStr = "true"
	OrderLoanStatusSucc  = 1
	OrderLoanStatusFail  = 2

	OrderLoanSuccess = "success"
	OrderLoanFail    = "fail"
)

// tag 名称
const (
	LoanType                 = "infra_loan_type"                   // 贷款类型 值为loan_first和loan_after
	LoanDetail               = "infra_loan_detail"                 // 贷款详情 值为LoanConfirmModel序列化后的值
	HasOrderLoan             = "infra_has_order_loan"              // 是否存在订单贷 true false
	OrderLoanStatus          = "infra_order_loan_status"           // 订单贷状态 success fail
	OrderLoanAmount          = "infra_order_loan_amount"           // 订单贷金额 当该值存在时，需要触发订单贷放款
	OrderLoanRealAmount      = "infra_order_loan_real_amount"      // 订单贷放款金额
	OrderLoanRealDate        = "infra_order_loan_real_date"        // 订单贷放款日期
	OrderLoanRepaymentAmount = "infra_order_loan_repayment_amount" // 订单贷还款金额
	OrderLoanRepaymentDate   = "infra_order_loan_repayment_date"   // 订单贷还款日期
)
