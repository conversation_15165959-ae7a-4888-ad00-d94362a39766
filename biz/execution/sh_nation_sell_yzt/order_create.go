package sh_nation_sell_yzt

import (
	"context"
	"errors"
	"strconv"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	sdkConst "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/scene/sh_sell_ef/common"
	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type createOrderExecution struct {
	*executor.CreateBaseExecution
	conf                sh_nation_sell_model.ConfV2
	yztSubMerchantInfos []*finance_account.SubMerchantInfo
}

// NewCreateOrderExecution 创建订单，仅支持biz_scene=090100
func NewCreateOrderExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	e := &createOrderExecution{}
	e.conf = sh_nation_sell_model.ConfV2{}
	e.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, sourceReq.(*engine.CreateOrderReq), &e.conf)
	return e
}

func (e *createOrderExecution) CheckParams(ctx context.Context) error {
	var (
		req = e.GetCreateOrderReq()
	)
	// 资金风控检查
	if bizErr := utils.CheckFundRiskOfAmount(ctx, req.IsTest, req.GetTotalAmount(), e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-CheckParams] CheckFundRiskOfAmount error, err = %v ", bizErr.Error())
		return bizErr
	}
	if req.ProductInfo == nil {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack product_info")
		return errors.New("lack product_info")
	}

	if req.BuyerInfo == nil || (req.BuyerInfo.PersonInfo == nil && req.BuyerInfo.CompanyInfo == nil) {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack buyer_info")
		return errors.New("lack buyer_info")
	}

	if req.BuyerInfo.PersonInfo != nil && req.BuyerInfo.PersonInfo.MobileID == nil {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] PersonInfo lack MobileID")
		return errors.New("lack MobileID")
	}
	// 校验费项
	for _, financeInfo := range req.GetFinanceList() {
		if len(financeInfo.FeeItemList) == 0 {
			return errdef.NewParamsErr("FeeItemList 不能为空")
		}
		tmpAmount := int64(0)
		for _, item := range financeInfo.FeeItemList {
			tmpAmount += item.Amount
		}
		if tmpAmount != financeInfo.Amount {
			return errdef.NewParamsErr("FeeItemList 费项金额之和跟资金单金额不等")
		}
	}

	if req.SellerInfo == nil || req.SellerInfo.FweMerchant == nil || req.SellerInfo.FweMerchant.FweAccountID == "" {
		logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] lack seller_info")
		return errors.New("lack seller_info")
	}
	// 验证账户
	//if bizErr := e.validFinanceAccount(ctx); bizErr != nil {
	//	logs.CtxWarn(ctx, "[createOrderExecution.CheckParams] validFinanceAccount error, err = %v", bizErr.Error())
	//	return bizErr
	//}
	return nil
}

func (e *createOrderExecution) PreProcess(ctx context.Context) error {
	err := e.CreateBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] PreProcess error, err = %v", err)
		return err
	}
	var createReq = e.GetCreateOrderReq()
	condMap := make(map[string]interface{})
	boolValue, err := strconv.ParseBool(createReq.OrderTag[common.CondShSellIsSkipEarnest.Val()])
	if err != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] ParseOrderTag error, err = %v", err)
		return errdef.NewParamsErr("orderTag ParseBool error, err = %v")
	}
	condMap[common.CondShSellIsSkipEarnest.Val()] = boolValue
	bizErr := e.FireWithCondition(ctx, condMap)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-PreProcess] FireWithCondition error, err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *createOrderExecution) Process(ctx context.Context) error {
	var (
		req                      = e.GetCreateOrderReq()
		fweOrder                 *db_model.FweOrder
		financeList              []*db_model.FFinanceOrder
		isTest                   int32
		uid, mobileID            int64
		operatorID, operatorName string
		totalAmount              = packer.CommonFinanceGetTotalAmount(req.GetFinanceList())
		buyerID                  = packer.CommonTradeSubjectIDGet(req.BuyerInfo)
		buyerExtra               = packer.CommonTradeSubjectSerialize(req.BuyerInfo)
		sellerID                 = packer.CommonTradeSubjectIDGet(req.SellerInfo)
		sellerExtra              = packer.CommonTradeSubjectSerialize(req.SellerInfo)
		serviceProviderID        = packer.CommonTradeSubjectIDGet(req.ServiceProviderInfo)
		serviceProviderExtra     = packer.CommonTradeSubjectSerialize(req.ServiceProviderInfo)
	)

	if req.IsTest {
		isTest = 1
	}
	if req.GetBuyerInfo().GetPersonInfo() != nil {
		uid = req.GetBuyerInfo().GetPersonInfo().GetUID()
	}
	if req.GetBuyerInfo().GetPersonInfo() != nil {
		mobileID = req.GetBuyerInfo().GetPersonInfo().GetMobileID()
	}
	var orderAmount = totalAmount
	if req.TotalAmount != int64(0) {
		orderAmount = req.TotalAmount
	}
	// 操作人
	if req.GetOperator() != nil {
		operatorID, operatorName = req.GetOperator().OperatorID, req.GetOperator().OperatorName
	}

	fweOrder = &db_model.FweOrder{
		TenantType:         int32(req.GetIdentity().GetTenantType()),
		BizScene:           req.GetIdentity().BizScene,
		SmVersion:          req.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(e.GetStateMachine().CurState()), // 初始态
		OrderName:          req.GetOrderName(),
		OrderDesc:          req.GetOrderDesc(),
		ProductID:          req.ProductInfo.ProductID,
		ProductType:        int32(req.ProductInfo.ProductType),
		ProductName:        req.ProductInfo.ProductName,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(req.ProductInfo.ProductDetail)),
		ProductExtra:       req.ProductInfo.ProductExtra,
		SkuID:              req.ProductInfo.SkuID,
		SkuVersion:         req.ProductInfo.SkuVersion,
		UID:                uid,
		MobileID:           mobileID,
		ProductVersion:     req.ProductInfo.ProductVersion,
		ProductQuantity:    int32(req.ProductInfo.ProductQuantity),
		ProductUnitPrice:   req.ProductInfo.ProductUnitPrice,
		TotalAmount:        orderAmount,
		TotalPayAmount:     totalAmount,
		TotalSubsidyAmount: 0,
		TradeType:          int32(req.TradeType),
		BuyerID:            buyerID,
		BuyerExtra:         &buyerExtra,
		SellerID:           sellerID,
		SellerExtra:        &sellerExtra,
		ServiceProviderID:  serviceProviderID,
		IsTest:             isTest,
		Creator:            operatorID,
		CreatorName:        operatorName,
		Operator:           operatorID,
		OperatorName:       operatorName,
	}
	if serviceProviderExtra != "" {
		fweOrder.ServiceProviderExtra = &serviceProviderExtra
	}
	// mock loanAmount
	loanAmount, loanFeeItems := getLoanData(req.FinanceList)
	if bizErr := utils.CheckFundRiskOfAmount(ctx, req.IsTest, loanAmount, e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-Process] CheckFundRiskOfAmount error, err = %v", bizErr.Error())
		return bizErr
	}
	for _, financeInfo := range req.FinanceList {
		// 跳过贷款
		if financeInfo.FinanceOrderType == sdkConst.FinanceLoan.Value() {
			continue
		}
		tmpLoanAmount := int64(0)
		// 在首付款资金单上放贷款金额
		if financeInfo.FinanceOrderType == sdkConst.FinanceAdvance.Value() {
			tmpLoanAmount = loanAmount
			financeInfo.FeeItemList = mergeFeeItem(financeInfo.FeeItemList, loanFeeItems)
		}
		var feeDetail string
		if len(financeInfo.FeeItemList) > 0 {
			feeDetail = tools.GetLogStr(financeInfo.FeeItemList)
		}
		financeList = append(financeList, &db_model.FFinanceOrder{
			TenantType:       int32(req.GetIdentity().GetTenantType()),
			BizScene:         req.GetIdentity().BizScene,
			OrderID:          e.GetOrderID(),
			OrderName:        req.OrderName,
			TradeType:        "",
			TradeCategory:    int32(fwe_trade_common.TradeCategory_Pay),
			FinanceOrderID:   utils.MakeFinanceOrderIDTool(fweOrder.OrderID, financeInfo.FinanceOrderType),
			FinanceOrderType: financeInfo.FinanceOrderType,
			Amount:           financeInfo.Amount,
			ProcessAmount:    0,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
			FeeItemDetail:    &feeDetail,
			LoanAmount:       tmpLoanAmount,
		})
	}

	order := &service_model.Order{
		FweOrder:       fweOrder,
		FinanceList:    financeList,
		TagMap:         req.OrderTag,
		BizExtra:       req.Extra,
		TradeSplitInfo: req.SplitInfo,
	}
	bizErr := service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[createOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *createOrderExecution) Result() interface{} {
	return e.GetOrderID()
}

func (e *createOrderExecution) validFinanceAccount(ctx context.Context) *errdef.BizErr {
	var (
		req          = e.GetCreateOrderReq()
		sellerInfo   = req.GetSellerInfo().GetFweMerchant()
		fweAccountID = packer.GetFinanceAccountId(sellerInfo.GetFinanceAccountID(), sellerInfo.GetFweAccountID())
		yztConfig    = e.conf.YZTPayMerchant
		posConfig    = e.conf.POSPayMerchant
	)
	if yztConfig == nil || yztConfig.MerchantID == "" || posConfig == nil || posConfig.MerchantID == "" {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] config error, config = %v", tools.GetLogStr(e.conf))
		return errdef.NewRawErr(errdef.LackConfigErr, "yztConfig/posConfig config error")
	}
	fweSubMerchantInfo, bizErr := service.NewFinanceAccountService().GetFweSubMerchantInfo(ctx, req.Identity.TenantType, fweAccountID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] GetFweSubMerchantInfo error, err = %v", bizErr.Error())
		return bizErr
	}
	// 验证 yzt
	yztSubMerchantInfos, bizErr := checkFinanceAccount(yztConfig.MerchantID, fweSubMerchantInfo, yztChannels)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] checkFinanceAccount yzt-account error, err = %v", bizErr.Error())
		return bizErr
	}
	e.yztSubMerchantInfos = yztSubMerchantInfos
	// 验证 pos
	_, bizErr = checkFinanceAccount(posConfig.MerchantID, fweSubMerchantInfo, posChannels)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-validFinanceAccount] checkFinanceAccount pos(hz)-account error, err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}
