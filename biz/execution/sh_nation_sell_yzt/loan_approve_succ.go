package sh_nation_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/sh_nation_sell"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

type loanApproveSuccessExecution struct {
	*common.ConsumerLoanPassExecution
}

func NewLoanApproveSuccessExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &loanApproveSuccessExecution{}
	e.ConsumerLoanPassExecution = common.NewAgreementPayBaseExecution(ctx, rpcReq)
	return e
}

func (e *loanApproveSuccessExecution) PreProcess(ctx context.Context) error {
	err := e.ConsumerLoanPassExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}
	// 设置回调动作
	err = e.SetCallbackAction(sh_nation_sell.SHNationSellLoanOverEt.Value())
	if err != nil {
		logs.CtxWarn(ctx, "[loanApproveSuccessExecution-PreProcess] SetCallbackAction error, err = %v", err)
		return err
	}
	return nil
}
