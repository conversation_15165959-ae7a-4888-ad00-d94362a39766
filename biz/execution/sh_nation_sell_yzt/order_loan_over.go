package sh_nation_sell_yzt

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
	"strconv"
)

type OrderLoanOverExecution struct {
	*executor.ActionBaseExecution
	param *sh_nation_sell_model.OrderLoanCallbackModel
}

func NewOrderLoanOverExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	e := &OrderLoanOverExecution{
		param: new(sh_nation_sell_model.OrderLoanCallbackModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param, options...)
	return e
}

func (e *OrderLoanOverExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	if e.param.Status != OrderLoanStatusSucc && e.param.Status != OrderLoanStatusFail {
		return errdef.NewParamsErr("订单贷回调未知状态")
	}

	if e.param.Amount < 0 {
		return errdef.NewParamsErr("订单贷金额小于0")
	}

	return nil
}

func (e *OrderLoanOverExecution) PreProcess(ctx context.Context) error {
	if err := e.ActionBaseExecution.PreProcess(ctx); err != nil {
		return err
	}

	if !OrderLoanExist(e.GetOrder()) {
		return errdef.NewRawErr(errdef.DataErr, "未发起订单贷 回调无效")
	}

	return nil
}

func (e *OrderLoanOverExecution) Process(ctx context.Context) error {
	var (
		orderLoanStatus string
		orderLoanAmount int64
		fweOrder        = e.GetOrder().FweOrder
	)

	// 订单贷放款成功
	if e.param.Status == OrderLoanStatusSucc {
		orderLoanStatus = OrderLoanSuccess

		// 订单贷大于 订单总金额
		if e.param.Amount >= e.GetOrder().FweOrder.TotalAmount {
			return errdef.NewParamsErr("订单贷成功 放款金额大于等于订单总金额")
		}

		orderLoanAmount = e.param.Amount
	}

	// 订单贷放款失败
	if e.param.Status == OrderLoanStatusFail {
		orderLoanStatus = OrderLoanFail
		orderLoanAmount = 0
	}

	// 1. 更新tag
	newOrderTags := map[string]string{
		OrderLoanStatus:          orderLoanStatus,
		OrderLoanAmount:          strconv.FormatInt(orderLoanAmount, 10),
		OrderLoanRealAmount:      e.param.RealAmount,
		OrderLoanRealDate:        e.param.RealDate,
		OrderLoanRepaymentAmount: e.param.RepaymentAmount,
		OrderLoanRepaymentDate:   e.param.RepaymentDate,
	}
	_, bizErr := service.NewTagService().UpdateTag(ctx, fweOrder.OrderID, e.GetBizIdentity().BizScene, newOrderTags)
	if bizErr != nil {
		logs.CtxError(ctx, "[OrderLoanOverExecution.Process] UpdateOrderTag failed, err is %+v", bizErr)
		return bizErr
	}
	return nil
}
