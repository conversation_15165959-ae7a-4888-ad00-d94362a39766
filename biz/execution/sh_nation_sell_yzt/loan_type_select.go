package sh_nation_sell_yzt

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type selectLoanTypeExecution struct {
	*executor.ActionBaseExecution
	param *sh_nation_sell_model.LoanTypeSelectModel
}

func NewSelectLoanTypeExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &selectLoanTypeExecution{
		param: new(sh_nation_sell_model.LoanTypeSelectModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param)
	return e
}

func (e *selectLoanTypeExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	param := e.param
	if param == nil || (param.LoanType != LoanFirst && param.LoanType != LoanAfter) {
		logs.CtxWarn(ctx, "[selectLoanTypeExecution.CheckParams] param error")
		return errdef.NewParamsErr("缺少贷款方式参数")
	}
	return nil
}

func (e *selectLoanTypeExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}
	// 条件跳转
	conditions := GetShSellCondition(ctx, e.GetOrder(), e.param.LoanType)
	bizErr := e.FireWithCondition(ctx, conditions)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[selectLoanTypeExecution-PreProcess] FireWithCondition error, err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}

// Process 设置贷款类型
func (e *selectLoanTypeExecution) Process(ctx context.Context) error {
	var (
		param        = e.param
		fweOrder     = e.GetOrder().FweOrder
		orderTag     = param.OrderTag
		stateMachine = e.GetStateMachine()
	)
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{
		WhereOrderStatus:   []int32{int32(stateMachine.GetOriginalState())},
		UpdateOrderStatus:  conv.Int32Ptr(int32(stateMachine.CurState())),
		UpdateBeforeStatus: conv.Int32Ptr(int32(stateMachine.GetOriginalState())),
		Operator:           e.GetActionOrderReq().GetOperator(),
	}
	if bizErr := service.NewOrderService().UpdateOrder(ctx, fweOrder.OrderID, updateParams); bizErr != nil {
		logs.CtxError(ctx, "[DeliverCarExecution.PostProcess] update order_status failed, err=%+v", bizErr.Error())
		return bizErr
	}
	// 更新tag
	loanType := LoanFirstStr
	if param.LoanType == LoanAfter {
		loanType = LoanAfterStr
	}
	if orderTag == nil {
		orderTag = make(map[string]string)
	}
	orderTag[loanType] = loanType
	_, bizErr := service.NewTagService().UpdateTag(ctx, fweOrder.OrderID, e.GetBizIdentity().BizScene, orderTag)
	if bizErr != nil {
		logs.CtxWarn(ctx, "", bizErr.Error())
		return bizErr
	}
	return nil
}
