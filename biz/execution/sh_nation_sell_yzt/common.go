package sh_nation_sell_yzt

import (
	"context"

	"code.byted.org/gopkg/lang/maps"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	sdkConst "code.byted.org/motor/fwe_trade_common/consts"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

const (
	CondParamRefundRemainingAmount = "remainingAmount"
)

var (
	yztChannels = []finance_account.TradeChannel{
		finance_account.TradeChannel_yzt_hz,
		finance_account.TradeChannel_yzt_alipay,
		finance_account.TradeChannel_syt_wx,
	}

	posChannels = []finance_account.TradeChannel{
		finance_account.TradeChannel_hz,
	}
)

func CheckActionReq(ctx context.Context, actionReq *engine.ActionOrderReq) *errdef.BizErr {
	if actionReq == nil || actionReq.Identity == nil || actionReq.Identity.TenantType == 0 ||
		actionReq.Identity.BizScene == 0 || actionReq.OrderID == "" || actionReq.Action == "" {
		logs.CtxWarn(ctx, "[CheckActionReq] param empty")
		return errdef.NewParamsErr("有必传参数为空，请检查")
	}
	return nil
}

// GetShSellCondition 获取状态机条件
func GetShSellCondition(ctx context.Context, order *service_model.Order, loanTypeParam int32) map[string]interface{} {
	var (
		hasLoan, isLoanFirst, hasOrderLoan, orderLoanOver bool
		finalPrice                                        int64
	)

	hasLoan = HasLoan(order)
	isLoanFirst = IsLoanFirst(order)
	hasOrderLoan = OrderLoanExist(order)
	orderLoanOver = OrderLoanIsOver(order)

	if loanTypeParam == LoanFirst {
		isLoanFirst = true
	}

	if finalFianceOrder := packer.FinanceGetByType(order.FinanceList, sdkConst.FinanceFinal.Value()); finalFianceOrder != nil {
		finalPrice = finalFianceOrder.Amount
	}

	return map[string]interface{}{
		consts.CondShSellHasLoan.Val():       hasLoan,
		consts.CondShSellIsLoanFirst.Val():   isLoanFirst,
		consts.CondShSellFinalPrice.Val():    finalPrice,
		consts.CondShSellHasOrderLoan.Val():  hasOrderLoan,
		consts.CondShSellOrderLoanOver.Val(): orderLoanOver,
	}
}

func HasLoan(order *service_model.Order) bool {
	var fweOrder = order.FweOrder
	if fweOrder.TradeType == int32(fwe_trade_common.TradeType_EarnestFinalLoan) ||
		fweOrder.TradeType == int32(fwe_trade_common.TradeType_FinalLoan) {
		return true
	}
	return false
}

func OrderLoanExist(order *service_model.Order) bool {
	var tagMap = order.TagMap
	if orderLoanExist, exist := tagMap[HasOrderLoan]; exist && orderLoanExist == HasOrderLoanExistStr {
		return true
	}
	return false
}

func OrderLoanIsOver(order *service_model.Order) bool {
	var tagMap = order.TagMap
	if status, exist := tagMap[OrderLoanStatus]; exist && (status == OrderLoanSuccess || status == OrderLoanFail) {
		return true
	}
	return false
}

func OrderLoanIsFail(order *service_model.Order) bool {
	var tagMap = order.TagMap
	if status, exist := tagMap[OrderLoanStatus]; exist && status == OrderLoanFail {
		return true
	}
	return false
}

func IsLoanFirst(order *service_model.Order) bool {
	var tagMap = order.TagMap
	if loanType, exist := tagMap[LoanType]; exist && loanType == LoanFirstStr {
		return true
	}
	return false
}

func checkFinanceAccount(merchantId string, fweSubMerchantInfo map[string][]*finance_account.SubMerchantInfo, validChannels []finance_account.TradeChannel) ([]*finance_account.SubMerchantInfo, *errdef.BizErr) {
	channels, exist := fweSubMerchantInfo[merchantId]
	if !exist {
		return nil, errdef.NewParamsErr("this seller dont owner channels")
	}
	validRes := slices.Filter(channels, func(dto *finance_account.SubMerchantInfo) bool {
		if slices.Contains(validChannels, dto.TradeChannel) && dto.ChannelStatus == finance_account.ChannelStatus_Ready {
			return true
		}
		return false
	}).([]*finance_account.SubMerchantInfo)
	if len(validRes) == 0 {
		return nil, errdef.NewParamsErr("this seller`s all channels is not ready")
	}
	return validRes, nil
}

func getLoanData(infos []*fwe_trade_common.FinanceInfo) (int64, []*fwe_trade_common.FeeItem) {
	loanAmount := int64(0)
	feeItems := make([]*fwe_trade_common.FeeItem, 0)
	for _, financeInfo := range infos {
		if financeInfo.FinanceOrderType == sdkConst.FinanceLoan.Value() {
			loanAmount = financeInfo.Amount
			feeItems = append(feeItems, financeInfo.FeeItemList...)
			break
		}
	}
	return loanAmount, feeItems
}

func mergeFeeItem(oldFeeItems, addFeeItems []*fwe_trade_common.FeeItem) []*fwe_trade_common.FeeItem {
	if len(addFeeItems) == 0 {
		return oldFeeItems
	}
	itemMap := make(map[string]*fwe_trade_common.FeeItem)
	for _, oldFeeItem := range oldFeeItems {
		itemMap[oldFeeItem.FeeItemName] = oldFeeItem
	}
	for _, feeItem := range addFeeItems {
		if itemMap[feeItem.FeeItemName] != nil {
			// 合并已有费项金额
			itemMap[feeItem.FeeItemName].Amount += feeItem.Amount
		} else {
			// 新增费项，追加到 map 中
			itemMap[feeItem.FeeItemName] = feeItem
		}
	}
	res := maps.Values(itemMap).([]*fwe_trade_common.FeeItem)
	return res
}

func AllowCancelRefund(order *service_model.Order) bool {
	var (
		orderTag = order.TagMap
	)
	// 存在消费贷，并且消费贷已经生效
	if HasLoan(order) && (orderTag[consts.InfraConsumerLoanStatusKey] == consts.InfraConsumerLoanStatusSuc) {
		return false
	}

	// 退款不限制订单贷
	//// 存在订单贷, 并且订单贷进行中/成功
	//if orderTag[HasOrderLoan] == HasOrderLoanExistStr && (orderTag[OrderLoanStatus] == "" || orderTag[OrderLoanStatus] == OrderLoanSuccess) {
	//	return false
	//}

	return true
}
