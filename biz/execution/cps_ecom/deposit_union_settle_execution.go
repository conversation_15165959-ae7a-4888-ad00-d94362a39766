package cps_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_ecom_product_common/lark_robot"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	adUtils "code.byted.org/motor/gopkg/motor_ad/utils"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/motor/gopkg/tools/tools_recover"
	"context"
	"encoding/json"
	"time"
)

type DstDepositUnionSettleExecution struct {
	*common.UnionSettleExecution
	depositPayExe      *common.UnionPayExecution
	smallDepositPayExe *common.UnionPayExecution
	bizReq             DstDepositSettleReq
	bizRsp             execution_common.SettleRsp
}

type DstDepositSettleReq struct {
	SettleReq               *execution_common.SettleReq
	DepositUnionPayReq      *execution_common.UnionPayReq
	SmallDepositUnionPayReq *execution_common.UnionPayReq
}

func NewDstDepositUnionSettleExecution(ctx context.Context, sourceActionReq interface{}) executor.IExecution {
	e := &DstDepositUnionSettleExecution{}
	sourceActionOrder := sourceActionReq.(*engine.ActionOrderReq)
	logs.CtxInfo(ctx, "[DstDepositUnionSettleExecution] configService get input=%s", sourceActionOrder.GetBizRequest())
	err := json.Unmarshal([]byte(sourceActionOrder.GetBizRequest()), &e.bizReq)
	if err != nil {
		logs.CtxError(ctx, "[DstDepositUnionSettleExecution] Unmarshal input failed error=%v", err)
		return nil
	}
	// 定金支付单
	if e.bizReq.DepositUnionPayReq != nil {
		depositActionReq := &engine.ActionOrderReq{}
		utils.DeepCopy(sourceActionOrder, depositActionReq)
		depositActionReq.BizRequest = adUtils.ToJSON(e.bizReq.DepositUnionPayReq)
		e.depositPayExe = common.NewUnionPayExecution(ctx, depositActionReq).(*common.UnionPayExecution)
		e.depositPayExe.Init(ctx)
	}
	// 订金支付单
	if e.bizReq.SmallDepositUnionPayReq != nil {
		depositActionReq := &engine.ActionOrderReq{}
		utils.DeepCopy(sourceActionOrder, depositActionReq)
		depositActionReq.BizRequest = adUtils.ToJSON(e.bizReq.SmallDepositUnionPayReq)
		e.smallDepositPayExe = common.NewUnionPayExecution(ctx, depositActionReq).(*common.UnionPayExecution)
		e.smallDepositPayExe.Init(ctx)
	}
	// 分账execution
	settleActionReq := &engine.ActionOrderReq{}
	utils.DeepCopy(sourceActionOrder, settleActionReq)
	settleActionReq.BizRequest = adUtils.ToJSON(e.bizReq.SettleReq)
	e.UnionSettleExecution = common.NewUnionSettleExecution(ctx, settleActionReq).(*common.UnionSettleExecution)
	return e
}

func (e *DstDepositUnionSettleExecution) CheckParams(ctx context.Context) error {
	if e.depositPayExe == nil && e.smallDepositPayExe == nil {
		return errdef.NewParamsErr("没有定金/订金支付单，不能发起实时分账")
	}
	if e.depositPayExe != nil {
		if bizErr := e.depositPayExe.CheckParams(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstDepositUnionSettleExecution] depositPayExe CheckParams error=%s", bizErr.Error())
			return bizErr
		}
	}
	if e.smallDepositPayExe != nil {
		if bizErr := e.smallDepositPayExe.CheckParams(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstDepositUnionSettleExecution] smallDepositPayExe CheckParams error=%s", bizErr.Error())
			return bizErr
		}
	}
	if e.UnionSettleExecution == nil {
		return errdef.NewParamsErr("没有分账信息，不能发起实时分账")
	}
	if bizErr := e.UnionSettleExecution.CheckParams(ctx); bizErr != nil {
		logs.CtxError(ctx, "[DstDepositUnionSettleExecution] UnionSettleExecution CheckParams error=%s", bizErr.Error())
		return bizErr
	}

	// 测试环境金额校验
	if e.isTestOrderOrEnv() {
		if e.bizReq.DepositUnionPayReq != nil && e.bizReq.DepositUnionPayReq.TotalAmount > 100 {
			return errdef.NewParamsErr("测试环境定金分账金额不能超过1元")
		}
		if e.bizReq.SmallDepositUnionPayReq != nil && e.bizReq.SmallDepositUnionPayReq.TotalAmount > 100 {
			return errdef.NewParamsErr("测试环境小订分账金额不能超过1元")
		}
	}

	return nil
}

func (e *DstDepositUnionSettleExecution) PreProcess(ctx context.Context) error {
	if e.depositPayExe != nil {
		if bizErr := e.depositPayExe.PreProcess(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstDepositUnionSettleExecution] depositPayExe PreProcess error=%s", bizErr.Error())
			return bizErr
		}
	}
	if e.smallDepositPayExe != nil {
		if bizErr := e.smallDepositPayExe.PreProcess(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstDepositUnionSettleExecution] smallDepositPayExe PreProcess error=%s", bizErr.Error())
			return bizErr
		}
	}
	if bizErr := e.UnionSettleExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[DstDepositUnionSettleExecution] UnionSettleExecution PreProcess error=%s", bizErr.Error())
		return bizErr
	}
	if e.isTestOrderOrEnv() && e.UnionSettleExecution.SettleAmount > 100 {
		return errdef.NewParamsErr("测试环境分账金额不能超过1元")
	}

	return nil
}

func (e *DstDepositUnionSettleExecution) isTestOrderOrEnv() bool {
	if e.UnionSettleExecution.GetOrder() != nil && e.UnionSettleExecution.GetOrder().FweOrder != nil && e.UnionSettleExecution.GetOrder().FweOrder.IsTest == 1 {
		return true
	}
	return false
}

func (e *DstDepositUnionSettleExecution) Process(ctx context.Context) error {
	if e.depositPayExe != nil {
		if bizErr := e.depositPayExe.Process(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstDepositUnionSettleExecution] depositPayExe Process error=%s", bizErr.Error())
			return bizErr
		}
	}
	if e.smallDepositPayExe != nil {
		if bizErr := e.smallDepositPayExe.Process(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstDepositUnionSettleExecution] smallDepositPayExe Process error=%s", bizErr.Error())
			return bizErr
		}
	}
	// 异步执行
	go func() {
		defer func() {
			tools_recover.CheckRecover(ctx, recover())
		}()
		time.Sleep(30 * time.Second)
		if e.depositPayExe != nil {
			if _, bizErr := service.NewTradePayment().YztOfflineUpsertAndAcceptance(ctx, &payment.YztOfflineUpsertAndAcceptanceRequest{
				PayOrderNo: e.depositPayExe.GetBizRsp().PayData,
			}); bizErr != nil {
				logs.CtxError(ctx, "[DstDepositUnionSettleExecution] depositPayExe YztOfflineUpsertAndAcceptance err=%s", bizErr.Error())

				lark_robot.DefaultDiyLarkAlertBot(ctx).
					Level(lark_robot.AlertLevel_Error).
					Title("【CPS订单】分账-定金分账失败").
					Content(map[string]string{
						"err":         bizErr.Error(),
						"logId":       utils.GetTraceID(ctx),
						"errorMethod": "YztOfflineUpsertAndAcceptance",
						"msg":         "定金mock成功回调基建失败",
					}).
					NotifyUserMap(map[string]string{
						"111": "姚俊杰",
						"222": "刘玉冲",
					}).
					SendCardMsg()

				return
			}

			if bizErr := e.SuccessFinanceOrder(ctx, e.bizReq.DepositUnionPayReq.FinanceOrderType); bizErr != nil {
				logs.CtxError(ctx, "[DstDepositUnionSettleExecution] depositPayExe SuccessFinanceOrder err=%s", bizErr.Error())
				return
			}
		}
		if e.smallDepositPayExe != nil {
			if _, bizErr := service.NewTradePayment().YztOfflineUpsertAndAcceptance(ctx, &payment.YztOfflineUpsertAndAcceptanceRequest{
				PayOrderNo: e.smallDepositPayExe.GetBizRsp().PayData,
			}); bizErr != nil {
				logs.CtxError(ctx, "[DstDepositUnionSettleExecution] smallDepositPayExe YztOfflineUpsertAndAcceptance err=%s", bizErr.Error())

				lark_robot.DefaultDiyLarkAlertBot(ctx).
					Level(lark_robot.AlertLevel_Error).
					Title("【CPS订单】分账-定金分账失败").
					Content(map[string]string{
						"err":         bizErr.Error(),
						"logId":       utils.GetTraceID(ctx),
						"errorMethod": "YztOfflineUpsertAndAcceptance",
						"msg":         "订金mock成功回调基建失败",
					}).
					NotifyUserMap(map[string]string{
						"111": "姚俊杰",
						"222": "刘玉冲",
					}).
					SendCardMsg()
				return
			}

			if bizErr := e.SuccessFinanceOrder(ctx, e.bizReq.SmallDepositUnionPayReq.FinanceOrderType); bizErr != nil {
				logs.CtxError(ctx, "[DstDepositUnionSettleExecution] smallDepositPayExe SuccessFinanceOrder err=%s", bizErr.Error())
				return
			}
		}
		if bizErr := e.UnionSettleExecution.Process(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstDepositUnionSettleExecution] UnionSettleExecution Process error=%s", bizErr.Error())

			lark_robot.DefaultDiyLarkAlertBot(ctx).
				Level(lark_robot.AlertLevel_Error).
				Title("【CPS订单】分账-定金分账失败").
				Content(map[string]string{
					"err":         bizErr.Error(),
					"logId":       utils.GetTraceID(ctx),
					"errorMethod": "UnionSettleExecution.Process",
					"msg":         "分账逻辑处理失败.",
				}).
				NotifyUserMap(map[string]string{
					"111": "姚俊杰",
					"222": "刘玉冲",
				}).
				SendCardMsg()
			return
		}
	}()
	return nil
}

func (e *DstDepositUnionSettleExecution) PostProcess(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		fweOrder     = e.UnionSettleExecution.GetOrder().FweOrder
		orderID      = fweOrder.OrderID
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.UnionSettleExecution.GetActionOrderReq().GetOperator(),
		}
		stateMachine = e.UnionSettleExecution.GetStateMachine()
	)

	if e.depositPayExe != nil {
		stateMachine = e.depositPayExe.GetStateMachine()
		logs.CtxInfo(ctx, "[DstDepositUnionSettleExecution] stateMachine use depositPayExe")
	} else if e.smallDepositPayExe != nil {
		stateMachine = e.smallDepositPayExe.GetStateMachine()
		logs.CtxInfo(ctx, "[DstDepositUnionSettleExecution] stateMachine use smallDepositPayExe")
	}
	// 更新order
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	bizErr = service.NewOrderService().UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[DstDepositUnionSettleExecution] UpdateOrder err=%s", bizErr.Error())
		return bizErr
	}
	if e.depositPayExe != nil {
		return e.depositPayExe.ActionBaseExecution.PostProcess(ctx)
	} else if e.smallDepositPayExe != nil {
		return e.smallDepositPayExe.ActionBaseExecution.PostProcess(ctx)
	}
	return nil
}

func (e *DstDepositUnionSettleExecution) SuccessFinanceOrder(ctx context.Context, financeOrderType int32) error {
	financeOrder := packer.FinanceGetByType(e.GetOrder().FinanceList, financeOrderType)
	if financeOrder == nil {
		logs.CtxError(ctx, "[DstDepositUnionSettleExecution] SuccessFinanceOrder data err")
		return nil
	}
	// 修改资金单状态
	updateStatus := int32(fwe_trade_common.FinanceStatus_Complete)
	updateFinanceOrderParams := &service_model.UpdateFinanceParams{
		UpdateFinanceStatus: &updateStatus,
		UpdateProcessAmount: conv.Int64Ptr(financeOrder.Amount),
		UpdateFinishTime:    conv.Int64Ptr(time.Now().Unix()),
	}
	bizErr := service.NewFinanceOrderService().UpdateV2(ctx, financeOrder.FinanceOrderID, updateFinanceOrderParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[DstDepositUnionSettleExecution] update failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
