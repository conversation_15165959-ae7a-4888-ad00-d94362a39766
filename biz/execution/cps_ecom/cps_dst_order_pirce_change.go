package cps_ecom

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/base"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/motor/trade/audit"
	"context"
	"fmt"
	"github.com/bytedance/sonic"
)

type EcomDstOrderLarkAuditApplyExecution struct {
	*executor.ActionBaseExecution
}

func NewEcomDstOrderLarkAuditApplyExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &EcomDstOrderLarkAuditApplyExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, nil)
	return e
}

func (e *EcomDstOrderLarkAuditApplyExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[ConfirmConsumerLoanExecution.PreProcess] base PreProcess error, err = %v", err.Error())
		return err
	}
	return nil
}

func (e *EcomDstOrderLarkAuditApplyExecution) Process(ctx context.Context) error {
	bizErr := e.FireWithCondition(ctx, map[string]interface{}{})
	if bizErr != nil {
		logs.CtxError(ctx, "[CancelOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	req := e.GetActionOrderReq()
	dstOrderLarkAuditApplyBizReq := DstOrderLarkAuditApplyBizReq{}
	err := sonic.UnmarshalString(req.BizRequest, &dstOrderLarkAuditApplyBizReq)
	if err != nil {
		logs.CtxError(ctx, "[ConfirmConsumerLoanExecution.Process] unmarshal bizReq failed, err=%+v", err)
		return err
	}

	auditReq := &audit.ApplyAuditReq{
		Tenant:      int64(audit.AuditServiceTenant_FWE_TRADE),
		CreatorId:   1,
		BizType:     req.Identity.BizScene,
		OuterId:     e.makeAuditOuterID(dstOrderLarkAuditApplyBizReq.AuditKey),
		AuditName:   dstOrderLarkAuditApplyBizReq.AuditName,
		AuditParams: []*audit.AuditParams{},
		Steps: []*audit.ApplyAuditStepInfo{
			{
				StepType:     int32(audit.StepTypeEnum_Or),
				StepName:     dstOrderLarkAuditApplyBizReq.AuditName,
				TargetSystem: audit.StepTargetSystemTypeEnum_Lark,
				LarkOuterAuditInfo: &audit.LarkOuterAuditInfo{
					ApprovalCode:      dstOrderLarkAuditApplyBizReq.ApprovalCode,
					Form:              dstOrderLarkAuditApplyBizReq.Form,
					OpenId:            dstOrderLarkAuditApplyBizReq.OpenID,
					Appid:             dstOrderLarkAuditApplyBizReq.LarkAppID,
					AppSecret:         dstOrderLarkAuditApplyBizReq.LarkAppSecret,
					VerificationToken: dstOrderLarkAuditApplyBizReq.LarkAppVerificationToken,
					EncryptKey:        dstOrderLarkAuditApplyBizReq.LarkEncryptKey,
				},
			},
		},
		BizIndex1: req.OrderID,
		BizIndex2: fmt.Sprintf("%d", req.Identity.TenantType),
		BizIndex3: fmt.Sprintf("%s:default:%s:motor.fwe_trade.engine", dstOrderLarkAuditApplyBizReq.SuccCallbackAction, env.Env()),
		BizIndex4: fmt.Sprintf("%s:default:%s:motor.fwe_trade.engine", dstOrderLarkAuditApplyBizReq.FailCallbackAction, env.Env()),
		BizIndex5: dstOrderLarkAuditApplyBizReq.AuditKey,
		Base:      base.NewBase(),
	}

	logs.CtxInfo(ctx, "[EcomDstOrderLarkAuditApplyExecution] auditReq=%s", utils.MarshalToStr(auditReq))
	auditNum, bizErr := service.NewAuditService().ApplyAuditWithAuditNum(ctx, auditReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[EcomDstOrderLarkAuditApplyExecution.SendAuditLark] apply audit failed, err=%+v", bizErr.Error())
		return bizErr
	}

	auditDetail, err := service.NewAuditService().GetAuditDetail(ctx, int32(audit.AuditServiceTenant_FWE_TRADE), auditNum)
	if err != nil || auditDetail == nil || len(auditDetail.Steps) == 0 || auditDetail.Steps[0] == nil || auditDetail.Steps[0].OuterAuditNo == nil {
		logs.CtxError(ctx, "[EcomDstOrderLarkAuditApplyExecution.SendAuditLark] get audit detail failed, err=%+v, auditDetail:%s", err, utils.MarshalToStr(auditDetail))
		return err
	}
	logs.CtxInfo(ctx, "[EcomDstOrderLarkAuditApplyExecution] auditDetail=%s", utils.MarshalToStr(auditDetail))
	instanceCode := *auditDetail.Steps[0].OuterAuditNo

	stateMachine := e.GetStateMachine()
	updateParams := &service_model.UpdateOrderParams{
		Operator: e.GetActionOrderReq().GetOperator(),
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	bizErr = service.NewOrderService().UpdateOrder(ctx, req.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	tagMap := map[string]string{
		dstOrderLarkAuditApplyBizReq.TagPrefix + "audit_num":     fmt.Sprintf("%d", auditNum),
		dstOrderLarkAuditApplyBizReq.TagPrefix + "instance_code": instanceCode,
	}
	if len(dstOrderLarkAuditApplyBizReq.OrderTag) > 0 {
		for key, value := range dstOrderLarkAuditApplyBizReq.OrderTag {
			tagMap[key] = value
		}
	}
	if len(tagMap) > 0 {
		bizErr = service.NewOrderService().UpdateOrderTag(ctx, req.OrderID, tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	return nil
}

func (e *EcomDstOrderLarkAuditApplyExecution) makeAuditOuterID(auditKey string) string {
	req := e.GetActionOrderReq()
	// 返回order_id$$event_name
	return fmt.Sprintf("%s$$%s", req.OrderID, auditKey)
}

type DstOrderLarkAuditApplyBizReq struct {
	AuditName string
	AuditKey  string
	TagPrefix string

	ApprovalCode             string
	Form                     string
	OpenID                   string
	LarkAppID                string
	LarkAppSecret            string
	LarkAppVerificationToken string
	LarkEncryptKey           string
	SuccCallbackAction       string
	FailCallbackAction       string
	OrderTag                 map[string]string
}
