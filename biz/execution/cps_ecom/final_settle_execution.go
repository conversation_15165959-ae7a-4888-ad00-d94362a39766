package cps_ecom

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_ecom_product_common/lark_robot"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	adUtils "code.byted.org/motor/gopkg/motor_ad/utils"
	"code.byted.org/motor/gopkg/tools/tools_recover"
	"context"
	"encoding/json"
	"time"
)

type DstFinalSettleExecution struct {
	*CpsDstUnionSettleExecution
	finalSettleExe   *common.UnionSettleExecution
	finalUnionPayExe *common.UnionPayExecution
	bizReq           DstFinalSettleReq
	bizRsp           execution_common.SettleRsp
}

type DstFinalSettleReq struct {
	// 尾款支付单
	FinalUnionPayReq *execution_common.UnionPayReq
	// 尾款分账单
	FinalSettleReq *execution_common.SettleReq
	// D+1分账单
	AllSettleReq *execution_common.SettleReq
}

func NewDstFinalSettleExecution(ctx context.Context, sourceActionReq interface{}) executor.IExecution {
	e := &DstFinalSettleExecution{}
	sourceActionOrder := sourceActionReq.(*engine.ActionOrderReq)
	logs.CtxInfo(ctx, "[DstFinalSettleExecution] configService get input=%s", sourceActionOrder.GetBizRequest())
	err := json.Unmarshal([]byte(sourceActionOrder.GetBizRequest()), &e.bizReq)
	if err != nil {
		logs.CtxError(ctx, "[DstFinalSettleExecution] Unmarshal input failed error=%v", err)
		return nil
	}
	// 尾款支付单
	if e.bizReq.FinalUnionPayReq != nil {
		finalPayActionReq := &engine.ActionOrderReq{}
		utils.DeepCopy(sourceActionOrder, finalPayActionReq)
		finalPayActionReq.BizRequest = adUtils.ToJSON(e.bizReq.FinalUnionPayReq)
		e.finalUnionPayExe = common.NewUnionPayExecution(ctx, finalPayActionReq).(*common.UnionPayExecution)
		e.finalUnionPayExe.Init(ctx)
	}
	// 尾款分账单
	if e.bizReq.FinalSettleReq != nil {
		finalSettleActionReq := &engine.ActionOrderReq{}
		utils.DeepCopy(sourceActionOrder, finalSettleActionReq)
		finalSettleActionReq.BizRequest = adUtils.ToJSON(e.bizReq.FinalSettleReq)
		e.finalSettleExe = common.NewUnionSettleExecution(ctx, finalSettleActionReq).(*common.UnionSettleExecution)
		e.finalSettleExe.Init(ctx)
	}
	// D+1分账单
	if e.bizReq.AllSettleReq != nil {
		allSettleActionReq := &engine.ActionOrderReq{}
		utils.DeepCopy(sourceActionOrder, allSettleActionReq)
		allSettleActionReq.BizRequest = adUtils.ToJSON(e.bizReq.AllSettleReq)
		e.CpsDstUnionSettleExecution = NewCpsDstUnionSettleExecution(ctx, allSettleActionReq).(*CpsDstUnionSettleExecution)
	}
	return e
}

func (e *DstFinalSettleExecution) CheckParams(ctx context.Context) error {
	if e.finalUnionPayExe != nil {
		if bizErr := e.finalUnionPayExe.CheckParams(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstFinalSettleExecution] finalUnionPayExe CheckParams error=%s", bizErr.Error())
			return bizErr
		}
	}
	if e.finalSettleExe != nil {
		if bizErr := e.finalSettleExe.CheckParams(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstFinalSettleExecution] finalSettleExe CheckParams error=%s", bizErr.Error())
			return bizErr
		}
	}
	if e.UnionSettleExecution == nil {
		return errdef.NewParamsErr("没有D+1分账单，不能发起D+1分账")
	}
	if bizErr := e.UnionSettleExecution.CheckParams(ctx); bizErr != nil {
		logs.CtxError(ctx, "[DstFinalSettleExecution] UnionSettleExecution CheckParams error=%s", bizErr.Error())
		return bizErr
	}

	// 测试环境金额校验
	if env.IsPPE() {
		if e.bizReq.FinalUnionPayReq != nil && e.bizReq.FinalUnionPayReq.TotalAmount > 100 {
			return errdef.NewParamsErr("测试环境尾款分账金额不能超过1元")
		}
	}

	return nil
}

func (e *DstFinalSettleExecution) PreProcess(ctx context.Context) error {
	if e.finalUnionPayExe != nil {
		if bizErr := e.finalUnionPayExe.PreProcess(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstFinalSettleExecution] finalUnionPayExe PreProcess error=%s", bizErr.Error())
			return bizErr
		}
	}
	if e.finalSettleExe != nil {
		if bizErr := e.finalSettleExe.PreProcess(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstFinalSettleExecution] finalSettleExe PreProcess error=%s", bizErr.Error())
			return bizErr
		}
	}
	if bizErr := e.UnionSettleExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[DstFinalSettleExecution] UnionSettleExecution PreProcess error=%s", bizErr.Error())
		return bizErr
	}

	if env.IsPPE() {
		if e.finalSettleExe != nil && e.finalSettleExe.SettleAmount > 100 {
			return errdef.NewParamsErr("测试环境尾款分账金额不能超过1元")
		}
		if e.UnionSettleExecution != nil && e.UnionSettleExecution.SettleAmount > 100 {
			return errdef.NewParamsErr("测试环境D+1分账金额不能超过1元")
		}
	}
	return nil
}

func (e *DstFinalSettleExecution) Process(ctx context.Context) error {
	// D+1分账单
	if bizErr := e.CpsDstUnionSettleExecution.Process(ctx); bizErr != nil {
		logs.CtxError(ctx, "[DstFinalSettleExecution] UnionSettleExecution Process error=%s", bizErr.Error())
		return bizErr
	}
	if e.finalUnionPayExe != nil {
		if bizErr := e.finalUnionPayExe.Process(ctx); bizErr != nil {
			logs.CtxError(ctx, "[DstFinalSettleExecution] finalUnionPayExe Process error=%s", bizErr.Error())
			return bizErr
		}
		go func() {
			defer func() {
				tools_recover.CheckRecover(ctx, recover())
			}()
			time.Sleep(30 * time.Second)
			if _, bizErr := service.NewTradePayment().YztOfflineUpsertAndAcceptance(ctx, &payment.YztOfflineUpsertAndAcceptanceRequest{
				PayOrderNo: e.finalUnionPayExe.GetBizRsp().PayData,
			}); bizErr != nil {
				logs.CtxError(ctx, "[DstFinalSettleExecution] YztOfflineUpsertAndAcceptance err=%s", bizErr.Error())

				lark_robot.DefaultDiyLarkAlertBot(ctx).
					Level(lark_robot.AlertLevel_Error).
					Title("【CPS订单】分账-尾款分账失败").
					Content(map[string]string{
						"err":         bizErr.Error(),
						"logId":       utils.GetTraceID(ctx),
						"errorMethod": "YztOfflineUpsertAndAcceptance",
					}).
					NotifyUserMap(map[string]string{
						"111": "姚俊杰",
						"222": "刘玉冲",
					}).
					SendCardMsg()
				return
			}

			if bizErr := e.SuccessFinanceOrder(ctx, e.bizReq.FinalUnionPayReq.FinanceOrderType); bizErr != nil {
				logs.CtxError(ctx, "[DstFinalSettleExecution] finalUnionPayExe SuccessFinanceOrder err=%s", bizErr.Error())
				return
			}

			if bizErr := e.finalSettleExe.Process(ctx); bizErr != nil {
				logs.CtxError(ctx, "[DstFinalSettleExecution] finalSettleExe Process error=%s", bizErr.Error())

				lark_robot.DefaultDiyLarkAlertBot(ctx).
					Level(lark_robot.AlertLevel_Error).
					Title("【CPS订单】分账-尾款分账失败").
					Content(map[string]string{
						"err":         bizErr.Error(),
						"logId":       utils.GetTraceID(ctx),
						"errorMethod": "finalSettleExe.Process",
					}).
					NotifyUserMap(map[string]string{
						"111": "姚俊杰",
						"222": "刘玉冲",
					}).
					SendCardMsg()
				return
			}
		}()
	}

	return nil
}

func (e *DstFinalSettleExecution) SuccessFinanceOrder(ctx context.Context, financeOrderType int32) error {
	financeOrder := packer.FinanceGetByType(e.GetOrder().FinanceList, financeOrderType)
	if financeOrder == nil {
		logs.CtxError(ctx, "[DstFinalSettleExecution] SuccessFinanceOrder data err")
		return nil
	}
	// 修改资金单状态
	updateStatus := int32(fwe_trade_common.FinanceStatus_Complete)
	updateFinanceOrderParams := &service_model.UpdateFinanceParams{
		UpdateFinanceStatus: &updateStatus,
		UpdateProcessAmount: conv.Int64Ptr(financeOrder.Amount),
		UpdateFinishTime:    conv.Int64Ptr(time.Now().Unix()),
	}
	bizErr := service.NewFinanceOrderService().UpdateV2(ctx, financeOrder.FinanceOrderID, updateFinanceOrderParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[DstFinalSettleExecution] update failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
