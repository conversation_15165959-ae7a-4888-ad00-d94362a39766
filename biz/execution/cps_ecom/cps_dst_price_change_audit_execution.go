package cps_ecom

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"context"
)

type DstPriceChangeAuditExecution struct {
	*common.UpdateOrderFireExecutionV2
}

func NewDstPriceChangeAuditExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &DstPriceChangeAuditExecution{}
	t.UpdateOrderFireExecutionV2 = common.NewUpdateOrderFireExecutionV2(ctx, actionReq).(*common.UpdateOrderFireExecutionV2)
	return t
}

func (e *DstPriceChangeAuditExecution) Process(ctx context.Context) error {
	err := e.UpdateOrderFireExecutionV2.Process(ctx)
	if err != nil {
		logs.CtxError(ctx, "[DstPriceChangeAuditExecution] err=%s", err.Error())
		return err
	}
	if len(e.GetActionOrderReq().OperateDesc) > 0 {
		return nil
	}
	dataMap, bizErr := service.NewOrderService().MGetOrderByIDs(ctx, []string{e.GetActionOrderReq().OrderID})
	if bizErr != nil {
		logs.CtxError(ctx, "[DstPriceChangeAuditExecution] err=%s", bizErr.Error())
		return bizErr
	}
	logs.CtxInfo(ctx, "[DstPriceChangeAuditExecution] order=%s", utils.MarshalToStr(dataMap))
	order := dataMap[e.GetActionOrderReq().OrderID]
	e.GetActionOrderReq().OperateDesc = order.TagMap[getInstanceCodeTag(ctx, e.GetActionOrderReq().Action)]
	logs.CtxInfo(ctx, "[DstPriceChangeAuditExecution] GetActionOrderReq=%s", utils.MarshalToStr(e.GetActionOrderReq()))
	return nil
}

const DstOrderPlatformLoanTagPrefix = "platform_loan_release_"
const DstOrderRefundTagPrefix = "refund_"

func getInstanceCodeTag(ctx context.Context, action string) string {
	instanceCodeTag := "instance_code"
	if gslice.Contains([]string{"PlatformLoanAuditPass", "PlatformLoanAuditReject"}, action) {
		instanceCodeTag = DstOrderPlatformLoanTagPrefix + instanceCodeTag
	}
	if gslice.Contains([]string{"RefundAuditPass", "RefundAuditReject"}, action) {
		instanceCodeTag = DstOrderRefundTagPrefix + instanceCodeTag
	}
	return instanceCodeTag
}
