package cps_ecom

import (
	"code.byted.org/motor/fwe_trade_common/scene/cps_ecom"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/base"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/tenant_base"
	"code.byted.org/overpass/motor_fwe_trade_product_cps_ecom/kitex_gen/motor/fwe_trade/product_cps_ecom"
	"context"
	"github.com/bytedance/sonic"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
)

type CreateOrderExecution struct {
	*executor.CreateBaseExecution
	result interface{}
}

// NewCreateOrderExecution 创建订单并创建资金单
func NewCreateOrderExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &CreateOrderExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), nil)
	return t
}

func (e *CreateOrderExecution) Process(ctx context.Context) error {
	var (
		err          error
		stateMachine = e.GetStateMachine()
		createReq    = e.GetCreateOrderReq()
		bizScene     = createReq.GetIdentity().GetBizScene()
		productInfo  = createReq.GetProductInfo()
		financeList  []*db_model.FFinanceOrder
	)

	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene,
		SmVersion:          createReq.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createReq.GetOrderName(),
		OrderDesc:          createReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductExtra:       productInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		ProductVersion:     productInfo.ProductVersion,
		TotalAmount:        createReq.TotalAmount,
		TotalPayAmount:     createReq.TotalAmount,
		TotalSubsidyAmount: 0,
		TradeType:          int32(createReq.TradeType),
		BuyerID:            packer.CommonTradeSubjectIDGet(createReq.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(createReq.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createReq.IsTest),
		Creator:            createReq.GetOperator().GetOperatorID(),
		CreatorName:        createReq.GetOperator().GetOperatorName(),
		Operator:           createReq.GetOperator().GetOperatorID(),
		OperatorName:       createReq.GetOperator().GetOperatorName(),
		IdempotentID:       createReq.GetIdemID(),
	}

	// 资金单列表
	for _, financeInfo := range createReq.FinanceList {
		financeOrderID, err := utils.TryGenId(3)
		if err != nil {
			logs.CtxError(ctx, "[createOrderExecution] gen id error, err=%+v", err)
			return err
		}
		financeOrder := &db_model.FFinanceOrder{
			TenantType:       int32(e.GetBizIdentity().TenantType),
			BizScene:         e.GetBizIdentity().BizScene,
			AppID:            "",
			MerchantID:       "",
			Mid:              "",
			OrderID:          e.GetOrderID(),
			OrderName:        fweOrder.OrderName,
			TradeType:        financeInfo.TradeType,
			TradeCategory:    int32(financeInfo.TradeCategory),
			FinanceOrderID:   utils.MakeFinanceOrderID(financeOrderID, financeInfo.FinanceOrderType),
			FinanceOrderType: financeInfo.FinanceOrderType,
			Amount:           financeInfo.Amount,
			ProcessAmount:    0,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
			FeeItemDetail:    conv.StringPtr(tools.GetLogStr(financeInfo.FeeItemList)),
			LoanAmount:       financeInfo.LoanAmount,
		}
		if financeOrder.TradeCategory == 0 {
			financeOrder.TradeCategory = int32(fwe_trade_common.TradeCategory_Pay)
		}
		financeList = append(financeList, financeOrder)
	}

	order := &service_model.Order{
		FweOrder:    fweOrder,
		TagMap:      createReq.OrderTag,
		BizExtra:    createReq.Extra,
		FinanceList: financeList,
	}

	if err = stateMachine.Fire(ctx, cps_ecom.CreateOrderEt.Value(), buildCondition(order)); err != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] Fire err: %s", err)
		return err
	}

	// 更改订单状态
	order.FweOrder.OrderStatus = int32(stateMachine.CurState())

	if bizErr := service.NewOrderService().CreateOrder(ctx, order); bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] CreateOrder err: %+v", bizErr)
		return bizErr
	}

	var pe product_cps_ecom.ProductExtra
	_ = sonic.UnmarshalString(productInfo.GetProductExtra(), &pe)
	if pe.GetIsSpecialPriceCar_4B() {
		// 特价车 扣减库存
		bizErr := e.decrStock(ctx, e.GetOrderID(), conv.Int64Default(e.GetCreateOrderReq().ProductInfo.SkuID, 0))
		if bizErr != nil {
			logs.CtxError(ctx, "[CreateOrderExecution] decrStock err: %+v", bizErr)
			return bizErr
		}
	}

	e.result = e.GetOrderID()
	return nil
}

func (e *CreateOrderExecution) decrStock(ctx context.Context, orderID string, skuID int64) *errdef.BizErr {
	req := &product_stock.DecrStockForOneReq{
		BizOrder: &product_stock.BizOrder{
			BizType: tenant_base.TenantType_Dealer,
			OrderId: orderID,
		},
		Item: &product_stock.ChangeStockItem{
			StockUnit: &product_stock.StockUnit{
				SkuId:     skuID,
				StockType: product_stock.StockType_NORMAL,
			},
			StockNum: conv.Int64Ptr(1),
		},
		BizExtra: nil,
		Base:     base.NewBase(),
	}
	bizErr := service.NewProductStockService().DecrStockOne(ctx, req)
	if bizErr != nil {
		logs.CtxError(ctx, "[decrStock] decr stock failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
