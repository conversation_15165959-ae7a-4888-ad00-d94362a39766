package cps_ecom

import (
	"context"
	"fmt"
	"github.com/tidwall/gjson"
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/byted/kitexutil"
	"code.byted.org/motor/fwe_ecom_product_common/lark_robot"
	"code.byted.org/motor/fwe_trade_common/scene/cps_ecom"
	"code.byted.org/motor/gopkg/tools"
	sdk "code.byted.org/motor/scheduler-go"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"
	overpass_tenant_base "code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/tenant_base"

	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

const (
	delayDuration                   = 24 * time.Hour // 延迟时间
	TaskPrefix                      = "SignContTimeout_"
	IntentionMoneyTimeOutTaskPrefix = "intention_money_time_out_task_"
	maxRetryTimes                   = 3  // 最大重试次数
	maxRetryDuration                = 60 // 最大重试时间间隔
	retryInterval                   = 10 // 重试间隔
	testDelayDuration               = 48 * 30 * time.Minute
)

func buildCondition(order *service_model.Order) map[string]interface{} {
	tagMap := order.TagMap
	var res = map[string]interface{}{
		"create_type": conv.Int64Default(tagMap["create_type"], 0),
	}
	if order.FweOrder.BizScene == cps_ecom.CPSEcomDepositScene.Value() ||
		order.FweOrder.BizScene == cps_ecom.CPSEcomDcdShopScene.Value() {
		res[cps_ecom.DepositAmount] = order.FweOrder.TotalAmount
	}
	if order.FweOrder.BizScene == cps_ecom.CPSEcomDstScene.Value() {
		hasSmallDeposit := false
		for _, financeOrder := range order.FinanceList {
			if financeOrder.FinanceOrderType == 13 && financeOrder.Status != int32(fwe_trade_common.CommonStatus_Closed) && financeOrder.Amount > 0 {
				hasSmallDeposit = true
				break
			}
		}
		res[cps_ecom.HasSmallDeposit.Val()] = hasSmallDeposit // 有意向金(即小订)
	}
	return res
}

func IncrStock(ctx context.Context, orderID string, skuID int64) *errdef.BizErr {
	req := &product_stock.IncrStockForOneReq{
		BizOrder: &product_stock.BizOrder{
			BizType: overpass_tenant_base.TenantType_Dealer,
			OrderId: orderID,
		},
		Item: &product_stock.ChangeStockItem{
			StockUnit: &product_stock.StockUnit{
				SkuId:     skuID,
				StockType: product_stock.StockType_NORMAL,
			},
			StockNum: conv.Int64Ptr(1),
		},
		BizExtra: nil,
	}
	bizErr := service.NewProductStockService().IncrStockOne(ctx, req)
	if bizErr != nil {
		lark_robot.DefaultDiyLarkAlertBot(ctx).
			Level(lark_robot.AlertLevel_Error).
			Title("【CPS订单】关闭订单-库存回补失败").
			Content(map[string]string{
				"err":     bizErr.Error(),
				"orderID": orderID,
				"skuID":   conv.StringDefault(skuID, ""),
				"req":     tools.GetLogStr(req),
				"处理方案":    "手动调用【motor.fwe_ecom.product_stock】的【IncrStockOne】接口, req传参【req】中的内容",
			}).
			NotifyUserMap(map[string]string{
				"111": "姚文振",
				"222": "李沛洋",
				"333": "陈晨",
				"444": "赵腾",
				"555": "高大勇",
			}).
			SendCardMsg()

		logs.CtxError(ctx, "[IncrStock] IncrStockOne err: %+v, orderID: %+v, skuID: %+v", bizErr, orderID, skuID)
		return bizErr
	}
	return nil
}

func CreateAutoCloseTask(ctx context.Context, orderId string, createReq *engine.CreateOrderReq) (*errdef.BizErr, int64) {
	// 任务key
	bizKey := fmt.Sprintf("%s%s", TaskPrefix, orderId)

	firstScheduleTime := gjson.Get(createReq.PurchasePlan.PurchasePlanParam, "product_detail.base_info.end_time").Int()
	if firstScheduleTime == 0 {
		dalyTime := delayDuration
		if env.IsBoe() || env.IsPPE() {
			dalyTime = testDelayDuration
		}

		// 调度时间戳
		firstScheduleTime = time.Now().Add(dalyTime).Unix()
		logs.CtxInfo(ctx, "CreateTask product_detail no end_time, firstScheduleTime %d", firstScheduleTime)
	}
	logs.CtxInfo(ctx, "CreateTask firstScheduleTime=%d", firstScheduleTime)

	// 任务请求体
	timeoutReq := &engine.ActionOrderReq{
		OrderID: orderId,
		Action:  cps_ecom.AutoCloseOrderEt.Value(),
		Operator: &fwe_trade_common.OperatorInfo{
			OperatorName: "系统",
			OperatorID:   "System",
		},
	}

	jobBody := sdk.NewRPCJobBodyWithReqObj(consts.PSM, "ActionOrder", timeoutReq)
	retryStrategy := sdk.NewRetryStrategy(maxRetryTimes, maxRetryDuration, retryInterval, sdk.RetryType_FixedInterval)

	// 回调泳道
	ctx = kitexutil.NewCtxWithEnv(ctx, env.Env())

	// 创建任务
	jobResp, err := caller.Scheduler.CreateDelayJob(ctx, bizKey, firstScheduleTime,
		jobBody, sdk.WithCreateRetryStrategy(retryStrategy))
	if err != nil {
		logs.CtxWarn(ctx, "[CreateTask] create delay job failed, err=%+v", err)
		return errdef.NewBizErr(errdef.SchedulerErr, err, "自动关单调度任务创建失败"), firstScheduleTime
	}

	logs.CtxInfo(ctx, "ContCreateWithTimeoutExecution CreateTask orderId=%s, jobResp=%s", orderId, tools.GetLogStr(jobResp))
	return nil, firstScheduleTime
}

func CreateIntentionMoneyTimeOutAutoCloseTask(ctx context.Context, orderId string, createReq *engine.CreateOrderReq) {
	existIntentionMoney := false
	for _, financeInfo := range createReq.FinanceList {
		if financeInfo.FinanceOrderType == 13 {
			existIntentionMoney = true
			break
		}
	}
	if !existIntentionMoney {
		return
	}

	// 任务key
	bizKey := fmt.Sprintf("%s%s", IntentionMoneyTimeOutTaskPrefix, orderId)
	firstScheduleTime := time.Now().Add(30 * time.Minute).Unix() // 调度时间戳
	logs.CtxInfo(ctx, "[CreateIntentionMoneyTimeOutAutoCloseTask] bizKey:%s, firstScheduleTime:%d", bizKey, firstScheduleTime)

	// 任务请求体
	timeoutReq := &engine.ActionOrderReq{
		OrderID: orderId,
		Action:  cps_ecom.PaySmallDepositTimeOutEt.Value(),
		Operator: &fwe_trade_common.OperatorInfo{
			OperatorName: "系统",
			OperatorID:   "System",
		},
	}

	jobBody := sdk.NewRPCJobBodyWithReqObj(consts.PSM, "ActionOrder", timeoutReq)
	retryStrategy := sdk.NewRetryStrategy(maxRetryTimes, maxRetryDuration, retryInterval, sdk.RetryType_FixedInterval)

	// 回调泳道
	ctx = kitexutil.NewCtxWithEnv(ctx, env.Env())

	// 创建任务
	jobResp, err := caller.Scheduler.CreateDelayJob(ctx, bizKey, firstScheduleTime,
		jobBody, sdk.WithCreateRetryStrategy(retryStrategy))
	if err != nil {
		logs.CtxWarn(ctx, "[CreateTask] create delay job failed, err=%+v", err)
		return
	}
	logs.CtxInfo(ctx, "ContCreateWithTimeoutExecution CreateTask orderId=%s, jobResp=%s", orderId, tools.GetLogStr(jobResp))
	return
}
