package cps_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/overpass/motor_fwe_trade_product_cps_ecom/kitex_gen/motor/fwe_trade/product_cps_ecom"
	"context"
	"github.com/bytedance/sonic"
)

type CPSPayTimeoutExecution struct {
	// 基于支付超时 额外添加还库存操作
	*callback.PayCallbackBaseExecution
}

func NewCPSPayTimeoutExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &CPSPayTimeoutExecution{}
	exe.PayCallbackBaseExecution = callback.NewPayCallbackCommonExecutionBase(ctx, sourceReq)
	return exe
}

func (e *CPSPayTimeoutExecution) PostProcess(ctx context.Context) error {
	// 父类型执行
	if err := e.PayCallbackBaseExecution.PostProcess(ctx); err != nil {
		return err
	}

	fweOrder := e.GetOrder().FweOrder
	orderInfo, _ := packer.OrderService2Common(e.GetOrder())
	var pe product_cps_ecom.ProductExtra
	_ = sonic.UnmarshalString(orderInfo.ProductInfo.GetProductExtra(), &pe)
	if pe.GetIsSpecialPriceCar_4B() {
		// 内部发送报警卡片
		_ = IncrStock(ctx, fweOrder.OrderID, conv.Int64Default(fweOrder.SkuID, 0))
	}

	return nil
}
