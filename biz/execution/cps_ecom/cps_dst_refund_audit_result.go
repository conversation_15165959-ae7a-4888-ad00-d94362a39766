package cps_ecom

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	common2 "code.byted.org/motor/pack_airpass/kitex_gen/normal/motor/service_rpc_idl/motor/fwe/trade/common"
	trade_order "code.byted.org/motor/pack_airpass/kitex_gen/normal/motor/service_rpc_idl/motor/fwe/trade/order"
	"code.byted.org/motor/pack_airpass/rpc/motor_fwe_trade_order"
	"context"
	"errors"
)

type DstRefundAuditExecution struct {
	*common.UpdateOrderStaticExecutionV2
}

func NewDstRefundAuditExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &DstRefundAuditExecution{}
	t.UpdateOrderStaticExecutionV2 = common.NewUpdateOrderStaticExecutionV2(ctx, actionReq).(*common.UpdateOrderStaticExecutionV2)
	return t
}

func (e *DstRefundAuditExecution) Process(ctx context.Context) error {
	err := e.UpdateOrderStaticExecutionV2.Process(ctx)
	if err != nil {
		logs.CtxError(ctx, "[DstRefundAuditExecution] err=%s", err.Error())
		return err
	}
	if len(e.GetActionOrderReq().OperateDesc) > 0 {
		return nil
	}
	afterSaleOrder, err := QueryAfterSaleOrderByID(ctx, QueryAfterSaleIDByOrderID(ctx, e.GetActionOrderReq().OrderID))
	if err != nil {
		logs.CtxError(ctx, "QueryAfterSaleOrderByID, err=%v", err)
		return err
	}

	e.GetActionOrderReq().OperateDesc = afterSaleOrder.OrderTag[getInstanceCodeTag(ctx, e.GetActionOrderReq().Action)]
	logs.CtxInfo(ctx, "[DstRefundAuditExecution] GetActionOrderReq=%s", utils.MarshalToStr(e.GetActionOrderReq()))
	return nil
}

func QueryAfterSaleOrderByID(ctx context.Context, afterSaleID string) (*common2.AfterSaleOrderInfo, error) {
	if len(afterSaleID) == 0 {
		return nil, errors.New("after_sale_id is empty")
	}
	afterSaleOrderResp, err := motor_fwe_trade_order.RawCall.MGetAfterSaleOrderInfo(ctx, &trade_order.MGetAfterSaleOrderInfoReq{
		AfterSaleIds: []string{afterSaleID},
		ReadStrategy: trade_order.ReadStrategy_ReadMaster,
	})
	if err != nil {
		logs.CtxError(ctx, "[ExecAction] MGetAfterSaleOrderInfo err: %+v", err)
		return nil, err
	}
	logs.CtxInfo(ctx, "[ExecAction] MGetAfterSaleOrderInfo: %s", utils.MarshalToStr(afterSaleOrderResp))
	if afterSaleOrderResp == nil || afterSaleOrderResp.Data == nil || afterSaleOrderResp.Data[afterSaleID] == nil {
		logs.CtxError(ctx, "[ExecAction] GetOrderInfo is nil. afterSaleID: %s", afterSaleID)
		return nil, errors.New("order not found")
	}
	return afterSaleOrderResp.Data[afterSaleID], nil
}

func QueryAfterSaleIDByOrderID(ctx context.Context, orderID string) string {
	resp, err := QueryAfterSaleOrderByOrderID(ctx, orderID)
	if err != nil {
		logs.CtxError(ctx, "[ExecAction] QueryAfterSaleOrderByOrderID err: %+v", err)
		return ""
	}
	return resp.AfterSaleId
}

// QueryAfterSaleOrderByOrderID 取订单的最后一个售后单
func QueryAfterSaleOrderByOrderID(ctx context.Context, orderID string) (*trade_order.AfterSaleOrderSimpleInfo, error) {
	if len(orderID) == 0 {
		return nil, errors.New("orderID is empty")
	}
	afterSaleOrderResp, err := motor_fwe_trade_order.RawCall.QueryAfterSaleOrder(ctx, &trade_order.QueryAfterSaleOrderReq{
		OrderIds:     []string{orderID},
		ReadStrategy: trade_order.ReadStrategy_ReadMaster,
	})
	if err != nil {
		logs.CtxError(ctx, "[ExecAction] QueryAfterSaleOrder err: %+v", err)
		return nil, err
	}
	logs.CtxInfo(ctx, "[ExecAction] QueryAfterSaleOrder: %s", utils.MarshalToStr(afterSaleOrderResp))
	if afterSaleOrderResp == nil || afterSaleOrderResp.Data == nil || len(afterSaleOrderResp.Data) == 0 {
		logs.CtxError(ctx, "[ExecAction] QueryAfterSaleOrder is nil. orderID: %s", orderID)
		return nil, errors.New("order not found")
	}
	return afterSaleOrderResp.Data[len(afterSaleOrderResp.Data)-1], nil
}
