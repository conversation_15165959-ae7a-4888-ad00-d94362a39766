package cps_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/overpass/motor_fwe_trade_product_cps_ecom/kitex_gen/motor/fwe_trade/product_cps_ecom"
	"context"
	"github.com/bytedance/sonic"
)

type CPSCommonUpdateExecution struct {
	*common.UpdateOrderFireExecutionV2
}

func NewCpsCommonUpdateExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &CPSCommonUpdateExecution{}
	exe.UpdateOrderFireExecutionV2 = common.NewUpdateOrderFireExecutionV2Base(ctx, sourceReq)
	return exe
}

func (e *CPSCommonUpdateExecution) PostProcess(ctx context.Context) error {
	// 父类型执行
	if err := e.UpdateOrderFireExecutionV2.PostProcess(ctx); err != nil {
		return err
	}

	fweOrder := e.GetOrder().FweOrder
	orderInfo, _ := packer.OrderService2Common(e.GetOrder())
	var pe product_cps_ecom.ProductExtra
	_ = sonic.UnmarshalString(orderInfo.ProductInfo.GetProductExtra(), &pe)
	// 特价车还库存
	if pe.GetIsSpecialPriceCar_4B() {
		// 内部发送报警卡片
		_ = IncrStock(ctx, fweOrder.OrderID, conv.Int64Default(fweOrder.SkuID, 0))
	}

	return nil
}
