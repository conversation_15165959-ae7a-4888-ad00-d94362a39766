package cps_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/cps_ecom"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"fmt"
)

type EcomDstAgreementPayExecution struct {
	*executor.ActionBaseExecution
	conf   *sh_sell_model.Conf
	bizReq DstOrderLoanAmountConfirmBizReq
}

type DstOrderLoanAmountConfirmBizReq struct {
	LoanAmount int64
}

func NewEcomDstAgreementPayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &EcomDstAgreementPayExecution{
		conf: new(sh_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), e.conf, &e.bizReq)
	return e
}

func (e *EcomDstAgreementPayExecution) CheckParams(ctx context.Context) error {
	if e.conf == nil || e.conf.AgreementMerchant == nil {
		logs.CtxWarn(ctx, "[EcomDstAgreementPayExecution.CheckParams] conf is empty")
		return errdef.NewRawErr(errdef.LackConfigErr, "缺少协议扣款配置")
	}
	if e.isTestOrderOrEnv() && e.bizReq.LoanAmount > 100 {
		return errdef.NewParamsErr("测试环境放款金额不能超过1元")
	}
	return nil
}

func (e *EcomDstAgreementPayExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[EcomDstAgreementPayExecution.PreProcess] base PreProcess error, err = %v", err.Error())
		return err
	}

	return nil
}

func (e *EcomDstAgreementPayExecution) Process(ctx context.Context) error {
	bizErr := e.FireWithCondition(ctx, map[string]interface{}{})
	if bizErr != nil {
		logs.CtxError(ctx, "[EcomDstAgreementPayExecution] err=%s", bizErr.Error())
		return bizErr
	}
	var (
		req           = e.GetActionOrderReq()
		agreeMerchant = e.conf.AgreementMerchant
	)

	// 发起协议代扣
	financeReq := &payment.AgreementPayReq{
		Identity: &fwe_trade_common.BizIdentity{
			TenantType: req.Identity.TenantType,
			BizScene:   req.Identity.BizScene,
			SmVersion:  req.Identity.SmVersion,
		},
		OrderID:          req.OrderID,
		MerchantID:       agreeMerchant.MerchantID,
		MerchantName:     agreeMerchant.MerchantName,
		AppID:            agreeMerchant.AppID,
		AppName:          agreeMerchant.AppName,
		UID:              agreeMerchant.Uid,
		UIDType:          agreeMerchant.UidType,
		AgreeOrderNo:     req.OrderID,
		AgreeOrderName:   fmt.Sprintf("订单-%s-协议扣款", req.OrderID),
		AgreeOrderDesc:   fmt.Sprintf("订单-%s-协议扣款", req.OrderID),
		Amount:           e.bizReq.LoanAmount,
		CardNo:           agreeMerchant.CardNo,
		InnerAgreementNo: agreeMerchant.InnerAgreementNo,
		AccountProp:      agreeMerchant.AccountProp,
		IPAddress:        "*************",
		CallbackEvent:    utils.MakeCallbackEvent(cps_ecom.PlatformLoanAmountConfirmFinishEt.Value()),
		Base:             base.NewBase(),
	}

	bizErr = service.NewTradePayment().AgreementPay(ctx, financeReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[EcomDstAgreementPayExecution.Process] agreement pay failed, err=%s", bizErr.Error())
		return bizErr
	}

	stateMachine := e.GetStateMachine()
	updateParams := &service_model.UpdateOrderParams{
		Operator: e.GetActionOrderReq().GetOperator(),
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	bizErr = service.NewOrderService().UpdateOrder(ctx, req.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[EcomDstAgreementPayExecution] err=%s", bizErr.Error())
		return bizErr
	}
	_, bizErr = service.NewTagService().UpdateTag(ctx, req.OrderID, req.Identity.BizScene, map[string]string{
		"agreement_merchant_inner_agreement_no": agreeMerchant.InnerAgreementNo,
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[EcomDstAgreementPayExecution] UpdateTag err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *EcomDstAgreementPayExecution) isTestOrderOrEnv() bool {
	if e.GetOrder() != nil && e.GetOrder().FweOrder != nil && e.GetOrder().FweOrder.IsTest == 1 {
		return true
	}
	return false
}
