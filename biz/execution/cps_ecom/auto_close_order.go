package cps_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/motor/fwe_trade_common/scene/cps_ecom"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

var autoCloseStatus = []int32{int32(cps_ecom.ToSignIntentionContractSt.Value()), int32(cps_ecom.SignIntentionContractProcessSt.Value())}

type AutoCloseOrderExecution struct {
	*executor.ActionBaseExecution
}

func NewAutoCloseOrderExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &AutoCloseOrderExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, nil)
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return e
}

func (e *AutoCloseOrderExecution) Process(ctx context.Context) error {
	if !slices.ContainsInt32(autoCloseStatus, e.GetOrder().FweOrder.OrderStatus) {
		return nil // 订单已流转 不做处理
	}
	// 不阻塞流程 内部告警
	_ = IncrStock(ctx, e.GetOrder().FweOrder.OrderID, conv.Int64Default(e.GetOrder().FweOrder.SkuID, 0))
	e.GetActionOrderReq().OperateDesc = "未在有效期内签署合同，订单关闭"
	return nil
}
