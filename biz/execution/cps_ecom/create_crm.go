package cps_ecom

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/motor/fwe_ecom_product_common/util/util_location"
	"context"
	"github.com/bytedance/sonic"
	"strconv"
	"strings"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/gopkg/tools/ptr"
	"code.byted.org/overpass/motor_crm_wb_core/kitex_gen/crm_base"
	"code.byted.org/overpass/motor_crm_wb_core/kitex_gen/crm_biz_extra"
	"code.byted.org/overpass/motor_crm_wb_core/kitex_gen/crm_leads"
	"code.byted.org/overpass/motor_fwe_trade_product_cps_ecom/kitex_gen/motor/fwe_trade/product_cps_ecom"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type CreateCrmExecution struct {
	*executor.StaticBaseExecution
}

func NewCreateCrmExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &CreateCrmExecution{}
	e.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, nil)
	return e
}

func (e *CreateCrmExecution) Process(ctx context.Context) error {
	buyerInfo, bizErr := packer.CommonTradeSubjectDeserialize(e.GetOrder().FweOrder.BuyerID, *e.GetOrder().FweOrder.BuyerExtra)
	if bizErr != nil {
		return bizErr
	}

	if buyerInfo.PersonInfo.CrmID != nil && *buyerInfo.PersonInfo.CrmID != 0 {
		return nil
	}

	sourceInfo := product_cps_ecom.OrderSourceInfo{}
	sourceInfoStr := e.GetOrder().TagMap["order_source_info"]
	if sourceInfoStr != "" {
		err := utils.Unmarshal(sourceInfoStr, &sourceInfo)
		if err != nil {
			return err
		}
	}

	productExtra := product_cps_ecom.ProductExtra{}
	if e.GetOrder().FweOrder.ProductExtra != nil {
		err := utils.Unmarshal(*e.GetOrder().FweOrder.ProductExtra, &productExtra)
		if err != nil {
			return err
		}
	}
	accountId := ""
	if productExtra.ChannelProductRel != nil && productExtra.ChannelProductRel.ChannelDef != nil {
		accountId = productExtra.ChannelProductRel.ChannelDef.ChannelId
	}
	if len(e.GetOrder().TagMap["seller_fw_account_id"]) > 0 {
		accountId = e.GetOrder().TagMap["seller_fw_account_id"]
	}
	if (env.IsPPE() || e.GetOrder().FweOrder.IsTest == 1) && accountId == "0057_7444817064683900990" {
		logs.CtxInfo(ctx, "[DstCreateCrm] is ppe or is test, accountId: %s", accountId)
		accountId = "0057_7447413695740645438" // 测试店铺
	}

	var cityId int32 = 0
	if len(productExtra.CityName) > 0 {
		cityId = util_location.GetCityIDByCityName(productExtra.CityName)
	}
	logs.CtxInfo(ctx, "[CreateCrmExecution] Updated cityId: %d", cityId)

	req := &crm_leads.SaveLeadsReq{
		Account: &crm_base.AppAccount{
			InstanceId:    "217",
			FwAccountType: 57,
			FwAccountId:   accountId,
		},
		Basic: &crm_leads.CRMLeadsBasic{
			CustomerName:   ptr.Ptr(buyerInfo.PersonInfo.PersonName),
			Phone:          ptr.Ptr(buyerInfo.PersonInfo.PersonPhone),
			SourceCategory: ptr.Ptr(int32(7)), // 固定值： 交易订单
			SourceFrom:     ptr.Ptr(crm_leads.SourceFrom_SourceFromDcd),
			Zt:             ptr.Ptr(sourceInfo.Zt),
			CityId:         choose.If(cityId == 0, nil, &cityId),
			CarId:          ptr.Ptr(productExtra.CarId),
			SeriesId:       ptr.Ptr(productExtra.SeriesId),
			BizExtra: &crm_biz_extra.CRMBizExtra{
				StrVal: map[string]string{"order_id": e.GetOrder().FweOrder.OrderID},
			},
		},
		IdemKey: ptr.Ptr(e.GetOrder().FweOrder.OrderID + "_" + buyerInfo.PersonInfo.PersonPhone),
	}
	if buyerInfo.PersonInfo.SaleID != nil && *buyerInfo.PersonInfo.SaleID != "" {
		followMemberKey := *buyerInfo.PersonInfo.SaleID + "-" + accountId + "-57"
		req.Ticket = &crm_base.CRMFollowupTicket{
			FollowMemberKey: ptr.Ptr(followMemberKey),
		}
		logs.CtxInfo(ctx, "[DstCreateCrm] SaveLeadsReq, saleId: %s, followMemberKey: %s", *buyerInfo.PersonInfo.SaleID, followMemberKey)
	}
	if buyerInfo.PersonInfo.UID != nil {
		dcdUids, err := sonic.MarshalString([]string{strconv.FormatInt(*buyerInfo.PersonInfo.UID, 10)})
		if err != nil {
			return err
		}
		dcdDids, err := sonic.MarshalString([]string{conv.Int64ToStr(sourceInfo.DeviceId)})
		if err != nil {
			return err
		}
		req.Basic.BizExtra.StrVal["dcd_uids"] = dcdUids
		req.Basic.BizExtra.StrVal["dcd_dids"] = dcdDids
	}
	leads, err := service.NewCrmWbCore().SaveLeads(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[DstCreateCrm] accountId is empty")
		LarkRobotAlarm(ctx, "【电商通订单】创建CRM失败", gmap.Keys(LarkNameOpenIDMap), map[string]string{
			"订单ID": e.GetOrder().FweOrder.OrderID,
			"err":  err.String(),
		})
		return err
	}
	buyerInfo.PersonInfo.CrmID = ptr.Ptr(leads.CustomerId)
	var saleAccountId string
	if leads.Ticket != nil && leads.Ticket.FollowMemberKey != nil {
		split := strings.Split(*leads.Ticket.FollowMemberKey, "-")
		if split[0] != "" {
			buyerInfo.PersonInfo.SaleID = ptr.Ptr(split[0])
		}
		if len(split) > 1 && split[1] != "" {
			saleAccountId = split[1]
		}
	}

	bizErr = service.NewOrderService().UpdateOrder(ctx, e.GetOrder().FweOrder.OrderID, &service_model.UpdateOrderParams{
		UpdateBuyerExtra: ptr.Ptr(packer.CommonTradeSubjectSerialize(buyerInfo)),
	})

	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	if len(saleAccountId) == 0 {
		return nil
	}
	logs.CtxInfo(ctx, "[CreateCrmExecution] UpdateTag saleAccountId: %s", saleAccountId)
	_, bizErr = service.NewTagService().UpdateTag(ctx, e.GetOrder().FweOrder.OrderID, e.GetBizIdentity().GetBizScene(), map[string]string{
		"sale_shop_id": saleAccountId,
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] UpdateTag err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
