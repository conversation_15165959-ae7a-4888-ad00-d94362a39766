package cps_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"context"
)

type RefundWithStockExecution struct {
	*common.UnionRefundExecution
}

func NewRefundWithStockExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &RefundWithStockExecution{}
	exe.UnionRefundExecution = common.NewUnionRefundBaseExecution(ctx, sourceReq)
	return exe
}

func (e *RefundWithStockExecution) PostProcess(ctx context.Context) error {
	// 父类型执行
	if err := e.UnionRefundExecution.PostProcess(ctx); err != nil {
		return err
	}

	fweOrder := e.GetOrder().FweOrder
	// 内部发送报警卡片
	_ = IncrStock(ctx, fweOrder.OrderID, conv.Int64Default(fweOrder.SkuID, 0))
	return nil
}
