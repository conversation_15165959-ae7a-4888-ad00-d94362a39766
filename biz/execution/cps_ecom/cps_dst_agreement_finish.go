package cps_ecom

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/cps_ecom"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/gopkg/tools"
	"context"
)

type EcomDstAgreementFinishExecution struct {
	*executor.ActionBaseExecution
}

func NewEcomDstAgreementFinishExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &EcomDstAgreementFinishExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, nil)
	return e
}

func (e *EcomDstAgreementFinishExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[EcomDstAgreementFinishExecution.PreProcess] base PreProcess error, err = %v", err.Error())
		return err
	}

	return nil
}

func (e *EcomDstAgreementFinishExecution) Process(ctx context.Context) error {
	condition := map[string]interface{}{
		cps_ecom.HasCarForm.Val(): e.GetOrder().TagMap != nil && len(e.GetOrder().TagMap["seller_confirm_delivered_car_delivery_form_list"]) > 0,
	}
	bizErr := e.FireWithCondition(ctx, condition)
	if bizErr != nil {
		logs.CtxError(ctx, "[EcomDstAgreementFinishExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams := &service_model.UpdateOrderParams{
		Operator: e.GetActionOrderReq().GetOperator(),
	}
	stateMachine := e.GetStateMachine()
	req := e.GetActionOrderReq()

	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	bizErr = service.NewOrderService().UpdateOrder(ctx, req.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[EcomDstAgreementFinishExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
