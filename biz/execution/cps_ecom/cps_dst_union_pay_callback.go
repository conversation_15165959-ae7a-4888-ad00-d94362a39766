package cps_ecom

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/cps_ecom"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/gopkg/motor_ad/utils"
	"context"
)

type CpsEcomDstUnionPayCallbackExecution struct {
	*callback.UnionPayCallbackBaseExecution
}

func NewCpsEcomDstUnionPayCallbackExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &CpsEcomDstUnionPayCallbackExecution{}
	exe.UnionPayCallbackBaseExecution = callback.NewUnionPayCallbackBaseExecutionWithOpt(ctx, sourceReq, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: exe.buildCondition,
	})
	return exe
}

func (e *CpsEcomDstUnionPayCallbackExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[CpsEcomDstUnionPayCallbackExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}
	logs.CtxInfo(ctx, "[CpsEcomDstUnionPayCallbackExecution-PreProcess] PreProcess done")
	return nil
}

func (e *CpsEcomDstUnionPayCallbackExecution) buildCondition(ctx context.Context) (condition map[string]interface{}, bizErr *errdef.BizErr) {
	if e.GetOrder() == nil || e.GetOrder().TagMap == nil {
		logs.CtxError(ctx, "[CpsEcomDstUnionPayCallbackExecution] buildCondition err, order or tagMap is nil, order=%s", utils.ToJSON(e.GetOrder()))
		return
	}
	confirmStatus := true
	if e.GetOrder() != nil && e.GetOrder().TagMap != nil && len(e.GetOrder().TagMap["delivery_store_confirm_status"]) > 0 && e.GetOrder().TagMap["delivery_store_confirm_status"] == "0" {
		confirmStatus = false
	}
	condition = map[string]interface{}{
		cps_ecom.CondDeliveryStoreConfirmed.Val(): confirmStatus,
	}
	logs.CtxInfo(ctx, "[CpsEcomDstUnionPayCallbackExecution] buildCondition done, condition = %v", utils.ToJSON(condition))
	return
}
