package cps_ecom

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"context"
)

type CancelRefundOverExecution struct {
	*callback.UnionRefundCallbackExecution
}

func NewCancelRefundOverExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &CancelRefundOverExecution{}
	exe.UnionRefundCallbackExecution = callback.NewUnionRefundCallbackBaseExecution(ctx, sourceReq, nil)
	return exe
}

func (e *CancelRefundOverExecution) PostProcess(ctx context.Context) error {
	logs.CtxInfo(ctx, "[CancelRefundOverExecution] PostProcess")
	fweOrder := e.GetOrder().FweOrder
	tagMap := e.GetActionOrderReq().GetTagMap()
	if tagMap == nil {
		tagMap = make(map[string]string)
	}
	tagMap["cancel_status"] = "4" // 退款成功
	// 更新orderTag
	_, bizErr := service.NewTagService().UpdateTag(ctx, fweOrder.OrderID, fweOrder.BizScene, tagMap)
	if bizErr != nil {
		return bizErr
	}
	if err := e.UnionRefundCallbackExecution.PostProcess(ctx); err != nil {
		return err
	}
	return nil
}
