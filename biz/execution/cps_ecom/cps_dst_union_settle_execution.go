package cps_ecom

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metrics"
	"code.byted.org/lang/gg/gmap"
	sdkUtils "code.byted.org/motor/fwe_trade_common/statemachine/utils"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"context"
	"fmt"
)

type CpsDstUnionSettleExecution struct {
	*common.UnionSettleExecution
}

func NewCpsDstUnionSettleExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &CpsDstUnionSettleExecution{}
	e.UnionSettleExecution = common.NewUnionSettleExecution(ctx, actionReq).(*common.UnionSettleExecution)
	return e
}

func (e *CpsDstUnionSettleExecution) Process(ctx context.Context) error {
	logs.CtxInfo(ctx, "[CpsDstUnionSettleExecution] Process")
	var (
		bizErr         *errdef.BizErr
		settleAmount   = e.SettleAmount
		actionOrderReq = e.GetActionOrderReq()
	)
	// 驱动
	conditions := sdkUtils.BuildSettleCondition(settleAmount)
	if actionOrderReq.ActionCondition != "" {
		conditions, _ = sdkUtils.AppendBizReqParams(ctx, conditions, actionOrderReq.ActionCondition)
	}
	bizErr = e.FireWithCondition(ctx, conditions)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	// 金额为0，结束
	if settleAmount == int64(0) {
		logs.CtxInfo(ctx, "[UnionSettleExecution] settleAmount is 0")
		tags := []metrics.T{
			{Name: consts.TagBizScene, Value: fmt.Sprintf("%d", e.GetBizIdentity().BizScene)},
			{Name: consts.TagStatus, Value: fmt.Sprintf("%d", e.GetStateMachine().CurState())},
		}
		utils.EmitCounter(consts.MetricSettleAmountIsZero, 1, tags...)
		return nil
	}
	// 分账,默认分账需要消费贷金额
	settleParam, bizErr := e.BuildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	mergeSettleNo, bizErr := service.NewUnionSettleService().UnionSettle(ctx, settleParam)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[UnionSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.BizRsp = execution_common.SettleRsp{
		MergeSettleNo: mergeSettleNo,
	}
	return nil
}

func (e *CpsDstUnionSettleExecution) BuildReq(ctx context.Context) (*service_model.UnionSettleParam, *errdef.BizErr) {
	logs.CtxInfo(ctx, "[CpsDstUnionSettleExecution] BuildReq")
	param, err := e.UnionSettleExecution.BuildReq(ctx)
	order := e.GetOrder()
	if param != nil && param.MergeSettleReq != nil && param.MergeSettleReq.SubsidyList != nil &&
		order.FweOrder != nil && order.FweOrder.BizScene == 81100 && order.TagMap != nil &&
		order.TagMap["agreement_merchant_inner_agreement_no"] != "202507302000402908291849" {
		// 兼容一下电商通订单平台贷款老账户
		for _, subsidy := range param.MergeSettleReq.SubsidyList {
			if subsidy.OutUID == "20221200008055" {
				continue
			}
			subsidy.OutUID = "20221200008055"
			logs.CtxInfo(ctx, "[UnionSettleExecution] Dst Order subsidy.OutUID = 20221200008055")
			LarkRobotNotice(ctx, "【电商通订单】分账兼容电商通订单平台贷款老账户生效", gmap.Keys(LarkNameOpenIDMap), map[string]string{
				"订单ID": e.GetOrder().FweOrder.OrderID,
			})
		}
	}

	return param, err
}
