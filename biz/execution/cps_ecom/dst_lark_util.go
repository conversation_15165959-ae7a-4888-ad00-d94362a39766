package cps_ecom

import (
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/motor/fwe_ecom_lib/ecom_robot/lark_robot"
	"context"
)

func LarkRobotAlarm(ctx context.Context, title string, notifyUser []string, content map[string]string) {
	LarkRobotNotify(ctx, title, notifyUser, content, lark_robot.AlertLevel_Error)
}

func LarkRobotNotice(ctx context.Context, title string, notifyUser []string, content map[string]string, userOpenIdMap ...map[string]string) {
	LarkRobotNotify(ctx, title, notifyUser, content, lark_robot.AlertLevel_Notice, userOpenIdMap...)
}

const (
	OpenID_YaoJunJie = "ou_ba8f28818ecb4703b643f3279fc2dba1"
)

var LarkNameOpenIDMap = map[string]string{
	"姚俊杰": choose.If(env.IsPPE(), "111111", OpenID_YaoJunJie),
}

func LarkRobotNotify(ctx context.Context, title string, notifyUser []string, content map[string]string, alertLevel lark_robot.AlertLevel, userOpenIdMap ...map[string]string) {
	defer func() {
		if r := recover(); r != nil {
			logs.CtxError(ctx, "[larkRobotAlarm] recover err:%v", r)
		}
	}()
	if len(notifyUser) == 0 {
		return
	}
	notifyUserMap := map[string]string{}
	larkNameOpenIDMap := LarkNameOpenIDMap
	if len(userOpenIdMap) > 0 {
		larkNameOpenIDMap = gmap.Merge(larkNameOpenIDMap, userOpenIdMap[0])
	}
	for _, userName := range notifyUser {
		notifyUserMap[LarkNameOpenIDMap[userName]] = userName
	}

	_ = lark_robot.DefaultDiyLarkAlertBot(ctx).Level(int32(alertLevel)).
		LogID(ctxvalues.LogIDDefault(ctx)).
		Title(title).
		Content(content).
		NotifyUserMap(notifyUserMap).
		SendCardMsg()
}
