package sh_auction

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_auction_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"sort"
	"strings"
)

type settleExecution struct {
	*executor.ActionBaseExecution
	bizReq sh_auction_model.SettleReq
	conf   sh_auction_model.ShAuctionConfig
}

func NewSettleExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	e := &settleExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq, opts...)
	return e
}

func (e *settleExecution) Process(ctx context.Context) error {
	var (
		bizErr      *errdef.BizErr
		orderID     = e.GetActionOrderReq().GetOrderID()
		order       = e.GetOrder()
		fweOrder    = e.GetOrder().FweOrder
		splitInfo   = e.bizReq.SplitInfo
		merchantID  = e.GetOrder().FinanceList[0].MerchantID
		shopService = service.NewAccountShop()
	)
	if int32(e.GetBizIdentity().TenantType) != fweOrder.TenantType || e.GetBizIdentity().BizScene != fweOrder.BizScene {
		bizErr = errdef.NewParamsErr("Identity 与db不一致")
		return bizErr
	}
	if splitInfo == nil || len(splitInfo.Detail) == 0 {
		logs.CtxWarn(ctx, "[settleExecution] data error orderID = %+v", orderID)
		return errdef.NewRawErr(errdef.DataErr, "splitInfo error ")
	}
	fweAccountIDs := make([]string, 0)
	for _, info := range splitInfo.Detail {
		// 找四轮id
		if info.SplitUIDType == fwe_trade_common.SplitUIDType_Shop {
			fweAccountIDs = append(fweAccountIDs, info.SplitUID)
		}
	}
	shopAccountMap, _, bizErr := shopService.MGetFinanceAccount(ctx, fweAccountIDs, merchantID)
	if bizErr != nil {
		logs.CtxError(ctx, "[settleExecution] MGetFinanceAccount error ,err = %+v", bizErr)
		return bizErr
	}
	if len(shopAccountMap) != len(fweAccountIDs) {
		logs.CtxError(ctx, "[settleExecution] MGetFinanceAccount error ,fweAccountIDs = %+v", fweAccountIDs)
		return errdef.NewRawErr(errdef.AccountShopErr, "data error")
	}
	// 转化uid
	paymentSplit, bizErr := packer.SplitInfoCommonList2PaymentList(splitInfo, shopAccountMap)
	if bizErr != nil {
		logs.CtxError(ctx, "[settleExecution] splitInfo packer error ,err = %+v", bizErr)
		return bizErr
	}
	//取出订单卖方
	sellerInfo, bizErr := packer.CommonTradeSubjectDeserialize(order.FweOrder.SellerID, *order.FweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxError(ctx, "[settleExecution] sellerInfo packer error ,err = %+v", bizErr)
		return bizErr
	}
	if sellerInfo == nil || sellerInfo.FweMerchant == nil {
		logs.CtxError(ctx, "[settleExecution] sellerInfo error, orderID = %+v", orderID)
		return errdef.NewRawErr(errdef.DataErr, "seller is error")
	}
	financeAccount, bizErr := shopService.GetFinanceAccountOne(ctx, sellerInfo.FweMerchant.FweAccountID, merchantID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[settleExecution] GetFinanceAccountOne error,err = %+v", bizErr)
		return bizErr
	}
	settleV2Req, bizErr := e.buildReq(paymentSplit, financeAccount)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[settleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	_, bizErr = service.NewTradePayment().MergeSettleV2(ctx, settleV2Req)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[settleExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *settleExecution) PostProcess(ctx context.Context) error {
	var (
		orderID = e.GetOrder().FweOrder.OrderID
		bizReq  = e.bizReq
	)

	// 更新订单tag
	if bizErr := service.NewOrderService().UpdateOrderTag(ctx, orderID, bizReq.OrderTag); bizErr != nil {
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *settleExecution) buildReq(splitInfo []*payment.SplitInfo, shopAccount *shop.FinanceAccount) (*payment.MergeSettleV2Req, *errdef.BizErr) {
	var (
		productReq  = e.bizReq
		actionReq   = e.GetActionOrderReq()
		orderID     = actionReq.GetOrderID()
		order       = e.GetOrder()
		financeList []string
	)
	_, financeList = e.groupFinanceOrderIDs(order.FinanceList)

	serviceReq := &payment.MergeSettleV2Req{
		Identity:          e.GetBizIdentity(),
		OrderID:           orderID,
		SettleFinanceType: productReq.SettleType,
		FinanceList:       financeList,
		SettleDesc:        productReq.Reason,
		SplitList:         splitInfo,
		SubsidyList:       nil,
		IPAddress:         conv.StringPtr(productReq.IpAddress),
		BizExtra:          nil,
		IsOmitPosFee:      conv.BoolPtr(false),
		IsAutoWithdraw:    conv.BoolPtr(false),
		CallbackEvent:     utils.MakeCallbackEvent(productReq.CallbackEvent),
		CallbackExtra:     "",
		Base:              base.NewBase(),
	}
	return serviceReq, nil
}

func (e *settleExecution) groupFinanceOrderIDs(financeOrders []*db_model.FFinanceOrder) (string, []string) {
	if len(financeOrders) == 0 {
		return "", nil
	}
	list := make([]string, 0)
	for _, v := range financeOrders {
		if v.Amount <= 0 {
			continue
		}
		list = append(list, v.FinanceOrderID)
	}

	sort.Strings(list)

	return strings.Join(list, "-"), list
}
