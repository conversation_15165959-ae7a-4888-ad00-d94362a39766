package sh_auction

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_auction_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"

	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
)

type depositOrderCreateExecution struct {
	*executor.CreateBaseExecution
	conf   sh_auction_model.ShAuctionConfig
	result string
}

func NewDepositOrderCreateExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &depositOrderCreateExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), &t.conf)
	return t
}

func (e *depositOrderCreateExecution) Process(ctx context.Context) error {
	var (
		err          error
		bizErr       *errdef.BizErr
		createReq    = e.GetCreateOrderReq()
		bizScene     = e.GetBizIdentity().GetBizScene()
		orderService = service.NewOrderService()
		productInfo  = createReq.GetProductInfo()
		stateMachine = e.GetStateMachine()
		tradeType    = CommonConsts.OrderTradeFull.Value()
		operator     = createReq.GetOperator()
	)

	// 驱动状态
	err = stateMachine.Fire(ctx, consts.CreateAction, nil)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "depositOrderCreateExecution")
		logs.CtxError(ctx, "[depositOrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}

	if bizScene != CommonConsts.BizSceneSHAuctionDeposit.Value() {
		return errdef.NewParamsErr("不支持的交易场景")
	}

	if len(createReq.FinanceList) != 1 {
		return errdef.NewParamsErr("资金单不符合需求")
	}

	if e.conf.NormalPayMerchant == nil {
		return errdef.NewParamsErr("缺少财经商户配置")
	}

	totalPayAmount := packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList())
	totalAmount := totalPayAmount
	if createReq.TotalAmount != int64(0) {
		totalAmount = createReq.TotalAmount
	}
	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene,
		SmVersion:          createReq.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createReq.GetOrderName(),
		OrderDesc:          createReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductExtra:       productInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		TotalAmount:        totalAmount,
		TotalPayAmount:     totalPayAmount,
		TotalSubsidyAmount: 0,
		TradeType:          tradeType,
		BuyerID:            packer.CommonTradeSubjectIDGet(createReq.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(createReq.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createReq.IsTest),
		Creator:            operator.GetOperatorID(),
		CreatorName:        operator.GetOperatorName(),
		Operator:           operator.GetOperatorID(),
		OperatorName:       operator.GetOperatorName(),
		IdempotentID:       createReq.GetIdemID(),
	}

	financeList, bizErr := e.buildFinanceList(ctx, fweOrder)
	if bizErr != nil {
		logs.CtxError(ctx, "[depositOrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}

	order := &service_model.Order{
		FweOrder:    fweOrder,
		FinanceList: financeList,
		BizExtra:    createReq.Extra,
		TagMap:      createReq.OrderTag,
	}

	bizErr = orderService.CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[depositOrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.result = e.GetOrderID()
	return nil
}

func (e *depositOrderCreateExecution) Result() interface{} {
	return e.result
}

func (e *depositOrderCreateExecution) buildFinanceList(ctx context.Context, order *db_model.FweOrder) ([]*db_model.FFinanceOrder, *errdef.BizErr) {
	var (
		merchant  = e.conf.NormalPayMerchant
		tradeType = CommonConsts.FinancePayCashier.Value()
		req       = e.GetCreateOrderReq()
	)

	output := make([]*db_model.FFinanceOrder, 0)

	for _, info := range req.FinanceList {
		output = append(output, &db_model.FFinanceOrder{
			TenantType:       order.TenantType,
			BizScene:         order.BizScene,
			AppID:            "",
			MerchantID:       merchant.MerchantID,
			Mid:              "",
			OrderID:          order.OrderID,
			OrderName:        order.OrderName,
			TradeType:        tradeType,
			FinanceOrderID:   utils.MakeFinanceOrderIDTool(order.OrderID, info.FinanceOrderType),
			FinanceOrderType: info.FinanceOrderType,
			Amount:           info.Amount,
			ProcessAmount:    0,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
			FeeItemDetail:    conv.StringPtr(tools.GetLogStr(info.FeeItemList)),
		})
	}
	return output, nil
}
