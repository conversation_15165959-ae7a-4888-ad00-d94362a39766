package sh_auction

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_auction_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"

	sdkConsts "code.byted.org/motor/fwe_trade_common/consts"
)

type OrderCreateExecution struct {
	*executor.CreateBaseExecution
	conf   sh_auction_model.ShAuctionConfig
	result interface{}
}

func NewOrderCreateExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &OrderCreateExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), &t.conf)
	return t
}

func (e *OrderCreateExecution) Process(ctx context.Context) error {
	var (
		err          error
		bizErr       *errdef.BizErr
		createReq    = e.GetCreateOrderReq()
		bizScene     = e.GetBizIdentity().GetBizScene()
		orderService = service.NewOrderService()
		productInfo  = createReq.GetProductInfo()
		stateMachine = e.GetStateMachine()
		operator     = createReq.GetOperator()
	)

	// 驱动状态
	err = stateMachine.Fire(ctx, consts.CreateAction, nil)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "OrderCreateExecution")
		logs.CtxError(ctx, "[OrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}

	bizErr = service.NewSplitInfoService().MCreateSplitInfo(ctx, e.GetOrderID(), createReq.SplitInfo)
	if bizErr != nil {
		logs.CtxError(ctx, "[OrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene,
		SmVersion:          createReq.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createReq.GetOrderName(),
		OrderDesc:          createReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		ProductExtra:       productInfo.ProductExtra,
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		TotalAmount:        packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList()),
		TotalPayAmount:     packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList()),
		TotalSubsidyAmount: 0,
		TradeType:          sdkConsts.OrderTradeFull.Value(),
		BuyerID:            packer.CommonTradeSubjectIDGet(createReq.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(createReq.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createReq.IsTest),
		Creator:            operator.GetOperatorID(),
		CreatorName:        operator.GetOperatorName(),
		Operator:           operator.GetOperatorID(),
		OperatorName:       operator.GetOperatorName(),
		IdempotentID:       createReq.GetIdemID(),
	}
	financeList, bizErr := e.buildFinanceList(ctx, fweOrder)
	if bizErr != nil {
		logs.CtxError(ctx, "[OrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}
	order := &service_model.Order{
		FweOrder:    fweOrder,
		FinanceList: financeList,
		BizExtra:    createReq.Extra,
		TagMap:      createReq.OrderTag,
	}

	bizErr = orderService.CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[OrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.result = e.GetOrderID()
	return nil
}

func (e *OrderCreateExecution) Result() interface{} {
	return e.result
}

func (e *OrderCreateExecution) buildFinanceList(ctx context.Context, order *db_model.FweOrder) ([]*db_model.FFinanceOrder, *errdef.BizErr) {
	var (
		merchant = e.conf.GuaranteePayMerchant
		req      = e.GetCreateOrderReq()
	)

	output := make([]*db_model.FFinanceOrder, 0)

	for _, info := range req.FinanceList {
		output = append(output, &db_model.FFinanceOrder{
			TenantType:       order.TenantType,
			BizScene:         order.BizScene,
			AppID:            "",
			MerchantID:       merchant.MerchantID,
			Mid:              "",
			OrderID:          order.OrderID,
			OrderName:        order.OrderName,
			TradeType:        consts.TradeTypePayGuarantee.String(),
			FinanceOrderID:   utils.MakeFinanceOrderIDTool(order.OrderID, info.FinanceOrderType),
			FinanceOrderType: info.FinanceOrderType,
			Amount:           info.Amount,
			ProcessAmount:    0,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
		})
	}
	return output, nil
}
