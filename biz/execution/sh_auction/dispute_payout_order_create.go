package sh_auction

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type disputePayoutOrderCreateExecution struct {
	*executor.CreateBaseExecution
	result string
}

func NewDisputePayoutOrderCreateExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &disputePayoutOrderCreateExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), nil)

	return t
}

func (e *disputePayoutOrderCreateExecution) Process(ctx context.Context) error {
	var (
		err          error
		bizErr       *errdef.BizErr
		createReq    = e.GetCreateOrderReq()
		bizScene     = e.GetBizIdentity().GetBizScene()
		orderService = service.NewOrderService()
		productInfo  = createReq.GetProductInfo()
		stateMachine = e.GetStateMachine()
		tradeType    = CommonConsts.OrderTradeFull.Value()
		operator     = createReq.GetOperator()
	)

	// 驱动状态
	err = stateMachine.Fire(ctx, consts.CreateAction, nil)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "disputePayoutOrderCreateExecution")
		logs.CtxError(ctx, "[disputePayoutOrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}

	if bizScene != CommonConsts.BizSceneSHAuctionDisputePayout.Value() {
		return errdef.NewParamsErr("不支持的交易场景")
	}

	if len(createReq.FinanceList) != 0 {
		return errdef.NewParamsErr("资金单不符合需求 创建订单不需要资金单信息")
	}

	totalPayAmount := packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList())
	totalAmount := totalPayAmount
	if createReq.TotalAmount != int64(0) {
		totalAmount = createReq.TotalAmount
	}

	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene,
		SmVersion:          createReq.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createReq.GetOrderName(),
		OrderDesc:          createReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		ProductExtra:       productInfo.ProductExtra,
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		TotalAmount:        totalAmount,
		TotalPayAmount:     totalPayAmount,
		TotalSubsidyAmount: 0,
		TradeType:          tradeType,
		BuyerID:            packer.CommonTradeSubjectIDGet(createReq.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(createReq.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createReq.IsTest),
		Creator:            operator.GetOperatorID(),
		CreatorName:        operator.GetOperatorName(),
		Operator:           operator.GetOperatorID(),
		OperatorName:       operator.GetOperatorName(),
		IdempotentID:       createReq.GetIdemID(),
	}

	order := &service_model.Order{
		FweOrder:    fweOrder,
		FinanceList: nil,
		BizExtra:    createReq.Extra,
		TagMap:      createReq.OrderTag,
	}

	bizErr = orderService.CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[disputePayoutOrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.result = e.GetOrderID()
	return nil
}

func (e *disputePayoutOrderCreateExecution) Result() interface{} {
	return e.result
}
