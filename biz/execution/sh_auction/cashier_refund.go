package sh_auction

import (
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_auction_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"context"
	"github.com/bytedance/sonic"
)

type cashRefundExecution struct {
	*executor.ActionBaseExecution
	bizReq sh_auction_model.CashRefundReq
	result interface{}
}

func NewCashRefundExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	e := &cashRefundExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.bizReq, opts...)
	return e
}

func (e *cashRefundExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.bizReq
	if bizReq.RefundList == nil || len(bizReq.RefundList) == 0 {
		bizErr := errdef.NewParamsErr("RefundList 参数错误")
		return bizErr
	}
	for _, refund := range bizReq.RefundList {
		if refund.Amount <= 0 {
			bizErr := errdef.NewParamsErr("RefundList.amount 参数错误")
			return bizErr
		}
	}
	return nil
}

func (e *cashRefundExecution) Process(ctx context.Context) error {

	var (
		cashRefundReq *payment.MergeRefundReq
		mergeRefundNo string
		bizErr        *errdef.BizErr
		err           error
		order         = e.GetOrder()
	)

	// refund req
	cashRefundReq, bizErr = e.buildRefundReq()
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr = service.NewTradePayment().MergeRefund(ctx, cashRefundReq)
	if bizErr != nil {
		return bizErr
	}

	rpcRsp := &action_model.CreateRefundRsp{
		MergeRefundID: mergeRefundNo,
	}
	e.result, err = sonic.MarshalString(rpcRsp)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		return bizErr
	}

	// 2。更新tag
	if e.bizReq.OrderTag == nil || len(e.bizReq.OrderTag) == 0 {
		return nil
	}
	bizErr = service.NewOrderService().UpdateOrderTag(ctx, order.FweOrder.OrderID, e.bizReq.OrderTag)
	if bizErr != nil {
		return bizErr
	}
	return nil
}

func (e *cashRefundExecution) buildRefundReq() (*payment.MergeRefundReq, *errdef.BizErr) {
	var (
		order    = e.GetOrder()
		orderID  = e.GetOrder().FweOrder.OrderID
		fweOrder = order.FweOrder
		bizReq   = e.bizReq
	)

	refundList := make([]*payment.SingleRefund, 0, len(bizReq.RefundList))

	for _, refund := range bizReq.RefundList {
		financeOrder := packer.FinanceGetByType(order.FinanceList, refund.FinanceOrderType)
		if financeOrder == nil {
			return nil, errdef.NewRawErr(errdef.DataErr, "找不到对应的资金单")
		}
		refundList = append(refundList, &payment.SingleRefund{
			FinanceOrderID: financeOrder.FinanceOrderID,
			Amount:         refund.Amount,
		})
	}

	serviceReq := &payment.MergeRefundReq{
		Identity:          e.GetBizIdentity(),
		RefundList:        refundList,
		OrderID:           orderID,
		RefundFinanceType: bizReq.RefundType,
		OrderName:         fweOrder.OrderName,
		Reason:            bizReq.Reason,
		Extra:             nil,
		CallbackEvent:     utils.MakeCallbackEvent(bizReq.CallbackEvent),
		CallbackExtra:     "",
		IPAddress:         bizReq.IpAddress,
	}
	return serviceReq, nil
}
