package sh_auction

import (
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"context"
	"fmt"
)

func CheckOrderTag(ctx context.Context, orderTag, reqTag map[string]string, key string) *errdef.BizErr {
	if reqTag == nil || len(reqTag) == 0 {
		return errdef.NewParamsErr("reqTag 参数错误")
	}
	reqValue := reqTag[key]
	if reqValue == "" {
		msg := fmt.Sprintf("tag中缺少key：%v", key)
		return errdef.NewParamsErr(msg)
	}
	dbValue := orderTag[key]
	if dbValue != "" && dbValue != reqValue {
		msg := fmt.Sprintf("tag中的value与db不一致key：%v，req：%v，db：%v", key, reqValue, dbValue)
		return errdef.NewParamsErr(msg)
	}
	return nil
}
