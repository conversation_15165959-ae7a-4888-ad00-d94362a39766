package sh_auction_v2

import (
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	sdkConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"context"
	"fmt"
)

func CheckOrderTag(ctx context.Context, orderTag, reqTag map[string]string, key string) *errdef.BizErr {
	if reqTag == nil || len(reqTag) == 0 {
		return errdef.NewParamsErr("reqTag 参数错误")
	}
	reqValue := reqTag[key]
	if reqValue == "" {
		msg := fmt.Sprintf("tag中缺少key：%v", key)
		return errdef.NewParamsErr(msg)
	}
	dbValue := orderTag[key]
	if dbValue != "" && dbValue != reqValue {
		msg := fmt.Sprintf("tag中的value与db不一致key：%v，req：%v，db：%v", key, reqValue, dbValue)
		return errdef.NewParamsErr(msg)
	}
	return nil
}

func CheckFinanceOrderProcessAmount(ctx context.Context, orderID string, amount int64, financeOrders []*db_model.FFinanceOrder) *errdef.BizErr {
	// 检查资金单总支付金额，这里的处理逻辑可以切换成咨询计费中心，由计费中心根据规则给出退款结果
	filter := slices.Filter(financeOrders, func(financeOrder *db_model.FFinanceOrder) bool {
		return financeOrder.FinanceOrderType == int32(sdkConsts.FinanceTotal)
	})
	fFinanceOrders, ok := filter.([]*db_model.FFinanceOrder)
	if !ok {
		return errdef.NewRawErr(errdef.ServerException, "interface assert error")
	}
	if len(fFinanceOrders) != 1 {
		logs.CtxWarn(ctx, "this order has dirty financeOrder data, orderID = %v", orderID)
		return errdef.NewRawErr(errdef.DataErr, "this order has dirty financeOrder data")
	}
	if amount > fFinanceOrders[0].ProcessAmount {
		logs.CtxWarn(ctx, "[disputeWithdrawExecution-CheckParams] withdraw amount over pay amount")
		return errdef.NewRawErr(errdef.ParamErr, "withdraw amount over pay amount")
	}
	return nil
}
