package sh_auction_v2

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/gopkg/tools"
	"context"
)

type PayCallbackBaseExecution struct {
	*executor.ActionBaseExecution
	cbModel *callback_model.PayCallbackModel
}

func NewPayCallbackCommonExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &PayCallbackBaseExecution{
		cbModel: new(callback_model.PayCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel)
	return exe
}

func (p PayCallbackBaseExecution) Process(ctx context.Context) error {
	// 手动跳转
	condition := p.GetFSMCondition()
	bizErr := p.FireWithCondition(ctx, condition)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", bizErr)
		return bizErr
	}
	// 更新状态
	var (
		orderId      = p.GetOrder().FweOrder.OrderID
		stateMachine = p.GetStateMachine()
	)
	updateParams := &service_model.UpdateOrderParams{}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	updateParams.Operator = p.GetActionOrderReq().GetOperator()
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderId, updateParams); bizErr != nil {
		return bizErr
	}
	return nil
}
