package sh_auction_v2

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_auction_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"fmt"
)

type disputeWithdrawExecution struct {
	*executor.ActionBaseExecution
	bizReq    execution_common.WithdrawReq
	result    interface{}
	condition map[string]interface{}
}

func NewDisputeWithdrawExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	var opts []*executor.Option
	e := &disputeWithdrawExecution{}
	orderReq, ok := actionReq.(*engine.ActionOrderReq)
	if !ok {
		logs.CtxWarn(ctx, "actionReq data type error, req = %v", tools.GetLogStr(actionReq))
		return nil
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, orderReq, nil, &e.bizReq, opts...)
	return e
}

func (e *disputeWithdrawExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err.Error())
		return err
	}
	var (
		bizReq        = e.bizReq
		orderId       = e.GetActionOrderReq().OrderID
		financeOrders = e.GetOrder().FinanceList
	)
	amount := bizReq.Amount
	// 验证金额
	if bizErr := CheckFinanceOrderProcessAmount(ctx, orderId, amount, financeOrders); bizErr != nil {
		return bizErr
	}
	// 跳转状态机
	e.condition = e.genCondition()
	bizErr := e.FireWithCondition(ctx, e.condition)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *disputeWithdrawExecution) Process(ctx context.Context) error {
	var (
		bizReq   = e.bizReq
		bizErr   *errdef.BizErr
		fweOrder = e.GetOrder().FweOrder
	)
	if int32(e.GetBizIdentity().TenantType) != fweOrder.TenantType || e.GetBizIdentity().BizScene != fweOrder.BizScene {
		bizErr = errdef.NewParamsErr("Identity 与db不一致")
		return bizErr
	}

	// 校验 是否已经存在争议订单t
	bizErr = CheckOrderTag(ctx, e.GetOrder().TagMap, bizReq.Tag, sh_auction_model.DisputeOrderNoTag)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[disputeRefundExecution-Process] CheckOrderTag error, err=%+v", bizErr.Error())
		return bizErr
	}

	// 不需要退款
	if !e.NeedWithdraw() {
		return nil
	}
	// 聚合出款
	withdrawReq, bizErr := e.buildWithdrawReq(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[disputeWithdrawExecution.process] MergeWithdrawDeposit failed, err=%s", bizErr.Error())
		return bizErr
	}
	_, bizErr = service.NewTradePayment().MergeWithdrawDeposit(ctx, withdrawReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[disputeWithdrawExecution.process] MergeWithdrawDeposit failed, err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *disputeWithdrawExecution) PostProcess(ctx context.Context) error {
	var (
		orderID      = e.GetOrder().FweOrder.OrderID
		stateMachine = e.GetStateMachine()
	)

	// 更新订单tag
	if bizErr := service.NewOrderService().UpdateOrderTag(ctx, orderID, e.bizReq.Tag); bizErr != nil {
		return bizErr
	}
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	updateParams.Operator = e.GetActionOrderReq().GetOperator()

	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *disputeWithdrawExecution) buildWithdrawReq(ctx context.Context) (*payment.MergeWithdrawDepositReq, *errdef.BizErr) {
	var (
		fweOrder = e.GetOrder().FweOrder
		bizReq   = e.bizReq
	)

	ids := make([]string, 0)
	for _, order := range e.GetOrder().FinanceList {
		ids = append(ids, order.FinanceOrderID)
	}

	if len(ids) == 0 {
		bizErr := errdef.NewRawErr(errdef.DirtyDataException, "资金单数据错误")
		return nil, bizErr
	}

	sellerInfo, bizErr := packer.CommonTradeSubjectDeserialize(fweOrder.SellerID, *fweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[GetSellAccountShop] err=%s", bizErr.Error())
		return nil, bizErr
	}
	if sellerInfo == nil || sellerInfo.FweMerchant == nil || sellerInfo.FweMerchant.FweAccountID == "" {
		bizErr = errdef.NewParamsErr("fwe account id is nil")
		logs.CtxWarn(ctx, "[GetSellAccountShop] err=%s", bizErr.Error())
		return nil, bizErr
	}

	// uid 和 uid_type 不是财经出款需要的参数
	item := &payment.WithdrawDepositData{
		Amount:       bizReq.Amount,
		MerchantID:   bizReq.MerchantID,
		MerchantName: bizReq.MerchantName,
		AppID:        bizReq.AppID,
		WithdrawType: bizReq.WithdrawType,
		UID:          sellerInfo.FweMerchant.FweAccountID,
		UIDType:      0,
		WithdrawName: fmt.Sprintf("%s-争议出款", fweOrder.OrderID),
		WitdhrawDesc: bizReq.Reason,
		Currency:     payment.CurrencyTypePtr(payment.CurrencyType_CNY),
		BankCardInfo: bizReq.BankCardInfo,
	}

	rpcReq := &payment.MergeWithdrawDepositReq{
		Identity:            e.GetBizIdentity(),
		OrderID:             fweOrder.OrderID,
		WithdrawFinanceType: 1,
		WithdrawList:        []*payment.WithdrawDepositData{item},
		FinanceOrderIDList:  ids,
		IPAddress:           bizReq.IPAddress,
		Extra:               nil,
		CallbackEvent:       utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:       "",
		FailCallbackEvent:   "",
		Base:                nil,
	}

	return rpcReq, nil
}

func (e *disputeWithdrawExecution) genCondition() map[string]interface{} {
	bizReq := e.bizReq
	res := map[string]interface{}{
		consts.CondShAuctionRefundOrPayoutAmount: bizReq.Amount,
	}
	return res
}

func (e *disputeWithdrawExecution) NeedWithdraw() bool {
	amount := int64(0)
	for key, value := range e.condition {
		if key == consts.CondShAuctionRefundOrPayoutAmount {
			if amountValue, ok := value.(int64); ok {
				amount += amountValue
			}
			break
		}
	}
	return amount > 0
}
