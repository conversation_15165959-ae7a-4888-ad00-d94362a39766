package sh_auction_v2

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	consts2 "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_auction_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"github.com/bytedance/sonic"
)

type disputeRefundExecution struct {
	*executor.ActionBaseExecution
	bizReq    sh_auction_model.CashRefundReq
	result    interface{}
	condition map[string]interface{}
}

func NewDisputeRefundExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	var opts []*executor.Option
	e := &disputeRefundExecution{}
	orderReq, ok := actionReq.(*engine.ActionOrderReq)
	if !ok {
		logs.CtxWarn(ctx, "actionReq data type error, req = %v", tools.GetLogStr(actionReq))
		return nil
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, orderReq, nil, &e.bizReq, opts...)
	return e
}

func (e *disputeRefundExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err.Error())
		return err
	}
	var (
		bizReq        = e.bizReq
		orderId       = e.GetActionOrderReq().OrderID
		financeOrders = e.GetOrder().FinanceList
	)
	amount := int64(0)
	if bizReq.RefundList != nil {
		for _, refund := range bizReq.RefundList {
			if refund.FinanceOrderType == int32(consts2.FinanceTotal) {
				amount += refund.Amount
			}
		}
	}
	// 验证金额
	if bizErr := CheckFinanceOrderProcessAmount(ctx, orderId, amount, financeOrders); bizErr != nil {
		return bizErr
	}
	// 跳转状态机
	e.condition = e.genCondition()
	bizErr := e.FireWithCondition(ctx, e.condition)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *disputeRefundExecution) Process(ctx context.Context) error {

	var (
		bizReq        = e.bizReq
		cashRefundReq *payment.MergeRefundReq
		mergeRefundNo string
		bizErr        *errdef.BizErr
		err           error
		fweOrder      = e.GetOrder().FweOrder
	)

	if int32(e.GetBizIdentity().TenantType) != fweOrder.TenantType || e.GetBizIdentity().BizScene != fweOrder.BizScene {
		bizErr = errdef.NewParamsErr("Identity 与db不一致")
		return bizErr
	}

	// 校验 是否已经存在争议订单t
	bizErr = CheckOrderTag(ctx, e.GetOrder().TagMap, bizReq.OrderTag, sh_auction_model.DisputeOrderNoTag)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[disputeRefundExecution-Process] CheckOrderTag error, err=%+v", bizErr.Error())
		return bizErr
	}
	// 不需要退款
	if !e.NeedRefund() {
		return nil
	}

	cashRefundReq, bizErr = e.buildRefundReq()
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr = service.NewTradePayment().MergeRefund(ctx, cashRefundReq)
	if bizErr != nil {
		return bizErr
	}

	rpcRsp := &action_model.CreateRefundRsp{
		MergeRefundID: mergeRefundNo,
	}
	e.result, err = sonic.MarshalString(rpcRsp)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		return bizErr
	}

	return nil
}

func (e *disputeRefundExecution) PostProcess(ctx context.Context) error {
	var (
		orderID      = e.GetOrder().FweOrder.OrderID
		stateMachine = e.GetStateMachine()
	)

	// 更新订单tag
	if bizErr := service.NewOrderService().UpdateOrderTag(ctx, orderID, e.bizReq.OrderTag); bizErr != nil {
		return bizErr
	}
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	updateParams.Operator = e.GetActionOrderReq().GetOperator()
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *disputeRefundExecution) buildRefundReq() (*payment.MergeRefundReq, *errdef.BizErr) {
	var (
		order    = e.GetOrder()
		orderID  = e.GetOrder().FweOrder.OrderID
		fweOrder = order.FweOrder
		bizReq   = e.bizReq
	)

	refundList := make([]*payment.SingleRefund, 0, len(bizReq.RefundList))

	for _, refund := range bizReq.RefundList {
		financeOrder := packer.FinanceGetByType(order.FinanceList, refund.FinanceOrderType)
		if financeOrder == nil {
			return nil, errdef.NewRawErr(errdef.DataErr, "找不到对应的资金单")
		}
		refundList = append(refundList, &payment.SingleRefund{
			FinanceOrderID: financeOrder.FinanceOrderID,
			Amount:         refund.Amount,
		})
	}

	serviceReq := &payment.MergeRefundReq{
		Identity:          e.GetBizIdentity(),
		RefundList:        refundList,
		OrderID:           orderID,
		RefundFinanceType: bizReq.RefundType,
		OrderName:         fweOrder.OrderName,
		Reason:            bizReq.Reason,
		Extra:             nil,
		CallbackEvent:     utils.MakeCallbackEvent(bizReq.CallbackEvent),
		CallbackExtra:     "",
		IPAddress:         bizReq.IpAddress,
	}
	return serviceReq, nil
}

func (e *disputeRefundExecution) genCondition() map[string]interface{} {
	bizReq := e.bizReq
	amount := int64(0)
	if bizReq.RefundList != nil {
		for _, refund := range bizReq.RefundList {
			amount += refund.Amount
		}
	}
	res := map[string]interface{}{
		consts.CondShAuctionRefundOrPayoutAmount: amount,
	}
	return res
}

func (e *disputeRefundExecution) NeedRefund() bool {
	amount := int64(0)
	for key, value := range e.condition {
		if key == consts.CondShAuctionRefundOrPayoutAmount {
			if amountValue, ok := value.(int64); ok {
				amount += amountValue
			}
			break
		}
	}
	return amount > 0
}
