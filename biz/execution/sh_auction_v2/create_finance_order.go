package sh_auction_v2

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_auction_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
)

type createFinanceOrderExecution struct {
	*executor.ActionBaseExecution
	bizReq sh_auction_model.CreateFinanceOrderReq
	bizRsp execution_common.CreateFinanceRsp
	conf   sh_auction_model.ShAuctionConfig
}

func NewCreateFinanceOrderExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &createFinanceOrderExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &t.conf, &t.bizReq)
	return t
}

func (e *createFinanceOrderExecution) Process(ctx context.Context) error {
	var (
		bizReq              = e.bizReq
		fweOrder            = e.GetOrder().FweOrder
		financeOrderService = service.NewFinanceOrderService()
		orderService        = service.NewOrderService()
		orderID             = e.GetActionOrderReq().GetOrderID()
	)

	if len(bizReq.OrderTag) > 0 {
		bizErr := service.NewOrderService().UpdateOrderTag(ctx, orderID, e.bizReq.OrderTag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	financeList, bizErr := e.buildFinanceList(ctx, fweOrder)
	if bizErr != nil {
		logs.CtxInfo(ctx, "[createFinanceOrderExecution-Process] buildFinanceList error, err = %+v", bizErr.Error())
		return bizErr
	}

	var isFind bool
	for _, v := range e.GetOrder().FinanceList {
		if v.FinanceOrderType == financeList[0].FinanceOrderType {
			isFind = true
			if v.Amount != financeList[0].Amount {
				bizErr = errdef.NewParamsErr("can not update amount")
				logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
				return bizErr
			}
		}
	}
	if isFind {
		logs.CtxInfo(ctx, "[CreateFinanceExecution] has find finances")
		return nil
	}
	// 新建资金单
	bizErr = financeOrderService.CreateFinanceOrderList(ctx, financeList)
	if bizErr != nil {
		logs.CtxInfo(ctx, "[createFinanceOrderExecution-Process] CreateFinanceOrderList error, err = %+v", bizErr.Error())
		return bizErr
	}
	updateParams := &service_model.UpdateOrderParams{
		UpdateTotalAmount: conv.Int64Ptr(bizReq.TotalAmount),
		Operator:          e.GetActionOrderReq().Operator,
	}
	// 更新订单行总金额
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *createFinanceOrderExecution) buildFinanceList(ctx context.Context, order *db_model.FweOrder) ([]*db_model.FFinanceOrder, *errdef.BizErr) {
	var (
		financeOrder = e.bizReq.FinanceOrder
	)
	output := make([]*db_model.FFinanceOrder, 0)
	output = append(output, &db_model.FFinanceOrder{
		TenantType:       order.TenantType,
		BizScene:         order.BizScene,
		AppID:            "",
		MerchantID:       financeOrder.MerchantID,
		Mid:              "",
		OrderID:          order.OrderID,
		OrderName:        order.OrderName,
		TradeType:        financeOrder.TradeType,
		FinanceOrderID:   utils.MakeFinanceOrderIDTool(order.OrderID, financeOrder.FinanceOrderType),
		FinanceOrderType: financeOrder.FinanceOrderType,
		Amount:           financeOrder.Amount,
		ProcessAmount:    0,
		FeeItemDetail:    conv.StringPtr(tools.GetLogStr(financeOrder.FeeItemList)),
		Status:           getStatusBool(financeOrder.Amount == 0, fwe_trade_common.FinanceStatus_Complete, fwe_trade_common.FinanceStatus_NotHandle),
	})
	return output, nil
}

func getStatusBool(cond bool, a, b fwe_trade_common.FinanceStatus) int32 {
	if cond {
		return int32(a)
	} else {
		return int32(b)
	}
}
