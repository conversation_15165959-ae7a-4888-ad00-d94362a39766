package execution

import (
	"code.byted.org/motor/fwe_trade_engine/biz/execution/finance"
	"context"
	"fmt"

	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/scene"
	"code.byted.org/motor/fwe_trade_common/statemachine/ams_state"
	"code.byted.org/motor/fwe_trade_common/statemachine/finance_saas"
	"code.byted.org/motor/fwe_trade_common/statemachine/nc_state"
	nc_ecom_state "code.byted.org/motor/fwe_trade_common/statemachine/nc_state/nc_ecom"
	CommonSHSell "code.byted.org/motor/fwe_trade_common/statemachine/sh_sell"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_common/statemachine/supply_state"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/aftermarket_retail"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/car_supply"
	CommonAction "code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/nc_ecom"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/nc_shop"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_auction"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_auction_v2"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_buy"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_finance"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_nation_sell"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_nation_sell_v2"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/sh_sell"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/test"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
)

type Constructor func(ctx context.Context, params interface{}) executor.IExecution

var testExecution = map[string]Constructor{
	"Create": test.NewCreatExecution,
	"Pay":    test.NewPayExecution,
	"Refund": test.NewRefundExecution,
}

// 二手车 自营卖车 订金-尾款|首付款-[贷款]
var shSellCarExecution1 = map[string]Constructor{
	consts.CreateAction:                        sh_sell.NewCreateOrderExecution,               // 创建订单
	consts.ShSellEFSignIntentContractEvent:     sh_sell.NewCreateContractExecution,            // 创建意向合同
	consts.ShSellEFSignIntentContractOverEvent: callback.NewContCallbackCommonExecution,       // 意向合同签署成功
	consts.ShSellEFPayEarnestEvent:             sh_sell.NewPayCashierExecution,                // 意向金-收银台支付
	consts.ShSellEFPayEarnestOverEvent:         callback.NewPayCallbackCommonExecution,        // 意向金-支付完成
	consts.ShSellEFRefundEarnestOverEvent:      callback.NewRefundCallbackWithFinishExecution, // 退款完成
	consts.ShSellEFSignSellContractEvent:       sh_sell.NewCreateContractExecution,            // 创建买卖合同
	consts.ShSellEFSignSellContractOverEvent:   callback.NewContCallbackCommonExecution,       // 买卖合同签署成功
	consts.ShSellEFPayFinalEvent:               sh_sell.NewPayPOSExecution,                    // 尾款|首付款-POS支付
	consts.ShSellEFPayFinalOverEvent:           sh_sell.NewPayPOSOverExecution,                // 尾款|首付款-支付完成
	consts.ShSellEFTransferOwnerEvent:          sh_sell.NewTransferOwnerExecution,             // 过户
	consts.ShSellEFSelectLoanEvent:             sh_sell.NewSelectLoanTypeExecution,            // 选择贷款方式
	consts.ShSellEFConfirmLoanEvent:            sh_sell.NewLoanConfirmExecution,               // 确认贷款
	consts.ShSellEFApproveLoanPassEvent:        sh_sell.NewLoanApproveSuccExecution,           // 贷款审批成功
	consts.ShSellEFApproveLoanFailEvent:        sh_sell.NewLoanApproveFailExecution,           // 贷款审批拒绝
	consts.ShSellEFLoanOverEvent:               sh_sell.NewLoanOverExecution,                  // 贷款完成
	consts.ShSellEFDeliveryEvent:               CommonAction.NewFinishExecution,               // 交车
	consts.ShSellEFCancelEvent:                 CommonAction.NewFinishExecution,               // 结算完成
	consts.ShSellEFCancelWithRefundEvent:       sh_sell.NewCancelRefundExecution,              // 取消
	consts.ShSellEFSettleOverEvent:             callback.NewSettleCallbackCommonExecution,     // 取消with退款
	consts.ShSellEFPayEarnestTimeoutEvent:      CommonAction.NewActionExecution,               // 支付意向金超时回调
	consts.ShSellEFPayPOSTimeoutEvent:          CommonAction.NewActionExecution,               // 支付pos超时回调，回调时会判断是否有进行中的支付
}

// 二手车 自营卖车 全款|首付款-[贷款]
var shSellCarExecution2 = map[string]Constructor{
	consts.CreateAction:                        sh_sell.NewCreateOrderExecution,           // 创建订单
	consts.ShSellFullSignSellContractEvent:     sh_sell.NewCreateContractExecution,        // 创建买卖合同
	consts.ShSellFullSignSellContractOverEvent: callback.NewContCallbackCommonExecution,   // 买卖合同签署成功
	consts.ShSellFullPayMoneyEvent:             sh_sell.NewPayPOSExecution,                // 尾款|首付款-POS支付
	consts.ShSellFullPayMoneyOverEvent:         sh_sell.NewPayPOSOverExecution,            // 尾款|首付款-支付完成
	consts.ShSellFullTransferOwnerEvent:        sh_sell.NewTransferOwnerExecution,         // 过户
	consts.ShSellFullSelectLoanEvent:           sh_sell.NewSelectLoanTypeExecution,        // 选择贷款方式
	consts.ShSellFullConfirmLoanEvent:          sh_sell.NewLoanConfirmExecution,           // 确认贷款
	consts.ShSellFullApproveLoanPassEvent:      sh_sell.NewLoanApproveSuccExecution,       // 贷款审批成功
	consts.ShSellFullApproveLoanFailEvent:      sh_sell.NewLoanApproveFailExecution,       // 贷款审批拒绝
	consts.ShSellFullLoanOverEvent:             sh_sell.NewLoanOverExecution,              // 贷款完成
	consts.ShSellFullDeliveryEvent:             CommonAction.NewFinishExecution,           // 交车
	consts.ShSellFullCancelEvent:               CommonAction.NewFinishExecution,           // 取消
	consts.ShSellFullSettleOverEvent:           callback.NewSettleCallbackCommonExecution, // 结算完成
	consts.ShSellFullPayPOSTimeoutEvent:        CommonAction.NewActionExecution,           // 支付pos超时回调，回调时会判断是否有进行中的支付
}

// 二手车 自营卖车-延保
var shSellCarInsuranceExecution = map[string]Constructor{
	consts.CreateAction:                        sh_sell.NewCreateOrderExecution,
	consts.ShSellWarrantySignContractEvent:     sh_sell.NewCreateContractExecution,
	consts.ShSellWarrantySignContractOverEvent: callback.NewContCallbackCommonExecution,
	consts.ShSellWarrantyPayEvent:              sh_sell.NewPayCashierExecution,
	consts.ShSellWarrantyPayOverEvent:          callback.NewPayCallbackWithFinishExecution,
	consts.ShSellWarrantyCancelEvent:           CommonAction.NewFinishExecution,
	consts.ShSellWarrantyPayTimeoutEvent:       CommonAction.NewActionExecution,
}

// 二手车 内网&寄售 订金-尾款|首付款-[贷款]
var shConsignEFExecution = map[string]Constructor{
	consts.CreateAction:                              sh_sell.NewCreateOrderExecution,               // 创建订单
	consts.ShConsignEFSignIntentContractEvent:        sh_sell.NewCreateContractExecution,            // 创建意向合同
	consts.ShConsignEFSignIntentContractOverEvent:    callback.NewContCallbackCommonExecution,       // 意向合同签署成功
	consts.ShConsignEFPayEarnestEvent:                sh_sell.NewPayCashierExecution,                // 意向金-收银台支付
	consts.ShConsignEFPayEarnestOverEvent:            callback.NewPayCallbackCommonExecution,        // 意向金-支付完成
	consts.ShConsignEFRefundEarnestOverEvent:         callback.NewRefundCallbackWithFinishExecution, // 退款完成
	consts.ShConsignEFRefundEarnestOverAfterPOSEvent: sh_sell.NewRefundOverAfterPOSExecution,        // POS支付后退订金完成
	consts.ShConsignEFSignSellContractEvent:          sh_sell.NewCreateContractExecution,            // 创建买卖合同
	consts.ShConsignEFSignSellContractOverEvent:      callback.NewContCallbackCommonExecution,       // 买卖合同签署成功
	consts.ShConsignEFPayFinalEvent:                  sh_sell.NewPayPOSExecution,                    // 尾款|首付款-POS支付
	consts.ShConsignEFPayFinalOverEvent:              sh_sell.NewPayPOSOverExecution,                // 尾款|首付款-支付完成
	consts.ShConsignEFTransferOwnerEvent:             sh_sell.NewTransferOwnerExecution,             // 过户
	consts.ShConsignEFSelectLoanEvent:                sh_sell.NewSelectLoanTypeExecution,            // 选择贷款方式
	consts.ShConsignEFConfirmLoanEvent:               sh_sell.NewLoanConfirmExecution,               // 确认贷款
	consts.ShConsignEFApproveLoanPassEvent:           sh_sell.NewLoanApproveSuccExecution,           // 贷款审批成功
	consts.ShConsignEFApproveLoanFailEvent:           sh_sell.NewLoanApproveFailExecution,           // 贷款审批拒绝
	consts.ShConsignEFLoanOverEvent:                  sh_sell.NewLoanOverExecution,                  // 贷款完成
	consts.ShConsignEFDeliveryEvent:                  CommonAction.NewFinishExecution,               // 交车
	consts.ShConsignEFSettleOverEvent:                callback.NewSettleCallbackCommonExecution,     // 结算完成
	consts.ShConsignEFCancelEvent:                    CommonAction.NewFinishExecution,               // 取消
	consts.ShConsignEFCancelWithRefundEvent:          sh_sell.NewCancelRefundExecution,              // 取消with退款
	consts.ShConsignEFPayPOSTimeoutEvent:             CommonAction.NewActionExecution,               // 支付pos超时回调，回调时会判断是否有进行中的支付
}

// 二手车 内网&寄售 全款|首付款-[贷款]
var shConsignFullExecution = map[string]Constructor{
	consts.CreateAction:                           sh_sell.NewCreateOrderExecution,           // 创建订单
	consts.ShConsignFullSignSellContractEvent:     sh_sell.NewCreateContractExecution,        // 创建买卖合同
	consts.ShConsignFullSignSellContractOverEvent: callback.NewContCallbackCommonExecution,   // 买卖合同签署成功
	consts.ShConsignFullPayMoneyEvent:             sh_sell.NewPayPOSExecution,                // 尾款|首付款-POS支付
	consts.ShConsignFullPayMoneyOverEvent:         sh_sell.NewPayPOSOverExecution,            // 尾款|首付款-支付完成
	consts.ShConsignFullTransferOwnerEvent:        sh_sell.NewTransferOwnerExecution,         // 过户
	consts.ShConsignFullSelectLoanEvent:           sh_sell.NewSelectLoanTypeExecution,        // 选择贷款方式
	consts.ShConsignFullConfirmLoanEvent:          sh_sell.NewLoanConfirmExecution,           // 确认贷款
	consts.ShConsignFullApproveLoanPassEvent:      sh_sell.NewLoanApproveSuccExecution,       // 贷款审批成功
	consts.ShConsignFullApproveLoanFailEvent:      sh_sell.NewLoanApproveFailExecution,       // 贷款审批拒绝
	consts.ShConsignFullLoanOverEvent:             sh_sell.NewLoanOverExecution,              // 贷款完成
	consts.ShConsignFullDeliveryEvent:             CommonAction.NewFinishExecution,           // 交车
	consts.ShConsignFullCancelEvent:               CommonAction.NewFinishExecution,           // 取消
	consts.ShConsignFullSettleOverEvent:           callback.NewSettleCallbackCommonExecution, // 结算完成
	consts.ShConsignFullPayPOSTimeoutEvent:        CommonAction.NewActionExecution,           // 支付pos超时回调，回调时会判断是否有进行中的支付
}

// 二手车 ACN 订金-尾款|首付款-[贷款]
var shACNEFExecution = map[string]Constructor{
	statemachine.ShACNEFCreateEvent.Value():                    sh_sell.NewCreateOrderExecution,               // 创建订单
	statemachine.ShACNEFSignIntentContractEvent.Value():        sh_sell.NewCreateContractExecution,            // 创建意向合同
	statemachine.ShACNEFSignIntentContractOverEvent.Value():    callback.NewContCallbackCommonExecution,       // 意向合同签署成功
	statemachine.ShACNEFPayEarnestEvent.Value():                sh_sell.NewPayCashierExecution,                // 意向金-收银台支付
	statemachine.ShACNEFPayEarnestOverEvent.Value():            callback.NewPayCallbackCommonExecution,        // 意向金-支付完成
	statemachine.ShACNEFRefundEarnestOverEvent.Value():         callback.NewRefundCallbackWithFinishExecution, // 退款完成
	statemachine.ShACNEFRefundEarnestOverAfterPOSEvent.Value(): sh_sell.NewRefundOverAfterPOSExecution,        // POS支付后退订金完成
	statemachine.ShACNEFSignSellContractEvent.Value():          sh_sell.NewCreateContractExecution,            // 创建买卖合同
	statemachine.ShACNEFSignSellContractOverEvent.Value():      callback.NewContCallbackCommonExecution,       // 买卖合同签署成功
	statemachine.ShACNEFPayFinalEvent.Value():                  sh_sell.NewPayPOSExecution,                    // 尾款|首付款-POS支付
	statemachine.ShACNEFPayFinalOverEvent.Value():              sh_sell.NewPayPOSOverExecution,                // 尾款|首付款-支付完成
	statemachine.ShACNEFTransferOwnerEvent.Value():             sh_sell.NewTransferOwnerExecution,             // 过户
	statemachine.ShACNEFSelectLoanEvent.Value():                sh_sell.NewSelectLoanTypeExecution,            // 选择贷款方式
	statemachine.ShACNEFConfirmLoanEvent.Value():               sh_sell.NewLoanConfirmExecution,               // 确认贷款
	statemachine.ShACNEFLoanOverEvent.Value():                  sh_sell.NewLoanOverExecution,                  // 贷款完成
	statemachine.ShACNEFApproveLoanPassEvent.Value():           sh_sell.NewLoanApproveSuccExecution,           // 贷款审批成功
	statemachine.ShACNEFApproveLoanFailEvent.Value():           sh_sell.NewLoanApproveFailExecution,           // 贷款审批拒绝
	statemachine.ShACNEFDeliveryEvent.Value():                  CommonAction.NewFinishExecution,               // 交车
	statemachine.ShACNEFCancelEvent.Value():                    CommonAction.NewFinishExecution,               // 取消
	statemachine.ShACNEFCancelWithRefundEvent.Value():          sh_sell.NewCancelRefundExecution,              // 取消with退款
	statemachine.ShACNEFPayPOSTimeoutEvent.Value():             CommonAction.NewActionExecution,               // 支付pos超时回调，回调时会判断是否有进行中的支付
}

// 二手车 ACN 全款|首付款-[贷款]
var shACNFullExecution = map[string]Constructor{
	statemachine.ShACNFullCreateEvent.Value():               sh_sell.NewCreateOrderExecution,         // 创建订单
	statemachine.ShACNFullSignSellContractEvent.Value():     sh_sell.NewCreateContractExecution,      // 创建买卖合同
	statemachine.ShACNFullSignSellContractOverEvent.Value(): callback.NewContCallbackCommonExecution, // 买卖合同签署成功
	statemachine.ShACNFullPayMoneyEvent.Value():             sh_sell.NewPayPOSExecution,              // 尾款|首付款-POS支付
	statemachine.ShACNFullPayMoneyOverEvent.Value():         sh_sell.NewPayPOSOverExecution,          // 尾款|首付款-支付完成
	statemachine.ShACNFullTransferOwnerEvent.Value():        sh_sell.NewTransferOwnerExecution,       // 过户
	statemachine.ShACNFullSelectLoanEvent.Value():           sh_sell.NewSelectLoanTypeExecution,      // 选择贷款方式
	statemachine.ShACNFullConfirmLoanEvent.Value():          sh_sell.NewLoanConfirmExecution,         // 确认贷款
	statemachine.ShACNFullApproveLoanPassEvent.Value():      sh_sell.NewLoanApproveSuccExecution,     // 贷款审批成功
	statemachine.ShACNFullApproveLoanFailEvent.Value():      sh_sell.NewLoanApproveFailExecution,     // 贷款审批拒绝
	statemachine.ShACNFullLoanOverEvent.Value():             sh_sell.NewLoanOverExecution,            // 贷款完成
	statemachine.ShACNFullDeliveryEvent.Value():             CommonAction.NewFinishExecution,         // 交车
	statemachine.ShACNFullCancelEvent.Value():               CommonAction.NewFinishExecution,         // 取消
	statemachine.ShACNEFPayPOSTimeoutEvent.Value():          CommonAction.NewActionExecution,         // 支付pos超时回调，回调时会判断是否有进行中的支付s
}

// 二手车-收车 个人收车
var shBuyPersonCarExecution = map[string]Constructor{
	consts.CreateAction:                     sh_buy.NewCreateOrderExecution,
	consts.SHBuyPersonCarBusinessContStart:  CommonAction.NewUnionContCreateExecution,     // 商业合同签署
	consts.SHBuyPersonCarBusinessContFinish: callback.NewContCallbackCommonExecution,      // 商业合同完成
	consts.SHBuyPersonCarEarnestPayStart:    CommonAction.NewWithdrawExecution,            // 订金支付发起
	consts.SHBuyPersonCarEarnestPayFinish:   callback.NewWithdrawCallbackCommonExecution,  // 订金支付完成
	consts.SHBuyPersonCarEarnestPayFailed:   sh_buy.NewWithdrawFailCallbackExecution,      // 订金支付失败
	consts.SHBuyPersonCarTransOwnerFinish:   CommonAction.NewActionExecution,              // 过户完成
	consts.SHBuyPersonCarFinalPayStart:      CommonAction.NewWithdrawExecution,            // 尾款支付发起
	consts.SHBuyPersonCarFinalPayFinish:     callback.NewWithdrawCallbackCommonExecution,  // 尾款支付完成
	consts.SHBuyPersonCarStorageInFinish:    CommonAction.NewFinishExecution,              // 入库完成
	consts.SHBuyPersonCarCancelOrderFinish:  CommonAction.NewFinishExecution,              // 取消订单
	consts.SHBuyPersonCarUpdateTotalAmount:  CommonAction.NewUpdateOrderStaticExecutionV2, // 更新总金额
}

// 二手车-收车 个人回购
var shBuyBackPersonCarExecution = map[string]Constructor{
	consts.CreateAction:                     sh_buy.NewCreateOrderExecution,
	consts.SHBuyPersonCarBusinessContStart:  CommonAction.NewUnionContCreateExecution,     // 商业合同签署
	consts.SHBuyPersonCarBusinessContFinish: callback.NewContCallbackCommonExecution,      // 商业合同完成
	consts.SHBuyPersonCarEarnestPayStart:    CommonAction.NewWithdrawExecution,            // 订金支付发起
	consts.SHBuyPersonCarEarnestPayFinish:   callback.NewWithdrawCallbackCommonExecution,  // 订金支付完成
	consts.SHBuyPersonCarEarnestPayFailed:   sh_buy.NewWithdrawFailCallbackExecution,      // 订金支付失败
	consts.SHBuyPersonCarTransOwnerFinish:   CommonAction.NewActionExecution,              // 过户完成
	consts.SHBuyPersonCarFinalPayStart:      CommonAction.NewWithdrawExecution,            // 尾款支付发起
	consts.SHBuyPersonCarFinalPayFinish:     callback.NewWithdrawCallbackCommonExecution,  // 尾款支付完成
	consts.SHBuyPersonCarStorageInFinish:    CommonAction.NewFinishExecution,              // 入库完成
	consts.SHBuyPersonCarCancelOrderFinish:  CommonAction.NewFinishExecution,              // 取消订单
	consts.SHBuyPersonCarUpdateTotalAmount:  CommonAction.NewUpdateOrderStaticExecutionV2, // 更新总金额
}

// 二手车-收车 库融企业收车
var shBuyCompanyCarExecution = map[string]Constructor{
	consts.CreateAction:                       sh_buy.NewCreateOrderExecution,
	consts.SHBuyCompanyCarGuaranteeContStart:  CommonAction.NewUnionContCreateExecution,    // 保证金合同签署
	consts.SHBuyCompanyCarGuaranteeContFinish: callback.NewContCallbackCommonExecution,     // 保证金合同完成
	consts.SHBuyCompanyCarBusinessContStart:   CommonAction.NewUnionContCreateExecution,    // 商业合同签署
	consts.SHBuyCompanyCarBusinessContFinish:  callback.NewContCallbackCommonExecution,     // 商业合同完成
	consts.SHBuyCompanyCarTransOwnerFinish:    CommonAction.NewActionExecution,             // 过户完成
	consts.SHBuyCompanyCarStorageInFinish:     CommonAction.NewActionExecution,             // 入库完成
	consts.SHBuyCompanyCarTotalPayStart:       CommonAction.NewWithdrawExecution,           // 车款支付发起
	consts.SHBuyCompanyCarTotalPayFinish:      callback.NewWithdrawCallbackCommonExecution, // 车款支付完成
	consts.SHBuyCompanyCarCancelOrderFinish:   CommonAction.NewFinishExecution,             // 取消订单
}

// 二手车-收车 企业回购
var shBackCompanyCarExecution = map[string]Constructor{
	consts.CreateAction:                        sh_buy.NewCreateOrderExecution,
	consts.SHBackCompanyCarEarnestPayStart:     sh_buy.NewEarnestCashPayStartExecution,        // 订金支付发起
	consts.SHBackCompanyCarEarnestPayFinish:    callback.NewPayCallbackCommonExecution,        // 订金支付完成
	consts.SHBackCompanyCarFinalPayStart:       sh_buy.NewFinalCashPayStartExecution,          // 尾款支付发起
	consts.SHBackCompanyCarFinalPayFinish:      callback.NewPayCallbackCommonExecution,        // 尾款支付完成
	consts.SHBackCompanyCarTransOwnerFinish:    CommonAction.NewFinishExecution,               // 过户完成
	consts.SHBackCompanyCarEarnestRefundStart:  sh_buy.NewEarnestCashRefundStartExecution,     // 订金退款发起
	consts.SHBackCompanyCarEarnestRefundFinish: callback.NewRefundCallbackWithFinishExecution, // 订金退款完成
	consts.SHBackCompanyCarCancelOrderFinish:   CommonAction.NewFinishExecution,               // 取消订单
}

// 二手车-收车 非库融企业收车
var shDirectBuyCompanyCarExecution = map[string]Constructor{
	sh_state.SHDirectBuyCompanyCarDepositCreateEt.Value():      CommonAction.NewCreateOrderExecution,
	sh_state.SHDirectBuyCompanyCarSkipDepositEt.Value():        CommonAction.NewUpdateOrderFireExecution,
	sh_state.SHDirectBuyCompanyCarDepositContStartEt.Value():   CommonAction.NewUnionContCreateExecution,
	sh_state.SHDirectBuyCompanyCarDepositContFinishEt.Value():  callback.NewContCallbackCommonExecution,
	sh_state.SHDirectBuyCompanyCarDepositPayStartEt.Value():    CommonAction.NewWithdrawExecution,
	sh_state.SHDirectBuyCompanyCarDepositPayFailEt.Value():     callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHDirectBuyCompanyCarDepositPayFinishEt.Value():   callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHDirectBuyCompanyCarBusinessContStartEt.Value():  CommonAction.NewUnionContCreateExecution,
	sh_state.SHDirectBuyCompanyCarBusinessContFinishEt.Value(): callback.NewContCallbackCommonExecution,
	sh_state.SHDirectBuyCompanyCarEarnestPayStartEt.Value():    CommonAction.NewWithdrawExecution,
	sh_state.SHDirectBuyCompanyCarEarnestPayFailEt.Value():     callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHDirectBuyCompanyCarEarnestPayFinishEt.Value():   callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHDirectBuyCompanyCarTransOwnerFinishEt.Value():   CommonAction.NewUpdateOrderFireExecution,
	sh_state.SHDirectBuyCompanyCarFinalPayStartEt.Value():      CommonAction.NewWithdrawExecution,
	sh_state.SHDirectBuyCompanyCarFinalPayFailEt.Value():       callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHDirectBuyCompanyCarFinalPayFinishEt.Value():     callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHDirectBuyCompanyCarCreateFinanceEt.Value():      CommonAction.NewCreateFinanceOrderExecution,
	sh_state.SHDirectBuyCompanyCarCancelOrderFinishEt.Value():  CommonAction.NewUpdateOrderFireExecution,
	sh_state.SHDirectBuyCompanyCarUpdateTotalAmountEt.Value():  CommonAction.NewUpdateOrderStaticExecutionV2,
}

// 二手车-收车 门店企业回购
var shBuyBackCompanyCarExecution = map[string]Constructor{
	sh_state.SHBuyBackCompanyCarDepositCreateEt.Value():      CommonAction.NewCreateOrderExecution,
	sh_state.SHBuyBackCompanyCarBusinessContStartEt.Value():  CommonAction.NewUnionContCreateExecution,
	sh_state.SHBuyBackCompanyCarBusinessContFinishEt.Value(): callback.NewContCallbackCommonExecution,
	sh_state.SHBuyBackCompanyCarEarnestPayStartEt.Value():    CommonAction.NewWithdrawExecution,
	sh_state.SHBuyBackCompanyCarEarnestPayFailEt.Value():     callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHBuyBackCompanyCarEarnestPayFinishEt.Value():   callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHBuyBackCompanyCarTransOwnerFinishEt.Value():   CommonAction.NewUpdateOrderFireExecution,
	sh_state.SHBuyBackCompanyCarFinalPayStartEt.Value():      CommonAction.NewWithdrawExecution,
	sh_state.SHBuyBackCompanyCarFinalPayFailEt.Value():       callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHBuyBackCompanyCarFinalPayFinishEt.Value():     callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHBuyBackCompanyCarCreateFinanceEt.Value():      CommonAction.NewCreateFinanceOrderExecution,
	sh_state.SHBuyBackCompanyCarCancelOrderFinishEt.Value():  CommonAction.NewUpdateOrderFireExecution,
	sh_state.SHBuyBackCompanyCarUpdateTotalAmountEt.Value():  CommonAction.NewUpdateOrderStaticExecutionV2,
}

// 二手车-金融
var shFinanceExecution = map[string]Constructor{
	consts.CreateAction:                   sh_finance.NewShFinanceOrderCreateExecution,
	consts.SHFinanceReviewApprove:         CommonAction.NewActionExecution,
	consts.SHFinanceReviewReject:          CommonAction.NewFinishExecution,
	consts.SHFinanceBeginSignContract:     sh_finance.NewContSignExecution,
	consts.SHFinanceSignSucAndTransferSuc: sh_finance.NewSignAndTransferSucExecution,
	consts.SHFinanceVerifyApprove:         CommonAction.NewFinishExecution,
	consts.SHFinanceFirstReviewResult:     CommonAction.NewTagExecution,
	consts.SHFinanceFirstVerifyResult:     CommonAction.NewTagExecution,
	consts.SHFinanceBeginVerify:           CommonAction.NewTagExecution,
}

// 二手车 全国购
var shNationSellExecution = map[string]Constructor{
	sh_state.SHNationSellCreateOrderEt.Value():               sh_nation_sell.NewCreateOrderExecution,
	sh_state.SHNationSellSignIntentContractEt.Value():        sh_nation_sell.NewCreateContractExecution,
	sh_state.SHNationSellSignIntentContractOverEt.Value():    callback.NewContCallbackCommonExecution,
	sh_state.ShNationSellPayEarnestMoneyEt.Value():           sh_nation_sell.NewPayGuaranteeExecution,
	sh_state.ShNationSellPayEarnestMoneyOverEt.Value():       callback.NewPayCallbackCommonExecution,
	sh_state.SHNationSellSignSellContractEt.Value():          sh_nation_sell.NewCreateContractExecution,
	sh_state.SHNationSellSignSellContractOverEt.Value():      callback.NewContCallbackCommonExecution,
	sh_state.SHNationSellSignAfterSaleContractEt.Value():     sh_nation_sell.NewCreateContractExecution,
	sh_state.SHNationSellSignAfterSaleContractOverEt.Value(): callback.NewContCallbackCommonExecution,
	sh_state.ShNationSellPayAdvanceMoneyEt.Value():           sh_nation_sell.NewPayGuaranteeExecution,
	sh_state.ShNationSellPayAdvanceMoneyOverEt.Value():       sh_nation_sell.NewPayAdvanceMoneyCallbackExecution,
	sh_state.ShNationSellPayFinalMoneyEt.Value():             sh_nation_sell.NewPayGuaranteeExecution,
	sh_state.ShNationSellPayFinalMoneyOverEt.Value():         callback.NewPayCallbackCommonExecution,
	sh_state.SHNationSellTransferOwnerEt.Value():             sh_nation_sell.NewTransferOwnerExecution,
	sh_state.SHNationSellDeliverCarEt.Value():                sh_nation_sell.NewDeliverCarExecution,
	sh_state.ShNationSellSelectLoanEt.Value():                sh_nation_sell.NewSelectLoanTypeExecution,
	sh_state.ShNationSellConfirmLoanEt.Value():               sh_nation_sell.NewLoanConfirmExecution,
	sh_state.ShNationSellApproveLoanPassEt.Value():           sh_nation_sell.NewLoanApproveSuccessExecution,
	sh_state.ShNationSellApproveLoanFailEt.Value():           sh_nation_sell.NewLoanApproveFailExecution,
	sh_state.ShNationSellLoanOverEt.Value():                  sh_nation_sell.NewLoanOverExecution,
	sh_state.ShNationSellPayTimeoutEt.Value():                CommonAction.NewActionExecution,
	sh_state.SHNationSellCancelEt.Value():                    sh_nation_sell.NewCancelExecution,
	sh_state.SHNationSellCancelWithRefundEt.Value():          sh_nation_sell.NewCancelWithRefundExecution,
	sh_state.SHNationSellCancelWithRefundOverEt.Value():      sh_nation_sell.NewCancelWithRefundCallbackExecution,
	sh_state.SHNationSellOrderLoanOverEt.Value():             sh_nation_sell.NewOrderLoanOverExecution,
	sh_state.SHNationSellConfirmOrderEt.Value():              sh_nation_sell.NewConfirmOrderExecution,
	sh_state.SHNationSellLaunchOrderLoanEt.Value():           sh_nation_sell.NewLaunchOrderLoanExecution,
}

// 二手车 全国购v2
var shNationSellV2Execution = map[string]Constructor{
	sh_state.SHNationSellV2CreateOrderEt.Value():               sh_nation_sell_v2.NewCreateOrderExecution,
	sh_state.SHNationSellV2SignIntentContractEt.Value():        sh_nation_sell_v2.NewCreateContractExecution,
	sh_state.SHNationSellV2SignIntentContractOverEt.Value():    callback.NewContCallbackCommonExecution,
	sh_state.SHNationSellV2PayEarnestMoneyEt.Value():           sh_nation_sell_v2.NewUnionPayExecution,
	sh_state.SHNationSellV2PayEarnestMoneyOverEt.Value():       callback.NewPayCallbackCommonExecution,
	sh_state.SHNationSellV2SignSellContractEt.Value():          sh_nation_sell_v2.NewCreateContractExecution,
	sh_state.SHNationSellV2SignSellContractOverEt.Value():      callback.NewContCallbackCommonExecution,
	sh_state.SHNationSellV2SignAfterSaleContractEt.Value():     sh_nation_sell_v2.NewCreateContractExecution,
	sh_state.SHNationSellV2SignAfterSaleContractOverEt.Value(): callback.NewContCallbackCommonExecution,
	sh_state.SHNationSellV2PayAdvanceMoneyEt.Value():           sh_nation_sell_v2.NewUnionPayExecution,
	sh_state.SHNationSellV2PayAdvanceMoneyOverEt.Value():       sh_nation_sell_v2.NewPayAdvanceMoneyCallbackExecution,
	sh_state.SHNationSellV2PayFinalMoneyEt.Value():             sh_nation_sell_v2.NewUnionPayExecution,
	sh_state.SHNationSellV2PayFinalMoneyOverEt.Value():         callback.NewPayCallbackCommonExecution,
	sh_state.SHNationSellV2TransferOwnerEt.Value():             sh_nation_sell_v2.NewTransferOwnerExecution,
	sh_state.SHNationSellV2DeliverCarEt.Value():                sh_nation_sell_v2.NewDeliverCarExecution,
	sh_state.SHNationSellV2SelectLoanEt.Value():                sh_nation_sell_v2.NewSelectLoanTypeExecution,
	sh_state.SHNationSellV2ConfirmLoanEt.Value():               sh_nation_sell_v2.NewLoanConfirmExecution,
	sh_state.SHNationSellV2ApproveLoanPassEt.Value():           sh_nation_sell_v2.NewLoanApproveSuccessExecution,
	sh_state.SHNationSellV2ApproveLoanFailEt.Value():           sh_nation_sell_v2.NewLoanApproveFailExecution,
	sh_state.SHNationSellV2LoanOverEt.Value():                  sh_nation_sell_v2.NewLoanOverExecution,
	sh_state.SHNationSellV2PayTimeoutEt.Value():                CommonAction.NewActionExecution,
	sh_state.SHNationSellV2CancelEt.Value():                    sh_nation_sell_v2.NewCancelExecution,
	sh_state.SHNationSellV2CancelWithRefundEt.Value():          sh_nation_sell_v2.NewCancelWithRefundExecution,
	sh_state.SHNationSellV2CancelWithRefundOverEt.Value():      sh_nation_sell_v2.NewCancelWithRefundCallbackExecution,
	sh_state.SHNationSellV2OrderLoanOverEt.Value():             sh_nation_sell_v2.NewOrderLoanOverExecution,
	sh_state.SHNationSellV2ConfirmOrderEt.Value():              sh_nation_sell_v2.NewConfirmOrderExecution,
	sh_state.SHNationSellV2LaunchOrderLoanEt.Value():           sh_nation_sell_v2.NewLaunchOrderLoanExecution,
	sh_state.SHNationSellV2AdvanceMoneyApprovePassEt.Value():   sh_nation_sell_v2.NewTransferMoneyApprovePassExecution,
	sh_state.SHNationSellV2FinalMoneyApprovePassEt.Value():     sh_nation_sell_v2.NewTransferMoneyApprovePassExecution,
	sh_state.SHNationSellV2SetBankcardEt.Value():               sh_nation_sell_v2.NewSetWithdrawBankcardExecution,
	sh_state.SHNationSellV2SettleFinishEt.Value():              callback.NewSettleCallbackCommonExecution,
}

// 二手车 全国购-延保
var shNationInsuranceExecution = map[string]Constructor{
	sh_state.SHNationWarrantyCreateOrderEt.Value(): sh_nation_sell.NewCreateOrderExecution,
	sh_state.SHNationWarrantyPayEt.Value():         sh_nation_sell.NewPayCashierExecution,
	sh_state.SHNationWarrantyPayOverEt.Value():     callback.NewPayCallbackWithFinishExecution,
	sh_state.SHNationWarrantyCancelEt.Value():      CommonAction.NewFinishExecution,
	sh_state.SHNationWarrantyPayTimeoutEt.Value():  CommonAction.NewActionExecution,
}

// 新车返佣
var ncShopBrokerageExecution = map[string]Constructor{
	"UpdateOrder": CommonAction.NewUpdateOrderStaticExecution,

	consts.UpdateExtra:  nc_shop.NewUpdateExtraNoFireExecution,
	consts.CreateAction: nc_shop.NewCreateOrderExecution,
	nc_state.NCBrokerageReviewFailUpdateOrder.Value():          nc_shop.NewUpdateOrderExecution,
	nc_state.NCBrokerageReviewPass.Value():                     nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBrokerageReviewReject.Value():                   nc_shop.NewActionBeforeStatusExecution,
	nc_state.NCBrokerageCancelStart.Value():                    nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBrokerageServiceContStart.Value():               CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCBrokerageServiceContFinish.Value():              callback.NewContCallbackCommonExecution,
	nc_state.NCBrokerageTerminationContStart.Value():           CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCBrokerageTerminationContFinish.Value():          callback.NewContCallbackCommonExecution,
	nc_state.NCBrokerageCustomerBookCarFinish.Value():          nc_shop.NewUpdateOrderExecution,
	nc_state.NCBrokerageRefundTerminationContFinish.Value():    nc_shop.NewContFinishWithRefundFullExecution,
	nc_state.NCBrokerageTotalPayStart.Value():                  nc_shop.NewPayUnionExecution,
	nc_state.NCBrokerageTotalPayTimeoutFinish.Value():          callback.NewPayCallbackCommonExecution,
	nc_state.NCBrokerageTotalPayFinish.Value():                 callback.NewPayCallbackCommonExecution,
	nc_state.NCBrokerageTotalRefundFinish.Value():              callback.NewRefundCallbackWithFinishExecution,
	nc_state.NCBrokerageWaitCarDone.Value():                    nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBrokerageWaitCarDoneToSettle.Value():            nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBrokerageLicenseDone.Value():                    nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBrokerageSettleStart.Value():                    nc_shop.NewMergeSettleExecution,
	nc_state.NCBrokerageSettleFinish.Value():                   callback.NewSettleCallbackCommonExecution,
	nc_state.NCBrokerageCompatibleSplitSettleToWairCar.Value(): nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBrokerageUpdateTotalPayTradeType.Value():        nc_shop.NewUpdateFinanceTradeTypeExecution,
}

// 新车返佣无水平
var ncShopBrokerageNoHorizonExecution = map[string]Constructor{
	"UpdateOrder": CommonAction.NewUpdateOrderStaticExecution,

	consts.UpdateExtra:  nc_shop.NewUpdateExtraNoFireExecution,
	consts.CreateAction: nc_shop.NewCreateOrderExecution,
	nc_state.NCBrokerageNoHorizonCustomerBookCarFinish.Value(): nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBrokerageNoHorizonWaitCarDone.Value():           nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBrokerageCancelStart.Value():                    nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBrokerageReviewPass.Value():                     nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBrokerageReviewReject.Value():                   nc_shop.NewActionBeforeStatusExecution,
}

// 新车大定
var ncShopBigDepositExecution = map[string]Constructor{
	"UpdateOrder": CommonAction.NewUpdateOrderStaticExecution,

	consts.UpdateExtra:  nc_shop.NewUpdateExtraNoFireExecution,
	consts.CreateAction: nc_shop.NewCreateOrderExecution,
	nc_state.NCBigDepositReviewFailUpdateOrder.Value():        nc_shop.NewUpdateOrderExecution,
	nc_state.NCBigDepositReviewPass.Value():                   nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBigDepositReviewReject.Value():                 nc_shop.NewActionBeforeStatusExecution,
	nc_state.NCBigDepositCancelStart.Value():                  nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBigDepositBusinessContStart.Value():            CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCBigDepositBusinessContFinish.Value():           callback.NewContCallbackCommonExecution,
	nc_state.NCBigDepositTerminationContStart.Value():         CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCBigDepositTerminationContFinish.Value():        callback.NewContCallbackCommonExecution,
	nc_state.NCBigDepositRefundTerminationContFinish.Value():  nc_shop.NewContFinishWithRefundBigDepositExecution,
	nc_state.NCBigDepositBigEarnestPayStart.Value():           nc_shop.NewPayUnionExecution,
	nc_state.NCBigDepositBigEarnestPayTimeoutFinish.Value():   callback.NewPayCallbackCommonExecution,
	nc_state.NCBigDepositBigEarnestPayFinish.Value():          callback.NewPayCallbackCommonExecution,
	nc_state.NCBigDepositBigEarnestRefundFinish.Value():       callback.NewRefundCallbackWithFinishExecution,
	nc_state.NCBigDepositCustomerGetCarFinish.Value():         nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBigDepositCustomerCheckCarFinish.Value():       nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBigDepositCheckCarContStart.Value():            CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCBigDepositCheckCarContFinish.Value():           nc_shop.NewContFinishWithFinalSkipExecution,
	nc_state.NCBigDepositFinalPayStart.Value():                nc_shop.NewPayUnionExecution,
	nc_state.NCBigDepositFinalPayTimeoutFinish.Value():        callback.NewPayCallbackCommonExecution,
	nc_state.NCBigDepositFinalPayFinish.Value():               callback.NewPayCallbackCommonExecution,
	nc_state.NCBigDepositWaitFinancePayDone.Value():           nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBigDepositWaitCarDone.Value():                  nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBigDepositWaitCarDoneToSettle.Value():          nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBigDepositLicenseDone.Value():                  nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCBigDepositSettleStart.Value():                  nc_shop.NewMergeSettleExecution,
	nc_state.NCBigDepositSettleFinish.Value():                 callback.NewSettleCallbackCommonExecution,
	nc_state.NCBigDepositUpdateBigEarnestPayTradeType.Value(): nc_shop.NewUpdateFinanceTradeTypeExecution,
	nc_state.NCBigDepositUpdateFinalPayTradeType.Value():      nc_shop.NewUpdateFinanceTradeTypeExecution,
}

// 新车小订
var ncShopSmallDepositExecution = map[string]Constructor{
	"UpdateOrder": CommonAction.NewUpdateOrderStaticExecution,

	consts.UpdateExtra:  nc_shop.NewUpdateExtraNoFireExecution,
	consts.CreateAction: nc_shop.NewCreateOrderExecution,
	nc_state.NCSmallDepositReviewFailUpdateOrder.Value():               nc_shop.NewUpdateOrderExecution,
	nc_state.NCSmallDepositReviewPass.Value():                          nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCSmallDepositReviewReject.Value():                        nc_shop.NewActionBeforeStatusExecution,
	nc_state.NCSmallDepositCancelStart.Value():                         nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCSmallDepositIntentionContStart.Value():                  CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCSmallDepositIntentionContFinish.Value():                 callback.NewContCallbackCommonExecution,
	nc_state.NCSmallDepositSmallEarnestPayStart.Value():                nc_shop.NewPayUnionExecution,
	nc_state.NCSmallDepositSmallEarnestPayTimeoutFinish.Value():        callback.NewPayCallbackCommonExecution,
	nc_state.NCSmallDepositSmallEarnestPayFinish.Value():               callback.NewPayCallbackCommonExecution,
	nc_state.NCSmallDepositBusinessContStart.Value():                   CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCSmallDepositBusinessContFinish.Value():                  nc_shop.NewContFinishWithBigDepositSkipExecution,
	nc_state.NCSmallDepositTerminationContStart.Value():                CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCSmallDepositTerminationContFinish.Value():               callback.NewContCallbackCommonExecution,
	nc_state.NCSmallDepositRefundSmallTerminationContFinish.Value():    nc_shop.NewContFinishWithRefundSmallDepositExecution,
	nc_state.NCSmallDepositRefundSmallBigTerminationContFinish.Value(): nc_shop.NewContFinishWithRefundSmallBigDepositExecution,
	nc_state.NCSmallDepositSmallEarnestRefundFinish.Value():            callback.NewRefundCallbackWithFinishExecution,
	nc_state.NCSmallDepositBigEarnestPayStart.Value():                  nc_shop.NewPayUnionExecution,
	nc_state.NCSmallDepositBigEarnestPayTimeoutFinish.Value():          callback.NewPayCallbackCommonExecution,
	nc_state.NCSmallDepositBigEarnestPayFinish.Value():                 callback.NewPayCallbackCommonExecution,
	nc_state.NCSmallDepositEarnestRefundFinish.Value():                 callback.NewRefundCallbackWithFinishExecution,
	nc_state.NCSmallDepositShopBookCarFinish.Value():                   nc_shop.NewUpdateOrderExecution,
	nc_state.NCSmallDepositCustomerGetCarFinish.Value():                nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCSmallDepositCustomerCheckCarFinish.Value():              nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCSmallDepositCheckCarContStart.Value():                   CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCSmallDepositCheckCarContFinish.Value():                  nc_shop.NewContFinishWithFinalSkipExecution,
	nc_state.NCSmallDepositFinalPayStart.Value():                       nc_shop.NewPayUnionExecution,
	nc_state.NCSmallDepositFinalPayTimeoutFinish.Value():               callback.NewPayCallbackCommonExecution,
	nc_state.NCSmallDepositFinalPayFinish.Value():                      callback.NewPayCallbackCommonExecution,
	nc_state.NCSmallDepositWaitFinancePayDone.Value():                  nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCSmallDepositWaitCarDone.Value():                         nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCSmallDepositWaitCarDoneToSettle.Value():                 nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCSmallDepositLicenseDone.Value():                         nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCSmallDepositSettleStart.Value():                         nc_shop.NewMergeSettleExecution,
	nc_state.NCSmallDepositSettleFinish.Value():                        callback.NewSettleCallbackCommonExecution,
	nc_state.NCSmallDepositUpdateSmallEarnestPayTradeType.Value():      nc_shop.NewUpdateFinanceTradeTypeExecution,
	nc_state.NCSmallDepositUpdateBigEarnestPayTradeType.Value():        nc_shop.NewUpdateFinanceTradeTypeExecution,
	nc_state.NCSmallDepositUpdateFinalPayTradeType.Value():             nc_shop.NewUpdateFinanceTradeTypeExecution,
	// nc_state.NCSmallDepositSkipSmallContract.Value():                   nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCSmallDepositReviewPassRefund.Value(): CommonAction.NewRefundExecution,
}

// 新车加盟大定
var ncShopFranchiseeBigDepositExecution = map[string]Constructor{
	"UpdateOrder": CommonAction.NewUpdateOrderStaticExecution,

	consts.UpdateExtra:  nc_shop.NewUpdateExtraNoFireExecution,
	consts.CreateAction: nc_shop.NewCreateOrderExecution,
	nc_state.NCFranchiseeBigDepositReviewFailUpdateOrder.Value():        nc_shop.NewUpdateOrderExecution,
	nc_state.NCFranchiseeBigDepositReviewPass.Value():                   nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositReviewReject.Value():                 nc_shop.NewActionBeforeStatusExecution,
	nc_state.NCFranchiseeBigDepositCancelStart.Value():                  nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositBusinessContStart.Value():            CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCFranchiseeBigDepositBusinessContFinish.Value():           callback.NewContCallbackCommonExecution,
	nc_state.NCFranchiseeBigDepositTerminationContStart.Value():         CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCFranchiseeBigDepositTerminationContFinish.Value():        callback.NewContCallbackCommonExecution,
	nc_state.NCFranchiseeBigDepositRefundTerminationContFinish.Value():  nc_shop.NewContFinishWithRefundBigDepositExecution,
	nc_state.NCFranchiseeBigDepositBigEarnestPayStart.Value():           nc_shop.NewPayUnionExecution,
	nc_state.NCFranchiseeBigDepositBigEarnestPayTimeoutFinish.Value():   callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeBigDepositBigEarnestPayFinish.Value():          callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeBigDepositBigEarnestRefundFinish.Value():       callback.NewRefundCallbackWithFinishExecution,
	nc_state.NCFranchiseeBigDepositCustomerGetCarFinish.Value():         nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositCustomerCheckCarFinish.Value():       nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositCheckCarContStart.Value():            CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCFranchiseeBigDepositCheckCarContFinish.Value():           nc_shop.NewContFinishWithFinalSkipExecution,
	nc_state.NCFranchiseeBigDepositFinalPayStart.Value():                nc_shop.NewPayUnionExecution,
	nc_state.NCFranchiseeBigDepositFinalPayTimeoutFinish.Value():        callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeBigDepositFinalPayFinish.Value():               callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeBigDepositWaitFinancePayDone.Value():           nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositWaitCarDone.Value():                  nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositWaitCarDoneToSettle.Value():          nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositLicenseDone.Value():                  nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositSettleStart.Value():                  nc_shop.NewMergeSettleExecution,
	nc_state.NCFranchiseeBigDepositSettleFinish.Value():                 callback.NewSettleCallbackCommonExecution,
	nc_state.NCFranchiseeBigDepositUpdateBigEarnestPayTradeType.Value(): nc_shop.NewUpdateFinanceTradeTypeExecution,
	nc_state.NCFranchiseeBigDepositUpdateFinalPayTradeType.Value():      nc_shop.NewUpdateFinanceTradeTypeExecution,
	nc_state.NCFranchiseeBigDepositUploadContPic.Value():                nc_shop.NewUpdateExtraFireExecution,
}

// 新车加盟小订
var ncShopFranchiseeSmallDepositExecution = map[string]Constructor{
	"UpdateOrder": CommonAction.NewUpdateOrderStaticExecution,

	consts.UpdateExtra:  nc_shop.NewUpdateExtraNoFireExecution,
	consts.CreateAction: nc_shop.NewCreateOrderExecution,
	nc_state.NCFranchiseeSmallDepositReviewFailUpdateOrder.Value():               nc_shop.NewUpdateOrderExecution,
	nc_state.NCFranchiseeSmallDepositReviewPass.Value():                          nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeSmallDepositReviewReject.Value():                        nc_shop.NewActionBeforeStatusExecution,
	nc_state.NCFranchiseeSmallDepositCancelStart.Value():                         nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeSmallDepositIntentionContStart.Value():                  CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCFranchiseeSmallDepositIntentionContFinish.Value():                 callback.NewContCallbackCommonExecution,
	nc_state.NCFranchiseeSmallDepositSmallEarnestPayStart.Value():                nc_shop.NewPayUnionExecution,
	nc_state.NCFranchiseeSmallDepositSmallEarnestPayTimeoutFinish.Value():        callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeSmallDepositSmallEarnestPayFinish.Value():               callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeSmallDepositBusinessContStart.Value():                   CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCFranchiseeSmallDepositBusinessContFinish.Value():                  nc_shop.NewContFinishWithBigDepositSkipExecution,
	nc_state.NCFranchiseeSmallDepositTerminationContStart.Value():                CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCFranchiseeSmallDepositTerminationContFinish.Value():               callback.NewContCallbackCommonExecution,
	nc_state.NCFranchiseeSmallDepositRefundSmallTerminationContFinish.Value():    nc_shop.NewContFinishWithRefundSmallDepositExecution,
	nc_state.NCFranchiseeSmallDepositRefundSmallBigTerminationContFinish.Value(): nc_shop.NewContFinishWithRefundSmallBigDepositExecution,
	nc_state.NCFranchiseeSmallDepositSmallEarnestRefundFinish.Value():            callback.NewRefundCallbackWithFinishExecution,
	nc_state.NCFranchiseeSmallDepositBigEarnestPayStart.Value():                  nc_shop.NewPayUnionExecution,
	nc_state.NCFranchiseeSmallDepositBigEarnestPayTimeoutFinish.Value():          callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeSmallDepositBigEarnestPayFinish.Value():                 callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeSmallDepositEarnestRefundFinish.Value():                 callback.NewRefundCallbackWithFinishExecution,
	nc_state.NCFranchiseeSmallDepositShopBookCarFinish.Value():                   nc_shop.NewUpdateOrderExecution,
	nc_state.NCFranchiseeSmallDepositCustomerGetCarFinish.Value():                nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeSmallDepositCustomerCheckCarFinish.Value():              nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeSmallDepositCheckCarContStart.Value():                   CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCFranchiseeSmallDepositCheckCarContFinish.Value():                  nc_shop.NewContFinishWithFinalSkipExecution,
	nc_state.NCFranchiseeSmallDepositFinalPayStart.Value():                       nc_shop.NewPayUnionExecution,
	nc_state.NCFranchiseeSmallDepositFinalPayTimeoutFinish.Value():               callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeSmallDepositFinalPayFinish.Value():                      callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeBigDepositWaitFinancePayDone.Value():                    nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositWaitCarDone.Value():                           nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositWaitCarDoneToSettle.Value():                   nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBigDepositLicenseDone.Value():                           nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeSmallDepositSettleStart.Value():                         nc_shop.NewMergeSettleExecution,
	nc_state.NCFranchiseeSmallDepositSettleFinish.Value():                        callback.NewSettleCallbackCommonExecution,
	nc_state.NCFranchiseeSmallDepositUpdateSmallEarnestPayTradeType.Value():      nc_shop.NewUpdateFinanceTradeTypeExecution,
	nc_state.NCFranchiseeSmallDepositUpdateBigEarnestPayTradeType.Value():        nc_shop.NewUpdateFinanceTradeTypeExecution,
	nc_state.NCFranchiseeSmallDepositUpdateFinalPayTradeType.Value():             nc_shop.NewUpdateFinanceTradeTypeExecution,
	// nc_state.NCFranchiseeSmallDepositSkipSmallContract.Value():                   nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeSmallDepositReviewPassRefund.Value(): CommonAction.NewRefundExecution,
}

// 新车加盟返佣
var ncShopFranchiseeBrokerageExecution = map[string]Constructor{
	"UpdateOrder": CommonAction.NewUpdateOrderStaticExecution,

	consts.UpdateExtra:  nc_shop.NewUpdateExtraNoFireExecution,
	consts.CreateAction: nc_shop.NewCreateOrderExecution,
	nc_state.NCFranchiseeBrokerageReviewFailUpdateOrder.Value():          nc_shop.NewUpdateOrderExecution,
	nc_state.NCFranchiseeBrokerageReviewPass.Value():                     nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBrokerageReviewReject.Value():                   nc_shop.NewActionBeforeStatusExecution,
	nc_state.NCFranchiseeBrokerageCancelStart.Value():                    nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBrokerageServiceContStart.Value():               CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCFranchiseeBrokerageServiceContFinish.Value():              callback.NewContCallbackCommonExecution,
	nc_state.NCFranchiseeBrokerageTerminationContStart.Value():           CommonAction.NewUnionContCreateExecution, // nc_shop.NewSignContStartExecution,
	nc_state.NCFranchiseeBrokerageTerminationContFinish.Value():          callback.NewContCallbackCommonExecution,
	nc_state.NCFranchiseeBrokerageCustomerBookCarFinish.Value():          nc_shop.NewUpdateOrderExecution,
	nc_state.NCFranchiseeBrokerageRefundTerminationContFinish.Value():    nc_shop.NewContFinishWithRefundFullExecution,
	nc_state.NCFranchiseeBrokerageTotalPayStart.Value():                  nc_shop.NewPayUnionExecution,
	nc_state.NCFranchiseeBrokerageTotalPayTimeoutFinish.Value():          callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeBrokerageTotalPayFinish.Value():                 callback.NewPayCallbackCommonExecution,
	nc_state.NCFranchiseeBrokerageTotalRefundFinish.Value():              callback.NewRefundCallbackWithFinishExecution,
	nc_state.NCFranchiseeBrokerageWaitCarDone.Value():                    nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBrokerageWaitCarDoneToSettle.Value():            nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBrokerageLicenseDone.Value():                    nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBrokerageSettleStart.Value():                    nc_shop.NewMergeSettleExecution,
	nc_state.NCFranchiseeBrokerageSettleFinish.Value():                   callback.NewSettleCallbackCommonExecution,
	nc_state.NCFranchiseeBrokerageCompatibleSplitSettleToWairCar.Value(): nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBrokerageUpdateTotalPayTradeType.Value():        nc_shop.NewUpdateFinanceTradeTypeExecution,
	nc_state.NCFranchiseeBrokerageUploadContPic.Value():                  nc_shop.NewUpdateExtraFireExecution,
}

// 新车加盟返佣无水平
var ncShopFranchiseeBrokerageNoHorizonExecution = map[string]Constructor{
	"UpdateOrder": CommonAction.NewUpdateOrderStaticExecution,

	consts.UpdateExtra:  nc_shop.NewUpdateExtraNoFireExecution,
	consts.CreateAction: nc_shop.NewCreateOrderExecution,
	nc_state.NCFranchiseeBrokerageNoHorizonCustomerBookCarFinish.Value(): nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBrokerageNoHorizonWaitCarDone.Value():           nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBrokerageCancelStart.Value():                    nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBrokerageReviewPass.Value():                     nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCFranchiseeBrokerageReviewReject.Value():                   nc_shop.NewActionBeforeStatusExecution,
}

// 新车采购单
var ncPurchaseExecution = map[string]Constructor{
	"UpdateOrder": CommonAction.NewUpdateOrderStaticExecution,
	nc_state.NCPurchaseCreateFinanceEt.Value():             nc_shop.NewCreateFinanceExecution,
	nc_state.NCPurchaseCreateEt.Value():                    nc_shop.NewCreatePurchaseOrderExecution,
	nc_state.NCPurchasePayEarnestStartEt.Value():           nc_shop.NewPurchasePayExecution,
	nc_state.NCPurchasePayEarnestFailCallbackEt.Value():    callback.NewWithdrawCallbackCommonExecution,
	nc_state.NCPurchasePayEarnestSuccessCallbackEt.Value(): callback.NewWithdrawCallbackCommonExecution,
	nc_state.NCPurchaseOrderCancelEt.Value():               nc_shop.NewUpdateExtraFireExecution,
	nc_state.NCPurchasePayFinalStartEt.Value():             nc_shop.NewPurchasePayExecution,
	nc_state.NCPurchasePayFinalFailCallbackEt.Value():      callback.NewWithdrawCallbackCommonExecution,
	nc_state.NCPurchasePayFinalSuccessCallbackEt.Value():   callback.NewWithdrawCallbackCommonExecution,
	nc_state.NCPurchaseOrderFinishEt.Value():               nc_shop.NewUpdateExtraFireExecution,
}

// 车品采购
var afterMarketRetailExecution = map[string]Constructor{
	ams_state.AfterMarketSupplyCreateEvent.Value():         aftermarket_retail.NewCreateOrderExecution,
	ams_state.AfterMarketSupplyAuditPassEvent.Value():      aftermarket_retail.NewAuditPassExecution,
	ams_state.AfterMarketSupplyAuditRejectEvent.Value():    aftermarket_retail.NewAuditRejectExecution,
	ams_state.AfterMarketSupplyPostProductEvent.Value():    CommonAction.NewTagExecution,
	ams_state.AfterMarketSupplyConfirmTakeDelivery.Value(): CommonAction.NewFinishExecution,
	ams_state.AfterMarketSupplyCancel.Value():              aftermarket_retail.NewCancelExecution,
	ams_state.AfterMarketSupplyInitiateBill.Value():        aftermarket_retail.NewInitBillExecution,
	ams_state.AfterMarketSupplySupplierConfirmBill.Value(): aftermarket_retail.NewSupplierConfirmExecution,
	ams_state.AfterMarketSupplyShopConfirmBill.Value():     aftermarket_retail.NewShopConfirmExecution,
	ams_state.AfterMarketSupplyInitiatePayment.Value():     aftermarket_retail.NewInitiatePaymentExecution,
	ams_state.AfterMarketSupplyPaymentFinished.Value():     aftermarket_retail.NewPaymentFinishedExecution,
	ams_state.AfterMarketSupplyCancelSettlement.Value():    aftermarket_retail.NewCancelSettlementExecution,
}

// 金融订单
var financeSaasExecution = map[string]Constructor{
	finance_saas.FSUpdateOrderEt.Value(): CommonAction.NewUpdateOrderStaticExecution,
	finance_saas.FSCreateEt.Value():      CommonAction.NewCreateOrderExecution,
	finance_saas.FSReportEt.Value():      finance.NewReportOrderExecution,

	// 合同相关
	finance_saas.FSPBOCContStartEt.Value():             CommonAction.NewUnionContCreateExecution,
	finance_saas.FSPBOCContTerminationEt.Value():       CommonAction.NewContTerminationExecution,
	finance_saas.FSPBOCContCallbackEt.Value():          callback.NewContCallbackCommonExecution,
	finance_saas.FSPersonInfoContStartEt.Value():       CommonAction.NewUnionContCreateExecution,
	finance_saas.FSPersonInfoContTerminationEt.Value(): CommonAction.NewContTerminationExecution,
	finance_saas.FSPersonInfoContCallbackEt.Value():    callback.NewContCallbackCommonExecution,

	finance_saas.FSReportReviewPassEt.Value():               CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSLoanReviewPassEt.Value():                 CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSMaterialLoanFinishEt.Value():             CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSShopPushLoanEt.Value():                   CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSMaterialBackFinishEt.Value():             CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSBrokerageFinishEt.Value():                CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSReviewRejectEt.Value():                   CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSShopCloseEt.Value():                      CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSApplicationSubmitEt.Value():              CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSApplicationSubmitPreviewPassEt.Value():   CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSApplicationSubmitPreviewRejectEt.Value(): CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSApplicationSubmitFormalEt.Value():        CommonAction.NewUpdateOrderFireExecution,
	finance_saas.FSApplicationSubmitBackEt.Value():          CommonAction.NewUpdateOrderFireExecution,
}

// 寄售撤回
var shSellConsignRevokeExecution = map[string]Constructor{
	CommonSHSell.SHSellConsignRevokeCreateEt.Value():         sh_sell.NewCreateOrderExecution,
	CommonSHSell.SHSellConsignRevokeContStartEt.Value():      sh_sell.NewRevokeContCreateExecution,
	CommonSHSell.SHSellConsignRevokeContFinishEt.Value():     callback.NewContCallbackCommonExecution,
	CommonSHSell.SHSellConsignRevokeCashPayStartEt.Value():   sh_sell.NewPayCashierExecution,
	CommonSHSell.SHSellConsignRevokeCashPayFinishEt.Value():  callback.NewPayCallbackWithFinishExecution,
	CommonSHSell.SHSellConsignRevokeCashPayExpiredEt.Value(): CommonAction.NewActionExecution,
	CommonSHSell.SHSellConsignRevokeCancelOrderEt.Value():    CommonAction.NewFinishExecution,
	CommonSHSell.SHSellConsignRevokeCashPaySkipEt.Value():    CommonAction.NewUpdateOrderFireExecutionV2,
}

// 二手车拍卖
// Deprecated
var shAuctionSellExecution = map[string]Constructor{
	sh_state.SHAuctionSellCreateEt.Value():              sh_auction.NewOrderCreateExecution,
	sh_state.SHAuctionSellConfirmUnfreezeEt.Value():     CommonAction.NewUpdateOrderFireExecution,
	sh_state.SHAuctionSellAutoConfirmEt.Value():         CommonAction.NewActionExecution,
	sh_state.SHAuctionSellDirectCancelEt.Value():        CommonAction.NewUpdateOrderFireExecution,
	sh_state.SHAuctionSellContSignStartEt.Value():       CommonAction.NewContSignExecution,
	sh_state.SHAuctionSellContSignFinishEt.Value():      callback.NewContCallbackCommonExecution,
	sh_state.SHAuctionSellPayFullStartEt.Value():        CommonAction.NewGuaranteePayExecution,
	sh_state.SHAuctionSellPayFullExpiredEt.Value():      CommonAction.NewActionExecution,
	sh_state.SHAuctionSellPayFullFinishEt.Value():       callback.NewPayCallbackCommonExecution,
	sh_state.SHAuctionSellExamineFinishEt.Value():       CommonAction.NewUpdateOrderFireExecution,
	sh_state.SHAuctionSellDeliveryContStartEt.Value():   CommonAction.NewContSignExecution,
	sh_state.SHAuctionSellDeliveryContFinishEt.Value():  callback.NewContCallbackCommonExecution,
	sh_state.SHAuctionSellDeliveryFinishEt.Value():      CommonAction.NewUpdateOrderFireExecution,
	sh_state.SHAuctionSellDisputeRefundStartEt.Value():  sh_auction.NewDisputeRefundExecution,
	sh_state.SHAuctionSellDisputeRefundFinishEt.Value(): callback.NewRefundCallbackCommonExecution,
	sh_state.SHAuctionSellDisputePayoutStartEt.Value():  sh_auction.NewDisputeWithdrawExecution,
	sh_state.SHAuctionSellDisputePayoutFinishEt.Value(): callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHAuctionSellFirstSettleStartEt.Value():    sh_auction.NewSettleExecution,
	sh_state.SHAuctionSellFirstSettleFinishEt.Value():   callback.NewSettleCallbackCommonExecution,
	sh_state.SHAuctionSellSecondSettleStartEt.Value():   sh_auction.NewSettleExecution,
	sh_state.SHAuctionSellSecondSettleFinishEt.Value():  callback.NewSettleCallbackCommonExecution,
	sh_state.SHAuctionSellThirdSettleStartEt.Value():    sh_auction.NewSettleExecution,
	sh_state.SHAuctionSellThirdSettleFinishEt.Value():   callback.NewSettleCallbackCommonExecution,
	sh_state.SHAuctionSellPayoutSettleStartEt.Value():   sh_auction.NewSettleExecution,
	sh_state.SHAuctionSellPayoutSettleFinishEt.Value():  callback.NewSettleCallbackCommonExecution,
}

// 二手车拍卖V2
var shAuctionSellV2Execution = map[string]Constructor{
	sh_state.SHAuctionSellV2CreateEt.Value():                 sh_auction_v2.NewOrderCreateExecution,
	sh_state.SHAuctionSellV2ConfirmUnfreezeEt.Value():        CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionSellV2AutoConfirmEt.Value():            CommonAction.NewActionExecution,
	sh_state.SHAuctionSellV2DirectCancelEt.Value():           CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionSellV2ContSignStartEt.Value():          sh_auction_v2.NewContSignWithLinkExecution,
	sh_state.SHAuctionSellV2ContSignFinishEt.Value():         callback.NewContCallbackCommonExecution,
	sh_state.SHAuctionSellV2PayFullStartEt.Value():           CommonAction.NewCashierPayExecution,
	sh_state.SHAuctionSellV2PayFullExpiredEt.Value():         CommonAction.NewActionExecution,
	sh_state.SHAuctionSellV2PayFullFinishEt.Value():          callback.NewPayCallbackCommonExecution,
	sh_state.SHAuctionSellV2OfflinePayFinishEt.Value():       CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionSellV2ExamineFinishEt.Value():          CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionSellV2DeliveryContStartEt.Value():      CommonAction.NewContSignExecution,
	sh_state.SHAuctionSellV2DeliveryContFinishEt.Value():     callback.NewContCallbackCommonExecution,
	sh_state.SHAuctionSellV2DeliveryFinishEt.Value():         CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionSellV2DisputeRefundStartEt.Value():     sh_auction_v2.NewDisputeRefundExecution,
	sh_state.SHAuctionSellV2DisputeRefundFinishEt.Value():    callback.NewRefundCallbackCommonExecution,
	sh_state.SHAuctionSellV2DisputePayoutStartEt.Value():     sh_auction_v2.NewDisputeWithdrawExecution,
	sh_state.SHAuctionSellV2DisputePayoutFinishEt.Value():    callback.NewWithdrawCallbackCommonExecution,
	sh_state.SHAuctionSellV2FirstSettleStartEt.Value():       sh_auction_v2.NewSettleExecution,
	sh_state.SHAuctionSellV2FirstSettleFinishEt.Value():      callback.NewSettleCallbackCommonExecution,
	sh_state.SHAuctionSellV2SecondSettleStartEt.Value():      sh_auction_v2.NewSettleExecution,
	sh_state.SHAuctionSellV2SecondSettleFinishEt.Value():     callback.NewSettleCallbackCommonExecution,
	sh_state.SHAuctionSellV2ThirdSettleStartEt.Value():       sh_auction_v2.NewSettleExecution,
	sh_state.SHAuctionSellV2ThirdSettleFinishEt.Value():      callback.NewSettleCallbackCommonExecution,
	sh_state.SHAuctionSellV2PayoutSettleStartEt.Value():      sh_auction_v2.NewSettleExecution,
	sh_state.SHAuctionSellV2PayoutSettleFinishEt.Value():     callback.NewSettleCallbackCommonExecution,
	sh_state.SHAuctionSellV2DeliveryServiceStartEt.Value():   CommonAction.NewContSignExecution,
	sh_state.SHAuctionSellV2DeliveryServiceFinishEt.Value():  callback.NewContCallbackCommonExecution,
	sh_state.SHAuctionSellV2PayServiceFeeStartEt.Value():     sh_auction_v2.NewCashierPayExecution,
	sh_state.SHAuctionSellV2PayServiceFeeFinishEt.Value():    sh_auction_v2.NewPayCallbackCommonExecution,
	sh_state.SHAuctionSellV2PayServiceFeeExpiredEt.Value():   CommonAction.NewActionExecution,
	sh_state.SHAuctionSellV2ServiceFeeRefundStartEt.Value():  CommonAction.NewRefundExecution,
	sh_state.SHAuctionSellV2ServiceFeeRefundFinishEt.Value(): callback.NewRefundCallbackCommonExecution,
	sh_state.SHAuctionSellV2ServiceFeeFOrderCreateEt.Value(): CommonAction.NewCreateFinanceOrderExecution,
	sh_state.SHAuctionSellV2CreateFinanceOrderEt.Value():     sh_auction_v2.NewCreateFinanceOrderExecution,
}

// 二手车拍卖保证金订单
var shAuctionDepositExecution = map[string]Constructor{
	sh_state.SHAuctionDepositCreateEt.Value():             sh_auction.NewDepositOrderCreateExecution,
	sh_state.SHAuctionDepositContSignStartEt.Value():      CommonAction.NewContSignExecution,
	sh_state.SHAuctionDepositContSignFinishEt.Value():     callback.NewContCallbackCommonExecution,
	sh_state.SHAuctionDepositPayFullStartEt.Value():       CommonAction.NewCashierPayExecution,
	sh_state.SHAuctionDepositPayFullExpiredEt.Value():     CommonAction.NewActionExecution,
	sh_state.SHAuctionDepositPayFullFinishEt.Value():      callback.NewPayCallbackCommonExecution,
	sh_state.SHAuctionDepositRefundStartEt.Value():        CommonAction.NewRefundExecution,
	sh_state.SHAuctionDepositRefundFinishEt.Value():       callback.NewRefundCallbackWithFinishExecution,
	sh_state.SHAuctionDepositCancelRefundStartEt.Value():  CommonAction.NewRefundExecution,
	sh_state.SHAuctionDepositCancelRefundFinishEt.Value(): callback.NewRefundCallbackWithFinishExecution,
	sh_state.SHAuctionDepositCancelOrderEt.Value():        CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionDepositSuccessOrderEt.Value():       CommonAction.NewUpdateOrderFireExecutionV2,
}

// 二手车拍卖争议赔付
var shAuctionDisputePayoutExecution = map[string]Constructor{
	sh_state.SHAuctionDisputePayoutCreateEt.Value():             sh_auction.NewDisputePayoutOrderCreateExecution,
	sh_state.SHAuctionDisputePayoutAuditPassEt.Value():          CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionDisputePayoutCreateFinanceOrderEt.Value(): CommonAction.NewCreateFinanceOrderExecution,
	sh_state.SHAuctionDisputePayoutAuditRejectEt.Value():        CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionDisputePayoutContSignStartEt.Value():      CommonAction.NewContSignExecution,
	sh_state.SHAuctionDisputePayoutContSignFinishEt.Value():     callback.NewContCallbackCommonExecution,
	sh_state.SHAuctionDisputePayoutWithdrawStartEt.Value():      CommonAction.NewWithdrawExecution,
	sh_state.SHAuctionDisputePayoutWithdrawFinishEt.Value():     callback.NewWithdrawCallbackWithFinishExecution,
	sh_state.SHAuctionDisputePayoutFinishEt.Value():             CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionDisputePayoutCancelEt.Value():             CommonAction.NewUpdateOrderFireExecutionV2,
}

// 二手车拍卖争议退款
var shAuctionDisputeRefundExecution = map[string]Constructor{
	sh_state.SHAuctionDisputeRefundCreateEt.Value():         sh_auction.NewDisputeRefundOrderCreateExecution,
	sh_state.SHAuctionDisputeRefundAuditPassEt.Value():      CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionDisputeRefundAuditRejectEt.Value():    CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionDisputeRefundContSignStartEt.Value():  sh_auction_v2.NewContSignWithLinkExecution,
	sh_state.SHAuctionDisputeRefundContSignFinishEt.Value(): callback.NewContCallbackCommonExecution,
	sh_state.SHAuctionDisputeRefundFinishEt.Value():         CommonAction.NewUpdateOrderFireExecutionV2,
	sh_state.SHAuctionDisputeRefundCancelEt.Value():         CommonAction.NewUpdateOrderFireExecutionV2,
}

// 二手车 自营卖车 定金-尾款 交车模式 1112
var shSellCarEFDCExecution = map[string]Constructor{
	consts.CreateAction: sh_sell.NewCreateOrderExecution, // 创建订单
	sh_state.ShSellEFDCSignIntentContractEvent.Value():                  sh_sell.NewCreateContractExecution,               // 创建意向合同
	sh_state.ShSellEFDCSignIntentContractOverEvent.Value():              callback.NewContCallbackCommonExecution,          // 意向合同签署成功
	sh_state.ShSellEFDCPayEarnestEvent.Value():                          sh_sell.NewPayCashierExecution,                   // 意向金-收银台支付
	sh_state.ShSellEFDCPayEarnestOverEvent.Value():                      callback.NewPayCallbackCommonExecution,           // 意向金-支付完成
	sh_state.ShSellEFDCRefundEarnestOverEvent.Value():                   callback.NewRefundCallbackWithFinishExecution,    // 退款完成
	sh_state.ShSellEFDCSignSellContractEvent.Value():                    sh_sell.NewCreateContractExecution,               // 创建买卖合同
	sh_state.ShSellEFDCSignSellContractOverEvent.Value():                callback.NewContCallbackCommonExecution,          // 买卖合同签署成功
	sh_state.ShSellEFDCPayFinalEvent.Value():                            sh_sell.NewPayPOSExecution,                       // 尾款|首付款-POS支付
	sh_state.ShSellEFDCPayFinalOverEvent.Value():                        sh_sell.NewPayPOSOverExecution,                   // 尾款|首付款-支付完成
	sh_state.ShSellEFDCTransferOwnerEvent.Value():                       sh_sell.NewTransferOwnerExecution,                // 过户
	sh_state.ShSellEFDCSettleOverEvent.Value():                          callback.NewSettleCallbackCommonExecution,        // 结算完成
	sh_state.ShSellEFDCCancelEvent.Value():                              CommonAction.NewFinishExecution,                  // 取消
	sh_state.ShSellEFDCCancelWithRefundEvent.Value():                    sh_sell.NewCancelRefundExecution,                 // 取消
	sh_state.ShSellEFDCPayEarnestTimeoutEvent.Value():                   CommonAction.NewActionExecution,                  // 支付意向金超时回调
	sh_state.ShSellEFDCPayPOSTimeoutEvent.Value():                       CommonAction.NewActionExecution,                  // 支付pos超时回调，回调时会判断是否有进行中的支付
	sh_state.ShSellEFDCSelectTransferOwnerEvent.Value():                 sh_sell.NewSelectTransferOwnerTypeExecution,      // 选择过户方式
	sh_state.ShSellEFDCTransferCommitmentContractSignStartEvent.Value(): CommonAction.NewUnionContCreateExecution,         // 运营发起签署过户承诺书
	sh_state.ShSellEFDCTransferCommitmentContractSignOverEvent.Value():  callback.NewContCallbackCommonExecution,          // 客户完成签署过户承诺书
	sh_state.ShSellEFDCTransferGuaranteePayStartEvent.Value():           sh_sell.NewPayCashierExecution,                   // 运营发起支付过户保证金
	sh_state.ShSellEFDCTransferGuaranteePayOverEvent.Value():            callback.NewPayCallbackCommonExecution,           // 客户完成支付过户保证金
	sh_state.ShSellEFDCTransferGuaranteePayTimeoutEvent.Value():         CommonAction.NewActionExecution,                  // 支付过户保证金超时
	sh_state.ShSellEFDCDeliveryCarContractSubmitEvent.Value():           CommonAction.NewActionExecution,                  // 运营完成提交交车单
	sh_state.ShSellEFDCDeliveryCarContractSignStartEvent.Value():        CommonAction.NewUnionContCreateExecution,         // 运营发起签署交车单
	sh_state.ShSellEFDCDeliveryCarContractSignOverEvent.Value():         callback.NewContCallbackCommonExecution,          // 客户完成签署交车单
	sh_state.ShSellEFDCTransferGuaranteeRefundStartEvent.Value():        sh_sell.NewRefundTransferOwnerGuaranteeExecution, // 退还过户保证金
	sh_state.ShSellEFDCTransferGuaranteeRefundOverEvent.Value():         CommonAction.NewActionExecution,                  // 退还过户保证金完成
	sh_state.ShSellEFDCCancelRefundStartEt.Value():                      sh_sell.NewUnionRefundExecution,                  // 取消并退款开始
	sh_state.ShSellEFDCCancelRefundFinishEt.Value():                     callback.NewRefundCallbackWithFinishExecution,    // 退款完成
	sh_state.ShSellEFDCDeliveryCarProcessStartEvent.Value():             CommonAction.NewActionExecution,                  // 发起交车
	sh_state.ShSellEFDCConfirmLoanOverEvent.Value():                     sh_sell.NewConfirmLoanOverExecution,              // 确认放贷&分账
	sh_state.ShSellEFDCLoanSubProcessStartEvent.Value():                 sh_sell.NewUpdateLoanSubStatusExecution,          // 发起金融贷款
	sh_state.ShSellEFDCConfirmLoanEvent.Value():                         sh_sell.NewLoanConfirmExecution,                  // 确认贷款
	sh_state.ShSellEFDCApproveLoanPassEvent.Value():                     sh_sell.NewLoanApproveSuccExecution,              // 贷款审批成功
	sh_state.ShSellEFDCApproveLoanFailEvent.Value():                     sh_sell.NewLoanApproveFailExecution,              // 贷款审批拒绝
	sh_state.ShSellEFDCLoanSubProcessOverEvent.Value():                  sh_sell.NewUpdateLoanSubStatusExecution,          // 贷款完成
}

// 二手车 自营卖车 全款 交车模式 1113
var shSellCarFullDCExecution = map[string]Constructor{
	consts.CreateAction: sh_sell.NewCreateOrderExecution, // 创建订单
	sh_state.ShSellFullDCSignSellContractEvent.Value():                    sh_sell.NewCreateContractExecution,               // 创建买卖合同
	sh_state.ShSellFullDCSignSellContractOverEvent.Value():                callback.NewContCallbackCommonExecution,          // 买卖合同签署成功
	sh_state.ShSellFullDCPayMoneyEvent.Value():                            sh_sell.NewPayUnionExecution,                     // 尾款|首付款-POS支付
	sh_state.ShSellFullDCPayMoneyOverEvent.Value():                        sh_sell.NewPayUnionOverExecution,                 // 尾款|首付款-支付完成
	sh_state.ShSellFullDCTransferOwnerEvent.Value():                       sh_sell.NewTransferOwnerExecution,                // 过户
	sh_state.ShSellFullDCSettleOverEvent.Value():                          callback.NewSettleCallbackCommonExecution,        // 结算完成
	sh_state.ShSellFullDCCancelEvent.Value():                              CommonAction.NewFinishExecution,                  // 取消
	sh_state.ShSellFullDCPayPOSTimeoutEvent.Value():                       CommonAction.NewActionExecution,                  // 支付pos超时回调，回调时会判断是否有进行中的支付
	sh_state.ShSellFullDCSelectTransferOwnerEvent.Value():                 sh_sell.NewSelectTransferOwnerTypeExecution,      // 选择过户方式
	sh_state.ShSellFullDCTransferCommitmentContractSignStartEvent.Value(): CommonAction.NewUnionContCreateExecution,         // 运营发起签署过户承诺书
	sh_state.ShSellFullDCTransferCommitmentContractSignOverEvent.Value():  callback.NewContCallbackCommonExecution,          // 客户完成签署过户承诺书
	sh_state.ShSellFullDCTransferGuaranteePayStartEvent.Value():           sh_sell.NewPayUnionExecution,                     // 运营发起支付过户保证金
	sh_state.ShSellFullDCTransferGuaranteePayOverEvent.Value():            callback.NewPayCallbackCommonExecution,           // 客户完成支付过户保证金
	sh_state.ShSellFullDCTransferGuaranteePayTimeoutEvent.Value():         CommonAction.NewActionExecution,                  // 支付过户保证金超时
	sh_state.ShSellFullDCDeliveryCarContractSubmitEvent.Value():           CommonAction.NewActionExecution,                  // 运营完成提交交车单
	sh_state.ShSellFullDCDeliveryCarContractSignStartEvent.Value():        CommonAction.NewUnionContCreateExecution,         // 运营发起签署交车单
	sh_state.ShSellFullDCDeliveryCarContractSignOverEvent.Value():         callback.NewContCallbackCommonExecution,          // 客户完成签署交车单
	sh_state.ShSellFullDCTransferGuaranteeRefundStartEvent.Value():        sh_sell.NewRefundTransferOwnerGuaranteeExecution, // 退还过户保证金
	sh_state.ShSellFullDCTransferGuaranteeRefundOverEvent.Value():         CommonAction.NewActionExecution,                  // 退还过户保证金完成
	sh_state.ShSellFullDCCancelWithRefundStartEt.Value():                  sh_sell.NewUnionRefundExecution,                  // 取消并退款开始
	sh_state.ShSellFullDCCancelWithRefundFinishEt.Value():                 callback.NewRefundCallbackWithFinishExecution,    // 取消并退款开始
	sh_state.ShSellFullDCDeliveryCarProcessStartEvent.Value():             CommonAction.NewActionExecution,                  // 发起交车
	sh_state.ShSellFullDCConfirmLoanOverEvent.Value():                     sh_sell.NewConfirmLoanOverExecution,              // 确认放贷&分账
	sh_state.ShSellFullDCLoanSubProcessStartEvent.Value():                 sh_sell.NewUpdateLoanSubStatusExecution,          // 发起金融贷款
	sh_state.ShSellFullDCConfirmLoanEvent.Value():                         sh_sell.NewLoanConfirmExecution,                  // 确认贷款
	sh_state.ShSellFullDCApproveLoanPassEvent.Value():                     sh_sell.NewLoanApproveSuccExecution,              // 贷款审批成功
	sh_state.ShSellFullDCApproveLoanFailEvent.Value():                     sh_sell.NewLoanApproveFailExecution,              // 贷款审批拒绝
	sh_state.ShSellFullDCLoanSubProcessOverEvent.Value():                  sh_sell.NewUpdateLoanSubStatusExecution,          // 贷款完成
	sh_state.ShSellFullDCRemoteDeliveryCarStartEvent.Value():              sh_sell.NewStartRemoteDeliveryCarExecution,       // 线下交车
}

// 二手车 内网&寄售 定金-尾款 交车模式 1114
var shConsignEFDCExecution = map[string]Constructor{
	consts.CreateAction: sh_sell.NewCreateOrderExecution, // 创建订单
	sh_state.ShConsignEFDCSignIntentContractEvent.Value():                    sh_sell.NewCreateContractExecution,               // 创建意向合同
	sh_state.ShConsignEFDCSignIntentContractOverEvent.Value():                callback.NewContCallbackCommonExecution,          // 意向合同签署成功
	sh_state.ShConsignEFDCPayEarnestEvent.Value():                            sh_sell.NewPayCashierExecution,                   // 意向金-收银台支付
	sh_state.ShConsignEFDCPayEarnestOverEvent.Value():                        callback.NewPayCallbackCommonExecution,           // 意向金-支付完成
	sh_state.ShConsignEFDCRefundEarnestOverEvent.Value():                     callback.NewRefundCallbackWithFinishExecution,    // 退款完成
	sh_state.ShConsignEFDCRefundEarnestOverAfterPOSEvent.Value():             sh_sell.NewRefundOverAfterPOSExecution,           // POS支付后退订金完成
	sh_state.ShConsignEFDCSignSellContractEvent.Value():                      sh_sell.NewCreateContractExecution,               // 创建买卖合同
	sh_state.ShConsignEFDCSignSellContractOverEvent.Value():                  callback.NewContCallbackCommonExecution,          // 买卖合同签署成功
	sh_state.ShConsignEFDCPayFinalEvent.Value():                              sh_sell.NewPayPOSExecution,                       // 尾款|首付款-POS支付
	sh_state.ShConsignEFDCPayFinalOverEvent.Value():                          sh_sell.NewPayPOSOverExecution,                   // 尾款|首付款-支付完成
	sh_state.ShConsignEFDCTransferOwnerEvent.Value():                         sh_sell.NewTransferOwnerExecution,                // 过户
	sh_state.ShConsignEFDCSettleOverEvent.Value():                            callback.NewSettleCallbackCommonExecution,        // 结算完成
	sh_state.ShConsignEFDCCancelEvent.Value():                                CommonAction.NewFinishExecution,                  // 取消
	sh_state.ShConsignEFDCCancelWithRefundEvent.Value():                      sh_sell.NewCancelRefundExecution,                 // 取消with退款
	sh_state.ShConsignEFDCPayPOSTimeoutEvent.Value():                         CommonAction.NewActionExecution,                  // 支付pos超时回调，回调时会判断是否有进行中的支付
	sh_state.ShConsignEFDCSelectTransferOwnerEvent.Value():                   sh_sell.NewSelectTransferOwnerTypeExecution,      // 选择过户方式
	sh_state.ShConsignEFDCMerchantNotificationContractSignStartEvent.Value(): CommonAction.NewUnionContCreateExecution,         // 运营发起签署车商告知书
	sh_state.ShConsignEFDCMerchantNotificationContractSignOverEvent.Value():  callback.NewContCallbackCommonExecution,          // 客户完成签署车商告知书
	sh_state.ShConsignEFDCTransferCommitmentContractSignStartEvent.Value():   CommonAction.NewUnionContCreateExecution,         // 运营发起签署过户承诺书
	sh_state.ShConsignEFDCTransferCommitmentContractSignOverEvent.Value():    callback.NewContCallbackCommonExecution,          // 客户完成签署过户承诺书
	sh_state.ShConsignEFDCTransferGuaranteePayStartEvent.Value():             sh_sell.NewPayCashierExecution,                   // 运营发起支付过户保证金
	sh_state.ShConsignEFDCTransferGuaranteePayOverEvent.Value():              callback.NewPayCallbackCommonExecution,           // 客户完成支付过户保证金
	sh_state.ShConsignEFDCTransferGuaranteePayTimeoutEvent.Value():           callback.NewPayCallbackCommonExecution,           // 客户完成支付过户保证金
	sh_state.ShConsignEFDCDeliveryCarContractSubmitEvent.Value():             CommonAction.NewActionExecution,                  // 运营完成提交交车单
	sh_state.ShConsignEFDCDeliveryCarContractSignStartEvent.Value():          CommonAction.NewUnionContCreateExecution,         // 运营发起签署交车单
	sh_state.ShConsignEFDCDeliveryCarContractSignOverEvent.Value():           callback.NewContCallbackCommonExecution,          // 客户完成签署交车单
	sh_state.ShConsignEFDCTransferGuaranteeRefundStartEvent.Value():          sh_sell.NewRefundTransferOwnerGuaranteeExecution, // 退还过户保证金
	sh_state.ShConsignEFDCTransferGuaranteeRefundOverEvent.Value():           CommonAction.NewActionExecution,                  // 退还过户保证金完成
	sh_state.ShConsignEFDCCancelRefundStartEt.Value():                        sh_sell.NewUnionRefundExecution,                  // 取消并退款开始
	sh_state.ShConsignEFDCCancelRefundFinishEt.Value():                       callback.NewRefundCallbackWithFinishExecution,    // 取消并退款开始
	sh_state.ShConsignEFDCDeliveryCarProcessStartEvent.Value():               CommonAction.NewActionExecution,                  // 发起交车
	sh_state.ShConsignEFDCConfirmLoanOverEvent.Value():                       sh_sell.NewConfirmLoanOverExecution,              // 确认放贷&分账
	sh_state.ShConsignEFDCLoanSubProcessStartEvent.Value():                   sh_sell.NewUpdateLoanSubStatusExecution,          // 发起金融贷款
	sh_state.ShConsignEFDCConfirmLoanEvent.Value():                           sh_sell.NewLoanConfirmExecution,                  // 确认贷款
	sh_state.ShConsignEFDCApproveLoanPassEvent.Value():                       sh_sell.NewLoanApproveSuccExecution,              // 贷款审批成功
	sh_state.ShConsignEFDCApproveLoanFailEvent.Value():                       sh_sell.NewLoanApproveFailExecution,              // 贷款审批拒绝
	sh_state.ShConsignEFDCLoanSubProcessOverEvent.Value():                    sh_sell.NewUpdateLoanSubStatusExecution,          // 贷款完成
}

// 二手车 内网&寄售 全款 交车模式 1115
var shConsignFullDCExecution = map[string]Constructor{
	consts.CreateAction: sh_sell.NewCreateOrderExecution, // 创建订单
	sh_state.ShConsignFullDCSignSellContractEvent.Value():                      sh_sell.NewCreateContractExecution,               // 创建买卖合同
	sh_state.ShConsignFullDCSignSellContractOverEvent.Value():                  callback.NewContCallbackCommonExecution,          // 买卖合同签署成功
	sh_state.ShConsignFullDCPayMoneyEvent.Value():                              sh_sell.NewPayPOSExecution,                       // 尾款|首付款-POS支付
	sh_state.ShConsignFullDCPayMoneyOverEvent.Value():                          sh_sell.NewPayPOSOverExecution,                   // 尾款|首付款-支付完成
	sh_state.ShConsignFullDCTransferOwnerEvent.Value():                         sh_sell.NewTransferOwnerExecution,                // 过户
	sh_state.ShConsignFullDCCancelEvent.Value():                                CommonAction.NewFinishExecution,                  // 取消
	sh_state.ShConsignFullDCSettleOverEvent.Value():                            callback.NewSettleCallbackCommonExecution,        // 结算完成
	sh_state.ShConsignFullDCPayPOSTimeoutEvent.Value():                         CommonAction.NewActionExecution,                  // 支付pos超时回调，回调时会判断是否有进行中的支付
	sh_state.ShConsignFullDCSelectTransferOwnerEvent.Value():                   sh_sell.NewSelectTransferOwnerTypeExecution,      // 选择过户方式
	sh_state.ShConsignFullDCMerchantNotificationContractSignStartEvent.Value(): CommonAction.NewUnionContCreateExecution,         // 运营发起签署车商告知书
	sh_state.ShConsignFullDCMerchantNotificationContractSignOverEvent.Value():  callback.NewContCallbackCommonExecution,          // 客户完成签署车商告知书
	sh_state.ShConsignFullDCTransferCommitmentContractSignStartEvent.Value():   CommonAction.NewUnionContCreateExecution,         // 运营发起签署过户承诺书
	sh_state.ShConsignFullDCTransferCommitmentContractSignOverEvent.Value():    callback.NewContCallbackCommonExecution,          // 客户完成签署过户承诺书
	sh_state.ShConsignFullDCTransferGuaranteePayStartEvent.Value():             sh_sell.NewPayCashierExecution,                   // 运营发起支付过户保证金
	sh_state.ShConsignFullDCTransferGuaranteePayOverEvent.Value():              callback.NewPayCallbackCommonExecution,           // 客户完成支付过户保证金
	sh_state.ShConsignFullDCTransferGuaranteePayTimeoutEvent.Value():           CommonAction.NewActionExecution,                  // 支付过户保证金超时
	sh_state.ShConsignFullDCDeliveryCarContractSubmitEvent.Value():             CommonAction.NewActionExecution,                  // 运营完成提交交车单
	sh_state.ShConsignFullDCDeliveryCarContractSignStartEvent.Value():          CommonAction.NewUnionContCreateExecution,         // 运营发起签署交车单
	sh_state.ShConsignFullDCDeliveryCarContractSignOverEvent.Value():           callback.NewContCallbackCommonExecution,          // 客户完成签署交车单
	sh_state.ShConsignFullDCTransferGuaranteeRefundStartEvent.Value():          sh_sell.NewRefundTransferOwnerGuaranteeExecution, // 退还过户保证金
	sh_state.ShConsignFullDCTransferGuaranteeRefundOverEvent.Value():           CommonAction.NewActionExecution,                  // 退还过户保证金完成
	sh_state.ShConsignFullDCCancelRefundStartEt.Value():                        sh_sell.NewUnionRefundExecution,                  // 取消并发起退款开始
	sh_state.ShConsignFullDCCancelRefundFinishEt.Value():                       callback.NewRefundCallbackWithFinishExecution,    // 退款完成
	sh_state.ShConsignFullDCDeliveryCarProcessStartEvent.Value():               CommonAction.NewActionExecution,                  // 发起交车
	sh_state.ShConsignFullDCConfirmLoanOverEvent.Value():                       sh_sell.NewConfirmLoanOverExecution,              // 确认放贷&分账
	sh_state.ShConsignFullDCLoanSubProcessStartEvent.Value():                   sh_sell.NewUpdateLoanSubStatusExecution,          // 发起金融贷款
	sh_state.ShConsignFullDCConfirmLoanEvent.Value():                           sh_sell.NewLoanConfirmExecution,                  // 确认贷款
	sh_state.ShConsignFullDCApproveLoanPassEvent.Value():                       sh_sell.NewLoanApproveSuccExecution,              // 贷款审批成功
	sh_state.ShConsignFullDCApproveLoanFailEvent.Value():                       sh_sell.NewLoanApproveFailExecution,              // 贷款审批拒绝
	sh_state.ShConsignFullDCLoanSubProcessOverEvent.Value():                    sh_sell.NewUpdateLoanSubStatusExecution,          // 贷款完成
}

// 全国购-订单贷
var purchaseFinanceExecution = map[string]Constructor{
	finance_saas.PFUpdateOrderEt.Value():              CommonAction.NewUpdateOrderStaticExecutionV2,
	finance_saas.PFCreateEt.Value():                   CommonAction.NewCreateOrderExecution,
	finance_saas.PFCarReviewStartEt.Value():           CommonAction.NewUpdateOrderFireExecution,
	finance_saas.PFCarReviewFailEt.Value():            CommonAction.NewUpdateOrderFireExecution,
	finance_saas.PFCarReviewSuccessEt.Value():         CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.PFNoticeContStartEt.Value():          CommonAction.NewContSignExecution,
	finance_saas.PFNoticeContTerminationEt.Value():    CommonAction.NewContTerminationExecution,
	finance_saas.PFNoticeContCallbackEt.Value():       callback.NewContCallbackCommonExecution,
	finance_saas.PFConfirmContStartEt.Value():         CommonAction.NewContSignExecution,
	finance_saas.PFConfirmContTerminationEt.Value():   CommonAction.NewContTerminationExecution,
	finance_saas.PFConfirmContCallbackEt.Value():      callback.NewContCallbackCommonExecution,
	finance_saas.PFFactoringContStartEt.Value():       CommonAction.NewContSignExecution,
	finance_saas.PFFactoringContTerminationEt.Value(): CommonAction.NewContTerminationExecution,
	finance_saas.PFFactoringContCallbackEt.Value():    callback.NewContCallbackCommonExecution,
	finance_saas.PFCreateFinanceEt.Value():            CommonAction.NewCreateFinanceOrderExecution,
	finance_saas.PFWithdrawStartEt.Value():            CommonAction.NewWithdrawExecution,
	finance_saas.PFWithdrawCallbackEt.Value():         callback.NewWithdrawCallbackCommonExecution,
	finance_saas.PFWithdrawFailCallbackEt.Value():     callback.NewWithdrawCallbackCommonExecution,
	finance_saas.PFOrderSuccessEt.Value():             CommonAction.NewUpdateOrderFireExecution,
	finance_saas.PFOrderRejectEt.Value():              CommonAction.NewUpdateOrderFireExecution,
	finance_saas.PFLoanOverPushNationSellEt.Value():   CommonAction.NewUpdateOrderFireExecution,
}

// 自营金融
var selfFinanceExecution = map[string]Constructor{
	finance_saas.SFCreateEt.Value():                          CommonAction.NewCreateOrderExecution,
	finance_saas.SFOrderReviewStartEt.Value():                CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFOrderReviewFailEt.Value():                 CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFOrderReviewSuccessEt.Value():              CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFPBOCContStartEt.Value():                   CommonAction.NewUnionContCreateExecution,
	finance_saas.SFPBOCContTerminationEt.Value():             CommonAction.NewContTerminationExecution,
	finance_saas.SFPBOCContCallbackEt.Value():                callback.NewContCallbackCommonExecution,
	finance_saas.SFPersonInfoContStartEt.Value():             CommonAction.NewUnionContCreateExecution,
	finance_saas.SFPersonInfoContTerminationEt.Value():       CommonAction.NewContTerminationExecution,
	finance_saas.SFPersonInfoContCallbackEt.Value():          callback.NewContCallbackCommonExecution,
	finance_saas.SFGuaranteeContStartEt.Value():              CommonAction.NewUnionContCreateExecution,
	finance_saas.SFGuaranteeContTerminationEt.Value():        CommonAction.NewContTerminationExecution,
	finance_saas.SFGuaranteeContCallbackEt.Value():           callback.NewContCallbackCommonExecution,
	finance_saas.SFFinanceLeaseContStartEt.Value():           CommonAction.NewUnionContCreateExecution,
	finance_saas.SFFinanceLeaseContTerminationEt.Value():     CommonAction.NewContTerminationExecution,
	finance_saas.SFFinanceLeaseContCallbackEt.Value():        callback.NewContCallbackCommonExecution,
	finance_saas.SFLoanReviewStartEt.Value():                 CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFLoanReviewFailEt.Value():                  CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFLoanReviewSuccessEt.Value():               CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFCreateFinanceEt.Value():                   CommonAction.NewCreateFinanceOrderExecution,
	finance_saas.SFWithdrawStartEt.Value():                   CommonAction.NewWithdrawExecution,
	finance_saas.SFWithdrawCallbackEt.Value():                callback.NewWithdrawCallbackCommonExecution,
	finance_saas.SFWithdrawFailCallbackEt.Value():            callback.NewWithdrawCallbackCommonExecution,
	finance_saas.SFOrderSuccessEt.Value():                    CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFOrderCancelEt.Value():                     CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFUpdateOrderEt.Value():                     CommonAction.NewUpdateOrderStaticExecutionV2,
	finance_saas.SFPaymentAgreementSignStartEt.Value():       CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFPaymentAgreementSignTerminationEt.Value(): CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFPaymentAgreementSignFinishEt.Value():      CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFPrepaymentApplyEt.Value():                 CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFPrepaymentContStartEt.Value():             CommonAction.NewUnionContCreateExecution,
	finance_saas.SFPrepaymentContTerminationEt.Value():       CommonAction.NewContTerminationExecution,
	finance_saas.SFPrepaymentContCallbackEt.Value():          callback.NewContCallbackCommonExecution,
	finance_saas.SFPrepaymentFinishEt.Value():                CommonAction.NewUpdateOrderFireExecutionV2,
	finance_saas.SFPrepaymentFailEt.Value():                  CommonAction.NewUpdateOrderFireExecutionV2,
}

// 车源供应链 增票集采
var carSupplyITExecution = map[string]Constructor{
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value():   car_supply.NewUpdateProductExtraExecution,
	supply_state.CarSupplyOrderCreateEt.Value():                car_supply.NewCreateOrderExecution,
	supply_state.CarSupplyPaySmallEarnestAuditEt.Value():       car_supply.NewTransferOnlineAuditExecution,
	supply_state.CarSupplyPaySmallEarnestAuditPassEt.Value():   car_supply.NewTransferOnlineAuditPassExecution,
	supply_state.CarSupplyPaySmallEarnestAuditRejectEt.Value(): car_supply.NewTransferOnlineAuditRejectExecution,
	supply_state.CarSupplyGroupPassEt.Value():                  car_supply.NewCommonExecution,
	supply_state.CarSupplyCancelEt.Value():                     car_supply.NewCancelOrderExecution,
	supply_state.CarSupplyCancelWithRefundEt.Value():           car_supply.NewRefundExecution,
	supply_state.CarSupplyCancelRefundOverEt.Value():           callback.NewRefundCallbackCommonExecution,
	supply_state.CarSupplyBusinessContSignEt.Value():           car_supply.NewContSignExecution,
	supply_state.CarSupplyBusinessContTerminationEt.Value():    CommonAction.NewContTerminationExecution,
	supply_state.CarSupplyBusinessContSignFinishEt.Value():     callback.NewContCallbackCommonExecution,
	supply_state.CarSupplyPayBigEarnestAuditEt.Value():         car_supply.NewTransferOnlineAuditExecution,
	supply_state.CarSupplyPayBigEarnestAuditPassEt.Value():     car_supply.NewTransferOnlineAuditPassExecution,
	supply_state.CarSupplyPayBigEarnestAuditRejectEt.Value():   car_supply.NewTransferOnlineAuditRejectExecution,
	supply_state.CarSupplyDeliverCarOnceEt.Value():             car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value():       car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCatExistFailEt.Value():        car_supply.NewCommonExecution,
	// OA签署
	supply_state.CarSupplyOABusinessContSignEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContTerminationEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContSignFinishEt.Value():  car_supply.NewCommonExecution,

	supply_state.CarSupplyPaySmallEarnestTransferStartEt.Value():   car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPaySmallEarnestTransferFinishEt.Value():  car_supply.NewCommonExecution,
	supply_state.CarSupplyPaySmallEarnestTransferExpiredEt.Value(): car_supply.NewCommonExecution,

	supply_state.CarSupplyPayBigEarnestTransferStartEt.Value():   car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPayBigEarnestTransferFinishEt.Value():  car_supply.NewCommonExecution,
	supply_state.CarSupplyPayBigEarnestTransferExpiredEt.Value(): car_supply.NewCommonExecution,

	supply_state.CarSupplyBusinessContSignJumpEt.Value():      car_supply.NewCommonExecution,
	supply_state.CarSupplyPayBigEarnestTransferJumpEt.Value(): car_supply.NewCommonExecution,
	// end
}

// 车源供应链 客票集采
var carSupplyPTExecution = map[string]Constructor{
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value():   car_supply.NewUpdateProductExtraExecution,
	supply_state.CarSupplyOrderCreateEt.Value():                car_supply.NewCreateOrderExecution,
	supply_state.CarSupplyPaySmallEarnestAuditEt.Value():       car_supply.NewTransferOnlineAuditExecution,
	supply_state.CarSupplyPaySmallEarnestAuditPassEt.Value():   car_supply.NewTransferOnlineAuditPassExecution,
	supply_state.CarSupplyPaySmallEarnestAuditRejectEt.Value(): car_supply.NewTransferOnlineAuditRejectExecution,
	supply_state.CarSupplyPaySmallEarnestEt.Value():            car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPaySmallEarnestExpiredEt.Value():     car_supply.NewCommonExecution,
	supply_state.CarSupplyPaySmallEarnestFinishEt.Value():      car_supply.NewCommonExecution,
	supply_state.CarSupplyGroupPassEt.Value():                  car_supply.NewCommonExecution,
	supply_state.CarSupplyInsuranceContSignEt.Value():          car_supply.NewContSignExecution,
	supply_state.CarSupplyInsuranceContTerminationEt.Value():   CommonAction.NewContTerminationExecution,
	supply_state.CarSupplyInsuranceContSignFinishEt.Value():    callback.NewContCallbackCommonExecution,
	supply_state.CarSupplyPayGuaranteeAuditEt.Value():          car_supply.NewTransferOnlineAuditExecution,
	supply_state.CarSupplyPayGuaranteeAuditPassEt.Value():      car_supply.NewTransferOnlineAuditPassExecution,
	supply_state.CarSupplyPayGuaranteeAuditRejectEt.Value():    car_supply.NewTransferOnlineAuditRejectExecution,
	supply_state.CarSupplyPayGuaranteeEt.Value():               car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPayGuaranteeExpiredEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyPayGuaranteeFinishEt.Value():         car_supply.NewCommonExecution,
	supply_state.CarSupplyCancelEt.Value():                     car_supply.NewCancelOrderExecution,
	supply_state.CarSupplyCancelWithRefundEt.Value():           car_supply.NewRefundExecution,
	supply_state.CarSupplyBusinessContSignEt.Value():           car_supply.NewContSignExecution,
	supply_state.CarSupplyBusinessContTerminationEt.Value():    CommonAction.NewContTerminationExecution,
	supply_state.CarSupplyBusinessContSignFinishEt.Value():     callback.NewContCallbackCommonExecution,
	supply_state.CarSupplyDeliverCarOnceEt.Value():             car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value():       car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCatExistFailEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyCancelRefundOverEt.Value():           callback.NewRefundCallbackCommonExecution,
	// OA签署
	supply_state.CarSupplyOAInsuranceContSignEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyOAInsuranceContTerminationEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyOAInsuranceContSignFinishEt.Value():  car_supply.NewCommonExecution,

	supply_state.CarSupplyOABusinessContSignEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContTerminationEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContSignFinishEt.Value():  car_supply.NewCommonExecution,

	supply_state.CarSupplyPaySmallEarnestTransferStartEt.Value():   car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPaySmallEarnestTransferFinishEt.Value():  car_supply.NewCommonExecution,
	supply_state.CarSupplyPaySmallEarnestTransferExpiredEt.Value(): car_supply.NewCommonExecution,

	supply_state.CarSupplyPayGuaranteeTransferStartEt.Value():   car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPayGuaranteeTransferFinishEt.Value():  car_supply.NewCommonExecution,
	supply_state.CarSupplyPayGuaranteeTransferExpiredEt.Value(): car_supply.NewCommonExecution,

	// end
}

// 车源供应链 加盟店 增票集采
var carSupplyITFranchiseExecution = map[string]Constructor{
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value():   car_supply.NewUpdateProductExtraExecution,
	supply_state.CarSupplyOrderCreateEt.Value():                car_supply.NewCreateOrderExecution,
	supply_state.CarSupplyPaySmallEarnestAuditEt.Value():       car_supply.NewTransferOnlineAuditExecution,
	supply_state.CarSupplyPaySmallEarnestAuditPassEt.Value():   car_supply.NewTransferOnlineAuditPassExecution,
	supply_state.CarSupplyPaySmallEarnestAuditRejectEt.Value(): car_supply.NewTransferOnlineAuditRejectExecution,
	supply_state.CarSupplyGroupPassEt.Value():                  car_supply.NewCommonExecution,
	supply_state.CarSupplyCancelEt.Value():                     car_supply.NewCancelOrderExecution,
	supply_state.CarSupplyCancelWithRefundEt.Value():           car_supply.NewRefundExecution,
	supply_state.CarSupplyCancelRefundOverEt.Value():           callback.NewRefundCallbackCommonExecution,
	supply_state.CarSupplyBusinessContSignEt.Value():           car_supply.NewContSignExecution,
	supply_state.CarSupplyBusinessContTerminationEt.Value():    CommonAction.NewContTerminationExecution,
	supply_state.CarSupplyBusinessContSignFinishEt.Value():     callback.NewContCallbackCommonExecution,
	supply_state.CarSupplyPayBigEarnestAuditEt.Value():         car_supply.NewTransferOnlineAuditExecution,
	supply_state.CarSupplyPayBigEarnestAuditPassEt.Value():     car_supply.NewTransferOnlineAuditPassExecution,
	supply_state.CarSupplyPayBigEarnestAuditRejectEt.Value():   car_supply.NewTransferOnlineAuditRejectExecution,
	supply_state.CarSupplyDeliverCarOnceEt.Value():             car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value():       car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCatExistFailEt.Value():        car_supply.NewCommonExecution,

	supply_state.CarSupplyPaySmallEarnestTransferStartEt.Value():   car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPaySmallEarnestTransferFinishEt.Value():  car_supply.NewCommonExecution,
	supply_state.CarSupplyPaySmallEarnestTransferExpiredEt.Value(): car_supply.NewCommonExecution,

	supply_state.CarSupplyPayBigEarnestTransferStartEt.Value():   car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPayBigEarnestTransferFinishEt.Value():  car_supply.NewCommonExecution,
	supply_state.CarSupplyPayBigEarnestTransferExpiredEt.Value(): car_supply.NewCommonExecution,

	// end
}

// 车源供应链 加盟店客票集采
var carSupplyPTFranchiseExecution = map[string]Constructor{
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value():   car_supply.NewUpdateProductExtraExecution,
	supply_state.CarSupplyOrderCreateEt.Value():                car_supply.NewCreateOrderExecution,
	supply_state.CarSupplyPaySmallEarnestAuditEt.Value():       car_supply.NewTransferOnlineAuditExecution,
	supply_state.CarSupplyPaySmallEarnestAuditPassEt.Value():   car_supply.NewTransferOnlineAuditPassExecution,
	supply_state.CarSupplyPaySmallEarnestAuditRejectEt.Value(): car_supply.NewTransferOnlineAuditRejectExecution,
	supply_state.CarSupplyPaySmallEarnestEt.Value():            car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPaySmallEarnestExpiredEt.Value():     car_supply.NewCommonExecution,
	supply_state.CarSupplyPaySmallEarnestFinishEt.Value():      car_supply.NewCommonExecution,
	supply_state.CarSupplyGroupPassEt.Value():                  car_supply.NewCommonExecution,
	supply_state.CarSupplyInsuranceContSignEt.Value():          car_supply.NewContSignExecution,
	supply_state.CarSupplyInsuranceContTerminationEt.Value():   CommonAction.NewContTerminationExecution,
	supply_state.CarSupplyInsuranceContSignFinishEt.Value():    callback.NewContCallbackCommonExecution,
	supply_state.CarSupplyPayGuaranteeAuditEt.Value():          car_supply.NewTransferOnlineAuditExecution,
	supply_state.CarSupplyPayGuaranteeAuditPassEt.Value():      car_supply.NewTransferOnlineAuditPassExecution,
	supply_state.CarSupplyPayGuaranteeAuditRejectEt.Value():    car_supply.NewTransferOnlineAuditRejectExecution,
	supply_state.CarSupplyPayGuaranteeEt.Value():               car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPayGuaranteeExpiredEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyPayGuaranteeFinishEt.Value():         car_supply.NewCommonExecution,
	supply_state.CarSupplyCancelEt.Value():                     car_supply.NewCancelOrderExecution,
	supply_state.CarSupplyCancelWithRefundEt.Value():           car_supply.NewRefundExecution,
	supply_state.CarSupplyBusinessContSignEt.Value():           car_supply.NewContSignExecution,
	supply_state.CarSupplyBusinessContTerminationEt.Value():    CommonAction.NewContTerminationExecution,
	supply_state.CarSupplyBusinessContSignFinishEt.Value():     callback.NewContCallbackCommonExecution,
	supply_state.CarSupplyDeliverCarOnceEt.Value():             car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value():       car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCatExistFailEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyCancelRefundOverEt.Value():           callback.NewRefundCallbackCommonExecution,

	supply_state.CarSupplyPaySmallEarnestTransferStartEt.Value():   car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPaySmallEarnestTransferFinishEt.Value():  car_supply.NewCommonExecution,
	supply_state.CarSupplyPaySmallEarnestTransferExpiredEt.Value(): car_supply.NewCommonExecution,

	supply_state.CarSupplyPayGuaranteeTransferStartEt.Value():   car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPayGuaranteeTransferFinishEt.Value():  car_supply.NewCommonExecution,
	supply_state.CarSupplyPayGuaranteeTransferExpiredEt.Value(): car_supply.NewCommonExecution,

	// end
}

// 车源供应链 门店增票
var carSupplyIncreaseTicketNCExecution = map[string]Constructor{
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value():      car_supply.NewUpdateProductExtraExecution,
	supply_state.CarSupplyOrderCreateEt.Value():                   car_supply.NewCreateOrderExecution,
	supply_state.CarSupplyBusinessContSignEt.Value():              car_supply.NewContSignExecution,
	supply_state.CarSupplyBusinessContTerminationEt.Value():       CommonAction.NewContTerminationExecution,
	supply_state.CarSupplyBusinessContSignFinishEt.Value():        callback.NewContCallbackCommonExecution,
	supply_state.CarSupplyWithdrawBigEarnestAuditEt.Value():       car_supply.NewCommonExecution,
	supply_state.CarSupplyWithdrawBigEarnestAuditRejectEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyWithdrawBigEarnestAuditPassEt.Value():   car_supply.NewCommonExecution,
	supply_state.CarSupplyWithdrawBigEarnestStartEt.Value():       CommonAction.NewWithdrawExecution,
	supply_state.CarSupplyWithdrawBigEarnestFailedEt.Value():      callback.NewWithdrawCallbackCommonExecution,
	supply_state.CarSupplyWithdrawBigEarnestSuccessEt.Value():     callback.NewWithdrawCallbackCommonExecution,
	supply_state.CarSupplyDeliverCarOnceEt.Value():                car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value():          car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCatExistFailEt.Value():           car_supply.NewCommonExecution,
	supply_state.CarSupplyCancelEt.Value():                        car_supply.NewCancelOrderExecution,
}

// 车源供应链 门店客票
var carSupplyPassengerTicketNCExecution = map[string]Constructor{
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,
	supply_state.CarSupplyOrderCreateEt.Value():              car_supply.NewCreateOrderExecution,
	supply_state.CarSupplyBusinessContSignEt.Value():         car_supply.NewContSignExecution,
	supply_state.CarSupplyBusinessContTerminationEt.Value():  CommonAction.NewContTerminationExecution,
	supply_state.CarSupplyBusinessContSignFinishEt.Value():   callback.NewContCallbackCommonExecution,
	supply_state.CarSupplyDeliverCarOnceEt.Value():           car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value():     car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCatExistFailEt.Value():      car_supply.NewCommonExecution,
	supply_state.CarSupplyCancelEt.Value():                   car_supply.NewCancelOrderExecution,
}

// 车源供应链 我司我司
var carSupplyInnerExecution = map[string]Constructor{
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,
	supply_state.CarSupplyOrderCreateEt.Value():              car_supply.NewCreateOrderExecution,
	supply_state.CarSupplyDeliverCarOnceEt.Value():           car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value():     car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCatExistFailEt.Value():      car_supply.NewCommonExecution,
	supply_state.CarSupplyCancelEt.Value():                   car_supply.NewCancelOrderExecution,
}

// carSupplyFinanceExecution 车源供应链 金融
var carSupplyFinanceExecution = map[string]Constructor{
	// start

	// 创单
	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,
	// 更新信息
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	// 合同相关
	supply_state.CarSupplyBusinessContSignEt.Value():        car_supply.NewContSignExecution,          // 签署买卖合同
	supply_state.CarSupplyBusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution, // 作废买卖合同
	supply_state.CarSupplyBusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,  // 买卖合同签署完成
	// OA签署
	supply_state.CarSupplyOABusinessContSignEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContTerminationEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContSignFinishEt.Value():  car_supply.NewCommonExecution,

	// 大定相关
	supply_state.CarSupplyPayBigEarnestTransferStartEt.Value():   car_supply.NewCashierPayExecution, // 大订对公转账发起
	supply_state.CarSupplyPayBigEarnestTransferFinishEt.Value():  car_supply.NewCommonExecution,     // 大订对公转账完成
	supply_state.CarSupplyPayBigEarnestTransferExpiredEt.Value(): car_supply.NewCommonExecution,     // 大订对公转账超时

	// 交付相关
	supply_state.CarSupplyDeliverCarOnceEt.Value():       car_supply.NewCommonExecution, // 完成一笔交付
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value(): car_supply.NewCommonExecution, // 交付成功 全部成功
	supply_state.CarSupplyDeliverCatExistFailEt.Value():  car_supply.NewCommonExecution, // 交付完成 存在异常交付

	// 订单取消相关
	supply_state.CarSupplyCancelWithRefundEt.Value(): car_supply.NewRefundExecution,             // 取消订单并退款
	supply_state.CarSupplyCancelRefundOverEt.Value(): callback.NewRefundCallbackCommonExecution, // 退款完成
	supply_state.CarSupplyCancelEt.Value():           car_supply.NewCancelOrderExecution,        // 取消订单

	// Jump相关
	supply_state.CarSupplyBusinessContSignJumpEt.Value():      car_supply.NewCommonExecution, // 买卖合同跳过
	supply_state.CarSupplyPayBigEarnestTransferJumpEt.Value(): car_supply.NewCommonExecution, // 大订对公转账跳过
	// 	end
}

// carSupplySecuredTransactionExecution 车源供应链 普通担保
var carSupplySecuredTransactionExecution = map[string]Constructor{
	// start

	// 创单
	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,
	// 更新信息
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	// 小订相关
	supply_state.CarSupplyPaySmallEarnestEt.Value():        car_supply.NewCashierPayExecution, // 大订发起
	supply_state.CarSupplyPaySmallEarnestExpiredEt.Value(): car_supply.NewCommonExecution,     // 大订支付超时
	supply_state.CarSupplyPaySmallEarnestFinishEt.Value():  car_supply.NewCommonExecution,     // 大订完成
	supply_state.CarSupplyPaySmallEarnestJumpEt.Value():    car_supply.NewCommonExecution,     // 跳过小订

	// 合同相关
	supply_state.CarSupplyBusinessContSignEt.Value():        car_supply.NewContSignExecution,          // 签署买卖合同
	supply_state.CarSupplyBusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution, // 作废买卖合同
	supply_state.CarSupplyBusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,  // 买卖合同签署完成
	// OA签署
	supply_state.CarSupplyOABusinessContSignEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContTerminationEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContSignFinishEt.Value():  car_supply.NewCommonExecution,

	// 定金代付相关
	supply_state.CarSupplyAgentPayEarnestStartEt.Value():   CommonAction.NewWithdrawExecution,           // 定金代付发起
	supply_state.CarSupplyAgentPayEarnestFailedEt.Value():  callback.NewWithdrawCallbackCommonExecution, // 定金代付失败
	supply_state.CarSupplyAgentPayEarnestSuccessEt.Value(): callback.NewWithdrawCallbackCommonExecution, // 定金代付成功
	supply_state.CarSupplyAgentPayEarnestJumpEt.Value():    car_supply.NewCommonExecution,               // 跳过定金代付

	// 定金退款相关
	supply_state.CarSupplyRefundEarnestStartEt.Value():   car_supply.NewNormalRefundExecution,       // 定金退款发起
	supply_state.CarSupplyRefundEarnestFailedEt.Value():  callback.NewRefundCallbackCommonExecution, // 定金退款失败
	supply_state.CarSupplyRefundEarnestSuccessEt.Value(): callback.NewRefundCallbackCommonExecution, // 定金退款成功
	supply_state.CarSupplyRefundEarnestJumpEt.Value():    car_supply.NewCommonExecution,             // 定金退款跳过

	// 交付相关
	supply_state.CarSupplyDeliverCarOnceEt.Value():       car_supply.NewCommonExecution, // 完成一笔交付
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value(): car_supply.NewCommonExecution, // 交付成功 全部成功
	supply_state.CarSupplyDeliverCatExistFailEt.Value():  car_supply.NewCommonExecution, // 交付完成 存在异常交付

	supply_state.CarSupplyPrepareCarWaitCompleteEt.Value(): car_supply.NewCommonExecution, // 备车完成
	supply_state.CarSupplyAutoFinishOrderEt.Value():        car_supply.NewCommonExecution, // 自动完成（关单）

	// 订单取消相关
	supply_state.CarSupplyCancelStartEt.Value():      car_supply.NewCommonExecution,             // 订单取消发起
	supply_state.CarSupplyCancelFailedEt.Value():     car_supply.NewCommonExecution,             // 订单取消失败
	supply_state.CarSupplyCancelWithRefundEt.Value(): car_supply.NewRefundExecution,             // 取消订单并退款
	supply_state.CarSupplyCancelRefundOverEt.Value(): callback.NewRefundCallbackCommonExecution, // 退款完成
	supply_state.CarSupplyCancelEt.Value():           car_supply.NewCancelOrderExecution,        // 取消订单

	// 	end
}

// carSupplySecuredTransactionNCExecution 车源供应链 门店担保
var carSupplySecuredTransactionNCExecution = map[string]Constructor{
	// start
	// 创单
	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,
	// 更新信息
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	// 小订相关
	supply_state.CarSupplyWithdrawSmallEarnestAuditEt.Value():       car_supply.NewCommonExecution,               // 小订出款审核发起
	supply_state.CarSupplyWithdrawSmallEarnestAuditRejectEt.Value(): car_supply.NewCommonExecution,               // 小订出款审核失败
	supply_state.CarSupplyWithdrawSmallEarnestAuditPassEt.Value():   car_supply.NewCommonExecution,               // 小订出款审核通过
	supply_state.CarSupplyWithdrawSmallEarnestStartEt.Value():       car_supply.NewWithdrawExecution,             // 小订出款发起
	supply_state.CarSupplyWithdrawSmallEarnestSuccessEt.Value():     callback.NewWithdrawCallbackCommonExecution, // 小订出款完成
	supply_state.CarSupplyWithdrawSmallEarnestFailedEt.Value():      callback.NewWithdrawCallbackCommonExecution, // 小订出款失败
	supply_state.CarSupplyWithdrawSmallEarnestJumpEt.Value():        car_supply.NewCommonExecution,               // 跳过小订

	// 合同相关
	supply_state.CarSupplyBusinessContSignEt.Value():        car_supply.NewContSignExecution,          // 签署买卖合同
	supply_state.CarSupplyBusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution, // 作废买卖合同
	supply_state.CarSupplyBusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,  // 买卖合同签署完成

	// 定金代付相关
	supply_state.CarSupplyAgentPayEarnestStartEt.Value():   CommonAction.NewWithdrawExecution,           // 定金代付发起
	supply_state.CarSupplyAgentPayEarnestFailedEt.Value():  callback.NewWithdrawCallbackCommonExecution, // 定金代付失败
	supply_state.CarSupplyAgentPayEarnestSuccessEt.Value(): callback.NewWithdrawCallbackCommonExecution, // 定金代付成功
	supply_state.CarSupplyAgentPayEarnestJumpEt.Value():    car_supply.NewCommonExecution,               // 跳过定金代付

	// 定金退款相关
	supply_state.CarSupplyRefundEarnestJumpEt.Value(): car_supply.NewCommonExecution, // 定金退款跳过

	// 交付相关
	supply_state.CarSupplyDeliverCarOnceEt.Value():       car_supply.NewCommonExecution, // 完成一笔交付
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value(): car_supply.NewCommonExecution, // 交付成功 全部成功
	supply_state.CarSupplyDeliverCatExistFailEt.Value():  car_supply.NewCommonExecution, // 交付完成 存在异常交付

	// 订单取消相关
	supply_state.CarSupplyCancelStartEt.Value():      car_supply.NewCommonExecution,             // 订单取消发起
	supply_state.CarSupplyCancelFailedEt.Value():     car_supply.NewCommonExecution,             // 订单取消失败
	supply_state.CarSupplyCancelWithRefundEt.Value(): car_supply.NewRefundExecution,             // 取消订单并退款
	supply_state.CarSupplyCancelRefundOverEt.Value(): callback.NewRefundCallbackCommonExecution, // 退款完成
	supply_state.CarSupplyCancelEt.Value():           car_supply.NewCancelOrderExecution,        // 取消订单
	// end
}

// carSupplyExhibitionSecuredTransactionNCExecution 车源供应链 门店担保展车
var carSupplySTExhibitionNCExecution = map[string]Constructor{
	// start
	// 创单
	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,
	// 更新信息
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	// 保证金
	supply_state.CarSupplyWithdrawGuaranteeAuditEt.Value():       car_supply.NewCommonExecution,               // 保证金审核发起
	supply_state.CarSupplyWithdrawGuaranteeAuditRejectEt.Value(): car_supply.NewCommonExecution,               // 保证金审核失败
	supply_state.CarSupplyWithdrawGuaranteeAuditPassEt.Value():   car_supply.NewCommonExecution,               // 保证金审核通过
	supply_state.CarSupplyWithdrawGuaranteeStartEt.Value():       CommonAction.NewWithdrawExecution,           // 保证金出款发起
	supply_state.CarSupplyWithdrawGuaranteeSuccessEt.Value():     callback.NewWithdrawCallbackCommonExecution, // 保证金出款完成
	supply_state.CarSupplyWithdrawGuaranteeFailedEt.Value():      callback.NewWithdrawCallbackCommonExecution, // 保证金出款失败

	// 合同相关
	supply_state.CarSupplyExhibitionCarContSignEt.Value():              car_supply.NewContSignExecution,          // 签署展车协议
	supply_state.CarSupplyExhibitionCarContTerminationEt.Value():       CommonAction.NewContTerminationExecution, // 作废展车协议
	supply_state.CarSupplyExhibitionCarContFinishEt.Value():            callback.NewContCallbackCommonExecution,  // 展车协议签署完成
	supply_state.CarSupplyExhibitionCarBorrowContSignEt.Value():        car_supply.NewContSignExecution,          // 签署借车单
	supply_state.CarSupplyExhibitionCarBorrowContTerminationEt.Value(): CommonAction.NewContTerminationExecution, // 作废借车单
	supply_state.CarSupplyExhibitionCarBorrowContFinishEt.Value():      callback.NewContCallbackCommonExecution,  // 借车单签署完成

	// 备车
	supply_state.CarSupplyPrepareCarWaitCompleteEt.Value(): car_supply.NewCommonExecution,

	// 交付相关
	supply_state.CarSupplyDeliverCarOnceEt.Value():       car_supply.NewCommonExecution, // 完成一笔交付
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value(): car_supply.NewCommonExecution, // 交付成功 全部成功
	supply_state.CarSupplyDeliverCatExistFailEt.Value():  car_supply.NewCommonExecution, // 交付完成 存在异常交付

	// 订单取消相关
	supply_state.CarSupplyCancelEt.Value(): car_supply.NewCancelOrderExecution, // 取消订单

	// Jump相关
	supply_state.CarSupplyExhibitionCarContJumpEt.Value():       car_supply.NewCommonExecution, // 展车协议跳过
	supply_state.CarSupplyWithdrawGuaranteeJumpEt.Value():       car_supply.NewCommonExecution, // 保证金出款跳过
	supply_state.CarSupplyExhibitionCarBorrowContJumpEt.Value(): car_supply.NewCommonExecution, // 借车单协议跳过

	// end
}

// carSupplyFinanceMatchExecution 车源供应链 金融撮合
var carSupplyFinanceMatchExecution = map[string]Constructor{
	// 创单
	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,
	// 更新信息
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	// 服务费相关
	supply_state.CarSupplyPayServiceFeeEt.Value():        car_supply.NewCashierPayExecution, // 服务费发起
	supply_state.CarSupplyPayServiceFeeExpiredEt.Value(): car_supply.NewCommonExecution,     // 服务费支付超时
	supply_state.CarSupplyPayServiceFeeFinishEt.Value():  car_supply.NewCommonExecution,     // 服务费支付完成

	// 合同相关
	supply_state.CarSupplyBusinessContSignEt.Value():        car_supply.NewContSignExecution,          // 签署买卖合同
	supply_state.CarSupplyBusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution, // 作废买卖合同
	supply_state.CarSupplyBusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,  // 买卖合同签署完成

	supply_state.CarSupplyOpCloseOrderEt.Value(): car_supply.NewCommonExecution,

	// 订单取消相关
	supply_state.CarSupplyCancelWithRefundEt.Value(): car_supply.NewRefundExecution,             // 取消订单并退款
	supply_state.CarSupplyCancelRefundOverEt.Value(): callback.NewRefundCallbackCommonExecution, // 退款完成
	supply_state.CarSupplyCancelEt.Value():           car_supply.NewCancelOrderExecution,        // 取消订单
}

// carSupplyEconSTExecution 车源供应链 电商担保
var carSupplyEcomSTExecution = map[string]Constructor{
	// 创单
	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,
	// 更新信息
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	// 服务费相关
	supply_state.CarSupplyPayServiceFeeJumpEt.Value(): car_supply.NewCommonExecution, // 服务费跳过

	// 合同相关
	supply_state.CarSupplyBusinessContSignEt.Value():        car_supply.NewContSignExecution,          // 签署买卖合同
	supply_state.CarSupplyBusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution, // 作废买卖合同
	supply_state.CarSupplyBusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,  // 买卖合同签署完成成

	// 自动完成
	supply_state.CarSupplyAutoFinishOrderEt.Value(): car_supply.NewCommonExecution,

	// 订单取消相关
	supply_state.CarSupplyCancelEt.Value(): car_supply.NewCancelOrderExecution, // 取消订单
}

// 新车电商
var ncEcomExecution = map[string]Constructor{
	nc_state.NCEcomOrderCreateEt.Value():  nc_ecom.NewCreateOrderExecution,
	nc_state.NCEcomPayStartEt.Value():     CommonAction.NewGuaranteePayExecution,
	nc_state.NCEcomPayTimeoutEt.Value():   callback.NewPayCallbackCommonExecution,
	nc_state.NCEcomPayFinishEt.Value():    callback.NewPayCallbackCommonExecution,
	nc_state.NCEcomRefundStartEt.Value():  CommonAction.NewRefundExecution,
	nc_state.NCEcomRefundFinishEt.Value(): callback.NewRefundCallbackCommonExecution,
	nc_state.NCEcomOrderCancelEt.Value():  CommonAction.NewUpdateOrderFireExecution,
}

// carSupplyPreOrderExecution 车源供应链 预订单
var carSupplyPreOrderExecution = map[string]Constructor{
	// 创单
	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,
	// 更新信息
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	// 意向金相关
	supply_state.CarSupplyPaySmallEarnestEt.Value():        car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPaySmallEarnestExpiredEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyPaySmallEarnestFinishEt.Value():  car_supply.NewCommonExecution,

	// 意向金退款相关
	supply_state.CarSupplyRefundSmallEarnestStartEt.Value():   car_supply.NewNormalRefundExecution,
	supply_state.CarSupplyRefundSmallEarnestFailedEt.Value():  callback.NewRefundCallbackCommonExecution,
	supply_state.CarSupplyRefundSmallEarnestSuccessEt.Value(): callback.NewRefundCallbackCommonExecution,
	supply_state.CarSupplyRefundSmallEarnestJumpEt.Value():    car_supply.NewCommonExecution,

	// 出价
	supply_state.CarSupplyMerchantBidEt.Value(): car_supply.NewCommonExecution, // 修改出价
	supply_state.CarSupplyAuctionOverEt.Value(): car_supply.NewCommonExecution, // 取消其他订单，中标订单变成待评估

	// 截止
	supply_state.CarSupplyBidSuccessfulEt.Value(): car_supply.NewCommonExecution, // 中标订单变成待成交

	// 未中标
	supply_state.CarSupplyBidFailedEt.Value():       car_supply.NewRefundCloseExecution,        // 关闭并退款
	supply_state.CarSupplyCloseRefundOverEt.Value(): callback.NewRefundCallbackCommonExecution, // 退款完成

	// 中标
	supply_state.CarSupplyAllCompleteEt.Value(): car_supply.NewCommonExecution, // 变成待成交

	// 订单取消相关
	supply_state.CarSupplyCancelWithRefundEt.Value(): car_supply.NewRefundExecution,             // 取消订单并退款
	supply_state.CarSupplyCancelRefundOverEt.Value(): callback.NewRefundCallbackCommonExecution, // 退款完成
	supply_state.CarSupplyCancelEt.Value():           car_supply.NewCancelOrderExecution,        // 取消订单
}

// carSupplyClearanceAuctionExecution 车源供应链 斩仓拍卖
var carSupplyClearanceAuctionExecution = map[string]Constructor{
	// 创单
	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,
	// 更新信息
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	// 合同相关
	supply_state.CarSupplyBusinessContSignEt.Value():        car_supply.NewContSignExecution,          // 签署买卖合同
	supply_state.CarSupplyBusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution, // 作废买卖合同
	supply_state.CarSupplyBusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,  // 买卖合同签署完成
	// OA签署
	supply_state.CarSupplyOABusinessContSignEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContTerminationEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContSignFinishEt.Value():  car_supply.NewCommonExecution,

	// 备车
	supply_state.CarSupplyPrepareCarWaitCompleteEt.Value(): car_supply.NewCommonExecution,

	supply_state.CarSupplyBusinessContSignJumpEt.Value(): car_supply.NewCommonExecution,

	// 交付相关
	supply_state.CarSupplyDeliverCarOnceEt.Value():       car_supply.NewCommonExecution, // 完成一笔交付
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value(): car_supply.NewCommonExecution, // 交付成功 全部成功
	supply_state.CarSupplyDeliverCatExistFailEt.Value():  car_supply.NewCommonExecution, // 交付完成 存在异常交付

	// 订单取消相关
	supply_state.CarSupplyCancelEt.Value(): car_supply.NewCancelOrderExecution, // 取消订单
}

// carSupplyTPAPPassengerTicketExecution 车源供应链 三方代采-普通客票
var carSupplyTPAPPassengerTicketExecution = map[string]Constructor{
	// 创单
	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,
	// 更新信息
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	// 小订支付
	supply_state.CarSupplyPaySmallEarnestEt.Value():        car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPaySmallEarnestExpiredEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyPaySmallEarnestFinishEt.Value():  car_supply.NewCommonExecution,
	supply_state.CarSupplyPaySmallEarnestJumpEt.Value():    car_supply.NewCommonExecution, // 跳过小订

	// 合同相关
	supply_state.CarSupplyBusinessContSignEt.Value():        car_supply.NewContSignExecution,          // 签署买卖合同
	supply_state.CarSupplyBusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution, // 作废买卖合同
	supply_state.CarSupplyBusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,  // 买卖合同签署完成

	// 定金代付相关
	supply_state.CarSupplyAgentPayEarnestStartEt.Value():   CommonAction.NewWithdrawExecution,           // 定金代付发起
	supply_state.CarSupplyAgentPayEarnestFailedEt.Value():  callback.NewWithdrawCallbackCommonExecution, // 定金代付失败
	supply_state.CarSupplyAgentPayEarnestSuccessEt.Value(): callback.NewWithdrawCallbackCommonExecution, // 定金代付成功
	supply_state.CarSupplyAgentPayEarnestJumpEt.Value():    car_supply.NewCommonExecution,               // 跳过定金代付

	// 备车
	supply_state.CarSupplyPrepareCarWaitCompleteEt.Value(): car_supply.NewCommonExecution,

	supply_state.CarSupplyBusinessContSignJumpEt.Value(): car_supply.NewCommonExecution,

	// 交付相关
	supply_state.CarSupplyDeliverCarOnceEt.Value():       car_supply.NewCommonExecution, // 完成一笔交付
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value(): car_supply.NewCommonExecution, // 交付成功 全部成功
	supply_state.CarSupplyDeliverCatExistFailEt.Value():  car_supply.NewCommonExecution, // 交付完成 存在异常交付

	// 订单取消相关
	supply_state.CarSupplyCancelWithRefundEt.Value(): car_supply.NewRefundExecution,             // 取消订单并退款
	supply_state.CarSupplyCancelRefundOverEt.Value(): callback.NewRefundCallbackCommonExecution, // 退款完成
	supply_state.CarSupplyCancelEt.Value():           car_supply.NewCancelOrderExecution,        // 取消订单
}

// carSupplyTPAPPassengerTicketNCExecution 车源供应链 三方代采-门店客票
var carSupplyTPAPPassengerTicketNCExecution = map[string]Constructor{
	// 创单
	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,
	// 更新信息
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	// 小订相关
	supply_state.CarSupplyWithdrawSmallEarnestAuditEt.Value():       car_supply.NewCommonExecution,               // 小订出款审核发起
	supply_state.CarSupplyWithdrawSmallEarnestAuditRejectEt.Value(): car_supply.NewCommonExecution,               // 小订出款审核失败
	supply_state.CarSupplyWithdrawSmallEarnestAuditPassEt.Value():   car_supply.NewCommonExecution,               // 小订出款审核通过
	supply_state.CarSupplyWithdrawSmallEarnestStartEt.Value():       car_supply.NewWithdrawExecution,             // 小订出款发起
	supply_state.CarSupplyWithdrawSmallEarnestSuccessEt.Value():     callback.NewWithdrawCallbackCommonExecution, // 小订出款完成
	supply_state.CarSupplyWithdrawSmallEarnestFailedEt.Value():      callback.NewWithdrawCallbackCommonExecution, // 小订出款失败
	supply_state.CarSupplyWithdrawSmallEarnestJumpEt.Value():        car_supply.NewCommonExecution,               // 跳过小订

	// 合同相关
	supply_state.CarSupplyBusinessContSignEt.Value():        car_supply.NewContSignExecution,          // 签署买卖合同
	supply_state.CarSupplyBusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution, // 作废买卖合同
	supply_state.CarSupplyBusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,  // 买卖合同签署完成

	// 定金代付相关
	supply_state.CarSupplyAgentPayEarnestStartEt.Value():   CommonAction.NewWithdrawExecution,           // 定金代付发起
	supply_state.CarSupplyAgentPayEarnestFailedEt.Value():  callback.NewWithdrawCallbackCommonExecution, // 定金代付失败
	supply_state.CarSupplyAgentPayEarnestSuccessEt.Value(): callback.NewWithdrawCallbackCommonExecution, // 定金代付成功
	supply_state.CarSupplyAgentPayEarnestJumpEt.Value():    car_supply.NewCommonExecution,               // 跳过定金代付

	// 备车
	supply_state.CarSupplyPrepareCarWaitCompleteEt.Value(): car_supply.NewCommonExecution,

	supply_state.CarSupplyWithdrawSmallEarnestJumpEt.Value(): car_supply.NewCommonExecution,

	supply_state.CarSupplyBusinessContSignJumpEt.Value(): car_supply.NewCommonExecution,

	// 交付相关
	supply_state.CarSupplyDeliverCarOnceEt.Value():       car_supply.NewCommonExecution, // 完成一笔交付
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value(): car_supply.NewCommonExecution, // 交付成功 全部成功
	supply_state.CarSupplyDeliverCatExistFailEt.Value():  car_supply.NewCommonExecution, // 交付完成 存在异常交付

	// 订单取消相关
	supply_state.CarSupplyCancelEt.Value(): car_supply.NewCancelOrderExecution, // 取消订单
}

// 车源供应链 自营库融
var carSupplyWarehouseFinanceExecution = map[string]Constructor{
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	supply_state.CarSupplyOrderCreateEt.Value(): car_supply.NewCreateOrderExecution,

	// 小订支付
	supply_state.CarSupplyPaySmallEarnestEt.Value():        car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPaySmallEarnestExpiredEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyPaySmallEarnestFinishEt.Value():  car_supply.NewCommonExecution,

	// 拼团
	supply_state.CarSupplyGroupPassEt.Value(): car_supply.NewCommonExecution,

	// 小订退款
	supply_state.CarSupplyRefundSmallEarnestStartEt.Value():   car_supply.NewNormalRefundExecution,
	supply_state.CarSupplyRefundSmallEarnestFailedEt.Value():  callback.NewRefundCallbackCommonExecution,
	supply_state.CarSupplyRefundSmallEarnestSuccessEt.Value(): callback.NewRefundCallbackCommonExecution,
	supply_state.CarSupplyRefundSmallEarnestJumpEt.Value():    car_supply.NewCommonExecution,

	// 大定
	supply_state.CarSupplyPayBigEarnestAuditEt.Value():           car_supply.NewTransferOnlineAuditExecution,
	supply_state.CarSupplyPayBigEarnestAuditPassEt.Value():       car_supply.NewTransferOnlineAuditPassExecution,
	supply_state.CarSupplyPayBigEarnestAuditRejectEt.Value():     car_supply.NewTransferOnlineAuditRejectExecution,
	supply_state.CarSupplyPayBigEarnestTransferStartEt.Value():   car_supply.NewCashierPayExecution,
	supply_state.CarSupplyPayBigEarnestTransferFinishEt.Value():  car_supply.NewCommonExecution,
	supply_state.CarSupplyPayBigEarnestTransferExpiredEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyPayBigEarnestTransferJumpEt.Value():    car_supply.NewCommonExecution, // 大订对公转账跳过

	// 合同相关
	supply_state.CarSupplyBusinessContSignEt.Value():        car_supply.NewContSignExecution,
	supply_state.CarSupplyBusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution,
	supply_state.CarSupplyBusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,
	// OA签署
	supply_state.CarSupplyOABusinessContSignEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContTerminationEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyOABusinessContSignFinishEt.Value():  car_supply.NewCommonExecution,

	// 交付
	supply_state.CarSupplyDeliverCarOnceEt.Value():       car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCarAllSuccessEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyDeliverCatExistFailEt.Value():  car_supply.NewCommonExecution,

	// 订单取消
	supply_state.CarSupplyCancelEt.Value():           car_supply.NewCancelOrderExecution,
	supply_state.CarSupplyCancelWithRefundEt.Value(): car_supply.NewRefundExecution,
	supply_state.CarSupplyCancelRefundOverEt.Value(): callback.NewRefundCallbackCommonExecution,
	// end
}

// 车源供应链 寻车单
var carSupplySearchCarsOrderExecution = map[string]Constructor{
	supply_state.CarSupplyUpdateProductExtraStaticEt.Value(): car_supply.NewUpdateProductExtraExecution,

	supply_state.CarSupplyOrderCreateEt.Value():               car_supply.NewCreateOrderExecution,
	supply_state.CarSupplySellerQuotationEt.Value():           car_supply.NewCommonExecution,
	supply_state.CarSupplyStartCommunicationEt.Value():        car_supply.NewCommonExecution,
	supply_state.CarSupplyBuyerCreateOrderEt.Value():          car_supply.NewCommonExecution,
	supply_state.CarSupplyOtherSellerCompletedCloseEt.Value(): car_supply.NewCommonExecution,

	// 关闭寻车
	supply_state.CarSupplyLoseInQuotationTimeoutEt.Value(): car_supply.NewCommonExecution,
	supply_state.CarSupplyBuyerCloseEt.Value():             car_supply.NewCommonExecution,
	// end
}

// 新车电商 一口价
var ncEcomFixedPriceExecution = map[string]Constructor{
	nc_ecom_state.OrderCreateEt.Value():             nc_ecom.NewCreateOrderExecution,
	nc_ecom_state.OrderCancelEt.Value():             nc_ecom.NewCancelOrderExecution,
	nc_ecom_state.OrderCancelWithRefundEt.Value():   nc_ecom.NewCancelWithRefundExecution,
	nc_ecom_state.PayIntentionStartEt.Value():       CommonAction.NewUnionPayExecution,
	nc_ecom_state.PayIntentionTimeoutEt.Value():     nc_ecom.NewPayTimeoutCallbackCommonExecution,
	nc_ecom_state.PayIntentionFinishEt.Value():      nc_ecom.NewPayCallbackCommonExecution,
	nc_ecom_state.ModifyPurchasePlanEt.Value():      nc_ecom.NewUpdateOrderFireExecutionV2,
	nc_ecom_state.PayEarnestStartEt.Value():         nc_ecom.NewUnionPayExecution,
	nc_ecom_state.PayEarnestTimeoutEt.Value():       nc_ecom.NewPayTimeoutCallbackCommonExecution,
	nc_ecom_state.PayEarnestFinishEt.Value():        nc_ecom.NewPayCallbackCommonExecution,
	nc_ecom_state.QualificationAuditPassEt.Value():  nc_ecom.NewUpdateOrderFireExecutionV2,
	nc_ecom_state.ApproveLoanDataConfirm.Value():    nc_ecom.NewUpdateOrderFireExecutionV2,
	nc_ecom_state.BusinessContSignEt.Value():        CommonAction.NewUnionContCreateExecution,
	nc_ecom_state.BusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,
	nc_ecom_state.BusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution,
	nc_ecom_state.CheckContSignStartEt.Value():      CommonAction.NewUnionContCreateExecution,
	nc_ecom_state.CheckContSignFinishEt.Value():     callback.NewContCallbackCommonExecution,
	nc_ecom_state.CheckContTerminationEt.Value():    CommonAction.NewContTerminationExecution,
	nc_ecom_state.CarPrepareFinishEt.Value():        nc_ecom.NewUpdateOrderFireExecutionV2,
	nc_ecom_state.PayFinalStartEt.Value():           CommonAction.NewUnionPayExecution,
	nc_ecom_state.PayFinalTimeoutEt.Value():         callback.NewPayCallbackCommonExecution,
	nc_ecom_state.PayFinalFinishEt.Value():          nc_ecom.NewPayCallbackCommonExecution,
	nc_ecom_state.PayFinalUserConfirmEt.Value():     nc_ecom.NewUpdateOrderFireExecutionV2,
	nc_ecom_state.SettleStartEt.Value():             nc_ecom.NewSettleExecution,
	nc_ecom_state.SettleFinishEt.Value():            callback.NewUnionSettleCallbackExecution,
	nc_ecom_state.RefundFinishEt.Value():            callback.NewUnionRefundCallbackExecution,
	nc_ecom_state.ConfirmOrderEt.Value():            CommonAction.NewUpdateOrderFireExecutionV2,

	nc_ecom_state.RefundIntentionSubProcessStartEt.Value(): CommonAction.NewUnionRefundExecution,
	nc_ecom_state.RefundIntentionFinishEt.Value():          callback.NewUnionRefundCallbackExecution,

	// static
	nc_ecom_state.StaticUpdateEt.Value():            CommonAction.NewUpdateOrderStaticExecution,
	nc_ecom_state.CommentFinishEt.Value():           nc_ecom.NewUpdateOrderStaticExecutionV2,
	nc_ecom_state.UpdateCommentInfoEt.Value():       nc_ecom.NewUpdateOrderStaticExecutionV2,
	nc_ecom_state.UpdateBuyerInfoEt.Value():         nc_ecom.NewUpdateOrderStaticExecutionV2,
	nc_ecom_state.UpdateProductExtraEt.Value():      nc_ecom.NewUpdateOrderStaticExecutionV2,
	nc_ecom_state.UpdateAfterDeliverDataEt.Value():  nc_ecom.NewUpdateOrderStaticExecutionV2,
	nc_ecom_state.UpdateCheckContAdditionEt.Value(): nc_ecom.NewUpdateOrderStaticExecutionV2,
}

// 新车电商 客户自付（仅收取服务费）
var ncEcomServiceFeeExecution = map[string]Constructor{
	nc_ecom_state.OrderCreateEt.Value():             nc_ecom.NewCreateOrderExecution,
	nc_ecom_state.OrderCancelEt.Value():             nc_ecom.NewCancelOrderExecution,
	nc_ecom_state.OrderCancelWithRefundEt.Value():   nc_ecom.NewCancelWithRefundExecution,
	nc_ecom_state.PayIntentionStartEt.Value():       CommonAction.NewUnionPayExecution,
	nc_ecom_state.PayIntentionTimeoutEt.Value():     nc_ecom.NewPayTimeoutCallbackCommonExecution,
	nc_ecom_state.PayIntentionFinishEt.Value():      nc_ecom.NewPayCallbackCommonExecution,
	nc_ecom_state.ModifyPurchasePlanEt.Value():      nc_ecom.NewUpdateOrderFireExecutionV2,
	nc_ecom_state.PayServiceFeeStartEt.Value():      nc_ecom.NewUnionPayExecution,
	nc_ecom_state.PayServiceFeeTimeoutEt.Value():    nc_ecom.NewPayTimeoutCallbackCommonExecution,
	nc_ecom_state.PayServiceFeeFinishEt.Value():     nc_ecom.NewPayCallbackCommonExecution,
	nc_ecom_state.QualificationAuditPassEt.Value():  nc_ecom.NewUpdateOrderFireExecutionV2,
	nc_ecom_state.ApproveLoanDataConfirm.Value():    nc_ecom.NewUpdateOrderFireExecutionV2,
	nc_ecom_state.BusinessContSignEt.Value():        CommonAction.NewUnionContCreateExecution,
	nc_ecom_state.BusinessContSignFinishEt.Value():  callback.NewContCallbackCommonExecution,
	nc_ecom_state.BusinessContTerminationEt.Value(): CommonAction.NewContTerminationExecution,
	nc_ecom_state.CheckContSignStartEt.Value():      CommonAction.NewUnionContCreateExecution,
	nc_ecom_state.CheckContSignFinishEt.Value():     callback.NewContCallbackCommonExecution,
	nc_ecom_state.CheckContTerminationEt.Value():    CommonAction.NewContTerminationExecution,
	nc_ecom_state.CarPrepareFinishEt.Value():        nc_ecom.NewUpdateOrderFireExecutionV2,
	nc_ecom_state.SettleStartEt.Value():             nc_ecom.NewSettleExecution,
	nc_ecom_state.SettleFinishEt.Value():            callback.NewUnionSettleCallbackExecution,
	nc_ecom_state.RefundFinishEt.Value():            callback.NewUnionRefundCallbackExecution,
	nc_ecom_state.ConfirmOrderEt.Value():            CommonAction.NewUpdateOrderFireExecutionV2,

	nc_ecom_state.RefundIntentionSubProcessStartEt.Value(): CommonAction.NewUnionRefundExecution,
	nc_ecom_state.RefundIntentionFinishEt.Value():          callback.NewUnionRefundCallbackExecution,

	// static
	nc_ecom_state.StaticUpdateEt.Value():            CommonAction.NewUpdateOrderStaticExecution,
	nc_ecom_state.CommentFinishEt.Value():           nc_ecom.NewUpdateOrderStaticExecutionV2,
	nc_ecom_state.UpdateCommentInfoEt.Value():       nc_ecom.NewUpdateOrderStaticExecutionV2,
	nc_ecom_state.UpdateBuyerInfoEt.Value():         nc_ecom.NewUpdateOrderStaticExecutionV2,
	nc_ecom_state.UpdateProductExtraEt.Value():      nc_ecom.NewUpdateOrderStaticExecutionV2,
	nc_ecom_state.UpdateAfterDeliverDataEt.Value():  nc_ecom.NewUpdateOrderStaticExecutionV2,
	nc_ecom_state.UpdateCheckContAdditionEt.Value(): nc_ecom.NewUpdateOrderStaticExecutionV2,
}

// 测试用，上线前删除
var testUnionPayExecution = map[string]Constructor{
	statemachine.CreateEt.Value():      CommonAction.NewCreateOrderExecution,
	statemachine.PayEt.Value():         CommonAction.NewUnionPayExecution,
	statemachine.PayCallbackEt.Value(): callback.NewUnionPayCallbackBaseExecution,
}

var action2Execution = map[consts.BizScene]map[string]Constructor{
	consts.BizScene(1001):                testUnionPayExecution,
	consts.BizSceneTest:                  testExecution,
	consts.BizSceneSHSellByEarnestFinal:  shSellCarExecution1,
	consts.BizSceneSHSellByFull:          shSellCarExecution2,
	consts.BizSceneSHSellInsurance:       shSellCarInsuranceExecution,
	consts.BizSceneSHConsignByEF:         shConsignEFExecution,
	consts.BizSceneSHConsignByFull:       shConsignFullExecution,
	consts.BizSceneSHBuyPersonCar:        shBuyPersonCarExecution,
	consts.BizSceneSHBuyCompanyCar:       shBuyCompanyCarExecution,
	consts.BizSceneSHBackCompanyCar:      shBackCompanyCarExecution,
	consts.BizSceneSHDirectBuyCompanyCar: shDirectBuyCompanyCarExecution,
	consts.BizSceneSHBuyBackPersonCar:    shBuyBackPersonCarExecution,
	consts.BizSceneSHBuyBackCompanyCar:   shBuyBackCompanyCarExecution,
	consts.BizSceneSHFinance:             shFinanceExecution,
	consts.BizSceneNCBrokerage:           ncShopBrokerageExecution,
	consts.BizSceneNCBrokerageNoHorizon:  ncShopBrokerageNoHorizonExecution,
	consts.BizSceneNCBigDeposit:          ncShopBigDepositExecution,
	consts.BizSceneNCSmallDeposit:        ncShopSmallDepositExecution,

	consts.BizSceneNCFranchiseeBigDeposit:         ncShopFranchiseeBigDepositExecution,
	consts.BizSceneNCFranchiseeSmallDeposit:       ncShopFranchiseeSmallDepositExecution,
	consts.BizSceneNCFranchiseeBrokerage:          ncShopFranchiseeBrokerageExecution,
	consts.BizSceneNCFranchiseeBrokerageNoHorizon: ncShopFranchiseeBrokerageNoHorizonExecution,

	consts.BizScene(CommonConsts.BizSceneFinanceSaas.Value()): financeSaasExecution,

	consts.BizScene(CommonConsts.BizSceneAfterMarketRetail.Value()):      afterMarketRetailExecution,
	consts.BizScene(CommonConsts.BizSceneNCPurchase.Value()):             ncPurchaseExecution,
	consts.BizScene(CommonConsts.BizSceneSHNationSell.Value()):           shNationSellExecution,
	consts.BizScene(CommonConsts.BizSceneSHNationInsurance.Value()):      shNationInsuranceExecution,
	consts.BizScene(CommonConsts.BizSceneSHSellConsignRevoke.Value()):    shSellConsignRevokeExecution,
	consts.BizScene(CommonConsts.BizSceneSHNationSellV2.Value()):         shNationSellV2Execution,
	consts.BizScene(CommonConsts.BizSceneSHAuctionSell.Value()):          shAuctionSellExecution,
	consts.BizScene(CommonConsts.BizSceneSHAuctionDeposit.Value()):       shAuctionDepositExecution,
	consts.BizScene(CommonConsts.BizSceneSHAuctionDisputeRefund.Value()): shAuctionDisputeRefundExecution,
	consts.BizScene(CommonConsts.BizSceneSHAuctionDisputePayout.Value()): shAuctionDisputePayoutExecution,
	consts.BizScene(CommonConsts.BizScenePurchaseFinance.Value()):        purchaseFinanceExecution,
	consts.BizScene(CommonConsts.BizSceneSelfFinance.Value()):            selfFinanceExecution,
	consts.BizScene(CommonConsts.BizSceneSHAuctionSellV2.Value()):        shAuctionSellV2Execution,
	consts.BizSceneSHSellByEarnestFinalDeliveryCar:                       shSellCarEFDCExecution,
	consts.BizSceneSHSellByFullDeliveryCar:                               shSellCarFullDCExecution,
	consts.BizSceneSHConsignByEarnestFinalDeliveryCar:                    shConsignEFDCExecution,
	consts.BizSceneSHConsignByFullDeliveryCar:                            shConsignFullDCExecution,
	consts.BizScene(CommonConsts.BizSceneNewCarEcom.Value()):             ncEcomExecution,
	consts.BizScene(CommonConsts.BizSceneNewCarEcomFixedPrice.Value()):   ncEcomFixedPriceExecution,
	consts.BizScene(CommonConsts.BizSceneNewCarEcomServiceFee.Value()):   ncEcomServiceFeeExecution,
	// ----------------- 供应链业务 start -----------------
	consts.BizScene(CommonConsts.BizSceneCarSupplyIncreaseTicket.Value()):           carSupplyITExecution,                    // 供应链 - 二网增票
	consts.BizScene(CommonConsts.BizSceneCarSupplyPassengerTicket.Value()):          carSupplyPTExecution,                    // 供应链 - 二网客票
	consts.BizScene(CommonConsts.BizSceneCarSupplyIncreaseTicketNC.Value()):         carSupplyIncreaseTicketNCExecution,      // 供应链 - 门店增票
	consts.BizScene(CommonConsts.BizSceneCarSupplyPassengerTicketNC.Value()):        carSupplyPassengerTicketNCExecution,     // 供应链 - 门店客票
	consts.BizScene(CommonConsts.BizSceneCarSupplyInner.Value()):                    carSupplyInnerExecution,                 // 供应链 - 体验店
	consts.BizScene(CommonConsts.BizSceneCarSupplyIncreaseTicketFranchise.Value()):  carSupplyITFranchiseExecution,           // 供应链 - 加盟店增票
	consts.BizScene(CommonConsts.BizSceneCarSupplyPassengerTicketFranchise.Value()): carSupplyPTFranchiseExecution,           // 供应链 - 加盟店客票
	consts.BizScene(CommonConsts.BizSceneCarSupplyFinance.Value()):                  carSupplyFinanceExecution,               // 供应链 - 金融
	consts.BizScene(CommonConsts.BizSceneCarSupplySecuredTransaction.Value()):       carSupplySecuredTransactionExecution,    // 供应链 - 普通担保
	consts.BizScene(CommonConsts.BizSceneCarSupplySecuredTransactionNC.Value()):     carSupplySecuredTransactionNCExecution,  // 供应链 - 门店担保
	consts.BizScene(CommonConsts.BizSceneCarSupplyExhibitionCarNC.Value()):          carSupplySTExhibitionNCExecution,        // 供应链 - 门店担保展车
	consts.BizScene(CommonConsts.BizSceneCarSupplyExhibitionChangeCarNC.Value()):    carSupplySTExhibitionNCExecution,        // 供应链 - 门店担保展车
	consts.BizScene(CommonConsts.BizSceneCarSupplyFinanceMatch.Value()):             carSupplyFinanceMatchExecution,          // 供应链 - 金融撮合
	consts.BizScene(CommonConsts.BizSceneCarSupplyEcomSecuredTransaction.Value()):   carSupplyEcomSTExecution,                // 供应链 - 电商担保
	consts.BizScene(CommonConsts.BizSceneCarSupplyPreOrder.Value()):                 carSupplyPreOrderExecution,              // 供应链 - 预订单
	consts.BizScene(CommonConsts.BizSceneCarSupplyClearanceAuction.Value()):         carSupplyClearanceAuctionExecution,      // 供应链 - 斩仓拍卖
	consts.BizScene(CommonConsts.BizSceneCarSupplyTPAPPassengerTicket.Value()):      carSupplyTPAPPassengerTicketExecution,   // 供应链 - 三方代采 - 普通客票
	consts.BizScene(CommonConsts.BizSceneCarSupplyTPAPPassengerTicketNC.Value()):    carSupplyTPAPPassengerTicketNCExecution, //  供应链 - 三方代采 - 门店客票
	consts.BizScene(CommonConsts.BizSceneCarSupplyWarehouseFinance.Value()):         carSupplyWarehouseFinanceExecution,      // 供应链 - 自营库融
	consts.BizScene(CommonConsts.BizSceneCarSupplySearchCarsOrder.Value()):          carSupplySearchCarsOrderExecution,       // 供应链 - 寻车单
	// ----------------- 供应链业务 end   -----------------

	// end
}

func GetExecution(ctx context.Context, bizScene, smVersion int32, action string) (Constructor, error) {
	if smVersion > 0 {
		return GetExecutionWithVersion(ctx, bizScene, smVersion, action)
	}
	bizExecution := action2Execution[consts.BizScene(bizScene)]
	if bizExecution == nil {
		return nil, errdef.NewRawErr(errdef.BizSceneNotExist, "")
	}
	f := bizExecution[action]
	if f == nil {
		return nil, errdef.NewRawErr(errdef.ActionNotRegister, "")
	}
	return f, nil
}

func GetExecutionWithVersion(ctx context.Context, bizScene, smVersion int32, action string) (Constructor, error) {
	bizSceneMeta := scene.AllBizSceneMap[bizScene]
	if bizSceneMeta == nil {
		bizErr := errdef.NewRawErr(errdef.BizSceneNotExist, fmt.Sprintf("biz_scene=%d sm_version=%d action=%s", bizScene, smVersion, action))
		logs.CtxError(ctx, "[GetExecutionWithVersion] err=%s", bizErr.Error())
		return nil, bizErr
	}
	stateMachineConfig := bizSceneMeta.SmMap()[smVersion]
	if stateMachineConfig == nil {
		bizErr := errdef.NewRawErr(errdef.SmVersionNotRegister, fmt.Sprintf("biz_scene=%d sm_version=%d action=%s", bizScene, smVersion, action))
		logs.CtxError(ctx, "[GetExecutionWithVersion] err=%s", bizErr.Error())
		return nil, bizErr
	}
	exeConfig := stateMachineConfig.ActionMap()[action]
	if exeConfig == nil {
		bizErr := errdef.NewRawErr(errdef.ActionNotRegister, fmt.Sprintf("biz_scene=%d sm_version=%d action=%s", bizScene, smVersion, action))
		logs.CtxError(ctx, "[GetExecutionWithVersion] err=%s", bizErr.Error())
		return nil, bizErr
	}
	if f := AllExecutionMap[exeConfig.Value()]; f == nil {
		bizErr := errdef.NewRawErr(errdef.ExecutionNotExist, fmt.Sprintf("biz_scene=%d sm_version=%d action=%s", bizScene, smVersion, action))
		logs.CtxError(ctx, "[GetExecutionWithVersion] err=%s", bizErr.Error())
		return nil, bizErr
	} else {
		logs.CtxInfo(ctx, "[GetExecutionWithVersion] choose execution_name=%s", exeConfig.Value())
		return f, nil
	}
}
