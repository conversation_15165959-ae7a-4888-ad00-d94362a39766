package demo

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/demo_scene"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type updateLoanSubStatusExecution struct {
	*executor.ActionBaseExecution
}

const (
	LoanSubStatusTag = "loan_sub_status" // 贷款子流程状态

	LoanSubStatusStartStr = "Start" // 开始
	LoanSubStatusOverStr  = "Over"  // 结束
)

func NewUpdateLoanSubStatusExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &updateLoanSubStatusExecution{}

	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, nil, opts...)
	return e
}

func (e *updateLoanSubStatusExecution) CheckParams(ctx context.Context) error {
	loanSubStatus := e.getLoanSubStatus(ctx)
	if loanSubStatus == "" {
		logs.CtxWarn(ctx, "[updateLoanSubStatusExecution.CheckParams] param error")
		return errdef.NewParamsErr("贷款子状态参数错误")
	}
	return nil
}

// Process 设置贷款类型
func (e *updateLoanSubStatusExecution) Process(ctx context.Context) error {
	var order = e.GetOrder()
	order.TagMap[LoanSubStatusTag] = e.getLoanSubStatus(ctx)
	_, bizErr := service.NewTagService().UpdateTag(ctx, order.FweOrder.OrderID, e.GetBizIdentity().BizScene, order.TagMap)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[updateLoanSubStatusExecution.Process] set tag failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

var mapping = map[string]string{
	demo_scene.DemoLoanSubProcessStartEvent.Value(): LoanSubStatusStartStr,
	demo_scene.DemoLoanSubProcessOverEvent.Value():  LoanSubStatusOverStr,
}

// 获取贷款子状态
func (e *updateLoanSubStatusExecution) getLoanSubStatus(_ context.Context) string {
	return mapping[e.GetActionOrderReq().Action]
}
