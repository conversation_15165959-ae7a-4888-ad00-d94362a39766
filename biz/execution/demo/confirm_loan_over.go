package demo

import (
	"context"

	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type confirmLoanOverExecution struct {
	*executor.ActionBaseExecution
}

func NewConfirmLoanOverExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &confirmLoanOverExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, nil)
	return e
}

func (e *confirmLoanOverExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	if err := e.FireDefault(ctx); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%v", err.Error())
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

// Process 主流程确认贷款完成后进行分账
func (e *confirmLoanOverExecution) Process(_ context.Context) error {

	return nil
}
