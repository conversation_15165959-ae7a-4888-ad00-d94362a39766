package car_supply

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/car_supply_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type CashierPayExecution struct {
	*executor.ActionBaseExecution
	bizReq car_supply_model.CashPayReq
	bizRsp execution_common.CashPayRsp
}

func NewCashierPayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &CashierPayExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.bizReq)
	return t
}

func (e *CashierPayExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.bizReq
	if bizReq.MerchantID == "" || bizReq.AppID == "" {
		return errdef.NewParamsErr("Merchant参数错误")
	}
	return nil
}

func (e *CashierPayExecution) Process(ctx context.Context) error {
	if e.bizReq.CashierDeskType == fwe_trade_common.CashierDeskType_OFFLINE { // 线下支付
		return e.ProcessForOffline(ctx)
	}
	// 在线支付
	return e.ProcessForOnline(ctx)
}

// ProcessForOnline 在线支付方式
func (e *CashierPayExecution) ProcessForOnline(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	rpcReq, bizErr := e.buildPayReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CashierPayExecution.Process] build pay req failed, err=%+v", bizErr.Error())
		return bizErr
	}

	// 驱动
	bizErr = e.FireWithCondition(ctx, buildCondition(e.GetOrder()))
	if bizErr != nil {
		logs.CtxError(ctx, "[CashierPayExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	// 更新资金单类型为pay_cashier
	bizErr = updateFinanceTradeType(ctx, e.GetOrder().FinanceList, rpcReq.FinanceType, CommonConsts.FinancePayCashier)
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditExecution] updateFinanceTradeType err=%s", bizErr.Error())
		return bizErr
	}

	// 创建支付单

	payOrderNo, payData, bizErr := service.NewTradePayment().CreateCashPay(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CashierPayExecution.Process] pay failed, err=%s", bizErr.Error())
		return bizErr
	}

	// 更新订单tag
	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	// 更新订单状态
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	e.bizRsp = execution_common.CashPayRsp{
		PayData:    payData,
		PayOrderNo: payOrderNo,
	}

	return nil
}

// ProcessForOffline 线下支付方式
func (e *CashierPayExecution) ProcessForOffline(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
		payOrderNo string
	)

	// 驱动
	bizErr = e.FireWithCondition(ctx, buildCondition(e.GetOrder()))
	if bizErr != nil {
		logs.CtxError(ctx, "[CashierPayExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	// 更新资金单类型为线下支付
	bizErr = updateFinanceTradeType(ctx, e.GetOrder().FinanceList, e.bizReq.FinanceOrderType, CommonConsts.FinancePayOffline)
	if bizErr != nil {
		logs.CtxError(ctx, "[CashierPayExecution] updateFinanceTradeType err=%s", bizErr.Error())
		return bizErr
	}

	// 通知payment线下收款
	offlineReq, bizErr1 := e.buildOfflineReq(ctx)
	if bizErr1 != nil {
		logs.CtxError(ctx, "[CashierPayExecution] buildOfflineReq err=%s", bizErr1.Error())
		return bizErr1
	}

	payOrderNo, bizErr = service.NewTradePayment().CreateOfflinePay(ctx, offlineReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[CashierPayExecution] CreateOfflinePay err=%s", bizErr.Error())
		return bizErr
	}

	// 更新tag
	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	// 更新订单状态
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.CashPayRsp{
		PayData:    "",
		PayOrderNo: payOrderNo,
	}

	return nil
}

func (e *CashierPayExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *CashierPayExecution) buildPayReq(_ context.Context) (*payment.CreateCashPayReq, *errdef.BizErr) {
	var (
		req      = e.GetActionOrderReq()
		bizReq   = e.bizReq
		currency = payment.CurrencyType_CNY
		order    = e.GetOrder()
		finance  = packer.FinanceGetByType(order.FinanceList, bizReq.FinanceOrderType)
	)

	userId := "user_id"
	if bizReq.UserID != nil {
		userId = *bizReq.UserID
	}

	// 普通收银台支付 不支持拆单
	if finance == nil {
		return nil, errdef.NewParamsErr("未找到资金单")
	}

	if finance.Amount != bizReq.Amount {
		return nil, errdef.NewParamsErr("金额与资金单不等")
	}
	merchantInfo := &payment.MerchantInfo{
		MerchantID: bizReq.MerchantID,
		AppID:      bizReq.AppID,
	}
	if env.IsBoe() {
		merchantInfo.MerchantID = "1300000004"
		merchantInfo.AppID = "800000040004"
	}
	rpcReq := &payment.CreateCashPayReq{
		Identity:             req.Identity,
		UserID:               userId,
		OrderID:              e.GetOrder().FweOrder.OrderID,
		FinanceType:          bizReq.FinanceOrderType,
		PayOrderNo:           bizReq.PayOrderNo,
		CashierDeskType:      bizReq.CashierDeskType,
		OsType:               &bizReq.OsType,
		Currency:             &currency,
		TotalAmount:          bizReq.Amount,
		ExpireTime:           bizReq.ExpireTime,
		RedirectURL:          bizReq.RedirectURL,
		IPAddress:            &bizReq.IPAddress,
		PayLimitList:         bizReq.PayLimitList,
		CallbackEvent:        utils.MakeCallbackEvent(bizReq.CallbackAction),
		TimeoutCallbackEvent: utils.MakeCallbackEvent(bizReq.TimeoutAction),
		MerchantInfo:         merchantInfo,
		Extra:                nil,
	}

	return rpcReq, nil
}

func (e *CashierPayExecution) buildOfflineReq(_ context.Context) (*payment.CreateOfflinePayReq, *errdef.BizErr) {
	var (
		bizReq = e.bizReq
		req    = e.GetActionOrderReq()
		order  = e.GetOrder().FweOrder
	)

	financeOrder := packer.FinanceGetByType(e.GetOrder().FinanceList, bizReq.FinanceOrderType)
	if financeOrder == nil {
		return nil, errdef.NewParamsErr("不支持的资金类型")
	}
	if financeOrder.Amount != bizReq.Amount {
		return nil, errdef.NewParamsErr("金额与资金单不等")
	}

	return &payment.CreateOfflinePayReq{
		Identity:           req.Identity,
		OrderID:            order.OrderID,
		FinanceType:        bizReq.FinanceOrderType,
		Currency:           payment.CurrencyTypePtr(payment.CurrencyType_CNY),
		Amount:             financeOrder.Amount,
		CheckData:          bizReq.CheckData,
		CallbackEvent:      utils.MakeCallbackEvent(bizReq.CallbackAction),
		CloseCallbackEvent: utils.MakeCallbackEvent(bizReq.CloseAction),
	}, nil
}
