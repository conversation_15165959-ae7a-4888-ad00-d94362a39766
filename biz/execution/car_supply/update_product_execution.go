package car_supply

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/car_supply_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type UpdateProductExtraExecution struct {
	*executor.StaticBaseExecution
	bizReq car_supply_model.UpdateProductExtraReq
}

func NewUpdateProductExtraExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &UpdateProductExtraExecution{}
	t.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.bizReq)
	return t
}

func (e *UpdateProductExtraExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		bizReq       = e.bizReq
	)

	updateParams := &service_model.UpdateOrderParams{
		UpdateProductExtra: conv.StringPtr(bizReq.ProductExtra),
		Operator:           e.GetActionOrderReq().GetOperator(),
	}

	// 最后更新
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateProductExtraExecution] UpdateOrder err=%s", bizErr.Error())
		return bizErr
	}

	_, bizErr = service.NewTagService().UpdateTag(ctx, orderID, e.GetOrder().FweOrder.BizScene, bizReq.Tag)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateProductExtraExecution] UpdateOrderTag err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
