package car_supply

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/car_supply_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"context"
)

type TransferOnlineAuditPassExecution struct {
	*executor.ActionBaseExecution
	bizReq car_supply_model.AuditCallbackReq
}

func NewTransferOnlineAuditPassExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &TransferOnlineAuditPassExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.bizReq)
	return t
}

func (e *TransferOnlineAuditPassExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		bizReq       = e.bizReq
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	// 驱动
	bizErr = e.FireWithCondition(ctx, buildCondition(e.GetOrder()))
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditPassExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	// 通知payment线下认款
	recognitionReq, bizErr := e.buildPayRecognitionReq(ctx, bizReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditPassExecution] buildOfflineReq err=%s", bizErr.Error())
		return bizErr
	}

	bizErr = service.NewTradePayment().PayRecognition(ctx, recognitionReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditPassExecution] CreateOfflinePay err=%s", bizErr.Error())
		return bizErr
	}

	// 最后更新
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditPassExecution] UpdateOrder err=%s", bizErr.Error())
		return bizErr
	}

	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditPassExecution] UpdateOrderTag err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *TransferOnlineAuditPassExecution) buildPayRecognitionReq(_ context.Context, bizReq car_supply_model.AuditCallbackReq) (*payment.PayRecognitionReq, *errdef.BizErr) {
	return &payment.PayRecognitionReq{
		Amount:       conv.Int64Default(e.GetOrder().TagMap["biz_index8"], 0), // todo
		PayOrderNo:   e.GetOrder().TagMap["biz_index9"],                       // todo
		TradeNo:      "mock",
		OperatorID:   e.GetActionOrderReq().GetOperator().GetOperatorID(),
		OperatorName: e.GetActionOrderReq().GetOperator().GetOperatorName(),
	}, nil
}
