package car_supply

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/car_supply_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"
	"code.byted.org/overpass/motor_trade_audit/kitex_gen/motor/trade/audit"
)

func BuildApplyAuditReq(param car_supply_model.ApplyAuditReq, no int64) *audit.ApplyAuditReq {
	// 如果没有传outerID进行拼接保证幂等
	var outerID = ""
	if param.OuterID == nil {
		outerID = genAuditOuterID(param.UniqID, no)
	} else {
		outerID = *param.OuterID
	}

	return &audit.ApplyAuditReq{
		Tenant:      param.Tenant,
		SystemId:    param.SystemId,
		CreatorId:   param.CreatorId,
		CreatorName: param.CreatorName,
		BizType:     param.BizType,
		OuterId:     outerID,
		AuditName:   param.AuditName,
		AuditParams: []*audit.AuditParams{
			{
				BeforeValue: param.AuditParams,
			},
		},
		Steps:     param.Steps,
		BizIndex1: param.OrderID,
		BizIndex2: param.AuditPassAction,
		BizIndex3: param.AuditRejectAction,
		BizIndex4: param.FulfillOrderID,
		BizIndex5: param.UniqID,
		BizIndex6: param.BizIndex6,
		BizIndex7: param.BizIndex7,
		BizIndex8: param.BizIndex8,
		BizIndex9: param.BizIndex9,
	}
}

func genAuditOuterID(uniqID string, no int64) string {
	return fmt.Sprintf("%s_no%d", uniqID, no)
}

func updateFinanceTradeType(ctx context.Context, financeList []*db_model.FFinanceOrder, financeType int32, tradeType CommonConsts.FinanceTradeType) *errdef.BizErr {
	financeInfo := packer.FinanceGetByType(financeList, financeType)
	if financeInfo == nil {
		return errdef.NewParamsErr("异常的资金类型")
	}

	if bizErr := service.NewFinanceOrderService().UpdateOrderFinance(ctx, financeInfo.FinanceOrderID, &service_model.UpdateFinanceParams{
		UpdateTradeType: conv.StringPtr(tradeType.Value()),
	}); bizErr != nil {
		return bizErr
	}

	return nil
}

func buildCondition(order *service_model.Order) map[string]interface{} {
	orderDB := order.FweOrder
	tagMap := order.TagMap
	return map[string]interface{}{
		car_supply_model.BuyerIsInnerTag:  conv.BoolDefault(tagMap[car_supply_model.BuyerIsInnerTag], false),
		car_supply_model.SellerIsInnerTag: conv.BoolDefault(tagMap[car_supply_model.SellerIsInnerTag], false),
		car_supply_model.ProductTypeTag:   orderDB.ProductType,
		car_supply_model.ActivityTypeTag:  conv.Int64Default(tagMap[car_supply_model.ActivityTypeTag], 0),
	}
}

func restoreStock(ctx context.Context, order *db_model.FweOrder) *errdef.BizErr {
	incrStockReq, err := buildIncrStockReq(ctx, order)
	if err != nil {
		logs.CtxError(ctx, "[restoreStock] buildIncrStockReq err: %+v", err)
		return errdef.NewRawErr(errdef.ProductStockRpcErr, "buildIncrStockReq err")
	}

	if bizErr := service.NewProductStockService().IncrStock(ctx, incrStockReq); bizErr != nil {
		logs.CtxError(ctx, "[restoreStock] IncrStock err: %+v", err)
		return bizErr
	}

	return nil
}

func buildIncrStockReq(ctx context.Context, order *db_model.FweOrder) (*product_stock.IncrStockReq, error) {
	incrStockReq := product_stock.NewIncrStockReq()

	skuID, err := conv.StrToInt64E(order.SkuID)
	if err != nil {
		return nil, err
	}

	productId, err := conv.StrToInt64E(order.ProductID)
	if err != nil {
		return nil, err
	}

	items := []*product_stock.ChangeStockItem{
		{
			StockUnit: &product_stock.StockUnit{
				SkuId:          skuID,
				StockType:      product_stock.StockType_NORMAL,
				ProductId:      conv.Int64Ptr(productId),
				ProductVersion: conv.Int64Ptr(order.ProductVersion),
			},
			StockNum: conv.Int64Ptr(int64(order.ProductQuantity)),
		},
	}

	incrStockReq.SetItems(items)
	incrStockReq.SetToken(fmt.Sprintf("order_center_%s", order.OrderID))
	incrStockReq.SetSource(consts.PSM)
	incrStockReq.SetOrderId(&order.OrderID)
	incrStockReq.SetIsTest(conv.Int32Ptr(order.IsTest))
	return incrStockReq, nil
}
