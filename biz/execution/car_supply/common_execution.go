package car_supply

import (
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"context"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type CommonExecution struct {
	*executor.ActionBaseExecution
}

func NewCommonExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &CommonExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, nil)
	return t
}

func (e *CommonExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
		tag          = e.GetActionOrderReq().GetTagMap()
		stateMachine = e.GetStateMachine()
	)

	// 驱动
	bizErr = e.FireWithCondition(ctx, buildCondition(e.GetOrder()))
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	bizErr = orderService.UpdateOrderTag(ctx, orderID, tag)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] UpdateOrderTag err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
