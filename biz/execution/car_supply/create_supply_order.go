package car_supply

import (
	"code.byted.org/gopkg/lang/slices"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"context"
	"fmt"

	"code.byted.org/motor/fwe_trade_common/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/overpass/motor_fwe_ecom_product_stock/kitex_gen/motor/fwe_ecom/product_stock"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type CreateOrderExecution struct {
	*executor.CreateBaseExecution
	result interface{}
}

func NewCreateOrderExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &CreateOrderExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), nil)
	return t
}

func (e *CreateOrderExecution) Process(ctx context.Context) error {
	var (
		err          error
		bizErr       *errdef.BizErr
		stateMachine = e.GetStateMachine()
		createReq    = e.GetCreateOrderReq()
		bizScene     = createReq.GetIdentity().GetBizScene()
		productInfo  = createReq.GetProductInfo()
	)

	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene,
		SmVersion:          createReq.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createReq.GetOrderName(),
		OrderDesc:          createReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductExtra:       productInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		ProductVersion:     productInfo.ProductVersion,
		TotalAmount:        createReq.GetTotalAmount(), // packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList()),
		TotalPayAmount:     createReq.GetTotalAmount(), // packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList()),
		TotalSubsidyAmount: 0,
		TradeType:          int32(createReq.TradeType),
		BuyerID:            packer.CommonTradeSubjectIDGet(createReq.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(createReq.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createReq.IsTest),
		Creator:            createReq.GetOperator().GetOperatorID(),
		CreatorName:        createReq.GetOperator().GetOperatorName(),
		Operator:           createReq.GetOperator().GetOperatorID(),
		OperatorName:       createReq.GetOperator().GetOperatorName(),
		IdempotentID:       createReq.GetIdemID(),
	}

	if fweOrder.TotalAmount == 0 {
		fweOrder.TotalAmount = packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList())
		fweOrder.TotalPayAmount = fweOrder.TotalAmount
	}

	financeList := e.buildFinanceList(ctx, fweOrder, createReq.FinanceList)
	order := &service_model.Order{
		FweOrder:    fweOrder,
		TagMap:      createReq.OrderTag,
		FinanceList: financeList,
		BizExtra:    createReq.Extra,
	}

	// 驱动状态
	err = stateMachine.Fire(ctx, statemachine.OrderCreateEt.Value(), buildCondition(order))
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "CreateOrderExecution")
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 更改订单状态
	order.FweOrder.OrderStatus = int32(stateMachine.CurState())

	bizErr = service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 预订单、展车换车架号订单、寻车单 不扣减库存
	isChangeVin := conv.BoolDefault(createReq.OrderTag["is_change_vin"], false)
	skipDecrStockSceneList := []int32{
		CommonConsts.BizSceneCarSupplyPreOrder.Value(),
		CommonConsts.BizSceneCarSupplySearchCarsOrder.Value(),
	}
	if !slices.ContainsInt32(skipDecrStockSceneList, bizScene) && !isChangeVin {
		// 构造扣减库存请求
		decrStockReq, err := e.buildDecrStockReq(ctx, order.FweOrder.OrderID, createReq.IsTest, productInfo, createReq.OrderTag)
		if err != nil {
			return errdef.NewBizErr(errdef.ServerException, err, "")
		}

		_, bizErr = service.NewProductStockService().DecrStock(ctx, decrStockReq)
		if bizErr != nil {
			return bizErr
		}
	}

	e.result = e.GetOrderID()
	return nil
}

func (e *CreateOrderExecution) Result() interface{} {
	return e.result
}

func (e *CreateOrderExecution) buildFinanceList(_ context.Context, order *db_model.FweOrder, financeList []*fwe_trade_common.FinanceInfo) []*db_model.FFinanceOrder {
	financeDBList := make([]*db_model.FFinanceOrder, 0, len(financeList))
	for _, finance := range financeList {
		financeDBList = append(financeDBList, &db_model.FFinanceOrder{
			TenantType:       order.TenantType,
			BizScene:         order.BizScene,
			AppID:            "",
			MerchantID:       finance.MerchantID,
			Mid:              "",
			OrderID:          order.OrderID,
			OrderName:        order.OrderName,
			TradeType:        finance.TradeType,
			FinanceOrderID:   utils.MakeFinanceOrderIDTool(order.OrderID, finance.FinanceOrderType),
			FinanceOrderType: finance.FinanceOrderType,
			Amount:           finance.Amount,
			ProcessAmount:    0,
			Status:           getStatusBool(finance.Amount == 0, fwe_trade_common.FinanceStatus_Complete, fwe_trade_common.FinanceStatus_NotHandle),
			FeeItemDetail:    conv.StringPtr(tools.GetLogStr(finance.FeeItemList)),
		})
	}
	return financeDBList
}

func getStatusBool(cond bool, a, b fwe_trade_common.FinanceStatus) int32 {
	if cond {
		return int32(a)
	} else {
		return int32(b)
	}
}

func (e *CreateOrderExecution) buildDecrStockReq(_ context.Context, orderID string, isTest bool, productInfo *fwe_trade_common.ProductInfo, orderTag map[string]string) (*product_stock.DecrStockReq, error) {
	decrStockReq := product_stock.NewDecrStockReq()
	skuID, err := conv.StrToInt64E(productInfo.SkuID)
	if err != nil {
		return nil, err
	}

	productId, err := conv.StrToInt64E(productInfo.ProductID)
	if err != nil {
		return nil, err
	}

	items := []*product_stock.ChangeStockItem{
		{
			StockUnit: &product_stock.StockUnit{
				SkuId:          skuID,
				StockType:      product_stock.StockType_NORMAL,
				ProductId:      conv.Int64Ptr(productId),
				ProductVersion: conv.Int64Ptr(productInfo.ProductVersion),
			},
			StockNum: &productInfo.ProductQuantity,
		},
	}

	decrStockReq.SetItems(items)
	decrStockReq.SetToken(fmt.Sprintf("order_center_%s", orderID))
	decrStockReq.SetSource(consts.PSM)
	decrStockReq.SetOrderId(&orderID)
	if isTest {
		decrStockReq.SetIsTest(conv.Int32Ptr(1))
	}
	decrStockReq.SetBizExtra(map[string]string{
		"lat": orderTag["lat"],
		"lon": orderTag["lon"],
	})

	return decrStockReq, nil
}
