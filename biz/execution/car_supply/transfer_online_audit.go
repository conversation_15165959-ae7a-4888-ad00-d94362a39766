package car_supply

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/car_supply_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
	"context"
)

type TransferOnlineAuditExecution struct {
	*executor.ActionBaseExecution
	bizReq car_supply_model.ApplyAuditReq
}

func NewTransferOnlineAuditExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &TransferOnlineAuditExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &t.bizReq)
	return t
}

func (e *TransferOnlineAuditExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		auditParam   = e.bizReq
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	// 驱动
	bizErr = e.FireWithCondition(ctx, buildCondition(e.GetOrder()))
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	// 获得序号
	auditNoStr := e.GetOrder().TagMap[auditParam.UniqID]
	auditNo := conv.Int64Default(auditNoStr, 0)

	// 更新资金单类型为线下支付
	bizErr = updateFinanceTradeType(ctx, e.GetOrder().FinanceList, auditParam.FinanceType, CommonConsts.FinancePayOffline)
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditExecution] updateFinanceTradeType err=%s", bizErr.Error())
		return bizErr
	}

	// 通知payment线下收款
	offlineReq, bizErr := e.buildOfflineReq(ctx, auditParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditExecution] buildOfflineReq err=%s", bizErr.Error())
		return bizErr
	}

	payOrderNo, bizErr := service.NewTradePayment().CreateOfflinePay(ctx, offlineReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditExecution] CreateOfflinePay err=%s", bizErr.Error())
		return bizErr
	}

	// 调用审批服务
	applyAuditReq := BuildApplyAuditReq(auditParam, auditNo)
	applyAuditReq.BizIndex8 = conv.StringDefault(offlineReq.Amount, "")
	applyAuditReq.BizIndex9 = payOrderNo

	bizErr = service.NewAuditService().ApplyAudit(ctx, applyAuditReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditExecution] ApplyAudit err=%s", bizErr.Error())
		return bizErr
	}

	// 最后更新
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditExecution] UpdateOrder err=%s", bizErr.Error())
		return bizErr
	}

	auditNo++
	bizErr = orderService.UpdateOrderTag(ctx, orderID, map[string]string{
		auditParam.UniqID: conv.Int64ToStr(auditNo),
		"biz_index8":      applyAuditReq.BizIndex8,
		"biz_index9":      applyAuditReq.BizIndex9,
	})

	if bizErr != nil {
		logs.CtxError(ctx, "[TransferOnlineAuditExecution] UpdateOrderTag err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *TransferOnlineAuditExecution) buildOfflineReq(_ context.Context, bizReq car_supply_model.ApplyAuditReq) (*payment.CreateOfflinePayReq, *errdef.BizErr) {
	order := e.GetOrder().FweOrder
	financeOrder := packer.FinanceGetByType(e.GetOrder().FinanceList, bizReq.FinanceType)
	if financeOrder == nil {
		return nil, errdef.NewParamsErr("不支持的资金类型")
	}

	return &payment.CreateOfflinePayReq{
		Identity: &fwe_trade_common.BizIdentity{
			TenantType: tenant_base.TenantType(order.TenantType),
			BizScene:   order.BizScene,
		},
		OrderID:     order.OrderID,
		FinanceType: bizReq.FinanceType,
		Amount:      financeOrder.Amount,
	}, nil
}
