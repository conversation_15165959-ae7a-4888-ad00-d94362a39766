package car_supply

import (
	"context"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/car_supply_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/gopkg/tools"
)

type ContSignExecution struct {
	*executor.ActionBaseExecution
	conf   model.CommonConf
	bizReq car_supply_model.SignContractReq
	bizRsp car_supply_model.SignContractResp
}

func NewContSignExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &ContSignExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &t.conf, &t.bizReq)
	return t
}

func (e *ContSignExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[ContSignExecution] err=%s", err.Error())
		return err
	}

	if !e.conf.CheckContStructField {
		return nil
	}

	param := &service.CheckContTotalAmountParam{
		TmplID:           e.bizReq.ContTmplID,
		TmplParams:       e.bizReq.ContTmplParams,
		OrderTotalAmount: e.GetOrder().FweOrder.TotalAmount,
		Params:           "",
		BizScene:         e.GetBizIdentity().BizScene,
		ContType:         e.bizReq.ContType,
		OrderID:          e.GetActionOrderReq().OrderID,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckContTotalAmount(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[ContSignExecution] err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[ContSignExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "合同总金额和订单不一致")
	}
	return nil
}

func (e *ContSignExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	// 驱动
	bizErr = e.FireWithCondition(ctx, buildCondition(e.GetOrder()))
	if bizErr != nil {
		logs.CtxError(ctx, "[ContSignExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	// 签署合同
	if bizErr = e.signContract(ctx); bizErr != nil {
		logs.CtxError(ctx, "[ContSignExecution] signContract err=%s", bizErr.Error())
		return bizErr
	}
	logs.CtxInfo(ctx, "[ContSignExecution] bizResp %s", tools.GetLogStr(e.bizRsp))

	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[ContSignExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[ContSignExecution] err=%s", bizErr.Error())
		return bizErr
	}

	logs.CtxWarn(ctx, "resp is %s", tools.GetLogStr(e.bizRsp))

	return nil
}

func (e *ContSignExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *ContSignExecution) LockTimeout() time.Duration {
	// 创建合同接口比较慢，调整超时时间为 20s
	return 20 * time.Second
}

// signContract 签署合同
func (e *ContSignExecution) signContract(ctx context.Context) *errdef.BizErr {
	var (
		actionReq = e.GetActionOrderReq()
		bizReq    = e.bizReq
		operator  = actionReq.GetOperator()
		fweOrder  = e.GetOrder().FweOrder
	)
	relatedContSerials, err := service.NewContractService().QueryRelatedContract(ctx, caller.WriteDB(ctx), fweOrder.OrderID)
	if err != nil {
		logs.CtxError(ctx, "[ContSignExecution] signContract err=%s", err.Error())
		return err
	}
	// 合同请求
	signParty := e.buildSignParty(bizReq.SignPartyInfoMap)
	serviceReq := &service_model.UnionContCreateReq{
		TenantType:   int32(actionReq.GetIdentity().GetTenantType()),
		BizScene:     actionReq.GetIdentity().GetBizScene(),
		OrderID:      actionReq.GetOrderID(),
		ContType:     bizReq.ContType,
		TmplID:       bizReq.ContTmplID,
		TmplParams:   bizReq.ContTmplParams,
		SignPartyMap: signParty,
		InOutData: &core.InOutData{
			Currency: core.Currency_CNY,
			TotalIn:  fweOrder.TotalAmount,
			TotalOut: fweOrder.TotalAmount,
		},
		CallbackAction: utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:  utils.MakeContractCallbackExtra(actionReq.GetOrderID()),
		Operator: &core.Operator{
			OperatorID:   conv.StrToInt64(operator.GetOperatorID(), 0),
			OperatorName: operator.GetOperatorName(),
		},
		IsTest:             fweOrder.IsTest == int32(1),
		RelatedContSerials: relatedContSerials,
	}

	// 请求
	serviceRsp, bizErr := service.NewContractService().UnionCreateContract(ctx, caller.WriteDB(ctx), serviceReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	e.bizRsp = car_supply_model.SignContractResp{
		ContSerial: serviceRsp.ContSerial,
		SignLink:   e.getSignLink(serviceRsp.SignLinks, signParty),
		SignLinks:  serviceRsp.SignLinks,
	}
	return nil
}

// SignInnerContract 创建我司合同签署
func (e *ContSignExecution) signInnerContract(ctx context.Context) *errdef.BizErr {
	// 合同
	sReq, bizErr := e.buildReqForInnerContract(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	bizScene := e.GetActionOrderReq().Identity.BizScene
	var signLink string
	var contSerial string
	if bizScene == CommonConsts.BizSceneCarSupplyIncreaseTicketNC.Value() {
		contSerial, bizErr = service.NewContractService().CreateContract(ctx, sReq)
	} else {
		signLink, bizErr = service.NewContractService().CreateContractWithApply(ctx, sReq)
	}
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	e.bizRsp = car_supply_model.SignContractResp{
		SignLink:   signLink,
		ContSerial: contSerial,
	}

	return nil
}

func (e *ContSignExecution) buildReqForInnerContract(_ context.Context) (sReq *service_model.ContractCreateParam, bizErr *errdef.BizErr) {
	var (
		req               = e.bizReq
		fweOrder          = e.GetOrder().FweOrder
		actionReq         = e.GetActionOrderReq()
		signPartyDataList []*core.SignPartyData
	)

	for _, signPartyInfo := range req.SignPartyInfoMap {
		if signPartyData := signPartyInfo.SignPartyData; signPartyData != nil {
			signPartyDataList = append(signPartyDataList, signPartyInfo.SignPartyData)
		} else {
			return nil, errdef.NewParamsErr("我司合同 缺少签署方信息")
		}
	}

	sReq = &service_model.ContractCreateParam{
		OrderID:           fweOrder.OrderID,
		TenantType:        int32(actionReq.GetIdentity().GetTenantType()),
		BizScene:          actionReq.GetIdentity().GetBizScene(),
		ContType:          req.ContType,
		OperatorID:        conv.StrToInt64(actionReq.GetOperator().GetOperatorID(), 0),
		OperatorName:      actionReq.GetOperator().GetOperatorName(),
		TmplID:            req.ContTmplID,
		SmsConfigMap:      req.SmsConfigMap,
		TmplParams:        req.ContTmplParams,
		SignPartyDataList: signPartyDataList,
		ReturnUrl:         conv.StringPtrToVal(req.RedirectURL, ""),
		InOutData: &service_model.InOutData{
			Currency: int32(core.Currency_CNY),
			TotalIn:  fweOrder.TotalAmount,
			TotalOut: fweOrder.TotalAmount,
		},
		CallbackEvent: utils.MakeCallbackEvent(req.CallbackAction),
		CallbackExtra: utils.MakeContractCallbackExtra(fweOrder.OrderID),
	}
	return
}

// 创建非我司合同签约
func (e *ContSignExecution) signNotInnerContract(ctx context.Context) *errdef.BizErr {
	// 合同
	sReq, bizErr := e.buildReqForNotInnerContract(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	contSerial, signLink, bizErr := service.NewContractService().CreateContractForNotInnerWithApply(ctx, sReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	e.bizRsp = car_supply_model.SignContractResp{
		ContSerial: contSerial,
		SignLink:   signLink,
	}

	return nil
}

func (e *ContSignExecution) buildReqForNotInnerContract(_ context.Context) (sReq *service_model.ContractCreateParamForNotInner, bizErr *errdef.BizErr) {
	var (
		req       = e.bizReq
		fweOrder  = e.GetOrder().FweOrder
		actionReq = e.GetActionOrderReq()
	)

	sReq = &service_model.ContractCreateParamForNotInner{
		OrderID:          fweOrder.OrderID,
		TenantType:       int32(actionReq.GetIdentity().GetTenantType()),
		BizScene:         actionReq.GetIdentity().GetBizScene(),
		ContType:         req.ContType,
		OperatorID:       conv.StrToInt64(actionReq.GetOperator().GetOperatorID(), 0),
		OperatorName:     actionReq.GetOperator().GetOperatorName(),
		TmplID:           req.ContTmplID,
		TmplParams:       req.ContTmplParams,
		ReturnUrl:        conv.StringPtrToVal(req.RedirectURL, ""),
		SignPartyInfoMap: req.SignPartyInfoMap,
		CallbackEvent:    utils.MakeCallbackEvent(req.CallbackAction),
		CallbackExtra:    "",
	}

	return sReq, nil
}

func (e *ContSignExecution) buildSignParty(infoMap map[core.SignPosition]*car_supply_model.SignPartyInfo) (
	signPartyMap map[core.SignPosition]*core.SignParty) {
	signPartyMap = make(map[core.SignPosition]*core.SignParty)
	for sp, info := range infoMap {
		signParty := &core.SignParty{
			SignPosition:   sp,
			IsInner:        conv.BoolPtr(info.SignPartyData.IsInner),
			CardType:       info.SignPartyData.CardType,
			IdentName:      info.SignPartyData.IdentName,
			IdentID:        info.SignPartyData.IdentID,
			StampCondition: core.StampConditionPtr(core.StampCondition_AutoApply),
			SignatureTypes: nil,
			SignerName:     info.SignPartyData.SignerName,
			SignerPhone:    info.SignPartyData.SignerPhone,
			SmsConfig:      info.SmsConfig,
			ReturnURL:      e.bizReq.RedirectURL,
		}
		// 非我司合同 甲方自动签署
		if !e.bizReq.IsInnerContract && sp == core.SignPosition_PA {
			signParty.StampCondition = core.StampConditionPtr(core.StampCondition_AutoSign)
		}
		// 展车合同 乙方自动签署
		if (e.bizReq.ContType == int32(CommonConsts.ExhibitionCarContType.ValueInt()) ||
			e.bizReq.ContType == int32(CommonConsts.ExhibitionCarBorrowContType.ValueInt()) ||
			e.GetOrder().FweOrder.BizScene == CommonConsts.BizSceneCarSupplyTPAPPassengerTicketNC.Value()) &&
			sp == core.SignPosition_PB {
			signParty.StampCondition = core.StampConditionPtr(core.StampCondition_AutoSign)
		}
		if info.SmsConfig != nil {
			signParty.StampCondition = core.StampConditionPtr(core.StampCondition_NeedSms)
		}
		signPartyMap[sp] = signParty
	}
	return
}

func (e *ContSignExecution) getSignLink(links map[core.SignPosition]string,
	signPartyMap map[core.SignPosition]*core.SignParty) string {
	if !e.bizReq.IsInnerContract {
		return links[core.SignPosition_PB]
	}
	spa := signPartyMap[core.SignPosition_PA]
	spb := signPartyMap[core.SignPosition_PB]
	if spa != nil && spa.GetIsInner() && spb != nil && spb.GetIsInner() {
		return ""
	}
	if spa != nil && spa.GetIsInner() {
		return links[core.SignPosition_PB]
	}
	if spb != nil && spb.GetIsInner() {
		return links[core.SignPosition_PA]
	}
	return links[core.SignPosition_PA]
}
