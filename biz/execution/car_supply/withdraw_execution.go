package car_supply

import (
	"context"
	"fmt"
	"time"
	"unicode/utf8"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/car_supply_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
)

type WithdrawExecution struct {
	*executor.ActionBaseExecution
	conf   car_supply_model.Conf
	bizReq execution_common.WithdrawReq
	bizRsp execution_common.WithdrawRsp
}

func NewWithdrawExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &WithdrawExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq)
	return e
}

func (e *WithdrawExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[WithdrawExecution] PreProcess failed, err=%+v", err)
		return err
	}

	// 判断是否做合同金额校验
	if !e.conf.CheckContStructField {
		return nil
	}

	// 校验
	param := &service.CheckAmountByContParam{
		OrderID:          e.GetActionOrderReq().OrderID,
		FinanceOrderType: e.bizReq.FinanceOrderType,
		Amount:           e.bizReq.Amount,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[WithdrawExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[WithdrawExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "出款合同校验未通过")
	}

	return nil
}

func (e *WithdrawExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		orderID      = e.GetActionOrderReq().GetOrderID()
		orderService = service.NewOrderService()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}

		bizReq  = e.bizReq
		finance = packer.FinanceGetByType(e.GetOrder().FinanceList, bizReq.FinanceOrderType)
	)

	// 检查资金单
	if finance == nil {
		return errdef.NewParamsErr("未找到资金单")
	}
	if finance.Amount != bizReq.Amount {
		return errdef.NewParamsErr("金额与资金单不等")
	}

	supportTradeTypeList := []string{CommonConsts.FinanceWithdrawBank.Value(), CommonConsts.FinanceTransferHz.Value()}
	if !slices.ContainsString(supportTradeTypeList, finance.TradeType) {
		return errdef.NewParamsErr("不支持的资金单类型")
	}

	// 驱动
	bizErr = e.FireWithCondition(ctx, map[string]interface{}{
		"trade_type": finance.TradeType,
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	withdrawNo, bizErr := e.withDraw(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[WithdrawExecution.Process] withDraw failed, err=%s", bizErr.Error())
		return bizErr
	}

	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.WithdrawRsp{
		WithdrawOrderNo: withdrawNo,
	}

	return nil
}

func (e *WithdrawExecution) withDraw(ctx context.Context) (string, *errdef.BizErr) {
	var (
		bizReq  = e.bizReq
		finance = packer.FinanceGetByType(e.GetOrder().FinanceList, bizReq.FinanceOrderType)
	)

	if finance.TradeType == CommonConsts.FinanceWithdrawBank.Value() {
		// 账户->银行卡
		rpcReq, err := e.buildPayReq(ctx)
		if err != nil {
			logs.CtxWarn(ctx, "[WithdrawExecution.withDraw] build pay req failed, err=%+v", err)
			return "", err
		}
		withdrawNo, bizErr := service.NewTradePayment().WithdrawDeposit(ctx, rpcReq)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[WithdrawExecution.withDraw] pay failed, err=%s", bizErr.Error())
			return "", bizErr
		}
		return withdrawNo, nil
	} else if finance.TradeType == CommonConsts.FinanceTransferHz.Value() {
		// 	账户 -> 账户
		rpcReq, err := e.buildTransferReq(ctx)
		if err != nil {
			logs.CtxWarn(ctx, "[WithdrawExecution.withDraw] build pay transfer failed, err=%+v", err)
			return "", err
		}
		bizErr := service.NewTradePayment().Transfer(ctx, rpcReq)
		if bizErr != nil {
			logs.CtxWarn(ctx, "[WithdrawExecution.withDraw] Transfer failed, err=%s", bizErr.Error())
			return "", bizErr
		}
		return "", nil
	}
	return "", errdef.NewParamsErr("不支持的资金单类型")
}

func (e *WithdrawExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *WithdrawExecution) buildPayReq(_ context.Context) (*payment.WithdrawDepositReq, *errdef.BizErr) {
	var (
		req      = e.GetActionOrderReq()
		bizReq   = e.bizReq
		currency = payment.CurrencyType_CNY
		finance  = packer.FinanceGetByType(e.GetOrder().FinanceList, bizReq.FinanceOrderType)
	)

	rpcReq := &payment.WithdrawDepositReq{
		OrderID:           req.OrderID,
		FinanceOrderID:    finance.FinanceOrderID,
		MerchantID:        bizReq.MerchantID,
		MerchantName:      bizReq.MerchantName,
		AppID:             bizReq.AppID,
		Amount:            finance.Amount,
		WithdrawType:      bizReq.WithdrawType,
		IPAddress:         bizReq.IPAddress,
		WitdhrawDesc:      bizReq.Reason,
		Mid:               nil,
		Currency:          &currency,
		BankCardInfo:      bizReq.BankCardInfo,
		CallbackEvent:     utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:     "",
		FailCallbackEvent: utils.MakeCallbackEvent(bizReq.FailCallbackAction),
		Extra:             nil,
		Operator:          req.GetOperator(),
		Identity:          req.GetIdentity(),
	}
	return rpcReq, nil
}

func (e *WithdrawExecution) buildTransferReq(ctx context.Context) (*payment.TransferReq, *errdef.BizErr) {
	var (
		bizErr  *errdef.BizErr
		conf    = e.conf
		proReq  = e.bizReq
		finance = packer.FinanceGetByType(e.GetOrder().FinanceList, e.bizReq.FinanceOrderType)
		order   = e.GetOrder().FweOrder
	)

	// 卖方信息（收款方）
	payeeConf := conf.CompanyMap["platform_3166"]
	if payeeConf == nil || payeeConf.MerchantID == "" || payeeConf.Uid == "" {
		bizErr = errdef.NewParamsErr("transfer payee conf not found")
		logs.CtxError(ctx, "[buildTransferReq] err=%s", bizErr.Error())
		return nil, bizErr
	}

	payerConf := &car_supply_model.ConfCompany{
		IdentifyType: payeeConf.IdentifyType,
		Aid:          payeeConf.Aid,
		MerchantID:   proReq.MerchantID,
		MerchantName: proReq.MerchantName,
		Uid:          proReq.UID,
		UidType:      payeeConf.UidType,
		AppID:        proReq.AppID,
		FcType:       payeeConf.FcType,
		FcSceneCode:  payeeConf.FcSceneCode,
	}
	orderName := order.OrderName
	if utf8.RuneCountInString(orderName) > 20 {
		orderName = fmt.Sprintf("%s...", string([]rune(orderName)[:20]))
	}
	rpcReq := &payment.TransferReq{
		Identity:          e.GetBizIdentity(),
		MerchantID:        payerConf.MerchantID,
		AppID:             payerConf.AppID,
		MerchantName:      payerConf.MerchantName,
		UID:               payerConf.Uid,
		UIDType:           payerConf.UidType,
		FcType:            conv.StringPtr(payerConf.FcType),
		FcSceneCode:       conv.Int64Ptr(payerConf.FcSceneCode),
		FinanceOrderID:    finance.FinanceOrderID,
		TransferOrderNo:   finance.FinanceOrderID,
		TransferOrderName: orderName,
		TransferOrderDesc: proReq.Reason, // 转账描述
		TradeTime:         time.Now().Unix(),
		Currency:          payment.CurrencyTypePtr(payment.CurrencyType_CNY),
		Amount:            proReq.Amount,
		PayerInfo:         e.packParticipant(payerConf),
		PayeeInfo:         e.packParticipant(payeeConf),
		Extra:             nil,
		Operator:          e.GetActionOrderReq().GetOperator(),
	}
	return rpcReq, nil
}

func (e *WithdrawExecution) packParticipant(conf *car_supply_model.ConfCompany) *payment.Participant {
	return &payment.Participant{
		IdentifyType: conf.IdentifyType,
		Aid:          nil,
		MerchantID:   conf.MerchantID,
		UID:          conf.Uid,
		UIDType:      int32(conf.UidType),
		AppID:        conf.AppID,
	}
}
