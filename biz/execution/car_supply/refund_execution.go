package car_supply

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/model/car_supply_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
)

type NormalRefundExecution struct {
	*executor.ActionBaseExecution
	bizReq car_supply_model.RefundReq
	bizRsp execution_common.RefundRsp
}

func NewNormalRefundExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &NormalRefundExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.bizReq)
	return e
}

func (e *NormalRefundExecution) CheckParams(ctx context.Context) error {
	var bizReq = e.bizReq
	if bizReq.RefundList == nil || len(bizReq.RefundList) == 0 {
		bizErr := errdef.NewParamsErr("RefundList 参数错误")
		return bizErr
	}
	for _, refund := range bizReq.RefundList {
		if refund.Amount <= 0 {
			bizErr := errdef.NewParamsErr("RefundList.amount 参数错误")
			return bizErr
		}
	}
	return nil
}

func (e *NormalRefundExecution) Process(ctx context.Context) error {

	var (
		cashRefundReq *payment.MergeRefundReq
		mergeRefundNo string
		bizErr        *errdef.BizErr
		orderID       = e.GetActionOrderReq().GetOrderID()
		orderService  = service.NewOrderService()
		updateParams  = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)

	// 驱动
	bizErr = e.FireWithCondition(ctx, buildCondition(e.GetOrder()))
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))

	// refund req
	cashRefundReq, bizErr = e.buildRefundReq()
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr = service.NewTradePayment().MergeRefund(ctx, cashRefundReq)
	if bizErr != nil {
		return bizErr
	}

	// 2。更新tag
	if len(e.bizReq.Tag) > 0 {
		bizErr = orderService.UpdateOrderTag(ctx, orderID, e.bizReq.Tag)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.bizRsp = execution_common.RefundRsp{
		MergeRefundNo: mergeRefundNo,
	}

	return nil
}

func (e *NormalRefundExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *NormalRefundExecution) buildRefundReq() (*payment.MergeRefundReq, *errdef.BizErr) {
	var (
		order    = e.GetOrder()
		orderID  = e.GetOrder().FweOrder.OrderID
		fweOrder = order.FweOrder
		bizReq   = e.bizReq
	)

	refundList := make([]*payment.SingleRefund, 0, len(bizReq.RefundList))

	for _, refund := range bizReq.RefundList {
		financeOrder := packer.FinanceGetByType(order.FinanceList, refund.FinanceOrderType)
		if financeOrder == nil {
			return nil, errdef.NewRawErr(errdef.DataErr, "找不到对应的资金单")
		}
		refundList = append(refundList, &payment.SingleRefund{
			FinanceOrderID: financeOrder.FinanceOrderID,
			Amount:         refund.Amount,
		})
	}

	serviceReq := &payment.MergeRefundReq{
		Identity:          e.GetBizIdentity(),
		RefundList:        refundList,
		OrderID:           orderID,
		RefundFinanceType: bizReq.RefundType,
		OrderName:         fweOrder.OrderName,
		Reason:            bizReq.Reason,
		Extra:             nil,
		CallbackEvent:     utils.MakeCallbackEvent(bizReq.CallbackAction),
		CallbackExtra:     "",
		IPAddress:         bizReq.IPAddress,
	}
	if len(e.bizReq.RefundExtra) > 0 {
		serviceReq.Extra = e.bizReq.RefundExtra
	}
	return serviceReq, nil
}
