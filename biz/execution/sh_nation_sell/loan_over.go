package sh_nation_sell

import (
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type loanOverExecution struct {
	*executor.ActionBaseExecution
	param *sh_nation_sell_model.LoanOverModel
	conf  *sh_nation_sell_model.Conf
}

func NewLoanOverExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	e := &loanOverExecution{
		param: new(sh_nation_sell_model.LoanOverModel),
		conf:  new(sh_nation_sell_model.Conf),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.param, options...)
	return e
}

func (e *loanOverExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	return nil
}

// Process 贷款完成
func (e *loanOverExecution) Process(ctx context.Context) error {
	return nil
}
