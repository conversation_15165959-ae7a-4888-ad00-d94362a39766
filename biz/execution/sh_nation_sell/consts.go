package sh_nation_sell

import "code.byted.org/gopkg/env"

const (
	LoanFirst = 1 // 先贷款后过户
	LoanAfter = 2 // 先过户后贷款

	LoanFirstStr = "loan_first"
	LoanAfterStr = "loan_after"

	LoanFirstName = "先贷款后过户"
	LoanAfterName = "先过户后贷款"

	HasOrderLoanExistStr = "true"
	OrderLoanStatusSucc  = 1
	OrderLoanStatusFail  = 2

	OrderLoanSuccess = "success"
	OrderLoanFail    = "fail"
)

const (
	OneHundredScale = 1 * 1e4
)

const (
	IntentContract    = int32(1) // 意向合同
	SaleContract      = int32(2) // 买卖合同
	InsuranceContract = int32(3) // 延保合同
	AfterSaleContract = int32(4) // 售后合同
)

// tag 名称
const (
	LoanType                 = "infra_loan_type"                   // 贷款类型 值为loan_first和loan_after
	LoanDetail               = "infra_loan_detail"                 // 贷款详情 值为LoanConfirmModel序列化后的值
	HasOrderLoan             = "infra_has_order_loan"              // 是否存在订单贷 true false
	OrderLoanStatus          = "infra_order_loan_status"           // 订单贷状态 success fail
	OrderLoanAmount          = "infra_order_loan_amount"           // 订单贷金额 当该值存在时，需要触发订单贷放款
	OrderLoanRealAmount      = "infra_order_loan_real_amount"      // 订单贷放款金额
	OrderLoanRealDate        = "infra_order_loan_real_date"        // 订单贷放款日期
	OrderLoanRepaymentAmount = "infra_order_loan_repayment_amount" // 订单贷还款金额
	OrderLoanRepaymentDate   = "infra_order_loan_repayment_date"   // 订单贷还款日期
)

// 飞书相关
const (
	LarkAppID                = "********************"
	LarkAppSecret            = "Vd0GipsjBmNbv7DQhmeY8eBSF3s8ZmwB"
	LarkAppVerificationToken = "f19MAp2kHpCGVh1XkYlfEdQ20i3v65SL"
	LarkEncryptKey           = "PYawhWlfpv0Chf8MtlQdqLnB2Smh1kc2"
)

func getOrderLoanApproveCode() string {
	if env.IsBoe() {
		return OrderLoanApproveCodeBoe
	}
	return OrderLoanApproveCodeProd
}

// 审批单字段id
const (
	ApproveName        = "审批"
	OutBankAccountName = "out_bank_account_name"
	OutBankBranch      = "out_bank_branch"
	OutBankAccountNo   = "out_bank_account_no"
	CarVin             = "car_vin"
	TradeStructure     = "trade_structure"
	BorrowerName       = "borrower_name"
	LoanAmount         = "loan_amount"
	ShopName           = "shop_name"
	FinanceName        = "finance_name"
	BizOrderId         = "biz_order_id"
)

const (
	OrderCancelRefundFinanceOrderType = 51
	OrderOverFinanceOrderType         = 52
)

// 订单贷
const (
	OrderLoanApproveCodeBoe  = "58D6AC60-BF4A-4D37-8A30-C14B2F7898B6"
	OrderLoanApproveCodeProd = "5B0D47A1-A281-41C7-8574-F99D7A329358"

	OpenID                   = "open_id"
	OrderLoanApproveName     = "订单贷"
	OrderLoanTag             = "order_loan_tag"
	OrderLoanApproveCodeName = "audit_approve_code"

	OaContractId    = "oa_contract_id"
	ShopAccountName = "shop_account_name"
	ShopAccountNo   = "shop_account_no"
	ShopAccountBank = "shop_account_bank"
	TradeAmount     = "trade_amount"
	StatusDesc      = "status_desc"
	BuyerName       = "buyer_name"
	ShVINCode       = "sh_vin_code"
	MoveInCity      = "sh_move_in_city"
	MoveOutCity     = "sh_move_out_city"
	DeliveryCity    = "sh_deliver_city"
	SalesName       = "sales_name"
	CarSourceBdName = "car_source_bd_name"
	AuditRemark     = "audit_remark"
	CarInfo         = "sh_car_info"
	ContSignTime    = "cont_sign_time"
)

const (
	EstimateSubsidyAmount = int64(*********) // 全国购补贴，预估补贴金额，100w
)
