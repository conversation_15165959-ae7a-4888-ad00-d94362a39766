package sh_nation_sell

import (
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop"
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

func CheckActionReq(ctx context.Context, actionReq *engine.ActionOrderReq) *errdef.BizErr {
	if actionReq == nil || actionReq.Identity == nil || actionReq.Identity.TenantType == 0 ||
		actionReq.Identity.BizScene == 0 || actionReq.OrderID == "" || actionReq.Action == "" {
		logs.CtxWarn(ctx, "[CheckActionReq] param empty")
		return errdef.NewParamsErr("有必传参数为空，请检查")
	}
	return nil
}

// GetShSellCondition 获取状态机条件
func GetShSellCondition(ctx context.Context, order *service_model.Order, loanTypeParam int32) map[string]interface{} {
	var (
		hasLoan, isLoanFirst, hasOrderLoan, orderLoanOver bool
		finalPrice                                        int64
	)

	hasLoan = HasLoan(order)
	isLoanFirst = IsLoanFirst(order)
	hasOrderLoan = OrderLoanExist(order)
	orderLoanOver = OrderLoanIsOver(order)

	if loanTypeParam == LoanFirst {
		isLoanFirst = true
	}

	if finalFianceOrder := packer.FinanceGetByType(order.FinanceList, CommonConsts.FinanceFinal.Value()); finalFianceOrder != nil {
		finalPrice = finalFianceOrder.Amount
	}

	return map[string]interface{}{
		consts.CondShSellHasLoan.Val():       hasLoan,
		consts.CondShSellIsLoanFirst.Val():   isLoanFirst,
		consts.CondShSellFinalPrice.Val():    finalPrice,
		consts.CondShSellHasOrderLoan.Val():  hasOrderLoan,
		consts.CondShSellOrderLoanOver.Val(): orderLoanOver,
	}
}

func HasLoan(order *service_model.Order) bool {
	var fweOrder = order.FweOrder
	if fweOrder.TradeType == int32(fwe_trade_common.TradeType_EarnestFinalLoan) ||
		fweOrder.TradeType == int32(fwe_trade_common.TradeType_FinalLoan) {
		return true
	}
	return false
}

func OrderLoanExist(order *service_model.Order) bool {
	var tagMap = order.TagMap
	if orderLoanExist, exist := tagMap[HasOrderLoan]; exist && orderLoanExist == HasOrderLoanExistStr {
		return true
	}
	return false
}

func OrderLoanIsOver(order *service_model.Order) bool {
	var tagMap = order.TagMap
	if _, exist := tagMap[OrderLoanStatus]; exist {
		return true
	}
	return false
}

func IsLoanFirst(order *service_model.Order) bool {
	var tagMap = order.TagMap
	if loanType, exist := tagMap[LoanType]; exist && loanType == LoanFirstStr {
		return true
	}
	return false
}

func GetSellAccountShop(ctx context.Context, order *service_model.Order, merchantID string) (*shop.FinanceAccount, *errdef.BizErr) {
	var (
		bizErr   *errdef.BizErr
		fweOrder = order.FweOrder
	)

	if fweOrder.SellerExtra == nil {
		bizErr = errdef.NewParamsErr("seller info nil")
		logs.CtxWarn(ctx, "[GetSellAccountShop] err=%s", bizErr.Error())
		return nil, bizErr
	}
	sellerInfo, bizErr := packer.CommonTradeSubjectDeserialize(fweOrder.SellerID, *fweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[GetSellAccountShop] err=%s", bizErr.Error())
		return nil, bizErr
	}
	if sellerInfo == nil || sellerInfo.FweMerchant == nil || sellerInfo.FweMerchant.FweAccountID == "" {
		bizErr = errdef.NewParamsErr("fwe account id is nil")
		logs.CtxWarn(ctx, "[GetSellAccountShop] err=%s", bizErr.Error())
		return nil, bizErr
	}

	account, bizErr := service.NewAccountShop().GetFinanceAccountOne(ctx, sellerInfo.FweMerchant.FweAccountID, merchantID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[GetSellAccountShop] GetFinanceAccountOne failed  err=%s", bizErr.Error())
		return nil, bizErr
	}

	return account, nil
}
