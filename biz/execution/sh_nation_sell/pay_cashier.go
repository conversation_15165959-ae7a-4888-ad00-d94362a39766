package sh_nation_sell

import (
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"context"

	"github.com/bytedance/sonic"

	"code.byted.org/gopkg/logs"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
)

type payCashierExecution struct {
	*executor.ActionBaseExecution
	payModel *sh_sell_model.CreateCashPayModel
	result   string
}

func NewPayCashierExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	exe := &payCashierExecution{
		payModel: new(sh_sell_model.CreateCashPayModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.payModel, options...)
	return exe
}

func (e *payCashierExecution) CheckParams(ctx context.Context) error {
	payModel := e.payModel
	if payModel.Identity == nil || payModel.Identity.BizScene == 0 || payModel.Identity.TenantType == 0 ||
		payModel.FinanceType == 0 || payModel.OrderId == "" {
		logs.CtxWarn(ctx, "[payCashierExecution.CheckParams] params empty")
		return errdef.NewParamsErr("param error")
	}
	return nil
}

func (e *payCashierExecution) Process(ctx context.Context) error {
	rpcReq, err := e.buildPayReq(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[payCashierExecution.Process] build pay req failed, err=%+v", err)
		return err
	}

	payOrderNo, payData, bizErr := service.NewTradePayment().CreateCashPay(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[payCashierExecution.Process] pay failed, err=%s", bizErr.Error())
		return bizErr
	}

	res := &sh_sell_model.CreateCashPayResult{
		PayData:    payData,
		PayOrderNo: payOrderNo,
	}

	e.result, err = sonic.MarshalString(res)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		return bizErr
	}
	return nil
}

func (e *payCashierExecution) Result() interface{} {
	return e.result
}

func (e *payCashierExecution) buildPayReq(_ context.Context) (*payment.CreateCashPayReq, error) {
	var (
		req             = e.GetActionOrderReq()
		payModel        = e.payModel
		userId          = "user_id"
		currency        = payment.CurrencyType_CNY
		order           = e.GetOrder()
		finance         = packer.FinanceGetByType(order.FinanceList, payModel.FinanceType)
		callbackAction  string
		timeoutCBAction string
	)

	if payModel.Operator != nil {
		userId = payModel.Operator.OperatorID
	}

	switch req.Identity.BizScene {
	case CommonConsts.BizSceneSHNationInsurance.Value():
		callbackAction = sh_state.SHNationWarrantyPayOverEt.Value()
		timeoutCBAction = sh_state.SHNationWarrantyPayTimeoutEt.Value()
	}

	rpcReq := &payment.CreateCashPayReq{
		Identity:             req.Identity,
		UserID:               userId,
		OrderID:              payModel.OrderId,
		FinanceType:          payModel.FinanceType,
		PayOrderNo:           payModel.PayOrderNo,
		CashierDeskType:      payModel.CashierDeskType,
		OsType:               payModel.OsType,
		Currency:             &currency,
		TotalAmount:          finance.Amount,
		ExpireTime:           payModel.ExpireTime,
		RedirectURL:          payModel.RedirectUrl,
		IPAddress:            payModel.IpAddress,
		PayLimitList:         payModel.PayLimitList,
		CallbackEvent:        utils.MakeCallbackEvent(callbackAction),
		TimeoutCallbackEvent: utils.MakeCallbackEvent(timeoutCBAction),
		Extra:                nil,
	}

	return rpcReq, nil
}
