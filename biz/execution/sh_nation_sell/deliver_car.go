package sh_nation_sell

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type DeliverCarExecution struct {
	*executor.ActionBaseExecution
	conf  *sh_nation_sell_model.Conf
	param *sh_nation_sell_model.DeliveryCarModel
}

func NewDeliverCarExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &DeliverCarExecution{
		conf:  new(sh_nation_sell_model.Conf),
		param: new(sh_nation_sell_model.DeliveryCarModel),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), e.conf, e.param)
	return e
}

func (e *DeliverCarExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	if e.param == nil {
		logs.CtxWarn(ctx, "[DeliverCarExecution.CheckParams] param error")
		return errdef.NewParamsErr("缺少交车参数")
	}

	return nil
}

func (e *DeliverCarExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditions := GetShSellCondition(ctx, e.GetOrder(), 0)
	e.SetFSMCondition(conditions)
	err = e.FireFSM(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

func (e *DeliverCarExecution) Process(ctx context.Context) error {
	var (
		order = e.GetOrder()
	)

	// 订单贷存在并且订单贷未结束 直接跳出
	if OrderLoanExist(order) && !OrderLoanIsOver(order) {
		logs.CtxInfo(ctx, "[DeliverCarExecution.Process] 订单贷存在并且订单贷未结束, 不需要分账")
		return nil
	}

	// 否则进行分账
	settleParam, bizErr := e.buildSettleReq(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[CancelWithRefundCallbackExecution] buildSettleReq failed, err is %v", bizErr)
		return bizErr
	}

	if bizErr = NewOrderSettleExecution(settleParam).Settle(ctx); bizErr != nil {
		logs.CtxError(ctx, "[CancelWithRefundCallbackExecution] MergeSettle failed, err is %v", bizErr)
		return bizErr
	}

	return nil
}

// 普通构建
func (e *DeliverCarExecution) buildSettleReq(ctx context.Context) (*CreateSettleParam, *errdef.BizErr) {
	var (
		order          = e.GetOrder()
		conf           = e.conf
		normalMerchant = conf.NormalPayMerchant
		settleList     = make([]*SingleSettle, 0)
	)

	for _, financeOrder := range order.FinanceList {
		settleList = append(settleList, &SingleSettle{
			FinanceOrderID: financeOrder.FinanceOrderID,
			Amount:         financeOrder.ProcessAmount + financeOrder.LoanAmount,
			SubsidyAmount:  financeOrder.LoanAmount,
		})
	}

	// 选卖家作为分账补贴方
	sellerAccount, bizErr := GetSellAccountShop(ctx, order, conf.NormalPayMerchant.MerchantID)
	if bizErr != nil {
		return nil, bizErr
	}

	settleParam := &CreateSettleParam{
		Order:               order,
		MerchantID:          normalMerchant.MerchantID,
		HelperFweAccountUID: normalMerchant.HelperFweAccountID,
		PlatformUID:         normalMerchant.PlatformUID,
		ReceiveUID:          sellerAccount.GetUID(),
		ReceiveUIDType:      consts.ShopUidType,
		SettleFinanceType:   OrderOverFinanceOrderType,
		SettleDesc:          "订单完成进行分账",
		SettleList:          settleList,
	}

	return settleParam, nil
}

func (e *DeliverCarExecution) PostProcess(ctx context.Context) error {
	// 更新订单tag
	if bizErr := service.NewOrderService().UpdateOrderTag(ctx, e.GetOrder().FweOrder.OrderID, e.param.GetOrderTag()); bizErr != nil {
		return bizErr
	}

	return e.ActionBaseExecution.PostProcess(ctx)
}
