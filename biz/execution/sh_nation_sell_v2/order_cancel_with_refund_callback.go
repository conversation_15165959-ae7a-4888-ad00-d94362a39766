package sh_nation_sell_v2

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"github.com/bytedance/sonic"
)

type CancelWithRefundCallbackExecution struct {
	*callback.RefundCallbackBaseExecution
	confPtr *sh_nation_sell_model.ConfV2

	refundFinanceInfoMap map[string]*payment.SingleRefund
	refundList           []*payment.SingleRefund
}

func NewCancelWithRefundCallbackExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}, {OptionID: executor.OptionAutoFinish}}
	e := &CancelWithRefundCallbackExecution{
		confPtr: new(sh_nation_sell_model.ConfV2),
	}
	e.RefundCallbackBaseExecution = callback.NewRefundCallbackBaseExecution(ctx, sourceReq.(*engine.ActionOrderReq), e.confPtr, options...)
	return e
}

func (e *CancelWithRefundCallbackExecution) PreProcess(ctx context.Context) error {
	var (
		refundFinanceInfoMap = make(map[string]*payment.SingleRefund)
	)
	if err := e.ActionBaseExecution.PreProcess(ctx); err != nil {
		return err
	}

	if e.CbModel.FinanceOrderType != OrderCancelRefundFinanceOrderType {
		return errdef.NewParamsErr("未知的退款资金单")
	}

	if err := sonic.UnmarshalString(e.CbModel.Extra, &e.refundList); err != nil {
		return errdef.NewBizErr(errdef.DataErr, err, "回调参数解析出错")
	}

	for _, refundFinanceInfo := range e.refundList {
		refundFinanceInfoMap[refundFinanceInfo.FinanceOrderID] = refundFinanceInfo
	}

	logs.CtxInfo(ctx, "[CancelWithRefundCallbackExecution] refund info is %s", tools.GetLogStr(refundFinanceInfoMap))

	e.refundFinanceInfoMap = refundFinanceInfoMap
	return nil
}

func (e *CancelWithRefundCallbackExecution) Process(ctx context.Context) error {
	return nil
}
