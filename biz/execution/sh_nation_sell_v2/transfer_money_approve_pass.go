package sh_nation_sell_v2

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_nation_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type transferMoneyApprovePassExecution struct {
	*executor.ActionBaseExecution
	dataModel *sh_nation_sell_model.TransferMoneyApprovePassModel
	conf      *sh_nation_sell_model.ConfV2
}

func NewTransferMoneyApprovePassExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &transferMoneyApprovePassExecution{
		dataModel: new(sh_nation_sell_model.TransferMoneyApprovePassModel),
		conf:      new(sh_nation_sell_model.ConfV2),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), e.conf, e.dataModel)
	return e
}

func (e *transferMoneyApprovePassExecution) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}

	if e.dataModel == nil || e.dataModel.OrderID == "" || e.dataModel.Amount == 0 {
		return errdef.NewParamsErr("缺少确认订单参数")
	}

	if !slices.Contains([]int32{CommonConsts.FinanceAdvance.Value(), CommonConsts.FinanceFinal.Value()}, e.dataModel.FinanceType) {
		return errdef.NewRawErr(errdef.FinanceTypeErr, "资金单类型不是首付款或尾款")
	}

	return nil
}

func (e *transferMoneyApprovePassExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	conditions := GetShSellCondition(ctx, e.GetOrder(), 0)
	e.SetFSMCondition(conditions)
	err = e.FireFSM(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%+v", err)
		return err
	}
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return nil
}

func (e *transferMoneyApprovePassExecution) Process(ctx context.Context) error {
	var (
		order       = e.GetOrder()
		dataModel   = e.dataModel
		financeType = dataModel.FinanceType
		curState    = e.GetStateMachine().GetOriginalState()
	)

	// 校验当前资金单是否满足当前状态
	payAdvanceStates := []int{
		sh_state.SHNationSellV2ToPayAdvanceMoneySt.Value(),
		sh_state.SHNationSellV2PayingAdvanceMoneySt.Value(),
	}
	payFinalStates := []int{
		sh_state.SHNationSellV2ToPayFinalMoneySt.Value(),
		sh_state.SHNationSellV2PayingFinalMoneySt.Value(),
	}
	if (financeType == CommonConsts.FinanceAdvance.Value() && !slices.Contains(payAdvanceStates, curState)) ||
		(financeType == CommonConsts.FinanceFinal.Value() && !slices.Contains(payFinalStates, curState)) {
		logs.CtxError(ctx, "[transferMoneyApprovePassExecution] finance_type error")
		return errdef.NewRawErr(errdef.FinanceTypeErr, "当前资金单类型和状态不符合")
	}

	financeOrder := packer.FinanceGetByType(order.FinanceList, e.dataModel.FinanceType)
	if financeOrder == nil || financeOrder.Amount != dataModel.Amount {
		logs.CtxError(ctx, "[transferMoneyApprovePassExecution] finance_type or amount not consistent")
		return errdef.NewRawErr(errdef.AmountNotConsistErr, "金额和资金单金额不一致")
	}

	// 如果是POS支付，需要校验：已经支付成功了一笔，则不允许再接收大额转账审批通过的消息
	payList, bizErr := e.queryPayList(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[transferMoneyApprovePassExecution] query pay list failed, err=%s", bizErr.Error())
		return bizErr
	}
	statusList := []fwe_trade_common.CommonStatus{fwe_trade_common.CommonStatus_ToHandle, fwe_trade_common.CommonStatus_Handling,
		fwe_trade_common.CommonStatus_Success}
	for _, pay := range payList {
		if slices.Contains(statusList, pay.Status) && pay.TradeType == consts.TradeTypePayPos.String() {
			logs.CtxError(ctx, "[transferMoneyApprovePassExecution] exist pay, won't accept transfer")
			return errdef.NewRawErr(errdef.DataErr, "存在在途或已完成的POS支付单，不能转账")
		}
	}

	// 修改资金单trade_type为withdraw_bank， 并更新资金单状态为"已完成"
	bizErr = service.NewFinanceOrderService().UpdateOrderFinance(ctx, financeOrder.FinanceOrderID, &service_model.UpdateFinanceParams{
		UpdateTradeType:     conv.StringPtr(consts.TradeTypeWithdrawBank.String()),
		UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Complete)),
	})
	if bizErr != nil {
		logs.CtxWarn(ctx, "[transferMoneyApprovePassExecution] update finance_order failed, err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *transferMoneyApprovePassExecution) PostProcess(ctx context.Context) error {
	// 更新订单tag
	//if bizErr := service.NewOrderService().UpdateOrderTag(ctx, e.GetOrder().FweOrder.OrderID, e.dataModel.GetOrderTag()); bizErr != nil {
	//	return bizErr
	//}
	if _, bizErr := service.NewTagService().UpdateTag(ctx, e.GetOrder().FweOrder.OrderID, e.GetOrder().FweOrder.BizScene, e.dataModel.GetOrderTag()); bizErr != nil {
		logs.CtxError(ctx, "[transferMoneyApprovePassExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return e.ActionBaseExecution.PostProcess(ctx)
}

func (e *transferMoneyApprovePassExecution) queryPayList(ctx context.Context) ([]*payment.FinancePay, *errdef.BizErr) {
	var (
		actionReq   = e.GetActionOrderReq()
		order       = e.GetOrder()
		dataModel   = e.dataModel
		financeType = dataModel.FinanceType
	)
	req := &payment.QueryFinancePayListReq{
		Identity:    actionReq.Identity,
		OrderID:     order.FweOrder.OrderID,
		FinanceType: financeType,
		Base:        base.NewBase(),
	}
	payList, bizErr := service.NewTradePayment().QueryFinancePayList(ctx, req)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[queryPayList] query pay list failed, err=%s", bizErr)
		return nil, bizErr
	}
	return payList, nil
}
