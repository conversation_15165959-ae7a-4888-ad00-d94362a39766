package sh_auction_car_history

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/motor/fwe_trade_common/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
)

var (
	// yzt支付默认
	yztChannels = []finance_account.TradeChannel{
		finance_account.TradeChannel_yzt_hz,     // 合众
		finance_account.TradeChannel_yzt_alipay, // 支付宝
		finance_account.TradeChannel_syt_wx,     // 微信
	}
)

type CarHistoryOrderCreateExecution struct {
	*executor.CreateBaseExecution
	conf                sh_sell_model.Conf
	yztSubMerchantInfos []*finance_account.SubMerchantInfo
}

func NewCarHistoryOrderCreateExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.CreateOrderReq)
	e := &CarHistoryOrderCreateExecution{}
	e.conf = sh_sell_model.Conf{}
	e.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, req, &e.conf)
	return e
}

func (e *CarHistoryOrderCreateExecution) CheckParams(ctx context.Context) error {
	var (
		req = e.GetCreateOrderReq()
	)
	// 资金风控检查
	if bizErr := utils.CheckFundRiskOfAmount(ctx, req.IsTest, req.GetTotalAmount(), e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[CarHistoryOrderCreateExecution-CheckParams] CheckFundRiskOfAmount error, err = %v ", bizErr.Error())
		return bizErr
	}
	// 验证 账户
	bizErr := e.validFinanceAccount(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CarHistoryOrderCreateExecution-CheckParams] validFinanceAccount error , err = %v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *CarHistoryOrderCreateExecution) PreProcess(ctx context.Context) error {
	err := e.CreateBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[CarHistoryOrderCreateExecution-PreProcess] PreProcess error, err = %v", err)
		return err
	}
	return nil
}

func (e *CarHistoryOrderCreateExecution) Process(ctx context.Context) error {

	var (
		req          = e.GetCreateOrderReq()
		fweOrder     *db_model.FweOrder
		financeList  []*db_model.FFinanceOrder
		totalAmount  = req.TotalAmount
		buyerID      = packer.CommonTradeSubjectIDGet(req.BuyerInfo)
		buyerExtra   = packer.CommonTradeSubjectSerialize(req.BuyerInfo)
		sellerID     = packer.CommonTradeSubjectIDGet(req.SellerInfo)
		sellerExtra  = packer.CommonTradeSubjectSerialize(req.SellerInfo)
		stateMachine = e.GetStateMachine()
	)

	// 驱动状态
	matchParams := make(map[string]interface{})
	if val, ok := req.GetOrderTag()["pay_type"]; ok {
		matchParams["pay_type"] = conv.Int64Default(val, 0)
	}
	err := stateMachine.Fire(ctx, statemachine.OrderCreateEt.Value(), matchParams)
	if err != nil {
		bizErr := errdef.NewBizErr(errdef.ServerException, err, "CarHistoryOrderCreateExecution")
		logs.CtxError(ctx, "[CarHistoryOrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 下单参数pack
	var isTest int32
	if req.IsTest {
		isTest = 1
	}
	fweOrder = &db_model.FweOrder{
		TenantType:         int32(req.GetIdentity().GetTenantType()),
		BizScene:           req.GetIdentity().BizScene,
		SmVersion:          req.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(e.GetStateMachine().CurState()), // 初始态
		OrderName:          req.GetOrderName(),
		OrderDesc:          req.GetOrderDesc(),
		ProductID:          req.ProductInfo.ProductID,
		ProductType:        int32(req.ProductInfo.ProductType),
		ProductName:        req.ProductInfo.ProductName,
		ProductExtra:       req.ProductInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(req.ProductInfo.ProductDetail)),
		SkuID:              req.ProductInfo.SkuID,
		ProductQuantity:    int32(req.ProductInfo.ProductQuantity),
		ProductUnitPrice:   req.ProductInfo.ProductUnitPrice,
		TotalAmount:        totalAmount,
		TotalPayAmount:     totalAmount,
		TotalSubsidyAmount: 0,
		TradeType:          int32(req.TradeType),
		BuyerID:            buyerID,
		BuyerExtra:         &buyerExtra,
		SellerID:           sellerID,
		SellerExtra:        &sellerExtra,
		IsTest:             isTest,
		IdempotentID:       req.GetIdemID(),
		Creator:            req.GetOperator().GetOperatorID(),
		CreatorName:        req.GetOperator().GetOperatorName(),
		Operator:           req.GetOperator().GetOperatorID(),
		OperatorName:       req.GetOperator().GetOperatorName(),
	}

	for _, financeInfo := range req.FinanceList {
		financeOrderID, bizErr := utils.TryGenId(3)
		if bizErr != nil {
			logs.CtxError(ctx, "[CarHistoryOrderCreateExecution] gen id error, err=%+v", err)
			return bizErr
		}
		financeOrder := &db_model.FFinanceOrder{
			TenantType:       int32(req.GetIdentity().GetTenantType()),
			BizScene:         req.GetIdentity().BizScene,
			AppID:            "",
			MerchantID:       "",
			Mid:              "",
			OrderID:          e.GetOrderID(),
			OrderName:        req.OrderName,
			TradeType:        financeInfo.TradeType,
			TradeCategory:    int32(fwe_trade_common.TradeCategory_Pay),
			FinanceOrderID:   utils.MakeFinanceOrderID(financeOrderID, financeInfo.FinanceOrderType),
			FinanceOrderType: financeInfo.FinanceOrderType,
			Amount:           financeInfo.Amount,
			ProcessAmount:    0,
			Status:           int32(fwe_trade_common.FinanceStatus_NotHandle),
			FeeItemDetail:    conv.StringPtr(tools.GetLogStr(financeInfo.FeeItemList)),
			LoanAmount:       financeInfo.LoanAmount,
		}
		financeList = append(financeList, financeOrder)
	}

	order := &service_model.Order{
		FweOrder:       fweOrder,
		FinanceList:    financeList,
		TagMap:         req.OrderTag,
		BizExtra:       req.Extra,
		TradeSplitInfo: req.SplitInfo,
	}
	bizErr := service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[CarHistoryOrderCreateExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *CarHistoryOrderCreateExecution) Result() interface{} {
	return e.GetOrderID()
}

func (e *CarHistoryOrderCreateExecution) validFinanceAccount(ctx context.Context) *errdef.BizErr {
	var (
		req          = e.GetCreateOrderReq()
		fweAccountID = e.GetCreateOrderReq().SellerInfo.GetFweMerchant().FweAccountID
		yztConfig    = e.conf.YZTPayMerchant
	)
	if yztConfig == nil || yztConfig.MerchantID == "" {
		logs.CtxWarn(ctx, "[CarHistoryOrderCreateExecution-validFinanceAccount] config error, config = %v", tools.GetLogStr(e.conf))
		return errdef.NewRawErr(errdef.LackConfigErr, "yztConfig/posConfig config error")
	}
	fweSubMerchantInfo, bizErr := service.NewFinanceAccountService().GetFweSubMerchantInfo(ctx, req.Identity.TenantType, fweAccountID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CarHistoryOrderCreateExecution-validFinanceAccount] GetFweSubMerchantInfo error, err = %v", bizErr.Error())
		return bizErr
	}
	// 验证 yzt
	yztSubMerchantChannels, bizErr := e.checkFinanceAccount(yztConfig.MerchantID, fweSubMerchantInfo, yztChannels)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CarHistoryOrderCreateExecution-validFinanceAccount] checkFinanceAccount yzt-account error, err = %v", bizErr.Error())
		return bizErr
	}
	e.yztSubMerchantInfos = yztSubMerchantChannels

	return nil
}

func (e *CarHistoryOrderCreateExecution) checkFinanceAccount(merchantId string, fweSubMerchantInfo map[string][]*finance_account.SubMerchantInfo, validChannels []finance_account.TradeChannel) ([]*finance_account.SubMerchantInfo, *errdef.BizErr) {
	channels, exist := fweSubMerchantInfo[merchantId]
	if !exist {
		return nil, errdef.NewParamsErr("this seller dont owner channels")
	}
	validRes := slices.Filter(channels, func(dto *finance_account.SubMerchantInfo) bool {
		if slices.Contains(validChannels, dto.TradeChannel) && dto.ChannelStatus == finance_account.ChannelStatus_Ready {
			return true
		}
		return false
	}).([]*finance_account.SubMerchantInfo)
	if len(validRes) == 0 {
		return nil, errdef.NewParamsErr("this seller`s all channels is not ready")
	}
	return validRes, nil
}
