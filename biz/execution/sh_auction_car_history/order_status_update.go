package sh_auction_car_history

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools/tools_recover_kite"
)

type CarHistoryOrderStaticUpdateExecution struct {
	*executor.StaticBaseExecution
	conf  sh_sell_model.Conf
	param *engine.CreateOrderReq
}

func NewCarHistoryOrderStaticUpdateExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &CarHistoryOrderStaticUpdateExecution{
		param: new(engine.CreateOrderReq),
	}
	e.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param)
	return e
}

func (e *CarHistoryOrderStaticUpdateExecution) CheckParams(ctx context.Context) error {
	actionReq := e.GetActionOrderReq()
	if actionReq == nil || actionReq.Identity == nil || actionReq.Identity.TenantType == 0 ||
		actionReq.Identity.BizScene == 0 || actionReq.OrderID == "" || actionReq.Action == "" {
		logs.CtxWarn(ctx, "[CarHistoryOrderStaticUpdateExecution-CheckParams] param empty")
		return errdef.NewParamsErr("有必传参数为空，请检查")
	}
	if e.param == nil {
		return errdef.NewParamsErr("缺少更新订单参数")
	}
	var req = e.param
	// 资金风控检查
	if bizErr := utils.CheckFundRiskOfAmount(ctx, req.IsTest, req.GetTotalAmount(), e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[CarHistoryOrderStaticUpdateExecution-CheckParams] CheckFundRiskOfAmount error, err = %v ", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *CarHistoryOrderStaticUpdateExecution) PreProcess(ctx context.Context) error {
	err := e.StaticBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[CarHistoryOrderStaticUpdateExecution-PreProcess] preProcess failed, err=%+v", err)
		return err
	}
	return nil
}

func (e *CarHistoryOrderStaticUpdateExecution) Process(ctx context.Context) error {
	var (
		orderService = service.NewOrderService()
		tagService   = service.NewTagService()
		buyerInfo    = e.param.BuyerInfo
		order        = e.GetOrder()
		bizErr       *errdef.BizErr
		updateOrder  = e.param
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		updateParams = &service_model.UpdateOrderParams{}
	)

	if len(updateOrder.GetExtra()) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.GetExtra())
		if bizErr != nil {
			logs.CtxError(ctx, "[CarHistoryOrderStaticUpdateExecution] UpdateOrderExtraMarshal failed, err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(updateOrder.OrderTag) > 0 || len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(updateOrder.OrderTag) > 0 {
			for key, value := range updateOrder.OrderTag {
				tagMap[key] = value
			}
		}
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		_, bizErr = tagService.UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[CarHistoryOrderStaticUpdateExecution] UpdateTag failed, err=%s", bizErr.Error())
			return bizErr
		}
	}

	if updateOrder.Operator != nil {
		updateParams.Operator = updateOrder.Operator
	}

	bizErr = orderService.UpdateOrder(ctx, order.FweOrder.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[CarHistoryOrderStaticUpdateExecution] UpdateOrder failed, err=%s", bizErr.Error())
		return bizErr
	}

	// 更新 ebs 数据
	go func() {
		defer func() {
			tools_recover_kite.CheckRecover(ctx, recover(), nil)
		}()
		err := orderService.CreateOrUpdateOrderSubject(ctx, []*fwe_trade_common.TradeSubjectInfo{buyerInfo})
		if err != nil {
			logs.CtxError(ctx, "[CarHistoryOrderStaticUpdateExecution] CreateOrUpdateOrderSubject failed, err = %v", err.Error())
		}
	}()
	return nil

}
