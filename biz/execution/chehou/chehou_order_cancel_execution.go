package chehou

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/pack_airpass/rpc/motor_fwe_trade_order"
	"code.byted.org/overpass/common/option/calloption"
	"code.byted.org/overpass/motor_fwe_trade_payment/rpc/motor_fwe_trade_payment"
	"context"
)

type ChOrderCancelExecution struct {
	*executor.ActionBaseExecution
}

func NewChOrderCancelExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &ChOrderCancelExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, nil)
	e.AppendOption(&executor.Option{OptionID: executor.OptionAutoFire})
	return e
}

func (e *ChOrderCancelExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		return err
	}
	if len(e.GetOrder().FinanceList) > 0 {
		for _, financeOrder := range e.GetOrder().FinanceList {
			if financeOrder.Status == int32(fwe_trade_common.FinanceStatus_Handling) ||
				financeOrder.Status == int32(fwe_trade_common.FinanceStatus_Complete) {
				return errdef.NewParamsErr("订单存在处理中或已完成的资金单, 不可直接取消")
			}
		}
	}
	return nil
}

func (e *ChOrderCancelExecution) Process(ctx context.Context) error {
	// 关闭聚合支付单+易宝支付单
	resp, err := motor_fwe_trade_order.QueryFinanceModelByOrderID(ctx, e.GetOrder().FweOrder.OrderID)
	if err != nil {
		logs.CtxError(ctx, "[Process] QueryFinanceModelByOrderID err: %+v", err)
		return err
	}
	// 拉起收银台就生成资金单
	if resp.OrderFinanceModel == nil {
		logs.CtxInfo(ctx, "[Process] order_id= %s finance_model is nil", e.GetOrder().FweOrder.OrderID)
		return nil
	}

	var payUnionNo string
	for _, info := range resp.GetOrderFinanceModel().GetPayUnionOrderList() {
		if fwe_trade_common.CommonStatus(info.Status) == fwe_trade_common.CommonStatus_Handling || fwe_trade_common.CommonStatus(info.Status) == fwe_trade_common.CommonStatus_ToHandle {
			payUnionNo = info.PayUnionNo
			break
		}
	}
	// 拉起收银台就生成聚合支付单
	if payUnionNo == "" {
		logs.CtxInfo(ctx, "[Process] order_id= %s union_pay is nil", e.GetOrder().FweOrder.OrderID)
		return nil
	}
	if _, err := motor_fwe_trade_payment.CloseUnionPay(ctx, payUnionNo, 0, calloption.WithReqRespLogsInfo()); err != nil {
		logs.CtxError(ctx, "[ChOrderCancelExecution] CloseUnionPay err:%v", err)
		return err
	}
	return nil
}
