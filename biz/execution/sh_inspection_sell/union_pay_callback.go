package sh_inspection_sell

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/condition"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"time"
)

type UnionPayCallbackBaseExecution struct {
	*executor.ActionBaseExecution
	cbModel *callback_model.UnionPayCallbackModel
}

func NewInspectionUnionPayCallbackExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &UnionPayCallbackBaseExecution{
		cbModel: new(callback_model.UnionPayCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.cbModel)
	return exe
}

func (e *UnionPayCallbackBaseExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[UnionPayCallbackBaseExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}
	var calCondition map[string]interface{}
	conditionFunc := condition.NewOrderConditionFuncFactory().GetActionOrderConditionFunc(ctx, e.GetActionOrderReq())
	if conditionFunc != nil {
		calCondition = conditionFunc(e.BuildConditionParam())
	}
	bizErr := e.FireWithCondition(ctx, calCondition)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionPayCallbackBaseExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *UnionPayCallbackBaseExecution) Process(ctx context.Context) error {
	var (
		order          = e.GetOrder()
		cbModel        = e.cbModel
		financeOrderID = cbModel.OutID
		status         = cbModel.Status
		amount         = cbModel.TotalAmount
	)

	// 校验状态和金额
	if status != fwe_trade_common.CommonStatus_Success {
		logs.CtxError(ctx, "[UnionPayCallbackBaseExecution] callback status is not success")
		return errdef.NewRawErr(errdef.PaymentRPCErr, "callback status is not success")
	}

	financeOrder := packer.FinanceGetByID(order.FinanceList, financeOrderID)
	if financeOrder.Amount != amount {
		logs.CtxError(ctx, "[UnionPayCallbackBaseExecution] callback amount is not equal with financeOrder's amount")
		return errdef.NewRawErr(errdef.PaymentRPCErr, "callback amount is not equal with financeOrder's amount")
	}

	if financeOrder.Status == int32(fwe_trade_common.FinanceStatus_Complete) {
		logs.CtxInfo(ctx, "[UnionPayCallbackBaseExecution] already complete, won't handle")
		return nil
	}

	// 修改资金单状态
	updateStatus := int32(fwe_trade_common.FinanceStatus_Complete)
	updateFinanceOrderParams := &service_model.UpdateFinanceParams{
		UpdateFinanceStatus: &updateStatus,
		UpdateProcessAmount: conv.Int64Ptr(financeOrder.Amount),
		UpdateFinishTime:    conv.Int64Ptr(time.Now().Unix()),
	}
	bizErr := service.NewFinanceOrderService().UpdateV2(ctx, financeOrderID, updateFinanceOrderParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionPayCallbackBaseExecution] update failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *UnionPayCallbackBaseExecution) PostProcess(ctx context.Context) error {
	var (
		order        = e.GetOrder()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
	)
	// 更新状态
	updateParams.WhereOrderStatus = []int32{int32(e.GetStateMachine().GetOriginalState())}
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	bizErr := service.NewOrderService().UpdateOrder(ctx, order.FweOrder.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionPayCallbackBaseExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}
