package sh_inspection_sell

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools/tools_recover_kite"
	"context"
)

type staticUpdateFweTradeOrder struct {
	*executor.StaticBaseExecution
	conf  sh_sell_model.Conf
	param *engine.CreateOrderReq
}

// 无状态变化的订单更新操作
func NewStaticUpdateInspectionOrderExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &staticUpdateFweTradeOrder{
		param: new(engine.CreateOrderReq),
	}
	e.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param)
	return e
}

func (e *staticUpdateFweTradeOrder) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}
	if e.param == nil {
		return errdef.NewParamsErr("缺少更新订单参数")
	}
	var req = e.param
	// 资金风控检查
	if bizErr = utils.CheckFundRiskOfAmount(ctx, req.IsTest, req.GetTotalAmount(), e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-CheckParams] CheckFundRiskOfAmount error, err = %v ", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *staticUpdateFweTradeOrder) PreProcess(ctx context.Context) error {
	err := e.StaticBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	return nil
}

func (e *staticUpdateFweTradeOrder) Process(ctx context.Context) error {
	var (
		orderService = service.NewOrderService()
		tagService   = service.NewTagService()
		buyerInfo    = e.param.BuyerInfo
		order        = e.GetOrder()
		bizErr       *errdef.BizErr
		updateOrder  = e.param
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		updateParams = &service_model.UpdateOrderParams{}
	)

	if len(updateOrder.GetExtra()) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.GetExtra())
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(updateOrder.OrderTag) > 0 || len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(updateOrder.OrderTag) > 0 {
			for key, value := range updateOrder.OrderTag {
				tagMap[key] = value
			}
		}
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		_, bizErr = tagService.UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if updateOrder.Operator != nil {
		updateParams.Operator = updateOrder.Operator
	}

	if e.param.TotalAmount != int64(0) {
		updateParams.UpdateTotalAmount = conv.Int64Ptr(e.param.TotalAmount)
	}
	if int64(e.param.TradeType) != int64(0) {
		updateParams.UpdateTradeType = conv.Int32Ptr(int32(e.param.TradeType))
	}

	bizErr = orderService.UpdateOrder(ctx, order.FweOrder.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[fireUpdateFweTradeOrder-Process] UpdateOrder error, err=%s", bizErr.Error())
		return bizErr
	}

	// 更新 ebs 数据
	go func() {
		defer func() {
			tools_recover_kite.CheckRecover(ctx, recover(), nil)
		}()
		err := orderService.CreateOrUpdateOrderSubject(ctx, []*fwe_trade_common.TradeSubjectInfo{buyerInfo})
		if err != nil {
			logs.CtxError(ctx, "[CreateOrUpdateOrderSubject] error, err = %v", err.Error())
		}
	}()
	return nil

}
