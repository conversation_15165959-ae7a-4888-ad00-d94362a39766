package sh_inspection_sell

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/condition"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/callback_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"time"
)

type UnionRefundCallbackExecution struct {
	*executor.ActionBaseExecution
	CbModel *callback_model.RefundCallbackModel
}

func NewInspectionUnionRefundCallbackExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &UnionRefundCallbackExecution{
		CbModel: new(callback_model.RefundCallbackModel),
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, nil, exe.CbModel)
	return exe
}

func (e *UnionRefundCallbackExecution) Process(ctx context.Context) error {
	var (
		order          = e.GetOrder()
		cbModel        = e.CbModel
		financeOrderID = cbModel.OutID
		status         = cbModel.Status
		amount         = cbModel.RefundAmount
		calCondition   map[string]interface{}
	)

	conditionFunc := condition.NewOrderConditionFuncFactory().GetActionOrderConditionFunc(ctx, e.GetActionOrderReq())
	if conditionFunc != nil {
		calCondition = conditionFunc(e.BuildConditionParam())
	}

	// 驱动
	bizErr := e.FireWithCondition(ctx, calCondition)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionRefundCallbackExecution] err=%s", bizErr.Error())
		return bizErr
	}
	// 校验状态和金额
	if status != int32(fwe_trade_common.CommonStatus_Success) {
		logs.CtxError(ctx, "[UnionRefundCallbackExecution] callback status is not success")
		return errdef.NewRawErr(errdef.PaymentRPCErr, "callback status is not success")
	}
	financeOrder := packer.FinanceGetByID(order.RefundFinanceList, financeOrderID)
	if financeOrder == nil {
		logs.CtxError(ctx, "[UnionRefundCallbackExecution] FinanceGetByID error, err = %v", tools.GetLogStr(cbModel))
		return errdef.NewRawErr(errdef.DataErr, "can not find financeOrder")
	}
	if financeOrder.Amount != amount {
		logs.CtxError(ctx, "[UnionRefundCallbackExecution] callback amount is not equal with financeOrder's amount")
		return errdef.NewRawErr(errdef.PaymentRPCErr, "callback amount is not equal with financeOrder's amount")
	}

	if financeOrder.Status == int32(fwe_trade_common.FinanceStatus_Complete) {
		logs.CtxInfo(ctx, "[UnionRefundCallbackExecution] already complete, won't handle")
		return nil
	}

	// 修改资金单状态
	updateStatus := int32(fwe_trade_common.FinanceStatus_Complete)
	updateFinanceOrderParams := &service_model.UpdateFinanceParams{
		UpdateFinanceStatus: &updateStatus,
		UpdateProcessAmount: conv.Int64Ptr(financeOrder.Amount),
		UpdateFinishTime:    conv.Int64Ptr(time.Now().Unix()),
	}
	bizErr = service.NewFinanceOrderService().UpdateV2(ctx, financeOrderID, updateFinanceOrderParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionRefundCallbackExecution] update failed, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *UnionRefundCallbackExecution) PostProcess(ctx context.Context) error {
	var (
		order        = e.GetOrder()
		updateParams = &service_model.UpdateOrderParams{
			Operator: e.GetActionOrderReq().GetOperator(),
		}
		stateMachine = e.GetStateMachine()
	)

	if e.GetStateMachine().GetOriginalState() != e.GetStateMachine().CurState() { // 主流程有发生流转
		updateParams.WhereOrderStatus = []int32{int32(e.GetStateMachine().GetOriginalState())}
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	}

	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))

	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	// 更新状态
	bizErr := service.NewOrderService().UpdateOrder(ctx, order.FweOrder.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionRefundCallbackExecution-PostProcess] err=%s", bizErr.Error())
		return bizErr
	}
	return e.ActionBaseExecution.PostProcess(ctx)
}
