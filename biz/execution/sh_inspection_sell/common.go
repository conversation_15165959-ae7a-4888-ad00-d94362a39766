package sh_inspection_sell

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

func CheckActionReq(ctx context.Context, actionReq *engine.ActionOrderReq) *errdef.BizErr {
	if actionReq == nil || actionReq.Identity == nil || actionReq.Identity.TenantType == 0 ||
		actionReq.Identity.BizScene == 0 || actionReq.OrderID == "" || actionReq.Action == "" {
		logs.CtxWarn(ctx, "[CheckActionReq] param empty")
		return errdef.NewParamsErr("有必传参数为空，请检查")
	}
	return nil
}
