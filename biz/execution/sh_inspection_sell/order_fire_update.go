package sh_inspection_sell

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_common/scene/sh_inspection_sell/common"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/sh_sell_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/gopkg/tools"
	"code.byted.org/motor/gopkg/tools/tools_recover_kite"
	"context"
	"time"
)

type fireUpdateFweTradeOrder struct {
	*executor.ActionBaseExecution
	conf  sh_sell_model.Conf
	param *engine.CreateOrderReq
}

// 状态变化的订单更新操作
func NewFireUpdateInspectionOrderExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &fireUpdateFweTradeOrder{
		param: new(engine.CreateOrderReq),
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, e.param)
	return e
}

func (e *fireUpdateFweTradeOrder) CheckParams(ctx context.Context) error {
	bizErr := CheckActionReq(ctx, e.GetActionOrderReq())
	if bizErr != nil {
		return bizErr
	}
	if e.param == nil {
		return errdef.NewParamsErr("缺少更新订单参数")
	}
	var req = e.param
	// 资金风控检查
	if bizErr = utils.CheckFundRiskOfAmount(ctx, req.IsTest, req.GetTotalAmount(), e.conf.TestOrderLimitAmount); bizErr != nil {
		logs.CtxWarn(ctx, "[createOrderExecution-CheckParams] CheckFundRiskOfAmount error, err = %v ", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *fireUpdateFweTradeOrder) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	return nil
}

func (e *fireUpdateFweTradeOrder) Process(ctx context.Context) error {
	var (
		orderService = service.NewOrderService()
		tagService   = service.NewTagService()
		buyerInfo    = e.param.BuyerInfo
		order        = e.GetOrder()
		bizErr       *errdef.BizErr
		updateOrder  = e.param
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		stateMachine = e.GetStateMachine()
		updateParams = &service_model.UpdateOrderParams{}
	)
	// 驱动状态机流转
	// 解析状态机条件
	var fireCondition = make(map[string]interface{})
	fireCondition = getFireCondition(order)
	bizErr = e.FireWithCondition(ctx, fireCondition)
	if bizErr != nil {
		logs.CtxError(ctx, "[PreProcess] fire failed, err=%s, fireCondition=%s", bizErr.Error(), tools.GetLogStr(fireCondition))
		return bizErr
	}
	if e.GetStateMachine().GetOriginalState() != e.GetStateMachine().CurState() {
		updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(e.GetStateMachine().GetOriginalState()))
		updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(e.GetStateMachine().CurState()))
	}
	updateParams.UpdateOrderSubStatus = conv.StringPtr(tools.GetLogStr(stateMachine.CurSubStates()))
	updateParams.UpdateTradeType = conv.Int32Ptr(int32(e.param.TradeType))

	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}

	if len(updateOrder.GetExtra()) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.GetExtra())
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if len(updateOrder.OrderTag) > 0 || len(actionReq.TagMap) > 0 {
		tagMap := make(map[string]string)
		if len(updateOrder.OrderTag) > 0 {
			for key, value := range updateOrder.OrderTag {
				tagMap[key] = value
			}
		}
		if len(actionReq.TagMap) > 0 {
			for key, value := range actionReq.TagMap {
				tagMap[key] = value
			}
		}
		_, bizErr = tagService.UpdateTag(ctx, orderID, e.GetBizIdentity().GetBizScene(), tagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderFireExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	if updateOrder.Operator != nil {
		updateParams.Operator = updateOrder.Operator
	}

	if e.param.TotalAmount != int64(0) {
		updateParams.UpdateTotalAmount = conv.Int64Ptr(e.param.TotalAmount)
	}
	if int64(e.param.TradeType) != int64(0) {
		updateParams.UpdateTradeType = conv.Int32Ptr(int32(e.param.TradeType))
	}

	bizErr = orderService.UpdateOrder(ctx, order.FweOrder.OrderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[fireUpdateFweTradeOrder-Process] UpdateOrder error, err=%s", bizErr.Error())
		return bizErr
	}

	// 更新 ebs 数据
	go func() {
		defer func() {
			tools_recover_kite.CheckRecover(ctx, recover(), nil)
		}()
		err := orderService.CreateOrUpdateOrderSubject(ctx, []*fwe_trade_common.TradeSubjectInfo{buyerInfo})
		if err != nil {
			logs.CtxError(ctx, "[CreateOrUpdateOrderSubject] error, err = %v", err.Error())
		}
	}()
	return nil

}

func getFireCondition(order *service_model.Order) map[string]interface{} {
	var (
		fsmConditionMap = make(map[string]interface{})
		tagMap          = order.TagMap
	)

	// 是否需要取消确认
	if cancelConfirmTagValue, ok := tagMap["cancel_confirm_tag_value"]; ok {
		fsmConditionMap[common.CancelConfirmTagValue.Val()] = conv.Int64Default(cancelConfirmTagValue, 0)
	}

	return fsmConditionMap
}
