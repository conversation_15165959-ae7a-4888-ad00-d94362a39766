package derivative

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
	"time"
)

type derivativeCommonTransferExecution struct {
	*executor.ActionBaseExecution
}

func NewDerivativeCommonTransferExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	e := &derivativeCommonTransferExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, nil)
	return e
}

func (e *derivativeCommonTransferExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxWarn(ctx, "[PreProcess] base preProcess failed, err=%+v", err)
		return err
	}

	if err := e.FireWithCondition(ctx, e.buildCondition(ctx)); err != nil {
		logs.CtxWarn(ctx, "[PreProcess] fire fsm failed, err=%v", err.Error())
		return err
	}
	return nil
}

// Process 主流程
func (e *derivativeCommonTransferExecution) Process(ctx context.Context) error {
	var (
		orderID      = e.GetOrder().FweOrder.OrderID
		stateMachine = e.GetStateMachine()
	)

	// 更新订单tag
	if _, bizErr := service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().BizScene, e.GetActionOrderReq().GetTagMap()); bizErr != nil {
		return bizErr
	}
	// 更新状态
	updateParams := &service_model.UpdateOrderParams{}
	updateParams.WhereOrderStatus = append(updateParams.WhereOrderStatus, int32(stateMachine.GetOriginalState()))
	updateParams.UpdateBeforeStatus = conv.Int32Ptr(int32(stateMachine.GetOriginalState()))
	updateParams.UpdateOrderStatus = conv.Int32Ptr(int32(stateMachine.CurState()))
	updateParams.Operator = e.GetActionOrderReq().GetOperator()
	if stateMachine.GetState(stateMachine.CurState()).StateType == bfsm.End {
		updateParams.UpdateFinishTime = utils.TimePtr(time.Now())
	}
	if bizErr := service.NewOrderService().UpdateOrder(ctx, orderID, updateParams); bizErr != nil {
		return bizErr
	}
	return nil
}

func (e *derivativeCommonTransferExecution) buildCondition(ctx context.Context) map[string]interface{} {
	mp := map[string]interface{}{
		statemachine.BeforeStatusKey: e.GetOrder().FweOrder.BeforeStatus,
	}
	return mp
}
