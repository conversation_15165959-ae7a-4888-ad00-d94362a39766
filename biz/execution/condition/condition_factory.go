package condition

import (
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

type OrderConditionFuncFactory struct {
}

func NewOrderConditionFuncFactory() *OrderConditionFuncFactory {
	return &OrderConditionFuncFactory{}
}

func (f *OrderConditionFuncFactory) GetActionOrderConditionFunc(ctx context.Context, req *engine.ActionOrderReq) ActionConditionFunc {
	conditionFunc, ok := ActionConditionFuncMap[req.Identity.GetBizScene()]
	if !ok {
		return nil
	}
	return conditionFunc
}
