package condition

import (
	"code.byted.org/motor/fwe_trade_common/scene/sh_consign_ef/common"
	"code.byted.org/motor/fwe_trade_common/statemachine/sh_state"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

var (
	LoanSubStatus         = "loan_sub_status"   // 贷款子流程状态
	DeliveryCarType       = "delivery_car_type" // 交车类型 值为Local, Remote
	LoanSubStatusOverStr  = "Over"              // 结束
	DeliveryCarTypeRemote = "Remote"            // 结束
)

func GetShSellConditionV2(param *ActionConditionGetParam) map[string]interface{} {
	var (
		hasLoan, IsLoanOver, isLoanNotStart bool
		result                              = make(map[string]interface{})
		fweOrder                            = param.Order.FweOrder
		tagMap                              = param.Order.TagMap
		actionOrderReq                      = param.ActionOrderReq
	)
	// 融合请求中的tag和数据库中的tag
	if actionOrderReq != nil && len(actionOrderReq.TagMap) > 0 {
		for key, tempTag := range actionOrderReq.TagMap {
			tagMap[key] = tempTag
		}
	}

	if fweOrder.TradeType == int32(fwe_trade_common.TradeType_EarnestFinalLoan) ||
		fweOrder.TradeType == int32(fwe_trade_common.TradeType_FinalLoan) {
		hasLoan = true
	}

	isLoanNotStart = true
	if loanSbbStatus, exist := tagMap[LoanSubStatus]; exist {
		isLoanNotStart = false
		IsLoanOver = loanSbbStatus == LoanSubStatusOverStr
	}

	result[common.CondShConsignIsRemoteDelivery.Val()] = tagMap[DeliveryCarType] == DeliveryCarTypeRemote
	result[consts.CondShSellHasLoan.Val()] = hasLoan
	result[sh_state.CondShSellIsLoanNotStart.Val()] = isLoanNotStart
	result[sh_state.CondShSellIsLoanOver.Val()] = IsLoanOver
	result[common.CondShConsignIsSkipFinalPay.Val()] = tagMap[common.CondShConsignIsSkipFinalPay.Val()] == "true"
	result[common.CondShConsignIsOrderChangeEarnestType.Val()] = tagMap[common.CondShConsignIsOrderChangeEarnestType.Val()] == "true"
	result[common.CondShConsignIsSelfSell.Val()] = tagMap[common.CondShConsignIsSelfSell.Val()] == "true"
	result[common.CondShConsignHasBigDeposit.Val()] = tagMap[common.CondShConsignHasBigDeposit.Val()] == "true"
	result[common.CondShOrderIsJoinMerchant.Val()] = tagMap[common.CondShOrderIsJoinMerchant.Val()] == "true"
	result[common.CondShConsignIsLocalDispatch.Val()] = tagMap[common.CondShConsignIsLocalDispatch.Val()] == "true"
	return result
}
