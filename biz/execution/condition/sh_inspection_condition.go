package condition

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/motor/fwe_trade_common/scene/sh_inspection_sell"
	"code.byted.org/motor/fwe_trade_common/scene/sh_inspection_sell/common"
)

func GetShInspectionSellCondition(param *ActionConditionGetParam) map[string]interface{} {
	var (
		fsmConditionMap                                      = make(map[string]interface{})
		tagMap                                               = param.Order.TagMap
		orderInfo                                            = param.Order.FweOrder
		action                                               = param.ActionOrderReq.Action
		isArriveDetect, isToDoorDetect, isCancelRefundSettle bool
	)
	// 检测方式
	if slices.ContainsInt32([]int32{sh_inspection_sell.SHInspectionSellVersion2.Value()}, orderInfo.SmVersion) &&
		slices.ContainsString([]string{
			sh_inspection_sell.ShInspectionOrderPayOverByCondEt.Value(), // 有支付金额，支付完成需要判断是否到店/上门
		}, action) { // 支付完成
		if detectMethod, ok := tagMap["detect_method"]; ok {
			if conv.Int32Default(detectMethod, 0) == 1 { // 到店
				isArriveDetect = true
			} else if conv.Int32Default(detectMethod, 0) == 2 { // 上门
				isToDoorDetect = true
			}
		}
		fsmConditionMap[common.CondIsArriveDetect.Val()] = isArriveDetect
		fsmConditionMap[common.CondIsToDoorDetect.Val()] = isToDoorDetect
	}
	// 取消退款分账标识
	if slices.ContainsInt32([]int32{sh_inspection_sell.SHInspectionSellVersion2.Value()}, orderInfo.SmVersion) &&
		action == sh_inspection_sell.ShInspectionCancelRefundOverByCondEt.Value() { // 退款完成
		if slices.ContainsInt32([]int32{int32(sh_inspection_sell.ShInspectionHaveDeparted.Value()),
			int32(sh_inspection_sell.ShInspectionToBeDetected.Value())}, orderInfo.BeforeStatus) {
			isCancelRefundSettle = true
		}
		fsmConditionMap[common.CondIsCancelRefundSettle.Val()] = isCancelRefundSettle
	}

	// 是否需要取消确认
	if cancelConfirmTagValue, ok := tagMap["cancel_confirm_tag_value"]; ok {
		fsmConditionMap[common.CancelConfirmTagValue.Val()] = conv.Int64Default(cancelConfirmTagValue, 0)
	}

	return fsmConditionMap
}
