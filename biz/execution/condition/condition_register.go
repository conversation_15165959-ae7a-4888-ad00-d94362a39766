package condition

import (
	"code.byted.org/motor/fwe_trade_common/scene/sh_consign_ef"
	"code.byted.org/motor/fwe_trade_common/scene/sh_inspection_sell"
	"code.byted.org/motor/fwe_trade_common/scene/sh_sell_ef"
)

var ActionConditionFuncMap = map[int32]ActionConditionFunc{
	sh_consign_ef.SHConsignEFAfterSaleScene.Value():  GetShSellConditionV2,
	sh_sell_ef.SHSellEFAfterSaleScene.Value():        GetShSellConditionV2,
	sh_inspection_sell.SHInspectionSellScene.Value(): GetShInspectionSellCondition,
}
