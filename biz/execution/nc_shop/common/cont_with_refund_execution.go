package common

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"github.com/bytedance/sonic"
)

type ContFinishWithRefundExecution struct {
	*callback.ContCallbackBaseExecution
	result          interface{}
	rfCallbackEvent string
	financeType     int32
}

func NewContFinishWithRefundExecution(ctx context.Context, actionReq interface{}, rfCallbackEvent string, financeType int32) *ContFinishWithRefundExecution {
	e := &ContFinishWithRefundExecution{
		rfCallbackEvent: rfCallbackEvent,
		financeType:     financeType,
	}
	e.ContCallbackBaseExecution = callback.NewContCallbackBaseExecution(ctx, actionReq, nil)
	return e
}

func (e *ContFinishWithRefundExecution) Process(ctx context.Context) error {

	var (
		cashRefundReq *payment.MergeRefundReq
		mergeRefundNo string
		bizErr        *errdef.BizErr
		err           error
	)

	// 请求
	cashRefundReq, bizErr = e.buildRefundReq()
	if bizErr != nil {
		return bizErr
	}
	mergeRefundNo, bizErr = service.NewTradePayment().MergeRefund(ctx, cashRefundReq)
	if bizErr != nil {
		return bizErr
	}

	rpcRsp := &action_model.CreateRefundRsp{
		MergeRefundID: mergeRefundNo,
	}
	e.result, err = sonic.MarshalString(rpcRsp)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "")
		return bizErr
	}

	return nil
}

func (e *ContFinishWithRefundExecution) Result() interface{} {
	return e.result
}

func (e *ContFinishWithRefundExecution) buildRefundReq() (*payment.MergeRefundReq, *errdef.BizErr) {
	var (
		order    = e.GetOrder()
		orderID  = e.GetOrder().FweOrder.OrderID
		fweOrder = order.FweOrder
	)
	refundList := e.buildSingleRefund(order.FinanceList)
	serviceReq := &payment.MergeRefundReq{
		Identity:                 e.GetBizIdentity(),
		RefundList:               refundList,
		OrderID:                  orderID,
		RefundFinanceType:        1,
		OrderName:                fweOrder.OrderName,
		Reason:                   "终止合同自动退款",
		Extra:                    nil,
		CallbackEvent:            utils.MakeCallbackEvent(e.rfCallbackEvent),
		CallbackExtra:            "",
		IPAddress:                "*************",
		NeedConfirmOfflineRefund: true,
	}
	return serviceReq, nil
}

func (e *ContFinishWithRefundExecution) buildSingleRefund(input []*db_model.FFinanceOrder) (output []*payment.SingleRefund) {
	for _, v := range input {
		if v == nil {
			continue
		}
		if v.Amount <= 0 {
			continue
		}
		if v.TradeType == consts.TradeTypePayPos.String() {
			continue
		}
		if v.Status != int32(fwe_trade_common.FinanceStatus_Complete) {
			continue
		}
		output = append(output, &payment.SingleRefund{
			FinanceOrderID: v.FinanceOrderID,
			Amount:         v.Amount,
		})
	}
	return
}
