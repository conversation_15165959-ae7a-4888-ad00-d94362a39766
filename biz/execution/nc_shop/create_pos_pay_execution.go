package nc_shop

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"github.com/bytedance/sonic"
)

type CreatePosPayExecution struct {
	*executor.ActionBaseExecution
	productReq action_model.CreatePosPayReq
	conf       model.ConfigWithFinance
	result     interface{}
}

func NewCreatePosPayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	e := &CreatePosPayExecution{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.productReq, opts...)
	return e
}

func (e *CreatePosPayExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[WithdrawExecution] PreProcess failed, err=%+v", err)
		return err
	}

	// 判断是否做合同金额校验
	if !e.conf.CheckContStructField {
		return nil
	}

	// 校验
	param := &service.CheckAmountByContParam{
		OrderID:          e.GetActionOrderReq().OrderID,
		FinanceOrderType: e.productReq.FinanceOrderType,
		Amount:           e.productReq.Amount,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[WithdrawExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[WithdrawExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "出款合同校验未通过")
	}

	return nil
}

func (e *CreatePosPayExecution) Process(ctx context.Context) error {
	var (
		bizErr     *errdef.BizErr
		serviceReq *payment.CreatePOSPayReq
		productRsp *action_model.CreatePosPayRsp
		order      = e.GetOrder()
		sellerInfo *fwe_trade_common.TradeSubjectInfo
		fweOrder   = order.FweOrder
		account    *shop.FinanceAccount
	)

	if fweOrder.SellerExtra == nil {
		bizErr = errdef.NewParamsErr("seller info nil")
		logs.CtxWarn(ctx, "[CreateGuaranteePayExecution] err=%s", bizErr.Error())
		return bizErr
	}
	sellerInfo, bizErr = packer.CommonTradeSubjectDeserialize(fweOrder.SellerID, *fweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CreateGuaranteePayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	account, bizErr = service.NewAccountShop().GetFinanceAccountOne(ctx, sellerInfo.FweMerchant.FweAccountID, e.conf.PosMerchantID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CreateGuaranteePayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	serviceReq, bizErr = e.buildReq(account)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CreatePosPayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	productRsp, bizErr = service.NewTradePayment().CreatePOSPay(ctx, serviceReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CreatePosPayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.result, _ = sonic.MarshalString(productRsp)

	return nil
}

func (e *CreatePosPayExecution) Result() interface{} {
	return e.result
}

func (e *CreatePosPayExecution) buildReq(account *shop.FinanceAccount) (*payment.CreatePOSPayReq, *errdef.BizErr) {
	var (
		productReq = e.productReq
		order      = e.GetOrder()
		orderID    = order.FweOrder.OrderID
		bizErr     *errdef.BizErr
	)
	financeOrder := packer.FinanceGetByType(order.FinanceList, productReq.FinanceOrderType)
	if financeOrder == nil {
		bizErr = errdef.NewParamsErr("no finance order")
		return nil, bizErr
	}
	serviceReq := &payment.CreatePOSPayReq{
		Identity:             e.GetBizIdentity(),
		FinanceOrderID:       financeOrder.FinanceOrderID,
		CallbackEvent:        utils.MakeCallbackEvent(productReq.CallbackEvent),
		TimeoutCallbackEvent: utils.MakeCallbackEvent(productReq.TimeoutCallbackEvent),
		OrderID:              orderID,
		UserID:               "user_id",
		FinanceOrderType:     productReq.FinanceOrderType,
		TotalAmount:          productReq.Amount,
		ExpireTime:           &productReq.ExpireTime,
		Extra:                nil,
		IPAddress:            "*************",
		CallbackExtra:        "",
		PayOrderNo:           nil,
		Currency:             nil,
		SellerUID:            conv.StringPtrToVal(account.UID, ""),
		SellerUIDType:        consts.ShopUidType,
	}
	return serviceReq, nil
}
