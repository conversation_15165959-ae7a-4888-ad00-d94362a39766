package nc_shop

import (
	"context"
	"fmt"

	"code.byted.org/motor/gopkg/tools"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UpdateOrderExecution struct {
	*executor.ActionBaseExecution
	updateOrder action_model.UpdateOrder
	conf        model.ConfigWithFinance
}

func NewUpdateOrderExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	t := &UpdateOrderExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &t.conf, &t.updateOrder, opts...)
	return t
}

func (e *UpdateOrderExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		oldOrder     = e.GetOrder()
		actionReq    = e.GetActionOrderReq()
		orderID      = actionReq.GetOrderID()
		orderService = service.NewOrderService()
		updateOrder  = e.updateOrder
		updateParams = &service_model.UpdateOrderParams{
			UpdateOrderName:   updateOrder.OrderName,
			UpdateOrderDesc:   updateOrder.OrderDesc,
			UpdateTotalAmount: updateOrder.TotalAmount,
			Operator:          actionReq.GetOperator(),
		}
	)

	if len(updateOrder.Extra) > 0 {
		bizErr = orderService.UpdateOrderExtraMarshal(ctx, orderID, updateOrder.Extra)
		if bizErr != nil {
			logs.CtxError(ctx, "[UpdateOrderExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	_, bizErr = service.NewTagService().UpdateTag(ctx, orderID, e.GetBizIdentity().BizScene, e.updateOrder.Tag)
	if bizErr != nil {
		logs.CtxError(ctx, "[NewUpdateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	if updateOrder.BuyerInfo != nil {
		buyerID := packer.CommonTradeSubjectIDGet(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerID = &buyerID
		buyerInfo := packer.CommonTradeSubjectSerialize(updateOrder.BuyerInfo)
		updateParams.UpdateBuyerExtra = &buyerInfo
	}

	if updateOrder.SellerInfo != nil {
		sellerID := packer.CommonTradeSubjectIDGet(updateOrder.SellerInfo)
		updateParams.UpdateSellerID = &sellerID
		sellerInfo := packer.CommonTradeSubjectSerialize(updateOrder.SellerInfo)
		updateParams.UpdateSellerExtra = &sellerInfo
	}

	if updateOrder.ProductInfo != nil {
		p := updateOrder.ProductInfo
		if p.ProductID != "" {
			updateParams.UpdateProductID = &p.ProductID
		}
		if p.SkuID != "" {
			updateParams.UpdateSkuID = &p.SkuID
		}
		if p.ProductName != "" {
			updateParams.UpdateProductName = &p.ProductName
		}
		if p.ProductType != 0 {
			updateParams.UpdateProductType = conv.Int32Ptr(int32(p.ProductType))
		}
		if p.ProductUnitPrice != 0 {
			updateParams.UpdateProductUnitPrice = &p.ProductUnitPrice
		}
		if p.ProductQuantity != 0 {
			updateParams.UpdateProductQuantity = &p.ProductQuantity
		}
		if p.ProductExtra != nil {
			updateParams.UpdateProductExtra = p.ProductExtra
		}
	}

	bizErr = e.updateFinanceOrderList(ctx, updateOrder.FinanceList)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// 更新全款金额
	if updateOrder.TotalAmount != nil {
		// 上游total传了，就直接用上游的
		updateParams.UpdateTotalAmount = updateOrder.TotalAmount
	} else {
		if len(updateOrder.FinanceList) > 0 {
			totalAmount := oldOrder.FweOrder.TotalAmount
			for _, v := range updateOrder.FinanceList {
				totalAmount += v.Amount
			}
			updateParams.UpdateTotalAmount = conv.Int64Ptr(totalAmount)
		}
	}

	// 更新订单
	bizErr = orderService.UpdateOrder(ctx, orderID, updateParams)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// upsert ebs信息
	subjects := []*fwe_trade_common.TradeSubjectInfo{updateOrder.BuyerInfo, updateOrder.SellerInfo}
	bizErr = service.NewOrderService().CreateOrUpdateOrderSubject(ctx, subjects)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderFireExecution] CreateOrUpdateOrderSubject err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *UpdateOrderExecution) updateFinanceOrderList(ctx context.Context, financeList []*fwe_trade_common.FinanceInfo) (bizErr *errdef.BizErr) {
	var (
		order             = e.GetOrder()
		conf              = e.conf
		createFinanceList []*fwe_trade_common.FinanceInfo
		financeOrder      = service.NewFinanceOrderService()
		stringPtrFunc     = func(input string) *string {
			if input == "" {
				return nil
			}
			return conv.StringPtr(input)
		}
	)

	for _, newF := range financeList {
		var findBool = false
		for _, oldF := range order.FinanceList {
			if newF.FinanceOrderType == oldF.FinanceOrderType {
				findBool = true
				if oldF.Status == int32(fwe_trade_common.FinanceStatus_Complete) ||
					oldF.Status == int32(fwe_trade_common.FinanceStatus_Handling) {
					bizErr = errdef.NewParamsErr(fmt.Sprintf("finance type = %d is in process, not support update", newF.FinanceOrderType))
					return
				}

				status := newF.PayStatus
				if int32(status) == 0 {
					status = fwe_trade_common.FinanceStatus_NotHandle
				}

				bizErr = financeOrder.UpdateOrderFinance(ctx, oldF.FinanceOrderID, &service_model.UpdateFinanceParams{
					UpdateAmount:        conv.Int64Ptr(newF.Amount),
					UpdateTradeType:     stringPtrFunc(newF.TradeType),
					UpdateFinanceStatus: conv.Int32Ptr(int32(status)),
					UpdateFeeItemDetail: stringPtrFunc(tools.GetLogStr(newF.FeeItemList)),
				})
				if bizErr != nil {
					logs.CtxError(ctx, "[UpdateOrderExecution] err=%s", bizErr.Error())
					return bizErr
				}
			}
		}
		if !findBool {
			createFinanceList = append(createFinanceList, newF)
		}
	}

	var dbFinanceList []*db_model.FFinanceOrder
	dbFinanceList, bizErr = packer.BuildFinanceListV1(ctx, order.FweOrder, &conf, createFinanceList)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}
	bizErr = financeOrder.CreateFinanceOrderList(ctx, dbFinanceList)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}
