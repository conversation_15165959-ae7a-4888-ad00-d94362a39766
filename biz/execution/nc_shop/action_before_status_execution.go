package nc_shop

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type ActionBeforeStatusExecution struct {
	*executor.ActionBaseExecution
}

func NewActionBeforeStatusExecution(ctx context.Context, rpcReq interface{}) executor.IExecution {
	var opts []*executor.Option
	e := &ActionBeforeStatusExecution{}
	opts = append(opts, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: e.buildCondition,
	})
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, rpcReq.(*engine.ActionOrderReq), nil, nil, opts...)
	return e
}

func (e *ActionBeforeStatusExecution) Process(ctx context.Context) error {
	return nil
}

func (e *ActionBeforeStatusExecution) buildCondition(ctx context.Context) (map[string]interface{}, *errdef.BizErr) {
	mp := map[string]interface{}{
		statemachine.BeforeStatusKey: e.GetOrder().FweOrder.BeforeStatus,
	}
	return mp, nil
}
