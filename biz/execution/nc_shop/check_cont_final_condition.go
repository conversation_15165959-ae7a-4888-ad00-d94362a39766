package nc_shop

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type ContFinishWithFinalSkipExecution struct {
	*callback.ContCallbackBaseExecution
}

func NewContFinishWithFinalSkipExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &ContFinishWithFinalSkipExecution{}
	var opts []*executor.Option
	opts = append(opts, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: e.buildCondition,
	})
	e.ContCallbackBaseExecution = callback.NewContCallbackWithOpt(ctx, actionReq, nil, opts...)
	return e
}

func (e *ContFinishWithFinalSkipExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	mp = make(map[string]interface{})

	{
		var finance = packer.FinanceGetByType(e.GetOrder().FinanceList, consts.NCShopBigDeposit.Int32())
		if finance != nil && finance.Status != int32(fwe_trade_common.FinanceStatus_Closed) {
			mp[statemachine.BigDepositPriceKey] = finance.Amount

			if finance.Amount == 0 {
				bizErr = service.NewFinanceOrderService().UpdateOrderFinance(ctx, finance.FinanceOrderID, &service_model.UpdateFinanceParams{
					UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Complete)),
				})
				if bizErr != nil {
					logs.CtxError(ctx, "[ContFinishWithFinalSkipExecution] err=%v", bizErr.Error())
					return nil, bizErr
				}
			}
		}
	}

	{
		var finance = packer.FinanceGetByType(e.GetOrder().FinanceList, consts.NCShopFinal.Int32())
		if finance != nil && finance.Status != int32(fwe_trade_common.FinanceStatus_Closed) {
			mp[statemachine.FinalPriceKey] = finance.Amount

			if finance.Amount == 0 {
				bizErr = service.NewFinanceOrderService().UpdateOrderFinance(ctx, finance.FinanceOrderID, &service_model.UpdateFinanceParams{
					UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Complete)),
				})
				if bizErr != nil {
					logs.CtxError(ctx, "[ContFinishWithFinalSkipExecution] err=%v", bizErr.Error())
					return nil, bizErr
				}
			}
		}
	}

	return mp, nil
}
