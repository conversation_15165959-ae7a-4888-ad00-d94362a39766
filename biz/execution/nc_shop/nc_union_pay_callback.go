package nc_shop

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/nc_scene/nc_small_deposit"

	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"

	pkgorder "code.byted.org/motor/trade_pkg/biz/order"
)

// NCUnionPayCallbackExecution 为 UnionPayCallbackBaseExecution注入新车的condition
type NCUnionPayCallbackExecution struct {
	*callback.UnionPayCallbackBaseExecution
}

func NewNCUnionPayCallbackBaseExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	exe := &NCUnionPayCallbackExecution{}
	exe.UnionPayCallbackBaseExecution = callback.NewUnionPayCallbackBaseExecutionWithOpt(ctx, sourceReq, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: exe.buildCondition,
	})
	return exe
}

func (e *NCUnionPayCallbackExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxError(ctx, "[NCUnionPayCallbackExecution-PreProcess] base PreProcess error, err = %v", bizErr.Error())
		return bizErr
	}
	logs.CtxInfo(ctx, "[NCUnionPayCallbackExecution-PreProcess] PreProcess done")
	//bizErr := e.FireDefault(ctx)
	//if bizErr != nil {
	//	logs.CtxError(ctx, "[UnionPayCallbackBaseExecution] err=%s", bizErr.Error())
	//	return bizErr
	//}
	return nil
}

func (e *NCUnionPayCallbackExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	mp = make(map[string]interface{})

	bizOrderPattern := pkgorder.UnBuildOrderPattern(e.GetOrder().BizExtra)
	if bizOrderPattern != "" {
		mp[nc_small_deposit.BrokerageModeKey] = bizOrderPattern.IsRakeBack()
		mp[nc_small_deposit.BrokerageHorizontalModeKey] = bizOrderPattern.IsRakeBackHorizontalBusiness()
		mp[nc_small_deposit.BrokerageNonHorizontalModeKey] = bizOrderPattern.IsRakeBackNonHorizontalBusiness()
	}

	return mp, nil
}
