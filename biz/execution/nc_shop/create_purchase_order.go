package nc_shop

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type CreatePurchaseOrderExecution struct {
	*executor.CreateBaseExecution
	result interface{}
}

func NewCreatePurchaseOrderExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &CreatePurchaseOrderExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), nil)
	return t
}

func (e *CreatePurchaseOrderExecution) Process(ctx context.Context) error {
	var (
		err          error
		bizErr       *errdef.BizErr
		createReq    = e.GetCreateOrderReq()
		bizScene     = createReq.GetIdentity().GetBizScene()
		tradeType    = CommonConsts.OrderEarnestFinal
		productInfo  = createReq.GetProductInfo()
		stateMachine = e.GetStateMachine()
	)

	// 驱动状态
	err = stateMachine.Fire(ctx, consts.CreateAction, nil)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "CreatePurchaseOrderExecution")
		logs.CtxError(ctx, "[CreatePurchaseOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene,
		SmVersion:          createReq.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createReq.GetOrderName(),
		OrderDesc:          createReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductExtra:       productInfo.ProductExtra,
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		TotalAmount:        packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList()),
		TotalPayAmount:     packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList()),
		TotalSubsidyAmount: 0,
		TradeType:          tradeType.Value(),
		BuyerID:            packer.CommonTradeSubjectIDGet(createReq.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(createReq.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createReq.IsTest),
		Creator:            createReq.GetOperator().GetOperatorID(),
		CreatorName:        createReq.GetOperator().GetOperatorName(),
		Operator:           createReq.GetOperator().GetOperatorID(),
		OperatorName:       createReq.GetOperator().GetOperatorName(),
		IdempotentID:       createReq.GetIdemID(),
	}

	order := &service_model.Order{
		FweOrder: fweOrder,
		TagMap:   createReq.OrderTag,
		BizExtra: createReq.Extra,
	}

	// 创建主订单
	bizErr = service.NewOrderService().CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreatePurchaseOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.result = e.GetOrderID()
	return nil
}

func (e *CreatePurchaseOrderExecution) Result() interface{} {
	return e.result
}
