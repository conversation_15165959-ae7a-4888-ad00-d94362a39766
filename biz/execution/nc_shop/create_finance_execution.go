package nc_shop

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type CreateFinanceExecution struct {
	*executor.StaticBaseExecution
	proReq action_model.CreateFinanceReq
}

func NewCreateFinanceExecution(ctx context.Context, req interface{}) executor.IExecution {
	e := &CreateFinanceExecution{}
	e.StaticBaseExecution = executor.NewStaticBaseExecution(ctx, req.(*engine.ActionOrderReq), nil, &e.proReq)
	return e
}

func (e *CreateFinanceExecution) Process(ctx context.Context) error {
	var (
		bizErr    *errdef.BizErr
		proReq    = e.proReq
		actionReq = e.GetActionOrderReq()
		order     = e.GetOrder().FweOrder
		finances  = e.GetOrder().FinanceList
	)

	// 一个资金单
	if len(proReq.Finances) != 1 {
		bizErr = errdef.NewParamsErr("only can create one")
		logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
		return bizErr
	}
	finance := proReq.Finances[0]

	// 更新tag
	if len(proReq.TagMap) > 0 {
		bizErr = service.NewOrderService().UpdateOrderTag(ctx, order.OrderID, proReq.TagMap)
		if bizErr != nil {
			logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}

	var isFind bool
	for _, v := range finances {
		if v.FinanceOrderType == finance.FinanceOrderType {
			isFind = true
			if v.Amount != finance.Amount {
				bizErr = errdef.NewParamsErr("can not update amount")
				logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
				return bizErr
			}
		}
	}
	if isFind {
		logs.CtxInfo(ctx, "[CreateFinanceExecution] has find finances")
		return nil
	}

	// 创建资金单
	bizErr = service.NewFinanceOrderService().Create(ctx, proReq.OrderTradeType, &service_model.CreateFinanceOrderReq{
		TenantType:               e.GetBizIdentity().GetTenantType(),
		BizScene:                 e.GetBizIdentity().GetBizScene(),
		AppID:                    "",
		MerchantID:               "",
		MID:                      "",
		OrderID:                  actionReq.GetOrderID(),
		OrderName:                order.OrderName,
		FOrderType2FTradeTypeMap: nil,
	}, proReq.Finances...)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
		return bizErr
	}

	return nil
}
