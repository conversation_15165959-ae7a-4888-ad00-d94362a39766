package nc_shop

import (
	sdkConst "code.byted.org/motor/fwe_trade_common/consts"
	"context"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UploadPayingVoucherExecution struct {
	*executor.ActionBaseExecution
	extraReq action_model.ExtraUpdateReq
}

func NewUploadPayingVoucherExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &UploadPayingVoucherExecution{}
	var opts []*executor.Option
	opts = append(opts, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: e.buildCondition,
	})
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.extraReq, opts...)
	return e
}

func (e *UploadPayingVoucherExecution) Process(ctx context.Context) error {
	var (
		orderID = e.GetActionOrderReq().GetOrderID()
		bizErr  *errdef.BizErr
	)
	if len(e.extraReq.Extra) != 0 {
		bizErr = service.NewOrderService().UpdateOrderExtraMarshal(ctx, orderID, e.extraReq.Extra)
		if bizErr != nil {
			logs.CtxError(ctx, "[UploadPayingVoucherExecution] err=%v", bizErr.Error())
			return bizErr
		}
	}

	// 意向金待收款
	if e.GetOrder().FweOrder.OrderStatus == 15 {
		var finance = packer.FinanceGetByType(e.GetOrder().FinanceList, consts.NCShopSmallDeposit.Int32())
		bizErr = service.NewFinanceOrderService().UpdateOrderFinance(ctx, finance.FinanceOrderID, &service_model.UpdateFinanceParams{
			UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Complete)),
			UpdateFinishTime:    conv.Int64Ptr(time.Now().Unix()),
			UpdateTradeType:     conv.StringPtr(sdkConst.FinancePayOffline.Value()),
		})
		if bizErr != nil {
			logs.CtxError(ctx, "[UploadPayingVoucherExecution] err=%v", bizErr.Error())
			return bizErr
		}
	}

	// 大定待收款
	if e.GetOrder().FweOrder.OrderStatus == 13 {
		var finance = packer.FinanceGetByType(e.GetOrder().FinanceList, consts.NCShopBigDeposit.Int32())
		bizErr = service.NewFinanceOrderService().UpdateOrderFinance(ctx, finance.FinanceOrderID, &service_model.UpdateFinanceParams{
			UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Complete)),
			UpdateFinishTime:    conv.Int64Ptr(time.Now().Unix()),
			UpdateTradeType:     conv.StringPtr(sdkConst.FinancePayOffline.Value()),
		})
		if bizErr != nil {
			logs.CtxError(ctx, "[UploadPayingVoucherExecution] err=%v", bizErr.Error())
			return bizErr
		}
	}

	// 首付款/尾款待收款
	if e.GetOrder().FweOrder.OrderStatus == 17 {
		var finance = packer.FinanceGetByType(e.GetOrder().FinanceList, consts.NCShopFinal.Int32())
		bizErr = service.NewFinanceOrderService().UpdateOrderFinance(ctx, finance.FinanceOrderID, &service_model.UpdateFinanceParams{
			UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Complete)),
			UpdateFinishTime:    conv.Int64Ptr(time.Now().Unix()),
			UpdateTradeType:     conv.StringPtr(sdkConst.FinancePayOffline.Value()),
		})
		if bizErr != nil {
			logs.CtxError(ctx, "[UploadPayingVoucherExecution] err=%v", bizErr.Error())
			return bizErr
		}
	}

	return nil
}

func (e *UploadPayingVoucherExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	mp = make(map[string]interface{})

	{
		var finance = packer.FinanceGetByType(e.GetOrder().FinanceList, consts.NCShopBigDeposit.Int32())
		if finance != nil && finance.Status != int32(fwe_trade_common.FinanceStatus_Closed) {
			mp[statemachine.BigDepositPriceKey] = finance.Amount
		}
	}

	{
		var finance = packer.FinanceGetByType(e.GetOrder().FinanceList, consts.NCShopFinal.Int32())
		if finance != nil && finance.Status != int32(fwe_trade_common.FinanceStatus_Closed) {
			mp[statemachine.FinalPriceKey] = finance.Amount
		}
	}

	return mp, nil
}
