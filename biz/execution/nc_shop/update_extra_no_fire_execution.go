package nc_shop

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"github.com/bytedance/sonic"
)

type UpdateExtraNoFireExecution struct {
	*executor.BaseExecution
	actionReq *engine.ActionOrderReq
	extraReq  action_model.ExtraUpdateReq
}

func NewUpdateExtraNoFireExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	t := &UpdateExtraNoFireExecution{}
	t.actionReq = actionReq.(*engine.ActionOrderReq)
	t.BaseExecution = executor.NewBaseExecution(ctx, t.actionReq.Action, t.actionReq.Identity)
	return t
}

func (e *UpdateExtraNoFireExecution) Process(ctx context.Context) error {

	var (
		orderID    = e.actionReq.GetOrderID()
		bizRequest = e.actionReq.GetBizRequest()
		bizErr     *errdef.BizErr
		err        error
	)

	err = sonic.UnmarshalString(bizRequest, &e.extraReq)
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "UpdateExtraNoFireExecution")
		logs.CtxError(ctx, "[UpdateExtraNoFireExecution] err=%v", bizErr.Error())
		return bizErr
	}

	if len(e.extraReq.Extra) == 0 {
		bizErr = errdef.NewParamsErr("extra is nil")
		logs.CtxError(ctx, "[UpdateExtraNoFireExecution] err=%v", bizErr.Error())
		return bizErr
	}

	bizErr = service.NewOrderService().UpdateOrderExtraMarshal(ctx, orderID, e.extraReq.Extra)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateExtraNoFireExecution] err=%v", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *UpdateExtraNoFireExecution) LockTarget() string {
	return e.actionReq.GetOrderID()
}
