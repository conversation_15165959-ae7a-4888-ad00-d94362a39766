package nc_shop

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
)

type CreateOfflinePayExecution struct {
	*executor.ActionBaseExecution
	conf   model.CommonConf
	bizReq *action_model.CreateOfflinePayModel
	bizRsp execution_common.OfflinePayRsp
}

func NewOfflinePayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	options := []*executor.Option{{OptionID: executor.OptionAutoFire}}
	e := &CreateOfflinePayExecution{
		bizReq: &action_model.CreateOfflinePayModel{},
	}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.bizReq, options...)
	return e
}

func (e *CreateOfflinePayExecution) CheckParams(ctx context.Context) error {
	if e.bizReq.FinanceOrderType == 0 || e.bizReq.Amount <= 0 || e.bizReq.CheckData == nil {
		return errdef.NewParamsErr("必传参数为空")
	}
	checkData := e.bizReq.CheckData
	if checkData.ReceiveAccountNo == "" || checkData.RecevieAccountName == "" || checkData.BuyerName == "" {
		return errdef.NewParamsErr("线下转账校验参数为空")
	}
	return nil
}

func (e *CreateOfflinePayExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[WithdrawExecution] PreProcess failed, err=%+v", err)
		return err
	}

	// 判断是否做合同金额校验
	if !e.conf.CheckContStructField {
		return nil
	}

	// 校验
	param := &service.CheckAmountByContParam{
		OrderID:          e.GetActionOrderReq().OrderID,
		FinanceOrderType: e.bizReq.FinanceOrderType,
		Amount:           e.bizReq.Amount,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckAmountByCont(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[WithdrawExecution] CheckAmountByCont failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[WithdrawExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "出款合同校验未通过")
	}

	return nil
}

func (e *CreateOfflinePayExecution) Process(ctx context.Context) error {
	serviceReq, bizErr := e.buildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CreateOfflinePayExecution] err=%s", bizErr.Error())
		return bizErr
	}

	payNo, bizErr := service.NewTradePayment().CreateOfflinePay(ctx, serviceReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[CreateOfflinePayExecution] err=%s", bizErr.Error())
		return bizErr
	}
	// 更新tag
	if len(e.bizReq.TagMap) > 0 {
		if bizErr = service.NewOrderService().UpdateOrderTag(ctx, e.ActionBaseExecution.GetActionOrderReq().OrderID, e.bizReq.TagMap); bizErr != nil {
			logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}
	e.bizRsp = execution_common.OfflinePayRsp{
		PayOrderNo: payNo,
	}
	return nil
}

func (e *CreateOfflinePayExecution) Result() interface{} {
	str, _ := utils.Marshal(e.bizRsp)
	return str
}

func (e *CreateOfflinePayExecution) buildReq(ctx context.Context) (*payment.CreateOfflinePayReq, *errdef.BizErr) {
	var (
		payModel = e.bizReq
		order    = e.GetOrder()
		fweOrder = order.FweOrder
		bizErr   *errdef.BizErr
	)

	financeOrder := packer.FinanceGetByType(order.FinanceList, payModel.FinanceOrderType)
	if financeOrder == nil {
		bizErr = errdef.NewParamsErr("no finance order")
		return nil, bizErr
	}
	if financeOrder.Amount != e.bizReq.Amount {
		bizErr = errdef.NewParamsErr("支付金额与资金单不一致")
		return nil, bizErr
	}

	serviceReq := &payment.CreateOfflinePayReq{
		Identity:           e.GetBizIdentity(),
		OrderID:            fweOrder.OrderID,
		FinanceType:        payModel.FinanceOrderType,
		Currency:           nil,
		Amount:             payModel.Amount,
		CheckData:          payModel.CheckData,
		Extra:              nil,
		CallbackEvent:      utils.MakeCallbackEvent(payModel.CallbackEvent),
		CallbackExtra:      "",
		CloseCallbackEvent: utils.MakeCallbackEvent(payModel.CloseCallbackEvent),
		Base:               base.NewBase(),
		FinancoreOrderID:   conv.StringPtr(financeOrder.FinanceOrderID),
	}

	return serviceReq, nil
}
