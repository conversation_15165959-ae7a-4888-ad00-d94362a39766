package nc_shop

import (
	"context"

	"code.byted.org/motor/fwe_trade_common/scene/nc_scene/nc_small_deposit"
	pkgorder "code.byted.org/motor/trade_pkg/biz/order"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/callback"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type ContFinishWithBigDepositSkipExecution struct {
	*callback.ContCallbackBaseExecution
}

func NewContFinishWithBigDepositSkipExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &ContFinishWithBigDepositSkipExecution{}
	var opts []*executor.Option
	opts = append(opts, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: e.buildCondition,
	})
	e.ContCallbackBaseExecution = callback.NewContCallbackWithOpt(ctx, actionReq, nil, opts...)
	return e
}

func (e *ContFinishWithBigDepositSkipExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	mp = make(map[string]interface{})

	if bizErr = e.insertFinanceAmountCond(ctx, mp, consts.NCShopBigDeposit.Int32(), statemachine.BigDepositPriceKey); bizErr != nil {
		return nil, bizErr
	}
	if bizErr = e.insertFinanceAmountCond(ctx, mp, consts.NCShopBrokerageFull.Int32(), nc_small_deposit.BrokeragePriceKey); bizErr != nil {
		return nil, bizErr
	}

	bizOrderPattern := pkgorder.UnBuildOrderPattern(e.GetOrder().BizExtra)
	if bizOrderPattern != "" {
		mp[nc_small_deposit.BrokerageModeKey] = bizOrderPattern.IsRakeBack()
		mp[nc_small_deposit.BrokerageHorizontalModeKey] = bizOrderPattern.IsRakeBackHorizontalBusiness()
		mp[nc_small_deposit.BrokerageNonHorizontalModeKey] = bizOrderPattern.IsRakeBackNonHorizontalBusiness()
	}

	return mp, nil
}

func (e *ContFinishWithBigDepositSkipExecution) insertFinanceAmountCond(ctx context.Context, mp map[string]interface{}, fType int32, condKey string) *errdef.BizErr {

	var finance = packer.FinanceGetByType(e.GetOrder().FinanceList, fType)
	if finance != nil && finance.Status != int32(fwe_trade_common.FinanceStatus_Closed) {
		mp[condKey] = finance.Amount
		if finance.Amount == 0 {
			bizErr := service.NewFinanceOrderService().UpdateOrderFinance(ctx, finance.FinanceOrderID, &service_model.UpdateFinanceParams{
				UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Complete)),
			})
			if bizErr != nil {
				logs.CtxError(ctx, "[ContFinishWithBigDepositSkipExecution] err=%v", bizErr.Error())
				return bizErr
			}
		}
	} else {
		mp[condKey] = int64(0)
	}

	return nil
}
