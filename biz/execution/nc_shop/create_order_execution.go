package nc_shop

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_common/scene/nc_scene/nc_small_deposit"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type CreateOrderExecution struct {
	*executor.CreateBaseExecution
	conf model.ConfigWithFinance
}

func NewCreateOrderExecution(ctx context.Context, createReq interface{}) executor.IExecution {
	t := &CreateOrderExecution{}
	t.CreateBaseExecution = executor.NewCreateBaseExecution(ctx, createReq.(*engine.CreateOrderReq), &t.conf)
	return t
}

func (e *CreateOrderExecution) Process(ctx context.Context) error {
	var (
		err          error
		bizErr       *errdef.BizErr
		conf         = e.conf
		createReq    = e.GetCreateOrderReq()
		bizScene     = consts.GetBizScene(createReq.GetIdentity().GetBizScene())
		tradeType    = int32(consts.GetTradeTypeByBizScene(bizScene))
		orderService = service.NewOrderService()
		productInfo  = createReq.GetProductInfo()
		stateMachine = e.GetStateMachine()
	)
	// 驱动状态
	err = stateMachine.Fire(ctx, consts.CreateAction, e.conditionMap(createReq))
	if err != nil {
		bizErr = errdef.NewBizErr(errdef.ServerException, err, "CreateOrderExecution")
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	bizErr = service.NewSplitInfoService().MCreateSplitInfo(ctx, e.GetOrderID(), createReq.SplitInfo)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}

	// pack
	fweOrder := &db_model.FweOrder{
		TenantType:         int32(createReq.GetIdentity().GetTenantType()),
		BizScene:           bizScene.Int32(),
		SmVersion:          createReq.GetIdentity().GetSmVersion(),
		OrderID:            e.GetOrderID(),
		OrderStatus:        int32(stateMachine.CurState()),
		OrderName:          createReq.GetOrderName(),
		OrderDesc:          createReq.GetOrderDesc(),
		ProductID:          productInfo.GetProductID(),
		ProductType:        int32(productInfo.GetProductType()),
		ProductName:        productInfo.GetProductName(),
		ProductDetail:      conv.StringPtr(packer.CommonProductDetailSerialize(productInfo.ProductDetail)),
		ProductExtra:       productInfo.ProductExtra,
		SkuID:              productInfo.GetSkuID(),
		ProductQuantity:    int32(productInfo.GetProductQuantity()),
		ProductUnitPrice:   productInfo.GetProductUnitPrice(),
		TotalAmount:        packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList()),
		TotalPayAmount:     packer.CommonFinanceGetAmountByTypes(createReq.GetFinanceList()),
		TotalSubsidyAmount: 0,
		TradeType:          tradeType,
		BuyerID:            packer.CommonTradeSubjectIDGet(createReq.BuyerInfo),
		BuyerExtra:         conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.BuyerInfo)),
		SellerID:           packer.CommonTradeSubjectIDGet(createReq.SellerInfo),
		SellerExtra:        conv.StringPtr(packer.CommonTradeSubjectSerialize(createReq.SellerInfo)),
		IsTest:             conv.BoolToInt32(createReq.IsTest),
		Creator:            createReq.GetOperator().GetOperatorID(),
		CreatorName:        createReq.GetOperator().GetOperatorName(),
		Operator:           createReq.GetOperator().GetOperatorID(),
		OperatorName:       createReq.GetOperator().GetOperatorName(),
		IdempotentID:       createReq.GetIdemID(),
	}

	// 上游total传了，就直接用上游的
	if createReq.TotalAmount != 0 {
		fweOrder.TotalAmount = createReq.TotalAmount
	}

	financeList, bizErr := packer.BuildFinanceListV1(ctx, fweOrder, &conf, createReq.GetFinanceList())
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}
	order := &service_model.Order{
		FweOrder:    fweOrder,
		FinanceList: financeList,
		TagMap:      createReq.OrderTag,
		BizExtra:    createReq.Extra,
	}

	bizErr = orderService.CreateOrder(ctx, order)
	if bizErr != nil {
		logs.CtxError(ctx, "[CreateOrderExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *CreateOrderExecution) Result() interface{} {
	return e.GetOrderID()
}

func (e *CreateOrderExecution) conditionMap(createReq *engine.CreateOrderReq) map[string]interface{} {

	mp := map[string]interface{}{
		nc_small_deposit.IntentionPriceKey: 0,
	}
	financeList := createReq.GetFinanceList()
	for _, finance := range financeList {
		if finance.FinanceOrderType == consts.NCShopSmallDeposit.Int32() {
			mp[nc_small_deposit.IntentionPriceKey] = finance.Amount
		}
	}

	return mp
}
