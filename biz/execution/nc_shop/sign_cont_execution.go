package nc_shop

import (
	"context"
	"fmt"

	"code.byted.org/motor/fwe_trade_engine/biz/utils"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/nc_shop_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"github.com/bytedance/sonic"
)

type SignContStartExecution struct {
	*executor.ActionBaseExecution
	conf   nc_shop_model.Conf
	pReq   action_model.SignContReq
	result interface{}
}

func NewSignContStartExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	var opts []*executor.Option
	opts = append(opts, &executor.Option{OptionID: executor.OptionAutoFire})
	t := &SignContStartExecution{}
	t.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &t.conf, &t.pReq, opts...)
	return t
}

func (e *SignContStartExecution) PreProcess(ctx context.Context) error {
	err := e.ActionBaseExecution.PreProcess(ctx)
	if err != nil {
		logs.CtxError(ctx, "[UnionContCreateExecution] PreProcess failed, err=%+v", err)
		return err
	}

	if !e.conf.CheckContStructField {
		logs.CtxInfo(ctx, "[UnionContCreateExecution] CheckContStructField is false")
		return nil
	}

	// 校验
	param := &service.CheckContTotalAmountParam{
		TmplID:           e.pReq.ContTmplID,
		TmplParams:       e.pReq.ContTmplParams,
		OrderTotalAmount: e.GetOrder().FweOrder.TotalAmount,
		Params:           "",
		BizScene:         e.GetBizIdentity().BizScene,
		ContType:         e.pReq.ContType,
		OrderID:          e.GetActionOrderReq().OrderID,
	}
	pass, blockMsg, bizErr := service.NewSafeService().CheckContTotalAmount(ctx, param)
	if bizErr != nil {
		logs.CtxError(ctx, "[UnionContCreateExecution] CheckContTotalAmount failed, err=%s", bizErr.Error())
		return bizErr
	}
	if !pass {
		logs.CtxError(ctx, "[UnionContCreateExecution] safe check not pass, blockMsg=%s", blockMsg)
		return errdef.NewRawErr(errdef.SafeCheckNotPassErr, "合同总金额和订单不一致")
	}

	return nil

}

func (e *SignContStartExecution) Process(ctx context.Context) error {

	var (
		bizErr      *errdef.BizErr
		signLink    string
		sReq        *service_model.ContractCreateParam
		contService = service.NewContractService()
	)

	sReq, bizErr = e.buildReq(ctx)
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	signLink, bizErr = contService.CreateContractWithApply(ctx, sReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return bizErr
	}

	pRsp := &action_model.SignContRsp{SignLink: signLink}
	e.result, _ = sonic.MarshalString(pRsp)

	return nil
}

func (e *SignContStartExecution) Result() interface{} {
	return e.result
}

func (e *SignContStartExecution) buildReq(ctx context.Context) (sReq *service_model.ContractCreateParam, bizErr *errdef.BizErr) {

	// 必须要有回调事件
	pReq := e.pReq
	if pReq.CallbackEvent == "" {
		bizErr = errdef.NewParamsErr("callback is nil")
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return nil, bizErr
	}

	// 订单
	fweOrder := e.GetOrder().FweOrder
	actionReq := e.GetActionOrderReq()
	var pa, pb *service_model.SignPart

	pa, bizErr = e.buildSignPart(core.SignPosition_PB, false, fweOrder.BuyerID, fweOrder.BuyerExtra)
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return nil, bizErr
	}

	pb, bizErr = e.buildSignPart(core.SignPosition_PA, pReq.PBInner, fweOrder.SellerID, fweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxError(ctx, "[%s] err=%s", e.Name(), bizErr.Error())
		return nil, bizErr
	}

	sReq = &service_model.ContractCreateParam{
		OrderID:        fweOrder.OrderID,
		TenantType:     int32(actionReq.GetIdentity().GetTenantType()),
		BizScene:       actionReq.GetIdentity().GetBizScene(),
		ContType:       pReq.ContType,
		OperatorID:     conv.StrToInt64(actionReq.GetOperator().GetOperatorID(), 0),
		OperatorName:   actionReq.GetOperator().GetOperatorName(),
		TmplID:         pReq.ContTmplID,
		NeedSignNoCert: false,
		SmsTmplID:      pReq.SmsID,
		SmsChannelID:   pReq.SmsChannelID,
		TmplParams:     pReq.ContTmplParams,
		SignPartList:   []*service_model.SignPart{pa, pb},
		ReturnUrl:      pReq.ReturnUrl,
		InOutData: &service_model.InOutData{
			Currency: int32(core.Currency_CNY),
			TotalIn:  fweOrder.TotalAmount,
			TotalOut: fweOrder.TotalAmount,
		},
		CallbackEvent: utils.MakeCallbackEvent(pReq.CallbackEvent),
		CallbackExtra: utils.MakeContractCallbackExtra(fweOrder.OrderID),
	}

	return
}

func (e *SignContStartExecution) buildSignPart(position core.SignPosition, isInner bool, tradeID string, tradeStr *string) (data *service_model.SignPart, bizErr *errdef.BizErr) {
	data = &service_model.SignPart{
		SignPosition: int32(position),
		IsInner:      isInner,
	}
	if tradeStr == nil {
		bizErr = errdef.NewParamsErr("trade str is nil")
		return
	}
	var tradeInfo *fwe_trade_common.TradeSubjectInfo
	tradeInfo, bizErr = packer.CommonTradeSubjectDeserialize(tradeID, *tradeStr)
	if bizErr != nil {
		return
	}
	switch tradeInfo.SubjectType {
	case fwe_trade_common.TradeSubjectType_Person:
		if tradeInfo.PersonInfo == nil {
			bizErr = errdef.NewParamsErr("seller subject person info err")
			return
		}
		data.CardType = int32(core.CardType_Identity)
		data.IdentName = tradeInfo.PersonInfo.PersonName
		data.IdentID = tradeInfo.PersonInfo.IDCard
		data.SignerName = tradeInfo.PersonInfo.PersonName
		data.SignerPhone = tradeInfo.PersonInfo.PersonPhone
	case fwe_trade_common.TradeSubjectType_Company:
		if tradeInfo.CompanyInfo == nil {
			bizErr = errdef.NewParamsErr("seller subject company info err")
			return
		}
		data.CardType = int32(core.CardType_CreditCode)
		data.IdentName = tradeInfo.CompanyInfo.CompanyName
		data.IdentID = tradeInfo.CompanyInfo.CreditCode
		data.SignerName = tradeInfo.CompanyInfo.OwnerName
		data.SignerPhone = tradeInfo.CompanyInfo.OwnerPhone
	case fwe_trade_common.TradeSubjectType_FweMerchant:
		if tradeInfo.FweMerchant == nil || tradeInfo.FweMerchant.FweAccountID == "" {
			bizErr = errdef.NewParamsErr("seller subject fwe info err")
			return
		}
		fweID := tradeInfo.FweMerchant.FweAccountID
		var companyInfo *nc_shop_model.ConfCompany
		if companyInfo = e.conf.CompanyMap[fweID]; companyInfo == nil {
			bizErr = errdef.NewParamsErr(fmt.Sprintf("fwe id = %s not found tcc", fweID))
			return
		}
		data.CardType = int32(core.CardType_CreditCode)
		data.IdentName = companyInfo.Name
		data.IdentID = companyInfo.CreditCode
	default:
		bizErr = errdef.NewParamsErr(fmt.Sprintf("subject type=%d illegal", tradeInfo.SubjectType))
		return
	}
	return
}
