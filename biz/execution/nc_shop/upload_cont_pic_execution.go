package nc_shop

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/nc_shop_model"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type UploadContPicExecution struct {
	*executor.ActionBaseExecution
	req nc_shop_model.UploadContPicReq
}

func NewUploadContPicExecution(ctx context.Context, actionReq interface{}) executor.IExecution {

	logs.CtxError(ctx, "[UploadContPicExecution] Debug start =%v", actionReq)
	e := &UploadContPicExecution{}
	var opts []*executor.Option
	opts = append(opts, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: e.buildCondition,
	})
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.req, opts...)
	logs.CtxError(ctx, "[UploadContPicExecution] Debug end =%v", e.req.NeedRefund)
	return e
}

func (e *UploadContPicExecution) Process(ctx context.Context) error {
	var (
		orderID = e.GetActionOrderReq().GetOrderID()
		bizErr  *errdef.BizErr
	)
	if len(e.req.Extra) != 0 {
		bizErr = service.NewOrderService().UpdateOrderExtraMarshal(ctx, orderID, e.req.Extra)
		if bizErr != nil {
			logs.CtxError(ctx, "[UploadContPicExecution] err=%v", bizErr.Error())
			return bizErr
		}
	}

	return nil
}

func (e *UploadContPicExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	mp = make(map[string]interface{})

	{
		mp[statemachine.NeedRefund] = e.req.NeedRefund
	}
	logs.CtxError(ctx, "[UploadContPicExecution] Debug1=%v", e.req)
	logs.CtxError(ctx, "[UploadContPicExecution] Debug2=%v", e.req.NeedRefund)
	logs.CtxError(ctx, "[UploadContPicExecution] Debug3=%v", mp)

	return mp, nil
}
