package nc_shop

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/statemachine/nc_state"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/nc_shop_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type PurchasePayWithdraw struct {
	*executor.ActionBaseExecution
	productReq *action_model.WithdrawReq
	conf       nc_shop_model.ConfPurchase
}

func NewPurchasePayExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &PurchasePayWithdraw{}
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), &e.conf, &e.productReq)
	return e
}

func (e *PurchasePayWithdraw) Process(ctx context.Context) error {
	var (
		bizErr    *errdef.BizErr
		proReq    = e.productReq
		order     = e.GetOrder().FweOrder
		buyerInfo *fwe_trade_common.TradeSubjectInfo
		machine   = e.GetStateMachine()
	)

	// 支付金额为0，直接跳过
	if proReq.Amount <= 0 {
		bizErr = e.FireWithCondition(ctx, map[string]interface{}{
			nc_state.AmountConditionKey:    0,
			nc_state.TradeTypeConditionKey: "unknown",
		})
		if bizErr != nil {
			logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
			return bizErr
		}
		bizErr = service.NewOrderService().UpdateOrder(ctx, order.OrderID,
			&service_model.UpdateOrderParams{
				WhereOrderStatus:   []int32{int32(machine.GetOriginalState())},
				UpdateOrderStatus:  conv.Int32Ptr(int32(machine.CurState())),
				UpdateBeforeStatus: conv.Int32Ptr(int32(machine.GetOriginalState())),
			})
		if bizErr != nil {
			logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
			return bizErr
		}
		return nil
	}

	// 支付金额大于0，必须传卖方信息
	if proReq == nil || proReq.SellerInfo == nil {
		bizErr = errdef.NewParamsErr("seller info is nil")
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
		return bizErr
	}

	// 订单上买方信息
	if order.BuyerExtra == nil || order.BuyerID == "" {
		bizErr = errdef.NewParamsErr("not find buyer extra")
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
		return bizErr
	}
	buyerInfo, bizErr = packer.CommonTradeSubjectDeserialize(order.BuyerID, *order.BuyerExtra)
	if bizErr != nil {
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
		return bizErr
	}
	if buyerInfo.SubjectType != fwe_trade_common.TradeSubjectType_FweMerchant ||
		buyerInfo.FweMerchant == nil || buyerInfo.FweMerchant.FweAccountID == "" {
		bizErr = errdef.NewParamsErr("buyer info fwe account id is nil")
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
		return bizErr
	}

	// 车源平台
	if proReq.SellerInfo.SubjectType == fwe_trade_common.TradeSubjectType_CarSourcePlatform {
		bizErr = e.FireWithCondition(ctx, map[string]interface{}{
			nc_state.AmountConditionKey:    0,
			nc_state.TradeTypeConditionKey: consts.TradeTypeTransferHz.String(),
		})
		if bizErr != nil {
			logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
			return bizErr
		}
		var rpcReq *payment.TransferReq
		rpcReq, bizErr = e.buildTransferReq(ctx, proReq.SellerInfo, buyerInfo)
		if bizErr != nil {
			logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
			return bizErr
		}
		bizErr = service.NewTradePayment().Transfer(ctx, rpcReq)
		if bizErr != nil {
			logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
			return bizErr
		}
		bizErr = service.NewOrderService().UpdateOrder(ctx, order.OrderID,
			&service_model.UpdateOrderParams{
				WhereOrderStatus:   []int32{int32(machine.GetOriginalState())},
				UpdateOrderStatus:  conv.Int32Ptr(int32(machine.CurState())),
				UpdateBeforeStatus: conv.Int32Ptr(int32(machine.GetOriginalState())),
			})
		if bizErr != nil {
			logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
			return bizErr
		}
		return nil
	}

	// 银行卡
	bizErr = e.FireWithCondition(ctx, map[string]interface{}{
		nc_state.AmountConditionKey:    proReq.Amount,
		nc_state.TradeTypeConditionKey: consts.TradeTypeWithdrawBank.String(),
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
		return bizErr
	}
	var rpcReq *payment.WithdrawDepositReq
	rpcReq, bizErr = e.buildWithdrawReq(ctx, proReq.SellerInfo, buyerInfo)
	if bizErr != nil {
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
		return bizErr
	}
	_, bizErr = service.NewTradePayment().WithdrawDeposit(ctx, rpcReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
		return bizErr
	}
	bizErr = service.NewOrderService().UpdateOrder(ctx, order.OrderID,
		&service_model.UpdateOrderParams{
			WhereOrderStatus:   []int32{int32(machine.GetOriginalState())},
			UpdateOrderStatus:  conv.Int32Ptr(int32(machine.CurState())),
			UpdateBeforeStatus: conv.Int32Ptr(int32(machine.GetOriginalState())),
		})
	if bizErr != nil {
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *PurchasePayWithdraw) buildTransferReq(ctx context.Context, sellerInfo, buyerInfo *fwe_trade_common.TradeSubjectInfo) (*payment.TransferReq, *errdef.BizErr) {
	var (
		bizErr      *errdef.BizErr
		conf        = e.conf
		proReq      = e.productReq
		finance     *db_model.FFinanceOrder
		financeList = e.GetOrder().FinanceList
		action      = e.GetActionOrderReq().GetAction()
		order       = e.GetOrder().FweOrder
	)

	// 卖方信息（收款方）
	if sellerInfo.CarSourcePlatform.ShopID == "" {
		bizErr = errdef.NewParamsErr("seller info car source shop id is nil")
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%v", bizErr.Error())
		return nil, bizErr
	}
	carSourceKey := fmt.Sprintf("car_source_platform_%s", sellerInfo.CarSourcePlatform.ShopID)
	payeeConf := conf.CompanyMap[carSourceKey]
	if payeeConf == nil || payeeConf.MerchantID == "" || payeeConf.Uid == "" {
		bizErr = errdef.NewParamsErr("transfer payee conf not found")
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%s", bizErr.Error())
		return nil, bizErr
	}

	// 不同阶段资金单
	switch action {
	case nc_state.NCPurchasePayEarnestStartEt.Value():
		finance = packer.FinanceGetByType(financeList, CommonConsts.FinanceEarnest.Value())
	case nc_state.NCPurchasePayFinalStartEt.Value():
		finance = packer.FinanceGetByType(financeList, CommonConsts.FinanceFinal.Value())
	default:
		bizErr = errdef.NewParamsErr(fmt.Sprintf("action %s not support", action))
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%s", bizErr.Error())
		return nil, bizErr
	}

	// 不能没有资金单
	if finance == nil {
		bizErr = errdef.NewParamsErr("finance order not find")
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%s", bizErr.Error())
		return nil, bizErr
	}

	// 买方信息（出款方）
	buyerFweID := buyerInfo.FweMerchant.FweAccountID
	payerConf := conf.CompanyMap[buyerFweID]
	if payerConf == nil || payerConf.MerchantID == "" || payerConf.Uid == "" {
		bizErr = errdef.NewParamsErr(fmt.Sprintf("transfer payer conf not found buyer fwe id = %s", buyerFweID))
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%s", bizErr.Error())
		return nil, bizErr
	}

	rpcReq := &payment.TransferReq{
		Identity:          e.GetBizIdentity(),
		MerchantID:        payerConf.MerchantID,
		AppID:             payerConf.AppID,
		MerchantName:      payerConf.MerchantName,
		UID:               payerConf.Uid,
		UIDType:           payerConf.UidType,
		FinanceOrderID:    finance.FinanceOrderID,
		TransferOrderNo:   finance.FinanceOrderID,
		TransferOrderName: order.OrderName,
		TransferOrderDesc: proReq.WithdrawDesc, // 转账描述
		TradeTime:         time.Now().Unix(),
		Currency:          payment.CurrencyTypePtr(payment.CurrencyType_CNY),
		Amount:            proReq.Amount,
		PayerInfo: &payment.Participant{
			IdentifyType: payerConf.IdentifyType,
			Aid:          nil,
			MerchantID:   payerConf.MerchantID,
			UID:          payerConf.Uid,
			UIDType:      int32(payerConf.UidType),
			AppID:        payerConf.AppID,
		},
		PayeeInfo: &payment.Participant{
			IdentifyType: payeeConf.IdentifyType,
			Aid:          nil,
			MerchantID:   payeeConf.MerchantID,
			UID:          payeeConf.Uid,
			UIDType:      int32(payeeConf.UidType),
			AppID:        payeeConf.AppID,
		},
		FcType:      conv.StringPtr(payerConf.FcType),
		FcSceneCode: conv.Int64Ptr(payerConf.FcSceneCode),
		Extra:       nil,
		Operator:    e.GetActionOrderReq().GetOperator(),
	}
	return rpcReq, nil
}

func (e *PurchasePayWithdraw) buildWithdrawReq(ctx context.Context, sellerInfo, buyerInfo *fwe_trade_common.TradeSubjectInfo) (*payment.WithdrawDepositReq, *errdef.BizErr) {
	var (
		bizErr         *errdef.BizErr
		conf           = e.conf
		action         = e.GetActionOrderReq().GetAction()
		proReq         = e.productReq
		order          = e.GetOrder().FweOrder
		financeList    = e.GetOrder().FinanceList
		bankInfo       *fwe_trade_common.BankInfo
		finance        *db_model.FFinanceOrder
		callbackEt     string
		failCallbackEt string
		buyerFweID     = buyerInfo.FweMerchant.FweAccountID
	)

	// 银行卡填充
	switch sellerInfo.SubjectType {
	case fwe_trade_common.TradeSubjectType_Person:
		bankInfo = sellerInfo.PersonInfo.BankInfo
	case fwe_trade_common.TradeSubjectType_Company:
		bankInfo = sellerInfo.CompanyInfo.BankInfo
	default:
		bizErr = errdef.NewParamsErr("type is err")
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%s", bizErr.Error())
		return nil, bizErr
	}

	payerConf := conf.CompanyMap[buyerFweID]
	if payerConf == nil || payerConf.MerchantID == "" {
		bizErr = errdef.NewParamsErr(fmt.Sprintf("fwe account %s payer conf not found", buyerFweID))
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%s", bizErr.Error())
		return nil, bizErr
	}

	// 不同阶段资金单、回调事件填充
	switch action {
	case nc_state.NCPurchasePayEarnestStartEt.Value():
		callbackEt = nc_state.NCPurchasePayEarnestSuccessCallbackEt.Value()
		failCallbackEt = nc_state.NCPurchasePayEarnestFailCallbackEt.Value()
		finance = packer.FinanceGetByType(financeList, CommonConsts.FinanceEarnest.Value())
	case nc_state.NCPurchasePayFinalStartEt.Value():
		callbackEt = nc_state.NCPurchasePayFinalSuccessCallbackEt.Value()
		failCallbackEt = nc_state.NCPurchasePayFinalFailCallbackEt.Value()
		finance = packer.FinanceGetByType(financeList, CommonConsts.FinanceFinal.Value())
	default:
		bizErr = errdef.NewParamsErr(fmt.Sprintf("action %s not support", action))
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%s", bizErr.Error())
		return nil, bizErr
	}

	// 不能没有资金单
	if finance == nil {
		bizErr = errdef.NewParamsErr("finance order not find")
		logs.CtxError(ctx, "[PurchasePayWithdraw] err=%s", bizErr.Error())
		return nil, bizErr
	}

	// 出款请求参数
	rpcReq := &payment.WithdrawDepositReq{
		OrderID:           order.OrderID,
		FinanceOrderID:    finance.FinanceOrderID,
		MerchantID:        payerConf.MerchantID,
		MerchantName:      payerConf.MerchantName,
		AppID:             payerConf.AppID,
		Amount:            proReq.Amount,
		WithdrawType:      payment.WithdrawType_TEMP_WITHDRAW,
		WitdhrawDesc:      proReq.WithdrawDesc,
		IPAddress:         "*************",
		Mid:               nil,
		Currency:          payment.CurrencyTypePtr(payment.CurrencyType_CNY),
		BankCardInfo:      bankInfo,
		Operator:          e.GetActionOrderReq().GetOperator(),
		Identity:          e.GetActionOrderReq().GetIdentity(),
		CallbackEvent:     callbackEt,
		FailCallbackEvent: failCallbackEt,
	}
	return rpcReq, nil
}
