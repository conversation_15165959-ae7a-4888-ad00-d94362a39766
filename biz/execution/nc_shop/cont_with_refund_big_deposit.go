package nc_shop

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/nc_shop/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
)

type ContFinishWithRefundBigDepositExecution struct {
	*common.ContFinishWithRefundExecution
}

func NewContFinishWithRefundBigDepositExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &ContFinishWithRefundBigDepositExecution{}
	e.ContFinishWithRefundExecution = common.NewContFinishWithRefundExecution(ctx, actionReq,
		consts.NCBigDepositBigEarnestRefundFinish, consts.NCShopBigDeposit.Int32())
	return e
}

type ContFinishWithRefundSmallBigDepositExecution struct {
	*common.ContFinishWithRefundExecution
}

func NewContFinishWithRefundSmallBigDepositExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &ContFinishWithRefundSmallBigDepositExecution{}
	e.ContFinishWithRefundExecution = common.NewContFinishWithRefundExecution(ctx, actionReq,
		consts.NCSmallDepositEarnestRefundFinish, 0)
	return e
}

type ContFinishWithRefundSmallDepositExecution struct {
	*common.ContFinishWithRefundExecution
}

func NewContFinishWithRefundSmallDepositExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &ContFinishWithRefundSmallDepositExecution{}
	e.ContFinishWithRefundExecution = common.NewContFinishWithRefundExecution(ctx, actionReq,
		consts.NCSmallDepositSmallEarnestRefundFinish, consts.NCShopSmallDeposit.Int32())
	return e
}

type ContFinishWithRefundFullExecution struct {
	*common.ContFinishWithRefundExecution
}

func NewContFinishWithRefundFullExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &ContFinishWithRefundFullExecution{}
	e.ContFinishWithRefundExecution = common.NewContFinishWithRefundExecution(ctx, actionReq,
		consts.NCBrokerageTotalRefundFinish, consts.NCShopBrokerageFull.Int32())
	return e
}
