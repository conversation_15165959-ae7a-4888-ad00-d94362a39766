package nc_shop

import (
	"context"

	"code.byted.org/motor/fwe_trade_common/scene/nc_scene/nc_small_deposit"

	pkgorder "code.byted.org/motor/trade_pkg/biz/order"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

type UpdateExtraFireExecution struct {
	*executor.ActionBaseExecution
	extraReq action_model.ExtraUpdateReq
}

func NewUpdateExtraFireExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &UpdateExtraFireExecution{}
	var opts []*executor.Option
	opts = append(opts, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: e.buildCondition,
	})
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.extraReq, opts...)
	return e
}

func (e *UpdateExtraFireExecution) Process(ctx context.Context) error {

	var (
		orderID = e.GetActionOrderReq().GetOrderID()
		bizErr  *errdef.BizErr
	)
	if len(e.extraReq.Extra) == 0 {
		return nil
	}

	bizErr = service.NewOrderService().UpdateOrderExtraMarshal(ctx, orderID, e.extraReq.Extra)
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateExtraFireExecution] err=%v", bizErr.Error())
		return bizErr
	}

	var totalAmount int64
	for _, v := range e.GetOrder().FinanceList {
		if v == nil {
			continue
		}
		totalAmount += v.Amount
	}

	bizErr = service.NewOrderService().UpdateOrder(ctx, orderID, &service_model.UpdateOrderParams{
		UpdateTotalPayAmount: conv.Int64Ptr(totalAmount),
	})
	if bizErr != nil {
		logs.CtxError(ctx, "[UpdateExtraFireExecution] err=%v", bizErr.Error())
		return bizErr
	}

	return nil
}

func (e *UpdateExtraFireExecution) buildCondition(ctx context.Context) (mp map[string]interface{}, bizErr *errdef.BizErr) {
	mp = make(map[string]interface{})

	{
		var finance = packer.FinanceGetByType(e.GetOrder().FinanceList, consts.NCShopBigDeposit.Int32())
		if finance != nil && finance.Status != int32(fwe_trade_common.FinanceStatus_Closed) {
			mp[statemachine.BigDepositPriceKey] = finance.Amount
			if e.GetOrder().FweOrder.OrderStatus == 9 && finance.Amount == 0 { // 大定-合同审批中 且 金额为0
				bizErr = service.NewFinanceOrderService().UpdateOrderFinance(ctx, finance.FinanceOrderID, &service_model.UpdateFinanceParams{
					UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Complete)),
				})
				if bizErr != nil {
					logs.CtxError(ctx, "[ContFinishWithBigDepositSkipExecution] err=%v", bizErr.Error())
					return nil, bizErr
				}
			}
		}
	}

	{
		var finance = packer.FinanceGetByType(e.GetOrder().FinanceList, consts.NCShopFinal.Int32())
		if finance != nil && finance.Status != int32(fwe_trade_common.FinanceStatus_Closed) {
			mp[statemachine.FinalPriceKey] = finance.Amount
			if e.GetOrder().FweOrder.OrderStatus == 10 && finance.Amount == 0 { // 验车-合同审批中 且 金额为0
				bizErr = service.NewFinanceOrderService().UpdateOrderFinance(ctx, finance.FinanceOrderID, &service_model.UpdateFinanceParams{
					UpdateFinanceStatus: conv.Int32Ptr(int32(fwe_trade_common.FinanceStatus_Complete)),
				})
				if bizErr != nil {
					logs.CtxError(ctx, "[ContFinishWithFinalSkipExecution] err=%v", bizErr.Error())
					return nil, bizErr
				}
			}
		}
	}

	{
		bizOrderPattern := pkgorder.UnBuildOrderPattern(e.GetOrder().BizExtra)
		if bizOrderPattern != "" {
			mp[nc_small_deposit.BrokerageModeKey] = bizOrderPattern.IsRakeBack()
			mp[nc_small_deposit.BrokerageHorizontalModeKey] = bizOrderPattern.IsRakeBackHorizontalBusiness()
			mp[nc_small_deposit.BrokerageNonHorizontalModeKey] = bizOrderPattern.IsRakeBackNonHorizontalBusiness()
		}
	}

	return mp, nil
}
