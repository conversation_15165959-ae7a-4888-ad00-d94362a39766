package nc_shop

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type unionPayExecution struct {
	*executor.ActionBaseExecution
	conf      model.CommonConf
	payReq    *action_model.CreateUnionPayReq
	execution executor.IExecution
	sourceReq interface{}
}

func NewPayUnionExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &unionPayExecution{
		payReq:    new(action_model.CreateUnionPayReq),
		sourceReq: sourceReq,
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, &exe.conf, exe.payReq)
	return exe
}

func (e *unionPayExecution) Init(ctx context.Context) error {
	if err := e.ActionBaseExecution.Init(ctx); err != nil {
		return err
	}

	if e.payReq.TradeType == "" {
		// load order data
		order, bizErr := service.NewOrderService().GetOrderByID(ctx, e.ActionBaseExecution.GetActionOrderReq().OrderID, &service.OrderOption{OptionID: service.OptionNeedFinance})
		if bizErr != nil {
			logs.CtxError(ctx, "[ActionBaseExecution] err=%s", bizErr.Error())
			return bizErr
		}
		if order == nil || order.FweOrder == nil || order.FweOrder.OrderID == "" {
			return errdef.NewParamsErr("orderID error, can not find order")
		}
		financeOrder := packer.FinanceGetByType(order.FinanceList, e.payReq.FinanceOrderType)
		if financeOrder == nil {
			return errdef.NewParamsErr("no finance order")
		}
		e.payReq.TradeType = financeOrder.TradeType
	}
	switch e.payReq.TradeType {
	case CommonConsts.FinancePayGuarantee.Value():
		e.execution = NewCreateGuaranteePayExecution(ctx, e.sourceReq)
	case CommonConsts.FinancePayPos.Value():
		e.execution = NewCreatePosPayExecution(ctx, e.sourceReq)
	case CommonConsts.FinancePayOffline.Value():
		e.execution = NewOfflinePayExecution(ctx, e.sourceReq)
	default:
		logs.CtxError(ctx, "[unionPayExecution.Init] unsupported trade type %v", e.payReq.TradeType)
		return errdef.NewParamsErr(fmt.Sprintf("unsupported trade type %v", e.payReq.TradeType))
	}
	return e.execution.Init(ctx)
}

func (e *unionPayExecution) CheckParams(ctx context.Context) error {
	if bizErr := e.execution.CheckParams(ctx); bizErr != nil {
		return bizErr
	}
	return nil
}

func (e *unionPayExecution) GetOrder(ctx context.Context) (*service_model.Order, error) {
	reqTradeType := e.payReq.TradeType
	switch reqTradeType {
	case CommonConsts.FinancePayGuarantee.Value():
		return (e.execution).(*CreateGuaranteePayExecution).GetOrder(), nil
	case CommonConsts.FinancePayPos.Value():
		return (e.execution).(*CreatePosPayExecution).GetOrder(), nil
	case CommonConsts.FinancePayOffline.Value():
		return (e.execution).(*CreateOfflinePayExecution).GetOrder(), nil
	default:
		logs.CtxError(ctx, "[unionPayExecution.updateFinanceOrder] unsupported trade type %v", reqTradeType)
		return nil, errdef.NewParamsErr(fmt.Sprintf("unsupported trade type %v", reqTradeType))
	}
}

func (e *unionPayExecution) PreProcess(ctx context.Context) error {
	err := e.execution.PreProcess(ctx)
	if err != nil {
		return err
	}
	order, err := e.GetOrder(ctx)
	if err != nil {
		return err
	}
	financeOrder := packer.FinanceGetByType(order.FinanceList, e.payReq.FinanceOrderType)
	if e.payReq.TradeType != financeOrder.TradeType {
		logs.CtxError(ctx, "[unionPayExecution.PreProcess] finance tradeType not matched %v. reqTradeType=%v, finance=%+v", e.payReq.TradeType, financeOrder)
		return errdef.NewParamsErr("finance tradeType not matched")
	}
	return err
}

func (e *unionPayExecution) Process(ctx context.Context) error {
	return e.execution.Process(ctx)
}

func (e *unionPayExecution) PostProcess(ctx context.Context) error {
	return e.execution.PostProcess(ctx)
}

func (e *unionPayExecution) Result() interface{} {
	if e.execution == nil {
		return nil
	}
	return e.execution.Result()
}

func (e *unionPayExecution) DebugLog(ctx context.Context) *model.OrderDebugLog {
	if e.execution == nil {
		return nil
	}
	return e.execution.DebugLog(ctx)
}
