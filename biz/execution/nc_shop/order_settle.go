package nc_shop

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/execution/common"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
)

type orderSettleExecution struct {
	*common.UnionSettleExecution
}

func NewOrderSettleExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	e := &orderSettleExecution{}
	e.UnionSettleExecution = common.NewUnionSettleBaseExecution(ctx, actionReq)
	return e
}

func (e *orderSettleExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.UnionSettleExecution.PreProcess(ctx); bizErr != nil {
		logs.CtxWarn(ctx, "[orderSettleExecution-PreProcess] base PreProcess error", bizErr.Error())
		return bizErr
	}
	conditions := map[string]interface{}{
		consts.CondParamRefundRemainingAmount: e.SettleAmount, // 退款剩余金额
	}
	if err := e.FireWithCondition(ctx, conditions); err != nil {
		logs.CtxWarn(ctx, "[orderSettleExecution-PreProcess] fire fsm failed, err=%+v", err.Error())
		return err
	}
	return nil
}

func (e *orderSettleExecution) Process(ctx context.Context) error {
	var (
		bizErr       *errdef.BizErr
		settleAmount = e.SettleAmount
	)

	// 金额为0，直接结束
	if settleAmount == int64(0) {
		logs.CtxWarn(ctx, "[orderSettleExecution-Process] settleAmount is 0, direct over", settleAmount)
		return nil
	}
	// 分账
	settleParam, bizErr := e.BuildReq(ctx)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[orderSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	mergeSettleNo, bizErr := service.NewUnionSettleService().UnionSettle(ctx, settleParam)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[orderSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}

	e.BizRsp = execution_common.SettleRsp{
		MergeSettleNo: mergeSettleNo,
	}
	return nil
}
