package nc_shop

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/service_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
)

type updateFinanceTradeTypeExecution struct {
	*executor.ActionBaseExecution
	bizReq       *action_model.UpdateFinanceTradeTypeReq
	conf         *model.ConfigWithFinance
	validPayList []*payment.FinancePay
	sourceReq    interface{}
}

func NewUpdateFinanceTradeTypeExecution(ctx context.Context, sourceReq interface{}) executor.IExecution {
	req := sourceReq.(*engine.ActionOrderReq)
	exe := &updateFinanceTradeTypeExecution{
		bizReq:    new(action_model.UpdateFinanceTradeTypeReq),
		conf:      new(model.ConfigWithFinance),
		sourceReq: sourceReq,
	}
	exe.ActionBaseExecution = executor.NewActionBaseExecution(ctx, req, exe.conf, exe.bizReq)
	return exe
}

func (e *updateFinanceTradeTypeExecution) PreProcess(ctx context.Context) error {
	if bizErr := e.ActionBaseExecution.PreProcess(ctx); bizErr != nil {
		return bizErr
	}
	// 查询有效支付单
	var (
		payModel         = e.bizReq
		validPayList     = make([]*payment.FinancePay, 0)
		activeStatusList = []fwe_trade_common.CommonStatus{fwe_trade_common.CommonStatus_ToHandle, fwe_trade_common.CommonStatus_Handling, fwe_trade_common.CommonStatus_Success}
	)
	queryReq := &payment.QueryFinancePayListReq{
		Identity:    e.GetBizIdentity(),
		OrderID:     e.GetOrder().FweOrder.OrderID,
		FinanceType: payModel.FinanceOrderType,
		Base:        base.NewBase(),
	}
	payList, bizErr := service.NewTradePayment().QueryFinancePayList(ctx, queryReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[unionPayExecution.CheckParams] query pay list failed, err=%s", bizErr.Error())
		return bizErr
	}
	for _, pay := range payList {
		if slices.Contains(activeStatusList, pay.Status) {
			validPayList = append(validPayList, pay)
		}
	}
	e.validPayList = validPayList

	// trade_type不允许替换
	if bizErr = e.checkPayListTradeType(ctx, validPayList); bizErr != nil {
		logs.CtxWarn(ctx, "[unionPayExecution.CheckParams] checkPayListTradeType not pass, err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *updateFinanceTradeTypeExecution) checkPayListTradeType(ctx context.Context, validPayList []*payment.FinancePay) *errdef.BizErr {
	if len(validPayList) == 0 {
		return nil
	}
	var (
		tradeType      = e.bizReq.NewTradeType
		existTradeType = validPayList[0].TradeType
	)

	if existTradeType != "" && existTradeType != tradeType {
		logs.CtxError(ctx, "[checkPayListTradeType] trade_type is not consist with exist_trade_type, "+
			"existTradeType=%s, tradeType=%s", existTradeType, tradeType)
		return errdef.NewRawErr(errdef.ExistDifferentPayTradeTypeErr, "支付方式不允许更换")
	}
	return nil
}

func (e *updateFinanceTradeTypeExecution) Process(ctx context.Context) error {
	// 修改资金单类型，每次调用都改，避免调用下游超时
	if bizErr := e.updateFinanceOrder(ctx); bizErr != nil {
		logs.CtxWarn(ctx, "[unionPayExecution.Process] updateFinanceOrder failed, err=%+v", bizErr.Error())
		return bizErr
	}
	return e.ActionBaseExecution.Process(ctx)
}

func (e *updateFinanceTradeTypeExecution) updateFinanceOrder(ctx context.Context) *errdef.BizErr {
	var (
		order        = e.GetOrder()
		financeType  = e.bizReq.FinanceOrderType
		reqTradeType = e.bizReq.NewTradeType
	)
	financeOrder := packer.FinanceGetByType(order.FinanceList, financeType)
	if financeOrder.TradeType == reqTradeType {
		logs.CtxInfo(ctx, "[unionPayExecution.updateFinanceOrder] no need update trade_type")
		return nil
	}
	if !slices.Contains([]int32{int32(fwe_trade_common.FinanceStatus_NotHandle), int32(fwe_trade_common.FinanceStatus_Handling)}, financeOrder.Status) {
		logs.CtxInfo(ctx, "[unionPayExecution.updateFinanceOrder] no need update trade_type")
		return nil
	}
	logs.CtxInfo(ctx, "[unionPayExecution.updateFinanceOrder] need update tradeType")
	updateFinanceParam := &service_model.UpdateFinanceParams{
		UpdateTradeType: &reqTradeType,
	}

	updateFinanceParam.UpdateMerchantID = conv.StringPtr(e.bizReq.MerchantId)
	updateFinanceParam.UpdateAppID = conv.StringPtr(e.bizReq.AppId)
	updateFinanceParam.UpdateMid = conv.StringPtr(e.bizReq.Mid)

	bizErr := service.NewFinanceOrderService().UpdateOrderFinance(ctx, financeOrder.FinanceOrderID, updateFinanceParam)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[unionPayExecution.updateFinanceOrder] update financeOrder failed, err=%+v", bizErr.Error())
		return bizErr
	}
	// 更新tag
	if len(e.bizReq.TagMap) > 0 {
		if bizErr = service.NewOrderService().UpdateOrderTag(ctx, e.ActionBaseExecution.GetActionOrderReq().OrderID, e.bizReq.TagMap); bizErr != nil {
			logs.CtxError(ctx, "[CreateFinanceExecution] err=%s", bizErr.Error())
			return bizErr
		}
	}
	return nil
}
