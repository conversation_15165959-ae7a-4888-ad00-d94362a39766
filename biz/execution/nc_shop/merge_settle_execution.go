package nc_shop

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	CommonConsts "code.byted.org/motor/fwe_trade_common/consts"
	"code.byted.org/motor/fwe_trade_common/statemachine/nc_state"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/errdef"
	"code.byted.org/motor/fwe_trade_engine/biz/executor"
	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"
	"code.byted.org/motor/fwe_trade_engine/biz/model/db_model"
	"code.byted.org/motor/fwe_trade_engine/biz/packer"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"sort"
	"strings"
)

type MergeSettleExecution struct {
	*executor.ActionBaseExecution
	productReq action_model.CreateSettleReq
}

func NewMergeSettleExecution(ctx context.Context, actionReq interface{}) executor.IExecution {
	var opts []*executor.Option
	e := &MergeSettleExecution{}
	opts = append(opts, &executor.Option{
		OptionID:      executor.OptionAutoFire,
		ConditionFunc: e.buildCondition,
	})
	e.ActionBaseExecution = executor.NewActionBaseExecution(ctx, actionReq.(*engine.ActionOrderReq), nil, &e.productReq, opts...)
	return e
}

func (e *MergeSettleExecution) buildCondition(ctx context.Context) (map[string]interface{}, *errdef.BizErr) {
	return map[string]interface{}{
		nc_state.NeedSettle: len(e.GetToSettleFinanceList()) > 0,
	}, nil
}

func (e *MergeSettleExecution) GetToSettleFinanceList() []*db_model.FFinanceOrder {
	var financeOrders []*db_model.FFinanceOrder
	for _, financeOrder := range e.GetOrder().FinanceList {
		if financeOrder.TradeType != CommonConsts.FinancePayOffline.Value() && financeOrder.MerchantID != "" && financeOrder.Amount > 0 {
			financeOrders = append(financeOrders, financeOrder)
		}
	}
	return financeOrders
}

func (e *MergeSettleExecution) Process(ctx context.Context) error {
	var (
		bizErr      *errdef.BizErr
		serviceReq  *payment.MergeSettleReq
		orderID     = e.GetActionOrderReq().GetOrderID()
		splitInfo   *fwe_trade_common.TradeSpiltInfo
		order       = e.GetOrder()
		merchantID  string // order.FinanceList[0].MerchantID // 随机拿一个merchantID，这里一定要保证，担保+pos的UID相同！！！
		shopService = service.NewAccountShop()
	)
	// 找到第一个不为空的 merchantID。如果全为线下支付时，merchantID 为空，不需要进行分账
	toSettleFinanceList := e.GetToSettleFinanceList()
	if len(toSettleFinanceList) == 0 {
		logs.CtxInfo(ctx, "[MergeSettleExecution] no need settle. origin FinanceList %+v", order.FinanceList)
		return nil
	}
	merchantID = toSettleFinanceList[0].MerchantID

	splitInfo, bizErr = service.NewSplitInfoService().GetSplitInfosByOrderID(ctx, orderID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[MergeSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}
	if splitInfo == nil || len(splitInfo.Detail) == 0 {
		logs.CtxWarn(ctx, "[MergeSettleExecution] data error orderID = %+v", orderID)
		return errdef.NewRawErr(errdef.DataErr, "splitInfo error ")
	}
	// 在新车场景下，同一个fwe_account_id 映射不同的 merchantID 应该得到同一结果,这里直接取第一个资金单的merchantID
	fweAccountIDs := make([]string, 0)
	for _, info := range splitInfo.Detail {
		// 找四轮id
		if info.SplitUIDType == fwe_trade_common.SplitUIDType_Shop {
			fweAccountIDs = append(fweAccountIDs, info.SplitUID)
		}
	}

	shopAccountMap, _, bizErr := shopService.MGetFinanceAccount(ctx, fweAccountIDs, merchantID)
	if bizErr != nil {
		logs.CtxError(ctx, "[MergeSettleExecution] MGetFinanceAccount error ,err = %+v", bizErr)
		return bizErr
	}
	if len(shopAccountMap) != len(fweAccountIDs) {
		logs.CtxError(ctx, "[MergeSettleExecution] MGetFinanceAccount error ,fweAccountIDs = %+v", fweAccountIDs)
		return errdef.NewRawErr(errdef.AccountShopErr, "data error")
	}
	// 转化uid
	paymentSplit, bizErr := packer.SplitInfoCommonList2PaymentList(splitInfo, shopAccountMap)
	if bizErr != nil {
		logs.CtxError(ctx, "[MergeSettleExecution] splitInfo packer error ,err = %+v", bizErr)
		return bizErr
	}
	//取出订单卖方
	sellerInfo, bizErr := packer.CommonTradeSubjectDeserialize(order.FweOrder.SellerID, *order.FweOrder.SellerExtra)
	if bizErr != nil {
		logs.CtxError(ctx, "[MergeSettleExecution] sellerInfo packer error ,err = %+v", bizErr)
		return bizErr
	}
	if sellerInfo == nil || sellerInfo.FweMerchant == nil {
		logs.CtxError(ctx, "[MergeSettleExecution] sellerInfo error, orderID = %+v", orderID)
		return errdef.NewRawErr(errdef.DataErr, "seller is error")
	}
	financeAccount, bizErr := shopService.GetFinanceAccountOne(ctx, sellerInfo.FweMerchant.FweAccountID, merchantID)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[MergeSettleExecution] GetFinanceAccountOne error,err = %+v", bizErr)
		return bizErr
	}
	serviceReq, bizErr = e.buildReq(paymentSplit, financeAccount)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[MergeSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}
	if serviceReq == nil {
		logs.CtxInfo(ctx, "[MergeSettleExecution] no need settle. origin FinanceList %+v", order.FinanceList)
		return nil
	}
	bizErr = service.NewTradePayment().MergeSettle(ctx, serviceReq)
	if bizErr != nil {
		logs.CtxWarn(ctx, "[MergeSettleExecution] err=%s", bizErr.Error())
		return bizErr
	}
	return nil
}

func (e *MergeSettleExecution) buildReq(splitInfo []*payment.SplitInfo, shopAccount *shop.FinanceAccount) (*payment.MergeSettleReq, *errdef.BizErr) {
	var (
		productReq      = e.productReq
		actionReq       = e.GetActionOrderReq()
		orderID         = actionReq.GetOrderID()
		order           = e.GetOrder()
		fweOrder        = order.FweOrder
		settleFinanceID string
		financeList     []string
	)
	settleFinanceID, financeList = e.groupFinanceOrderIDs(order.FinanceList)
	if len(financeList) == 0 {
		return nil, nil
	}
	serviceReq := &payment.MergeSettleReq{
		Identity:             e.GetBizIdentity(),
		FinanceList:          financeList,
		SplitList:            splitInfo,
		SettleFinanceOrderID: settleFinanceID,
		OrderID:              orderID,
		OrderName:            fweOrder.OrderName,
		IPAddress:            nil,
		Extra:                nil,
		CallbackEvent:        utils.MakeCallbackEvent(productReq.CallbackEvent),
		CallbackExtra:        "",
		IsOmitPosFee:         conv.BoolPtr(true),
		ReceiveUID:           *shopAccount.UID,
		ReceiveUIDType:       consts.ShopUidType,
	}
	return serviceReq, nil
}

func (e *MergeSettleExecution) groupFinanceOrderIDs(financeOrders []*db_model.FFinanceOrder) (string, []string) {
	if len(financeOrders) == 0 {
		return "", nil
	}
	list := make([]string, 0)
	for _, v := range financeOrders {
		if v.Amount <= 0 || v.TradeType == consts.TradeTypePayOffline.String() {
			continue
		}
		list = append(list, v.FinanceOrderID)
	}

	sort.Strings(list)

	return strings.Join(list, "-"), list
}
