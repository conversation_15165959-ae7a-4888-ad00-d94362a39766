// Code generated by Kitex v1.20.3. DO NOT EDIT.

package engine

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"

	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/execution_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
)

var (
	_ = base.KitexUnusedProtection
	_ = execution_common.KitexUnusedProtection
	_ = fwe_trade_common.KitexUnusedProtection
	_ = tenant_base.KitexUnusedProtection
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *CreateOrderReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentity bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 102:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField102(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 103:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField103(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 105:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField105(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 106:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField106(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 104:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField104(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 107:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField107(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 108:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField108(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 201:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField201(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 202:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField202(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 203:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField203(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 253:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField253(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField254(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateOrderReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateOrderReq[fieldId]))
}

func (p *CreateOrderReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderName = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderDesc = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.TradeType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.TradeType(v)
	}
	p.TradeType = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalAmount = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalPayAmount = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalSubsidyAmount = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField100(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BuyerInfo = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField101(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.SellerInfo = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField102(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewProductInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ProductInfo = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField103(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeSpiltInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.SplitInfo = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField105(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ServiceProviderInfo = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField106(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.TalentInfo = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField104(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.FinanceInfo, 0, size)
	values := make([]fwe_trade_common.FinanceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.FinanceList = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField107(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewPurchasePlan()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.PurchasePlan = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField108(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeOption()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.TradeOption = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.OrderTag = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField201(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Extra = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField202(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IsTest = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField203(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.IdemID = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField253(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField254(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *CreateOrderReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *CreateOrderReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateOrderReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField202(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField102(buf[offset:], w)
		offset += p.fastWriteField103(buf[offset:], w)
		offset += p.fastWriteField105(buf[offset:], w)
		offset += p.fastWriteField106(buf[offset:], w)
		offset += p.fastWriteField104(buf[offset:], w)
		offset += p.fastWriteField107(buf[offset:], w)
		offset += p.fastWriteField108(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
		offset += p.fastWriteField201(buf[offset:], w)
		offset += p.fastWriteField203(buf[offset:], w)
		offset += p.fastWriteField253(buf[offset:], w)
		offset += p.fastWriteField254(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateOrderReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field100Length()
		l += p.field101Length()
		l += p.field102Length()
		l += p.field103Length()
		l += p.field105Length()
		l += p.field106Length()
		l += p.field104Length()
		l += p.field107Length()
		l += p.field108Length()
		l += p.field200Length()
		l += p.field201Length()
		l += p.field202Length()
		l += p.field203Length()
		l += p.field253Length()
		l += p.field254Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateOrderReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderName)
	return offset
}

func (p *CreateOrderReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderDesc)
	return offset
}

func (p *CreateOrderReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.TradeType))
	return offset
}

func (p *CreateOrderReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TotalAmount)
	return offset
}

func (p *CreateOrderReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 7)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TotalPayAmount)
	return offset
}

func (p *CreateOrderReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 8)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TotalSubsidyAmount)
	return offset
}

func (p *CreateOrderReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 100)
	offset += p.BuyerInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateOrderReq) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 101)
	offset += p.SellerInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateOrderReq) fastWriteField102(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 102)
	offset += p.ProductInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateOrderReq) fastWriteField103(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 103)
	offset += p.SplitInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateOrderReq) fastWriteField105(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 105)
	offset += p.ServiceProviderInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateOrderReq) fastWriteField106(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 106)
	offset += p.TalentInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateOrderReq) fastWriteField104(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 104)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FinanceList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *CreateOrderReq) fastWriteField107(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 107)
	offset += p.PurchasePlan.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateOrderReq) fastWriteField108(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 108)
	offset += p.TradeOption.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateOrderReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.OrderTag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *CreateOrderReq) fastWriteField201(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 201)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Extra {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *CreateOrderReq) fastWriteField202(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 202)
	offset += thrift.Binary.WriteBool(buf[offset:], p.IsTest)
	return offset
}

func (p *CreateOrderReq) fastWriteField203(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetIdemID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 203)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.IdemID)
	}
	return offset
}

func (p *CreateOrderReq) fastWriteField253(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 253)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateOrderReq) fastWriteField254(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 254)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CreateOrderReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *CreateOrderReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderName)
	return l
}

func (p *CreateOrderReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderDesc)
	return l
}

func (p *CreateOrderReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *CreateOrderReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *CreateOrderReq) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *CreateOrderReq) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *CreateOrderReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BuyerInfo.BLength()
	return l
}

func (p *CreateOrderReq) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.SellerInfo.BLength()
	return l
}

func (p *CreateOrderReq) field102Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ProductInfo.BLength()
	return l
}

func (p *CreateOrderReq) field103Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.SplitInfo.BLength()
	return l
}

func (p *CreateOrderReq) field105Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ServiceProviderInfo.BLength()
	return l
}

func (p *CreateOrderReq) field106Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.TalentInfo.BLength()
	return l
}

func (p *CreateOrderReq) field104Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FinanceList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *CreateOrderReq) field107Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.PurchasePlan.BLength()
	return l
}

func (p *CreateOrderReq) field108Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.TradeOption.BLength()
	return l
}

func (p *CreateOrderReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.OrderTag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *CreateOrderReq) field201Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Extra {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *CreateOrderReq) field202Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *CreateOrderReq) field203Length() int {
	l := 0
	if p.IsSetIdemID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.IdemID)
	}
	return l
}

func (p *CreateOrderReq) field253Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *CreateOrderReq) field254Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *CreateOrderReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *CreateOrderReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateOrderReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderName != "" {
		p.OrderName = kutils.StringDeepCopy(src.OrderName)
	}

	if src.OrderDesc != "" {
		p.OrderDesc = kutils.StringDeepCopy(src.OrderDesc)
	}

	p.TradeType = src.TradeType

	p.TotalAmount = src.TotalAmount

	p.TotalPayAmount = src.TotalPayAmount

	p.TotalSubsidyAmount = src.TotalSubsidyAmount

	var _buyerInfo *fwe_trade_common.TradeSubjectInfo
	if src.BuyerInfo != nil {
		_buyerInfo = &fwe_trade_common.TradeSubjectInfo{}
		if err := _buyerInfo.DeepCopy(src.BuyerInfo); err != nil {
			return err
		}
	}
	p.BuyerInfo = _buyerInfo

	var _sellerInfo *fwe_trade_common.TradeSubjectInfo
	if src.SellerInfo != nil {
		_sellerInfo = &fwe_trade_common.TradeSubjectInfo{}
		if err := _sellerInfo.DeepCopy(src.SellerInfo); err != nil {
			return err
		}
	}
	p.SellerInfo = _sellerInfo

	var _productInfo *fwe_trade_common.ProductInfo
	if src.ProductInfo != nil {
		_productInfo = &fwe_trade_common.ProductInfo{}
		if err := _productInfo.DeepCopy(src.ProductInfo); err != nil {
			return err
		}
	}
	p.ProductInfo = _productInfo

	var _splitInfo *fwe_trade_common.TradeSpiltInfo
	if src.SplitInfo != nil {
		_splitInfo = &fwe_trade_common.TradeSpiltInfo{}
		if err := _splitInfo.DeepCopy(src.SplitInfo); err != nil {
			return err
		}
	}
	p.SplitInfo = _splitInfo

	var _serviceProviderInfo *fwe_trade_common.TradeSubjectInfo
	if src.ServiceProviderInfo != nil {
		_serviceProviderInfo = &fwe_trade_common.TradeSubjectInfo{}
		if err := _serviceProviderInfo.DeepCopy(src.ServiceProviderInfo); err != nil {
			return err
		}
	}
	p.ServiceProviderInfo = _serviceProviderInfo

	var _talentInfo *fwe_trade_common.TradeSubjectInfo
	if src.TalentInfo != nil {
		_talentInfo = &fwe_trade_common.TradeSubjectInfo{}
		if err := _talentInfo.DeepCopy(src.TalentInfo); err != nil {
			return err
		}
	}
	p.TalentInfo = _talentInfo

	if src.FinanceList != nil {
		p.FinanceList = make([]*fwe_trade_common.FinanceInfo, 0, len(src.FinanceList))
		for _, elem := range src.FinanceList {
			var _elem *fwe_trade_common.FinanceInfo
			if elem != nil {
				_elem = &fwe_trade_common.FinanceInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.FinanceList = append(p.FinanceList, _elem)
		}
	}

	var _purchasePlan *fwe_trade_common.PurchasePlan
	if src.PurchasePlan != nil {
		_purchasePlan = &fwe_trade_common.PurchasePlan{}
		if err := _purchasePlan.DeepCopy(src.PurchasePlan); err != nil {
			return err
		}
	}
	p.PurchasePlan = _purchasePlan

	var _tradeOption *fwe_trade_common.TradeOption
	if src.TradeOption != nil {
		_tradeOption = &fwe_trade_common.TradeOption{}
		if err := _tradeOption.DeepCopy(src.TradeOption); err != nil {
			return err
		}
	}
	p.TradeOption = _tradeOption

	if src.OrderTag != nil {
		p.OrderTag = make(map[string]string, len(src.OrderTag))
		for key, val := range src.OrderTag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.OrderTag[_key] = _val
		}
	}

	if src.Extra != nil {
		p.Extra = make(map[string]string, len(src.Extra))
		for key, val := range src.Extra {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Extra[_key] = _val
		}
	}

	p.IsTest = src.IsTest

	if src.IdemID != nil {
		var tmp string
		if *src.IdemID != "" {
			tmp = kutils.StringDeepCopy(*src.IdemID)
		}
		p.IdemID = &tmp
	}

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *CreateOrderResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateOrderResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CreateOrderResp[fieldId]))
}

func (p *CreateOrderResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *CreateOrderResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BizResponse = _field
	return offset, nil
}

func (p *CreateOrderResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *CreateOrderResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateOrderResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateOrderResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateOrderResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *CreateOrderResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BizResponse)
	return offset
}

func (p *CreateOrderResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *CreateOrderResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *CreateOrderResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BizResponse)
	return l
}

func (p *CreateOrderResp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *CreateOrderResp) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateOrderResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	if src.BizResponse != "" {
		p.BizResponse = kutils.StringDeepCopy(src.BizResponse)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *ActionOrderReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetAction bool = false
	var issetIdentity bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAction = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 253:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField253(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField254(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAction {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ActionOrderReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ActionOrderReq[fieldId]))
}

func (p *ActionOrderReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *ActionOrderReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Action = _field
	return offset, nil
}

func (p *ActionOrderReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BizRequest = _field
	return offset, nil
}

func (p *ActionOrderReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ActionCondition = _field
	return offset, nil
}

func (p *ActionOrderReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.TagMap = _field
	return offset, nil
}

func (p *ActionOrderReq) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OperateDesc = _field
	return offset, nil
}

func (p *ActionOrderReq) FastReadField253(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *ActionOrderReq) FastReadField254(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *ActionOrderReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *ActionOrderReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ActionOrderReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField253(buf[offset:], w)
		offset += p.fastWriteField254(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ActionOrderReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field253Length()
		l += p.field254Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ActionOrderReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *ActionOrderReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Action)
	return offset
}

func (p *ActionOrderReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BizRequest)
	return offset
}

func (p *ActionOrderReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ActionCondition)
	return offset
}

func (p *ActionOrderReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 12)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.TagMap {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *ActionOrderReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 13)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OperateDesc)
	return offset
}

func (p *ActionOrderReq) fastWriteField253(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 253)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *ActionOrderReq) fastWriteField254(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 254)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *ActionOrderReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ActionOrderReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *ActionOrderReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Action)
	return l
}

func (p *ActionOrderReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BizRequest)
	return l
}

func (p *ActionOrderReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ActionCondition)
	return l
}

func (p *ActionOrderReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.TagMap {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *ActionOrderReq) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OperateDesc)
	return l
}

func (p *ActionOrderReq) field253Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *ActionOrderReq) field254Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *ActionOrderReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *ActionOrderReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ActionOrderReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	if src.Action != "" {
		p.Action = kutils.StringDeepCopy(src.Action)
	}

	if src.BizRequest != "" {
		p.BizRequest = kutils.StringDeepCopy(src.BizRequest)
	}

	if src.ActionCondition != "" {
		p.ActionCondition = kutils.StringDeepCopy(src.ActionCondition)
	}

	if src.TagMap != nil {
		p.TagMap = make(map[string]string, len(src.TagMap))
		for key, val := range src.TagMap {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.TagMap[_key] = _val
		}
	}

	if src.OperateDesc != "" {
		p.OperateDesc = kutils.StringDeepCopy(src.OperateDesc)
	}

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *ActionOrderResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ActionOrderResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ActionOrderResp[fieldId]))
}

func (p *ActionOrderResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *ActionOrderResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BizResponse = _field
	return offset, nil
}

func (p *ActionOrderResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *ActionOrderResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ActionOrderResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ActionOrderResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ActionOrderResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *ActionOrderResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BizResponse)
	return offset
}

func (p *ActionOrderResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ActionOrderResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *ActionOrderResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BizResponse)
	return l
}

func (p *ActionOrderResp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *ActionOrderResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ActionOrderResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	if src.BizResponse != "" {
		p.BizResponse = kutils.StringDeepCopy(src.BizResponse)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *MGetOrderInfoReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentity bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField254(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetOrderInfoReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_MGetOrderInfoReq[fieldId]))
}

func (p *MGetOrderInfoReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.OrderIds = _field
	return offset, nil
}

func (p *MGetOrderInfoReq) FastReadField254(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *MGetOrderInfoReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *MGetOrderInfoReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *MGetOrderInfoReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField254(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *MGetOrderInfoReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field254Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *MGetOrderInfoReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.OrderIds {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *MGetOrderInfoReq) fastWriteField254(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 254)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *MGetOrderInfoReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *MGetOrderInfoReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.OrderIds {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *MGetOrderInfoReq) field254Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *MGetOrderInfoReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *MGetOrderInfoReq) DeepCopy(s interface{}) error {
	src, ok := s.(*MGetOrderInfoReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderIds != nil {
		p.OrderIds = make([]string, 0, len(src.OrderIds))
		for _, elem := range src.OrderIds {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.OrderIds = append(p.OrderIds, _elem)
		}
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *MGetOrderInfoResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetOrderInfoResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *MGetOrderInfoResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]*fwe_trade_common.OrderInfo, size)
	values := make([]fwe_trade_common.OrderInfo, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if l, err := _val.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field[_key] = _val
	}
	p.Data = _field
	return offset, nil
}

func (p *MGetOrderInfoResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *MGetOrderInfoResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *MGetOrderInfoResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *MGetOrderInfoResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *MGetOrderInfoResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 1)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Data {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRUCT, length)
	return offset
}

func (p *MGetOrderInfoResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *MGetOrderInfoResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Data {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += v.BLength()
	}
	return l
}

func (p *MGetOrderInfoResp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *MGetOrderInfoResp) DeepCopy(s interface{}) error {
	src, ok := s.(*MGetOrderInfoResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Data != nil {
		p.Data = make(map[string]*fwe_trade_common.OrderInfo, len(src.Data))
		for key, val := range src.Data {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val *fwe_trade_common.OrderInfo
			if val != nil {
				_val = &fwe_trade_common.OrderInfo{}
				if err := _val.DeepCopy(val); err != nil {
					return err
				}
			}

			p.Data[_key] = _val
		}
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *GetOrderLogReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetIdentity bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField254(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetOrderLogReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_GetOrderLogReq[fieldId]))
}

func (p *GetOrderLogReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *GetOrderLogReq) FastReadField254(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *GetOrderLogReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *GetOrderLogReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GetOrderLogReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField254(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GetOrderLogReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field254Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GetOrderLogReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *GetOrderLogReq) fastWriteField254(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 254)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *GetOrderLogReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *GetOrderLogReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *GetOrderLogReq) field254Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *GetOrderLogReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *GetOrderLogReq) DeepCopy(s interface{}) error {
	src, ok := s.(*GetOrderLogReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *GetOrderLogResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetOrderLogResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *GetOrderLogResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.OrderLog, 0, size)
	values := make([]fwe_trade_common.OrderLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Data = _field
	return offset, nil
}

func (p *GetOrderLogResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *GetOrderLogResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GetOrderLogResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GetOrderLogResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GetOrderLogResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.Data {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *GetOrderLogResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *GetOrderLogResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.Data {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *GetOrderLogResp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *GetOrderLogResp) DeepCopy(s interface{}) error {
	src, ok := s.(*GetOrderLogResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Data != nil {
		p.Data = make([]*fwe_trade_common.OrderLog, 0, len(src.Data))
		for _, elem := range src.Data {
			var _elem *fwe_trade_common.OrderLog
			if elem != nil {
				_elem = &fwe_trade_common.OrderLog{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Data = append(p.Data, _elem)
		}
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *BindUserOrderReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BindUserOrderReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *BindUserOrderReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.OrderIds = _field
	return offset, nil
}

func (p *BindUserOrderReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UID = _field
	return offset, nil
}

func (p *BindUserOrderReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *BindUserOrderReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *BindUserOrderReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *BindUserOrderReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *BindUserOrderReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.OrderIds {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *BindUserOrderReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.UID)
	return offset
}

func (p *BindUserOrderReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *BindUserOrderReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.OrderIds {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *BindUserOrderReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *BindUserOrderReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *BindUserOrderReq) DeepCopy(s interface{}) error {
	src, ok := s.(*BindUserOrderReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderIds != nil {
		p.OrderIds = make([]string, 0, len(src.OrderIds))
		for _, elem := range src.OrderIds {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.OrderIds = append(p.OrderIds, _elem)
		}
	}

	p.UID = src.UID

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *BindUserOrderResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BindUserOrderResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *BindUserOrderResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *BindUserOrderResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *BindUserOrderResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *BindUserOrderResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *BindUserOrderResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *BindUserOrderResp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *BindUserOrderResp) DeepCopy(s interface{}) error {
	src, ok := s.(*BindUserOrderResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *GetOrderInfoReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetIdentity bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField254(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetOrderInfoReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_GetOrderInfoReq[fieldId]))
}

func (p *GetOrderInfoReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *GetOrderInfoReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.NeedSettleInfo = _field
	return offset, nil
}

func (p *GetOrderInfoReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.NeedRefundInfo = _field
	return offset, nil
}

func (p *GetOrderInfoReq) FastReadField254(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *GetOrderInfoReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *GetOrderInfoReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GetOrderInfoReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField254(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GetOrderInfoReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field254Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GetOrderInfoReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *GetOrderInfoReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNeedSettleInfo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 2)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.NeedSettleInfo)
	}
	return offset
}

func (p *GetOrderInfoReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNeedRefundInfo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 3)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.NeedRefundInfo)
	}
	return offset
}

func (p *GetOrderInfoReq) fastWriteField254(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 254)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *GetOrderInfoReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *GetOrderInfoReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *GetOrderInfoReq) field2Length() int {
	l := 0
	if p.IsSetNeedSettleInfo() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *GetOrderInfoReq) field3Length() int {
	l := 0
	if p.IsSetNeedRefundInfo() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *GetOrderInfoReq) field254Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *GetOrderInfoReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *GetOrderInfoReq) DeepCopy(s interface{}) error {
	src, ok := s.(*GetOrderInfoReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	if src.NeedSettleInfo != nil {
		tmp := *src.NeedSettleInfo
		p.NeedSettleInfo = &tmp
	}

	if src.NeedRefundInfo != nil {
		tmp := *src.NeedRefundInfo
		p.NeedRefundInfo = &tmp
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *OrderInfoDetailResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderInfoDetailResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *OrderInfoDetailResp) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOrderInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.OrderInfo = _field
	return offset, nil
}

func (p *OrderInfoDetailResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *OrderInfoDetailResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *OrderInfoDetailResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *OrderInfoDetailResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *OrderInfoDetailResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.OrderInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *OrderInfoDetailResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *OrderInfoDetailResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.OrderInfo.BLength()
	return l
}

func (p *OrderInfoDetailResp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *OrderInfoDetailResp) DeepCopy(s interface{}) error {
	src, ok := s.(*OrderInfoDetailResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _orderInfo *fwe_trade_common.OrderInfo
	if src.OrderInfo != nil {
		_orderInfo = &fwe_trade_common.OrderInfo{}
		if err := _orderInfo.DeepCopy(src.OrderInfo); err != nil {
			return err
		}
	}
	p.OrderInfo = _orderInfo

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *UpdateOrderProductReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetProductDetail bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetProductDetail = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 253:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField253(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetProductDetail {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateOrderProductReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_UpdateOrderProductReq[fieldId]))
}

func (p *UpdateOrderProductReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *UpdateOrderProductReq) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewProductDetail()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ProductDetail = _field
	return offset, nil
}

func (p *UpdateOrderProductReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.TagMap = _field
	return offset, nil
}

func (p *UpdateOrderProductReq) FastReadField253(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *UpdateOrderProductReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *UpdateOrderProductReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpdateOrderProductReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField253(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpdateOrderProductReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field10Length()
		l += p.field253Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpdateOrderProductReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *UpdateOrderProductReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
	offset += p.ProductDetail.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpdateOrderProductReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 10)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.TagMap {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *UpdateOrderProductReq) fastWriteField253(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 253)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpdateOrderProductReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *UpdateOrderProductReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *UpdateOrderProductReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ProductDetail.BLength()
	return l
}

func (p *UpdateOrderProductReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.TagMap {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *UpdateOrderProductReq) field253Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *UpdateOrderProductReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *UpdateOrderProductReq) DeepCopy(s interface{}) error {
	src, ok := s.(*UpdateOrderProductReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	var _productDetail *fwe_trade_common.ProductDetail
	if src.ProductDetail != nil {
		_productDetail = &fwe_trade_common.ProductDetail{}
		if err := _productDetail.DeepCopy(src.ProductDetail); err != nil {
			return err
		}
	}
	p.ProductDetail = _productDetail

	if src.TagMap != nil {
		p.TagMap = make(map[string]string, len(src.TagMap))
		for key, val := range src.TagMap {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.TagMap[_key] = _val
		}
	}

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *UpdateOrderProductResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateOrderProductResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *UpdateOrderProductResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *UpdateOrderProductResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpdateOrderProductResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpdateOrderProductResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpdateOrderProductResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *UpdateOrderProductResp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *UpdateOrderProductResp) DeepCopy(s interface{}) error {
	src, ok := s.(*UpdateOrderProductResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *RepostOrderMessageReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetOrderAction bool = false
	var issetBeforeStatus bool = false
	var issetAfterStatus bool = false
	var issetIdentity bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderAction = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBeforeStatus = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAfterStatus = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField254(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOrderAction {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetBeforeStatus {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAfterStatus {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RepostOrderMessageReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_RepostOrderMessageReq[fieldId]))
}

func (p *RepostOrderMessageReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *RepostOrderMessageReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderAction = _field
	return offset, nil
}

func (p *RepostOrderMessageReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BeforeStatus = _field
	return offset, nil
}

func (p *RepostOrderMessageReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AfterStatus = _field
	return offset, nil
}

func (p *RepostOrderMessageReq) FastReadField254(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *RepostOrderMessageReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *RepostOrderMessageReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RepostOrderMessageReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField254(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RepostOrderMessageReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field254Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RepostOrderMessageReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *RepostOrderMessageReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderAction)
	return offset
}

func (p *RepostOrderMessageReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], p.BeforeStatus)
	return offset
}

func (p *RepostOrderMessageReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.AfterStatus)
	return offset
}

func (p *RepostOrderMessageReq) fastWriteField254(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 254)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RepostOrderMessageReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *RepostOrderMessageReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *RepostOrderMessageReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderAction)
	return l
}

func (p *RepostOrderMessageReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RepostOrderMessageReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RepostOrderMessageReq) field254Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *RepostOrderMessageReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *RepostOrderMessageReq) DeepCopy(s interface{}) error {
	src, ok := s.(*RepostOrderMessageReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	if src.OrderAction != "" {
		p.OrderAction = kutils.StringDeepCopy(src.OrderAction)
	}

	p.BeforeStatus = src.BeforeStatus

	p.AfterStatus = src.AfterStatus

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *RepostOrderMessageResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RepostOrderMessageResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RepostOrderMessageResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *RepostOrderMessageResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RepostOrderMessageResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RepostOrderMessageResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RepostOrderMessageResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *RepostOrderMessageResp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *RepostOrderMessageResp) DeepCopy(s interface{}) error {
	src, ok := s.(*RepostOrderMessageResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *TradeEngineServiceCreateOrderArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceCreateOrderArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceCreateOrderArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewCreateOrderReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *TradeEngineServiceCreateOrderArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceCreateOrderArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceCreateOrderArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceCreateOrderArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *TradeEngineServiceCreateOrderArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *TradeEngineServiceCreateOrderArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceCreateOrderArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *CreateOrderReq
	if src.Req != nil {
		_req = &CreateOrderReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *TradeEngineServiceCreateOrderResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceCreateOrderResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceCreateOrderResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewCreateOrderResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *TradeEngineServiceCreateOrderResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceCreateOrderResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceCreateOrderResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceCreateOrderResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TradeEngineServiceCreateOrderResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *TradeEngineServiceCreateOrderResult) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceCreateOrderResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *CreateOrderResp
	if src.Success != nil {
		_success = &CreateOrderResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *TradeEngineServiceActionOrderArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceActionOrderArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceActionOrderArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewActionOrderReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *TradeEngineServiceActionOrderArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceActionOrderArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceActionOrderArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceActionOrderArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *TradeEngineServiceActionOrderArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *TradeEngineServiceActionOrderArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceActionOrderArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *ActionOrderReq
	if src.Req != nil {
		_req = &ActionOrderReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *TradeEngineServiceActionOrderResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceActionOrderResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceActionOrderResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewActionOrderResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *TradeEngineServiceActionOrderResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceActionOrderResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceActionOrderResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceActionOrderResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TradeEngineServiceActionOrderResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *TradeEngineServiceActionOrderResult) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceActionOrderResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *ActionOrderResp
	if src.Success != nil {
		_success = &ActionOrderResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *TradeEngineServiceGetOrderInfoArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceGetOrderInfoArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceGetOrderInfoArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewGetOrderInfoReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *TradeEngineServiceGetOrderInfoArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceGetOrderInfoArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceGetOrderInfoArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceGetOrderInfoArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *TradeEngineServiceGetOrderInfoArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *TradeEngineServiceGetOrderInfoArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceGetOrderInfoArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *GetOrderInfoReq
	if src.Req != nil {
		_req = &GetOrderInfoReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *TradeEngineServiceGetOrderInfoResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceGetOrderInfoResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceGetOrderInfoResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewOrderInfoDetailResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *TradeEngineServiceGetOrderInfoResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceGetOrderInfoResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceGetOrderInfoResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceGetOrderInfoResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TradeEngineServiceGetOrderInfoResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *TradeEngineServiceGetOrderInfoResult) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceGetOrderInfoResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *OrderInfoDetailResp
	if src.Success != nil {
		_success = &OrderInfoDetailResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *TradeEngineServiceMGetOrderInfoArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceMGetOrderInfoArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceMGetOrderInfoArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewMGetOrderInfoReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *TradeEngineServiceMGetOrderInfoArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceMGetOrderInfoArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceMGetOrderInfoArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceMGetOrderInfoArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *TradeEngineServiceMGetOrderInfoArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *TradeEngineServiceMGetOrderInfoArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceMGetOrderInfoArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *MGetOrderInfoReq
	if src.Req != nil {
		_req = &MGetOrderInfoReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *TradeEngineServiceMGetOrderInfoResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceMGetOrderInfoResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceMGetOrderInfoResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewMGetOrderInfoResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *TradeEngineServiceMGetOrderInfoResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceMGetOrderInfoResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceMGetOrderInfoResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceMGetOrderInfoResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TradeEngineServiceMGetOrderInfoResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *TradeEngineServiceMGetOrderInfoResult) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceMGetOrderInfoResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *MGetOrderInfoResp
	if src.Success != nil {
		_success = &MGetOrderInfoResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *TradeEngineServiceGetOrderLogArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceGetOrderLogArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceGetOrderLogArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewGetOrderLogReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *TradeEngineServiceGetOrderLogArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceGetOrderLogArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceGetOrderLogArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceGetOrderLogArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *TradeEngineServiceGetOrderLogArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *TradeEngineServiceGetOrderLogArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceGetOrderLogArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *GetOrderLogReq
	if src.Req != nil {
		_req = &GetOrderLogReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *TradeEngineServiceGetOrderLogResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceGetOrderLogResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceGetOrderLogResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewGetOrderLogResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *TradeEngineServiceGetOrderLogResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceGetOrderLogResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceGetOrderLogResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceGetOrderLogResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TradeEngineServiceGetOrderLogResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *TradeEngineServiceGetOrderLogResult) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceGetOrderLogResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *GetOrderLogResp
	if src.Success != nil {
		_success = &GetOrderLogResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *TradeEngineServiceBindUserOrderArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceBindUserOrderArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceBindUserOrderArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewBindUserOrderReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *TradeEngineServiceBindUserOrderArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceBindUserOrderArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceBindUserOrderArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceBindUserOrderArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *TradeEngineServiceBindUserOrderArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *TradeEngineServiceBindUserOrderArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceBindUserOrderArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *BindUserOrderReq
	if src.Req != nil {
		_req = &BindUserOrderReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *TradeEngineServiceBindUserOrderResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceBindUserOrderResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceBindUserOrderResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewBindUserOrderResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *TradeEngineServiceBindUserOrderResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceBindUserOrderResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceBindUserOrderResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceBindUserOrderResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TradeEngineServiceBindUserOrderResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *TradeEngineServiceBindUserOrderResult) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceBindUserOrderResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *BindUserOrderResp
	if src.Success != nil {
		_success = &BindUserOrderResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *TradeEngineServiceUpdateOrderProductArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceUpdateOrderProductArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceUpdateOrderProductArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewUpdateOrderProductReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *TradeEngineServiceUpdateOrderProductArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceUpdateOrderProductArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceUpdateOrderProductArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceUpdateOrderProductArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *TradeEngineServiceUpdateOrderProductArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *TradeEngineServiceUpdateOrderProductArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceUpdateOrderProductArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *UpdateOrderProductReq
	if src.Req != nil {
		_req = &UpdateOrderProductReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *TradeEngineServiceUpdateOrderProductResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceUpdateOrderProductResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceUpdateOrderProductResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewUpdateOrderProductResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *TradeEngineServiceUpdateOrderProductResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceUpdateOrderProductResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceUpdateOrderProductResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceUpdateOrderProductResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TradeEngineServiceUpdateOrderProductResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *TradeEngineServiceUpdateOrderProductResult) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceUpdateOrderProductResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *UpdateOrderProductResp
	if src.Success != nil {
		_success = &UpdateOrderProductResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *TradeEngineServiceRepostOrderMessageArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceRepostOrderMessageArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceRepostOrderMessageArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewRepostOrderMessageReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *TradeEngineServiceRepostOrderMessageArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceRepostOrderMessageArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceRepostOrderMessageArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceRepostOrderMessageArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *TradeEngineServiceRepostOrderMessageArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *TradeEngineServiceRepostOrderMessageArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceRepostOrderMessageArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *RepostOrderMessageReq
	if src.Req != nil {
		_req = &RepostOrderMessageReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *TradeEngineServiceRepostOrderMessageResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceRepostOrderMessageResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TradeEngineServiceRepostOrderMessageResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewRepostOrderMessageResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *TradeEngineServiceRepostOrderMessageResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TradeEngineServiceRepostOrderMessageResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TradeEngineServiceRepostOrderMessageResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TradeEngineServiceRepostOrderMessageResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TradeEngineServiceRepostOrderMessageResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *TradeEngineServiceRepostOrderMessageResult) DeepCopy(s interface{}) error {
	src, ok := s.(*TradeEngineServiceRepostOrderMessageResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *RepostOrderMessageResp
	if src.Success != nil {
		_success = &RepostOrderMessageResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *TradeEngineServiceCreateOrderArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *TradeEngineServiceCreateOrderResult) GetResult() interface{} {
	return p.Success
}

func (p *TradeEngineServiceActionOrderArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *TradeEngineServiceActionOrderResult) GetResult() interface{} {
	return p.Success
}

func (p *TradeEngineServiceGetOrderInfoArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *TradeEngineServiceGetOrderInfoResult) GetResult() interface{} {
	return p.Success
}

func (p *TradeEngineServiceMGetOrderInfoArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *TradeEngineServiceMGetOrderInfoResult) GetResult() interface{} {
	return p.Success
}

func (p *TradeEngineServiceGetOrderLogArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *TradeEngineServiceGetOrderLogResult) GetResult() interface{} {
	return p.Success
}

func (p *TradeEngineServiceBindUserOrderArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *TradeEngineServiceBindUserOrderResult) GetResult() interface{} {
	return p.Success
}

func (p *TradeEngineServiceUpdateOrderProductArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *TradeEngineServiceUpdateOrderProductResult) GetResult() interface{} {
	return p.Success
}

func (p *TradeEngineServiceRepostOrderMessageArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *TradeEngineServiceRepostOrderMessageResult) GetResult() interface{} {
	return p.Success
}

func (p *CreateOrderReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *ActionOrderReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *MGetOrderInfoReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *GetOrderLogReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *BindUserOrderReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *GetOrderInfoReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *UpdateOrderProductReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *RepostOrderMessageReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *CreateOrderResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *ActionOrderResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *MGetOrderInfoResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *GetOrderLogResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *BindUserOrderResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *OrderInfoDetailResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *UpdateOrderProductResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *RepostOrderMessageResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}
