// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package engine

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
)

type CreateOrderReq struct {
	OrderName           string                             `thrift:"order_name,3" frugal:"3,default,string" json:"order_name"`
	OrderDesc           string                             `thrift:"order_desc,4" frugal:"4,default,string" json:"order_desc"`
	TradeType           fwe_trade_common.TradeType         `thrift:"trade_type,5" frugal:"5,default,TradeType" json:"trade_type"`
	TotalAmount         int64                              `thrift:"total_amount,6" frugal:"6,default,i64" json:"total_amount"`
	TotalPayAmount      int64                              `thrift:"total_pay_amount,7" frugal:"7,default,i64" json:"total_pay_amount"`
	TotalSubsidyAmount  int64                              `thrift:"total_subsidy_amount,8" frugal:"8,default,i64" json:"total_subsidy_amount"`
	BuyerInfo           *fwe_trade_common.TradeSubjectInfo `thrift:"buyer_info,100" frugal:"100,default,fwe_trade_common.TradeSubjectInfo" json:"buyer_info"`
	SellerInfo          *fwe_trade_common.TradeSubjectInfo `thrift:"seller_info,101" frugal:"101,default,fwe_trade_common.TradeSubjectInfo" json:"seller_info"`
	ProductInfo         *fwe_trade_common.ProductInfo      `thrift:"product_info,102" frugal:"102,default,fwe_trade_common.ProductInfo" json:"product_info"`
	SplitInfo           *fwe_trade_common.TradeSpiltInfo   `thrift:"split_info,103" frugal:"103,default,fwe_trade_common.TradeSpiltInfo" json:"split_info"`
	ServiceProviderInfo *fwe_trade_common.TradeSubjectInfo `thrift:"service_provider_info,105" frugal:"105,default,fwe_trade_common.TradeSubjectInfo" json:"service_provider_info"`
	TalentInfo          *fwe_trade_common.TradeSubjectInfo `thrift:"talent_info,106" frugal:"106,default,fwe_trade_common.TradeSubjectInfo" json:"talent_info"`
	FinanceList         []*fwe_trade_common.FinanceInfo    `thrift:"finance_list,104" frugal:"104,default,list<fwe_trade_common.FinanceInfo>" json:"finance_list"`
	PurchasePlan        *fwe_trade_common.PurchasePlan     `thrift:"purchase_plan,107" frugal:"107,default,fwe_trade_common.PurchasePlan" json:"purchase_plan"`
	TradeOption         *fwe_trade_common.TradeOption      `thrift:"trade_option,108" frugal:"108,default,fwe_trade_common.TradeOption" json:"trade_option"`
	OrderTag            map[string]string                  `thrift:"order_tag,200" frugal:"200,default,map<string:string>" json:"order_tag"`
	Extra               map[string]string                  `thrift:"extra,201" frugal:"201,default,map<string:string>" json:"extra"`
	IsTest              bool                               `thrift:"is_test,202" frugal:"202,default,bool" json:"is_test"`
	IdemID              *string                            `thrift:"idem_id,203,optional" frugal:"203,optional,string" json:"idem_id,omitempty"`
	Operator            *fwe_trade_common.OperatorInfo     `thrift:"operator,253" frugal:"253,default,fwe_trade_common.OperatorInfo" json:"operator"`
	Identity            *fwe_trade_common.BizIdentity      `thrift:"identity,254,required" frugal:"254,required,fwe_trade_common.BizIdentity" json:"identity"`
	Base                *base.Base                         `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewCreateOrderReq() *CreateOrderReq {
	return &CreateOrderReq{}
}

func (p *CreateOrderReq) InitDefault() {
}

func (p *CreateOrderReq) GetOrderName() (v string) {
	return p.OrderName
}

func (p *CreateOrderReq) GetOrderDesc() (v string) {
	return p.OrderDesc
}

func (p *CreateOrderReq) GetTradeType() (v fwe_trade_common.TradeType) {
	return p.TradeType
}

func (p *CreateOrderReq) GetTotalAmount() (v int64) {
	return p.TotalAmount
}

func (p *CreateOrderReq) GetTotalPayAmount() (v int64) {
	return p.TotalPayAmount
}

func (p *CreateOrderReq) GetTotalSubsidyAmount() (v int64) {
	return p.TotalSubsidyAmount
}

var CreateOrderReq_BuyerInfo_DEFAULT *fwe_trade_common.TradeSubjectInfo

func (p *CreateOrderReq) GetBuyerInfo() (v *fwe_trade_common.TradeSubjectInfo) {
	if !p.IsSetBuyerInfo() {
		return CreateOrderReq_BuyerInfo_DEFAULT
	}
	return p.BuyerInfo
}

var CreateOrderReq_SellerInfo_DEFAULT *fwe_trade_common.TradeSubjectInfo

func (p *CreateOrderReq) GetSellerInfo() (v *fwe_trade_common.TradeSubjectInfo) {
	if !p.IsSetSellerInfo() {
		return CreateOrderReq_SellerInfo_DEFAULT
	}
	return p.SellerInfo
}

var CreateOrderReq_ProductInfo_DEFAULT *fwe_trade_common.ProductInfo

func (p *CreateOrderReq) GetProductInfo() (v *fwe_trade_common.ProductInfo) {
	if !p.IsSetProductInfo() {
		return CreateOrderReq_ProductInfo_DEFAULT
	}
	return p.ProductInfo
}

var CreateOrderReq_SplitInfo_DEFAULT *fwe_trade_common.TradeSpiltInfo

func (p *CreateOrderReq) GetSplitInfo() (v *fwe_trade_common.TradeSpiltInfo) {
	if !p.IsSetSplitInfo() {
		return CreateOrderReq_SplitInfo_DEFAULT
	}
	return p.SplitInfo
}

var CreateOrderReq_ServiceProviderInfo_DEFAULT *fwe_trade_common.TradeSubjectInfo

func (p *CreateOrderReq) GetServiceProviderInfo() (v *fwe_trade_common.TradeSubjectInfo) {
	if !p.IsSetServiceProviderInfo() {
		return CreateOrderReq_ServiceProviderInfo_DEFAULT
	}
	return p.ServiceProviderInfo
}

var CreateOrderReq_TalentInfo_DEFAULT *fwe_trade_common.TradeSubjectInfo

func (p *CreateOrderReq) GetTalentInfo() (v *fwe_trade_common.TradeSubjectInfo) {
	if !p.IsSetTalentInfo() {
		return CreateOrderReq_TalentInfo_DEFAULT
	}
	return p.TalentInfo
}

func (p *CreateOrderReq) GetFinanceList() (v []*fwe_trade_common.FinanceInfo) {
	return p.FinanceList
}

var CreateOrderReq_PurchasePlan_DEFAULT *fwe_trade_common.PurchasePlan

func (p *CreateOrderReq) GetPurchasePlan() (v *fwe_trade_common.PurchasePlan) {
	if !p.IsSetPurchasePlan() {
		return CreateOrderReq_PurchasePlan_DEFAULT
	}
	return p.PurchasePlan
}

var CreateOrderReq_TradeOption_DEFAULT *fwe_trade_common.TradeOption

func (p *CreateOrderReq) GetTradeOption() (v *fwe_trade_common.TradeOption) {
	if !p.IsSetTradeOption() {
		return CreateOrderReq_TradeOption_DEFAULT
	}
	return p.TradeOption
}

func (p *CreateOrderReq) GetOrderTag() (v map[string]string) {
	return p.OrderTag
}

func (p *CreateOrderReq) GetExtra() (v map[string]string) {
	return p.Extra
}

func (p *CreateOrderReq) GetIsTest() (v bool) {
	return p.IsTest
}

var CreateOrderReq_IdemID_DEFAULT string

func (p *CreateOrderReq) GetIdemID() (v string) {
	if !p.IsSetIdemID() {
		return CreateOrderReq_IdemID_DEFAULT
	}
	return *p.IdemID
}

var CreateOrderReq_Operator_DEFAULT *fwe_trade_common.OperatorInfo

func (p *CreateOrderReq) GetOperator() (v *fwe_trade_common.OperatorInfo) {
	if !p.IsSetOperator() {
		return CreateOrderReq_Operator_DEFAULT
	}
	return p.Operator
}

var CreateOrderReq_Identity_DEFAULT *fwe_trade_common.BizIdentity

func (p *CreateOrderReq) GetIdentity() (v *fwe_trade_common.BizIdentity) {
	if !p.IsSetIdentity() {
		return CreateOrderReq_Identity_DEFAULT
	}
	return p.Identity
}

var CreateOrderReq_Base_DEFAULT *base.Base

func (p *CreateOrderReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CreateOrderReq_Base_DEFAULT
	}
	return p.Base
}
func (p *CreateOrderReq) SetOrderName(val string) {
	p.OrderName = val
}
func (p *CreateOrderReq) SetOrderDesc(val string) {
	p.OrderDesc = val
}
func (p *CreateOrderReq) SetTradeType(val fwe_trade_common.TradeType) {
	p.TradeType = val
}
func (p *CreateOrderReq) SetTotalAmount(val int64) {
	p.TotalAmount = val
}
func (p *CreateOrderReq) SetTotalPayAmount(val int64) {
	p.TotalPayAmount = val
}
func (p *CreateOrderReq) SetTotalSubsidyAmount(val int64) {
	p.TotalSubsidyAmount = val
}
func (p *CreateOrderReq) SetBuyerInfo(val *fwe_trade_common.TradeSubjectInfo) {
	p.BuyerInfo = val
}
func (p *CreateOrderReq) SetSellerInfo(val *fwe_trade_common.TradeSubjectInfo) {
	p.SellerInfo = val
}
func (p *CreateOrderReq) SetProductInfo(val *fwe_trade_common.ProductInfo) {
	p.ProductInfo = val
}
func (p *CreateOrderReq) SetSplitInfo(val *fwe_trade_common.TradeSpiltInfo) {
	p.SplitInfo = val
}
func (p *CreateOrderReq) SetServiceProviderInfo(val *fwe_trade_common.TradeSubjectInfo) {
	p.ServiceProviderInfo = val
}
func (p *CreateOrderReq) SetTalentInfo(val *fwe_trade_common.TradeSubjectInfo) {
	p.TalentInfo = val
}
func (p *CreateOrderReq) SetFinanceList(val []*fwe_trade_common.FinanceInfo) {
	p.FinanceList = val
}
func (p *CreateOrderReq) SetPurchasePlan(val *fwe_trade_common.PurchasePlan) {
	p.PurchasePlan = val
}
func (p *CreateOrderReq) SetTradeOption(val *fwe_trade_common.TradeOption) {
	p.TradeOption = val
}
func (p *CreateOrderReq) SetOrderTag(val map[string]string) {
	p.OrderTag = val
}
func (p *CreateOrderReq) SetExtra(val map[string]string) {
	p.Extra = val
}
func (p *CreateOrderReq) SetIsTest(val bool) {
	p.IsTest = val
}
func (p *CreateOrderReq) SetIdemID(val *string) {
	p.IdemID = val
}
func (p *CreateOrderReq) SetOperator(val *fwe_trade_common.OperatorInfo) {
	p.Operator = val
}
func (p *CreateOrderReq) SetIdentity(val *fwe_trade_common.BizIdentity) {
	p.Identity = val
}
func (p *CreateOrderReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CreateOrderReq = map[int16]string{
	3:   "order_name",
	4:   "order_desc",
	5:   "trade_type",
	6:   "total_amount",
	7:   "total_pay_amount",
	8:   "total_subsidy_amount",
	100: "buyer_info",
	101: "seller_info",
	102: "product_info",
	103: "split_info",
	105: "service_provider_info",
	106: "talent_info",
	104: "finance_list",
	107: "purchase_plan",
	108: "trade_option",
	200: "order_tag",
	201: "extra",
	202: "is_test",
	203: "idem_id",
	253: "operator",
	254: "identity",
	255: "Base",
}

func (p *CreateOrderReq) IsSetBuyerInfo() bool {
	return p.BuyerInfo != nil
}

func (p *CreateOrderReq) IsSetSellerInfo() bool {
	return p.SellerInfo != nil
}

func (p *CreateOrderReq) IsSetProductInfo() bool {
	return p.ProductInfo != nil
}

func (p *CreateOrderReq) IsSetSplitInfo() bool {
	return p.SplitInfo != nil
}

func (p *CreateOrderReq) IsSetServiceProviderInfo() bool {
	return p.ServiceProviderInfo != nil
}

func (p *CreateOrderReq) IsSetTalentInfo() bool {
	return p.TalentInfo != nil
}

func (p *CreateOrderReq) IsSetPurchasePlan() bool {
	return p.PurchasePlan != nil
}

func (p *CreateOrderReq) IsSetTradeOption() bool {
	return p.TradeOption != nil
}

func (p *CreateOrderReq) IsSetIdemID() bool {
	return p.IdemID != nil
}

func (p *CreateOrderReq) IsSetOperator() bool {
	return p.Operator != nil
}

func (p *CreateOrderReq) IsSetIdentity() bool {
	return p.Identity != nil
}

func (p *CreateOrderReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *CreateOrderReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateOrderReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentity bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 102:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField102(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 103:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField103(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 105:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField105(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 106:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField106(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 104:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField104(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 107:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField107(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 108:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField108(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 203:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField203(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateOrderReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateOrderReq[fieldId]))
}

func (p *CreateOrderReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderName = _field
	return nil
}
func (p *CreateOrderReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderDesc = _field
	return nil
}
func (p *CreateOrderReq) ReadField5(iprot thrift.TProtocol) error {

	var _field fwe_trade_common.TradeType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = fwe_trade_common.TradeType(v)
	}
	p.TradeType = _field
	return nil
}
func (p *CreateOrderReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalAmount = _field
	return nil
}
func (p *CreateOrderReq) ReadField7(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalPayAmount = _field
	return nil
}
func (p *CreateOrderReq) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalSubsidyAmount = _field
	return nil
}
func (p *CreateOrderReq) ReadField100(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BuyerInfo = _field
	return nil
}
func (p *CreateOrderReq) ReadField101(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SellerInfo = _field
	return nil
}
func (p *CreateOrderReq) ReadField102(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewProductInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ProductInfo = _field
	return nil
}
func (p *CreateOrderReq) ReadField103(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeSpiltInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SplitInfo = _field
	return nil
}
func (p *CreateOrderReq) ReadField105(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ServiceProviderInfo = _field
	return nil
}
func (p *CreateOrderReq) ReadField106(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TalentInfo = _field
	return nil
}
func (p *CreateOrderReq) ReadField104(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.FinanceInfo, 0, size)
	values := make([]fwe_trade_common.FinanceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FinanceList = _field
	return nil
}
func (p *CreateOrderReq) ReadField107(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewPurchasePlan()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PurchasePlan = _field
	return nil
}
func (p *CreateOrderReq) ReadField108(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeOption()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TradeOption = _field
	return nil
}
func (p *CreateOrderReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.OrderTag = _field
	return nil
}
func (p *CreateOrderReq) ReadField201(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Extra = _field
	return nil
}
func (p *CreateOrderReq) ReadField202(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsTest = _field
	return nil
}
func (p *CreateOrderReq) ReadField203(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IdemID = _field
	return nil
}
func (p *CreateOrderReq) ReadField253(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewOperatorInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Operator = _field
	return nil
}
func (p *CreateOrderReq) ReadField254(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewBizIdentity()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identity = _field
	return nil
}
func (p *CreateOrderReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CreateOrderReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateOrderReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateOrderReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField102(oprot); err != nil {
			fieldId = 102
			goto WriteFieldError
		}
		if err = p.writeField103(oprot); err != nil {
			fieldId = 103
			goto WriteFieldError
		}
		if err = p.writeField105(oprot); err != nil {
			fieldId = 105
			goto WriteFieldError
		}
		if err = p.writeField106(oprot); err != nil {
			fieldId = 106
			goto WriteFieldError
		}
		if err = p.writeField104(oprot); err != nil {
			fieldId = 104
			goto WriteFieldError
		}
		if err = p.writeField107(oprot); err != nil {
			fieldId = 107
			goto WriteFieldError
		}
		if err = p.writeField108(oprot); err != nil {
			fieldId = 108
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
		if err = p.writeField203(oprot); err != nil {
			fieldId = 203
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateOrderReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CreateOrderReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_desc", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CreateOrderReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("trade_type", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TradeType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *CreateOrderReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total_amount", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TotalAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *CreateOrderReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total_pay_amount", thrift.I64, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TotalPayAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *CreateOrderReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total_subsidy_amount", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TotalSubsidyAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *CreateOrderReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("buyer_info", thrift.STRUCT, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BuyerInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *CreateOrderReq) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("seller_info", thrift.STRUCT, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SellerInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *CreateOrderReq) writeField102(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("product_info", thrift.STRUCT, 102); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ProductInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 end error: ", p), err)
}
func (p *CreateOrderReq) writeField103(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("split_info", thrift.STRUCT, 103); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SplitInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 103 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 103 end error: ", p), err)
}
func (p *CreateOrderReq) writeField105(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("service_provider_info", thrift.STRUCT, 105); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ServiceProviderInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 105 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 105 end error: ", p), err)
}
func (p *CreateOrderReq) writeField106(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("talent_info", thrift.STRUCT, 106); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TalentInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 106 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 106 end error: ", p), err)
}
func (p *CreateOrderReq) writeField104(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_list", thrift.LIST, 104); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FinanceList)); err != nil {
		return err
	}
	for _, v := range p.FinanceList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 104 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 104 end error: ", p), err)
}
func (p *CreateOrderReq) writeField107(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("purchase_plan", thrift.STRUCT, 107); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PurchasePlan.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 107 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 107 end error: ", p), err)
}
func (p *CreateOrderReq) writeField108(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("trade_option", thrift.STRUCT, 108); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TradeOption.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 108 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 108 end error: ", p), err)
}
func (p *CreateOrderReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.OrderTag)); err != nil {
		return err
	}
	for k, v := range p.OrderTag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}
func (p *CreateOrderReq) writeField201(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("extra", thrift.MAP, 201); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Extra)); err != nil {
		return err
	}
	for k, v := range p.Extra {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *CreateOrderReq) writeField202(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("is_test", thrift.BOOL, 202); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsTest); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}
func (p *CreateOrderReq) writeField203(oprot thrift.TProtocol) (err error) {
	if p.IsSetIdemID() {
		if err = oprot.WriteFieldBegin("idem_id", thrift.STRING, 203); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.IdemID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 203 end error: ", p), err)
}
func (p *CreateOrderReq) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRUCT, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Operator.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *CreateOrderReq) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("identity", thrift.STRUCT, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identity.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *CreateOrderReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateOrderReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateOrderReq(%+v)", *p)

}

type CreateOrderResp struct {
	OrderID     string         `thrift:"order_id,1,required" frugal:"1,required,string" json:"order_id"`
	BizResponse string         `thrift:"biz_response,2" frugal:"2,default,string" json:"biz_response"`
	BaseResp    *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewCreateOrderResp() *CreateOrderResp {
	return &CreateOrderResp{}
}

func (p *CreateOrderResp) InitDefault() {
}

func (p *CreateOrderResp) GetOrderID() (v string) {
	return p.OrderID
}

func (p *CreateOrderResp) GetBizResponse() (v string) {
	return p.BizResponse
}

var CreateOrderResp_BaseResp_DEFAULT *base.BaseResp

func (p *CreateOrderResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CreateOrderResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CreateOrderResp) SetOrderID(val string) {
	p.OrderID = val
}
func (p *CreateOrderResp) SetBizResponse(val string) {
	p.BizResponse = val
}
func (p *CreateOrderResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CreateOrderResp = map[int16]string{
	1:   "order_id",
	2:   "biz_response",
	255: "BaseResp",
}

func (p *CreateOrderResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CreateOrderResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateOrderResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateOrderResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateOrderResp[fieldId]))
}

func (p *CreateOrderResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *CreateOrderResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizResponse = _field
	return nil
}
func (p *CreateOrderResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CreateOrderResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateOrderResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateOrderResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateOrderResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateOrderResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_response", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BizResponse); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateOrderResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateOrderResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateOrderResp(%+v)", *p)

}

type ActionOrderReq struct {
	OrderID         string                         `thrift:"order_id,1,required" frugal:"1,required,string" json:"order_id"`
	Action          string                         `thrift:"action,2,required" frugal:"2,required,string" json:"action"`
	BizRequest      string                         `thrift:"biz_request,10" frugal:"10,default,string" json:"biz_request"`
	ActionCondition string                         `thrift:"action_condition,11" frugal:"11,default,string" json:"action_condition"`
	TagMap          map[string]string              `thrift:"tag_map,12" frugal:"12,default,map<string:string>" json:"tag_map"`
	OperateDesc     string                         `thrift:"operate_desc,13" frugal:"13,default,string" json:"operate_desc"`
	Operator        *fwe_trade_common.OperatorInfo `thrift:"operator,253" frugal:"253,default,fwe_trade_common.OperatorInfo" json:"operator"`
	Identity        *fwe_trade_common.BizIdentity  `thrift:"identity,254,required" frugal:"254,required,fwe_trade_common.BizIdentity" json:"identity"`
	Base            *base.Base                     `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewActionOrderReq() *ActionOrderReq {
	return &ActionOrderReq{}
}

func (p *ActionOrderReq) InitDefault() {
}

func (p *ActionOrderReq) GetOrderID() (v string) {
	return p.OrderID
}

func (p *ActionOrderReq) GetAction() (v string) {
	return p.Action
}

func (p *ActionOrderReq) GetBizRequest() (v string) {
	return p.BizRequest
}

func (p *ActionOrderReq) GetActionCondition() (v string) {
	return p.ActionCondition
}

func (p *ActionOrderReq) GetTagMap() (v map[string]string) {
	return p.TagMap
}

func (p *ActionOrderReq) GetOperateDesc() (v string) {
	return p.OperateDesc
}

var ActionOrderReq_Operator_DEFAULT *fwe_trade_common.OperatorInfo

func (p *ActionOrderReq) GetOperator() (v *fwe_trade_common.OperatorInfo) {
	if !p.IsSetOperator() {
		return ActionOrderReq_Operator_DEFAULT
	}
	return p.Operator
}

var ActionOrderReq_Identity_DEFAULT *fwe_trade_common.BizIdentity

func (p *ActionOrderReq) GetIdentity() (v *fwe_trade_common.BizIdentity) {
	if !p.IsSetIdentity() {
		return ActionOrderReq_Identity_DEFAULT
	}
	return p.Identity
}

var ActionOrderReq_Base_DEFAULT *base.Base

func (p *ActionOrderReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ActionOrderReq_Base_DEFAULT
	}
	return p.Base
}
func (p *ActionOrderReq) SetOrderID(val string) {
	p.OrderID = val
}
func (p *ActionOrderReq) SetAction(val string) {
	p.Action = val
}
func (p *ActionOrderReq) SetBizRequest(val string) {
	p.BizRequest = val
}
func (p *ActionOrderReq) SetActionCondition(val string) {
	p.ActionCondition = val
}
func (p *ActionOrderReq) SetTagMap(val map[string]string) {
	p.TagMap = val
}
func (p *ActionOrderReq) SetOperateDesc(val string) {
	p.OperateDesc = val
}
func (p *ActionOrderReq) SetOperator(val *fwe_trade_common.OperatorInfo) {
	p.Operator = val
}
func (p *ActionOrderReq) SetIdentity(val *fwe_trade_common.BizIdentity) {
	p.Identity = val
}
func (p *ActionOrderReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ActionOrderReq = map[int16]string{
	1:   "order_id",
	2:   "action",
	10:  "biz_request",
	11:  "action_condition",
	12:  "tag_map",
	13:  "operate_desc",
	253: "operator",
	254: "identity",
	255: "Base",
}

func (p *ActionOrderReq) IsSetOperator() bool {
	return p.Operator != nil
}

func (p *ActionOrderReq) IsSetIdentity() bool {
	return p.Identity != nil
}

func (p *ActionOrderReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *ActionOrderReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ActionOrderReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetAction bool = false
	var issetIdentity bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAction = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAction {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ActionOrderReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ActionOrderReq[fieldId]))
}

func (p *ActionOrderReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *ActionOrderReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Action = _field
	return nil
}
func (p *ActionOrderReq) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizRequest = _field
	return nil
}
func (p *ActionOrderReq) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ActionCondition = _field
	return nil
}
func (p *ActionOrderReq) ReadField12(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.TagMap = _field
	return nil
}
func (p *ActionOrderReq) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OperateDesc = _field
	return nil
}
func (p *ActionOrderReq) ReadField253(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewOperatorInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Operator = _field
	return nil
}
func (p *ActionOrderReq) ReadField254(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewBizIdentity()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identity = _field
	return nil
}
func (p *ActionOrderReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ActionOrderReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ActionOrderReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ActionOrderReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ActionOrderReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ActionOrderReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("action", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Action); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ActionOrderReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_request", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BizRequest); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *ActionOrderReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("action_condition", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ActionCondition); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *ActionOrderReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag_map", thrift.MAP, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.TagMap)); err != nil {
		return err
	}
	for k, v := range p.TagMap {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *ActionOrderReq) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operate_desc", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OperateDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *ActionOrderReq) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRUCT, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Operator.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *ActionOrderReq) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("identity", thrift.STRUCT, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identity.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *ActionOrderReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ActionOrderReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ActionOrderReq(%+v)", *p)

}

type ActionOrderResp struct {
	OrderID     string         `thrift:"order_id,1,required" frugal:"1,required,string" json:"order_id"`
	BizResponse string         `thrift:"biz_response,2" frugal:"2,default,string" json:"biz_response"`
	BaseResp    *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewActionOrderResp() *ActionOrderResp {
	return &ActionOrderResp{}
}

func (p *ActionOrderResp) InitDefault() {
}

func (p *ActionOrderResp) GetOrderID() (v string) {
	return p.OrderID
}

func (p *ActionOrderResp) GetBizResponse() (v string) {
	return p.BizResponse
}

var ActionOrderResp_BaseResp_DEFAULT *base.BaseResp

func (p *ActionOrderResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ActionOrderResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *ActionOrderResp) SetOrderID(val string) {
	p.OrderID = val
}
func (p *ActionOrderResp) SetBizResponse(val string) {
	p.BizResponse = val
}
func (p *ActionOrderResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_ActionOrderResp = map[int16]string{
	1:   "order_id",
	2:   "biz_response",
	255: "BaseResp",
}

func (p *ActionOrderResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ActionOrderResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ActionOrderResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ActionOrderResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ActionOrderResp[fieldId]))
}

func (p *ActionOrderResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *ActionOrderResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizResponse = _field
	return nil
}
func (p *ActionOrderResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ActionOrderResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ActionOrderResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ActionOrderResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ActionOrderResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ActionOrderResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_response", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BizResponse); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ActionOrderResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ActionOrderResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ActionOrderResp(%+v)", *p)

}

type MGetOrderInfoReq struct {
	OrderIds []string                      `thrift:"order_ids,1" frugal:"1,default,list<string>" json:"order_ids"`
	Identity *fwe_trade_common.BizIdentity `thrift:"identity,254,required" frugal:"254,required,fwe_trade_common.BizIdentity" json:"identity"`
	Base     *base.Base                    `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewMGetOrderInfoReq() *MGetOrderInfoReq {
	return &MGetOrderInfoReq{}
}

func (p *MGetOrderInfoReq) InitDefault() {
}

func (p *MGetOrderInfoReq) GetOrderIds() (v []string) {
	return p.OrderIds
}

var MGetOrderInfoReq_Identity_DEFAULT *fwe_trade_common.BizIdentity

func (p *MGetOrderInfoReq) GetIdentity() (v *fwe_trade_common.BizIdentity) {
	if !p.IsSetIdentity() {
		return MGetOrderInfoReq_Identity_DEFAULT
	}
	return p.Identity
}

var MGetOrderInfoReq_Base_DEFAULT *base.Base

func (p *MGetOrderInfoReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return MGetOrderInfoReq_Base_DEFAULT
	}
	return p.Base
}
func (p *MGetOrderInfoReq) SetOrderIds(val []string) {
	p.OrderIds = val
}
func (p *MGetOrderInfoReq) SetIdentity(val *fwe_trade_common.BizIdentity) {
	p.Identity = val
}
func (p *MGetOrderInfoReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_MGetOrderInfoReq = map[int16]string{
	1:   "order_ids",
	254: "identity",
	255: "Base",
}

func (p *MGetOrderInfoReq) IsSetIdentity() bool {
	return p.Identity != nil
}

func (p *MGetOrderInfoReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *MGetOrderInfoReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetOrderInfoReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIdentity bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetOrderInfoReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_MGetOrderInfoReq[fieldId]))
}

func (p *MGetOrderInfoReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.OrderIds = _field
	return nil
}
func (p *MGetOrderInfoReq) ReadField254(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewBizIdentity()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identity = _field
	return nil
}
func (p *MGetOrderInfoReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *MGetOrderInfoReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetOrderInfoReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("MGetOrderInfoReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MGetOrderInfoReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_ids", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.OrderIds)); err != nil {
		return err
	}
	for _, v := range p.OrderIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MGetOrderInfoReq) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("identity", thrift.STRUCT, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identity.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *MGetOrderInfoReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *MGetOrderInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MGetOrderInfoReq(%+v)", *p)

}

type MGetOrderInfoResp struct {
	Data     map[string]*fwe_trade_common.OrderInfo `thrift:"data,1" frugal:"1,default,map<string:fwe_trade_common.OrderInfo>" json:"data"`
	BaseResp *base.BaseResp                         `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewMGetOrderInfoResp() *MGetOrderInfoResp {
	return &MGetOrderInfoResp{}
}

func (p *MGetOrderInfoResp) InitDefault() {
}

func (p *MGetOrderInfoResp) GetData() (v map[string]*fwe_trade_common.OrderInfo) {
	return p.Data
}

var MGetOrderInfoResp_BaseResp_DEFAULT *base.BaseResp

func (p *MGetOrderInfoResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return MGetOrderInfoResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *MGetOrderInfoResp) SetData(val map[string]*fwe_trade_common.OrderInfo) {
	p.Data = val
}
func (p *MGetOrderInfoResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_MGetOrderInfoResp = map[int16]string{
	1:   "data",
	255: "BaseResp",
}

func (p *MGetOrderInfoResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *MGetOrderInfoResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetOrderInfoResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetOrderInfoResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MGetOrderInfoResp) ReadField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]*fwe_trade_common.OrderInfo, size)
	values := make([]fwe_trade_common.OrderInfo, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *MGetOrderInfoResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *MGetOrderInfoResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetOrderInfoResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("MGetOrderInfoResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MGetOrderInfoResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.MAP, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRUCT, len(p.Data)); err != nil {
		return err
	}
	for k, v := range p.Data {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MGetOrderInfoResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *MGetOrderInfoResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MGetOrderInfoResp(%+v)", *p)

}

type GetOrderLogReq struct {
	OrderID  string                        `thrift:"order_id,1,required" frugal:"1,required,string" json:"order_id"`
	Identity *fwe_trade_common.BizIdentity `thrift:"identity,254,required" frugal:"254,required,fwe_trade_common.BizIdentity" json:"identity"`
	Base     *base.Base                    `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewGetOrderLogReq() *GetOrderLogReq {
	return &GetOrderLogReq{}
}

func (p *GetOrderLogReq) InitDefault() {
}

func (p *GetOrderLogReq) GetOrderID() (v string) {
	return p.OrderID
}

var GetOrderLogReq_Identity_DEFAULT *fwe_trade_common.BizIdentity

func (p *GetOrderLogReq) GetIdentity() (v *fwe_trade_common.BizIdentity) {
	if !p.IsSetIdentity() {
		return GetOrderLogReq_Identity_DEFAULT
	}
	return p.Identity
}

var GetOrderLogReq_Base_DEFAULT *base.Base

func (p *GetOrderLogReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetOrderLogReq_Base_DEFAULT
	}
	return p.Base
}
func (p *GetOrderLogReq) SetOrderID(val string) {
	p.OrderID = val
}
func (p *GetOrderLogReq) SetIdentity(val *fwe_trade_common.BizIdentity) {
	p.Identity = val
}
func (p *GetOrderLogReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetOrderLogReq = map[int16]string{
	1:   "order_id",
	254: "identity",
	255: "Base",
}

func (p *GetOrderLogReq) IsSetIdentity() bool {
	return p.Identity != nil
}

func (p *GetOrderLogReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetOrderLogReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GetOrderLogReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetIdentity bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetOrderLogReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetOrderLogReq[fieldId]))
}

func (p *GetOrderLogReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *GetOrderLogReq) ReadField254(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewBizIdentity()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identity = _field
	return nil
}
func (p *GetOrderLogReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetOrderLogReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GetOrderLogReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetOrderLogReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetOrderLogReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetOrderLogReq) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("identity", thrift.STRUCT, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identity.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetOrderLogReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetOrderLogReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOrderLogReq(%+v)", *p)

}

type GetOrderLogResp struct {
	Data     []*fwe_trade_common.OrderLog `thrift:"data,1" frugal:"1,default,list<fwe_trade_common.OrderLog>" json:"data"`
	BaseResp *base.BaseResp               `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewGetOrderLogResp() *GetOrderLogResp {
	return &GetOrderLogResp{}
}

func (p *GetOrderLogResp) InitDefault() {
}

func (p *GetOrderLogResp) GetData() (v []*fwe_trade_common.OrderLog) {
	return p.Data
}

var GetOrderLogResp_BaseResp_DEFAULT *base.BaseResp

func (p *GetOrderLogResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetOrderLogResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *GetOrderLogResp) SetData(val []*fwe_trade_common.OrderLog) {
	p.Data = val
}
func (p *GetOrderLogResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_GetOrderLogResp = map[int16]string{
	1:   "data",
	255: "BaseResp",
}

func (p *GetOrderLogResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetOrderLogResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GetOrderLogResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetOrderLogResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetOrderLogResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.OrderLog, 0, size)
	values := make([]fwe_trade_common.OrderLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Data = _field
	return nil
}
func (p *GetOrderLogResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetOrderLogResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GetOrderLogResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetOrderLogResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetOrderLogResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Data)); err != nil {
		return err
	}
	for _, v := range p.Data {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetOrderLogResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetOrderLogResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOrderLogResp(%+v)", *p)

}

type BindUserOrderReq struct {
	OrderIds []string   `thrift:"order_ids,1" frugal:"1,default,list<string>" json:"order_ids"`
	UID      int64      `thrift:"uid,2" frugal:"2,default,i64" json:"uid"`
	Base     *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewBindUserOrderReq() *BindUserOrderReq {
	return &BindUserOrderReq{}
}

func (p *BindUserOrderReq) InitDefault() {
}

func (p *BindUserOrderReq) GetOrderIds() (v []string) {
	return p.OrderIds
}

func (p *BindUserOrderReq) GetUID() (v int64) {
	return p.UID
}

var BindUserOrderReq_Base_DEFAULT *base.Base

func (p *BindUserOrderReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return BindUserOrderReq_Base_DEFAULT
	}
	return p.Base
}
func (p *BindUserOrderReq) SetOrderIds(val []string) {
	p.OrderIds = val
}
func (p *BindUserOrderReq) SetUID(val int64) {
	p.UID = val
}
func (p *BindUserOrderReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_BindUserOrderReq = map[int16]string{
	1:   "order_ids",
	2:   "uid",
	255: "Base",
}

func (p *BindUserOrderReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *BindUserOrderReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("BindUserOrderReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BindUserOrderReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BindUserOrderReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.OrderIds = _field
	return nil
}
func (p *BindUserOrderReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UID = _field
	return nil
}
func (p *BindUserOrderReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *BindUserOrderReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("BindUserOrderReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("BindUserOrderReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BindUserOrderReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_ids", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.OrderIds)); err != nil {
		return err
	}
	for _, v := range p.OrderIds {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *BindUserOrderReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("uid", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *BindUserOrderReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *BindUserOrderReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BindUserOrderReq(%+v)", *p)

}

type BindUserOrderResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewBindUserOrderResp() *BindUserOrderResp {
	return &BindUserOrderResp{}
}

func (p *BindUserOrderResp) InitDefault() {
}

var BindUserOrderResp_BaseResp_DEFAULT *base.BaseResp

func (p *BindUserOrderResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return BindUserOrderResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *BindUserOrderResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_BindUserOrderResp = map[int16]string{
	255: "BaseResp",
}

func (p *BindUserOrderResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *BindUserOrderResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("BindUserOrderResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BindUserOrderResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BindUserOrderResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *BindUserOrderResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("BindUserOrderResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("BindUserOrderResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BindUserOrderResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *BindUserOrderResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BindUserOrderResp(%+v)", *p)

}

type GetOrderInfoReq struct {
	OrderID        string                        `thrift:"order_id,1,required" frugal:"1,required,string" json:"order_id"`
	NeedSettleInfo *bool                         `thrift:"need_settle_info,2,optional" frugal:"2,optional,bool" json:"need_settle_info,omitempty"`
	NeedRefundInfo *bool                         `thrift:"need_refund_info,3,optional" frugal:"3,optional,bool" json:"need_refund_info,omitempty"`
	Identity       *fwe_trade_common.BizIdentity `thrift:"identity,254,required" frugal:"254,required,fwe_trade_common.BizIdentity" json:"identity"`
	Base           *base.Base                    `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewGetOrderInfoReq() *GetOrderInfoReq {
	return &GetOrderInfoReq{}
}

func (p *GetOrderInfoReq) InitDefault() {
}

func (p *GetOrderInfoReq) GetOrderID() (v string) {
	return p.OrderID
}

var GetOrderInfoReq_NeedSettleInfo_DEFAULT bool

func (p *GetOrderInfoReq) GetNeedSettleInfo() (v bool) {
	if !p.IsSetNeedSettleInfo() {
		return GetOrderInfoReq_NeedSettleInfo_DEFAULT
	}
	return *p.NeedSettleInfo
}

var GetOrderInfoReq_NeedRefundInfo_DEFAULT bool

func (p *GetOrderInfoReq) GetNeedRefundInfo() (v bool) {
	if !p.IsSetNeedRefundInfo() {
		return GetOrderInfoReq_NeedRefundInfo_DEFAULT
	}
	return *p.NeedRefundInfo
}

var GetOrderInfoReq_Identity_DEFAULT *fwe_trade_common.BizIdentity

func (p *GetOrderInfoReq) GetIdentity() (v *fwe_trade_common.BizIdentity) {
	if !p.IsSetIdentity() {
		return GetOrderInfoReq_Identity_DEFAULT
	}
	return p.Identity
}

var GetOrderInfoReq_Base_DEFAULT *base.Base

func (p *GetOrderInfoReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetOrderInfoReq_Base_DEFAULT
	}
	return p.Base
}
func (p *GetOrderInfoReq) SetOrderID(val string) {
	p.OrderID = val
}
func (p *GetOrderInfoReq) SetNeedSettleInfo(val *bool) {
	p.NeedSettleInfo = val
}
func (p *GetOrderInfoReq) SetNeedRefundInfo(val *bool) {
	p.NeedRefundInfo = val
}
func (p *GetOrderInfoReq) SetIdentity(val *fwe_trade_common.BizIdentity) {
	p.Identity = val
}
func (p *GetOrderInfoReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_GetOrderInfoReq = map[int16]string{
	1:   "order_id",
	2:   "need_settle_info",
	3:   "need_refund_info",
	254: "identity",
	255: "Base",
}

func (p *GetOrderInfoReq) IsSetNeedSettleInfo() bool {
	return p.NeedSettleInfo != nil
}

func (p *GetOrderInfoReq) IsSetNeedRefundInfo() bool {
	return p.NeedRefundInfo != nil
}

func (p *GetOrderInfoReq) IsSetIdentity() bool {
	return p.Identity != nil
}

func (p *GetOrderInfoReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetOrderInfoReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GetOrderInfoReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetIdentity bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetOrderInfoReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetOrderInfoReq[fieldId]))
}

func (p *GetOrderInfoReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *GetOrderInfoReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NeedSettleInfo = _field
	return nil
}
func (p *GetOrderInfoReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NeedRefundInfo = _field
	return nil
}
func (p *GetOrderInfoReq) ReadField254(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewBizIdentity()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identity = _field
	return nil
}
func (p *GetOrderInfoReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetOrderInfoReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GetOrderInfoReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetOrderInfoReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetOrderInfoReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetOrderInfoReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetNeedSettleInfo() {
		if err = oprot.WriteFieldBegin("need_settle_info", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.NeedSettleInfo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetOrderInfoReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetNeedRefundInfo() {
		if err = oprot.WriteFieldBegin("need_refund_info", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.NeedRefundInfo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GetOrderInfoReq) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("identity", thrift.STRUCT, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identity.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetOrderInfoReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetOrderInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOrderInfoReq(%+v)", *p)

}

type OrderInfoDetailResp struct {
	OrderInfo *fwe_trade_common.OrderInfo `thrift:"order_info,1" frugal:"1,default,fwe_trade_common.OrderInfo" json:"order_info"`
	BaseResp  *base.BaseResp              `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewOrderInfoDetailResp() *OrderInfoDetailResp {
	return &OrderInfoDetailResp{}
}

func (p *OrderInfoDetailResp) InitDefault() {
}

var OrderInfoDetailResp_OrderInfo_DEFAULT *fwe_trade_common.OrderInfo

func (p *OrderInfoDetailResp) GetOrderInfo() (v *fwe_trade_common.OrderInfo) {
	if !p.IsSetOrderInfo() {
		return OrderInfoDetailResp_OrderInfo_DEFAULT
	}
	return p.OrderInfo
}

var OrderInfoDetailResp_BaseResp_DEFAULT *base.BaseResp

func (p *OrderInfoDetailResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return OrderInfoDetailResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *OrderInfoDetailResp) SetOrderInfo(val *fwe_trade_common.OrderInfo) {
	p.OrderInfo = val
}
func (p *OrderInfoDetailResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_OrderInfoDetailResp = map[int16]string{
	1:   "order_info",
	255: "BaseResp",
}

func (p *OrderInfoDetailResp) IsSetOrderInfo() bool {
	return p.OrderInfo != nil
}

func (p *OrderInfoDetailResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *OrderInfoDetailResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OrderInfoDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderInfoDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OrderInfoDetailResp) ReadField1(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewOrderInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OrderInfo = _field
	return nil
}
func (p *OrderInfoDetailResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *OrderInfoDetailResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OrderInfoDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("OrderInfoDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OrderInfoDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_info", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.OrderInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *OrderInfoDetailResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *OrderInfoDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderInfoDetailResp(%+v)", *p)

}

type UpdateOrderProductReq struct {
	OrderID       string                          `thrift:"order_id,1,required" frugal:"1,required,string" json:"order_id"`
	ProductDetail *fwe_trade_common.ProductDetail `thrift:"product_detail,2,required" frugal:"2,required,fwe_trade_common.ProductDetail" json:"product_detail"`
	TagMap        map[string]string               `thrift:"tag_map,10" frugal:"10,default,map<string:string>" json:"tag_map"`
	Operator      *fwe_trade_common.OperatorInfo  `thrift:"operator,253" frugal:"253,default,fwe_trade_common.OperatorInfo" json:"operator"`
	Base          *base.Base                      `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewUpdateOrderProductReq() *UpdateOrderProductReq {
	return &UpdateOrderProductReq{}
}

func (p *UpdateOrderProductReq) InitDefault() {
}

func (p *UpdateOrderProductReq) GetOrderID() (v string) {
	return p.OrderID
}

var UpdateOrderProductReq_ProductDetail_DEFAULT *fwe_trade_common.ProductDetail

func (p *UpdateOrderProductReq) GetProductDetail() (v *fwe_trade_common.ProductDetail) {
	if !p.IsSetProductDetail() {
		return UpdateOrderProductReq_ProductDetail_DEFAULT
	}
	return p.ProductDetail
}

func (p *UpdateOrderProductReq) GetTagMap() (v map[string]string) {
	return p.TagMap
}

var UpdateOrderProductReq_Operator_DEFAULT *fwe_trade_common.OperatorInfo

func (p *UpdateOrderProductReq) GetOperator() (v *fwe_trade_common.OperatorInfo) {
	if !p.IsSetOperator() {
		return UpdateOrderProductReq_Operator_DEFAULT
	}
	return p.Operator
}

var UpdateOrderProductReq_Base_DEFAULT *base.Base

func (p *UpdateOrderProductReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return UpdateOrderProductReq_Base_DEFAULT
	}
	return p.Base
}
func (p *UpdateOrderProductReq) SetOrderID(val string) {
	p.OrderID = val
}
func (p *UpdateOrderProductReq) SetProductDetail(val *fwe_trade_common.ProductDetail) {
	p.ProductDetail = val
}
func (p *UpdateOrderProductReq) SetTagMap(val map[string]string) {
	p.TagMap = val
}
func (p *UpdateOrderProductReq) SetOperator(val *fwe_trade_common.OperatorInfo) {
	p.Operator = val
}
func (p *UpdateOrderProductReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_UpdateOrderProductReq = map[int16]string{
	1:   "order_id",
	2:   "product_detail",
	10:  "tag_map",
	253: "operator",
	255: "Base",
}

func (p *UpdateOrderProductReq) IsSetProductDetail() bool {
	return p.ProductDetail != nil
}

func (p *UpdateOrderProductReq) IsSetOperator() bool {
	return p.Operator != nil
}

func (p *UpdateOrderProductReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *UpdateOrderProductReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpdateOrderProductReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetProductDetail bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetProductDetail = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetProductDetail {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateOrderProductReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateOrderProductReq[fieldId]))
}

func (p *UpdateOrderProductReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *UpdateOrderProductReq) ReadField2(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewProductDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ProductDetail = _field
	return nil
}
func (p *UpdateOrderProductReq) ReadField10(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.TagMap = _field
	return nil
}
func (p *UpdateOrderProductReq) ReadField253(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewOperatorInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Operator = _field
	return nil
}
func (p *UpdateOrderProductReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *UpdateOrderProductReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpdateOrderProductReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateOrderProductReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateOrderProductReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdateOrderProductReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("product_detail", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ProductDetail.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpdateOrderProductReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag_map", thrift.MAP, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.TagMap)); err != nil {
		return err
	}
	for k, v := range p.TagMap {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *UpdateOrderProductReq) writeField253(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRUCT, 253); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Operator.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *UpdateOrderProductReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpdateOrderProductReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateOrderProductReq(%+v)", *p)

}

type UpdateOrderProductResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewUpdateOrderProductResp() *UpdateOrderProductResp {
	return &UpdateOrderProductResp{}
}

func (p *UpdateOrderProductResp) InitDefault() {
}

var UpdateOrderProductResp_BaseResp_DEFAULT *base.BaseResp

func (p *UpdateOrderProductResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return UpdateOrderProductResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *UpdateOrderProductResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_UpdateOrderProductResp = map[int16]string{
	255: "BaseResp",
}

func (p *UpdateOrderProductResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *UpdateOrderProductResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpdateOrderProductResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateOrderProductResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateOrderProductResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *UpdateOrderProductResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpdateOrderProductResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateOrderProductResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateOrderProductResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpdateOrderProductResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateOrderProductResp(%+v)", *p)

}

type RepostOrderMessageReq struct {
	OrderID      string                        `thrift:"order_id,1,required" frugal:"1,required,string" json:"order_id"`
	OrderAction  string                        `thrift:"order_action,2,required" frugal:"2,required,string" json:"order_action"`
	BeforeStatus int32                         `thrift:"before_status,3,required" frugal:"3,required,i32" json:"before_status"`
	AfterStatus  int32                         `thrift:"after_status,4,required" frugal:"4,required,i32" json:"after_status"`
	Identity     *fwe_trade_common.BizIdentity `thrift:"identity,254,required" frugal:"254,required,fwe_trade_common.BizIdentity" json:"identity"`
	Base         *base.Base                    `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewRepostOrderMessageReq() *RepostOrderMessageReq {
	return &RepostOrderMessageReq{}
}

func (p *RepostOrderMessageReq) InitDefault() {
}

func (p *RepostOrderMessageReq) GetOrderID() (v string) {
	return p.OrderID
}

func (p *RepostOrderMessageReq) GetOrderAction() (v string) {
	return p.OrderAction
}

func (p *RepostOrderMessageReq) GetBeforeStatus() (v int32) {
	return p.BeforeStatus
}

func (p *RepostOrderMessageReq) GetAfterStatus() (v int32) {
	return p.AfterStatus
}

var RepostOrderMessageReq_Identity_DEFAULT *fwe_trade_common.BizIdentity

func (p *RepostOrderMessageReq) GetIdentity() (v *fwe_trade_common.BizIdentity) {
	if !p.IsSetIdentity() {
		return RepostOrderMessageReq_Identity_DEFAULT
	}
	return p.Identity
}

var RepostOrderMessageReq_Base_DEFAULT *base.Base

func (p *RepostOrderMessageReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return RepostOrderMessageReq_Base_DEFAULT
	}
	return p.Base
}
func (p *RepostOrderMessageReq) SetOrderID(val string) {
	p.OrderID = val
}
func (p *RepostOrderMessageReq) SetOrderAction(val string) {
	p.OrderAction = val
}
func (p *RepostOrderMessageReq) SetBeforeStatus(val int32) {
	p.BeforeStatus = val
}
func (p *RepostOrderMessageReq) SetAfterStatus(val int32) {
	p.AfterStatus = val
}
func (p *RepostOrderMessageReq) SetIdentity(val *fwe_trade_common.BizIdentity) {
	p.Identity = val
}
func (p *RepostOrderMessageReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_RepostOrderMessageReq = map[int16]string{
	1:   "order_id",
	2:   "order_action",
	3:   "before_status",
	4:   "after_status",
	254: "identity",
	255: "Base",
}

func (p *RepostOrderMessageReq) IsSetIdentity() bool {
	return p.Identity != nil
}

func (p *RepostOrderMessageReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *RepostOrderMessageReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RepostOrderMessageReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetOrderAction bool = false
	var issetBeforeStatus bool = false
	var issetAfterStatus bool = false
	var issetIdentity bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderAction = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetBeforeStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAfterStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
				issetIdentity = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOrderAction {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetBeforeStatus {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAfterStatus {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetIdentity {
		fieldId = 254
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RepostOrderMessageReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RepostOrderMessageReq[fieldId]))
}

func (p *RepostOrderMessageReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *RepostOrderMessageReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderAction = _field
	return nil
}
func (p *RepostOrderMessageReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BeforeStatus = _field
	return nil
}
func (p *RepostOrderMessageReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AfterStatus = _field
	return nil
}
func (p *RepostOrderMessageReq) ReadField254(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewBizIdentity()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Identity = _field
	return nil
}
func (p *RepostOrderMessageReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *RepostOrderMessageReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RepostOrderMessageReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RepostOrderMessageReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RepostOrderMessageReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RepostOrderMessageReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_action", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RepostOrderMessageReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("before_status", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.BeforeStatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *RepostOrderMessageReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("after_status", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.AfterStatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *RepostOrderMessageReq) writeField254(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("identity", thrift.STRUCT, 254); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Identity.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *RepostOrderMessageReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *RepostOrderMessageReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RepostOrderMessageReq(%+v)", *p)

}

type RepostOrderMessageResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewRepostOrderMessageResp() *RepostOrderMessageResp {
	return &RepostOrderMessageResp{}
}

func (p *RepostOrderMessageResp) InitDefault() {
}

var RepostOrderMessageResp_BaseResp_DEFAULT *base.BaseResp

func (p *RepostOrderMessageResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return RepostOrderMessageResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *RepostOrderMessageResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_RepostOrderMessageResp = map[int16]string{
	255: "BaseResp",
}

func (p *RepostOrderMessageResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *RepostOrderMessageResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RepostOrderMessageResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RepostOrderMessageResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RepostOrderMessageResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *RepostOrderMessageResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RepostOrderMessageResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("RepostOrderMessageResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RepostOrderMessageResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *RepostOrderMessageResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RepostOrderMessageResp(%+v)", *p)

}

type TradeEngineService interface {
	CreateOrder(ctx context.Context, req *CreateOrderReq) (r *CreateOrderResp, err error)

	ActionOrder(ctx context.Context, req *ActionOrderReq) (r *ActionOrderResp, err error)

	GetOrderInfo(ctx context.Context, req *GetOrderInfoReq) (r *OrderInfoDetailResp, err error)

	MGetOrderInfo(ctx context.Context, req *MGetOrderInfoReq) (r *MGetOrderInfoResp, err error)

	GetOrderLog(ctx context.Context, req *GetOrderLogReq) (r *GetOrderLogResp, err error)

	BindUserOrder(ctx context.Context, req *BindUserOrderReq) (r *BindUserOrderResp, err error)

	UpdateOrderProduct(ctx context.Context, req *UpdateOrderProductReq) (r *UpdateOrderProductResp, err error)

	RepostOrderMessage(ctx context.Context, req *RepostOrderMessageReq) (r *RepostOrderMessageResp, err error)
}

type TradeEngineServiceCreateOrderArgs struct {
	Req *CreateOrderReq `thrift:"req,1" frugal:"1,default,CreateOrderReq" json:"req"`
}

func NewTradeEngineServiceCreateOrderArgs() *TradeEngineServiceCreateOrderArgs {
	return &TradeEngineServiceCreateOrderArgs{}
}

func (p *TradeEngineServiceCreateOrderArgs) InitDefault() {
}

var TradeEngineServiceCreateOrderArgs_Req_DEFAULT *CreateOrderReq

func (p *TradeEngineServiceCreateOrderArgs) GetReq() (v *CreateOrderReq) {
	if !p.IsSetReq() {
		return TradeEngineServiceCreateOrderArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeEngineServiceCreateOrderArgs) SetReq(val *CreateOrderReq) {
	p.Req = val
}

var fieldIDToName_TradeEngineServiceCreateOrderArgs = map[int16]string{
	1: "req",
}

func (p *TradeEngineServiceCreateOrderArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeEngineServiceCreateOrderArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceCreateOrderArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceCreateOrderArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceCreateOrderArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreateOrderReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeEngineServiceCreateOrderArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceCreateOrderArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateOrder_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceCreateOrderArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeEngineServiceCreateOrderArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceCreateOrderArgs(%+v)", *p)

}

type TradeEngineServiceCreateOrderResult struct {
	Success *CreateOrderResp `thrift:"success,0,optional" frugal:"0,optional,CreateOrderResp" json:"success,omitempty"`
}

func NewTradeEngineServiceCreateOrderResult() *TradeEngineServiceCreateOrderResult {
	return &TradeEngineServiceCreateOrderResult{}
}

func (p *TradeEngineServiceCreateOrderResult) InitDefault() {
}

var TradeEngineServiceCreateOrderResult_Success_DEFAULT *CreateOrderResp

func (p *TradeEngineServiceCreateOrderResult) GetSuccess() (v *CreateOrderResp) {
	if !p.IsSetSuccess() {
		return TradeEngineServiceCreateOrderResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeEngineServiceCreateOrderResult) SetSuccess(x interface{}) {
	p.Success = x.(*CreateOrderResp)
}

var fieldIDToName_TradeEngineServiceCreateOrderResult = map[int16]string{
	0: "success",
}

func (p *TradeEngineServiceCreateOrderResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeEngineServiceCreateOrderResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceCreateOrderResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceCreateOrderResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceCreateOrderResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreateOrderResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeEngineServiceCreateOrderResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceCreateOrderResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateOrder_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceCreateOrderResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeEngineServiceCreateOrderResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceCreateOrderResult(%+v)", *p)

}

type TradeEngineServiceActionOrderArgs struct {
	Req *ActionOrderReq `thrift:"req,1" frugal:"1,default,ActionOrderReq" json:"req"`
}

func NewTradeEngineServiceActionOrderArgs() *TradeEngineServiceActionOrderArgs {
	return &TradeEngineServiceActionOrderArgs{}
}

func (p *TradeEngineServiceActionOrderArgs) InitDefault() {
}

var TradeEngineServiceActionOrderArgs_Req_DEFAULT *ActionOrderReq

func (p *TradeEngineServiceActionOrderArgs) GetReq() (v *ActionOrderReq) {
	if !p.IsSetReq() {
		return TradeEngineServiceActionOrderArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeEngineServiceActionOrderArgs) SetReq(val *ActionOrderReq) {
	p.Req = val
}

var fieldIDToName_TradeEngineServiceActionOrderArgs = map[int16]string{
	1: "req",
}

func (p *TradeEngineServiceActionOrderArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeEngineServiceActionOrderArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceActionOrderArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceActionOrderArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceActionOrderArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewActionOrderReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeEngineServiceActionOrderArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceActionOrderArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ActionOrder_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceActionOrderArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeEngineServiceActionOrderArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceActionOrderArgs(%+v)", *p)

}

type TradeEngineServiceActionOrderResult struct {
	Success *ActionOrderResp `thrift:"success,0,optional" frugal:"0,optional,ActionOrderResp" json:"success,omitempty"`
}

func NewTradeEngineServiceActionOrderResult() *TradeEngineServiceActionOrderResult {
	return &TradeEngineServiceActionOrderResult{}
}

func (p *TradeEngineServiceActionOrderResult) InitDefault() {
}

var TradeEngineServiceActionOrderResult_Success_DEFAULT *ActionOrderResp

func (p *TradeEngineServiceActionOrderResult) GetSuccess() (v *ActionOrderResp) {
	if !p.IsSetSuccess() {
		return TradeEngineServiceActionOrderResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeEngineServiceActionOrderResult) SetSuccess(x interface{}) {
	p.Success = x.(*ActionOrderResp)
}

var fieldIDToName_TradeEngineServiceActionOrderResult = map[int16]string{
	0: "success",
}

func (p *TradeEngineServiceActionOrderResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeEngineServiceActionOrderResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceActionOrderResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceActionOrderResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceActionOrderResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewActionOrderResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeEngineServiceActionOrderResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceActionOrderResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ActionOrder_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceActionOrderResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeEngineServiceActionOrderResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceActionOrderResult(%+v)", *p)

}

type TradeEngineServiceGetOrderInfoArgs struct {
	Req *GetOrderInfoReq `thrift:"req,1" frugal:"1,default,GetOrderInfoReq" json:"req"`
}

func NewTradeEngineServiceGetOrderInfoArgs() *TradeEngineServiceGetOrderInfoArgs {
	return &TradeEngineServiceGetOrderInfoArgs{}
}

func (p *TradeEngineServiceGetOrderInfoArgs) InitDefault() {
}

var TradeEngineServiceGetOrderInfoArgs_Req_DEFAULT *GetOrderInfoReq

func (p *TradeEngineServiceGetOrderInfoArgs) GetReq() (v *GetOrderInfoReq) {
	if !p.IsSetReq() {
		return TradeEngineServiceGetOrderInfoArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeEngineServiceGetOrderInfoArgs) SetReq(val *GetOrderInfoReq) {
	p.Req = val
}

var fieldIDToName_TradeEngineServiceGetOrderInfoArgs = map[int16]string{
	1: "req",
}

func (p *TradeEngineServiceGetOrderInfoArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeEngineServiceGetOrderInfoArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceGetOrderInfoArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceGetOrderInfoArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderInfoArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetOrderInfoReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeEngineServiceGetOrderInfoArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceGetOrderInfoArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetOrderInfo_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceGetOrderInfoArgs(%+v)", *p)

}

type TradeEngineServiceGetOrderInfoResult struct {
	Success *OrderInfoDetailResp `thrift:"success,0,optional" frugal:"0,optional,OrderInfoDetailResp" json:"success,omitempty"`
}

func NewTradeEngineServiceGetOrderInfoResult() *TradeEngineServiceGetOrderInfoResult {
	return &TradeEngineServiceGetOrderInfoResult{}
}

func (p *TradeEngineServiceGetOrderInfoResult) InitDefault() {
}

var TradeEngineServiceGetOrderInfoResult_Success_DEFAULT *OrderInfoDetailResp

func (p *TradeEngineServiceGetOrderInfoResult) GetSuccess() (v *OrderInfoDetailResp) {
	if !p.IsSetSuccess() {
		return TradeEngineServiceGetOrderInfoResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeEngineServiceGetOrderInfoResult) SetSuccess(x interface{}) {
	p.Success = x.(*OrderInfoDetailResp)
}

var fieldIDToName_TradeEngineServiceGetOrderInfoResult = map[int16]string{
	0: "success",
}

func (p *TradeEngineServiceGetOrderInfoResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeEngineServiceGetOrderInfoResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceGetOrderInfoResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceGetOrderInfoResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderInfoResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewOrderInfoDetailResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeEngineServiceGetOrderInfoResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceGetOrderInfoResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetOrderInfo_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceGetOrderInfoResult(%+v)", *p)

}

type TradeEngineServiceMGetOrderInfoArgs struct {
	Req *MGetOrderInfoReq `thrift:"req,1" frugal:"1,default,MGetOrderInfoReq" json:"req"`
}

func NewTradeEngineServiceMGetOrderInfoArgs() *TradeEngineServiceMGetOrderInfoArgs {
	return &TradeEngineServiceMGetOrderInfoArgs{}
}

func (p *TradeEngineServiceMGetOrderInfoArgs) InitDefault() {
}

var TradeEngineServiceMGetOrderInfoArgs_Req_DEFAULT *MGetOrderInfoReq

func (p *TradeEngineServiceMGetOrderInfoArgs) GetReq() (v *MGetOrderInfoReq) {
	if !p.IsSetReq() {
		return TradeEngineServiceMGetOrderInfoArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeEngineServiceMGetOrderInfoArgs) SetReq(val *MGetOrderInfoReq) {
	p.Req = val
}

var fieldIDToName_TradeEngineServiceMGetOrderInfoArgs = map[int16]string{
	1: "req",
}

func (p *TradeEngineServiceMGetOrderInfoArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeEngineServiceMGetOrderInfoArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceMGetOrderInfoArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceMGetOrderInfoArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceMGetOrderInfoArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewMGetOrderInfoReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeEngineServiceMGetOrderInfoArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceMGetOrderInfoArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("MGetOrderInfo_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceMGetOrderInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeEngineServiceMGetOrderInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceMGetOrderInfoArgs(%+v)", *p)

}

type TradeEngineServiceMGetOrderInfoResult struct {
	Success *MGetOrderInfoResp `thrift:"success,0,optional" frugal:"0,optional,MGetOrderInfoResp" json:"success,omitempty"`
}

func NewTradeEngineServiceMGetOrderInfoResult() *TradeEngineServiceMGetOrderInfoResult {
	return &TradeEngineServiceMGetOrderInfoResult{}
}

func (p *TradeEngineServiceMGetOrderInfoResult) InitDefault() {
}

var TradeEngineServiceMGetOrderInfoResult_Success_DEFAULT *MGetOrderInfoResp

func (p *TradeEngineServiceMGetOrderInfoResult) GetSuccess() (v *MGetOrderInfoResp) {
	if !p.IsSetSuccess() {
		return TradeEngineServiceMGetOrderInfoResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeEngineServiceMGetOrderInfoResult) SetSuccess(x interface{}) {
	p.Success = x.(*MGetOrderInfoResp)
}

var fieldIDToName_TradeEngineServiceMGetOrderInfoResult = map[int16]string{
	0: "success",
}

func (p *TradeEngineServiceMGetOrderInfoResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeEngineServiceMGetOrderInfoResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceMGetOrderInfoResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceMGetOrderInfoResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceMGetOrderInfoResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewMGetOrderInfoResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeEngineServiceMGetOrderInfoResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceMGetOrderInfoResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("MGetOrderInfo_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceMGetOrderInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeEngineServiceMGetOrderInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceMGetOrderInfoResult(%+v)", *p)

}

type TradeEngineServiceGetOrderLogArgs struct {
	Req *GetOrderLogReq `thrift:"req,1" frugal:"1,default,GetOrderLogReq" json:"req"`
}

func NewTradeEngineServiceGetOrderLogArgs() *TradeEngineServiceGetOrderLogArgs {
	return &TradeEngineServiceGetOrderLogArgs{}
}

func (p *TradeEngineServiceGetOrderLogArgs) InitDefault() {
}

var TradeEngineServiceGetOrderLogArgs_Req_DEFAULT *GetOrderLogReq

func (p *TradeEngineServiceGetOrderLogArgs) GetReq() (v *GetOrderLogReq) {
	if !p.IsSetReq() {
		return TradeEngineServiceGetOrderLogArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeEngineServiceGetOrderLogArgs) SetReq(val *GetOrderLogReq) {
	p.Req = val
}

var fieldIDToName_TradeEngineServiceGetOrderLogArgs = map[int16]string{
	1: "req",
}

func (p *TradeEngineServiceGetOrderLogArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeEngineServiceGetOrderLogArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceGetOrderLogArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceGetOrderLogArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderLogArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetOrderLogReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeEngineServiceGetOrderLogArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceGetOrderLogArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetOrderLog_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderLogArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderLogArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceGetOrderLogArgs(%+v)", *p)

}

type TradeEngineServiceGetOrderLogResult struct {
	Success *GetOrderLogResp `thrift:"success,0,optional" frugal:"0,optional,GetOrderLogResp" json:"success,omitempty"`
}

func NewTradeEngineServiceGetOrderLogResult() *TradeEngineServiceGetOrderLogResult {
	return &TradeEngineServiceGetOrderLogResult{}
}

func (p *TradeEngineServiceGetOrderLogResult) InitDefault() {
}

var TradeEngineServiceGetOrderLogResult_Success_DEFAULT *GetOrderLogResp

func (p *TradeEngineServiceGetOrderLogResult) GetSuccess() (v *GetOrderLogResp) {
	if !p.IsSetSuccess() {
		return TradeEngineServiceGetOrderLogResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeEngineServiceGetOrderLogResult) SetSuccess(x interface{}) {
	p.Success = x.(*GetOrderLogResp)
}

var fieldIDToName_TradeEngineServiceGetOrderLogResult = map[int16]string{
	0: "success",
}

func (p *TradeEngineServiceGetOrderLogResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeEngineServiceGetOrderLogResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceGetOrderLogResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceGetOrderLogResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderLogResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetOrderLogResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeEngineServiceGetOrderLogResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceGetOrderLogResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetOrderLog_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderLogResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeEngineServiceGetOrderLogResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceGetOrderLogResult(%+v)", *p)

}

type TradeEngineServiceBindUserOrderArgs struct {
	Req *BindUserOrderReq `thrift:"req,1" frugal:"1,default,BindUserOrderReq" json:"req"`
}

func NewTradeEngineServiceBindUserOrderArgs() *TradeEngineServiceBindUserOrderArgs {
	return &TradeEngineServiceBindUserOrderArgs{}
}

func (p *TradeEngineServiceBindUserOrderArgs) InitDefault() {
}

var TradeEngineServiceBindUserOrderArgs_Req_DEFAULT *BindUserOrderReq

func (p *TradeEngineServiceBindUserOrderArgs) GetReq() (v *BindUserOrderReq) {
	if !p.IsSetReq() {
		return TradeEngineServiceBindUserOrderArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeEngineServiceBindUserOrderArgs) SetReq(val *BindUserOrderReq) {
	p.Req = val
}

var fieldIDToName_TradeEngineServiceBindUserOrderArgs = map[int16]string{
	1: "req",
}

func (p *TradeEngineServiceBindUserOrderArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeEngineServiceBindUserOrderArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceBindUserOrderArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceBindUserOrderArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceBindUserOrderArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewBindUserOrderReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeEngineServiceBindUserOrderArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceBindUserOrderArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("BindUserOrder_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceBindUserOrderArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeEngineServiceBindUserOrderArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceBindUserOrderArgs(%+v)", *p)

}

type TradeEngineServiceBindUserOrderResult struct {
	Success *BindUserOrderResp `thrift:"success,0,optional" frugal:"0,optional,BindUserOrderResp" json:"success,omitempty"`
}

func NewTradeEngineServiceBindUserOrderResult() *TradeEngineServiceBindUserOrderResult {
	return &TradeEngineServiceBindUserOrderResult{}
}

func (p *TradeEngineServiceBindUserOrderResult) InitDefault() {
}

var TradeEngineServiceBindUserOrderResult_Success_DEFAULT *BindUserOrderResp

func (p *TradeEngineServiceBindUserOrderResult) GetSuccess() (v *BindUserOrderResp) {
	if !p.IsSetSuccess() {
		return TradeEngineServiceBindUserOrderResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeEngineServiceBindUserOrderResult) SetSuccess(x interface{}) {
	p.Success = x.(*BindUserOrderResp)
}

var fieldIDToName_TradeEngineServiceBindUserOrderResult = map[int16]string{
	0: "success",
}

func (p *TradeEngineServiceBindUserOrderResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeEngineServiceBindUserOrderResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceBindUserOrderResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceBindUserOrderResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceBindUserOrderResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewBindUserOrderResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeEngineServiceBindUserOrderResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceBindUserOrderResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("BindUserOrder_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceBindUserOrderResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeEngineServiceBindUserOrderResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceBindUserOrderResult(%+v)", *p)

}

type TradeEngineServiceUpdateOrderProductArgs struct {
	Req *UpdateOrderProductReq `thrift:"req,1" frugal:"1,default,UpdateOrderProductReq" json:"req"`
}

func NewTradeEngineServiceUpdateOrderProductArgs() *TradeEngineServiceUpdateOrderProductArgs {
	return &TradeEngineServiceUpdateOrderProductArgs{}
}

func (p *TradeEngineServiceUpdateOrderProductArgs) InitDefault() {
}

var TradeEngineServiceUpdateOrderProductArgs_Req_DEFAULT *UpdateOrderProductReq

func (p *TradeEngineServiceUpdateOrderProductArgs) GetReq() (v *UpdateOrderProductReq) {
	if !p.IsSetReq() {
		return TradeEngineServiceUpdateOrderProductArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeEngineServiceUpdateOrderProductArgs) SetReq(val *UpdateOrderProductReq) {
	p.Req = val
}

var fieldIDToName_TradeEngineServiceUpdateOrderProductArgs = map[int16]string{
	1: "req",
}

func (p *TradeEngineServiceUpdateOrderProductArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeEngineServiceUpdateOrderProductArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceUpdateOrderProductArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceUpdateOrderProductArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceUpdateOrderProductArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpdateOrderProductReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeEngineServiceUpdateOrderProductArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceUpdateOrderProductArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateOrderProduct_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceUpdateOrderProductArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeEngineServiceUpdateOrderProductArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceUpdateOrderProductArgs(%+v)", *p)

}

type TradeEngineServiceUpdateOrderProductResult struct {
	Success *UpdateOrderProductResp `thrift:"success,0,optional" frugal:"0,optional,UpdateOrderProductResp" json:"success,omitempty"`
}

func NewTradeEngineServiceUpdateOrderProductResult() *TradeEngineServiceUpdateOrderProductResult {
	return &TradeEngineServiceUpdateOrderProductResult{}
}

func (p *TradeEngineServiceUpdateOrderProductResult) InitDefault() {
}

var TradeEngineServiceUpdateOrderProductResult_Success_DEFAULT *UpdateOrderProductResp

func (p *TradeEngineServiceUpdateOrderProductResult) GetSuccess() (v *UpdateOrderProductResp) {
	if !p.IsSetSuccess() {
		return TradeEngineServiceUpdateOrderProductResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeEngineServiceUpdateOrderProductResult) SetSuccess(x interface{}) {
	p.Success = x.(*UpdateOrderProductResp)
}

var fieldIDToName_TradeEngineServiceUpdateOrderProductResult = map[int16]string{
	0: "success",
}

func (p *TradeEngineServiceUpdateOrderProductResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeEngineServiceUpdateOrderProductResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceUpdateOrderProductResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceUpdateOrderProductResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceUpdateOrderProductResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpdateOrderProductResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeEngineServiceUpdateOrderProductResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceUpdateOrderProductResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateOrderProduct_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceUpdateOrderProductResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeEngineServiceUpdateOrderProductResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceUpdateOrderProductResult(%+v)", *p)

}

type TradeEngineServiceRepostOrderMessageArgs struct {
	Req *RepostOrderMessageReq `thrift:"req,1" frugal:"1,default,RepostOrderMessageReq" json:"req"`
}

func NewTradeEngineServiceRepostOrderMessageArgs() *TradeEngineServiceRepostOrderMessageArgs {
	return &TradeEngineServiceRepostOrderMessageArgs{}
}

func (p *TradeEngineServiceRepostOrderMessageArgs) InitDefault() {
}

var TradeEngineServiceRepostOrderMessageArgs_Req_DEFAULT *RepostOrderMessageReq

func (p *TradeEngineServiceRepostOrderMessageArgs) GetReq() (v *RepostOrderMessageReq) {
	if !p.IsSetReq() {
		return TradeEngineServiceRepostOrderMessageArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeEngineServiceRepostOrderMessageArgs) SetReq(val *RepostOrderMessageReq) {
	p.Req = val
}

var fieldIDToName_TradeEngineServiceRepostOrderMessageArgs = map[int16]string{
	1: "req",
}

func (p *TradeEngineServiceRepostOrderMessageArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeEngineServiceRepostOrderMessageArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceRepostOrderMessageArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceRepostOrderMessageArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceRepostOrderMessageArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewRepostOrderMessageReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeEngineServiceRepostOrderMessageArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceRepostOrderMessageArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("RepostOrderMessage_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceRepostOrderMessageArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeEngineServiceRepostOrderMessageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceRepostOrderMessageArgs(%+v)", *p)

}

type TradeEngineServiceRepostOrderMessageResult struct {
	Success *RepostOrderMessageResp `thrift:"success,0,optional" frugal:"0,optional,RepostOrderMessageResp" json:"success,omitempty"`
}

func NewTradeEngineServiceRepostOrderMessageResult() *TradeEngineServiceRepostOrderMessageResult {
	return &TradeEngineServiceRepostOrderMessageResult{}
}

func (p *TradeEngineServiceRepostOrderMessageResult) InitDefault() {
}

var TradeEngineServiceRepostOrderMessageResult_Success_DEFAULT *RepostOrderMessageResp

func (p *TradeEngineServiceRepostOrderMessageResult) GetSuccess() (v *RepostOrderMessageResp) {
	if !p.IsSetSuccess() {
		return TradeEngineServiceRepostOrderMessageResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeEngineServiceRepostOrderMessageResult) SetSuccess(x interface{}) {
	p.Success = x.(*RepostOrderMessageResp)
}

var fieldIDToName_TradeEngineServiceRepostOrderMessageResult = map[int16]string{
	0: "success",
}

func (p *TradeEngineServiceRepostOrderMessageResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeEngineServiceRepostOrderMessageResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceRepostOrderMessageResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeEngineServiceRepostOrderMessageResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeEngineServiceRepostOrderMessageResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewRepostOrderMessageResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeEngineServiceRepostOrderMessageResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeEngineServiceRepostOrderMessageResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("RepostOrderMessage_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeEngineServiceRepostOrderMessageResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeEngineServiceRepostOrderMessageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeEngineServiceRepostOrderMessageResult(%+v)", *p)

}
