// Code generated by Kitex v1.20.3. DO NOT EDIT.

package tradeengineservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	engine "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	CreateOrder(ctx context.Context, req *engine.CreateOrderReq, callOptions ...callopt.Option) (r *engine.CreateOrderResp, err error)
	ActionOrder(ctx context.Context, req *engine.ActionOrderReq, callOptions ...callopt.Option) (r *engine.ActionOrderResp, err error)
	GetOrderInfo(ctx context.Context, req *engine.GetOrderInfoReq, callOptions ...callopt.Option) (r *engine.OrderInfoDetailResp, err error)
	MGetOrderInfo(ctx context.Context, req *engine.MGetOrderInfoReq, callOptions ...callopt.Option) (r *engine.MGetOrderInfoResp, err error)
	GetOrderLog(ctx context.Context, req *engine.GetOrderLogReq, callOptions ...callopt.Option) (r *engine.GetOrderLogResp, err error)
	BindUserOrder(ctx context.Context, req *engine.BindUserOrderReq, callOptions ...callopt.Option) (r *engine.BindUserOrderResp, err error)
	UpdateOrderProduct(ctx context.Context, req *engine.UpdateOrderProductReq, callOptions ...callopt.Option) (r *engine.UpdateOrderProductResp, err error)
	RepostOrderMessage(ctx context.Context, req *engine.RepostOrderMessageReq, callOptions ...callopt.Option) (r *engine.RepostOrderMessageResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kTradeEngineServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kTradeEngineServiceClient struct {
	*kClient
}

func (p *kTradeEngineServiceClient) CreateOrder(ctx context.Context, req *engine.CreateOrderReq, callOptions ...callopt.Option) (r *engine.CreateOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateOrder(ctx, req)
}

func (p *kTradeEngineServiceClient) ActionOrder(ctx context.Context, req *engine.ActionOrderReq, callOptions ...callopt.Option) (r *engine.ActionOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ActionOrder(ctx, req)
}

func (p *kTradeEngineServiceClient) GetOrderInfo(ctx context.Context, req *engine.GetOrderInfoReq, callOptions ...callopt.Option) (r *engine.OrderInfoDetailResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetOrderInfo(ctx, req)
}

func (p *kTradeEngineServiceClient) MGetOrderInfo(ctx context.Context, req *engine.MGetOrderInfoReq, callOptions ...callopt.Option) (r *engine.MGetOrderInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetOrderInfo(ctx, req)
}

func (p *kTradeEngineServiceClient) GetOrderLog(ctx context.Context, req *engine.GetOrderLogReq, callOptions ...callopt.Option) (r *engine.GetOrderLogResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetOrderLog(ctx, req)
}

func (p *kTradeEngineServiceClient) BindUserOrder(ctx context.Context, req *engine.BindUserOrderReq, callOptions ...callopt.Option) (r *engine.BindUserOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.BindUserOrder(ctx, req)
}

func (p *kTradeEngineServiceClient) UpdateOrderProduct(ctx context.Context, req *engine.UpdateOrderProductReq, callOptions ...callopt.Option) (r *engine.UpdateOrderProductResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateOrderProduct(ctx, req)
}

func (p *kTradeEngineServiceClient) RepostOrderMessage(ctx context.Context, req *engine.RepostOrderMessageReq, callOptions ...callopt.Option) (r *engine.RepostOrderMessageResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.RepostOrderMessage(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kTradeEngineServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
