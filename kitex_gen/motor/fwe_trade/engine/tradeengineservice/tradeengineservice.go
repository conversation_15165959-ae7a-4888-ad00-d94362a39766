// Code generated by Kitex v1.20.3. DO NOT EDIT.

package tradeengineservice

import (
	client "code.byted.org/kite/kitex/client"
	engine "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"CreateOrder": kitex.NewMethodInfo(
		createOrderHandler,
		newTradeEngineServiceCreateOrderArgs,
		newTradeEngineServiceCreateOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ActionOrder": kitex.NewMethodInfo(
		actionOrderHandler,
		newTradeEngineServiceActionOrderArgs,
		newTradeEngineServiceActionOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetOrderInfo": kitex.NewMethodInfo(
		getOrderInfoHandler,
		newTradeEngineServiceGetOrderInfoArgs,
		newTradeEngineServiceGetOrderInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetOrderInfo": kitex.NewMethodInfo(
		mGetOrderInfoHandler,
		newTradeEngineServiceMGetOrderInfoArgs,
		newTradeEngineServiceMGetOrderInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetOrderLog": kitex.NewMethodInfo(
		getOrderLogHandler,
		newTradeEngineServiceGetOrderLogArgs,
		newTradeEngineServiceGetOrderLogResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"BindUserOrder": kitex.NewMethodInfo(
		bindUserOrderHandler,
		newTradeEngineServiceBindUserOrderArgs,
		newTradeEngineServiceBindUserOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateOrderProduct": kitex.NewMethodInfo(
		updateOrderProductHandler,
		newTradeEngineServiceUpdateOrderProductArgs,
		newTradeEngineServiceUpdateOrderProductResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"RepostOrderMessage": kitex.NewMethodInfo(
		repostOrderMessageHandler,
		newTradeEngineServiceRepostOrderMessageArgs,
		newTradeEngineServiceRepostOrderMessageResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	tradeEngineServiceServiceInfo                = NewServiceInfo()
	tradeEngineServiceServiceInfoForClient       = NewServiceInfoForClient()
	tradeEngineServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return tradeEngineServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return tradeEngineServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return tradeEngineServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "TradeEngineService"
	handlerType := (*engine.TradeEngineService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "engine",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func createOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.TradeEngineServiceCreateOrderArgs)
	realResult := result.(*engine.TradeEngineServiceCreateOrderResult)
	success, err := handler.(engine.TradeEngineService).CreateOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeEngineServiceCreateOrderArgs() interface{} {
	return engine.NewTradeEngineServiceCreateOrderArgs()
}

func newTradeEngineServiceCreateOrderResult() interface{} {
	return engine.NewTradeEngineServiceCreateOrderResult()
}

func actionOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.TradeEngineServiceActionOrderArgs)
	realResult := result.(*engine.TradeEngineServiceActionOrderResult)
	success, err := handler.(engine.TradeEngineService).ActionOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeEngineServiceActionOrderArgs() interface{} {
	return engine.NewTradeEngineServiceActionOrderArgs()
}

func newTradeEngineServiceActionOrderResult() interface{} {
	return engine.NewTradeEngineServiceActionOrderResult()
}

func getOrderInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.TradeEngineServiceGetOrderInfoArgs)
	realResult := result.(*engine.TradeEngineServiceGetOrderInfoResult)
	success, err := handler.(engine.TradeEngineService).GetOrderInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeEngineServiceGetOrderInfoArgs() interface{} {
	return engine.NewTradeEngineServiceGetOrderInfoArgs()
}

func newTradeEngineServiceGetOrderInfoResult() interface{} {
	return engine.NewTradeEngineServiceGetOrderInfoResult()
}

func mGetOrderInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.TradeEngineServiceMGetOrderInfoArgs)
	realResult := result.(*engine.TradeEngineServiceMGetOrderInfoResult)
	success, err := handler.(engine.TradeEngineService).MGetOrderInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeEngineServiceMGetOrderInfoArgs() interface{} {
	return engine.NewTradeEngineServiceMGetOrderInfoArgs()
}

func newTradeEngineServiceMGetOrderInfoResult() interface{} {
	return engine.NewTradeEngineServiceMGetOrderInfoResult()
}

func getOrderLogHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.TradeEngineServiceGetOrderLogArgs)
	realResult := result.(*engine.TradeEngineServiceGetOrderLogResult)
	success, err := handler.(engine.TradeEngineService).GetOrderLog(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeEngineServiceGetOrderLogArgs() interface{} {
	return engine.NewTradeEngineServiceGetOrderLogArgs()
}

func newTradeEngineServiceGetOrderLogResult() interface{} {
	return engine.NewTradeEngineServiceGetOrderLogResult()
}

func bindUserOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.TradeEngineServiceBindUserOrderArgs)
	realResult := result.(*engine.TradeEngineServiceBindUserOrderResult)
	success, err := handler.(engine.TradeEngineService).BindUserOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeEngineServiceBindUserOrderArgs() interface{} {
	return engine.NewTradeEngineServiceBindUserOrderArgs()
}

func newTradeEngineServiceBindUserOrderResult() interface{} {
	return engine.NewTradeEngineServiceBindUserOrderResult()
}

func updateOrderProductHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.TradeEngineServiceUpdateOrderProductArgs)
	realResult := result.(*engine.TradeEngineServiceUpdateOrderProductResult)
	success, err := handler.(engine.TradeEngineService).UpdateOrderProduct(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeEngineServiceUpdateOrderProductArgs() interface{} {
	return engine.NewTradeEngineServiceUpdateOrderProductArgs()
}

func newTradeEngineServiceUpdateOrderProductResult() interface{} {
	return engine.NewTradeEngineServiceUpdateOrderProductResult()
}

func repostOrderMessageHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.TradeEngineServiceRepostOrderMessageArgs)
	realResult := result.(*engine.TradeEngineServiceRepostOrderMessageResult)
	success, err := handler.(engine.TradeEngineService).RepostOrderMessage(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeEngineServiceRepostOrderMessageArgs() interface{} {
	return engine.NewTradeEngineServiceRepostOrderMessageArgs()
}

func newTradeEngineServiceRepostOrderMessageResult() interface{} {
	return engine.NewTradeEngineServiceRepostOrderMessageResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) CreateOrder(ctx context.Context, req *engine.CreateOrderReq) (r *engine.CreateOrderResp, err error) {
	var _args engine.TradeEngineServiceCreateOrderArgs
	_args.Req = req
	var _result engine.TradeEngineServiceCreateOrderResult
	if err = p.c.Call(ctx, "CreateOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ActionOrder(ctx context.Context, req *engine.ActionOrderReq) (r *engine.ActionOrderResp, err error) {
	var _args engine.TradeEngineServiceActionOrderArgs
	_args.Req = req
	var _result engine.TradeEngineServiceActionOrderResult
	if err = p.c.Call(ctx, "ActionOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetOrderInfo(ctx context.Context, req *engine.GetOrderInfoReq) (r *engine.OrderInfoDetailResp, err error) {
	var _args engine.TradeEngineServiceGetOrderInfoArgs
	_args.Req = req
	var _result engine.TradeEngineServiceGetOrderInfoResult
	if err = p.c.Call(ctx, "GetOrderInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetOrderInfo(ctx context.Context, req *engine.MGetOrderInfoReq) (r *engine.MGetOrderInfoResp, err error) {
	var _args engine.TradeEngineServiceMGetOrderInfoArgs
	_args.Req = req
	var _result engine.TradeEngineServiceMGetOrderInfoResult
	if err = p.c.Call(ctx, "MGetOrderInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetOrderLog(ctx context.Context, req *engine.GetOrderLogReq) (r *engine.GetOrderLogResp, err error) {
	var _args engine.TradeEngineServiceGetOrderLogArgs
	_args.Req = req
	var _result engine.TradeEngineServiceGetOrderLogResult
	if err = p.c.Call(ctx, "GetOrderLog", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) BindUserOrder(ctx context.Context, req *engine.BindUserOrderReq) (r *engine.BindUserOrderResp, err error) {
	var _args engine.TradeEngineServiceBindUserOrderArgs
	_args.Req = req
	var _result engine.TradeEngineServiceBindUserOrderResult
	if err = p.c.Call(ctx, "BindUserOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateOrderProduct(ctx context.Context, req *engine.UpdateOrderProductReq) (r *engine.UpdateOrderProductResp, err error) {
	var _args engine.TradeEngineServiceUpdateOrderProductArgs
	_args.Req = req
	var _result engine.TradeEngineServiceUpdateOrderProductResult
	if err = p.c.Call(ctx, "UpdateOrderProduct", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) RepostOrderMessage(ctx context.Context, req *engine.RepostOrderMessageReq) (r *engine.RepostOrderMessageResp, err error) {
	var _args engine.TradeEngineServiceRepostOrderMessageArgs
	_args.Req = req
	var _result engine.TradeEngineServiceRepostOrderMessageResult
	if err = p.c.Call(ctx, "RepostOrderMessage", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
