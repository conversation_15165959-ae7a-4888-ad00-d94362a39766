// Code generated by Kitex v1.20.3. DO NOT EDIT.

package financeaccountservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	finance_account "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	CreateFweSubMerchant(ctx context.Context, req *finance_account.CreateFweSubMerchantReq, callOptions ...callopt.Option) (r *finance_account.CreateFweSubMerchantResp, err error)
	ModifyFweSubMerchant(ctx context.Context, req *finance_account.ModifyFweSubMerchantReq, callOptions ...callopt.Option) (r *finance_account.ModifyFweSubMerchantResp, err error)
	QueryFweSubMerchantInfo(ctx context.Context, req *finance_account.QueryFweSubMerchantInfoReq, callOptions ...callopt.Option) (r *finance_account.QueryFweSubMerchantInfoResp, err error)
	QuerySubMerchantInfo(ctx context.Context, req *finance_account.QuerySubMerchantInfoReq, callOptions ...callopt.Option) (r *finance_account.QuerySubMerchantInfoResp, err error)
	GetFweSubMerchantState(ctx context.Context, req *finance_account.GetFweSubMerchantStateReq, callOptions ...callopt.Option) (r *finance_account.GetFweSubMerchantStateResp, err error)
	GetFweSubMerchantInfo(ctx context.Context, req *finance_account.GetFweSubMerchantInfoReq, callOptions ...callopt.Option) (r *finance_account.GetFweSubMerchantInfoResp, err error)
	MGetFweSubMerchantInfo(ctx context.Context, req *finance_account.MGetFweSubMerchantInfoReq, callOptions ...callopt.Option) (r *finance_account.MGetFweSubMerchantInfoResp, err error)
	UnionNotifySolutionSubMerchant(ctx context.Context, req *finance_account.UnionNotifyReq, callOptions ...callopt.Option) (r *finance_account.UnionNotifyResp, err error)
	UnionNotifyFweSubMerchant(ctx context.Context, req *finance_account.UnionNotifyReq, callOptions ...callopt.Option) (r *finance_account.UnionNotifyResp, err error)
	CheckJstAccountBank(ctx context.Context, req *finance_account.CheckJstAccountBankReq, callOptions ...callopt.Option) (r *finance_account.CheckJstAccountBankRsp, err error)
	ChangeAccountRule(ctx context.Context, req *finance_account.ChangeAccountRuleReq, callOptions ...callopt.Option) (r *finance_account.ChangeAccountRuleResp, err error)
	FetchPlatformURL(ctx context.Context, req *finance_account.FetchPlatformURLReq, callOptions ...callopt.Option) (r *finance_account.FetchPlatformURLResp, err error)
	FetchAccountCenterURL(ctx context.Context, req *finance_account.FetchAccountCenterURLReq, callOptions ...callopt.Option) (r *finance_account.FetchAccountCenterURLResp, err error)
	GetFinanceAccountTokenInfo(ctx context.Context, req *finance_account.GetFinanceAccountTokenInfoReq, callOptions ...callopt.Option) (r *finance_account.GetFinanceAccountTokenInfoResp, err error)
	ValidateApplyResultInfo(ctx context.Context, req *finance_account.ValidateApplyResultInfoReq, callOptions ...callopt.Option) (r *finance_account.ValidateApplyResultInfoResp, err error)
	MappingFinanceAccount(ctx context.Context, req *finance_account.MappingFinanceAccountReq, callOptions ...callopt.Option) (r *finance_account.MappingFinanceAccountResp, err error)
	SetWithdrawMode(ctx context.Context, req *finance_account.SetWithdrawModeReq, callOptions ...callopt.Option) (r *finance_account.SetWithdrawModeResp, err error)
	GetWithdrawMode(ctx context.Context, req *finance_account.GetWithdrawModeReq, callOptions ...callopt.Option) (r *finance_account.GetWithdrawModeResp, err error)
	QueryAccountBalance(ctx context.Context, req *finance_account.QueryAccountBalanceReq, callOptions ...callopt.Option) (r *finance_account.QueryAccountBalanceResp, err error)
	WithdrawAllBalance(ctx context.Context, req *finance_account.WithdrawAllBalanceReq, callOptions ...callopt.Option) (r *finance_account.WithdrawAllBalanceResp, err error)
	WithdrawNotify(ctx context.Context, req *finance_account.WithdrawNotifyReq, callOptions ...callopt.Option) (r *finance_account.WithdrawNotifyResp, err error)
	AutoWithdrawAllBalance(ctx context.Context, req *finance_account.AutoWithdrawAllBalanceReq, callOptions ...callopt.Option) (r *finance_account.AutoWithdrawAllBalanceResp, err error)
	QueryFweSubMerchantList(ctx context.Context, req *finance_account.QueryFweSubMerchantListReq, callOptions ...callopt.Option) (r *finance_account.QueryFweSubMerchantListResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kFinanceAccountServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kFinanceAccountServiceClient struct {
	*kClient
}

func (p *kFinanceAccountServiceClient) CreateFweSubMerchant(ctx context.Context, req *finance_account.CreateFweSubMerchantReq, callOptions ...callopt.Option) (r *finance_account.CreateFweSubMerchantResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateFweSubMerchant(ctx, req)
}

func (p *kFinanceAccountServiceClient) ModifyFweSubMerchant(ctx context.Context, req *finance_account.ModifyFweSubMerchantReq, callOptions ...callopt.Option) (r *finance_account.ModifyFweSubMerchantResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ModifyFweSubMerchant(ctx, req)
}

func (p *kFinanceAccountServiceClient) QueryFweSubMerchantInfo(ctx context.Context, req *finance_account.QueryFweSubMerchantInfoReq, callOptions ...callopt.Option) (r *finance_account.QueryFweSubMerchantInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFweSubMerchantInfo(ctx, req)
}

func (p *kFinanceAccountServiceClient) QuerySubMerchantInfo(ctx context.Context, req *finance_account.QuerySubMerchantInfoReq, callOptions ...callopt.Option) (r *finance_account.QuerySubMerchantInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QuerySubMerchantInfo(ctx, req)
}

func (p *kFinanceAccountServiceClient) GetFweSubMerchantState(ctx context.Context, req *finance_account.GetFweSubMerchantStateReq, callOptions ...callopt.Option) (r *finance_account.GetFweSubMerchantStateResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetFweSubMerchantState(ctx, req)
}

func (p *kFinanceAccountServiceClient) GetFweSubMerchantInfo(ctx context.Context, req *finance_account.GetFweSubMerchantInfoReq, callOptions ...callopt.Option) (r *finance_account.GetFweSubMerchantInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetFweSubMerchantInfo(ctx, req)
}

func (p *kFinanceAccountServiceClient) MGetFweSubMerchantInfo(ctx context.Context, req *finance_account.MGetFweSubMerchantInfoReq, callOptions ...callopt.Option) (r *finance_account.MGetFweSubMerchantInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetFweSubMerchantInfo(ctx, req)
}

func (p *kFinanceAccountServiceClient) UnionNotifySolutionSubMerchant(ctx context.Context, req *finance_account.UnionNotifyReq, callOptions ...callopt.Option) (r *finance_account.UnionNotifyResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifySolutionSubMerchant(ctx, req)
}

func (p *kFinanceAccountServiceClient) UnionNotifyFweSubMerchant(ctx context.Context, req *finance_account.UnionNotifyReq, callOptions ...callopt.Option) (r *finance_account.UnionNotifyResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyFweSubMerchant(ctx, req)
}

func (p *kFinanceAccountServiceClient) CheckJstAccountBank(ctx context.Context, req *finance_account.CheckJstAccountBankReq, callOptions ...callopt.Option) (r *finance_account.CheckJstAccountBankRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CheckJstAccountBank(ctx, req)
}

func (p *kFinanceAccountServiceClient) ChangeAccountRule(ctx context.Context, req *finance_account.ChangeAccountRuleReq, callOptions ...callopt.Option) (r *finance_account.ChangeAccountRuleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ChangeAccountRule(ctx, req)
}

func (p *kFinanceAccountServiceClient) FetchPlatformURL(ctx context.Context, req *finance_account.FetchPlatformURLReq, callOptions ...callopt.Option) (r *finance_account.FetchPlatformURLResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FetchPlatformURL(ctx, req)
}

func (p *kFinanceAccountServiceClient) FetchAccountCenterURL(ctx context.Context, req *finance_account.FetchAccountCenterURLReq, callOptions ...callopt.Option) (r *finance_account.FetchAccountCenterURLResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FetchAccountCenterURL(ctx, req)
}

func (p *kFinanceAccountServiceClient) GetFinanceAccountTokenInfo(ctx context.Context, req *finance_account.GetFinanceAccountTokenInfoReq, callOptions ...callopt.Option) (r *finance_account.GetFinanceAccountTokenInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetFinanceAccountTokenInfo(ctx, req)
}

func (p *kFinanceAccountServiceClient) ValidateApplyResultInfo(ctx context.Context, req *finance_account.ValidateApplyResultInfoReq, callOptions ...callopt.Option) (r *finance_account.ValidateApplyResultInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ValidateApplyResultInfo(ctx, req)
}

func (p *kFinanceAccountServiceClient) MappingFinanceAccount(ctx context.Context, req *finance_account.MappingFinanceAccountReq, callOptions ...callopt.Option) (r *finance_account.MappingFinanceAccountResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MappingFinanceAccount(ctx, req)
}

func (p *kFinanceAccountServiceClient) SetWithdrawMode(ctx context.Context, req *finance_account.SetWithdrawModeReq, callOptions ...callopt.Option) (r *finance_account.SetWithdrawModeResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SetWithdrawMode(ctx, req)
}

func (p *kFinanceAccountServiceClient) GetWithdrawMode(ctx context.Context, req *finance_account.GetWithdrawModeReq, callOptions ...callopt.Option) (r *finance_account.GetWithdrawModeResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetWithdrawMode(ctx, req)
}

func (p *kFinanceAccountServiceClient) QueryAccountBalance(ctx context.Context, req *finance_account.QueryAccountBalanceReq, callOptions ...callopt.Option) (r *finance_account.QueryAccountBalanceResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryAccountBalance(ctx, req)
}

func (p *kFinanceAccountServiceClient) WithdrawAllBalance(ctx context.Context, req *finance_account.WithdrawAllBalanceReq, callOptions ...callopt.Option) (r *finance_account.WithdrawAllBalanceResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.WithdrawAllBalance(ctx, req)
}

func (p *kFinanceAccountServiceClient) WithdrawNotify(ctx context.Context, req *finance_account.WithdrawNotifyReq, callOptions ...callopt.Option) (r *finance_account.WithdrawNotifyResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.WithdrawNotify(ctx, req)
}

func (p *kFinanceAccountServiceClient) AutoWithdrawAllBalance(ctx context.Context, req *finance_account.AutoWithdrawAllBalanceReq, callOptions ...callopt.Option) (r *finance_account.AutoWithdrawAllBalanceResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AutoWithdrawAllBalance(ctx, req)
}

func (p *kFinanceAccountServiceClient) QueryFweSubMerchantList(ctx context.Context, req *finance_account.QueryFweSubMerchantListReq, callOptions ...callopt.Option) (r *finance_account.QueryFweSubMerchantListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFweSubMerchantList(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kFinanceAccountServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
