// Code generated by Kitex v1.20.3. DO NOT EDIT.

package financeaccountservice

import (
	client "code.byted.org/kite/kitex/client"
	finance_account "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/finance_account"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"CreateFweSubMerchant": kitex.NewMethodInfo(
		createFweSubMerchantHandler,
		newFinanceAccountServiceCreateFweSubMerchantArgs,
		newFinanceAccountServiceCreateFweSubMerchantResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ModifyFweSubMerchant": kitex.NewMethodInfo(
		modifyFweSubMerchantHandler,
		newFinanceAccountServiceModifyFweSubMerchantArgs,
		newFinanceAccountServiceModifyFweSubMerchantResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryFweSubMerchantInfo": kitex.NewMethodInfo(
		queryFweSubMerchantInfoHandler,
		newFinanceAccountServiceQueryFweSubMerchantInfoArgs,
		newFinanceAccountServiceQueryFweSubMerchantInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QuerySubMerchantInfo": kitex.NewMethodInfo(
		querySubMerchantInfoHandler,
		newFinanceAccountServiceQuerySubMerchantInfoArgs,
		newFinanceAccountServiceQuerySubMerchantInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetFweSubMerchantState": kitex.NewMethodInfo(
		getFweSubMerchantStateHandler,
		newFinanceAccountServiceGetFweSubMerchantStateArgs,
		newFinanceAccountServiceGetFweSubMerchantStateResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetFweSubMerchantInfo": kitex.NewMethodInfo(
		getFweSubMerchantInfoHandler,
		newFinanceAccountServiceGetFweSubMerchantInfoArgs,
		newFinanceAccountServiceGetFweSubMerchantInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetFweSubMerchantInfo": kitex.NewMethodInfo(
		mGetFweSubMerchantInfoHandler,
		newFinanceAccountServiceMGetFweSubMerchantInfoArgs,
		newFinanceAccountServiceMGetFweSubMerchantInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifySolutionSubMerchant": kitex.NewMethodInfo(
		unionNotifySolutionSubMerchantHandler,
		newFinanceAccountServiceUnionNotifySolutionSubMerchantArgs,
		newFinanceAccountServiceUnionNotifySolutionSubMerchantResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyFweSubMerchant": kitex.NewMethodInfo(
		unionNotifyFweSubMerchantHandler,
		newFinanceAccountServiceUnionNotifyFweSubMerchantArgs,
		newFinanceAccountServiceUnionNotifyFweSubMerchantResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CheckJstAccountBank": kitex.NewMethodInfo(
		checkJstAccountBankHandler,
		newFinanceAccountServiceCheckJstAccountBankArgs,
		newFinanceAccountServiceCheckJstAccountBankResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ChangeAccountRule": kitex.NewMethodInfo(
		changeAccountRuleHandler,
		newFinanceAccountServiceChangeAccountRuleArgs,
		newFinanceAccountServiceChangeAccountRuleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FetchPlatformUrl": kitex.NewMethodInfo(
		fetchPlatformURLHandler,
		newFinanceAccountServiceFetchPlatformURLArgs,
		newFinanceAccountServiceFetchPlatformURLResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FetchAccountCenterUrl": kitex.NewMethodInfo(
		fetchAccountCenterURLHandler,
		newFinanceAccountServiceFetchAccountCenterURLArgs,
		newFinanceAccountServiceFetchAccountCenterURLResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetFinanceAccountTokenInfo": kitex.NewMethodInfo(
		getFinanceAccountTokenInfoHandler,
		newFinanceAccountServiceGetFinanceAccountTokenInfoArgs,
		newFinanceAccountServiceGetFinanceAccountTokenInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ValidateApplyResultInfo": kitex.NewMethodInfo(
		validateApplyResultInfoHandler,
		newFinanceAccountServiceValidateApplyResultInfoArgs,
		newFinanceAccountServiceValidateApplyResultInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MappingFinanceAccount": kitex.NewMethodInfo(
		mappingFinanceAccountHandler,
		newFinanceAccountServiceMappingFinanceAccountArgs,
		newFinanceAccountServiceMappingFinanceAccountResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SetWithdrawMode": kitex.NewMethodInfo(
		setWithdrawModeHandler,
		newFinanceAccountServiceSetWithdrawModeArgs,
		newFinanceAccountServiceSetWithdrawModeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetWithdrawMode": kitex.NewMethodInfo(
		getWithdrawModeHandler,
		newFinanceAccountServiceGetWithdrawModeArgs,
		newFinanceAccountServiceGetWithdrawModeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryAccountBalance": kitex.NewMethodInfo(
		queryAccountBalanceHandler,
		newFinanceAccountServiceQueryAccountBalanceArgs,
		newFinanceAccountServiceQueryAccountBalanceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"WithdrawAllBalance": kitex.NewMethodInfo(
		withdrawAllBalanceHandler,
		newFinanceAccountServiceWithdrawAllBalanceArgs,
		newFinanceAccountServiceWithdrawAllBalanceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"WithdrawNotify": kitex.NewMethodInfo(
		withdrawNotifyHandler,
		newFinanceAccountServiceWithdrawNotifyArgs,
		newFinanceAccountServiceWithdrawNotifyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AutoWithdrawAllBalance": kitex.NewMethodInfo(
		autoWithdrawAllBalanceHandler,
		newFinanceAccountServiceAutoWithdrawAllBalanceArgs,
		newFinanceAccountServiceAutoWithdrawAllBalanceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryFweSubMerchantList": kitex.NewMethodInfo(
		queryFweSubMerchantListHandler,
		newFinanceAccountServiceQueryFweSubMerchantListArgs,
		newFinanceAccountServiceQueryFweSubMerchantListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	financeAccountServiceServiceInfo                = NewServiceInfo()
	financeAccountServiceServiceInfoForClient       = NewServiceInfoForClient()
	financeAccountServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return financeAccountServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return financeAccountServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return financeAccountServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "FinanceAccountService"
	handlerType := (*finance_account.FinanceAccountService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "finance_account",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func createFweSubMerchantHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceCreateFweSubMerchantArgs)
	realResult := result.(*finance_account.FinanceAccountServiceCreateFweSubMerchantResult)
	success, err := handler.(finance_account.FinanceAccountService).CreateFweSubMerchant(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceCreateFweSubMerchantArgs() interface{} {
	return finance_account.NewFinanceAccountServiceCreateFweSubMerchantArgs()
}

func newFinanceAccountServiceCreateFweSubMerchantResult() interface{} {
	return finance_account.NewFinanceAccountServiceCreateFweSubMerchantResult()
}

func modifyFweSubMerchantHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceModifyFweSubMerchantArgs)
	realResult := result.(*finance_account.FinanceAccountServiceModifyFweSubMerchantResult)
	success, err := handler.(finance_account.FinanceAccountService).ModifyFweSubMerchant(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceModifyFweSubMerchantArgs() interface{} {
	return finance_account.NewFinanceAccountServiceModifyFweSubMerchantArgs()
}

func newFinanceAccountServiceModifyFweSubMerchantResult() interface{} {
	return finance_account.NewFinanceAccountServiceModifyFweSubMerchantResult()
}

func queryFweSubMerchantInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceQueryFweSubMerchantInfoArgs)
	realResult := result.(*finance_account.FinanceAccountServiceQueryFweSubMerchantInfoResult)
	success, err := handler.(finance_account.FinanceAccountService).QueryFweSubMerchantInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceQueryFweSubMerchantInfoArgs() interface{} {
	return finance_account.NewFinanceAccountServiceQueryFweSubMerchantInfoArgs()
}

func newFinanceAccountServiceQueryFweSubMerchantInfoResult() interface{} {
	return finance_account.NewFinanceAccountServiceQueryFweSubMerchantInfoResult()
}

func querySubMerchantInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceQuerySubMerchantInfoArgs)
	realResult := result.(*finance_account.FinanceAccountServiceQuerySubMerchantInfoResult)
	success, err := handler.(finance_account.FinanceAccountService).QuerySubMerchantInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceQuerySubMerchantInfoArgs() interface{} {
	return finance_account.NewFinanceAccountServiceQuerySubMerchantInfoArgs()
}

func newFinanceAccountServiceQuerySubMerchantInfoResult() interface{} {
	return finance_account.NewFinanceAccountServiceQuerySubMerchantInfoResult()
}

func getFweSubMerchantStateHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceGetFweSubMerchantStateArgs)
	realResult := result.(*finance_account.FinanceAccountServiceGetFweSubMerchantStateResult)
	success, err := handler.(finance_account.FinanceAccountService).GetFweSubMerchantState(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceGetFweSubMerchantStateArgs() interface{} {
	return finance_account.NewFinanceAccountServiceGetFweSubMerchantStateArgs()
}

func newFinanceAccountServiceGetFweSubMerchantStateResult() interface{} {
	return finance_account.NewFinanceAccountServiceGetFweSubMerchantStateResult()
}

func getFweSubMerchantInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceGetFweSubMerchantInfoArgs)
	realResult := result.(*finance_account.FinanceAccountServiceGetFweSubMerchantInfoResult)
	success, err := handler.(finance_account.FinanceAccountService).GetFweSubMerchantInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceGetFweSubMerchantInfoArgs() interface{} {
	return finance_account.NewFinanceAccountServiceGetFweSubMerchantInfoArgs()
}

func newFinanceAccountServiceGetFweSubMerchantInfoResult() interface{} {
	return finance_account.NewFinanceAccountServiceGetFweSubMerchantInfoResult()
}

func mGetFweSubMerchantInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceMGetFweSubMerchantInfoArgs)
	realResult := result.(*finance_account.FinanceAccountServiceMGetFweSubMerchantInfoResult)
	success, err := handler.(finance_account.FinanceAccountService).MGetFweSubMerchantInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceMGetFweSubMerchantInfoArgs() interface{} {
	return finance_account.NewFinanceAccountServiceMGetFweSubMerchantInfoArgs()
}

func newFinanceAccountServiceMGetFweSubMerchantInfoResult() interface{} {
	return finance_account.NewFinanceAccountServiceMGetFweSubMerchantInfoResult()
}

func unionNotifySolutionSubMerchantHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceUnionNotifySolutionSubMerchantArgs)
	realResult := result.(*finance_account.FinanceAccountServiceUnionNotifySolutionSubMerchantResult)
	success, err := handler.(finance_account.FinanceAccountService).UnionNotifySolutionSubMerchant(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceUnionNotifySolutionSubMerchantArgs() interface{} {
	return finance_account.NewFinanceAccountServiceUnionNotifySolutionSubMerchantArgs()
}

func newFinanceAccountServiceUnionNotifySolutionSubMerchantResult() interface{} {
	return finance_account.NewFinanceAccountServiceUnionNotifySolutionSubMerchantResult()
}

func unionNotifyFweSubMerchantHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceUnionNotifyFweSubMerchantArgs)
	realResult := result.(*finance_account.FinanceAccountServiceUnionNotifyFweSubMerchantResult)
	success, err := handler.(finance_account.FinanceAccountService).UnionNotifyFweSubMerchant(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceUnionNotifyFweSubMerchantArgs() interface{} {
	return finance_account.NewFinanceAccountServiceUnionNotifyFweSubMerchantArgs()
}

func newFinanceAccountServiceUnionNotifyFweSubMerchantResult() interface{} {
	return finance_account.NewFinanceAccountServiceUnionNotifyFweSubMerchantResult()
}

func checkJstAccountBankHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceCheckJstAccountBankArgs)
	realResult := result.(*finance_account.FinanceAccountServiceCheckJstAccountBankResult)
	success, err := handler.(finance_account.FinanceAccountService).CheckJstAccountBank(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceCheckJstAccountBankArgs() interface{} {
	return finance_account.NewFinanceAccountServiceCheckJstAccountBankArgs()
}

func newFinanceAccountServiceCheckJstAccountBankResult() interface{} {
	return finance_account.NewFinanceAccountServiceCheckJstAccountBankResult()
}

func changeAccountRuleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceChangeAccountRuleArgs)
	realResult := result.(*finance_account.FinanceAccountServiceChangeAccountRuleResult)
	success, err := handler.(finance_account.FinanceAccountService).ChangeAccountRule(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceChangeAccountRuleArgs() interface{} {
	return finance_account.NewFinanceAccountServiceChangeAccountRuleArgs()
}

func newFinanceAccountServiceChangeAccountRuleResult() interface{} {
	return finance_account.NewFinanceAccountServiceChangeAccountRuleResult()
}

func fetchPlatformURLHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceFetchPlatformURLArgs)
	realResult := result.(*finance_account.FinanceAccountServiceFetchPlatformURLResult)
	success, err := handler.(finance_account.FinanceAccountService).FetchPlatformURL(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceFetchPlatformURLArgs() interface{} {
	return finance_account.NewFinanceAccountServiceFetchPlatformURLArgs()
}

func newFinanceAccountServiceFetchPlatformURLResult() interface{} {
	return finance_account.NewFinanceAccountServiceFetchPlatformURLResult()
}

func fetchAccountCenterURLHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceFetchAccountCenterURLArgs)
	realResult := result.(*finance_account.FinanceAccountServiceFetchAccountCenterURLResult)
	success, err := handler.(finance_account.FinanceAccountService).FetchAccountCenterURL(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceFetchAccountCenterURLArgs() interface{} {
	return finance_account.NewFinanceAccountServiceFetchAccountCenterURLArgs()
}

func newFinanceAccountServiceFetchAccountCenterURLResult() interface{} {
	return finance_account.NewFinanceAccountServiceFetchAccountCenterURLResult()
}

func getFinanceAccountTokenInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceGetFinanceAccountTokenInfoArgs)
	realResult := result.(*finance_account.FinanceAccountServiceGetFinanceAccountTokenInfoResult)
	success, err := handler.(finance_account.FinanceAccountService).GetFinanceAccountTokenInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceGetFinanceAccountTokenInfoArgs() interface{} {
	return finance_account.NewFinanceAccountServiceGetFinanceAccountTokenInfoArgs()
}

func newFinanceAccountServiceGetFinanceAccountTokenInfoResult() interface{} {
	return finance_account.NewFinanceAccountServiceGetFinanceAccountTokenInfoResult()
}

func validateApplyResultInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceValidateApplyResultInfoArgs)
	realResult := result.(*finance_account.FinanceAccountServiceValidateApplyResultInfoResult)
	success, err := handler.(finance_account.FinanceAccountService).ValidateApplyResultInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceValidateApplyResultInfoArgs() interface{} {
	return finance_account.NewFinanceAccountServiceValidateApplyResultInfoArgs()
}

func newFinanceAccountServiceValidateApplyResultInfoResult() interface{} {
	return finance_account.NewFinanceAccountServiceValidateApplyResultInfoResult()
}

func mappingFinanceAccountHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceMappingFinanceAccountArgs)
	realResult := result.(*finance_account.FinanceAccountServiceMappingFinanceAccountResult)
	success, err := handler.(finance_account.FinanceAccountService).MappingFinanceAccount(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceMappingFinanceAccountArgs() interface{} {
	return finance_account.NewFinanceAccountServiceMappingFinanceAccountArgs()
}

func newFinanceAccountServiceMappingFinanceAccountResult() interface{} {
	return finance_account.NewFinanceAccountServiceMappingFinanceAccountResult()
}

func setWithdrawModeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceSetWithdrawModeArgs)
	realResult := result.(*finance_account.FinanceAccountServiceSetWithdrawModeResult)
	success, err := handler.(finance_account.FinanceAccountService).SetWithdrawMode(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceSetWithdrawModeArgs() interface{} {
	return finance_account.NewFinanceAccountServiceSetWithdrawModeArgs()
}

func newFinanceAccountServiceSetWithdrawModeResult() interface{} {
	return finance_account.NewFinanceAccountServiceSetWithdrawModeResult()
}

func getWithdrawModeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceGetWithdrawModeArgs)
	realResult := result.(*finance_account.FinanceAccountServiceGetWithdrawModeResult)
	success, err := handler.(finance_account.FinanceAccountService).GetWithdrawMode(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceGetWithdrawModeArgs() interface{} {
	return finance_account.NewFinanceAccountServiceGetWithdrawModeArgs()
}

func newFinanceAccountServiceGetWithdrawModeResult() interface{} {
	return finance_account.NewFinanceAccountServiceGetWithdrawModeResult()
}

func queryAccountBalanceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceQueryAccountBalanceArgs)
	realResult := result.(*finance_account.FinanceAccountServiceQueryAccountBalanceResult)
	success, err := handler.(finance_account.FinanceAccountService).QueryAccountBalance(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceQueryAccountBalanceArgs() interface{} {
	return finance_account.NewFinanceAccountServiceQueryAccountBalanceArgs()
}

func newFinanceAccountServiceQueryAccountBalanceResult() interface{} {
	return finance_account.NewFinanceAccountServiceQueryAccountBalanceResult()
}

func withdrawAllBalanceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceWithdrawAllBalanceArgs)
	realResult := result.(*finance_account.FinanceAccountServiceWithdrawAllBalanceResult)
	success, err := handler.(finance_account.FinanceAccountService).WithdrawAllBalance(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceWithdrawAllBalanceArgs() interface{} {
	return finance_account.NewFinanceAccountServiceWithdrawAllBalanceArgs()
}

func newFinanceAccountServiceWithdrawAllBalanceResult() interface{} {
	return finance_account.NewFinanceAccountServiceWithdrawAllBalanceResult()
}

func withdrawNotifyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceWithdrawNotifyArgs)
	realResult := result.(*finance_account.FinanceAccountServiceWithdrawNotifyResult)
	success, err := handler.(finance_account.FinanceAccountService).WithdrawNotify(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceWithdrawNotifyArgs() interface{} {
	return finance_account.NewFinanceAccountServiceWithdrawNotifyArgs()
}

func newFinanceAccountServiceWithdrawNotifyResult() interface{} {
	return finance_account.NewFinanceAccountServiceWithdrawNotifyResult()
}

func autoWithdrawAllBalanceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceAutoWithdrawAllBalanceArgs)
	realResult := result.(*finance_account.FinanceAccountServiceAutoWithdrawAllBalanceResult)
	success, err := handler.(finance_account.FinanceAccountService).AutoWithdrawAllBalance(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceAutoWithdrawAllBalanceArgs() interface{} {
	return finance_account.NewFinanceAccountServiceAutoWithdrawAllBalanceArgs()
}

func newFinanceAccountServiceAutoWithdrawAllBalanceResult() interface{} {
	return finance_account.NewFinanceAccountServiceAutoWithdrawAllBalanceResult()
}

func queryFweSubMerchantListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*finance_account.FinanceAccountServiceQueryFweSubMerchantListArgs)
	realResult := result.(*finance_account.FinanceAccountServiceQueryFweSubMerchantListResult)
	success, err := handler.(finance_account.FinanceAccountService).QueryFweSubMerchantList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFinanceAccountServiceQueryFweSubMerchantListArgs() interface{} {
	return finance_account.NewFinanceAccountServiceQueryFweSubMerchantListArgs()
}

func newFinanceAccountServiceQueryFweSubMerchantListResult() interface{} {
	return finance_account.NewFinanceAccountServiceQueryFweSubMerchantListResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) CreateFweSubMerchant(ctx context.Context, req *finance_account.CreateFweSubMerchantReq) (r *finance_account.CreateFweSubMerchantResp, err error) {
	var _args finance_account.FinanceAccountServiceCreateFweSubMerchantArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceCreateFweSubMerchantResult
	if err = p.c.Call(ctx, "CreateFweSubMerchant", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ModifyFweSubMerchant(ctx context.Context, req *finance_account.ModifyFweSubMerchantReq) (r *finance_account.ModifyFweSubMerchantResp, err error) {
	var _args finance_account.FinanceAccountServiceModifyFweSubMerchantArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceModifyFweSubMerchantResult
	if err = p.c.Call(ctx, "ModifyFweSubMerchant", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryFweSubMerchantInfo(ctx context.Context, req *finance_account.QueryFweSubMerchantInfoReq) (r *finance_account.QueryFweSubMerchantInfoResp, err error) {
	var _args finance_account.FinanceAccountServiceQueryFweSubMerchantInfoArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceQueryFweSubMerchantInfoResult
	if err = p.c.Call(ctx, "QueryFweSubMerchantInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QuerySubMerchantInfo(ctx context.Context, req *finance_account.QuerySubMerchantInfoReq) (r *finance_account.QuerySubMerchantInfoResp, err error) {
	var _args finance_account.FinanceAccountServiceQuerySubMerchantInfoArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceQuerySubMerchantInfoResult
	if err = p.c.Call(ctx, "QuerySubMerchantInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetFweSubMerchantState(ctx context.Context, req *finance_account.GetFweSubMerchantStateReq) (r *finance_account.GetFweSubMerchantStateResp, err error) {
	var _args finance_account.FinanceAccountServiceGetFweSubMerchantStateArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceGetFweSubMerchantStateResult
	if err = p.c.Call(ctx, "GetFweSubMerchantState", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetFweSubMerchantInfo(ctx context.Context, req *finance_account.GetFweSubMerchantInfoReq) (r *finance_account.GetFweSubMerchantInfoResp, err error) {
	var _args finance_account.FinanceAccountServiceGetFweSubMerchantInfoArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceGetFweSubMerchantInfoResult
	if err = p.c.Call(ctx, "GetFweSubMerchantInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetFweSubMerchantInfo(ctx context.Context, req *finance_account.MGetFweSubMerchantInfoReq) (r *finance_account.MGetFweSubMerchantInfoResp, err error) {
	var _args finance_account.FinanceAccountServiceMGetFweSubMerchantInfoArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceMGetFweSubMerchantInfoResult
	if err = p.c.Call(ctx, "MGetFweSubMerchantInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifySolutionSubMerchant(ctx context.Context, req *finance_account.UnionNotifyReq) (r *finance_account.UnionNotifyResp, err error) {
	var _args finance_account.FinanceAccountServiceUnionNotifySolutionSubMerchantArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceUnionNotifySolutionSubMerchantResult
	if err = p.c.Call(ctx, "UnionNotifySolutionSubMerchant", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyFweSubMerchant(ctx context.Context, req *finance_account.UnionNotifyReq) (r *finance_account.UnionNotifyResp, err error) {
	var _args finance_account.FinanceAccountServiceUnionNotifyFweSubMerchantArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceUnionNotifyFweSubMerchantResult
	if err = p.c.Call(ctx, "UnionNotifyFweSubMerchant", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CheckJstAccountBank(ctx context.Context, req *finance_account.CheckJstAccountBankReq) (r *finance_account.CheckJstAccountBankRsp, err error) {
	var _args finance_account.FinanceAccountServiceCheckJstAccountBankArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceCheckJstAccountBankResult
	if err = p.c.Call(ctx, "CheckJstAccountBank", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ChangeAccountRule(ctx context.Context, req *finance_account.ChangeAccountRuleReq) (r *finance_account.ChangeAccountRuleResp, err error) {
	var _args finance_account.FinanceAccountServiceChangeAccountRuleArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceChangeAccountRuleResult
	if err = p.c.Call(ctx, "ChangeAccountRule", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FetchPlatformURL(ctx context.Context, req *finance_account.FetchPlatformURLReq) (r *finance_account.FetchPlatformURLResp, err error) {
	var _args finance_account.FinanceAccountServiceFetchPlatformURLArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceFetchPlatformURLResult
	if err = p.c.Call(ctx, "FetchPlatformUrl", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FetchAccountCenterURL(ctx context.Context, req *finance_account.FetchAccountCenterURLReq) (r *finance_account.FetchAccountCenterURLResp, err error) {
	var _args finance_account.FinanceAccountServiceFetchAccountCenterURLArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceFetchAccountCenterURLResult
	if err = p.c.Call(ctx, "FetchAccountCenterUrl", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetFinanceAccountTokenInfo(ctx context.Context, req *finance_account.GetFinanceAccountTokenInfoReq) (r *finance_account.GetFinanceAccountTokenInfoResp, err error) {
	var _args finance_account.FinanceAccountServiceGetFinanceAccountTokenInfoArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceGetFinanceAccountTokenInfoResult
	if err = p.c.Call(ctx, "GetFinanceAccountTokenInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ValidateApplyResultInfo(ctx context.Context, req *finance_account.ValidateApplyResultInfoReq) (r *finance_account.ValidateApplyResultInfoResp, err error) {
	var _args finance_account.FinanceAccountServiceValidateApplyResultInfoArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceValidateApplyResultInfoResult
	if err = p.c.Call(ctx, "ValidateApplyResultInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MappingFinanceAccount(ctx context.Context, req *finance_account.MappingFinanceAccountReq) (r *finance_account.MappingFinanceAccountResp, err error) {
	var _args finance_account.FinanceAccountServiceMappingFinanceAccountArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceMappingFinanceAccountResult
	if err = p.c.Call(ctx, "MappingFinanceAccount", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SetWithdrawMode(ctx context.Context, req *finance_account.SetWithdrawModeReq) (r *finance_account.SetWithdrawModeResp, err error) {
	var _args finance_account.FinanceAccountServiceSetWithdrawModeArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceSetWithdrawModeResult
	if err = p.c.Call(ctx, "SetWithdrawMode", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetWithdrawMode(ctx context.Context, req *finance_account.GetWithdrawModeReq) (r *finance_account.GetWithdrawModeResp, err error) {
	var _args finance_account.FinanceAccountServiceGetWithdrawModeArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceGetWithdrawModeResult
	if err = p.c.Call(ctx, "GetWithdrawMode", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryAccountBalance(ctx context.Context, req *finance_account.QueryAccountBalanceReq) (r *finance_account.QueryAccountBalanceResp, err error) {
	var _args finance_account.FinanceAccountServiceQueryAccountBalanceArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceQueryAccountBalanceResult
	if err = p.c.Call(ctx, "QueryAccountBalance", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) WithdrawAllBalance(ctx context.Context, req *finance_account.WithdrawAllBalanceReq) (r *finance_account.WithdrawAllBalanceResp, err error) {
	var _args finance_account.FinanceAccountServiceWithdrawAllBalanceArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceWithdrawAllBalanceResult
	if err = p.c.Call(ctx, "WithdrawAllBalance", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) WithdrawNotify(ctx context.Context, req *finance_account.WithdrawNotifyReq) (r *finance_account.WithdrawNotifyResp, err error) {
	var _args finance_account.FinanceAccountServiceWithdrawNotifyArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceWithdrawNotifyResult
	if err = p.c.Call(ctx, "WithdrawNotify", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AutoWithdrawAllBalance(ctx context.Context, req *finance_account.AutoWithdrawAllBalanceReq) (r *finance_account.AutoWithdrawAllBalanceResp, err error) {
	var _args finance_account.FinanceAccountServiceAutoWithdrawAllBalanceArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceAutoWithdrawAllBalanceResult
	if err = p.c.Call(ctx, "AutoWithdrawAllBalance", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryFweSubMerchantList(ctx context.Context, req *finance_account.QueryFweSubMerchantListReq) (r *finance_account.QueryFweSubMerchantListResp, err error) {
	var _args finance_account.FinanceAccountServiceQueryFweSubMerchantListArgs
	_args.Req = req
	var _result finance_account.FinanceAccountServiceQueryFweSubMerchantListResult
	if err = p.c.Call(ctx, "QueryFweSubMerchantList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
