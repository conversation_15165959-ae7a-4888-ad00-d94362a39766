// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package safe

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
)

type OrderCategory int64

const (
	OrderCategory_BizOrder   OrderCategory = 1
	OrderCategory_TradeOrder OrderCategory = 2
)

func (p OrderCategory) String() string {
	switch p {
	case OrderCategory_BizOrder:
		return "BizOrder"
	case OrderCategory_TradeOrder:
		return "TradeOrder"
	}
	return "<UNSET>"
}

func OrderCategoryFromString(s string) (OrderCategory, error) {
	switch s {
	case "BizOrder":
		return OrderCategory_BizOrder, nil
	case "TradeOrder":
		return OrderCategory_TradeOrder, nil
	}
	return OrderCategory(0), fmt.Errorf("not a valid OrderCategory string")
}

func OrderCategoryPtr(v OrderCategory) *OrderCategory { return &v }
func (p *OrderCategory) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OrderCategory(result.Int64)
	return
}

func (p *OrderCategory) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ExecStatus int64

const (
	ExecStatus_Same  ExecStatus = 0
	ExecStatus_Error ExecStatus = -1
	ExecStatus_Diff  ExecStatus = -2
)

func (p ExecStatus) String() string {
	switch p {
	case ExecStatus_Same:
		return "Same"
	case ExecStatus_Error:
		return "Error"
	case ExecStatus_Diff:
		return "Diff"
	}
	return "<UNSET>"
}

func ExecStatusFromString(s string) (ExecStatus, error) {
	switch s {
	case "Same":
		return ExecStatus_Same, nil
	case "Error":
		return ExecStatus_Error, nil
	case "Diff":
		return ExecStatus_Diff, nil
	}
	return ExecStatus(0), fmt.Errorf("not a valid ExecStatus string")
}

func ExecStatusPtr(v ExecStatus) *ExecStatus { return &v }
func (p *ExecStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ExecStatus(result.Int64)
	return
}

func (p *ExecStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type BlockStatus int64

const (
	BlockStatus_PASS          BlockStatus = 1
	BlockStatus_BLOCK         BlockStatus = 2
	BlockStatus_BLOCK_BY_FUSE BlockStatus = 3
)

func (p BlockStatus) String() string {
	switch p {
	case BlockStatus_PASS:
		return "PASS"
	case BlockStatus_BLOCK:
		return "BLOCK"
	case BlockStatus_BLOCK_BY_FUSE:
		return "BLOCK_BY_FUSE"
	}
	return "<UNSET>"
}

func BlockStatusFromString(s string) (BlockStatus, error) {
	switch s {
	case "PASS":
		return BlockStatus_PASS, nil
	case "BLOCK":
		return BlockStatus_BLOCK, nil
	case "BLOCK_BY_FUSE":
		return BlockStatus_BLOCK_BY_FUSE, nil
	}
	return BlockStatus(0), fmt.Errorf("not a valid BlockStatus string")
}

func BlockStatusPtr(v BlockStatus) *BlockStatus { return &v }
func (p *BlockStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = BlockStatus(result.Int64)
	return
}

func (p *BlockStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type UpsertType int64

const (
	UpsertType_Update UpsertType = 1
	UpsertType_Insert UpsertType = 2
)

func (p UpsertType) String() string {
	switch p {
	case UpsertType_Update:
		return "Update"
	case UpsertType_Insert:
		return "Insert"
	}
	return "<UNSET>"
}

func UpsertTypeFromString(s string) (UpsertType, error) {
	switch s {
	case "Update":
		return UpsertType_Update, nil
	case "Insert":
		return UpsertType_Insert, nil
	}
	return UpsertType(0), fmt.Errorf("not a valid UpsertType string")
}

func UpsertTypePtr(v UpsertType) *UpsertType { return &v }
func (p *UpsertType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = UpsertType(result.Int64)
	return
}

func (p *UpsertType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ScheduleType int64

const (
	ScheduleType_DeadOrder   ScheduleType = 1
	ScheduleType_TimeoutOrer ScheduleType = 2
)

func (p ScheduleType) String() string {
	switch p {
	case ScheduleType_DeadOrder:
		return "DeadOrder"
	case ScheduleType_TimeoutOrer:
		return "TimeoutOrer"
	}
	return "<UNSET>"
}

func ScheduleTypeFromString(s string) (ScheduleType, error) {
	switch s {
	case "DeadOrder":
		return ScheduleType_DeadOrder, nil
	case "TimeoutOrer":
		return ScheduleType_TimeoutOrer, nil
	}
	return ScheduleType(0), fmt.Errorf("not a valid ScheduleType string")
}

func ScheduleTypePtr(v ScheduleType) *ScheduleType { return &v }
func (p *ScheduleType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ScheduleType(result.Int64)
	return
}

func (p *ScheduleType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type OperateType int64

const (
	OperateType_Insert OperateType = 1
	OperateType_Update OperateType = 2
)

func (p OperateType) String() string {
	switch p {
	case OperateType_Insert:
		return "Insert"
	case OperateType_Update:
		return "Update"
	}
	return "<UNSET>"
}

func OperateTypeFromString(s string) (OperateType, error) {
	switch s {
	case "Insert":
		return OperateType_Insert, nil
	case "Update":
		return OperateType_Update, nil
	}
	return OperateType(0), fmt.Errorf("not a valid OperateType string")
}

func OperateTypePtr(v OperateType) *OperateType { return &v }
func (p *OperateType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OperateType(result.Int64)
	return
}

func (p *OperateType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type MGetRuleReq struct {
	RuleNames []string   `thrift:"rule_names,1" frugal:"1,default,list<string>" json:"rule_names"`
	Base      *base.Base `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewMGetRuleReq() *MGetRuleReq {
	return &MGetRuleReq{}
}

func (p *MGetRuleReq) InitDefault() {
}

func (p *MGetRuleReq) GetRuleNames() (v []string) {
	return p.RuleNames
}

var MGetRuleReq_Base_DEFAULT *base.Base

func (p *MGetRuleReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return MGetRuleReq_Base_DEFAULT
	}
	return p.Base
}
func (p *MGetRuleReq) SetRuleNames(val []string) {
	p.RuleNames = val
}
func (p *MGetRuleReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_MGetRuleReq = map[int16]string{
	1:   "rule_names",
	255: "Base",
}

func (p *MGetRuleReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *MGetRuleReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MGetRuleReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RuleNames = _field
	return nil
}
func (p *MGetRuleReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *MGetRuleReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("MGetRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MGetRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_names", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.RuleNames)); err != nil {
		return err
	}
	for _, v := range p.RuleNames {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MGetRuleReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *MGetRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MGetRuleReq(%+v)", *p)

}

type RuleInfo struct {
	RuleName      string        `thrift:"rule_name,1" frugal:"1,default,string" json:"rule_name"`
	RuleDesc      string        `thrift:"rule_desc,2" frugal:"2,default,string" json:"rule_desc"`
	RuleFormula   string        `thrift:"rule_formula,3" frugal:"3,default,string" json:"rule_formula"`
	OrderCategory OrderCategory `thrift:"order_category,5" frugal:"5,default,OrderCategory" json:"order_category"`
}

func NewRuleInfo() *RuleInfo {
	return &RuleInfo{}
}

func (p *RuleInfo) InitDefault() {
}

func (p *RuleInfo) GetRuleName() (v string) {
	return p.RuleName
}

func (p *RuleInfo) GetRuleDesc() (v string) {
	return p.RuleDesc
}

func (p *RuleInfo) GetRuleFormula() (v string) {
	return p.RuleFormula
}

func (p *RuleInfo) GetOrderCategory() (v OrderCategory) {
	return p.OrderCategory
}
func (p *RuleInfo) SetRuleName(val string) {
	p.RuleName = val
}
func (p *RuleInfo) SetRuleDesc(val string) {
	p.RuleDesc = val
}
func (p *RuleInfo) SetRuleFormula(val string) {
	p.RuleFormula = val
}
func (p *RuleInfo) SetOrderCategory(val OrderCategory) {
	p.OrderCategory = val
}

var fieldIDToName_RuleInfo = map[int16]string{
	1: "rule_name",
	2: "rule_desc",
	3: "rule_formula",
	5: "order_category",
}

func (p *RuleInfo) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RuleInfo")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RuleInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RuleInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleName = _field
	return nil
}
func (p *RuleInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleDesc = _field
	return nil
}
func (p *RuleInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleFormula = _field
	return nil
}
func (p *RuleInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field OrderCategory
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = OrderCategory(v)
	}
	p.OrderCategory = _field
	return nil
}

func (p *RuleInfo) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RuleInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("RuleInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RuleInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RuleInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_desc", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RuleInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_formula", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleFormula); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *RuleInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_category", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.OrderCategory)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *RuleInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RuleInfo(%+v)", *p)

}

type MGetRuleResp struct {
	RuleMap  map[string]*RuleInfo `thrift:"rule_map,1" frugal:"1,default,map<string:RuleInfo>" json:"rule_map"`
	BaseResp *base.BaseResp       `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewMGetRuleResp() *MGetRuleResp {
	return &MGetRuleResp{}
}

func (p *MGetRuleResp) InitDefault() {
}

func (p *MGetRuleResp) GetRuleMap() (v map[string]*RuleInfo) {
	return p.RuleMap
}

var MGetRuleResp_BaseResp_DEFAULT *base.BaseResp

func (p *MGetRuleResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return MGetRuleResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *MGetRuleResp) SetRuleMap(val map[string]*RuleInfo) {
	p.RuleMap = val
}
func (p *MGetRuleResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_MGetRuleResp = map[int16]string{
	1:   "rule_map",
	255: "BaseResp",
}

func (p *MGetRuleResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *MGetRuleResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetRuleResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MGetRuleResp) ReadField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]*RuleInfo, size)
	values := make([]RuleInfo, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.RuleMap = _field
	return nil
}
func (p *MGetRuleResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *MGetRuleResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetRuleResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("MGetRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MGetRuleResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_map", thrift.MAP, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRUCT, len(p.RuleMap)); err != nil {
		return err
	}
	for k, v := range p.RuleMap {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MGetRuleResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *MGetRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MGetRuleResp(%+v)", *p)

}

type ExecRuleByBinlogReq struct {
	RuleName      string     `thrift:"rule_name,1" frugal:"1,default,string" json:"rule_name"`
	SourceMessage string     `thrift:"source_message,2" frugal:"2,default,string" json:"source_message"`
	Base          *base.Base `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewExecRuleByBinlogReq() *ExecRuleByBinlogReq {
	return &ExecRuleByBinlogReq{}
}

func (p *ExecRuleByBinlogReq) InitDefault() {
}

func (p *ExecRuleByBinlogReq) GetRuleName() (v string) {
	return p.RuleName
}

func (p *ExecRuleByBinlogReq) GetSourceMessage() (v string) {
	return p.SourceMessage
}

var ExecRuleByBinlogReq_Base_DEFAULT *base.Base

func (p *ExecRuleByBinlogReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ExecRuleByBinlogReq_Base_DEFAULT
	}
	return p.Base
}
func (p *ExecRuleByBinlogReq) SetRuleName(val string) {
	p.RuleName = val
}
func (p *ExecRuleByBinlogReq) SetSourceMessage(val string) {
	p.SourceMessage = val
}
func (p *ExecRuleByBinlogReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ExecRuleByBinlogReq = map[int16]string{
	1:   "rule_name",
	2:   "source_message",
	255: "Base",
}

func (p *ExecRuleByBinlogReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *ExecRuleByBinlogReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ExecRuleByBinlogReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecRuleByBinlogReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecRuleByBinlogReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleName = _field
	return nil
}
func (p *ExecRuleByBinlogReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SourceMessage = _field
	return nil
}
func (p *ExecRuleByBinlogReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ExecRuleByBinlogReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ExecRuleByBinlogReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecRuleByBinlogReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecRuleByBinlogReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ExecRuleByBinlogReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("source_message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SourceMessage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ExecRuleByBinlogReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ExecRuleByBinlogReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecRuleByBinlogReq(%+v)", *p)

}

type ExecRuleResp struct {
	ExecStatus ExecStatus     `thrift:"exec_status,1" frugal:"1,default,ExecStatus" json:"exec_status"`
	ExecRes    string         `thrift:"exec_res,2" frugal:"2,default,string" json:"exec_res"`
	BaseResp   *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewExecRuleResp() *ExecRuleResp {
	return &ExecRuleResp{}
}

func (p *ExecRuleResp) InitDefault() {
}

func (p *ExecRuleResp) GetExecStatus() (v ExecStatus) {
	return p.ExecStatus
}

func (p *ExecRuleResp) GetExecRes() (v string) {
	return p.ExecRes
}

var ExecRuleResp_BaseResp_DEFAULT *base.BaseResp

func (p *ExecRuleResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ExecRuleResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *ExecRuleResp) SetExecStatus(val ExecStatus) {
	p.ExecStatus = val
}
func (p *ExecRuleResp) SetExecRes(val string) {
	p.ExecRes = val
}
func (p *ExecRuleResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_ExecRuleResp = map[int16]string{
	1:   "exec_status",
	2:   "exec_res",
	255: "BaseResp",
}

func (p *ExecRuleResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ExecRuleResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ExecRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecRuleResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecRuleResp) ReadField1(iprot thrift.TProtocol) error {

	var _field ExecStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ExecStatus(v)
	}
	p.ExecStatus = _field
	return nil
}
func (p *ExecRuleResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecRes = _field
	return nil
}
func (p *ExecRuleResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ExecRuleResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ExecRuleResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecRuleResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("exec_status", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ExecStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ExecRuleResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("exec_res", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecRes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ExecRuleResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ExecRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecRuleResp(%+v)", *p)

}

type MultiStreamCheckReq struct {
	RuleName    string            `thrift:"rule_name,1" frugal:"1,default,string" json:"rule_name"`
	CheckValues map[string]string `thrift:"check_values,2" frugal:"2,default,map<string:string>" json:"check_values"`
	Base        *base.Base        `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewMultiStreamCheckReq() *MultiStreamCheckReq {
	return &MultiStreamCheckReq{}
}

func (p *MultiStreamCheckReq) InitDefault() {
}

func (p *MultiStreamCheckReq) GetRuleName() (v string) {
	return p.RuleName
}

func (p *MultiStreamCheckReq) GetCheckValues() (v map[string]string) {
	return p.CheckValues
}

var MultiStreamCheckReq_Base_DEFAULT *base.Base

func (p *MultiStreamCheckReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return MultiStreamCheckReq_Base_DEFAULT
	}
	return p.Base
}
func (p *MultiStreamCheckReq) SetRuleName(val string) {
	p.RuleName = val
}
func (p *MultiStreamCheckReq) SetCheckValues(val map[string]string) {
	p.CheckValues = val
}
func (p *MultiStreamCheckReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_MultiStreamCheckReq = map[int16]string{
	1:   "rule_name",
	2:   "check_values",
	255: "Base",
}

func (p *MultiStreamCheckReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *MultiStreamCheckReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MultiStreamCheckReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MultiStreamCheckReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MultiStreamCheckReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleName = _field
	return nil
}
func (p *MultiStreamCheckReq) ReadField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.CheckValues = _field
	return nil
}
func (p *MultiStreamCheckReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *MultiStreamCheckReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MultiStreamCheckReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("MultiStreamCheckReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MultiStreamCheckReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MultiStreamCheckReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("check_values", thrift.MAP, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.CheckValues)); err != nil {
		return err
	}
	for k, v := range p.CheckValues {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *MultiStreamCheckReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *MultiStreamCheckReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MultiStreamCheckReq(%+v)", *p)

}

type MultiStreamCheckResp struct {
	ExecStatus ExecStatus     `thrift:"exec_status,1" frugal:"1,default,ExecStatus" json:"exec_status"`
	ExecRes    string         `thrift:"exec_res,2" frugal:"2,default,string" json:"exec_res"`
	BaseResp   *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewMultiStreamCheckResp() *MultiStreamCheckResp {
	return &MultiStreamCheckResp{}
}

func (p *MultiStreamCheckResp) InitDefault() {
}

func (p *MultiStreamCheckResp) GetExecStatus() (v ExecStatus) {
	return p.ExecStatus
}

func (p *MultiStreamCheckResp) GetExecRes() (v string) {
	return p.ExecRes
}

var MultiStreamCheckResp_BaseResp_DEFAULT *base.BaseResp

func (p *MultiStreamCheckResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return MultiStreamCheckResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *MultiStreamCheckResp) SetExecStatus(val ExecStatus) {
	p.ExecStatus = val
}
func (p *MultiStreamCheckResp) SetExecRes(val string) {
	p.ExecRes = val
}
func (p *MultiStreamCheckResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_MultiStreamCheckResp = map[int16]string{
	1:   "exec_status",
	2:   "exec_res",
	255: "BaseResp",
}

func (p *MultiStreamCheckResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *MultiStreamCheckResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MultiStreamCheckResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MultiStreamCheckResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MultiStreamCheckResp) ReadField1(iprot thrift.TProtocol) error {

	var _field ExecStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ExecStatus(v)
	}
	p.ExecStatus = _field
	return nil
}
func (p *MultiStreamCheckResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecRes = _field
	return nil
}
func (p *MultiStreamCheckResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *MultiStreamCheckResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MultiStreamCheckResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("MultiStreamCheckResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MultiStreamCheckResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("exec_status", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ExecStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MultiStreamCheckResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("exec_res", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecRes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *MultiStreamCheckResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *MultiStreamCheckResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MultiStreamCheckResp(%+v)", *p)

}

type InjectData struct {
	TableName  string     `thrift:"table_name,1" frugal:"1,default,string" json:"table_name"`
	ModelData  string     `thrift:"model_data,2" frugal:"2,default,string" json:"model_data"`
	UpsertType UpsertType `thrift:"upsert_type,3" frugal:"3,default,UpsertType" json:"upsert_type"`
}

func NewInjectData() *InjectData {
	return &InjectData{}
}

func (p *InjectData) InitDefault() {
}

func (p *InjectData) GetTableName() (v string) {
	return p.TableName
}

func (p *InjectData) GetModelData() (v string) {
	return p.ModelData
}

func (p *InjectData) GetUpsertType() (v UpsertType) {
	return p.UpsertType
}
func (p *InjectData) SetTableName(val string) {
	p.TableName = val
}
func (p *InjectData) SetModelData(val string) {
	p.ModelData = val
}
func (p *InjectData) SetUpsertType(val UpsertType) {
	p.UpsertType = val
}

var fieldIDToName_InjectData = map[int16]string{
	1: "table_name",
	2: "model_data",
	3: "upsert_type",
}

func (p *InjectData) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("InjectData")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InjectData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InjectData) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *InjectData) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ModelData = _field
	return nil
}
func (p *InjectData) ReadField3(iprot thrift.TProtocol) error {

	var _field UpsertType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = UpsertType(v)
	}
	p.UpsertType = _field
	return nil
}

func (p *InjectData) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("InjectData")

	var fieldId int16
	if err = oprot.WriteStructBegin("InjectData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InjectData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("table_name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *InjectData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("model_data", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ModelData); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *InjectData) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("upsert_type", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.UpsertType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InjectData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InjectData(%+v)", *p)

}

type AdviseReq struct {
	RuleNameList   []string    `thrift:"rule_name_list,1" frugal:"1,default,list<string>" json:"rule_name_list"`
	AdviseData     *InjectData `thrift:"advise_data,2" frugal:"2,default,InjectData" json:"advise_data"`
	OrderID        string      `thrift:"order_id,4" frugal:"4,default,string" json:"order_id"`
	PaymentOrderNo string      `thrift:"payment_order_no,5" frugal:"5,default,string" json:"payment_order_no"`
	Base           *base.Base  `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewAdviseReq() *AdviseReq {
	return &AdviseReq{}
}

func (p *AdviseReq) InitDefault() {
}

func (p *AdviseReq) GetRuleNameList() (v []string) {
	return p.RuleNameList
}

var AdviseReq_AdviseData_DEFAULT *InjectData

func (p *AdviseReq) GetAdviseData() (v *InjectData) {
	if !p.IsSetAdviseData() {
		return AdviseReq_AdviseData_DEFAULT
	}
	return p.AdviseData
}

func (p *AdviseReq) GetOrderID() (v string) {
	return p.OrderID
}

func (p *AdviseReq) GetPaymentOrderNo() (v string) {
	return p.PaymentOrderNo
}

var AdviseReq_Base_DEFAULT *base.Base

func (p *AdviseReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return AdviseReq_Base_DEFAULT
	}
	return p.Base
}
func (p *AdviseReq) SetRuleNameList(val []string) {
	p.RuleNameList = val
}
func (p *AdviseReq) SetAdviseData(val *InjectData) {
	p.AdviseData = val
}
func (p *AdviseReq) SetOrderID(val string) {
	p.OrderID = val
}
func (p *AdviseReq) SetPaymentOrderNo(val string) {
	p.PaymentOrderNo = val
}
func (p *AdviseReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_AdviseReq = map[int16]string{
	1:   "rule_name_list",
	2:   "advise_data",
	4:   "order_id",
	5:   "payment_order_no",
	255: "Base",
}

func (p *AdviseReq) IsSetAdviseData() bool {
	return p.AdviseData != nil
}

func (p *AdviseReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *AdviseReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AdviseReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AdviseReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AdviseReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RuleNameList = _field
	return nil
}
func (p *AdviseReq) ReadField2(iprot thrift.TProtocol) error {
	_field := NewInjectData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.AdviseData = _field
	return nil
}
func (p *AdviseReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *AdviseReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PaymentOrderNo = _field
	return nil
}
func (p *AdviseReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *AdviseReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AdviseReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AdviseReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AdviseReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_name_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.RuleNameList)); err != nil {
		return err
	}
	for _, v := range p.RuleNameList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AdviseReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("advise_data", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.AdviseData.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *AdviseReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *AdviseReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("payment_order_no", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PaymentOrderNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *AdviseReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *AdviseReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdviseReq(%+v)", *p)

}

type ExecResult_ struct {
	ExecStatus ExecStatus `thrift:"exec_status,1" frugal:"1,default,ExecStatus" json:"exec_status"`
	ExecRes    string     `thrift:"exec_res,2" frugal:"2,default,string" json:"exec_res"`
}

func NewExecResult_() *ExecResult_ {
	return &ExecResult_{}
}

func (p *ExecResult_) InitDefault() {
}

func (p *ExecResult_) GetExecStatus() (v ExecStatus) {
	return p.ExecStatus
}

func (p *ExecResult_) GetExecRes() (v string) {
	return p.ExecRes
}
func (p *ExecResult_) SetExecStatus(val ExecStatus) {
	p.ExecStatus = val
}
func (p *ExecResult_) SetExecRes(val string) {
	p.ExecRes = val
}

var fieldIDToName_ExecResult_ = map[int16]string{
	1: "exec_status",
	2: "exec_res",
}

func (p *ExecResult_) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ExecResult_")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ExecResult_) ReadField1(iprot thrift.TProtocol) error {

	var _field ExecStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ExecStatus(v)
	}
	p.ExecStatus = _field
	return nil
}
func (p *ExecResult_) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecRes = _field
	return nil
}

func (p *ExecResult_) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ExecResult_")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("exec_status", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ExecStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ExecResult_) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("exec_res", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecRes); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExecResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecResult_(%+v)", *p)

}

type AdviseResp struct {
	BlockStatus   BlockStatus             `thrift:"block_status,1" frugal:"1,default,BlockStatus" json:"block_status"`
	ExecStatusMap map[string]*ExecResult_ `thrift:"exec_status_map,2" frugal:"2,default,map<string:ExecResult_>" json:"exec_status_map"`
	BaseResp      *base.BaseResp          `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewAdviseResp() *AdviseResp {
	return &AdviseResp{}
}

func (p *AdviseResp) InitDefault() {
}

func (p *AdviseResp) GetBlockStatus() (v BlockStatus) {
	return p.BlockStatus
}

func (p *AdviseResp) GetExecStatusMap() (v map[string]*ExecResult_) {
	return p.ExecStatusMap
}

var AdviseResp_BaseResp_DEFAULT *base.BaseResp

func (p *AdviseResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return AdviseResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *AdviseResp) SetBlockStatus(val BlockStatus) {
	p.BlockStatus = val
}
func (p *AdviseResp) SetExecStatusMap(val map[string]*ExecResult_) {
	p.ExecStatusMap = val
}
func (p *AdviseResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_AdviseResp = map[int16]string{
	1:   "block_status",
	2:   "exec_status_map",
	255: "BaseResp",
}

func (p *AdviseResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *AdviseResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AdviseResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AdviseResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AdviseResp) ReadField1(iprot thrift.TProtocol) error {

	var _field BlockStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BlockStatus(v)
	}
	p.BlockStatus = _field
	return nil
}
func (p *AdviseResp) ReadField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]*ExecResult_, size)
	values := make([]ExecResult_, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ExecStatusMap = _field
	return nil
}
func (p *AdviseResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *AdviseResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AdviseResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("AdviseResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AdviseResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("block_status", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BlockStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AdviseResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("exec_status_map", thrift.MAP, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRUCT, len(p.ExecStatusMap)); err != nil {
		return err
	}
	for k, v := range p.ExecStatusMap {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *AdviseResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *AdviseResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdviseResp(%+v)", *p)

}

type ScheduleCallbackReq struct {
	ScheduleType ScheduleType `thrift:"schedule_type,1" frugal:"1,default,ScheduleType" json:"schedule_type"`
	Body         string       `thrift:"body,2" frugal:"2,default,string" json:"body"`
	Base         *base.Base   `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewScheduleCallbackReq() *ScheduleCallbackReq {
	return &ScheduleCallbackReq{}
}

func (p *ScheduleCallbackReq) InitDefault() {
}

func (p *ScheduleCallbackReq) GetScheduleType() (v ScheduleType) {
	return p.ScheduleType
}

func (p *ScheduleCallbackReq) GetBody() (v string) {
	return p.Body
}

var ScheduleCallbackReq_Base_DEFAULT *base.Base

func (p *ScheduleCallbackReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ScheduleCallbackReq_Base_DEFAULT
	}
	return p.Base
}
func (p *ScheduleCallbackReq) SetScheduleType(val ScheduleType) {
	p.ScheduleType = val
}
func (p *ScheduleCallbackReq) SetBody(val string) {
	p.Body = val
}
func (p *ScheduleCallbackReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ScheduleCallbackReq = map[int16]string{
	1:   "schedule_type",
	2:   "body",
	255: "Base",
}

func (p *ScheduleCallbackReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *ScheduleCallbackReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ScheduleCallbackReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ScheduleCallbackReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ScheduleCallbackReq) ReadField1(iprot thrift.TProtocol) error {

	var _field ScheduleType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ScheduleType(v)
	}
	p.ScheduleType = _field
	return nil
}
func (p *ScheduleCallbackReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Body = _field
	return nil
}
func (p *ScheduleCallbackReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ScheduleCallbackReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ScheduleCallbackReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ScheduleCallbackReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ScheduleCallbackReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("schedule_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ScheduleType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ScheduleCallbackReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("body", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Body); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ScheduleCallbackReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ScheduleCallbackReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ScheduleCallbackReq(%+v)", *p)

}

type ScheduleCallbackResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewScheduleCallbackResp() *ScheduleCallbackResp {
	return &ScheduleCallbackResp{}
}

func (p *ScheduleCallbackResp) InitDefault() {
}

var ScheduleCallbackResp_BaseResp_DEFAULT *base.BaseResp

func (p *ScheduleCallbackResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ScheduleCallbackResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *ScheduleCallbackResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_ScheduleCallbackResp = map[int16]string{
	255: "BaseResp",
}

func (p *ScheduleCallbackResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ScheduleCallbackResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ScheduleCallbackResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ScheduleCallbackResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ScheduleCallbackResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ScheduleCallbackResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ScheduleCallbackResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ScheduleCallbackResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ScheduleCallbackResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ScheduleCallbackResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ScheduleCallbackResp(%+v)", *p)

}

type OperateRuleReq struct {
	OperateType   OperateType   `thrift:"operate_type,1,required" frugal:"1,required,OperateType" json:"operate_type"`
	RuleName      string        `thrift:"rule_name,2,required" frugal:"2,required,string" json:"rule_name"`
	RuleDesc      string        `thrift:"rule_desc,3,required" frugal:"3,required,string" json:"rule_desc"`
	RuleFormula   string        `thrift:"rule_formula,4,required" frugal:"4,required,string" json:"rule_formula"`
	ListenTables  []string      `thrift:"listen_tables,6" frugal:"6,default,list<string>" json:"listen_tables"`
	OrderCategory OrderCategory `thrift:"order_category,7" frugal:"7,default,OrderCategory" json:"order_category"`
	BizScenes     []string      `thrift:"biz_scenes,8" frugal:"8,default,list<string>" json:"biz_scenes"`
	Base          *base.Base    `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewOperateRuleReq() *OperateRuleReq {
	return &OperateRuleReq{}
}

func (p *OperateRuleReq) InitDefault() {
}

func (p *OperateRuleReq) GetOperateType() (v OperateType) {
	return p.OperateType
}

func (p *OperateRuleReq) GetRuleName() (v string) {
	return p.RuleName
}

func (p *OperateRuleReq) GetRuleDesc() (v string) {
	return p.RuleDesc
}

func (p *OperateRuleReq) GetRuleFormula() (v string) {
	return p.RuleFormula
}

func (p *OperateRuleReq) GetListenTables() (v []string) {
	return p.ListenTables
}

func (p *OperateRuleReq) GetOrderCategory() (v OrderCategory) {
	return p.OrderCategory
}

func (p *OperateRuleReq) GetBizScenes() (v []string) {
	return p.BizScenes
}

var OperateRuleReq_Base_DEFAULT *base.Base

func (p *OperateRuleReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return OperateRuleReq_Base_DEFAULT
	}
	return p.Base
}
func (p *OperateRuleReq) SetOperateType(val OperateType) {
	p.OperateType = val
}
func (p *OperateRuleReq) SetRuleName(val string) {
	p.RuleName = val
}
func (p *OperateRuleReq) SetRuleDesc(val string) {
	p.RuleDesc = val
}
func (p *OperateRuleReq) SetRuleFormula(val string) {
	p.RuleFormula = val
}
func (p *OperateRuleReq) SetListenTables(val []string) {
	p.ListenTables = val
}
func (p *OperateRuleReq) SetOrderCategory(val OrderCategory) {
	p.OrderCategory = val
}
func (p *OperateRuleReq) SetBizScenes(val []string) {
	p.BizScenes = val
}
func (p *OperateRuleReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_OperateRuleReq = map[int16]string{
	1:   "operate_type",
	2:   "rule_name",
	3:   "rule_desc",
	4:   "rule_formula",
	6:   "listen_tables",
	7:   "order_category",
	8:   "biz_scenes",
	255: "Base",
}

func (p *OperateRuleReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *OperateRuleReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OperateRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOperateType bool = false
	var issetRuleName bool = false
	var issetRuleDesc bool = false
	var issetRuleFormula bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperateType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleDesc = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleFormula = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOperateType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRuleName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRuleDesc {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetRuleFormula {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OperateRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_OperateRuleReq[fieldId]))
}

func (p *OperateRuleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field OperateType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = OperateType(v)
	}
	p.OperateType = _field
	return nil
}
func (p *OperateRuleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleName = _field
	return nil
}
func (p *OperateRuleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleDesc = _field
	return nil
}
func (p *OperateRuleReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleFormula = _field
	return nil
}
func (p *OperateRuleReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ListenTables = _field
	return nil
}
func (p *OperateRuleReq) ReadField7(iprot thrift.TProtocol) error {

	var _field OrderCategory
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = OrderCategory(v)
	}
	p.OrderCategory = _field
	return nil
}
func (p *OperateRuleReq) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.BizScenes = _field
	return nil
}
func (p *OperateRuleReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *OperateRuleReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OperateRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("OperateRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OperateRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operate_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.OperateType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *OperateRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *OperateRuleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_desc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *OperateRuleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_formula", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleFormula); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *OperateRuleReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("listen_tables", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.ListenTables)); err != nil {
		return err
	}
	for _, v := range p.ListenTables {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *OperateRuleReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_category", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.OrderCategory)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *OperateRuleReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_scenes", thrift.LIST, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.BizScenes)); err != nil {
		return err
	}
	for _, v := range p.BizScenes {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *OperateRuleReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *OperateRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OperateRuleReq(%+v)", *p)

}

type OperateRuleResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewOperateRuleResp() *OperateRuleResp {
	return &OperateRuleResp{}
}

func (p *OperateRuleResp) InitDefault() {
}

var OperateRuleResp_BaseResp_DEFAULT *base.BaseResp

func (p *OperateRuleResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return OperateRuleResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *OperateRuleResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_OperateRuleResp = map[int16]string{
	255: "BaseResp",
}

func (p *OperateRuleResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *OperateRuleResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OperateRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OperateRuleResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OperateRuleResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *OperateRuleResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OperateRuleResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("OperateRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OperateRuleResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *OperateRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OperateRuleResp(%+v)", *p)

}

type CreateOrUpdateDrillReq struct {
	DrillNo       string        `thrift:"drill_no,1" frugal:"1,default,string" json:"drill_no"`
	RuleName      string        `thrift:"rule_name,2" frugal:"2,default,string" json:"rule_name"`
	DrillDataList []*InjectData `thrift:"drill_data_list,3" frugal:"3,default,list<InjectData>" json:"drill_data_list"`
	Base          *base.Base    `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewCreateOrUpdateDrillReq() *CreateOrUpdateDrillReq {
	return &CreateOrUpdateDrillReq{}
}

func (p *CreateOrUpdateDrillReq) InitDefault() {
}

func (p *CreateOrUpdateDrillReq) GetDrillNo() (v string) {
	return p.DrillNo
}

func (p *CreateOrUpdateDrillReq) GetRuleName() (v string) {
	return p.RuleName
}

func (p *CreateOrUpdateDrillReq) GetDrillDataList() (v []*InjectData) {
	return p.DrillDataList
}

var CreateOrUpdateDrillReq_Base_DEFAULT *base.Base

func (p *CreateOrUpdateDrillReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CreateOrUpdateDrillReq_Base_DEFAULT
	}
	return p.Base
}
func (p *CreateOrUpdateDrillReq) SetDrillNo(val string) {
	p.DrillNo = val
}
func (p *CreateOrUpdateDrillReq) SetRuleName(val string) {
	p.RuleName = val
}
func (p *CreateOrUpdateDrillReq) SetDrillDataList(val []*InjectData) {
	p.DrillDataList = val
}
func (p *CreateOrUpdateDrillReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CreateOrUpdateDrillReq = map[int16]string{
	1:   "drill_no",
	2:   "rule_name",
	3:   "drill_data_list",
	255: "Base",
}

func (p *CreateOrUpdateDrillReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *CreateOrUpdateDrillReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateOrUpdateDrillReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateOrUpdateDrillReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateOrUpdateDrillReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DrillNo = _field
	return nil
}
func (p *CreateOrUpdateDrillReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleName = _field
	return nil
}
func (p *CreateOrUpdateDrillReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InjectData, 0, size)
	values := make([]InjectData, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DrillDataList = _field
	return nil
}
func (p *CreateOrUpdateDrillReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CreateOrUpdateDrillReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateOrUpdateDrillReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateOrUpdateDrillReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateOrUpdateDrillReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("drill_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DrillNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateOrUpdateDrillReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateOrUpdateDrillReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("drill_data_list", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DrillDataList)); err != nil {
		return err
	}
	for _, v := range p.DrillDataList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CreateOrUpdateDrillReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateOrUpdateDrillReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateOrUpdateDrillReq(%+v)", *p)

}

type CreateOrUpdateDrillResp struct {
	DrillNo  string         `thrift:"drill_no,1" frugal:"1,default,string" json:"drill_no"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewCreateOrUpdateDrillResp() *CreateOrUpdateDrillResp {
	return &CreateOrUpdateDrillResp{}
}

func (p *CreateOrUpdateDrillResp) InitDefault() {
}

func (p *CreateOrUpdateDrillResp) GetDrillNo() (v string) {
	return p.DrillNo
}

var CreateOrUpdateDrillResp_BaseResp_DEFAULT *base.BaseResp

func (p *CreateOrUpdateDrillResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CreateOrUpdateDrillResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CreateOrUpdateDrillResp) SetDrillNo(val string) {
	p.DrillNo = val
}
func (p *CreateOrUpdateDrillResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CreateOrUpdateDrillResp = map[int16]string{
	1:   "drill_no",
	255: "BaseResp",
}

func (p *CreateOrUpdateDrillResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CreateOrUpdateDrillResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateOrUpdateDrillResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateOrUpdateDrillResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateOrUpdateDrillResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DrillNo = _field
	return nil
}
func (p *CreateOrUpdateDrillResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CreateOrUpdateDrillResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateOrUpdateDrillResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateOrUpdateDrillResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateOrUpdateDrillResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("drill_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DrillNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateOrUpdateDrillResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CreateOrUpdateDrillResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateOrUpdateDrillResp(%+v)", *p)

}

type DrillReq struct {
	DrillNo    string      `thrift:"drill_no,1" frugal:"1,default,string" json:"drill_no"`
	ListenData *InjectData `thrift:"listen_data,2" frugal:"2,default,InjectData" json:"listen_data"`
	Base       *base.Base  `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewDrillReq() *DrillReq {
	return &DrillReq{}
}

func (p *DrillReq) InitDefault() {
}

func (p *DrillReq) GetDrillNo() (v string) {
	return p.DrillNo
}

var DrillReq_ListenData_DEFAULT *InjectData

func (p *DrillReq) GetListenData() (v *InjectData) {
	if !p.IsSetListenData() {
		return DrillReq_ListenData_DEFAULT
	}
	return p.ListenData
}

var DrillReq_Base_DEFAULT *base.Base

func (p *DrillReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return DrillReq_Base_DEFAULT
	}
	return p.Base
}
func (p *DrillReq) SetDrillNo(val string) {
	p.DrillNo = val
}
func (p *DrillReq) SetListenData(val *InjectData) {
	p.ListenData = val
}
func (p *DrillReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_DrillReq = map[int16]string{
	1:   "drill_no",
	2:   "listen_data",
	255: "Base",
}

func (p *DrillReq) IsSetListenData() bool {
	return p.ListenData != nil
}

func (p *DrillReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *DrillReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DrillReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DrillReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DrillReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DrillNo = _field
	return nil
}
func (p *DrillReq) ReadField2(iprot thrift.TProtocol) error {
	_field := NewInjectData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ListenData = _field
	return nil
}
func (p *DrillReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *DrillReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DrillReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DrillReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DrillReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("drill_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DrillNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DrillReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("listen_data", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ListenData.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DrillReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DrillReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DrillReq(%+v)", *p)

}

type DrillResp struct {
	ExecResult_ *ExecResult_   `thrift:"exec_result,1" frugal:"1,default,ExecResult_" json:"exec_result"`
	BaseResp    *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewDrillResp() *DrillResp {
	return &DrillResp{}
}

func (p *DrillResp) InitDefault() {
}

var DrillResp_ExecResult__DEFAULT *ExecResult_

func (p *DrillResp) GetExecResult_() (v *ExecResult_) {
	if !p.IsSetExecResult_() {
		return DrillResp_ExecResult__DEFAULT
	}
	return p.ExecResult_
}

var DrillResp_BaseResp_DEFAULT *base.BaseResp

func (p *DrillResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return DrillResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *DrillResp) SetExecResult_(val *ExecResult_) {
	p.ExecResult_ = val
}
func (p *DrillResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_DrillResp = map[int16]string{
	1:   "exec_result",
	255: "BaseResp",
}

func (p *DrillResp) IsSetExecResult_() bool {
	return p.ExecResult_ != nil
}

func (p *DrillResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *DrillResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DrillResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DrillResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DrillResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewExecResult_()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ExecResult_ = _field
	return nil
}
func (p *DrillResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *DrillResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DrillResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DrillResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DrillResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("exec_result", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ExecResult_.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DrillResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DrillResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DrillResp(%+v)", *p)

}

type DailyReportReq struct {
	StartTime string     `thrift:"start_time,1" frugal:"1,default,string" json:"start_time"`
	EndTime   string     `thrift:"end_time,2" frugal:"2,default,string" json:"end_time"`
	Base      *base.Base `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewDailyReportReq() *DailyReportReq {
	return &DailyReportReq{}
}

func (p *DailyReportReq) InitDefault() {
}

func (p *DailyReportReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *DailyReportReq) GetEndTime() (v string) {
	return p.EndTime
}

var DailyReportReq_Base_DEFAULT *base.Base

func (p *DailyReportReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return DailyReportReq_Base_DEFAULT
	}
	return p.Base
}
func (p *DailyReportReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *DailyReportReq) SetEndTime(val string) {
	p.EndTime = val
}
func (p *DailyReportReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_DailyReportReq = map[int16]string{
	1:   "start_time",
	2:   "end_time",
	255: "Base",
}

func (p *DailyReportReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *DailyReportReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DailyReportReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DailyReportReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DailyReportReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DailyReportReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DailyReportReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *DailyReportReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DailyReportReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DailyReportReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DailyReportReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("start_time", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DailyReportReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("end_time", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DailyReportReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DailyReportReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DailyReportReq(%+v)", *p)

}

type DailyReportResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewDailyReportResp() *DailyReportResp {
	return &DailyReportResp{}
}

func (p *DailyReportResp) InitDefault() {
}

var DailyReportResp_BaseResp_DEFAULT *base.BaseResp

func (p *DailyReportResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return DailyReportResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *DailyReportResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_DailyReportResp = map[int16]string{
	255: "BaseResp",
}

func (p *DailyReportResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *DailyReportResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DailyReportResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DailyReportResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DailyReportResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *DailyReportResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DailyReportResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DailyReportResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DailyReportResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DailyReportResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DailyReportResp(%+v)", *p)

}

type CheckAmountByContStructReq struct {
	OrderID                   string                         `thrift:"order_id,1" frugal:"1,default,string" json:"order_id"`
	FinanceOrderType          int32                          `thrift:"finance_order_type,2" frugal:"2,default,i32" json:"finance_order_type"`
	Amount                    int64                          `thrift:"amount,3" frugal:"3,default,i64" json:"amount"`
	AllowIncomeAmountOverflow *bool                          `thrift:"allow_income_amount_overflow,4,optional" frugal:"4,optional,bool" json:"allow_income_amount_overflow,omitempty"`
	Params                    *string                        `thrift:"params,5,optional" frugal:"5,optional,string" json:"params,omitempty"`
	FulfillID                 *string                        `thrift:"fulfill_id,6,optional" frugal:"6,optional,string" json:"fulfill_id,omitempty"`
	TradeCatogory             fwe_trade_common.TradeCategory `thrift:"trade_catogory,7" frugal:"7,default,TradeCategory" json:"trade_catogory"`
	Base                      *base.Base                     `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewCheckAmountByContStructReq() *CheckAmountByContStructReq {
	return &CheckAmountByContStructReq{}
}

func (p *CheckAmountByContStructReq) InitDefault() {
}

func (p *CheckAmountByContStructReq) GetOrderID() (v string) {
	return p.OrderID
}

func (p *CheckAmountByContStructReq) GetFinanceOrderType() (v int32) {
	return p.FinanceOrderType
}

func (p *CheckAmountByContStructReq) GetAmount() (v int64) {
	return p.Amount
}

var CheckAmountByContStructReq_AllowIncomeAmountOverflow_DEFAULT bool

func (p *CheckAmountByContStructReq) GetAllowIncomeAmountOverflow() (v bool) {
	if !p.IsSetAllowIncomeAmountOverflow() {
		return CheckAmountByContStructReq_AllowIncomeAmountOverflow_DEFAULT
	}
	return *p.AllowIncomeAmountOverflow
}

var CheckAmountByContStructReq_Params_DEFAULT string

func (p *CheckAmountByContStructReq) GetParams() (v string) {
	if !p.IsSetParams() {
		return CheckAmountByContStructReq_Params_DEFAULT
	}
	return *p.Params
}

var CheckAmountByContStructReq_FulfillID_DEFAULT string

func (p *CheckAmountByContStructReq) GetFulfillID() (v string) {
	if !p.IsSetFulfillID() {
		return CheckAmountByContStructReq_FulfillID_DEFAULT
	}
	return *p.FulfillID
}

func (p *CheckAmountByContStructReq) GetTradeCatogory() (v fwe_trade_common.TradeCategory) {
	return p.TradeCatogory
}

var CheckAmountByContStructReq_Base_DEFAULT *base.Base

func (p *CheckAmountByContStructReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CheckAmountByContStructReq_Base_DEFAULT
	}
	return p.Base
}
func (p *CheckAmountByContStructReq) SetOrderID(val string) {
	p.OrderID = val
}
func (p *CheckAmountByContStructReq) SetFinanceOrderType(val int32) {
	p.FinanceOrderType = val
}
func (p *CheckAmountByContStructReq) SetAmount(val int64) {
	p.Amount = val
}
func (p *CheckAmountByContStructReq) SetAllowIncomeAmountOverflow(val *bool) {
	p.AllowIncomeAmountOverflow = val
}
func (p *CheckAmountByContStructReq) SetParams(val *string) {
	p.Params = val
}
func (p *CheckAmountByContStructReq) SetFulfillID(val *string) {
	p.FulfillID = val
}
func (p *CheckAmountByContStructReq) SetTradeCatogory(val fwe_trade_common.TradeCategory) {
	p.TradeCatogory = val
}
func (p *CheckAmountByContStructReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CheckAmountByContStructReq = map[int16]string{
	1:   "order_id",
	2:   "finance_order_type",
	3:   "amount",
	4:   "allow_income_amount_overflow",
	5:   "params",
	6:   "fulfill_id",
	7:   "trade_catogory",
	255: "Base",
}

func (p *CheckAmountByContStructReq) IsSetAllowIncomeAmountOverflow() bool {
	return p.AllowIncomeAmountOverflow != nil
}

func (p *CheckAmountByContStructReq) IsSetParams() bool {
	return p.Params != nil
}

func (p *CheckAmountByContStructReq) IsSetFulfillID() bool {
	return p.FulfillID != nil
}

func (p *CheckAmountByContStructReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *CheckAmountByContStructReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckAmountByContStructReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckAmountByContStructReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckAmountByContStructReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *CheckAmountByContStructReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceOrderType = _field
	return nil
}
func (p *CheckAmountByContStructReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *CheckAmountByContStructReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AllowIncomeAmountOverflow = _field
	return nil
}
func (p *CheckAmountByContStructReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Params = _field
	return nil
}
func (p *CheckAmountByContStructReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FulfillID = _field
	return nil
}
func (p *CheckAmountByContStructReq) ReadField7(iprot thrift.TProtocol) error {

	var _field fwe_trade_common.TradeCategory
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = fwe_trade_common.TradeCategory(v)
	}
	p.TradeCatogory = _field
	return nil
}
func (p *CheckAmountByContStructReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CheckAmountByContStructReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckAmountByContStructReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckAmountByContStructReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckAmountByContStructReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckAmountByContStructReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_order_type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FinanceOrderType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CheckAmountByContStructReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CheckAmountByContStructReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAllowIncomeAmountOverflow() {
		if err = oprot.WriteFieldBegin("allow_income_amount_overflow", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.AllowIncomeAmountOverflow); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CheckAmountByContStructReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetParams() {
		if err = oprot.WriteFieldBegin("params", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Params); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *CheckAmountByContStructReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFulfillID() {
		if err = oprot.WriteFieldBegin("fulfill_id", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FulfillID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *CheckAmountByContStructReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("trade_catogory", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TradeCatogory)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *CheckAmountByContStructReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CheckAmountByContStructReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckAmountByContStructReq(%+v)", *p)

}

type CheckAmountByContStructResp struct {
	BlockStatus  BlockStatus    `thrift:"block_status,1" frugal:"1,default,BlockStatus" json:"block_status"`
	BlockMessage string         `thrift:"block_message,2" frugal:"2,default,string" json:"block_message"`
	BaseResp     *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewCheckAmountByContStructResp() *CheckAmountByContStructResp {
	return &CheckAmountByContStructResp{}
}

func (p *CheckAmountByContStructResp) InitDefault() {
}

func (p *CheckAmountByContStructResp) GetBlockStatus() (v BlockStatus) {
	return p.BlockStatus
}

func (p *CheckAmountByContStructResp) GetBlockMessage() (v string) {
	return p.BlockMessage
}

var CheckAmountByContStructResp_BaseResp_DEFAULT *base.BaseResp

func (p *CheckAmountByContStructResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CheckAmountByContStructResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CheckAmountByContStructResp) SetBlockStatus(val BlockStatus) {
	p.BlockStatus = val
}
func (p *CheckAmountByContStructResp) SetBlockMessage(val string) {
	p.BlockMessage = val
}
func (p *CheckAmountByContStructResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CheckAmountByContStructResp = map[int16]string{
	1:   "block_status",
	2:   "block_message",
	255: "BaseResp",
}

func (p *CheckAmountByContStructResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CheckAmountByContStructResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckAmountByContStructResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckAmountByContStructResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckAmountByContStructResp) ReadField1(iprot thrift.TProtocol) error {

	var _field BlockStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BlockStatus(v)
	}
	p.BlockStatus = _field
	return nil
}
func (p *CheckAmountByContStructResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BlockMessage = _field
	return nil
}
func (p *CheckAmountByContStructResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CheckAmountByContStructResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckAmountByContStructResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckAmountByContStructResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckAmountByContStructResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("block_status", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BlockStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckAmountByContStructResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("block_message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BlockMessage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CheckAmountByContStructResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CheckAmountByContStructResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckAmountByContStructResp(%+v)", *p)

}

type CheckContTotalAmountReq struct {
	TmplID           int64             `thrift:"tmpl_id,1" frugal:"1,default,i64" json:"tmpl_id"`
	TmplParams       map[string]string `thrift:"tmpl_params,2" frugal:"2,default,map<string:string>" json:"tmpl_params"`
	OrderTotalAmount int64             `thrift:"order_total_amount,3" frugal:"3,default,i64" json:"order_total_amount"`
	Params           *string           `thrift:"params,4,optional" frugal:"4,optional,string" json:"params,omitempty"`
	BizScene         *int32            `thrift:"biz_scene,5,optional" frugal:"5,optional,i32" json:"biz_scene,omitempty"`
	ContType         *int32            `thrift:"cont_type,6,optional" frugal:"6,optional,i32" json:"cont_type,omitempty"`
	OrderID          *string           `thrift:"order_id,7,optional" frugal:"7,optional,string" json:"order_id,omitempty"`
}

func NewCheckContTotalAmountReq() *CheckContTotalAmountReq {
	return &CheckContTotalAmountReq{}
}

func (p *CheckContTotalAmountReq) InitDefault() {
}

func (p *CheckContTotalAmountReq) GetTmplID() (v int64) {
	return p.TmplID
}

func (p *CheckContTotalAmountReq) GetTmplParams() (v map[string]string) {
	return p.TmplParams
}

func (p *CheckContTotalAmountReq) GetOrderTotalAmount() (v int64) {
	return p.OrderTotalAmount
}

var CheckContTotalAmountReq_Params_DEFAULT string

func (p *CheckContTotalAmountReq) GetParams() (v string) {
	if !p.IsSetParams() {
		return CheckContTotalAmountReq_Params_DEFAULT
	}
	return *p.Params
}

var CheckContTotalAmountReq_BizScene_DEFAULT int32

func (p *CheckContTotalAmountReq) GetBizScene() (v int32) {
	if !p.IsSetBizScene() {
		return CheckContTotalAmountReq_BizScene_DEFAULT
	}
	return *p.BizScene
}

var CheckContTotalAmountReq_ContType_DEFAULT int32

func (p *CheckContTotalAmountReq) GetContType() (v int32) {
	if !p.IsSetContType() {
		return CheckContTotalAmountReq_ContType_DEFAULT
	}
	return *p.ContType
}

var CheckContTotalAmountReq_OrderID_DEFAULT string

func (p *CheckContTotalAmountReq) GetOrderID() (v string) {
	if !p.IsSetOrderID() {
		return CheckContTotalAmountReq_OrderID_DEFAULT
	}
	return *p.OrderID
}
func (p *CheckContTotalAmountReq) SetTmplID(val int64) {
	p.TmplID = val
}
func (p *CheckContTotalAmountReq) SetTmplParams(val map[string]string) {
	p.TmplParams = val
}
func (p *CheckContTotalAmountReq) SetOrderTotalAmount(val int64) {
	p.OrderTotalAmount = val
}
func (p *CheckContTotalAmountReq) SetParams(val *string) {
	p.Params = val
}
func (p *CheckContTotalAmountReq) SetBizScene(val *int32) {
	p.BizScene = val
}
func (p *CheckContTotalAmountReq) SetContType(val *int32) {
	p.ContType = val
}
func (p *CheckContTotalAmountReq) SetOrderID(val *string) {
	p.OrderID = val
}

var fieldIDToName_CheckContTotalAmountReq = map[int16]string{
	1: "tmpl_id",
	2: "tmpl_params",
	3: "order_total_amount",
	4: "params",
	5: "biz_scene",
	6: "cont_type",
	7: "order_id",
}

func (p *CheckContTotalAmountReq) IsSetParams() bool {
	return p.Params != nil
}

func (p *CheckContTotalAmountReq) IsSetBizScene() bool {
	return p.BizScene != nil
}

func (p *CheckContTotalAmountReq) IsSetContType() bool {
	return p.ContType != nil
}

func (p *CheckContTotalAmountReq) IsSetOrderID() bool {
	return p.OrderID != nil
}

func (p *CheckContTotalAmountReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckContTotalAmountReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckContTotalAmountReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckContTotalAmountReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TmplID = _field
	return nil
}
func (p *CheckContTotalAmountReq) ReadField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.TmplParams = _field
	return nil
}
func (p *CheckContTotalAmountReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderTotalAmount = _field
	return nil
}
func (p *CheckContTotalAmountReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Params = _field
	return nil
}
func (p *CheckContTotalAmountReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BizScene = _field
	return nil
}
func (p *CheckContTotalAmountReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ContType = _field
	return nil
}
func (p *CheckContTotalAmountReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OrderID = _field
	return nil
}

func (p *CheckContTotalAmountReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckContTotalAmountReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckContTotalAmountReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckContTotalAmountReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tmpl_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TmplID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckContTotalAmountReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tmpl_params", thrift.MAP, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.TmplParams)); err != nil {
		return err
	}
	for k, v := range p.TmplParams {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CheckContTotalAmountReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_total_amount", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.OrderTotalAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CheckContTotalAmountReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetParams() {
		if err = oprot.WriteFieldBegin("params", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Params); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CheckContTotalAmountReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizScene() {
		if err = oprot.WriteFieldBegin("biz_scene", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.BizScene); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *CheckContTotalAmountReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetContType() {
		if err = oprot.WriteFieldBegin("cont_type", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ContType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *CheckContTotalAmountReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderID() {
		if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OrderID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CheckContTotalAmountReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckContTotalAmountReq(%+v)", *p)

}

type CheckContTotalAmountResp struct {
	BlockStatus  BlockStatus    `thrift:"block_status,1" frugal:"1,default,BlockStatus" json:"block_status"`
	BlockMessage string         `thrift:"block_message,2" frugal:"2,default,string" json:"block_message"`
	BaseResp     *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewCheckContTotalAmountResp() *CheckContTotalAmountResp {
	return &CheckContTotalAmountResp{}
}

func (p *CheckContTotalAmountResp) InitDefault() {
}

func (p *CheckContTotalAmountResp) GetBlockStatus() (v BlockStatus) {
	return p.BlockStatus
}

func (p *CheckContTotalAmountResp) GetBlockMessage() (v string) {
	return p.BlockMessage
}

var CheckContTotalAmountResp_BaseResp_DEFAULT *base.BaseResp

func (p *CheckContTotalAmountResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CheckContTotalAmountResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CheckContTotalAmountResp) SetBlockStatus(val BlockStatus) {
	p.BlockStatus = val
}
func (p *CheckContTotalAmountResp) SetBlockMessage(val string) {
	p.BlockMessage = val
}
func (p *CheckContTotalAmountResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CheckContTotalAmountResp = map[int16]string{
	1:   "block_status",
	2:   "block_message",
	255: "BaseResp",
}

func (p *CheckContTotalAmountResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CheckContTotalAmountResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckContTotalAmountResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckContTotalAmountResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckContTotalAmountResp) ReadField1(iprot thrift.TProtocol) error {

	var _field BlockStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = BlockStatus(v)
	}
	p.BlockStatus = _field
	return nil
}
func (p *CheckContTotalAmountResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BlockMessage = _field
	return nil
}
func (p *CheckContTotalAmountResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CheckContTotalAmountResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckContTotalAmountResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckContTotalAmountResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckContTotalAmountResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("block_status", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.BlockStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckContTotalAmountResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("block_message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BlockMessage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CheckContTotalAmountResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CheckContTotalAmountResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckContTotalAmountResp(%+v)", *p)

}

type TradeSafeService interface {
	ExecRuleByBinlog(ctx context.Context, req *ExecRuleByBinlogReq) (r *ExecRuleResp, err error)

	MultiStreamCheck(ctx context.Context, req *MultiStreamCheckReq) (r *MultiStreamCheckResp, err error)

	Advise(ctx context.Context, req *AdviseReq) (r *AdviseResp, err error)

	ScheduleCallback(ctx context.Context, req *ScheduleCallbackReq) (r *ScheduleCallbackResp, err error)

	OperateRule(ctx context.Context, req *OperateRuleReq) (r *OperateRuleResp, err error)

	CreateOrUpdateDrill(ctx context.Context, req *CreateOrUpdateDrillReq) (r *CreateOrUpdateDrillResp, err error)

	Drill(ctx context.Context, req *DrillReq) (r *DrillResp, err error)

	DailyReport(ctx context.Context, req *DailyReportReq) (r *DailyReportResp, err error)

	CheckAmountByContStruct(ctx context.Context, req *CheckAmountByContStructReq) (r *CheckAmountByContStructResp, err error)

	CheckContTotalAmount(ctx context.Context, req *CheckContTotalAmountReq) (r *CheckContTotalAmountResp, err error)
}

type TradeSafeServiceExecRuleByBinlogArgs struct {
	Req *ExecRuleByBinlogReq `thrift:"req,1" frugal:"1,default,ExecRuleByBinlogReq" json:"req"`
}

func NewTradeSafeServiceExecRuleByBinlogArgs() *TradeSafeServiceExecRuleByBinlogArgs {
	return &TradeSafeServiceExecRuleByBinlogArgs{}
}

func (p *TradeSafeServiceExecRuleByBinlogArgs) InitDefault() {
}

var TradeSafeServiceExecRuleByBinlogArgs_Req_DEFAULT *ExecRuleByBinlogReq

func (p *TradeSafeServiceExecRuleByBinlogArgs) GetReq() (v *ExecRuleByBinlogReq) {
	if !p.IsSetReq() {
		return TradeSafeServiceExecRuleByBinlogArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeSafeServiceExecRuleByBinlogArgs) SetReq(val *ExecRuleByBinlogReq) {
	p.Req = val
}

var fieldIDToName_TradeSafeServiceExecRuleByBinlogArgs = map[int16]string{
	1: "req",
}

func (p *TradeSafeServiceExecRuleByBinlogArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeSafeServiceExecRuleByBinlogArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceExecRuleByBinlogArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceExecRuleByBinlogArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceExecRuleByBinlogArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewExecRuleByBinlogReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeSafeServiceExecRuleByBinlogArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceExecRuleByBinlogArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecRuleByBinlog_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceExecRuleByBinlogArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeSafeServiceExecRuleByBinlogArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceExecRuleByBinlogArgs(%+v)", *p)

}

type TradeSafeServiceExecRuleByBinlogResult struct {
	Success *ExecRuleResp `thrift:"success,0,optional" frugal:"0,optional,ExecRuleResp" json:"success,omitempty"`
}

func NewTradeSafeServiceExecRuleByBinlogResult() *TradeSafeServiceExecRuleByBinlogResult {
	return &TradeSafeServiceExecRuleByBinlogResult{}
}

func (p *TradeSafeServiceExecRuleByBinlogResult) InitDefault() {
}

var TradeSafeServiceExecRuleByBinlogResult_Success_DEFAULT *ExecRuleResp

func (p *TradeSafeServiceExecRuleByBinlogResult) GetSuccess() (v *ExecRuleResp) {
	if !p.IsSetSuccess() {
		return TradeSafeServiceExecRuleByBinlogResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeSafeServiceExecRuleByBinlogResult) SetSuccess(x interface{}) {
	p.Success = x.(*ExecRuleResp)
}

var fieldIDToName_TradeSafeServiceExecRuleByBinlogResult = map[int16]string{
	0: "success",
}

func (p *TradeSafeServiceExecRuleByBinlogResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeSafeServiceExecRuleByBinlogResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceExecRuleByBinlogResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceExecRuleByBinlogResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceExecRuleByBinlogResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewExecRuleResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeSafeServiceExecRuleByBinlogResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceExecRuleByBinlogResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecRuleByBinlog_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceExecRuleByBinlogResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeSafeServiceExecRuleByBinlogResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceExecRuleByBinlogResult(%+v)", *p)

}

type TradeSafeServiceMultiStreamCheckArgs struct {
	Req *MultiStreamCheckReq `thrift:"req,1" frugal:"1,default,MultiStreamCheckReq" json:"req"`
}

func NewTradeSafeServiceMultiStreamCheckArgs() *TradeSafeServiceMultiStreamCheckArgs {
	return &TradeSafeServiceMultiStreamCheckArgs{}
}

func (p *TradeSafeServiceMultiStreamCheckArgs) InitDefault() {
}

var TradeSafeServiceMultiStreamCheckArgs_Req_DEFAULT *MultiStreamCheckReq

func (p *TradeSafeServiceMultiStreamCheckArgs) GetReq() (v *MultiStreamCheckReq) {
	if !p.IsSetReq() {
		return TradeSafeServiceMultiStreamCheckArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeSafeServiceMultiStreamCheckArgs) SetReq(val *MultiStreamCheckReq) {
	p.Req = val
}

var fieldIDToName_TradeSafeServiceMultiStreamCheckArgs = map[int16]string{
	1: "req",
}

func (p *TradeSafeServiceMultiStreamCheckArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeSafeServiceMultiStreamCheckArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceMultiStreamCheckArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceMultiStreamCheckArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceMultiStreamCheckArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewMultiStreamCheckReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeSafeServiceMultiStreamCheckArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceMultiStreamCheckArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("MultiStreamCheck_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceMultiStreamCheckArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeSafeServiceMultiStreamCheckArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceMultiStreamCheckArgs(%+v)", *p)

}

type TradeSafeServiceMultiStreamCheckResult struct {
	Success *MultiStreamCheckResp `thrift:"success,0,optional" frugal:"0,optional,MultiStreamCheckResp" json:"success,omitempty"`
}

func NewTradeSafeServiceMultiStreamCheckResult() *TradeSafeServiceMultiStreamCheckResult {
	return &TradeSafeServiceMultiStreamCheckResult{}
}

func (p *TradeSafeServiceMultiStreamCheckResult) InitDefault() {
}

var TradeSafeServiceMultiStreamCheckResult_Success_DEFAULT *MultiStreamCheckResp

func (p *TradeSafeServiceMultiStreamCheckResult) GetSuccess() (v *MultiStreamCheckResp) {
	if !p.IsSetSuccess() {
		return TradeSafeServiceMultiStreamCheckResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeSafeServiceMultiStreamCheckResult) SetSuccess(x interface{}) {
	p.Success = x.(*MultiStreamCheckResp)
}

var fieldIDToName_TradeSafeServiceMultiStreamCheckResult = map[int16]string{
	0: "success",
}

func (p *TradeSafeServiceMultiStreamCheckResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeSafeServiceMultiStreamCheckResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceMultiStreamCheckResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceMultiStreamCheckResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceMultiStreamCheckResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewMultiStreamCheckResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeSafeServiceMultiStreamCheckResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceMultiStreamCheckResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("MultiStreamCheck_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceMultiStreamCheckResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeSafeServiceMultiStreamCheckResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceMultiStreamCheckResult(%+v)", *p)

}

type TradeSafeServiceAdviseArgs struct {
	Req *AdviseReq `thrift:"req,1" frugal:"1,default,AdviseReq" json:"req"`
}

func NewTradeSafeServiceAdviseArgs() *TradeSafeServiceAdviseArgs {
	return &TradeSafeServiceAdviseArgs{}
}

func (p *TradeSafeServiceAdviseArgs) InitDefault() {
}

var TradeSafeServiceAdviseArgs_Req_DEFAULT *AdviseReq

func (p *TradeSafeServiceAdviseArgs) GetReq() (v *AdviseReq) {
	if !p.IsSetReq() {
		return TradeSafeServiceAdviseArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeSafeServiceAdviseArgs) SetReq(val *AdviseReq) {
	p.Req = val
}

var fieldIDToName_TradeSafeServiceAdviseArgs = map[int16]string{
	1: "req",
}

func (p *TradeSafeServiceAdviseArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeSafeServiceAdviseArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceAdviseArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceAdviseArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceAdviseArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewAdviseReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeSafeServiceAdviseArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceAdviseArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("Advise_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceAdviseArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeSafeServiceAdviseArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceAdviseArgs(%+v)", *p)

}

type TradeSafeServiceAdviseResult struct {
	Success *AdviseResp `thrift:"success,0,optional" frugal:"0,optional,AdviseResp" json:"success,omitempty"`
}

func NewTradeSafeServiceAdviseResult() *TradeSafeServiceAdviseResult {
	return &TradeSafeServiceAdviseResult{}
}

func (p *TradeSafeServiceAdviseResult) InitDefault() {
}

var TradeSafeServiceAdviseResult_Success_DEFAULT *AdviseResp

func (p *TradeSafeServiceAdviseResult) GetSuccess() (v *AdviseResp) {
	if !p.IsSetSuccess() {
		return TradeSafeServiceAdviseResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeSafeServiceAdviseResult) SetSuccess(x interface{}) {
	p.Success = x.(*AdviseResp)
}

var fieldIDToName_TradeSafeServiceAdviseResult = map[int16]string{
	0: "success",
}

func (p *TradeSafeServiceAdviseResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeSafeServiceAdviseResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceAdviseResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceAdviseResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceAdviseResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewAdviseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeSafeServiceAdviseResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceAdviseResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("Advise_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceAdviseResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeSafeServiceAdviseResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceAdviseResult(%+v)", *p)

}

type TradeSafeServiceScheduleCallbackArgs struct {
	Req *ScheduleCallbackReq `thrift:"req,1" frugal:"1,default,ScheduleCallbackReq" json:"req"`
}

func NewTradeSafeServiceScheduleCallbackArgs() *TradeSafeServiceScheduleCallbackArgs {
	return &TradeSafeServiceScheduleCallbackArgs{}
}

func (p *TradeSafeServiceScheduleCallbackArgs) InitDefault() {
}

var TradeSafeServiceScheduleCallbackArgs_Req_DEFAULT *ScheduleCallbackReq

func (p *TradeSafeServiceScheduleCallbackArgs) GetReq() (v *ScheduleCallbackReq) {
	if !p.IsSetReq() {
		return TradeSafeServiceScheduleCallbackArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeSafeServiceScheduleCallbackArgs) SetReq(val *ScheduleCallbackReq) {
	p.Req = val
}

var fieldIDToName_TradeSafeServiceScheduleCallbackArgs = map[int16]string{
	1: "req",
}

func (p *TradeSafeServiceScheduleCallbackArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeSafeServiceScheduleCallbackArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceScheduleCallbackArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceScheduleCallbackArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceScheduleCallbackArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewScheduleCallbackReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeSafeServiceScheduleCallbackArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceScheduleCallbackArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ScheduleCallback_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceScheduleCallbackArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeSafeServiceScheduleCallbackArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceScheduleCallbackArgs(%+v)", *p)

}

type TradeSafeServiceScheduleCallbackResult struct {
	Success *ScheduleCallbackResp `thrift:"success,0,optional" frugal:"0,optional,ScheduleCallbackResp" json:"success,omitempty"`
}

func NewTradeSafeServiceScheduleCallbackResult() *TradeSafeServiceScheduleCallbackResult {
	return &TradeSafeServiceScheduleCallbackResult{}
}

func (p *TradeSafeServiceScheduleCallbackResult) InitDefault() {
}

var TradeSafeServiceScheduleCallbackResult_Success_DEFAULT *ScheduleCallbackResp

func (p *TradeSafeServiceScheduleCallbackResult) GetSuccess() (v *ScheduleCallbackResp) {
	if !p.IsSetSuccess() {
		return TradeSafeServiceScheduleCallbackResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeSafeServiceScheduleCallbackResult) SetSuccess(x interface{}) {
	p.Success = x.(*ScheduleCallbackResp)
}

var fieldIDToName_TradeSafeServiceScheduleCallbackResult = map[int16]string{
	0: "success",
}

func (p *TradeSafeServiceScheduleCallbackResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeSafeServiceScheduleCallbackResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceScheduleCallbackResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceScheduleCallbackResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceScheduleCallbackResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewScheduleCallbackResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeSafeServiceScheduleCallbackResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceScheduleCallbackResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ScheduleCallback_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceScheduleCallbackResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeSafeServiceScheduleCallbackResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceScheduleCallbackResult(%+v)", *p)

}

type TradeSafeServiceOperateRuleArgs struct {
	Req *OperateRuleReq `thrift:"req,1" frugal:"1,default,OperateRuleReq" json:"req"`
}

func NewTradeSafeServiceOperateRuleArgs() *TradeSafeServiceOperateRuleArgs {
	return &TradeSafeServiceOperateRuleArgs{}
}

func (p *TradeSafeServiceOperateRuleArgs) InitDefault() {
}

var TradeSafeServiceOperateRuleArgs_Req_DEFAULT *OperateRuleReq

func (p *TradeSafeServiceOperateRuleArgs) GetReq() (v *OperateRuleReq) {
	if !p.IsSetReq() {
		return TradeSafeServiceOperateRuleArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeSafeServiceOperateRuleArgs) SetReq(val *OperateRuleReq) {
	p.Req = val
}

var fieldIDToName_TradeSafeServiceOperateRuleArgs = map[int16]string{
	1: "req",
}

func (p *TradeSafeServiceOperateRuleArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeSafeServiceOperateRuleArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceOperateRuleArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceOperateRuleArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceOperateRuleArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewOperateRuleReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeSafeServiceOperateRuleArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceOperateRuleArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("OperateRule_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceOperateRuleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeSafeServiceOperateRuleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceOperateRuleArgs(%+v)", *p)

}

type TradeSafeServiceOperateRuleResult struct {
	Success *OperateRuleResp `thrift:"success,0,optional" frugal:"0,optional,OperateRuleResp" json:"success,omitempty"`
}

func NewTradeSafeServiceOperateRuleResult() *TradeSafeServiceOperateRuleResult {
	return &TradeSafeServiceOperateRuleResult{}
}

func (p *TradeSafeServiceOperateRuleResult) InitDefault() {
}

var TradeSafeServiceOperateRuleResult_Success_DEFAULT *OperateRuleResp

func (p *TradeSafeServiceOperateRuleResult) GetSuccess() (v *OperateRuleResp) {
	if !p.IsSetSuccess() {
		return TradeSafeServiceOperateRuleResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeSafeServiceOperateRuleResult) SetSuccess(x interface{}) {
	p.Success = x.(*OperateRuleResp)
}

var fieldIDToName_TradeSafeServiceOperateRuleResult = map[int16]string{
	0: "success",
}

func (p *TradeSafeServiceOperateRuleResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeSafeServiceOperateRuleResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceOperateRuleResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceOperateRuleResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceOperateRuleResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewOperateRuleResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeSafeServiceOperateRuleResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceOperateRuleResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("OperateRule_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceOperateRuleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeSafeServiceOperateRuleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceOperateRuleResult(%+v)", *p)

}

type TradeSafeServiceCreateOrUpdateDrillArgs struct {
	Req *CreateOrUpdateDrillReq `thrift:"req,1" frugal:"1,default,CreateOrUpdateDrillReq" json:"req"`
}

func NewTradeSafeServiceCreateOrUpdateDrillArgs() *TradeSafeServiceCreateOrUpdateDrillArgs {
	return &TradeSafeServiceCreateOrUpdateDrillArgs{}
}

func (p *TradeSafeServiceCreateOrUpdateDrillArgs) InitDefault() {
}

var TradeSafeServiceCreateOrUpdateDrillArgs_Req_DEFAULT *CreateOrUpdateDrillReq

func (p *TradeSafeServiceCreateOrUpdateDrillArgs) GetReq() (v *CreateOrUpdateDrillReq) {
	if !p.IsSetReq() {
		return TradeSafeServiceCreateOrUpdateDrillArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeSafeServiceCreateOrUpdateDrillArgs) SetReq(val *CreateOrUpdateDrillReq) {
	p.Req = val
}

var fieldIDToName_TradeSafeServiceCreateOrUpdateDrillArgs = map[int16]string{
	1: "req",
}

func (p *TradeSafeServiceCreateOrUpdateDrillArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeSafeServiceCreateOrUpdateDrillArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCreateOrUpdateDrillArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceCreateOrUpdateDrillArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceCreateOrUpdateDrillArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreateOrUpdateDrillReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeSafeServiceCreateOrUpdateDrillArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCreateOrUpdateDrillArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateOrUpdateDrill_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceCreateOrUpdateDrillArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeSafeServiceCreateOrUpdateDrillArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceCreateOrUpdateDrillArgs(%+v)", *p)

}

type TradeSafeServiceCreateOrUpdateDrillResult struct {
	Success *CreateOrUpdateDrillResp `thrift:"success,0,optional" frugal:"0,optional,CreateOrUpdateDrillResp" json:"success,omitempty"`
}

func NewTradeSafeServiceCreateOrUpdateDrillResult() *TradeSafeServiceCreateOrUpdateDrillResult {
	return &TradeSafeServiceCreateOrUpdateDrillResult{}
}

func (p *TradeSafeServiceCreateOrUpdateDrillResult) InitDefault() {
}

var TradeSafeServiceCreateOrUpdateDrillResult_Success_DEFAULT *CreateOrUpdateDrillResp

func (p *TradeSafeServiceCreateOrUpdateDrillResult) GetSuccess() (v *CreateOrUpdateDrillResp) {
	if !p.IsSetSuccess() {
		return TradeSafeServiceCreateOrUpdateDrillResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeSafeServiceCreateOrUpdateDrillResult) SetSuccess(x interface{}) {
	p.Success = x.(*CreateOrUpdateDrillResp)
}

var fieldIDToName_TradeSafeServiceCreateOrUpdateDrillResult = map[int16]string{
	0: "success",
}

func (p *TradeSafeServiceCreateOrUpdateDrillResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeSafeServiceCreateOrUpdateDrillResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCreateOrUpdateDrillResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceCreateOrUpdateDrillResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceCreateOrUpdateDrillResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreateOrUpdateDrillResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeSafeServiceCreateOrUpdateDrillResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCreateOrUpdateDrillResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateOrUpdateDrill_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceCreateOrUpdateDrillResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeSafeServiceCreateOrUpdateDrillResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceCreateOrUpdateDrillResult(%+v)", *p)

}

type TradeSafeServiceDrillArgs struct {
	Req *DrillReq `thrift:"req,1" frugal:"1,default,DrillReq" json:"req"`
}

func NewTradeSafeServiceDrillArgs() *TradeSafeServiceDrillArgs {
	return &TradeSafeServiceDrillArgs{}
}

func (p *TradeSafeServiceDrillArgs) InitDefault() {
}

var TradeSafeServiceDrillArgs_Req_DEFAULT *DrillReq

func (p *TradeSafeServiceDrillArgs) GetReq() (v *DrillReq) {
	if !p.IsSetReq() {
		return TradeSafeServiceDrillArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeSafeServiceDrillArgs) SetReq(val *DrillReq) {
	p.Req = val
}

var fieldIDToName_TradeSafeServiceDrillArgs = map[int16]string{
	1: "req",
}

func (p *TradeSafeServiceDrillArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeSafeServiceDrillArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceDrillArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceDrillArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceDrillArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDrillReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeSafeServiceDrillArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceDrillArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("Drill_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceDrillArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeSafeServiceDrillArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceDrillArgs(%+v)", *p)

}

type TradeSafeServiceDrillResult struct {
	Success *DrillResp `thrift:"success,0,optional" frugal:"0,optional,DrillResp" json:"success,omitempty"`
}

func NewTradeSafeServiceDrillResult() *TradeSafeServiceDrillResult {
	return &TradeSafeServiceDrillResult{}
}

func (p *TradeSafeServiceDrillResult) InitDefault() {
}

var TradeSafeServiceDrillResult_Success_DEFAULT *DrillResp

func (p *TradeSafeServiceDrillResult) GetSuccess() (v *DrillResp) {
	if !p.IsSetSuccess() {
		return TradeSafeServiceDrillResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeSafeServiceDrillResult) SetSuccess(x interface{}) {
	p.Success = x.(*DrillResp)
}

var fieldIDToName_TradeSafeServiceDrillResult = map[int16]string{
	0: "success",
}

func (p *TradeSafeServiceDrillResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeSafeServiceDrillResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceDrillResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceDrillResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceDrillResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDrillResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeSafeServiceDrillResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceDrillResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("Drill_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceDrillResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeSafeServiceDrillResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceDrillResult(%+v)", *p)

}

type TradeSafeServiceDailyReportArgs struct {
	Req *DailyReportReq `thrift:"req,1" frugal:"1,default,DailyReportReq" json:"req"`
}

func NewTradeSafeServiceDailyReportArgs() *TradeSafeServiceDailyReportArgs {
	return &TradeSafeServiceDailyReportArgs{}
}

func (p *TradeSafeServiceDailyReportArgs) InitDefault() {
}

var TradeSafeServiceDailyReportArgs_Req_DEFAULT *DailyReportReq

func (p *TradeSafeServiceDailyReportArgs) GetReq() (v *DailyReportReq) {
	if !p.IsSetReq() {
		return TradeSafeServiceDailyReportArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeSafeServiceDailyReportArgs) SetReq(val *DailyReportReq) {
	p.Req = val
}

var fieldIDToName_TradeSafeServiceDailyReportArgs = map[int16]string{
	1: "req",
}

func (p *TradeSafeServiceDailyReportArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeSafeServiceDailyReportArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceDailyReportArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceDailyReportArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceDailyReportArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDailyReportReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeSafeServiceDailyReportArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceDailyReportArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DailyReport_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceDailyReportArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeSafeServiceDailyReportArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceDailyReportArgs(%+v)", *p)

}

type TradeSafeServiceDailyReportResult struct {
	Success *DailyReportResp `thrift:"success,0,optional" frugal:"0,optional,DailyReportResp" json:"success,omitempty"`
}

func NewTradeSafeServiceDailyReportResult() *TradeSafeServiceDailyReportResult {
	return &TradeSafeServiceDailyReportResult{}
}

func (p *TradeSafeServiceDailyReportResult) InitDefault() {
}

var TradeSafeServiceDailyReportResult_Success_DEFAULT *DailyReportResp

func (p *TradeSafeServiceDailyReportResult) GetSuccess() (v *DailyReportResp) {
	if !p.IsSetSuccess() {
		return TradeSafeServiceDailyReportResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeSafeServiceDailyReportResult) SetSuccess(x interface{}) {
	p.Success = x.(*DailyReportResp)
}

var fieldIDToName_TradeSafeServiceDailyReportResult = map[int16]string{
	0: "success",
}

func (p *TradeSafeServiceDailyReportResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeSafeServiceDailyReportResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceDailyReportResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceDailyReportResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceDailyReportResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDailyReportResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeSafeServiceDailyReportResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceDailyReportResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DailyReport_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceDailyReportResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeSafeServiceDailyReportResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceDailyReportResult(%+v)", *p)

}

type TradeSafeServiceCheckAmountByContStructArgs struct {
	Req *CheckAmountByContStructReq `thrift:"req,1" frugal:"1,default,CheckAmountByContStructReq" json:"req"`
}

func NewTradeSafeServiceCheckAmountByContStructArgs() *TradeSafeServiceCheckAmountByContStructArgs {
	return &TradeSafeServiceCheckAmountByContStructArgs{}
}

func (p *TradeSafeServiceCheckAmountByContStructArgs) InitDefault() {
}

var TradeSafeServiceCheckAmountByContStructArgs_Req_DEFAULT *CheckAmountByContStructReq

func (p *TradeSafeServiceCheckAmountByContStructArgs) GetReq() (v *CheckAmountByContStructReq) {
	if !p.IsSetReq() {
		return TradeSafeServiceCheckAmountByContStructArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeSafeServiceCheckAmountByContStructArgs) SetReq(val *CheckAmountByContStructReq) {
	p.Req = val
}

var fieldIDToName_TradeSafeServiceCheckAmountByContStructArgs = map[int16]string{
	1: "req",
}

func (p *TradeSafeServiceCheckAmountByContStructArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeSafeServiceCheckAmountByContStructArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCheckAmountByContStructArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceCheckAmountByContStructArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceCheckAmountByContStructArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCheckAmountByContStructReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeSafeServiceCheckAmountByContStructArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCheckAmountByContStructArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckAmountByContStruct_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceCheckAmountByContStructArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeSafeServiceCheckAmountByContStructArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceCheckAmountByContStructArgs(%+v)", *p)

}

type TradeSafeServiceCheckAmountByContStructResult struct {
	Success *CheckAmountByContStructResp `thrift:"success,0,optional" frugal:"0,optional,CheckAmountByContStructResp" json:"success,omitempty"`
}

func NewTradeSafeServiceCheckAmountByContStructResult() *TradeSafeServiceCheckAmountByContStructResult {
	return &TradeSafeServiceCheckAmountByContStructResult{}
}

func (p *TradeSafeServiceCheckAmountByContStructResult) InitDefault() {
}

var TradeSafeServiceCheckAmountByContStructResult_Success_DEFAULT *CheckAmountByContStructResp

func (p *TradeSafeServiceCheckAmountByContStructResult) GetSuccess() (v *CheckAmountByContStructResp) {
	if !p.IsSetSuccess() {
		return TradeSafeServiceCheckAmountByContStructResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeSafeServiceCheckAmountByContStructResult) SetSuccess(x interface{}) {
	p.Success = x.(*CheckAmountByContStructResp)
}

var fieldIDToName_TradeSafeServiceCheckAmountByContStructResult = map[int16]string{
	0: "success",
}

func (p *TradeSafeServiceCheckAmountByContStructResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeSafeServiceCheckAmountByContStructResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCheckAmountByContStructResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceCheckAmountByContStructResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceCheckAmountByContStructResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCheckAmountByContStructResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeSafeServiceCheckAmountByContStructResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCheckAmountByContStructResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckAmountByContStruct_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceCheckAmountByContStructResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeSafeServiceCheckAmountByContStructResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceCheckAmountByContStructResult(%+v)", *p)

}

type TradeSafeServiceCheckContTotalAmountArgs struct {
	Req *CheckContTotalAmountReq `thrift:"req,1" frugal:"1,default,CheckContTotalAmountReq" json:"req"`
}

func NewTradeSafeServiceCheckContTotalAmountArgs() *TradeSafeServiceCheckContTotalAmountArgs {
	return &TradeSafeServiceCheckContTotalAmountArgs{}
}

func (p *TradeSafeServiceCheckContTotalAmountArgs) InitDefault() {
}

var TradeSafeServiceCheckContTotalAmountArgs_Req_DEFAULT *CheckContTotalAmountReq

func (p *TradeSafeServiceCheckContTotalAmountArgs) GetReq() (v *CheckContTotalAmountReq) {
	if !p.IsSetReq() {
		return TradeSafeServiceCheckContTotalAmountArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *TradeSafeServiceCheckContTotalAmountArgs) SetReq(val *CheckContTotalAmountReq) {
	p.Req = val
}

var fieldIDToName_TradeSafeServiceCheckContTotalAmountArgs = map[int16]string{
	1: "req",
}

func (p *TradeSafeServiceCheckContTotalAmountArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TradeSafeServiceCheckContTotalAmountArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCheckContTotalAmountArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceCheckContTotalAmountArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceCheckContTotalAmountArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCheckContTotalAmountReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TradeSafeServiceCheckContTotalAmountArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCheckContTotalAmountArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckContTotalAmount_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceCheckContTotalAmountArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TradeSafeServiceCheckContTotalAmountArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceCheckContTotalAmountArgs(%+v)", *p)

}

type TradeSafeServiceCheckContTotalAmountResult struct {
	Success *CheckContTotalAmountResp `thrift:"success,0,optional" frugal:"0,optional,CheckContTotalAmountResp" json:"success,omitempty"`
}

func NewTradeSafeServiceCheckContTotalAmountResult() *TradeSafeServiceCheckContTotalAmountResult {
	return &TradeSafeServiceCheckContTotalAmountResult{}
}

func (p *TradeSafeServiceCheckContTotalAmountResult) InitDefault() {
}

var TradeSafeServiceCheckContTotalAmountResult_Success_DEFAULT *CheckContTotalAmountResp

func (p *TradeSafeServiceCheckContTotalAmountResult) GetSuccess() (v *CheckContTotalAmountResp) {
	if !p.IsSetSuccess() {
		return TradeSafeServiceCheckContTotalAmountResult_Success_DEFAULT
	}
	return p.Success
}
func (p *TradeSafeServiceCheckContTotalAmountResult) SetSuccess(x interface{}) {
	p.Success = x.(*CheckContTotalAmountResp)
}

var fieldIDToName_TradeSafeServiceCheckContTotalAmountResult = map[int16]string{
	0: "success",
}

func (p *TradeSafeServiceCheckContTotalAmountResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TradeSafeServiceCheckContTotalAmountResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCheckContTotalAmountResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TradeSafeServiceCheckContTotalAmountResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TradeSafeServiceCheckContTotalAmountResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCheckContTotalAmountResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TradeSafeServiceCheckContTotalAmountResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TradeSafeServiceCheckContTotalAmountResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckContTotalAmount_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TradeSafeServiceCheckContTotalAmountResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TradeSafeServiceCheckContTotalAmountResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TradeSafeServiceCheckContTotalAmountResult(%+v)", *p)

}
