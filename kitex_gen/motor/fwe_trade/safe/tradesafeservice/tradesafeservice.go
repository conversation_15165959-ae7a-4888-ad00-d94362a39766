// Code generated by Kitex v1.20.3. DO NOT EDIT.

package tradesafeservice

import (
	client "code.byted.org/kite/kitex/client"
	safe "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/safe"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"ExecRuleByBinlog": kitex.NewMethodInfo(
		execRuleByBinlogHandler,
		newTradeSafeServiceExecRuleByBinlogArgs,
		newTradeSafeServiceExecRuleByBinlogResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MultiStreamCheck": kitex.NewMethodInfo(
		multiStreamCheckHandler,
		newTradeSafeServiceMultiStreamCheckArgs,
		newTradeSafeServiceMultiStreamCheckResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Advise": kitex.NewMethodInfo(
		adviseHandler,
		newTradeSafeServiceAdviseArgs,
		newTradeSafeServiceAdviseResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ScheduleCallback": kitex.NewMethodInfo(
		scheduleCallbackHandler,
		newTradeSafeServiceScheduleCallbackArgs,
		newTradeSafeServiceScheduleCallbackResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"OperateRule": kitex.NewMethodInfo(
		operateRuleHandler,
		newTradeSafeServiceOperateRuleArgs,
		newTradeSafeServiceOperateRuleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateOrUpdateDrill": kitex.NewMethodInfo(
		createOrUpdateDrillHandler,
		newTradeSafeServiceCreateOrUpdateDrillArgs,
		newTradeSafeServiceCreateOrUpdateDrillResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Drill": kitex.NewMethodInfo(
		drillHandler,
		newTradeSafeServiceDrillArgs,
		newTradeSafeServiceDrillResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DailyReport": kitex.NewMethodInfo(
		dailyReportHandler,
		newTradeSafeServiceDailyReportArgs,
		newTradeSafeServiceDailyReportResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CheckAmountByContStruct": kitex.NewMethodInfo(
		checkAmountByContStructHandler,
		newTradeSafeServiceCheckAmountByContStructArgs,
		newTradeSafeServiceCheckAmountByContStructResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CheckContTotalAmount": kitex.NewMethodInfo(
		checkContTotalAmountHandler,
		newTradeSafeServiceCheckContTotalAmountArgs,
		newTradeSafeServiceCheckContTotalAmountResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	tradeSafeServiceServiceInfo                = NewServiceInfo()
	tradeSafeServiceServiceInfoForClient       = NewServiceInfoForClient()
	tradeSafeServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return tradeSafeServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return tradeSafeServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return tradeSafeServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "TradeSafeService"
	handlerType := (*safe.TradeSafeService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "safe",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func execRuleByBinlogHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*safe.TradeSafeServiceExecRuleByBinlogArgs)
	realResult := result.(*safe.TradeSafeServiceExecRuleByBinlogResult)
	success, err := handler.(safe.TradeSafeService).ExecRuleByBinlog(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeSafeServiceExecRuleByBinlogArgs() interface{} {
	return safe.NewTradeSafeServiceExecRuleByBinlogArgs()
}

func newTradeSafeServiceExecRuleByBinlogResult() interface{} {
	return safe.NewTradeSafeServiceExecRuleByBinlogResult()
}

func multiStreamCheckHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*safe.TradeSafeServiceMultiStreamCheckArgs)
	realResult := result.(*safe.TradeSafeServiceMultiStreamCheckResult)
	success, err := handler.(safe.TradeSafeService).MultiStreamCheck(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeSafeServiceMultiStreamCheckArgs() interface{} {
	return safe.NewTradeSafeServiceMultiStreamCheckArgs()
}

func newTradeSafeServiceMultiStreamCheckResult() interface{} {
	return safe.NewTradeSafeServiceMultiStreamCheckResult()
}

func adviseHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*safe.TradeSafeServiceAdviseArgs)
	realResult := result.(*safe.TradeSafeServiceAdviseResult)
	success, err := handler.(safe.TradeSafeService).Advise(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeSafeServiceAdviseArgs() interface{} {
	return safe.NewTradeSafeServiceAdviseArgs()
}

func newTradeSafeServiceAdviseResult() interface{} {
	return safe.NewTradeSafeServiceAdviseResult()
}

func scheduleCallbackHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*safe.TradeSafeServiceScheduleCallbackArgs)
	realResult := result.(*safe.TradeSafeServiceScheduleCallbackResult)
	success, err := handler.(safe.TradeSafeService).ScheduleCallback(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeSafeServiceScheduleCallbackArgs() interface{} {
	return safe.NewTradeSafeServiceScheduleCallbackArgs()
}

func newTradeSafeServiceScheduleCallbackResult() interface{} {
	return safe.NewTradeSafeServiceScheduleCallbackResult()
}

func operateRuleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*safe.TradeSafeServiceOperateRuleArgs)
	realResult := result.(*safe.TradeSafeServiceOperateRuleResult)
	success, err := handler.(safe.TradeSafeService).OperateRule(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeSafeServiceOperateRuleArgs() interface{} {
	return safe.NewTradeSafeServiceOperateRuleArgs()
}

func newTradeSafeServiceOperateRuleResult() interface{} {
	return safe.NewTradeSafeServiceOperateRuleResult()
}

func createOrUpdateDrillHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*safe.TradeSafeServiceCreateOrUpdateDrillArgs)
	realResult := result.(*safe.TradeSafeServiceCreateOrUpdateDrillResult)
	success, err := handler.(safe.TradeSafeService).CreateOrUpdateDrill(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeSafeServiceCreateOrUpdateDrillArgs() interface{} {
	return safe.NewTradeSafeServiceCreateOrUpdateDrillArgs()
}

func newTradeSafeServiceCreateOrUpdateDrillResult() interface{} {
	return safe.NewTradeSafeServiceCreateOrUpdateDrillResult()
}

func drillHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*safe.TradeSafeServiceDrillArgs)
	realResult := result.(*safe.TradeSafeServiceDrillResult)
	success, err := handler.(safe.TradeSafeService).Drill(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeSafeServiceDrillArgs() interface{} {
	return safe.NewTradeSafeServiceDrillArgs()
}

func newTradeSafeServiceDrillResult() interface{} {
	return safe.NewTradeSafeServiceDrillResult()
}

func dailyReportHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*safe.TradeSafeServiceDailyReportArgs)
	realResult := result.(*safe.TradeSafeServiceDailyReportResult)
	success, err := handler.(safe.TradeSafeService).DailyReport(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeSafeServiceDailyReportArgs() interface{} {
	return safe.NewTradeSafeServiceDailyReportArgs()
}

func newTradeSafeServiceDailyReportResult() interface{} {
	return safe.NewTradeSafeServiceDailyReportResult()
}

func checkAmountByContStructHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*safe.TradeSafeServiceCheckAmountByContStructArgs)
	realResult := result.(*safe.TradeSafeServiceCheckAmountByContStructResult)
	success, err := handler.(safe.TradeSafeService).CheckAmountByContStruct(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeSafeServiceCheckAmountByContStructArgs() interface{} {
	return safe.NewTradeSafeServiceCheckAmountByContStructArgs()
}

func newTradeSafeServiceCheckAmountByContStructResult() interface{} {
	return safe.NewTradeSafeServiceCheckAmountByContStructResult()
}

func checkContTotalAmountHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*safe.TradeSafeServiceCheckContTotalAmountArgs)
	realResult := result.(*safe.TradeSafeServiceCheckContTotalAmountResult)
	success, err := handler.(safe.TradeSafeService).CheckContTotalAmount(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeSafeServiceCheckContTotalAmountArgs() interface{} {
	return safe.NewTradeSafeServiceCheckContTotalAmountArgs()
}

func newTradeSafeServiceCheckContTotalAmountResult() interface{} {
	return safe.NewTradeSafeServiceCheckContTotalAmountResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) ExecRuleByBinlog(ctx context.Context, req *safe.ExecRuleByBinlogReq) (r *safe.ExecRuleResp, err error) {
	var _args safe.TradeSafeServiceExecRuleByBinlogArgs
	_args.Req = req
	var _result safe.TradeSafeServiceExecRuleByBinlogResult
	if err = p.c.Call(ctx, "ExecRuleByBinlog", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MultiStreamCheck(ctx context.Context, req *safe.MultiStreamCheckReq) (r *safe.MultiStreamCheckResp, err error) {
	var _args safe.TradeSafeServiceMultiStreamCheckArgs
	_args.Req = req
	var _result safe.TradeSafeServiceMultiStreamCheckResult
	if err = p.c.Call(ctx, "MultiStreamCheck", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Advise(ctx context.Context, req *safe.AdviseReq) (r *safe.AdviseResp, err error) {
	var _args safe.TradeSafeServiceAdviseArgs
	_args.Req = req
	var _result safe.TradeSafeServiceAdviseResult
	if err = p.c.Call(ctx, "Advise", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ScheduleCallback(ctx context.Context, req *safe.ScheduleCallbackReq) (r *safe.ScheduleCallbackResp, err error) {
	var _args safe.TradeSafeServiceScheduleCallbackArgs
	_args.Req = req
	var _result safe.TradeSafeServiceScheduleCallbackResult
	if err = p.c.Call(ctx, "ScheduleCallback", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) OperateRule(ctx context.Context, req *safe.OperateRuleReq) (r *safe.OperateRuleResp, err error) {
	var _args safe.TradeSafeServiceOperateRuleArgs
	_args.Req = req
	var _result safe.TradeSafeServiceOperateRuleResult
	if err = p.c.Call(ctx, "OperateRule", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateOrUpdateDrill(ctx context.Context, req *safe.CreateOrUpdateDrillReq) (r *safe.CreateOrUpdateDrillResp, err error) {
	var _args safe.TradeSafeServiceCreateOrUpdateDrillArgs
	_args.Req = req
	var _result safe.TradeSafeServiceCreateOrUpdateDrillResult
	if err = p.c.Call(ctx, "CreateOrUpdateDrill", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Drill(ctx context.Context, req *safe.DrillReq) (r *safe.DrillResp, err error) {
	var _args safe.TradeSafeServiceDrillArgs
	_args.Req = req
	var _result safe.TradeSafeServiceDrillResult
	if err = p.c.Call(ctx, "Drill", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DailyReport(ctx context.Context, req *safe.DailyReportReq) (r *safe.DailyReportResp, err error) {
	var _args safe.TradeSafeServiceDailyReportArgs
	_args.Req = req
	var _result safe.TradeSafeServiceDailyReportResult
	if err = p.c.Call(ctx, "DailyReport", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CheckAmountByContStruct(ctx context.Context, req *safe.CheckAmountByContStructReq) (r *safe.CheckAmountByContStructResp, err error) {
	var _args safe.TradeSafeServiceCheckAmountByContStructArgs
	_args.Req = req
	var _result safe.TradeSafeServiceCheckAmountByContStructResult
	if err = p.c.Call(ctx, "CheckAmountByContStruct", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CheckContTotalAmount(ctx context.Context, req *safe.CheckContTotalAmountReq) (r *safe.CheckContTotalAmountResp, err error) {
	var _args safe.TradeSafeServiceCheckContTotalAmountArgs
	_args.Req = req
	var _result safe.TradeSafeServiceCheckContTotalAmountResult
	if err = p.c.Call(ctx, "CheckContTotalAmount", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
