// Code generated by Kitex v1.20.3. DO NOT EDIT.

package tradesafeservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	safe "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/safe"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	ExecRuleByBinlog(ctx context.Context, req *safe.ExecRuleByBinlogReq, callOptions ...callopt.Option) (r *safe.ExecRuleResp, err error)
	MultiStreamCheck(ctx context.Context, req *safe.MultiStreamCheckReq, callOptions ...callopt.Option) (r *safe.MultiStreamCheckResp, err error)
	Advise(ctx context.Context, req *safe.AdviseReq, callOptions ...callopt.Option) (r *safe.AdviseResp, err error)
	ScheduleCallback(ctx context.Context, req *safe.ScheduleCallbackReq, callOptions ...callopt.Option) (r *safe.ScheduleCallbackResp, err error)
	OperateRule(ctx context.Context, req *safe.OperateRuleReq, callOptions ...callopt.Option) (r *safe.OperateRuleResp, err error)
	CreateOrUpdateDrill(ctx context.Context, req *safe.CreateOrUpdateDrillReq, callOptions ...callopt.Option) (r *safe.CreateOrUpdateDrillResp, err error)
	Drill(ctx context.Context, req *safe.DrillReq, callOptions ...callopt.Option) (r *safe.DrillResp, err error)
	DailyReport(ctx context.Context, req *safe.DailyReportReq, callOptions ...callopt.Option) (r *safe.DailyReportResp, err error)
	CheckAmountByContStruct(ctx context.Context, req *safe.CheckAmountByContStructReq, callOptions ...callopt.Option) (r *safe.CheckAmountByContStructResp, err error)
	CheckContTotalAmount(ctx context.Context, req *safe.CheckContTotalAmountReq, callOptions ...callopt.Option) (r *safe.CheckContTotalAmountResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kTradeSafeServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kTradeSafeServiceClient struct {
	*kClient
}

func (p *kTradeSafeServiceClient) ExecRuleByBinlog(ctx context.Context, req *safe.ExecRuleByBinlogReq, callOptions ...callopt.Option) (r *safe.ExecRuleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ExecRuleByBinlog(ctx, req)
}

func (p *kTradeSafeServiceClient) MultiStreamCheck(ctx context.Context, req *safe.MultiStreamCheckReq, callOptions ...callopt.Option) (r *safe.MultiStreamCheckResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MultiStreamCheck(ctx, req)
}

func (p *kTradeSafeServiceClient) Advise(ctx context.Context, req *safe.AdviseReq, callOptions ...callopt.Option) (r *safe.AdviseResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Advise(ctx, req)
}

func (p *kTradeSafeServiceClient) ScheduleCallback(ctx context.Context, req *safe.ScheduleCallbackReq, callOptions ...callopt.Option) (r *safe.ScheduleCallbackResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ScheduleCallback(ctx, req)
}

func (p *kTradeSafeServiceClient) OperateRule(ctx context.Context, req *safe.OperateRuleReq, callOptions ...callopt.Option) (r *safe.OperateRuleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.OperateRule(ctx, req)
}

func (p *kTradeSafeServiceClient) CreateOrUpdateDrill(ctx context.Context, req *safe.CreateOrUpdateDrillReq, callOptions ...callopt.Option) (r *safe.CreateOrUpdateDrillResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateOrUpdateDrill(ctx, req)
}

func (p *kTradeSafeServiceClient) Drill(ctx context.Context, req *safe.DrillReq, callOptions ...callopt.Option) (r *safe.DrillResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Drill(ctx, req)
}

func (p *kTradeSafeServiceClient) DailyReport(ctx context.Context, req *safe.DailyReportReq, callOptions ...callopt.Option) (r *safe.DailyReportResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DailyReport(ctx, req)
}

func (p *kTradeSafeServiceClient) CheckAmountByContStruct(ctx context.Context, req *safe.CheckAmountByContStructReq, callOptions ...callopt.Option) (r *safe.CheckAmountByContStructResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CheckAmountByContStruct(ctx, req)
}

func (p *kTradeSafeServiceClient) CheckContTotalAmount(ctx context.Context, req *safe.CheckContTotalAmountReq, callOptions ...callopt.Option) (r *safe.CheckContTotalAmountResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CheckContTotalAmount(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kTradeSafeServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
