// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package fee

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
)

type FormulaType int64

const (
	FormulaType_ReconcileType        FormulaType = 11
	FormulaType_ReverseReconcileType FormulaType = 12
)

func (p FormulaType) String() string {
	switch p {
	case FormulaType_ReconcileType:
		return "ReconcileType"
	case FormulaType_ReverseReconcileType:
		return "ReverseReconcileType"
	}
	return "<UNSET>"
}

func FormulaTypeFromString(s string) (FormulaType, error) {
	switch s {
	case "ReconcileType":
		return FormulaType_ReconcileType, nil
	case "ReverseReconcileType":
		return FormulaType_ReverseReconcileType, nil
	}
	return FormulaType(0), fmt.Errorf("not a valid FormulaType string")
}

func FormulaTypePtr(v FormulaType) *FormulaType { return &v }
func (p *FormulaType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FormulaType(result.Int64)
	return
}

func (p *FormulaType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type FeeRuleType int64

const (
	FeeRuleType_Settle    FeeRuleType = 1
	FeeRuleType_Refund    FeeRuleType = 2
	FeeRuleType_Reconcile FeeRuleType = 10
	FeeRuleType_Invoice   FeeRuleType = 11
)

func (p FeeRuleType) String() string {
	switch p {
	case FeeRuleType_Settle:
		return "Settle"
	case FeeRuleType_Refund:
		return "Refund"
	case FeeRuleType_Reconcile:
		return "Reconcile"
	case FeeRuleType_Invoice:
		return "Invoice"
	}
	return "<UNSET>"
}

func FeeRuleTypeFromString(s string) (FeeRuleType, error) {
	switch s {
	case "Settle":
		return FeeRuleType_Settle, nil
	case "Refund":
		return FeeRuleType_Refund, nil
	case "Reconcile":
		return FeeRuleType_Reconcile, nil
	case "Invoice":
		return FeeRuleType_Invoice, nil
	}
	return FeeRuleType(0), fmt.Errorf("not a valid FeeRuleType string")
}

func FeeRuleTypePtr(v FeeRuleType) *FeeRuleType { return &v }
func (p *FeeRuleType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FeeRuleType(result.Int64)
	return
}

func (p *FeeRuleType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type FeeItem struct {
	ID          int64  `thrift:"id,1" frugal:"1,default,i64" json:"id"`
	FeeID       string `thrift:"fee_id,2" frugal:"2,default,string" json:"fee_id"`
	FeeName     string `thrift:"fee_name,3" frugal:"3,default,string" json:"fee_name"`
	FeeDesc     string `thrift:"fee_desc,4" frugal:"4,default,string" json:"fee_desc"`
	CreatorName string `thrift:"creator_name,5" frugal:"5,default,string" json:"creator_name"`
	CreateTime  int64  `thrift:"create_time,6" frugal:"6,default,i64" json:"create_time"`
	UpdateTime  int64  `thrift:"update_time,7" frugal:"7,default,i64" json:"update_time"`
	IsDeleted   bool   `thrift:"is_deleted,10" frugal:"10,default,bool" json:"is_deleted"`
}

func NewFeeItem() *FeeItem {
	return &FeeItem{}
}

func (p *FeeItem) InitDefault() {
}

func (p *FeeItem) GetID() (v int64) {
	return p.ID
}

func (p *FeeItem) GetFeeID() (v string) {
	return p.FeeID
}

func (p *FeeItem) GetFeeName() (v string) {
	return p.FeeName
}

func (p *FeeItem) GetFeeDesc() (v string) {
	return p.FeeDesc
}

func (p *FeeItem) GetCreatorName() (v string) {
	return p.CreatorName
}

func (p *FeeItem) GetCreateTime() (v int64) {
	return p.CreateTime
}

func (p *FeeItem) GetUpdateTime() (v int64) {
	return p.UpdateTime
}

func (p *FeeItem) GetIsDeleted() (v bool) {
	return p.IsDeleted
}
func (p *FeeItem) SetID(val int64) {
	p.ID = val
}
func (p *FeeItem) SetFeeID(val string) {
	p.FeeID = val
}
func (p *FeeItem) SetFeeName(val string) {
	p.FeeName = val
}
func (p *FeeItem) SetFeeDesc(val string) {
	p.FeeDesc = val
}
func (p *FeeItem) SetCreatorName(val string) {
	p.CreatorName = val
}
func (p *FeeItem) SetCreateTime(val int64) {
	p.CreateTime = val
}
func (p *FeeItem) SetUpdateTime(val int64) {
	p.UpdateTime = val
}
func (p *FeeItem) SetIsDeleted(val bool) {
	p.IsDeleted = val
}

var fieldIDToName_FeeItem = map[int16]string{
	1:  "id",
	2:  "fee_id",
	3:  "fee_name",
	4:  "fee_desc",
	5:  "creator_name",
	6:  "create_time",
	7:  "update_time",
	10: "is_deleted",
}

func (p *FeeItem) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeItem")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeItem) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *FeeItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FeeID = _field
	return nil
}
func (p *FeeItem) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FeeName = _field
	return nil
}
func (p *FeeItem) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FeeDesc = _field
	return nil
}
func (p *FeeItem) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorName = _field
	return nil
}
func (p *FeeItem) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *FeeItem) ReadField7(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *FeeItem) ReadField10(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsDeleted = _field
	return nil
}

func (p *FeeItem) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("FeeItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FeeItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fee_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FeeID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *FeeItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fee_name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FeeName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *FeeItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fee_desc", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FeeDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *FeeItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_name", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreatorName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *FeeItem) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("create_time", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *FeeItem) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("update_time", thrift.I64, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *FeeItem) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("is_deleted", thrift.BOOL, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsDeleted); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *FeeItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeItem(%+v)", *p)

}

type QueryFeeItemReq struct {
	FeeIDList       []string   `thrift:"fee_id_list,1,optional" frugal:"1,optional,list<string>" json:"fee_id_list,omitempty"`
	ContainsDeleted bool       `thrift:"contains_deleted,10" frugal:"10,default,bool" json:"contains_deleted"`
	Offset          int32      `thrift:"offset,250" frugal:"250,default,i32" json:"offset"`
	Count           int32      `thrift:"count,251" frugal:"251,default,i32" json:"count"`
	Base            *base.Base `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewQueryFeeItemReq() *QueryFeeItemReq {
	return &QueryFeeItemReq{}
}

func (p *QueryFeeItemReq) InitDefault() {
}

var QueryFeeItemReq_FeeIDList_DEFAULT []string

func (p *QueryFeeItemReq) GetFeeIDList() (v []string) {
	if !p.IsSetFeeIDList() {
		return QueryFeeItemReq_FeeIDList_DEFAULT
	}
	return p.FeeIDList
}

func (p *QueryFeeItemReq) GetContainsDeleted() (v bool) {
	return p.ContainsDeleted
}

func (p *QueryFeeItemReq) GetOffset() (v int32) {
	return p.Offset
}

func (p *QueryFeeItemReq) GetCount() (v int32) {
	return p.Count
}

var QueryFeeItemReq_Base_DEFAULT *base.Base

func (p *QueryFeeItemReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return QueryFeeItemReq_Base_DEFAULT
	}
	return p.Base
}
func (p *QueryFeeItemReq) SetFeeIDList(val []string) {
	p.FeeIDList = val
}
func (p *QueryFeeItemReq) SetContainsDeleted(val bool) {
	p.ContainsDeleted = val
}
func (p *QueryFeeItemReq) SetOffset(val int32) {
	p.Offset = val
}
func (p *QueryFeeItemReq) SetCount(val int32) {
	p.Count = val
}
func (p *QueryFeeItemReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_QueryFeeItemReq = map[int16]string{
	1:   "fee_id_list",
	10:  "contains_deleted",
	250: "offset",
	251: "count",
	255: "Base",
}

func (p *QueryFeeItemReq) IsSetFeeIDList() bool {
	return p.FeeIDList != nil
}

func (p *QueryFeeItemReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *QueryFeeItemReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeItemReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 250:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField250(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 251:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField251(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeItemReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *QueryFeeItemReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FeeIDList = _field
	return nil
}
func (p *QueryFeeItemReq) ReadField10(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContainsDeleted = _field
	return nil
}
func (p *QueryFeeItemReq) ReadField250(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Offset = _field
	return nil
}
func (p *QueryFeeItemReq) ReadField251(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Count = _field
	return nil
}
func (p *QueryFeeItemReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *QueryFeeItemReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeItemReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeItemReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField250(oprot); err != nil {
			fieldId = 250
			goto WriteFieldError
		}
		if err = p.writeField251(oprot); err != nil {
			fieldId = 251
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *QueryFeeItemReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetFeeIDList() {
		if err = oprot.WriteFieldBegin("fee_id_list", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.FeeIDList)); err != nil {
			return err
		}
		for _, v := range p.FeeIDList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *QueryFeeItemReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("contains_deleted", thrift.BOOL, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.ContainsDeleted); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *QueryFeeItemReq) writeField250(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("offset", thrift.I32, 250); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Offset); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 250 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 250 end error: ", p), err)
}
func (p *QueryFeeItemReq) writeField251(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("count", thrift.I32, 251); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Count); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 251 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 251 end error: ", p), err)
}
func (p *QueryFeeItemReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *QueryFeeItemReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryFeeItemReq(%+v)", *p)

}

type QueryFeeItemResp struct {
	DataList   []*FeeItem     `thrift:"data_list,1" frugal:"1,default,list<FeeItem>" json:"data_list"`
	TotalCount int64          `thrift:"total_count,250" frugal:"250,default,i64" json:"total_count"`
	HasMore    bool           `thrift:"has_more,251" frugal:"251,default,bool" json:"has_more"`
	BaseResp   *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewQueryFeeItemResp() *QueryFeeItemResp {
	return &QueryFeeItemResp{}
}

func (p *QueryFeeItemResp) InitDefault() {
}

func (p *QueryFeeItemResp) GetDataList() (v []*FeeItem) {
	return p.DataList
}

func (p *QueryFeeItemResp) GetTotalCount() (v int64) {
	return p.TotalCount
}

func (p *QueryFeeItemResp) GetHasMore() (v bool) {
	return p.HasMore
}

var QueryFeeItemResp_BaseResp_DEFAULT *base.BaseResp

func (p *QueryFeeItemResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return QueryFeeItemResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *QueryFeeItemResp) SetDataList(val []*FeeItem) {
	p.DataList = val
}
func (p *QueryFeeItemResp) SetTotalCount(val int64) {
	p.TotalCount = val
}
func (p *QueryFeeItemResp) SetHasMore(val bool) {
	p.HasMore = val
}
func (p *QueryFeeItemResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_QueryFeeItemResp = map[int16]string{
	1:   "data_list",
	250: "total_count",
	251: "has_more",
	255: "BaseResp",
}

func (p *QueryFeeItemResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *QueryFeeItemResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeItemResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 250:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField250(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 251:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField251(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeItemResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *QueryFeeItemResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FeeItem, 0, size)
	values := make([]FeeItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DataList = _field
	return nil
}
func (p *QueryFeeItemResp) ReadField250(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalCount = _field
	return nil
}
func (p *QueryFeeItemResp) ReadField251(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasMore = _field
	return nil
}
func (p *QueryFeeItemResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *QueryFeeItemResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeItemResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeItemResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField250(oprot); err != nil {
			fieldId = 250
			goto WriteFieldError
		}
		if err = p.writeField251(oprot); err != nil {
			fieldId = 251
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *QueryFeeItemResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("data_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DataList)); err != nil {
		return err
	}
	for _, v := range p.DataList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *QueryFeeItemResp) writeField250(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total_count", thrift.I64, 250); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TotalCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 250 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 250 end error: ", p), err)
}
func (p *QueryFeeItemResp) writeField251(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("has_more", thrift.BOOL, 251); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasMore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 251 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 251 end error: ", p), err)
}
func (p *QueryFeeItemResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *QueryFeeItemResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryFeeItemResp(%+v)", *p)

}

type UpsertFeeItemReq struct {
	ID           int64      `thrift:"id,1" frugal:"1,default,i64" json:"id"`
	FeeID        string     `thrift:"fee_id,2" frugal:"2,default,string" json:"fee_id"`
	FeeName      *string    `thrift:"fee_name,3,optional" frugal:"3,optional,string" json:"fee_name,omitempty"`
	FeeDesc      *string    `thrift:"fee_desc,4,optional" frugal:"4,optional,string" json:"fee_desc,omitempty"`
	CreatorName  string     `thrift:"creator_name,10,required" frugal:"10,required,string" json:"creator_name"`
	OperatorName string     `thrift:"operator_name,11,required" frugal:"11,required,string" json:"operator_name"`
	Base         *base.Base `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewUpsertFeeItemReq() *UpsertFeeItemReq {
	return &UpsertFeeItemReq{}
}

func (p *UpsertFeeItemReq) InitDefault() {
}

func (p *UpsertFeeItemReq) GetID() (v int64) {
	return p.ID
}

func (p *UpsertFeeItemReq) GetFeeID() (v string) {
	return p.FeeID
}

var UpsertFeeItemReq_FeeName_DEFAULT string

func (p *UpsertFeeItemReq) GetFeeName() (v string) {
	if !p.IsSetFeeName() {
		return UpsertFeeItemReq_FeeName_DEFAULT
	}
	return *p.FeeName
}

var UpsertFeeItemReq_FeeDesc_DEFAULT string

func (p *UpsertFeeItemReq) GetFeeDesc() (v string) {
	if !p.IsSetFeeDesc() {
		return UpsertFeeItemReq_FeeDesc_DEFAULT
	}
	return *p.FeeDesc
}

func (p *UpsertFeeItemReq) GetCreatorName() (v string) {
	return p.CreatorName
}

func (p *UpsertFeeItemReq) GetOperatorName() (v string) {
	return p.OperatorName
}

var UpsertFeeItemReq_Base_DEFAULT *base.Base

func (p *UpsertFeeItemReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return UpsertFeeItemReq_Base_DEFAULT
	}
	return p.Base
}
func (p *UpsertFeeItemReq) SetID(val int64) {
	p.ID = val
}
func (p *UpsertFeeItemReq) SetFeeID(val string) {
	p.FeeID = val
}
func (p *UpsertFeeItemReq) SetFeeName(val *string) {
	p.FeeName = val
}
func (p *UpsertFeeItemReq) SetFeeDesc(val *string) {
	p.FeeDesc = val
}
func (p *UpsertFeeItemReq) SetCreatorName(val string) {
	p.CreatorName = val
}
func (p *UpsertFeeItemReq) SetOperatorName(val string) {
	p.OperatorName = val
}
func (p *UpsertFeeItemReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_UpsertFeeItemReq = map[int16]string{
	1:   "id",
	2:   "fee_id",
	3:   "fee_name",
	4:   "fee_desc",
	10:  "creator_name",
	11:  "operator_name",
	255: "Base",
}

func (p *UpsertFeeItemReq) IsSetFeeName() bool {
	return p.FeeName != nil
}

func (p *UpsertFeeItemReq) IsSetFeeDesc() bool {
	return p.FeeDesc != nil
}

func (p *UpsertFeeItemReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *UpsertFeeItemReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeItemReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCreatorName bool = false
	var issetOperatorName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreatorName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperatorName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCreatorName {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetOperatorName {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeItemReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpsertFeeItemReq[fieldId]))
}

func (p *UpsertFeeItemReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *UpsertFeeItemReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FeeID = _field
	return nil
}
func (p *UpsertFeeItemReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FeeName = _field
	return nil
}
func (p *UpsertFeeItemReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FeeDesc = _field
	return nil
}
func (p *UpsertFeeItemReq) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorName = _field
	return nil
}
func (p *UpsertFeeItemReq) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OperatorName = _field
	return nil
}
func (p *UpsertFeeItemReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *UpsertFeeItemReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeItemReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeItemReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpsertFeeItemReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpsertFeeItemReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fee_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FeeID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpsertFeeItemReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFeeName() {
		if err = oprot.WriteFieldBegin("fee_name", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FeeName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UpsertFeeItemReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFeeDesc() {
		if err = oprot.WriteFieldBegin("fee_desc", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FeeDesc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UpsertFeeItemReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_name", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreatorName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *UpsertFeeItemReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator_name", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OperatorName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *UpsertFeeItemReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpsertFeeItemReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpsertFeeItemReq(%+v)", *p)

}

type UpsertFeeItemResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewUpsertFeeItemResp() *UpsertFeeItemResp {
	return &UpsertFeeItemResp{}
}

func (p *UpsertFeeItemResp) InitDefault() {
}

var UpsertFeeItemResp_BaseResp_DEFAULT *base.BaseResp

func (p *UpsertFeeItemResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return UpsertFeeItemResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *UpsertFeeItemResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_UpsertFeeItemResp = map[int16]string{
	255: "BaseResp",
}

func (p *UpsertFeeItemResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *UpsertFeeItemResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeItemResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeItemResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpsertFeeItemResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *UpsertFeeItemResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeItemResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeItemResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpsertFeeItemResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpsertFeeItemResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpsertFeeItemResp(%+v)", *p)

}

type DeleteFeeItemReq struct {
	ID           int64      `thrift:"id,1" frugal:"1,default,i64" json:"id"`
	OperatorName string     `thrift:"operator_name,11,required" frugal:"11,required,string" json:"operator_name"`
	Base         *base.Base `thrift:"Base,255" frugal:"255,default,base.Base" json:"Base"`
}

func NewDeleteFeeItemReq() *DeleteFeeItemReq {
	return &DeleteFeeItemReq{}
}

func (p *DeleteFeeItemReq) InitDefault() {
}

func (p *DeleteFeeItemReq) GetID() (v int64) {
	return p.ID
}

func (p *DeleteFeeItemReq) GetOperatorName() (v string) {
	return p.OperatorName
}

var DeleteFeeItemReq_Base_DEFAULT *base.Base

func (p *DeleteFeeItemReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return DeleteFeeItemReq_Base_DEFAULT
	}
	return p.Base
}
func (p *DeleteFeeItemReq) SetID(val int64) {
	p.ID = val
}
func (p *DeleteFeeItemReq) SetOperatorName(val string) {
	p.OperatorName = val
}
func (p *DeleteFeeItemReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_DeleteFeeItemReq = map[int16]string{
	1:   "id",
	11:  "operator_name",
	255: "Base",
}

func (p *DeleteFeeItemReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *DeleteFeeItemReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeItemReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOperatorName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperatorName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOperatorName {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeItemReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteFeeItemReq[fieldId]))
}

func (p *DeleteFeeItemReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *DeleteFeeItemReq) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OperatorName = _field
	return nil
}
func (p *DeleteFeeItemReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *DeleteFeeItemReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeItemReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeItemReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteFeeItemReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DeleteFeeItemReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator_name", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OperatorName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *DeleteFeeItemReq) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DeleteFeeItemReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFeeItemReq(%+v)", *p)

}

type DeleteFeeItemResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewDeleteFeeItemResp() *DeleteFeeItemResp {
	return &DeleteFeeItemResp{}
}

func (p *DeleteFeeItemResp) InitDefault() {
}

var DeleteFeeItemResp_BaseResp_DEFAULT *base.BaseResp

func (p *DeleteFeeItemResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return DeleteFeeItemResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *DeleteFeeItemResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_DeleteFeeItemResp = map[int16]string{
	255: "BaseResp",
}

func (p *DeleteFeeItemResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *DeleteFeeItemResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeItemResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeItemResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteFeeItemResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *DeleteFeeItemResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeItemResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeItemResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteFeeItemResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DeleteFeeItemResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFeeItemResp(%+v)", *p)

}

type FeeFormula struct {
	FormulaID       int64                     `thrift:"formula_id,1" frugal:"1,default,i64" json:"formula_id"`
	FormulaName     string                    `thrift:"formula_name,2" frugal:"2,default,string" json:"formula_name"`
	FromRoleType    fwe_trade_common.RoleType `thrift:"from_role_type,3" frugal:"3,default,RoleType" json:"from_role_type"`
	ToRoleType      fwe_trade_common.RoleType `thrift:"to_role_type,4" frugal:"4,default,RoleType" json:"to_role_type"`
	Expression      string                    `thrift:"expression,5" frugal:"5,default,string" json:"expression"`
	RuleID          int64                     `thrift:"rule_id,6" frugal:"6,default,i64" json:"rule_id"`
	FeeItemNameList []string                  `thrift:"fee_item_name_list,7" frugal:"7,default,list<string>" json:"fee_item_name_list"`
	EaConfig        string                    `thrift:"ea_config,8" frugal:"8,default,string" json:"ea_config"`
	FormulaType     FormulaType               `thrift:"formula_type,9" frugal:"9,default,FormulaType" json:"formula_type"`
	AutoWithdraw    bool                      `thrift:"auto_withdraw,10" frugal:"10,default,bool" json:"auto_withdraw"`
	CreatorName     string                    `thrift:"creator_name,101" frugal:"101,default,string" json:"creator_name"`
	CreateTime      int64                     `thrift:"create_time,102" frugal:"102,default,i64" json:"create_time"`
	UpdateTime      int64                     `thrift:"update_time,103" frugal:"103,default,i64" json:"update_time"`
}

func NewFeeFormula() *FeeFormula {
	return &FeeFormula{}
}

func (p *FeeFormula) InitDefault() {
}

func (p *FeeFormula) GetFormulaID() (v int64) {
	return p.FormulaID
}

func (p *FeeFormula) GetFormulaName() (v string) {
	return p.FormulaName
}

func (p *FeeFormula) GetFromRoleType() (v fwe_trade_common.RoleType) {
	return p.FromRoleType
}

func (p *FeeFormula) GetToRoleType() (v fwe_trade_common.RoleType) {
	return p.ToRoleType
}

func (p *FeeFormula) GetExpression() (v string) {
	return p.Expression
}

func (p *FeeFormula) GetRuleID() (v int64) {
	return p.RuleID
}

func (p *FeeFormula) GetFeeItemNameList() (v []string) {
	return p.FeeItemNameList
}

func (p *FeeFormula) GetEaConfig() (v string) {
	return p.EaConfig
}

func (p *FeeFormula) GetFormulaType() (v FormulaType) {
	return p.FormulaType
}

func (p *FeeFormula) GetAutoWithdraw() (v bool) {
	return p.AutoWithdraw
}

func (p *FeeFormula) GetCreatorName() (v string) {
	return p.CreatorName
}

func (p *FeeFormula) GetCreateTime() (v int64) {
	return p.CreateTime
}

func (p *FeeFormula) GetUpdateTime() (v int64) {
	return p.UpdateTime
}
func (p *FeeFormula) SetFormulaID(val int64) {
	p.FormulaID = val
}
func (p *FeeFormula) SetFormulaName(val string) {
	p.FormulaName = val
}
func (p *FeeFormula) SetFromRoleType(val fwe_trade_common.RoleType) {
	p.FromRoleType = val
}
func (p *FeeFormula) SetToRoleType(val fwe_trade_common.RoleType) {
	p.ToRoleType = val
}
func (p *FeeFormula) SetExpression(val string) {
	p.Expression = val
}
func (p *FeeFormula) SetRuleID(val int64) {
	p.RuleID = val
}
func (p *FeeFormula) SetFeeItemNameList(val []string) {
	p.FeeItemNameList = val
}
func (p *FeeFormula) SetEaConfig(val string) {
	p.EaConfig = val
}
func (p *FeeFormula) SetFormulaType(val FormulaType) {
	p.FormulaType = val
}
func (p *FeeFormula) SetAutoWithdraw(val bool) {
	p.AutoWithdraw = val
}
func (p *FeeFormula) SetCreatorName(val string) {
	p.CreatorName = val
}
func (p *FeeFormula) SetCreateTime(val int64) {
	p.CreateTime = val
}
func (p *FeeFormula) SetUpdateTime(val int64) {
	p.UpdateTime = val
}

var fieldIDToName_FeeFormula = map[int16]string{
	1:   "formula_id",
	2:   "formula_name",
	3:   "from_role_type",
	4:   "to_role_type",
	5:   "expression",
	6:   "rule_id",
	7:   "fee_item_name_list",
	8:   "ea_config",
	9:   "formula_type",
	10:  "auto_withdraw",
	101: "creator_name",
	102: "create_time",
	103: "update_time",
}

func (p *FeeFormula) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeFormula")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 102:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField102(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 103:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField103(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeFormula[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeFormula) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FormulaID = _field
	return nil
}
func (p *FeeFormula) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FormulaName = _field
	return nil
}
func (p *FeeFormula) ReadField3(iprot thrift.TProtocol) error {

	var _field fwe_trade_common.RoleType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = fwe_trade_common.RoleType(v)
	}
	p.FromRoleType = _field
	return nil
}
func (p *FeeFormula) ReadField4(iprot thrift.TProtocol) error {

	var _field fwe_trade_common.RoleType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = fwe_trade_common.RoleType(v)
	}
	p.ToRoleType = _field
	return nil
}
func (p *FeeFormula) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Expression = _field
	return nil
}
func (p *FeeFormula) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleID = _field
	return nil
}
func (p *FeeFormula) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FeeItemNameList = _field
	return nil
}
func (p *FeeFormula) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EaConfig = _field
	return nil
}
func (p *FeeFormula) ReadField9(iprot thrift.TProtocol) error {

	var _field FormulaType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = FormulaType(v)
	}
	p.FormulaType = _field
	return nil
}
func (p *FeeFormula) ReadField10(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AutoWithdraw = _field
	return nil
}
func (p *FeeFormula) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorName = _field
	return nil
}
func (p *FeeFormula) ReadField102(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *FeeFormula) ReadField103(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}

func (p *FeeFormula) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeFormula")

	var fieldId int16
	if err = oprot.WriteStructBegin("FeeFormula"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField102(oprot); err != nil {
			fieldId = 102
			goto WriteFieldError
		}
		if err = p.writeField103(oprot); err != nil {
			fieldId = 103
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeFormula) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("formula_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.FormulaID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FeeFormula) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("formula_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FormulaName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *FeeFormula) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("from_role_type", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.FromRoleType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *FeeFormula) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("to_role_type", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ToRoleType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *FeeFormula) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("expression", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Expression); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *FeeFormula) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_id", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RuleID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *FeeFormula) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fee_item_name_list", thrift.LIST, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.FeeItemNameList)); err != nil {
		return err
	}
	for _, v := range p.FeeItemNameList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *FeeFormula) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ea_config", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EaConfig); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *FeeFormula) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("formula_type", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.FormulaType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *FeeFormula) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("auto_withdraw", thrift.BOOL, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.AutoWithdraw); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *FeeFormula) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_name", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreatorName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *FeeFormula) writeField102(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("create_time", thrift.I64, 102); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 end error: ", p), err)
}
func (p *FeeFormula) writeField103(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("update_time", thrift.I64, 103); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 103 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 103 end error: ", p), err)
}

func (p *FeeFormula) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeFormula(%+v)", *p)

}

type RuleAttribute struct {
	AfterSettleRefund bool `thrift:"after_settle_refund,1" frugal:"1,default,bool" json:"after_settle_refund"`
}

func NewRuleAttribute() *RuleAttribute {
	return &RuleAttribute{}
}

func (p *RuleAttribute) InitDefault() {
}

func (p *RuleAttribute) GetAfterSettleRefund() (v bool) {
	return p.AfterSettleRefund
}
func (p *RuleAttribute) SetAfterSettleRefund(val bool) {
	p.AfterSettleRefund = val
}

var fieldIDToName_RuleAttribute = map[int16]string{
	1: "after_settle_refund",
}

func (p *RuleAttribute) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RuleAttribute")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RuleAttribute[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RuleAttribute) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AfterSettleRefund = _field
	return nil
}

func (p *RuleAttribute) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RuleAttribute")

	var fieldId int16
	if err = oprot.WriteStructBegin("RuleAttribute"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RuleAttribute) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("after_settle_refund", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.AfterSettleRefund); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RuleAttribute) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RuleAttribute(%+v)", *p)

}

type FeeRule struct {
	RuleID              int64          `thrift:"rule_id,1" frugal:"1,default,i64" json:"rule_id"`
	RuleName            string         `thrift:"rule_name,2" frugal:"2,default,string" json:"rule_name"`
	RuleDesc            string         `thrift:"rule_desc,3" frugal:"3,default,string" json:"rule_desc"`
	RuleType            FeeRuleType    `thrift:"rule_type,4" frugal:"4,default,FeeRuleType" json:"rule_type"`
	FormulaList         []*FeeFormula  `thrift:"formula_list,5" frugal:"5,default,list<FeeFormula>" json:"formula_list"`
	IncludeConsumerLoan bool           `thrift:"include_consumer_loan,6" frugal:"6,default,bool" json:"include_consumer_loan"`
	ConditionExpression string         `thrift:"condition_expression,7" frugal:"7,default,string" json:"condition_expression"`
	IsFullSettle        bool           `thrift:"is_full_settle,8" frugal:"8,default,bool" json:"is_full_settle"`
	RuleAttribute       *RuleAttribute `thrift:"rule_attribute,10" frugal:"10,default,RuleAttribute" json:"rule_attribute"`
	BizScene            int32          `thrift:"biz_scene,101" frugal:"101,default,i32" json:"biz_scene"`
	CreatorName         string         `thrift:"creator_name,102" frugal:"102,default,string" json:"creator_name"`
	CreateTime          int64          `thrift:"create_time,103" frugal:"103,default,i64" json:"create_time"`
	UpdateTime          int64          `thrift:"update_time,104" frugal:"104,default,i64" json:"update_time"`
	Status              string         `thrift:"status,105" frugal:"105,default,string" json:"status"`
}

func NewFeeRule() *FeeRule {
	return &FeeRule{}
}

func (p *FeeRule) InitDefault() {
}

func (p *FeeRule) GetRuleID() (v int64) {
	return p.RuleID
}

func (p *FeeRule) GetRuleName() (v string) {
	return p.RuleName
}

func (p *FeeRule) GetRuleDesc() (v string) {
	return p.RuleDesc
}

func (p *FeeRule) GetRuleType() (v FeeRuleType) {
	return p.RuleType
}

func (p *FeeRule) GetFormulaList() (v []*FeeFormula) {
	return p.FormulaList
}

func (p *FeeRule) GetIncludeConsumerLoan() (v bool) {
	return p.IncludeConsumerLoan
}

func (p *FeeRule) GetConditionExpression() (v string) {
	return p.ConditionExpression
}

func (p *FeeRule) GetIsFullSettle() (v bool) {
	return p.IsFullSettle
}

var FeeRule_RuleAttribute_DEFAULT *RuleAttribute

func (p *FeeRule) GetRuleAttribute() (v *RuleAttribute) {
	if !p.IsSetRuleAttribute() {
		return FeeRule_RuleAttribute_DEFAULT
	}
	return p.RuleAttribute
}

func (p *FeeRule) GetBizScene() (v int32) {
	return p.BizScene
}

func (p *FeeRule) GetCreatorName() (v string) {
	return p.CreatorName
}

func (p *FeeRule) GetCreateTime() (v int64) {
	return p.CreateTime
}

func (p *FeeRule) GetUpdateTime() (v int64) {
	return p.UpdateTime
}

func (p *FeeRule) GetStatus() (v string) {
	return p.Status
}
func (p *FeeRule) SetRuleID(val int64) {
	p.RuleID = val
}
func (p *FeeRule) SetRuleName(val string) {
	p.RuleName = val
}
func (p *FeeRule) SetRuleDesc(val string) {
	p.RuleDesc = val
}
func (p *FeeRule) SetRuleType(val FeeRuleType) {
	p.RuleType = val
}
func (p *FeeRule) SetFormulaList(val []*FeeFormula) {
	p.FormulaList = val
}
func (p *FeeRule) SetIncludeConsumerLoan(val bool) {
	p.IncludeConsumerLoan = val
}
func (p *FeeRule) SetConditionExpression(val string) {
	p.ConditionExpression = val
}
func (p *FeeRule) SetIsFullSettle(val bool) {
	p.IsFullSettle = val
}
func (p *FeeRule) SetRuleAttribute(val *RuleAttribute) {
	p.RuleAttribute = val
}
func (p *FeeRule) SetBizScene(val int32) {
	p.BizScene = val
}
func (p *FeeRule) SetCreatorName(val string) {
	p.CreatorName = val
}
func (p *FeeRule) SetCreateTime(val int64) {
	p.CreateTime = val
}
func (p *FeeRule) SetUpdateTime(val int64) {
	p.UpdateTime = val
}
func (p *FeeRule) SetStatus(val string) {
	p.Status = val
}

var fieldIDToName_FeeRule = map[int16]string{
	1:   "rule_id",
	2:   "rule_name",
	3:   "rule_desc",
	4:   "rule_type",
	5:   "formula_list",
	6:   "include_consumer_loan",
	7:   "condition_expression",
	8:   "is_full_settle",
	10:  "rule_attribute",
	101: "biz_scene",
	102: "creator_name",
	103: "create_time",
	104: "update_time",
	105: "status",
}

func (p *FeeRule) IsSetRuleAttribute() bool {
	return p.RuleAttribute != nil
}

func (p *FeeRule) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeRule")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 102:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField102(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 103:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField103(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 104:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField104(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 105:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField105(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeRule[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeRule) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleID = _field
	return nil
}
func (p *FeeRule) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleName = _field
	return nil
}
func (p *FeeRule) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleDesc = _field
	return nil
}
func (p *FeeRule) ReadField4(iprot thrift.TProtocol) error {

	var _field FeeRuleType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = FeeRuleType(v)
	}
	p.RuleType = _field
	return nil
}
func (p *FeeRule) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FeeFormula, 0, size)
	values := make([]FeeFormula, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FormulaList = _field
	return nil
}
func (p *FeeRule) ReadField6(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IncludeConsumerLoan = _field
	return nil
}
func (p *FeeRule) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConditionExpression = _field
	return nil
}
func (p *FeeRule) ReadField8(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsFullSettle = _field
	return nil
}
func (p *FeeRule) ReadField10(iprot thrift.TProtocol) error {
	_field := NewRuleAttribute()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RuleAttribute = _field
	return nil
}
func (p *FeeRule) ReadField101(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizScene = _field
	return nil
}
func (p *FeeRule) ReadField102(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorName = _field
	return nil
}
func (p *FeeRule) ReadField103(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *FeeRule) ReadField104(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *FeeRule) ReadField105(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Status = _field
	return nil
}

func (p *FeeRule) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeRule")

	var fieldId int16
	if err = oprot.WriteStructBegin("FeeRule"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField102(oprot); err != nil {
			fieldId = 102
			goto WriteFieldError
		}
		if err = p.writeField103(oprot); err != nil {
			fieldId = 103
			goto WriteFieldError
		}
		if err = p.writeField104(oprot); err != nil {
			fieldId = 104
			goto WriteFieldError
		}
		if err = p.writeField105(oprot); err != nil {
			fieldId = 105
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeRule) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RuleID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FeeRule) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *FeeRule) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_desc", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RuleDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *FeeRule) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_type", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.RuleType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *FeeRule) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("formula_list", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FormulaList)); err != nil {
		return err
	}
	for _, v := range p.FormulaList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *FeeRule) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("include_consumer_loan", thrift.BOOL, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IncludeConsumerLoan); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *FeeRule) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("condition_expression", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConditionExpression); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *FeeRule) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("is_full_settle", thrift.BOOL, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsFullSettle); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *FeeRule) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_attribute", thrift.STRUCT, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.RuleAttribute.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *FeeRule) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_scene", thrift.I32, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.BizScene); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *FeeRule) writeField102(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_name", thrift.STRING, 102); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreatorName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 end error: ", p), err)
}
func (p *FeeRule) writeField103(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("create_time", thrift.I64, 103); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 103 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 103 end error: ", p), err)
}
func (p *FeeRule) writeField104(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("update_time", thrift.I64, 104); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 104 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 104 end error: ", p), err)
}
func (p *FeeRule) writeField105(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("status", thrift.STRING, 105); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Status); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 105 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 105 end error: ", p), err)
}

func (p *FeeRule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeRule(%+v)", *p)

}

type QueryFeeRulesReq struct {
	BizScene int32      `thrift:"biz_scene,1,required" frugal:"1,required,i32" json:"biz_scene"`
	Base     *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewQueryFeeRulesReq() *QueryFeeRulesReq {
	return &QueryFeeRulesReq{}
}

func (p *QueryFeeRulesReq) InitDefault() {
}

func (p *QueryFeeRulesReq) GetBizScene() (v int32) {
	return p.BizScene
}

var QueryFeeRulesReq_Base_DEFAULT *base.Base

func (p *QueryFeeRulesReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return QueryFeeRulesReq_Base_DEFAULT
	}
	return p.Base
}
func (p *QueryFeeRulesReq) SetBizScene(val int32) {
	p.BizScene = val
}
func (p *QueryFeeRulesReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_QueryFeeRulesReq = map[int16]string{
	1:   "biz_scene",
	255: "Base",
}

func (p *QueryFeeRulesReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *QueryFeeRulesReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeRulesReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBizScene bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetBizScene = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBizScene {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeRulesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_QueryFeeRulesReq[fieldId]))
}

func (p *QueryFeeRulesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizScene = _field
	return nil
}
func (p *QueryFeeRulesReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *QueryFeeRulesReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeRulesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeRulesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *QueryFeeRulesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_scene", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.BizScene); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *QueryFeeRulesReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *QueryFeeRulesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryFeeRulesReq(%+v)", *p)

}

type QueryFeeRulesResp struct {
	RuleList []*FeeRule     `thrift:"rule_list,1" frugal:"1,default,list<FeeRule>" json:"rule_list"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewQueryFeeRulesResp() *QueryFeeRulesResp {
	return &QueryFeeRulesResp{}
}

func (p *QueryFeeRulesResp) InitDefault() {
}

func (p *QueryFeeRulesResp) GetRuleList() (v []*FeeRule) {
	return p.RuleList
}

var QueryFeeRulesResp_BaseResp_DEFAULT *base.BaseResp

func (p *QueryFeeRulesResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return QueryFeeRulesResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *QueryFeeRulesResp) SetRuleList(val []*FeeRule) {
	p.RuleList = val
}
func (p *QueryFeeRulesResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_QueryFeeRulesResp = map[int16]string{
	1:   "rule_list",
	255: "BaseResp",
}

func (p *QueryFeeRulesResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *QueryFeeRulesResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeRulesResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeRulesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *QueryFeeRulesResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FeeRule, 0, size)
	values := make([]FeeRule, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RuleList = _field
	return nil
}
func (p *QueryFeeRulesResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *QueryFeeRulesResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeRulesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeRulesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *QueryFeeRulesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RuleList)); err != nil {
		return err
	}
	for _, v := range p.RuleList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *QueryFeeRulesResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *QueryFeeRulesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryFeeRulesResp(%+v)", *p)

}

type UpsertFeeRuleReq struct {
	Rule     *FeeRule   `thrift:"rule,1,required" frugal:"1,required,FeeRule" json:"rule"`
	Operator string     `thrift:"operator,2,required" frugal:"2,required,string" json:"operator"`
	Base     *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewUpsertFeeRuleReq() *UpsertFeeRuleReq {
	return &UpsertFeeRuleReq{}
}

func (p *UpsertFeeRuleReq) InitDefault() {
}

var UpsertFeeRuleReq_Rule_DEFAULT *FeeRule

func (p *UpsertFeeRuleReq) GetRule() (v *FeeRule) {
	if !p.IsSetRule() {
		return UpsertFeeRuleReq_Rule_DEFAULT
	}
	return p.Rule
}

func (p *UpsertFeeRuleReq) GetOperator() (v string) {
	return p.Operator
}

var UpsertFeeRuleReq_Base_DEFAULT *base.Base

func (p *UpsertFeeRuleReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return UpsertFeeRuleReq_Base_DEFAULT
	}
	return p.Base
}
func (p *UpsertFeeRuleReq) SetRule(val *FeeRule) {
	p.Rule = val
}
func (p *UpsertFeeRuleReq) SetOperator(val string) {
	p.Operator = val
}
func (p *UpsertFeeRuleReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_UpsertFeeRuleReq = map[int16]string{
	1:   "rule",
	2:   "operator",
	255: "Base",
}

func (p *UpsertFeeRuleReq) IsSetRule() bool {
	return p.Rule != nil
}

func (p *UpsertFeeRuleReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *UpsertFeeRuleReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRule bool = false
	var issetOperator bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRule = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRule {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpsertFeeRuleReq[fieldId]))
}

func (p *UpsertFeeRuleReq) ReadField1(iprot thrift.TProtocol) error {
	_field := NewFeeRule()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Rule = _field
	return nil
}
func (p *UpsertFeeRuleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Operator = _field
	return nil
}
func (p *UpsertFeeRuleReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *UpsertFeeRuleReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpsertFeeRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Rule.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpsertFeeRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Operator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpsertFeeRuleReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpsertFeeRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpsertFeeRuleReq(%+v)", *p)

}

type UpsertFeeRuleResp struct {
	Rule     *FeeRule       `thrift:"rule,1" frugal:"1,default,FeeRule" json:"rule"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewUpsertFeeRuleResp() *UpsertFeeRuleResp {
	return &UpsertFeeRuleResp{}
}

func (p *UpsertFeeRuleResp) InitDefault() {
}

var UpsertFeeRuleResp_Rule_DEFAULT *FeeRule

func (p *UpsertFeeRuleResp) GetRule() (v *FeeRule) {
	if !p.IsSetRule() {
		return UpsertFeeRuleResp_Rule_DEFAULT
	}
	return p.Rule
}

var UpsertFeeRuleResp_BaseResp_DEFAULT *base.BaseResp

func (p *UpsertFeeRuleResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return UpsertFeeRuleResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *UpsertFeeRuleResp) SetRule(val *FeeRule) {
	p.Rule = val
}
func (p *UpsertFeeRuleResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_UpsertFeeRuleResp = map[int16]string{
	1:   "rule",
	255: "BaseResp",
}

func (p *UpsertFeeRuleResp) IsSetRule() bool {
	return p.Rule != nil
}

func (p *UpsertFeeRuleResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *UpsertFeeRuleResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeRuleResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpsertFeeRuleResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewFeeRule()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Rule = _field
	return nil
}
func (p *UpsertFeeRuleResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *UpsertFeeRuleResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeRuleResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpsertFeeRuleResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Rule.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpsertFeeRuleResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpsertFeeRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpsertFeeRuleResp(%+v)", *p)

}

type DeleteFeeRuleReq struct {
	RuleID   int64      `thrift:"rule_id,1,required" frugal:"1,required,i64" json:"rule_id"`
	Operator string     `thrift:"operator,2,required" frugal:"2,required,string" json:"operator"`
	Base     *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewDeleteFeeRuleReq() *DeleteFeeRuleReq {
	return &DeleteFeeRuleReq{}
}

func (p *DeleteFeeRuleReq) InitDefault() {
}

func (p *DeleteFeeRuleReq) GetRuleID() (v int64) {
	return p.RuleID
}

func (p *DeleteFeeRuleReq) GetOperator() (v string) {
	return p.Operator
}

var DeleteFeeRuleReq_Base_DEFAULT *base.Base

func (p *DeleteFeeRuleReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return DeleteFeeRuleReq_Base_DEFAULT
	}
	return p.Base
}
func (p *DeleteFeeRuleReq) SetRuleID(val int64) {
	p.RuleID = val
}
func (p *DeleteFeeRuleReq) SetOperator(val string) {
	p.Operator = val
}
func (p *DeleteFeeRuleReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_DeleteFeeRuleReq = map[int16]string{
	1:   "rule_id",
	2:   "operator",
	255: "Base",
}

func (p *DeleteFeeRuleReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *DeleteFeeRuleReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRuleID bool = false
	var issetOperator bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRuleID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteFeeRuleReq[fieldId]))
}

func (p *DeleteFeeRuleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleID = _field
	return nil
}
func (p *DeleteFeeRuleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Operator = _field
	return nil
}
func (p *DeleteFeeRuleReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *DeleteFeeRuleReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteFeeRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RuleID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DeleteFeeRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Operator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DeleteFeeRuleReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DeleteFeeRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFeeRuleReq(%+v)", *p)

}

type DeleteFeeRuleResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewDeleteFeeRuleResp() *DeleteFeeRuleResp {
	return &DeleteFeeRuleResp{}
}

func (p *DeleteFeeRuleResp) InitDefault() {
}

var DeleteFeeRuleResp_BaseResp_DEFAULT *base.BaseResp

func (p *DeleteFeeRuleResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return DeleteFeeRuleResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *DeleteFeeRuleResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_DeleteFeeRuleResp = map[int16]string{
	255: "BaseResp",
}

func (p *DeleteFeeRuleResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *DeleteFeeRuleResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeRuleResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteFeeRuleResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *DeleteFeeRuleResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeRuleResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteFeeRuleResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DeleteFeeRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFeeRuleResp(%+v)", *p)

}

type QueryFeeFormulasReq struct {
	RuleID        *int64     `thrift:"rule_id,1,optional" frugal:"1,optional,i64" json:"rule_id,omitempty"`
	FormulaIDList []int64    `thrift:"formula_id_list,2,optional" frugal:"2,optional,list<i64>" json:"formula_id_list,omitempty"`
	Base          *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewQueryFeeFormulasReq() *QueryFeeFormulasReq {
	return &QueryFeeFormulasReq{}
}

func (p *QueryFeeFormulasReq) InitDefault() {
}

var QueryFeeFormulasReq_RuleID_DEFAULT int64

func (p *QueryFeeFormulasReq) GetRuleID() (v int64) {
	if !p.IsSetRuleID() {
		return QueryFeeFormulasReq_RuleID_DEFAULT
	}
	return *p.RuleID
}

var QueryFeeFormulasReq_FormulaIDList_DEFAULT []int64

func (p *QueryFeeFormulasReq) GetFormulaIDList() (v []int64) {
	if !p.IsSetFormulaIDList() {
		return QueryFeeFormulasReq_FormulaIDList_DEFAULT
	}
	return p.FormulaIDList
}

var QueryFeeFormulasReq_Base_DEFAULT *base.Base

func (p *QueryFeeFormulasReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return QueryFeeFormulasReq_Base_DEFAULT
	}
	return p.Base
}
func (p *QueryFeeFormulasReq) SetRuleID(val *int64) {
	p.RuleID = val
}
func (p *QueryFeeFormulasReq) SetFormulaIDList(val []int64) {
	p.FormulaIDList = val
}
func (p *QueryFeeFormulasReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_QueryFeeFormulasReq = map[int16]string{
	1:   "rule_id",
	2:   "formula_id_list",
	255: "Base",
}

func (p *QueryFeeFormulasReq) IsSetRuleID() bool {
	return p.RuleID != nil
}

func (p *QueryFeeFormulasReq) IsSetFormulaIDList() bool {
	return p.FormulaIDList != nil
}

func (p *QueryFeeFormulasReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *QueryFeeFormulasReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeFormulasReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeFormulasReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *QueryFeeFormulasReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RuleID = _field
	return nil
}
func (p *QueryFeeFormulasReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int64, 0, size)
	for i := 0; i < size; i++ {

		var _elem int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FormulaIDList = _field
	return nil
}
func (p *QueryFeeFormulasReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *QueryFeeFormulasReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeFormulasReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeFormulasReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *QueryFeeFormulasReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetRuleID() {
		if err = oprot.WriteFieldBegin("rule_id", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.RuleID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *QueryFeeFormulasReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFormulaIDList() {
		if err = oprot.WriteFieldBegin("formula_id_list", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.FormulaIDList)); err != nil {
			return err
		}
		for _, v := range p.FormulaIDList {
			if err := oprot.WriteI64(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *QueryFeeFormulasReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *QueryFeeFormulasReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryFeeFormulasReq(%+v)", *p)

}

type QueryFeeFormulasResp struct {
	FormulaList []*FeeFormula  `thrift:"formula_list,1" frugal:"1,default,list<FeeFormula>" json:"formula_list"`
	BaseResp    *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewQueryFeeFormulasResp() *QueryFeeFormulasResp {
	return &QueryFeeFormulasResp{}
}

func (p *QueryFeeFormulasResp) InitDefault() {
}

func (p *QueryFeeFormulasResp) GetFormulaList() (v []*FeeFormula) {
	return p.FormulaList
}

var QueryFeeFormulasResp_BaseResp_DEFAULT *base.BaseResp

func (p *QueryFeeFormulasResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return QueryFeeFormulasResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *QueryFeeFormulasResp) SetFormulaList(val []*FeeFormula) {
	p.FormulaList = val
}
func (p *QueryFeeFormulasResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_QueryFeeFormulasResp = map[int16]string{
	1:   "formula_list",
	255: "BaseResp",
}

func (p *QueryFeeFormulasResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *QueryFeeFormulasResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeFormulasResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeFormulasResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *QueryFeeFormulasResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FeeFormula, 0, size)
	values := make([]FeeFormula, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FormulaList = _field
	return nil
}
func (p *QueryFeeFormulasResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *QueryFeeFormulasResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("QueryFeeFormulasResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeFormulasResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *QueryFeeFormulasResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("formula_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FormulaList)); err != nil {
		return err
	}
	for _, v := range p.FormulaList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *QueryFeeFormulasResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *QueryFeeFormulasResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryFeeFormulasResp(%+v)", *p)

}

type UpsertFeeFormulaReq struct {
	Formula  *FeeFormula `thrift:"formula,1,required" frugal:"1,required,FeeFormula" json:"formula"`
	Operator string      `thrift:"operator,2,required" frugal:"2,required,string" json:"operator"`
	Base     *base.Base  `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewUpsertFeeFormulaReq() *UpsertFeeFormulaReq {
	return &UpsertFeeFormulaReq{}
}

func (p *UpsertFeeFormulaReq) InitDefault() {
}

var UpsertFeeFormulaReq_Formula_DEFAULT *FeeFormula

func (p *UpsertFeeFormulaReq) GetFormula() (v *FeeFormula) {
	if !p.IsSetFormula() {
		return UpsertFeeFormulaReq_Formula_DEFAULT
	}
	return p.Formula
}

func (p *UpsertFeeFormulaReq) GetOperator() (v string) {
	return p.Operator
}

var UpsertFeeFormulaReq_Base_DEFAULT *base.Base

func (p *UpsertFeeFormulaReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return UpsertFeeFormulaReq_Base_DEFAULT
	}
	return p.Base
}
func (p *UpsertFeeFormulaReq) SetFormula(val *FeeFormula) {
	p.Formula = val
}
func (p *UpsertFeeFormulaReq) SetOperator(val string) {
	p.Operator = val
}
func (p *UpsertFeeFormulaReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_UpsertFeeFormulaReq = map[int16]string{
	1:   "formula",
	2:   "operator",
	255: "Base",
}

func (p *UpsertFeeFormulaReq) IsSetFormula() bool {
	return p.Formula != nil
}

func (p *UpsertFeeFormulaReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *UpsertFeeFormulaReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeFormulaReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFormula bool = false
	var issetOperator bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFormula = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFormula {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeFormulaReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpsertFeeFormulaReq[fieldId]))
}

func (p *UpsertFeeFormulaReq) ReadField1(iprot thrift.TProtocol) error {
	_field := NewFeeFormula()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Formula = _field
	return nil
}
func (p *UpsertFeeFormulaReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Operator = _field
	return nil
}
func (p *UpsertFeeFormulaReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *UpsertFeeFormulaReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeFormulaReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeFormulaReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpsertFeeFormulaReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("formula", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Formula.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpsertFeeFormulaReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Operator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpsertFeeFormulaReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpsertFeeFormulaReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpsertFeeFormulaReq(%+v)", *p)

}

type UpsertFeeFormulaResp struct {
	Formula  *FeeFormula    `thrift:"formula,1" frugal:"1,default,FeeFormula" json:"formula"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewUpsertFeeFormulaResp() *UpsertFeeFormulaResp {
	return &UpsertFeeFormulaResp{}
}

func (p *UpsertFeeFormulaResp) InitDefault() {
}

var UpsertFeeFormulaResp_Formula_DEFAULT *FeeFormula

func (p *UpsertFeeFormulaResp) GetFormula() (v *FeeFormula) {
	if !p.IsSetFormula() {
		return UpsertFeeFormulaResp_Formula_DEFAULT
	}
	return p.Formula
}

var UpsertFeeFormulaResp_BaseResp_DEFAULT *base.BaseResp

func (p *UpsertFeeFormulaResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return UpsertFeeFormulaResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *UpsertFeeFormulaResp) SetFormula(val *FeeFormula) {
	p.Formula = val
}
func (p *UpsertFeeFormulaResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_UpsertFeeFormulaResp = map[int16]string{
	1:   "formula",
	255: "BaseResp",
}

func (p *UpsertFeeFormulaResp) IsSetFormula() bool {
	return p.Formula != nil
}

func (p *UpsertFeeFormulaResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *UpsertFeeFormulaResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeFormulaResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeFormulaResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpsertFeeFormulaResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewFeeFormula()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Formula = _field
	return nil
}
func (p *UpsertFeeFormulaResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *UpsertFeeFormulaResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpsertFeeFormulaResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeFormulaResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpsertFeeFormulaResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("formula", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Formula.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpsertFeeFormulaResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *UpsertFeeFormulaResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpsertFeeFormulaResp(%+v)", *p)

}

type DeleteFeeFormulaReq struct {
	FormulaID int64      `thrift:"formula_id,1,required" frugal:"1,required,i64" json:"formula_id"`
	Operator  string     `thrift:"operator,2,required" frugal:"2,required,string" json:"operator"`
	Base      *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewDeleteFeeFormulaReq() *DeleteFeeFormulaReq {
	return &DeleteFeeFormulaReq{}
}

func (p *DeleteFeeFormulaReq) InitDefault() {
}

func (p *DeleteFeeFormulaReq) GetFormulaID() (v int64) {
	return p.FormulaID
}

func (p *DeleteFeeFormulaReq) GetOperator() (v string) {
	return p.Operator
}

var DeleteFeeFormulaReq_Base_DEFAULT *base.Base

func (p *DeleteFeeFormulaReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return DeleteFeeFormulaReq_Base_DEFAULT
	}
	return p.Base
}
func (p *DeleteFeeFormulaReq) SetFormulaID(val int64) {
	p.FormulaID = val
}
func (p *DeleteFeeFormulaReq) SetOperator(val string) {
	p.Operator = val
}
func (p *DeleteFeeFormulaReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_DeleteFeeFormulaReq = map[int16]string{
	1:   "formula_id",
	2:   "operator",
	255: "Base",
}

func (p *DeleteFeeFormulaReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *DeleteFeeFormulaReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeFormulaReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFormulaID bool = false
	var issetOperator bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFormulaID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFormulaID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeFormulaReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteFeeFormulaReq[fieldId]))
}

func (p *DeleteFeeFormulaReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FormulaID = _field
	return nil
}
func (p *DeleteFeeFormulaReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Operator = _field
	return nil
}
func (p *DeleteFeeFormulaReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *DeleteFeeFormulaReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeFormulaReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeFormulaReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteFeeFormulaReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("formula_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.FormulaID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DeleteFeeFormulaReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Operator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DeleteFeeFormulaReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DeleteFeeFormulaReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFeeFormulaReq(%+v)", *p)

}

type DeleteFeeFormulaResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewDeleteFeeFormulaResp() *DeleteFeeFormulaResp {
	return &DeleteFeeFormulaResp{}
}

func (p *DeleteFeeFormulaResp) InitDefault() {
}

var DeleteFeeFormulaResp_BaseResp_DEFAULT *base.BaseResp

func (p *DeleteFeeFormulaResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return DeleteFeeFormulaResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *DeleteFeeFormulaResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_DeleteFeeFormulaResp = map[int16]string{
	255: "BaseResp",
}

func (p *DeleteFeeFormulaResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *DeleteFeeFormulaResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeFormulaResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeFormulaResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteFeeFormulaResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *DeleteFeeFormulaResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("DeleteFeeFormulaResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeFormulaResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteFeeFormulaResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DeleteFeeFormulaResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFeeFormulaResp(%+v)", *p)

}

type ChargeResult_ struct {
	RoleType         fwe_trade_common.RoleType `thrift:"role_type,1" frugal:"1,default,RoleType" json:"role_type"`
	Amount           int64                     `thrift:"amount,2" frugal:"2,default,i64" json:"amount"`
	FormulaID        int64                     `thrift:"formula_id,3" frugal:"3,default,i64" json:"formula_id"`
	CurrencyType     payment.CurrencyType      `thrift:"currency_type,4" frugal:"4,default,CurrencyType" json:"currency_type"`
	NeedAutoWithdraw bool                      `thrift:"need_auto_withdraw,5" frugal:"5,default,bool" json:"need_auto_withdraw"`
}

func NewChargeResult_() *ChargeResult_ {
	return &ChargeResult_{}
}

func (p *ChargeResult_) InitDefault() {
}

func (p *ChargeResult_) GetRoleType() (v fwe_trade_common.RoleType) {
	return p.RoleType
}

func (p *ChargeResult_) GetAmount() (v int64) {
	return p.Amount
}

func (p *ChargeResult_) GetFormulaID() (v int64) {
	return p.FormulaID
}

func (p *ChargeResult_) GetCurrencyType() (v payment.CurrencyType) {
	return p.CurrencyType
}

func (p *ChargeResult_) GetNeedAutoWithdraw() (v bool) {
	return p.NeedAutoWithdraw
}
func (p *ChargeResult_) SetRoleType(val fwe_trade_common.RoleType) {
	p.RoleType = val
}
func (p *ChargeResult_) SetAmount(val int64) {
	p.Amount = val
}
func (p *ChargeResult_) SetFormulaID(val int64) {
	p.FormulaID = val
}
func (p *ChargeResult_) SetCurrencyType(val payment.CurrencyType) {
	p.CurrencyType = val
}
func (p *ChargeResult_) SetNeedAutoWithdraw(val bool) {
	p.NeedAutoWithdraw = val
}

var fieldIDToName_ChargeResult_ = map[int16]string{
	1: "role_type",
	2: "amount",
	3: "formula_id",
	4: "currency_type",
	5: "need_auto_withdraw",
}

func (p *ChargeResult_) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ChargeResult_")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeResult_[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ChargeResult_) ReadField1(iprot thrift.TProtocol) error {

	var _field fwe_trade_common.RoleType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = fwe_trade_common.RoleType(v)
	}
	p.RoleType = _field
	return nil
}
func (p *ChargeResult_) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *ChargeResult_) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FormulaID = _field
	return nil
}
func (p *ChargeResult_) ReadField4(iprot thrift.TProtocol) error {

	var _field payment.CurrencyType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = payment.CurrencyType(v)
	}
	p.CurrencyType = _field
	return nil
}
func (p *ChargeResult_) ReadField5(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NeedAutoWithdraw = _field
	return nil
}

func (p *ChargeResult_) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ChargeResult_")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeResult"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChargeResult_) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("role_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.RoleType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChargeResult_) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ChargeResult_) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("formula_id", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.FormulaID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ChargeResult_) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("currency_type", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.CurrencyType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ChargeResult_) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("need_auto_withdraw", thrift.BOOL, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.NeedAutoWithdraw); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ChargeResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChargeResult_(%+v)", *p)

}

type ChargeSettleReq struct {
	OrderID   string                      `thrift:"order_id,1,required" frugal:"1,required,string" json:"order_id"`
	RuleID    int64                       `thrift:"rule_id,2,required" frugal:"2,required,i64" json:"rule_id"`
	Params    *string                     `thrift:"params,3,optional" frugal:"3,optional,string" json:"params,omitempty"`
	FulfillID *string                     `thrift:"fulfill_id,4,optional" frugal:"4,optional,string" json:"fulfill_id,omitempty"`
	OrderType *fwe_trade_common.OrderType `thrift:"order_type,5,optional" frugal:"5,optional,OrderType" json:"order_type,omitempty"`
	Operator  string                      `thrift:"operator,100,required" frugal:"100,required,string" json:"operator"`
	Base      *base.Base                  `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewChargeSettleReq() *ChargeSettleReq {
	return &ChargeSettleReq{}
}

func (p *ChargeSettleReq) InitDefault() {
}

func (p *ChargeSettleReq) GetOrderID() (v string) {
	return p.OrderID
}

func (p *ChargeSettleReq) GetRuleID() (v int64) {
	return p.RuleID
}

var ChargeSettleReq_Params_DEFAULT string

func (p *ChargeSettleReq) GetParams() (v string) {
	if !p.IsSetParams() {
		return ChargeSettleReq_Params_DEFAULT
	}
	return *p.Params
}

var ChargeSettleReq_FulfillID_DEFAULT string

func (p *ChargeSettleReq) GetFulfillID() (v string) {
	if !p.IsSetFulfillID() {
		return ChargeSettleReq_FulfillID_DEFAULT
	}
	return *p.FulfillID
}

var ChargeSettleReq_OrderType_DEFAULT fwe_trade_common.OrderType

func (p *ChargeSettleReq) GetOrderType() (v fwe_trade_common.OrderType) {
	if !p.IsSetOrderType() {
		return ChargeSettleReq_OrderType_DEFAULT
	}
	return *p.OrderType
}

func (p *ChargeSettleReq) GetOperator() (v string) {
	return p.Operator
}

var ChargeSettleReq_Base_DEFAULT *base.Base

func (p *ChargeSettleReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ChargeSettleReq_Base_DEFAULT
	}
	return p.Base
}
func (p *ChargeSettleReq) SetOrderID(val string) {
	p.OrderID = val
}
func (p *ChargeSettleReq) SetRuleID(val int64) {
	p.RuleID = val
}
func (p *ChargeSettleReq) SetParams(val *string) {
	p.Params = val
}
func (p *ChargeSettleReq) SetFulfillID(val *string) {
	p.FulfillID = val
}
func (p *ChargeSettleReq) SetOrderType(val *fwe_trade_common.OrderType) {
	p.OrderType = val
}
func (p *ChargeSettleReq) SetOperator(val string) {
	p.Operator = val
}
func (p *ChargeSettleReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ChargeSettleReq = map[int16]string{
	1:   "order_id",
	2:   "rule_id",
	3:   "params",
	4:   "fulfill_id",
	5:   "order_type",
	100: "operator",
	255: "Base",
}

func (p *ChargeSettleReq) IsSetParams() bool {
	return p.Params != nil
}

func (p *ChargeSettleReq) IsSetFulfillID() bool {
	return p.FulfillID != nil
}

func (p *ChargeSettleReq) IsSetOrderType() bool {
	return p.OrderType != nil
}

func (p *ChargeSettleReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *ChargeSettleReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ChargeSettleReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetRuleID bool = false
	var issetOperator bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRuleID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 100
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeSettleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ChargeSettleReq[fieldId]))
}

func (p *ChargeSettleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *ChargeSettleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleID = _field
	return nil
}
func (p *ChargeSettleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Params = _field
	return nil
}
func (p *ChargeSettleReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FulfillID = _field
	return nil
}
func (p *ChargeSettleReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *fwe_trade_common.OrderType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := fwe_trade_common.OrderType(v)
		_field = &tmp
	}
	p.OrderType = _field
	return nil
}
func (p *ChargeSettleReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Operator = _field
	return nil
}
func (p *ChargeSettleReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ChargeSettleReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ChargeSettleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeSettleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChargeSettleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChargeSettleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RuleID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ChargeSettleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetParams() {
		if err = oprot.WriteFieldBegin("params", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Params); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ChargeSettleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFulfillID() {
		if err = oprot.WriteFieldBegin("fulfill_id", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FulfillID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ChargeSettleReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderType() {
		if err = oprot.WriteFieldBegin("order_type", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ChargeSettleReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Operator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *ChargeSettleReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ChargeSettleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChargeSettleReq(%+v)", *p)

}

type ChargeSettleResp struct {
	ChargeResultList []*ChargeResult_ `thrift:"charge_result_list,1" frugal:"1,default,list<ChargeResult_>" json:"charge_result_list"`
	FinanceIDList    []string         `thrift:"finance_id_list,2" frugal:"2,default,list<string>" json:"finance_id_list"`
	RecordID         string           `thrift:"record_id,100" frugal:"100,default,string" json:"record_id"`
	BaseResp         *base.BaseResp   `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewChargeSettleResp() *ChargeSettleResp {
	return &ChargeSettleResp{}
}

func (p *ChargeSettleResp) InitDefault() {
}

func (p *ChargeSettleResp) GetChargeResultList() (v []*ChargeResult_) {
	return p.ChargeResultList
}

func (p *ChargeSettleResp) GetFinanceIDList() (v []string) {
	return p.FinanceIDList
}

func (p *ChargeSettleResp) GetRecordID() (v string) {
	return p.RecordID
}

var ChargeSettleResp_BaseResp_DEFAULT *base.BaseResp

func (p *ChargeSettleResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ChargeSettleResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *ChargeSettleResp) SetChargeResultList(val []*ChargeResult_) {
	p.ChargeResultList = val
}
func (p *ChargeSettleResp) SetFinanceIDList(val []string) {
	p.FinanceIDList = val
}
func (p *ChargeSettleResp) SetRecordID(val string) {
	p.RecordID = val
}
func (p *ChargeSettleResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_ChargeSettleResp = map[int16]string{
	1:   "charge_result_list",
	2:   "finance_id_list",
	100: "record_id",
	255: "BaseResp",
}

func (p *ChargeSettleResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ChargeSettleResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ChargeSettleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeSettleResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ChargeSettleResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChargeResult_, 0, size)
	values := make([]ChargeResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ChargeResultList = _field
	return nil
}
func (p *ChargeSettleResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FinanceIDList = _field
	return nil
}
func (p *ChargeSettleResp) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RecordID = _field
	return nil
}
func (p *ChargeSettleResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ChargeSettleResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ChargeSettleResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeSettleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChargeSettleResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("charge_result_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChargeResultList)); err != nil {
		return err
	}
	for _, v := range p.ChargeResultList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChargeSettleResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_id_list", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.FinanceIDList)); err != nil {
		return err
	}
	for _, v := range p.FinanceIDList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ChargeSettleResp) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("record_id", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RecordID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *ChargeSettleResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ChargeSettleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChargeSettleResp(%+v)", *p)

}

type SingleRefund struct {
	FinanceOrderID   string                              `thrift:"finance_order_id,1" frugal:"1,default,string" json:"finance_order_id"`
	Amount           int64                               `thrift:"amount,2" frugal:"2,default,i64" json:"amount"`
	YztOfflineAmount int64                               `thrift:"yzt_offline_amount,3" frugal:"3,default,i64" json:"yzt_offline_amount"`
	JstRefundList    []*fwe_trade_common.JstRefundSingle `thrift:"jst_refund_list,10" frugal:"10,default,list<fwe_trade_common.JstRefundSingle>" json:"jst_refund_list"`
}

func NewSingleRefund() *SingleRefund {
	return &SingleRefund{}
}

func (p *SingleRefund) InitDefault() {
}

func (p *SingleRefund) GetFinanceOrderID() (v string) {
	return p.FinanceOrderID
}

func (p *SingleRefund) GetAmount() (v int64) {
	return p.Amount
}

func (p *SingleRefund) GetYztOfflineAmount() (v int64) {
	return p.YztOfflineAmount
}

func (p *SingleRefund) GetJstRefundList() (v []*fwe_trade_common.JstRefundSingle) {
	return p.JstRefundList
}
func (p *SingleRefund) SetFinanceOrderID(val string) {
	p.FinanceOrderID = val
}
func (p *SingleRefund) SetAmount(val int64) {
	p.Amount = val
}
func (p *SingleRefund) SetYztOfflineAmount(val int64) {
	p.YztOfflineAmount = val
}
func (p *SingleRefund) SetJstRefundList(val []*fwe_trade_common.JstRefundSingle) {
	p.JstRefundList = val
}

var fieldIDToName_SingleRefund = map[int16]string{
	1:  "finance_order_id",
	2:  "amount",
	3:  "yzt_offline_amount",
	10: "jst_refund_list",
}

func (p *SingleRefund) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("SingleRefund")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SingleRefund[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SingleRefund) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceOrderID = _field
	return nil
}
func (p *SingleRefund) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *SingleRefund) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.YztOfflineAmount = _field
	return nil
}
func (p *SingleRefund) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.JstRefundSingle, 0, size)
	values := make([]fwe_trade_common.JstRefundSingle, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.JstRefundList = _field
	return nil
}

func (p *SingleRefund) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("SingleRefund")

	var fieldId int16
	if err = oprot.WriteStructBegin("SingleRefund"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SingleRefund) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FinanceOrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SingleRefund) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SingleRefund) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("yzt_offline_amount", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.YztOfflineAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SingleRefund) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("jst_refund_list", thrift.LIST, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.JstRefundList)); err != nil {
		return err
	}
	for _, v := range p.JstRefundList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SingleRefund) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SingleRefund(%+v)", *p)

}

type ChargeRefundReq struct {
	OrderID    string                      `thrift:"order_id,1,required" frugal:"1,required,string" json:"order_id"`
	RuleID     int64                       `thrift:"rule_id,2,required" frugal:"2,required,i64" json:"rule_id"`
	RefundList []*SingleRefund             `thrift:"refund_list,3,optional" frugal:"3,optional,list<SingleRefund>" json:"refund_list,omitempty"`
	FulfillID  *string                     `thrift:"fulfill_id,4,optional" frugal:"4,optional,string" json:"fulfill_id,omitempty"`
	OrderType  *fwe_trade_common.OrderType `thrift:"order_type,5,optional" frugal:"5,optional,OrderType" json:"order_type,omitempty"`
	Operator   string                      `thrift:"operator,100,required" frugal:"100,required,string" json:"operator"`
	Base       *base.Base                  `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewChargeRefundReq() *ChargeRefundReq {
	return &ChargeRefundReq{}
}

func (p *ChargeRefundReq) InitDefault() {
}

func (p *ChargeRefundReq) GetOrderID() (v string) {
	return p.OrderID
}

func (p *ChargeRefundReq) GetRuleID() (v int64) {
	return p.RuleID
}

var ChargeRefundReq_RefundList_DEFAULT []*SingleRefund

func (p *ChargeRefundReq) GetRefundList() (v []*SingleRefund) {
	if !p.IsSetRefundList() {
		return ChargeRefundReq_RefundList_DEFAULT
	}
	return p.RefundList
}

var ChargeRefundReq_FulfillID_DEFAULT string

func (p *ChargeRefundReq) GetFulfillID() (v string) {
	if !p.IsSetFulfillID() {
		return ChargeRefundReq_FulfillID_DEFAULT
	}
	return *p.FulfillID
}

var ChargeRefundReq_OrderType_DEFAULT fwe_trade_common.OrderType

func (p *ChargeRefundReq) GetOrderType() (v fwe_trade_common.OrderType) {
	if !p.IsSetOrderType() {
		return ChargeRefundReq_OrderType_DEFAULT
	}
	return *p.OrderType
}

func (p *ChargeRefundReq) GetOperator() (v string) {
	return p.Operator
}

var ChargeRefundReq_Base_DEFAULT *base.Base

func (p *ChargeRefundReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ChargeRefundReq_Base_DEFAULT
	}
	return p.Base
}
func (p *ChargeRefundReq) SetOrderID(val string) {
	p.OrderID = val
}
func (p *ChargeRefundReq) SetRuleID(val int64) {
	p.RuleID = val
}
func (p *ChargeRefundReq) SetRefundList(val []*SingleRefund) {
	p.RefundList = val
}
func (p *ChargeRefundReq) SetFulfillID(val *string) {
	p.FulfillID = val
}
func (p *ChargeRefundReq) SetOrderType(val *fwe_trade_common.OrderType) {
	p.OrderType = val
}
func (p *ChargeRefundReq) SetOperator(val string) {
	p.Operator = val
}
func (p *ChargeRefundReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_ChargeRefundReq = map[int16]string{
	1:   "order_id",
	2:   "rule_id",
	3:   "refund_list",
	4:   "fulfill_id",
	5:   "order_type",
	100: "operator",
	255: "Base",
}

func (p *ChargeRefundReq) IsSetRefundList() bool {
	return p.RefundList != nil
}

func (p *ChargeRefundReq) IsSetFulfillID() bool {
	return p.FulfillID != nil
}

func (p *ChargeRefundReq) IsSetOrderType() bool {
	return p.OrderType != nil
}

func (p *ChargeRefundReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *ChargeRefundReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ChargeRefundReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetRuleID bool = false
	var issetOperator bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRuleID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 100
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeRefundReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ChargeRefundReq[fieldId]))
}

func (p *ChargeRefundReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *ChargeRefundReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleID = _field
	return nil
}
func (p *ChargeRefundReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SingleRefund, 0, size)
	values := make([]SingleRefund, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RefundList = _field
	return nil
}
func (p *ChargeRefundReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FulfillID = _field
	return nil
}
func (p *ChargeRefundReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *fwe_trade_common.OrderType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := fwe_trade_common.OrderType(v)
		_field = &tmp
	}
	p.OrderType = _field
	return nil
}
func (p *ChargeRefundReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Operator = _field
	return nil
}
func (p *ChargeRefundReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ChargeRefundReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ChargeRefundReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeRefundReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChargeRefundReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChargeRefundReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RuleID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ChargeRefundReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRefundList() {
		if err = oprot.WriteFieldBegin("refund_list", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RefundList)); err != nil {
			return err
		}
		for _, v := range p.RefundList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ChargeRefundReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFulfillID() {
		if err = oprot.WriteFieldBegin("fulfill_id", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FulfillID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ChargeRefundReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderType() {
		if err = oprot.WriteFieldBegin("order_type", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ChargeRefundReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Operator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *ChargeRefundReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ChargeRefundReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChargeRefundReq(%+v)", *p)

}

type ChargeRefundResp struct {
	ChargeResultList []*ChargeResult_            `thrift:"charge_result_list,1" frugal:"1,default,list<ChargeResult_>" json:"charge_result_list"`
	RefundList       []*SingleRefund             `thrift:"refund_list,2" frugal:"2,default,list<SingleRefund>" json:"refund_list"`
	RefundMode       fwe_trade_common.RefundMode `thrift:"refund_mode,3" frugal:"3,default,RefundMode" json:"refund_mode"`
	RecordID         string                      `thrift:"record_id,100" frugal:"100,default,string" json:"record_id"`
	BaseResp         *base.BaseResp              `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewChargeRefundResp() *ChargeRefundResp {
	return &ChargeRefundResp{}
}

func (p *ChargeRefundResp) InitDefault() {
}

func (p *ChargeRefundResp) GetChargeResultList() (v []*ChargeResult_) {
	return p.ChargeResultList
}

func (p *ChargeRefundResp) GetRefundList() (v []*SingleRefund) {
	return p.RefundList
}

func (p *ChargeRefundResp) GetRefundMode() (v fwe_trade_common.RefundMode) {
	return p.RefundMode
}

func (p *ChargeRefundResp) GetRecordID() (v string) {
	return p.RecordID
}

var ChargeRefundResp_BaseResp_DEFAULT *base.BaseResp

func (p *ChargeRefundResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ChargeRefundResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *ChargeRefundResp) SetChargeResultList(val []*ChargeResult_) {
	p.ChargeResultList = val
}
func (p *ChargeRefundResp) SetRefundList(val []*SingleRefund) {
	p.RefundList = val
}
func (p *ChargeRefundResp) SetRefundMode(val fwe_trade_common.RefundMode) {
	p.RefundMode = val
}
func (p *ChargeRefundResp) SetRecordID(val string) {
	p.RecordID = val
}
func (p *ChargeRefundResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_ChargeRefundResp = map[int16]string{
	1:   "charge_result_list",
	2:   "refund_list",
	3:   "refund_mode",
	100: "record_id",
	255: "BaseResp",
}

func (p *ChargeRefundResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ChargeRefundResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ChargeRefundResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeRefundResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ChargeRefundResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChargeResult_, 0, size)
	values := make([]ChargeResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ChargeResultList = _field
	return nil
}
func (p *ChargeRefundResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SingleRefund, 0, size)
	values := make([]SingleRefund, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RefundList = _field
	return nil
}
func (p *ChargeRefundResp) ReadField3(iprot thrift.TProtocol) error {

	var _field fwe_trade_common.RefundMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = fwe_trade_common.RefundMode(v)
	}
	p.RefundMode = _field
	return nil
}
func (p *ChargeRefundResp) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RecordID = _field
	return nil
}
func (p *ChargeRefundResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ChargeRefundResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ChargeRefundResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeRefundResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ChargeRefundResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("charge_result_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChargeResultList)); err != nil {
		return err
	}
	for _, v := range p.ChargeResultList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ChargeRefundResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_list", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RefundList)); err != nil {
		return err
	}
	for _, v := range p.RefundList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ChargeRefundResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_mode", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.RefundMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ChargeRefundResp) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("record_id", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RecordID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *ChargeRefundResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ChargeRefundResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChargeRefundResp(%+v)", *p)

}

type CommonChargeReq struct {
	OrderID     string                      `thrift:"order_id,1,required" frugal:"1,required,string" json:"order_id"`
	RuleID      int64                       `thrift:"rule_id,2,required" frugal:"2,required,i64" json:"rule_id"`
	Params      *string                     `thrift:"params,3,optional" frugal:"3,optional,string" json:"params,omitempty"`
	IsReconcile *bool                       `thrift:"is_reconcile,4,optional" frugal:"4,optional,bool" json:"is_reconcile,omitempty"`
	FulfillID   *string                     `thrift:"fulfill_id,5,optional" frugal:"5,optional,string" json:"fulfill_id,omitempty"`
	OrderType   *fwe_trade_common.OrderType `thrift:"order_type,20,optional" frugal:"20,optional,OrderType" json:"order_type,omitempty"`
	Operator    string                      `thrift:"operator,100,required" frugal:"100,required,string" json:"operator"`
	Base        *base.Base                  `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewCommonChargeReq() *CommonChargeReq {
	return &CommonChargeReq{}
}

func (p *CommonChargeReq) InitDefault() {
}

func (p *CommonChargeReq) GetOrderID() (v string) {
	return p.OrderID
}

func (p *CommonChargeReq) GetRuleID() (v int64) {
	return p.RuleID
}

var CommonChargeReq_Params_DEFAULT string

func (p *CommonChargeReq) GetParams() (v string) {
	if !p.IsSetParams() {
		return CommonChargeReq_Params_DEFAULT
	}
	return *p.Params
}

var CommonChargeReq_IsReconcile_DEFAULT bool

func (p *CommonChargeReq) GetIsReconcile() (v bool) {
	if !p.IsSetIsReconcile() {
		return CommonChargeReq_IsReconcile_DEFAULT
	}
	return *p.IsReconcile
}

var CommonChargeReq_FulfillID_DEFAULT string

func (p *CommonChargeReq) GetFulfillID() (v string) {
	if !p.IsSetFulfillID() {
		return CommonChargeReq_FulfillID_DEFAULT
	}
	return *p.FulfillID
}

var CommonChargeReq_OrderType_DEFAULT fwe_trade_common.OrderType

func (p *CommonChargeReq) GetOrderType() (v fwe_trade_common.OrderType) {
	if !p.IsSetOrderType() {
		return CommonChargeReq_OrderType_DEFAULT
	}
	return *p.OrderType
}

func (p *CommonChargeReq) GetOperator() (v string) {
	return p.Operator
}

var CommonChargeReq_Base_DEFAULT *base.Base

func (p *CommonChargeReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CommonChargeReq_Base_DEFAULT
	}
	return p.Base
}
func (p *CommonChargeReq) SetOrderID(val string) {
	p.OrderID = val
}
func (p *CommonChargeReq) SetRuleID(val int64) {
	p.RuleID = val
}
func (p *CommonChargeReq) SetParams(val *string) {
	p.Params = val
}
func (p *CommonChargeReq) SetIsReconcile(val *bool) {
	p.IsReconcile = val
}
func (p *CommonChargeReq) SetFulfillID(val *string) {
	p.FulfillID = val
}
func (p *CommonChargeReq) SetOrderType(val *fwe_trade_common.OrderType) {
	p.OrderType = val
}
func (p *CommonChargeReq) SetOperator(val string) {
	p.Operator = val
}
func (p *CommonChargeReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CommonChargeReq = map[int16]string{
	1:   "order_id",
	2:   "rule_id",
	3:   "params",
	4:   "is_reconcile",
	5:   "fulfill_id",
	20:  "order_type",
	100: "operator",
	255: "Base",
}

func (p *CommonChargeReq) IsSetParams() bool {
	return p.Params != nil
}

func (p *CommonChargeReq) IsSetIsReconcile() bool {
	return p.IsReconcile != nil
}

func (p *CommonChargeReq) IsSetFulfillID() bool {
	return p.FulfillID != nil
}

func (p *CommonChargeReq) IsSetOrderType() bool {
	return p.OrderType != nil
}

func (p *CommonChargeReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *CommonChargeReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CommonChargeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetRuleID bool = false
	var issetOperator bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRuleID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 100
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CommonChargeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CommonChargeReq[fieldId]))
}

func (p *CommonChargeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *CommonChargeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleID = _field
	return nil
}
func (p *CommonChargeReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Params = _field
	return nil
}
func (p *CommonChargeReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsReconcile = _field
	return nil
}
func (p *CommonChargeReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FulfillID = _field
	return nil
}
func (p *CommonChargeReq) ReadField20(iprot thrift.TProtocol) error {

	var _field *fwe_trade_common.OrderType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := fwe_trade_common.OrderType(v)
		_field = &tmp
	}
	p.OrderType = _field
	return nil
}
func (p *CommonChargeReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Operator = _field
	return nil
}
func (p *CommonChargeReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CommonChargeReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CommonChargeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CommonChargeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CommonChargeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CommonChargeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RuleID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CommonChargeReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetParams() {
		if err = oprot.WriteFieldBegin("params", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Params); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CommonChargeReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsReconcile() {
		if err = oprot.WriteFieldBegin("is_reconcile", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsReconcile); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CommonChargeReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetFulfillID() {
		if err = oprot.WriteFieldBegin("fulfill_id", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FulfillID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *CommonChargeReq) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderType() {
		if err = oprot.WriteFieldBegin("order_type", thrift.I32, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}
func (p *CommonChargeReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Operator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *CommonChargeReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CommonChargeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonChargeReq(%+v)", *p)

}

type CommonChargeResp struct {
	ChargeResultList []*ChargeResult_ `thrift:"charge_result_list,1" frugal:"1,default,list<ChargeResult_>" json:"charge_result_list"`
	RecordID         string           `thrift:"record_id,100" frugal:"100,default,string" json:"record_id"`
	BaseResp         *base.BaseResp   `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewCommonChargeResp() *CommonChargeResp {
	return &CommonChargeResp{}
}

func (p *CommonChargeResp) InitDefault() {
}

func (p *CommonChargeResp) GetChargeResultList() (v []*ChargeResult_) {
	return p.ChargeResultList
}

func (p *CommonChargeResp) GetRecordID() (v string) {
	return p.RecordID
}

var CommonChargeResp_BaseResp_DEFAULT *base.BaseResp

func (p *CommonChargeResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CommonChargeResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CommonChargeResp) SetChargeResultList(val []*ChargeResult_) {
	p.ChargeResultList = val
}
func (p *CommonChargeResp) SetRecordID(val string) {
	p.RecordID = val
}
func (p *CommonChargeResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CommonChargeResp = map[int16]string{
	1:   "charge_result_list",
	100: "record_id",
	255: "BaseResp",
}

func (p *CommonChargeResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CommonChargeResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CommonChargeResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CommonChargeResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CommonChargeResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChargeResult_, 0, size)
	values := make([]ChargeResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ChargeResultList = _field
	return nil
}
func (p *CommonChargeResp) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RecordID = _field
	return nil
}
func (p *CommonChargeResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CommonChargeResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CommonChargeResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CommonChargeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CommonChargeResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("charge_result_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChargeResultList)); err != nil {
		return err
	}
	for _, v := range p.ChargeResultList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CommonChargeResp) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("record_id", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RecordID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *CommonChargeResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CommonChargeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonChargeResp(%+v)", *p)

}

type FeeChargeRecord struct {
	BizScene            int32            `thrift:"biz_scene,1" frugal:"1,default,i32" json:"biz_scene"`
	OrderID             string           `thrift:"order_id,2" frugal:"2,default,string" json:"order_id"`
	RecordID            string           `thrift:"record_id,3" frugal:"3,default,string" json:"record_id"`
	Rule                *FeeRule         `thrift:"rule,4" frugal:"4,default,FeeRule" json:"rule"`
	ExprParams          string           `thrift:"expr_params,5" frugal:"5,default,string" json:"expr_params"`
	ChargeResultList    []*ChargeResult_ `thrift:"charge_result_list,6" frugal:"6,default,list<ChargeResult_>" json:"charge_result_list"`
	FinanceModel        string           `thrift:"finance_model,7" frugal:"7,default,string" json:"finance_model"`
	RawReq              string           `thrift:"raw_req,8" frugal:"8,default,string" json:"raw_req"`
	RefundList          []*SingleRefund  `thrift:"refund_list,9" frugal:"9,default,list<SingleRefund>" json:"refund_list"`
	LogID               string           `thrift:"log_id,10" frugal:"10,default,string" json:"log_id"`
	AmountData          string           `thrift:"amount_data,11" frugal:"11,default,string" json:"amount_data"`
	SettleFinanceIDList []string         `thrift:"settle_finance_id_list,12" frugal:"12,default,list<string>" json:"settle_finance_id_list"`
	Operator            string           `thrift:"operator,100" frugal:"100,default,string" json:"operator"`
}

func NewFeeChargeRecord() *FeeChargeRecord {
	return &FeeChargeRecord{}
}

func (p *FeeChargeRecord) InitDefault() {
}

func (p *FeeChargeRecord) GetBizScene() (v int32) {
	return p.BizScene
}

func (p *FeeChargeRecord) GetOrderID() (v string) {
	return p.OrderID
}

func (p *FeeChargeRecord) GetRecordID() (v string) {
	return p.RecordID
}

var FeeChargeRecord_Rule_DEFAULT *FeeRule

func (p *FeeChargeRecord) GetRule() (v *FeeRule) {
	if !p.IsSetRule() {
		return FeeChargeRecord_Rule_DEFAULT
	}
	return p.Rule
}

func (p *FeeChargeRecord) GetExprParams() (v string) {
	return p.ExprParams
}

func (p *FeeChargeRecord) GetChargeResultList() (v []*ChargeResult_) {
	return p.ChargeResultList
}

func (p *FeeChargeRecord) GetFinanceModel() (v string) {
	return p.FinanceModel
}

func (p *FeeChargeRecord) GetRawReq() (v string) {
	return p.RawReq
}

func (p *FeeChargeRecord) GetRefundList() (v []*SingleRefund) {
	return p.RefundList
}

func (p *FeeChargeRecord) GetLogID() (v string) {
	return p.LogID
}

func (p *FeeChargeRecord) GetAmountData() (v string) {
	return p.AmountData
}

func (p *FeeChargeRecord) GetSettleFinanceIDList() (v []string) {
	return p.SettleFinanceIDList
}

func (p *FeeChargeRecord) GetOperator() (v string) {
	return p.Operator
}
func (p *FeeChargeRecord) SetBizScene(val int32) {
	p.BizScene = val
}
func (p *FeeChargeRecord) SetOrderID(val string) {
	p.OrderID = val
}
func (p *FeeChargeRecord) SetRecordID(val string) {
	p.RecordID = val
}
func (p *FeeChargeRecord) SetRule(val *FeeRule) {
	p.Rule = val
}
func (p *FeeChargeRecord) SetExprParams(val string) {
	p.ExprParams = val
}
func (p *FeeChargeRecord) SetChargeResultList(val []*ChargeResult_) {
	p.ChargeResultList = val
}
func (p *FeeChargeRecord) SetFinanceModel(val string) {
	p.FinanceModel = val
}
func (p *FeeChargeRecord) SetRawReq(val string) {
	p.RawReq = val
}
func (p *FeeChargeRecord) SetRefundList(val []*SingleRefund) {
	p.RefundList = val
}
func (p *FeeChargeRecord) SetLogID(val string) {
	p.LogID = val
}
func (p *FeeChargeRecord) SetAmountData(val string) {
	p.AmountData = val
}
func (p *FeeChargeRecord) SetSettleFinanceIDList(val []string) {
	p.SettleFinanceIDList = val
}
func (p *FeeChargeRecord) SetOperator(val string) {
	p.Operator = val
}

var fieldIDToName_FeeChargeRecord = map[int16]string{
	1:   "biz_scene",
	2:   "order_id",
	3:   "record_id",
	4:   "rule",
	5:   "expr_params",
	6:   "charge_result_list",
	7:   "finance_model",
	8:   "raw_req",
	9:   "refund_list",
	10:  "log_id",
	11:  "amount_data",
	12:  "settle_finance_id_list",
	100: "operator",
}

func (p *FeeChargeRecord) IsSetRule() bool {
	return p.Rule != nil
}

func (p *FeeChargeRecord) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeChargeRecord")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeChargeRecord[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeChargeRecord) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizScene = _field
	return nil
}
func (p *FeeChargeRecord) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *FeeChargeRecord) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RecordID = _field
	return nil
}
func (p *FeeChargeRecord) ReadField4(iprot thrift.TProtocol) error {
	_field := NewFeeRule()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Rule = _field
	return nil
}
func (p *FeeChargeRecord) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExprParams = _field
	return nil
}
func (p *FeeChargeRecord) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ChargeResult_, 0, size)
	values := make([]ChargeResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ChargeResultList = _field
	return nil
}
func (p *FeeChargeRecord) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceModel = _field
	return nil
}
func (p *FeeChargeRecord) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RawReq = _field
	return nil
}
func (p *FeeChargeRecord) ReadField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SingleRefund, 0, size)
	values := make([]SingleRefund, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RefundList = _field
	return nil
}
func (p *FeeChargeRecord) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LogID = _field
	return nil
}
func (p *FeeChargeRecord) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AmountData = _field
	return nil
}
func (p *FeeChargeRecord) ReadField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SettleFinanceIDList = _field
	return nil
}
func (p *FeeChargeRecord) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Operator = _field
	return nil
}

func (p *FeeChargeRecord) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeChargeRecord")

	var fieldId int16
	if err = oprot.WriteStructBegin("FeeChargeRecord"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeChargeRecord) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_scene", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.BizScene); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("record_id", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RecordID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule", thrift.STRUCT, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Rule.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("expr_params", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExprParams); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("charge_result_list", thrift.LIST, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ChargeResultList)); err != nil {
		return err
	}
	for _, v := range p.ChargeResultList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_model", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FinanceModel); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("raw_req", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RawReq); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_list", thrift.LIST, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RefundList)); err != nil {
		return err
	}
	for _, v := range p.RefundList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("log_id", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LogID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount_data", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AmountData); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("settle_finance_id_list", thrift.LIST, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.SettleFinanceIDList)); err != nil {
		return err
	}
	for _, v := range p.SettleFinanceIDList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *FeeChargeRecord) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Operator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}

func (p *FeeChargeRecord) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeChargeRecord(%+v)", *p)

}

type MGetChargeRecordReq struct {
	RecordIDList []string   `thrift:"record_id_list,1,optional" frugal:"1,optional,list<string>" json:"record_id_list,omitempty"`
	Base         *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewMGetChargeRecordReq() *MGetChargeRecordReq {
	return &MGetChargeRecordReq{}
}

func (p *MGetChargeRecordReq) InitDefault() {
}

var MGetChargeRecordReq_RecordIDList_DEFAULT []string

func (p *MGetChargeRecordReq) GetRecordIDList() (v []string) {
	if !p.IsSetRecordIDList() {
		return MGetChargeRecordReq_RecordIDList_DEFAULT
	}
	return p.RecordIDList
}

var MGetChargeRecordReq_Base_DEFAULT *base.Base

func (p *MGetChargeRecordReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return MGetChargeRecordReq_Base_DEFAULT
	}
	return p.Base
}
func (p *MGetChargeRecordReq) SetRecordIDList(val []string) {
	p.RecordIDList = val
}
func (p *MGetChargeRecordReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_MGetChargeRecordReq = map[int16]string{
	1:   "record_id_list",
	255: "Base",
}

func (p *MGetChargeRecordReq) IsSetRecordIDList() bool {
	return p.RecordIDList != nil
}

func (p *MGetChargeRecordReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *MGetChargeRecordReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetChargeRecordReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetChargeRecordReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MGetChargeRecordReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RecordIDList = _field
	return nil
}
func (p *MGetChargeRecordReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *MGetChargeRecordReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetChargeRecordReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("MGetChargeRecordReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MGetChargeRecordReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetRecordIDList() {
		if err = oprot.WriteFieldBegin("record_id_list", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.RecordIDList)); err != nil {
			return err
		}
		for _, v := range p.RecordIDList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MGetChargeRecordReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *MGetChargeRecordReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MGetChargeRecordReq(%+v)", *p)

}

type MGetChargeRecordResp struct {
	RecordList []*FeeChargeRecord `thrift:"record_list,1" frugal:"1,default,list<FeeChargeRecord>" json:"record_list"`
	BaseResp   *base.BaseResp     `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewMGetChargeRecordResp() *MGetChargeRecordResp {
	return &MGetChargeRecordResp{}
}

func (p *MGetChargeRecordResp) InitDefault() {
}

func (p *MGetChargeRecordResp) GetRecordList() (v []*FeeChargeRecord) {
	return p.RecordList
}

var MGetChargeRecordResp_BaseResp_DEFAULT *base.BaseResp

func (p *MGetChargeRecordResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return MGetChargeRecordResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *MGetChargeRecordResp) SetRecordList(val []*FeeChargeRecord) {
	p.RecordList = val
}
func (p *MGetChargeRecordResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_MGetChargeRecordResp = map[int16]string{
	1:   "record_list",
	255: "BaseResp",
}

func (p *MGetChargeRecordResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *MGetChargeRecordResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetChargeRecordResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetChargeRecordResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MGetChargeRecordResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*FeeChargeRecord, 0, size)
	values := make([]FeeChargeRecord, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RecordList = _field
	return nil
}
func (p *MGetChargeRecordResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *MGetChargeRecordResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MGetChargeRecordResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("MGetChargeRecordResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MGetChargeRecordResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("record_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RecordList)); err != nil {
		return err
	}
	for _, v := range p.RecordList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MGetChargeRecordResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *MGetChargeRecordResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MGetChargeRecordResp(%+v)", *p)

}

type CheckBizFeeItemsReq struct {
	BizScene    int32      `thrift:"biz_scene,1" frugal:"1,default,i32" json:"biz_scene"`
	FeeNameList []string   `thrift:"fee_name_list,2" frugal:"2,default,list<string>" json:"fee_name_list"`
	Base        *base.Base `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewCheckBizFeeItemsReq() *CheckBizFeeItemsReq {
	return &CheckBizFeeItemsReq{}
}

func (p *CheckBizFeeItemsReq) InitDefault() {
}

func (p *CheckBizFeeItemsReq) GetBizScene() (v int32) {
	return p.BizScene
}

func (p *CheckBizFeeItemsReq) GetFeeNameList() (v []string) {
	return p.FeeNameList
}

var CheckBizFeeItemsReq_Base_DEFAULT *base.Base

func (p *CheckBizFeeItemsReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CheckBizFeeItemsReq_Base_DEFAULT
	}
	return p.Base
}
func (p *CheckBizFeeItemsReq) SetBizScene(val int32) {
	p.BizScene = val
}
func (p *CheckBizFeeItemsReq) SetFeeNameList(val []string) {
	p.FeeNameList = val
}
func (p *CheckBizFeeItemsReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CheckBizFeeItemsReq = map[int16]string{
	1:   "biz_scene",
	2:   "fee_name_list",
	255: "Base",
}

func (p *CheckBizFeeItemsReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *CheckBizFeeItemsReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckBizFeeItemsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckBizFeeItemsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckBizFeeItemsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizScene = _field
	return nil
}
func (p *CheckBizFeeItemsReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FeeNameList = _field
	return nil
}
func (p *CheckBizFeeItemsReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CheckBizFeeItemsReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckBizFeeItemsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckBizFeeItemsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckBizFeeItemsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_scene", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.BizScene); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckBizFeeItemsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fee_name_list", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.FeeNameList)); err != nil {
		return err
	}
	for _, v := range p.FeeNameList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CheckBizFeeItemsReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CheckBizFeeItemsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckBizFeeItemsReq(%+v)", *p)

}

type CheckBizFeeItemsResp struct {
	Ok       bool           `thrift:"ok,1" frugal:"1,default,bool" json:"ok"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewCheckBizFeeItemsResp() *CheckBizFeeItemsResp {
	return &CheckBizFeeItemsResp{}
}

func (p *CheckBizFeeItemsResp) InitDefault() {
}

func (p *CheckBizFeeItemsResp) GetOk() (v bool) {
	return p.Ok
}

var CheckBizFeeItemsResp_BaseResp_DEFAULT *base.BaseResp

func (p *CheckBizFeeItemsResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CheckBizFeeItemsResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CheckBizFeeItemsResp) SetOk(val bool) {
	p.Ok = val
}
func (p *CheckBizFeeItemsResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CheckBizFeeItemsResp = map[int16]string{
	1:   "ok",
	255: "BaseResp",
}

func (p *CheckBizFeeItemsResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CheckBizFeeItemsResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckBizFeeItemsResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckBizFeeItemsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckBizFeeItemsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Ok = _field
	return nil
}
func (p *CheckBizFeeItemsResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CheckBizFeeItemsResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CheckBizFeeItemsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckBizFeeItemsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckBizFeeItemsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ok", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Ok); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckBizFeeItemsResp) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CheckBizFeeItemsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckBizFeeItemsResp(%+v)", *p)

}

type FeeService interface {
	QueryFeeItem(ctx context.Context, req *QueryFeeItemReq) (r *QueryFeeItemResp, err error)

	UpsertFeeItem(ctx context.Context, req *UpsertFeeItemReq) (r *UpsertFeeItemResp, err error)

	DeleteFeeItem(ctx context.Context, req *DeleteFeeItemReq) (r *DeleteFeeItemResp, err error)

	QueryFeeRules(ctx context.Context, req *QueryFeeRulesReq) (r *QueryFeeRulesResp, err error)

	UpsertFeeRule(ctx context.Context, req *UpsertFeeRuleReq) (r *UpsertFeeRuleResp, err error)

	DeleteFeeRule(ctx context.Context, req *DeleteFeeRuleReq) (r *DeleteFeeRuleResp, err error)

	QueryFeeFormulas(ctx context.Context, req *QueryFeeFormulasReq) (r *QueryFeeFormulasResp, err error)

	UpsertFeeFormula(ctx context.Context, req *UpsertFeeFormulaReq) (r *UpsertFeeFormulaResp, err error)

	DeleteFeeFormula(ctx context.Context, req *DeleteFeeFormulaReq) (r *DeleteFeeFormulaResp, err error)

	ChargeSettle(ctx context.Context, req *ChargeSettleReq) (r *ChargeSettleResp, err error)

	ChargeRefund(ctx context.Context, req *ChargeRefundReq) (r *ChargeRefundResp, err error)

	CommonCharge(ctx context.Context, req *CommonChargeReq) (r *CommonChargeResp, err error)

	MGetChargeRecord(ctx context.Context, req *MGetChargeRecordReq) (r *MGetChargeRecordResp, err error)

	CheckBizFeeItems(ctx context.Context, req *CheckBizFeeItemsReq) (r *CheckBizFeeItemsResp, err error)
}

type FeeServiceQueryFeeItemArgs struct {
	Req *QueryFeeItemReq `thrift:"req,1" frugal:"1,default,QueryFeeItemReq" json:"req"`
}

func NewFeeServiceQueryFeeItemArgs() *FeeServiceQueryFeeItemArgs {
	return &FeeServiceQueryFeeItemArgs{}
}

func (p *FeeServiceQueryFeeItemArgs) InitDefault() {
}

var FeeServiceQueryFeeItemArgs_Req_DEFAULT *QueryFeeItemReq

func (p *FeeServiceQueryFeeItemArgs) GetReq() (v *QueryFeeItemReq) {
	if !p.IsSetReq() {
		return FeeServiceQueryFeeItemArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceQueryFeeItemArgs) SetReq(val *QueryFeeItemReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceQueryFeeItemArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceQueryFeeItemArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceQueryFeeItemArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeItemArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeItemArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeItemArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewQueryFeeItemReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceQueryFeeItemArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeItemArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeItem_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeItemArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceQueryFeeItemArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceQueryFeeItemArgs(%+v)", *p)

}

type FeeServiceQueryFeeItemResult struct {
	Success *QueryFeeItemResp `thrift:"success,0,optional" frugal:"0,optional,QueryFeeItemResp" json:"success,omitempty"`
}

func NewFeeServiceQueryFeeItemResult() *FeeServiceQueryFeeItemResult {
	return &FeeServiceQueryFeeItemResult{}
}

func (p *FeeServiceQueryFeeItemResult) InitDefault() {
}

var FeeServiceQueryFeeItemResult_Success_DEFAULT *QueryFeeItemResp

func (p *FeeServiceQueryFeeItemResult) GetSuccess() (v *QueryFeeItemResp) {
	if !p.IsSetSuccess() {
		return FeeServiceQueryFeeItemResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceQueryFeeItemResult) SetSuccess(x interface{}) {
	p.Success = x.(*QueryFeeItemResp)
}

var fieldIDToName_FeeServiceQueryFeeItemResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceQueryFeeItemResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceQueryFeeItemResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeItemResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeItemResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeItemResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewQueryFeeItemResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceQueryFeeItemResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeItemResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeItem_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeItemResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceQueryFeeItemResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceQueryFeeItemResult(%+v)", *p)

}

type FeeServiceUpsertFeeItemArgs struct {
	Req *UpsertFeeItemReq `thrift:"req,1" frugal:"1,default,UpsertFeeItemReq" json:"req"`
}

func NewFeeServiceUpsertFeeItemArgs() *FeeServiceUpsertFeeItemArgs {
	return &FeeServiceUpsertFeeItemArgs{}
}

func (p *FeeServiceUpsertFeeItemArgs) InitDefault() {
}

var FeeServiceUpsertFeeItemArgs_Req_DEFAULT *UpsertFeeItemReq

func (p *FeeServiceUpsertFeeItemArgs) GetReq() (v *UpsertFeeItemReq) {
	if !p.IsSetReq() {
		return FeeServiceUpsertFeeItemArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceUpsertFeeItemArgs) SetReq(val *UpsertFeeItemReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceUpsertFeeItemArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceUpsertFeeItemArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceUpsertFeeItemArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeItemArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeItemArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeItemArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpsertFeeItemReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceUpsertFeeItemArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeItemArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeItem_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeItemArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceUpsertFeeItemArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceUpsertFeeItemArgs(%+v)", *p)

}

type FeeServiceUpsertFeeItemResult struct {
	Success *UpsertFeeItemResp `thrift:"success,0,optional" frugal:"0,optional,UpsertFeeItemResp" json:"success,omitempty"`
}

func NewFeeServiceUpsertFeeItemResult() *FeeServiceUpsertFeeItemResult {
	return &FeeServiceUpsertFeeItemResult{}
}

func (p *FeeServiceUpsertFeeItemResult) InitDefault() {
}

var FeeServiceUpsertFeeItemResult_Success_DEFAULT *UpsertFeeItemResp

func (p *FeeServiceUpsertFeeItemResult) GetSuccess() (v *UpsertFeeItemResp) {
	if !p.IsSetSuccess() {
		return FeeServiceUpsertFeeItemResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceUpsertFeeItemResult) SetSuccess(x interface{}) {
	p.Success = x.(*UpsertFeeItemResp)
}

var fieldIDToName_FeeServiceUpsertFeeItemResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceUpsertFeeItemResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceUpsertFeeItemResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeItemResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeItemResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeItemResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpsertFeeItemResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceUpsertFeeItemResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeItemResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeItem_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeItemResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceUpsertFeeItemResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceUpsertFeeItemResult(%+v)", *p)

}

type FeeServiceDeleteFeeItemArgs struct {
	Req *DeleteFeeItemReq `thrift:"req,1" frugal:"1,default,DeleteFeeItemReq" json:"req"`
}

func NewFeeServiceDeleteFeeItemArgs() *FeeServiceDeleteFeeItemArgs {
	return &FeeServiceDeleteFeeItemArgs{}
}

func (p *FeeServiceDeleteFeeItemArgs) InitDefault() {
}

var FeeServiceDeleteFeeItemArgs_Req_DEFAULT *DeleteFeeItemReq

func (p *FeeServiceDeleteFeeItemArgs) GetReq() (v *DeleteFeeItemReq) {
	if !p.IsSetReq() {
		return FeeServiceDeleteFeeItemArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceDeleteFeeItemArgs) SetReq(val *DeleteFeeItemReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceDeleteFeeItemArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceDeleteFeeItemArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceDeleteFeeItemArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeItemArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeItemArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeItemArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDeleteFeeItemReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceDeleteFeeItemArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeItemArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeItem_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeItemArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceDeleteFeeItemArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceDeleteFeeItemArgs(%+v)", *p)

}

type FeeServiceDeleteFeeItemResult struct {
	Success *DeleteFeeItemResp `thrift:"success,0,optional" frugal:"0,optional,DeleteFeeItemResp" json:"success,omitempty"`
}

func NewFeeServiceDeleteFeeItemResult() *FeeServiceDeleteFeeItemResult {
	return &FeeServiceDeleteFeeItemResult{}
}

func (p *FeeServiceDeleteFeeItemResult) InitDefault() {
}

var FeeServiceDeleteFeeItemResult_Success_DEFAULT *DeleteFeeItemResp

func (p *FeeServiceDeleteFeeItemResult) GetSuccess() (v *DeleteFeeItemResp) {
	if !p.IsSetSuccess() {
		return FeeServiceDeleteFeeItemResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceDeleteFeeItemResult) SetSuccess(x interface{}) {
	p.Success = x.(*DeleteFeeItemResp)
}

var fieldIDToName_FeeServiceDeleteFeeItemResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceDeleteFeeItemResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceDeleteFeeItemResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeItemResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeItemResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeItemResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDeleteFeeItemResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceDeleteFeeItemResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeItemResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeItem_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeItemResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceDeleteFeeItemResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceDeleteFeeItemResult(%+v)", *p)

}

type FeeServiceQueryFeeRulesArgs struct {
	Req *QueryFeeRulesReq `thrift:"req,1" frugal:"1,default,QueryFeeRulesReq" json:"req"`
}

func NewFeeServiceQueryFeeRulesArgs() *FeeServiceQueryFeeRulesArgs {
	return &FeeServiceQueryFeeRulesArgs{}
}

func (p *FeeServiceQueryFeeRulesArgs) InitDefault() {
}

var FeeServiceQueryFeeRulesArgs_Req_DEFAULT *QueryFeeRulesReq

func (p *FeeServiceQueryFeeRulesArgs) GetReq() (v *QueryFeeRulesReq) {
	if !p.IsSetReq() {
		return FeeServiceQueryFeeRulesArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceQueryFeeRulesArgs) SetReq(val *QueryFeeRulesReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceQueryFeeRulesArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceQueryFeeRulesArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceQueryFeeRulesArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeRulesArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeRulesArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeRulesArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewQueryFeeRulesReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceQueryFeeRulesArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeRulesArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeRules_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeRulesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceQueryFeeRulesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceQueryFeeRulesArgs(%+v)", *p)

}

type FeeServiceQueryFeeRulesResult struct {
	Success *QueryFeeRulesResp `thrift:"success,0,optional" frugal:"0,optional,QueryFeeRulesResp" json:"success,omitempty"`
}

func NewFeeServiceQueryFeeRulesResult() *FeeServiceQueryFeeRulesResult {
	return &FeeServiceQueryFeeRulesResult{}
}

func (p *FeeServiceQueryFeeRulesResult) InitDefault() {
}

var FeeServiceQueryFeeRulesResult_Success_DEFAULT *QueryFeeRulesResp

func (p *FeeServiceQueryFeeRulesResult) GetSuccess() (v *QueryFeeRulesResp) {
	if !p.IsSetSuccess() {
		return FeeServiceQueryFeeRulesResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceQueryFeeRulesResult) SetSuccess(x interface{}) {
	p.Success = x.(*QueryFeeRulesResp)
}

var fieldIDToName_FeeServiceQueryFeeRulesResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceQueryFeeRulesResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceQueryFeeRulesResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeRulesResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeRulesResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeRulesResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewQueryFeeRulesResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceQueryFeeRulesResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeRulesResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeRules_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeRulesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceQueryFeeRulesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceQueryFeeRulesResult(%+v)", *p)

}

type FeeServiceUpsertFeeRuleArgs struct {
	Req *UpsertFeeRuleReq `thrift:"req,1" frugal:"1,default,UpsertFeeRuleReq" json:"req"`
}

func NewFeeServiceUpsertFeeRuleArgs() *FeeServiceUpsertFeeRuleArgs {
	return &FeeServiceUpsertFeeRuleArgs{}
}

func (p *FeeServiceUpsertFeeRuleArgs) InitDefault() {
}

var FeeServiceUpsertFeeRuleArgs_Req_DEFAULT *UpsertFeeRuleReq

func (p *FeeServiceUpsertFeeRuleArgs) GetReq() (v *UpsertFeeRuleReq) {
	if !p.IsSetReq() {
		return FeeServiceUpsertFeeRuleArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceUpsertFeeRuleArgs) SetReq(val *UpsertFeeRuleReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceUpsertFeeRuleArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceUpsertFeeRuleArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceUpsertFeeRuleArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeRuleArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeRuleArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeRuleArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpsertFeeRuleReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceUpsertFeeRuleArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeRuleArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeRule_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeRuleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceUpsertFeeRuleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceUpsertFeeRuleArgs(%+v)", *p)

}

type FeeServiceUpsertFeeRuleResult struct {
	Success *UpsertFeeRuleResp `thrift:"success,0,optional" frugal:"0,optional,UpsertFeeRuleResp" json:"success,omitempty"`
}

func NewFeeServiceUpsertFeeRuleResult() *FeeServiceUpsertFeeRuleResult {
	return &FeeServiceUpsertFeeRuleResult{}
}

func (p *FeeServiceUpsertFeeRuleResult) InitDefault() {
}

var FeeServiceUpsertFeeRuleResult_Success_DEFAULT *UpsertFeeRuleResp

func (p *FeeServiceUpsertFeeRuleResult) GetSuccess() (v *UpsertFeeRuleResp) {
	if !p.IsSetSuccess() {
		return FeeServiceUpsertFeeRuleResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceUpsertFeeRuleResult) SetSuccess(x interface{}) {
	p.Success = x.(*UpsertFeeRuleResp)
}

var fieldIDToName_FeeServiceUpsertFeeRuleResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceUpsertFeeRuleResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceUpsertFeeRuleResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeRuleResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeRuleResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeRuleResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpsertFeeRuleResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceUpsertFeeRuleResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeRuleResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeRule_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeRuleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceUpsertFeeRuleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceUpsertFeeRuleResult(%+v)", *p)

}

type FeeServiceDeleteFeeRuleArgs struct {
	Req *DeleteFeeRuleReq `thrift:"req,1" frugal:"1,default,DeleteFeeRuleReq" json:"req"`
}

func NewFeeServiceDeleteFeeRuleArgs() *FeeServiceDeleteFeeRuleArgs {
	return &FeeServiceDeleteFeeRuleArgs{}
}

func (p *FeeServiceDeleteFeeRuleArgs) InitDefault() {
}

var FeeServiceDeleteFeeRuleArgs_Req_DEFAULT *DeleteFeeRuleReq

func (p *FeeServiceDeleteFeeRuleArgs) GetReq() (v *DeleteFeeRuleReq) {
	if !p.IsSetReq() {
		return FeeServiceDeleteFeeRuleArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceDeleteFeeRuleArgs) SetReq(val *DeleteFeeRuleReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceDeleteFeeRuleArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceDeleteFeeRuleArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceDeleteFeeRuleArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeRuleArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeRuleArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeRuleArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDeleteFeeRuleReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceDeleteFeeRuleArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeRuleArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeRule_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeRuleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceDeleteFeeRuleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceDeleteFeeRuleArgs(%+v)", *p)

}

type FeeServiceDeleteFeeRuleResult struct {
	Success *DeleteFeeRuleResp `thrift:"success,0,optional" frugal:"0,optional,DeleteFeeRuleResp" json:"success,omitempty"`
}

func NewFeeServiceDeleteFeeRuleResult() *FeeServiceDeleteFeeRuleResult {
	return &FeeServiceDeleteFeeRuleResult{}
}

func (p *FeeServiceDeleteFeeRuleResult) InitDefault() {
}

var FeeServiceDeleteFeeRuleResult_Success_DEFAULT *DeleteFeeRuleResp

func (p *FeeServiceDeleteFeeRuleResult) GetSuccess() (v *DeleteFeeRuleResp) {
	if !p.IsSetSuccess() {
		return FeeServiceDeleteFeeRuleResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceDeleteFeeRuleResult) SetSuccess(x interface{}) {
	p.Success = x.(*DeleteFeeRuleResp)
}

var fieldIDToName_FeeServiceDeleteFeeRuleResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceDeleteFeeRuleResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceDeleteFeeRuleResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeRuleResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeRuleResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeRuleResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDeleteFeeRuleResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceDeleteFeeRuleResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeRuleResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeRule_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeRuleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceDeleteFeeRuleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceDeleteFeeRuleResult(%+v)", *p)

}

type FeeServiceQueryFeeFormulasArgs struct {
	Req *QueryFeeFormulasReq `thrift:"req,1" frugal:"1,default,QueryFeeFormulasReq" json:"req"`
}

func NewFeeServiceQueryFeeFormulasArgs() *FeeServiceQueryFeeFormulasArgs {
	return &FeeServiceQueryFeeFormulasArgs{}
}

func (p *FeeServiceQueryFeeFormulasArgs) InitDefault() {
}

var FeeServiceQueryFeeFormulasArgs_Req_DEFAULT *QueryFeeFormulasReq

func (p *FeeServiceQueryFeeFormulasArgs) GetReq() (v *QueryFeeFormulasReq) {
	if !p.IsSetReq() {
		return FeeServiceQueryFeeFormulasArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceQueryFeeFormulasArgs) SetReq(val *QueryFeeFormulasReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceQueryFeeFormulasArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceQueryFeeFormulasArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceQueryFeeFormulasArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeFormulasArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeFormulasArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeFormulasArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewQueryFeeFormulasReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceQueryFeeFormulasArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeFormulasArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeFormulas_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeFormulasArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceQueryFeeFormulasArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceQueryFeeFormulasArgs(%+v)", *p)

}

type FeeServiceQueryFeeFormulasResult struct {
	Success *QueryFeeFormulasResp `thrift:"success,0,optional" frugal:"0,optional,QueryFeeFormulasResp" json:"success,omitempty"`
}

func NewFeeServiceQueryFeeFormulasResult() *FeeServiceQueryFeeFormulasResult {
	return &FeeServiceQueryFeeFormulasResult{}
}

func (p *FeeServiceQueryFeeFormulasResult) InitDefault() {
}

var FeeServiceQueryFeeFormulasResult_Success_DEFAULT *QueryFeeFormulasResp

func (p *FeeServiceQueryFeeFormulasResult) GetSuccess() (v *QueryFeeFormulasResp) {
	if !p.IsSetSuccess() {
		return FeeServiceQueryFeeFormulasResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceQueryFeeFormulasResult) SetSuccess(x interface{}) {
	p.Success = x.(*QueryFeeFormulasResp)
}

var fieldIDToName_FeeServiceQueryFeeFormulasResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceQueryFeeFormulasResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceQueryFeeFormulasResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeFormulasResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeFormulasResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeFormulasResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewQueryFeeFormulasResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceQueryFeeFormulasResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceQueryFeeFormulasResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryFeeFormulas_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceQueryFeeFormulasResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceQueryFeeFormulasResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceQueryFeeFormulasResult(%+v)", *p)

}

type FeeServiceUpsertFeeFormulaArgs struct {
	Req *UpsertFeeFormulaReq `thrift:"req,1" frugal:"1,default,UpsertFeeFormulaReq" json:"req"`
}

func NewFeeServiceUpsertFeeFormulaArgs() *FeeServiceUpsertFeeFormulaArgs {
	return &FeeServiceUpsertFeeFormulaArgs{}
}

func (p *FeeServiceUpsertFeeFormulaArgs) InitDefault() {
}

var FeeServiceUpsertFeeFormulaArgs_Req_DEFAULT *UpsertFeeFormulaReq

func (p *FeeServiceUpsertFeeFormulaArgs) GetReq() (v *UpsertFeeFormulaReq) {
	if !p.IsSetReq() {
		return FeeServiceUpsertFeeFormulaArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceUpsertFeeFormulaArgs) SetReq(val *UpsertFeeFormulaReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceUpsertFeeFormulaArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceUpsertFeeFormulaArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceUpsertFeeFormulaArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeFormulaArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeFormulaArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeFormulaArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpsertFeeFormulaReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceUpsertFeeFormulaArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeFormulaArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeFormula_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeFormulaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceUpsertFeeFormulaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceUpsertFeeFormulaArgs(%+v)", *p)

}

type FeeServiceUpsertFeeFormulaResult struct {
	Success *UpsertFeeFormulaResp `thrift:"success,0,optional" frugal:"0,optional,UpsertFeeFormulaResp" json:"success,omitempty"`
}

func NewFeeServiceUpsertFeeFormulaResult() *FeeServiceUpsertFeeFormulaResult {
	return &FeeServiceUpsertFeeFormulaResult{}
}

func (p *FeeServiceUpsertFeeFormulaResult) InitDefault() {
}

var FeeServiceUpsertFeeFormulaResult_Success_DEFAULT *UpsertFeeFormulaResp

func (p *FeeServiceUpsertFeeFormulaResult) GetSuccess() (v *UpsertFeeFormulaResp) {
	if !p.IsSetSuccess() {
		return FeeServiceUpsertFeeFormulaResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceUpsertFeeFormulaResult) SetSuccess(x interface{}) {
	p.Success = x.(*UpsertFeeFormulaResp)
}

var fieldIDToName_FeeServiceUpsertFeeFormulaResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceUpsertFeeFormulaResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceUpsertFeeFormulaResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeFormulaResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeFormulaResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeFormulaResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpsertFeeFormulaResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceUpsertFeeFormulaResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceUpsertFeeFormulaResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpsertFeeFormula_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceUpsertFeeFormulaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceUpsertFeeFormulaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceUpsertFeeFormulaResult(%+v)", *p)

}

type FeeServiceDeleteFeeFormulaArgs struct {
	Req *DeleteFeeFormulaReq `thrift:"req,1" frugal:"1,default,DeleteFeeFormulaReq" json:"req"`
}

func NewFeeServiceDeleteFeeFormulaArgs() *FeeServiceDeleteFeeFormulaArgs {
	return &FeeServiceDeleteFeeFormulaArgs{}
}

func (p *FeeServiceDeleteFeeFormulaArgs) InitDefault() {
}

var FeeServiceDeleteFeeFormulaArgs_Req_DEFAULT *DeleteFeeFormulaReq

func (p *FeeServiceDeleteFeeFormulaArgs) GetReq() (v *DeleteFeeFormulaReq) {
	if !p.IsSetReq() {
		return FeeServiceDeleteFeeFormulaArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceDeleteFeeFormulaArgs) SetReq(val *DeleteFeeFormulaReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceDeleteFeeFormulaArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceDeleteFeeFormulaArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceDeleteFeeFormulaArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeFormulaArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeFormulaArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeFormulaArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDeleteFeeFormulaReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceDeleteFeeFormulaArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeFormulaArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeFormula_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeFormulaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceDeleteFeeFormulaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceDeleteFeeFormulaArgs(%+v)", *p)

}

type FeeServiceDeleteFeeFormulaResult struct {
	Success *DeleteFeeFormulaResp `thrift:"success,0,optional" frugal:"0,optional,DeleteFeeFormulaResp" json:"success,omitempty"`
}

func NewFeeServiceDeleteFeeFormulaResult() *FeeServiceDeleteFeeFormulaResult {
	return &FeeServiceDeleteFeeFormulaResult{}
}

func (p *FeeServiceDeleteFeeFormulaResult) InitDefault() {
}

var FeeServiceDeleteFeeFormulaResult_Success_DEFAULT *DeleteFeeFormulaResp

func (p *FeeServiceDeleteFeeFormulaResult) GetSuccess() (v *DeleteFeeFormulaResp) {
	if !p.IsSetSuccess() {
		return FeeServiceDeleteFeeFormulaResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceDeleteFeeFormulaResult) SetSuccess(x interface{}) {
	p.Success = x.(*DeleteFeeFormulaResp)
}

var fieldIDToName_FeeServiceDeleteFeeFormulaResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceDeleteFeeFormulaResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceDeleteFeeFormulaResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeFormulaResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeFormulaResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeFormulaResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDeleteFeeFormulaResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceDeleteFeeFormulaResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceDeleteFeeFormulaResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteFeeFormula_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceDeleteFeeFormulaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceDeleteFeeFormulaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceDeleteFeeFormulaResult(%+v)", *p)

}

type FeeServiceChargeSettleArgs struct {
	Req *ChargeSettleReq `thrift:"req,1" frugal:"1,default,ChargeSettleReq" json:"req"`
}

func NewFeeServiceChargeSettleArgs() *FeeServiceChargeSettleArgs {
	return &FeeServiceChargeSettleArgs{}
}

func (p *FeeServiceChargeSettleArgs) InitDefault() {
}

var FeeServiceChargeSettleArgs_Req_DEFAULT *ChargeSettleReq

func (p *FeeServiceChargeSettleArgs) GetReq() (v *ChargeSettleReq) {
	if !p.IsSetReq() {
		return FeeServiceChargeSettleArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceChargeSettleArgs) SetReq(val *ChargeSettleReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceChargeSettleArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceChargeSettleArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceChargeSettleArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceChargeSettleArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceChargeSettleArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceChargeSettleArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewChargeSettleReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceChargeSettleArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceChargeSettleArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeSettle_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceChargeSettleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceChargeSettleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceChargeSettleArgs(%+v)", *p)

}

type FeeServiceChargeSettleResult struct {
	Success *ChargeSettleResp `thrift:"success,0,optional" frugal:"0,optional,ChargeSettleResp" json:"success,omitempty"`
}

func NewFeeServiceChargeSettleResult() *FeeServiceChargeSettleResult {
	return &FeeServiceChargeSettleResult{}
}

func (p *FeeServiceChargeSettleResult) InitDefault() {
}

var FeeServiceChargeSettleResult_Success_DEFAULT *ChargeSettleResp

func (p *FeeServiceChargeSettleResult) GetSuccess() (v *ChargeSettleResp) {
	if !p.IsSetSuccess() {
		return FeeServiceChargeSettleResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceChargeSettleResult) SetSuccess(x interface{}) {
	p.Success = x.(*ChargeSettleResp)
}

var fieldIDToName_FeeServiceChargeSettleResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceChargeSettleResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceChargeSettleResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceChargeSettleResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceChargeSettleResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceChargeSettleResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewChargeSettleResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceChargeSettleResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceChargeSettleResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeSettle_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceChargeSettleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceChargeSettleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceChargeSettleResult(%+v)", *p)

}

type FeeServiceChargeRefundArgs struct {
	Req *ChargeRefundReq `thrift:"req,1" frugal:"1,default,ChargeRefundReq" json:"req"`
}

func NewFeeServiceChargeRefundArgs() *FeeServiceChargeRefundArgs {
	return &FeeServiceChargeRefundArgs{}
}

func (p *FeeServiceChargeRefundArgs) InitDefault() {
}

var FeeServiceChargeRefundArgs_Req_DEFAULT *ChargeRefundReq

func (p *FeeServiceChargeRefundArgs) GetReq() (v *ChargeRefundReq) {
	if !p.IsSetReq() {
		return FeeServiceChargeRefundArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceChargeRefundArgs) SetReq(val *ChargeRefundReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceChargeRefundArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceChargeRefundArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceChargeRefundArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceChargeRefundArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceChargeRefundArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceChargeRefundArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewChargeRefundReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceChargeRefundArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceChargeRefundArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeRefund_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceChargeRefundArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceChargeRefundArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceChargeRefundArgs(%+v)", *p)

}

type FeeServiceChargeRefundResult struct {
	Success *ChargeRefundResp `thrift:"success,0,optional" frugal:"0,optional,ChargeRefundResp" json:"success,omitempty"`
}

func NewFeeServiceChargeRefundResult() *FeeServiceChargeRefundResult {
	return &FeeServiceChargeRefundResult{}
}

func (p *FeeServiceChargeRefundResult) InitDefault() {
}

var FeeServiceChargeRefundResult_Success_DEFAULT *ChargeRefundResp

func (p *FeeServiceChargeRefundResult) GetSuccess() (v *ChargeRefundResp) {
	if !p.IsSetSuccess() {
		return FeeServiceChargeRefundResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceChargeRefundResult) SetSuccess(x interface{}) {
	p.Success = x.(*ChargeRefundResp)
}

var fieldIDToName_FeeServiceChargeRefundResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceChargeRefundResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceChargeRefundResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceChargeRefundResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceChargeRefundResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceChargeRefundResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewChargeRefundResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceChargeRefundResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceChargeRefundResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("ChargeRefund_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceChargeRefundResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceChargeRefundResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceChargeRefundResult(%+v)", *p)

}

type FeeServiceCommonChargeArgs struct {
	Req *CommonChargeReq `thrift:"req,1" frugal:"1,default,CommonChargeReq" json:"req"`
}

func NewFeeServiceCommonChargeArgs() *FeeServiceCommonChargeArgs {
	return &FeeServiceCommonChargeArgs{}
}

func (p *FeeServiceCommonChargeArgs) InitDefault() {
}

var FeeServiceCommonChargeArgs_Req_DEFAULT *CommonChargeReq

func (p *FeeServiceCommonChargeArgs) GetReq() (v *CommonChargeReq) {
	if !p.IsSetReq() {
		return FeeServiceCommonChargeArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceCommonChargeArgs) SetReq(val *CommonChargeReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceCommonChargeArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceCommonChargeArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceCommonChargeArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceCommonChargeArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceCommonChargeArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceCommonChargeArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCommonChargeReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceCommonChargeArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceCommonChargeArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CommonCharge_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceCommonChargeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceCommonChargeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceCommonChargeArgs(%+v)", *p)

}

type FeeServiceCommonChargeResult struct {
	Success *CommonChargeResp `thrift:"success,0,optional" frugal:"0,optional,CommonChargeResp" json:"success,omitempty"`
}

func NewFeeServiceCommonChargeResult() *FeeServiceCommonChargeResult {
	return &FeeServiceCommonChargeResult{}
}

func (p *FeeServiceCommonChargeResult) InitDefault() {
}

var FeeServiceCommonChargeResult_Success_DEFAULT *CommonChargeResp

func (p *FeeServiceCommonChargeResult) GetSuccess() (v *CommonChargeResp) {
	if !p.IsSetSuccess() {
		return FeeServiceCommonChargeResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceCommonChargeResult) SetSuccess(x interface{}) {
	p.Success = x.(*CommonChargeResp)
}

var fieldIDToName_FeeServiceCommonChargeResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceCommonChargeResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceCommonChargeResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceCommonChargeResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceCommonChargeResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceCommonChargeResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCommonChargeResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceCommonChargeResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceCommonChargeResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CommonCharge_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceCommonChargeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceCommonChargeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceCommonChargeResult(%+v)", *p)

}

type FeeServiceMGetChargeRecordArgs struct {
	Req *MGetChargeRecordReq `thrift:"req,1" frugal:"1,default,MGetChargeRecordReq" json:"req"`
}

func NewFeeServiceMGetChargeRecordArgs() *FeeServiceMGetChargeRecordArgs {
	return &FeeServiceMGetChargeRecordArgs{}
}

func (p *FeeServiceMGetChargeRecordArgs) InitDefault() {
}

var FeeServiceMGetChargeRecordArgs_Req_DEFAULT *MGetChargeRecordReq

func (p *FeeServiceMGetChargeRecordArgs) GetReq() (v *MGetChargeRecordReq) {
	if !p.IsSetReq() {
		return FeeServiceMGetChargeRecordArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceMGetChargeRecordArgs) SetReq(val *MGetChargeRecordReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceMGetChargeRecordArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceMGetChargeRecordArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceMGetChargeRecordArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceMGetChargeRecordArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceMGetChargeRecordArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceMGetChargeRecordArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewMGetChargeRecordReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceMGetChargeRecordArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceMGetChargeRecordArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("MGetChargeRecord_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceMGetChargeRecordArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceMGetChargeRecordArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceMGetChargeRecordArgs(%+v)", *p)

}

type FeeServiceMGetChargeRecordResult struct {
	Success *MGetChargeRecordResp `thrift:"success,0,optional" frugal:"0,optional,MGetChargeRecordResp" json:"success,omitempty"`
}

func NewFeeServiceMGetChargeRecordResult() *FeeServiceMGetChargeRecordResult {
	return &FeeServiceMGetChargeRecordResult{}
}

func (p *FeeServiceMGetChargeRecordResult) InitDefault() {
}

var FeeServiceMGetChargeRecordResult_Success_DEFAULT *MGetChargeRecordResp

func (p *FeeServiceMGetChargeRecordResult) GetSuccess() (v *MGetChargeRecordResp) {
	if !p.IsSetSuccess() {
		return FeeServiceMGetChargeRecordResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceMGetChargeRecordResult) SetSuccess(x interface{}) {
	p.Success = x.(*MGetChargeRecordResp)
}

var fieldIDToName_FeeServiceMGetChargeRecordResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceMGetChargeRecordResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceMGetChargeRecordResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceMGetChargeRecordResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceMGetChargeRecordResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceMGetChargeRecordResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewMGetChargeRecordResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceMGetChargeRecordResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceMGetChargeRecordResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("MGetChargeRecord_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceMGetChargeRecordResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceMGetChargeRecordResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceMGetChargeRecordResult(%+v)", *p)

}

type FeeServiceCheckBizFeeItemsArgs struct {
	Req *CheckBizFeeItemsReq `thrift:"req,1" frugal:"1,default,CheckBizFeeItemsReq" json:"req"`
}

func NewFeeServiceCheckBizFeeItemsArgs() *FeeServiceCheckBizFeeItemsArgs {
	return &FeeServiceCheckBizFeeItemsArgs{}
}

func (p *FeeServiceCheckBizFeeItemsArgs) InitDefault() {
}

var FeeServiceCheckBizFeeItemsArgs_Req_DEFAULT *CheckBizFeeItemsReq

func (p *FeeServiceCheckBizFeeItemsArgs) GetReq() (v *CheckBizFeeItemsReq) {
	if !p.IsSetReq() {
		return FeeServiceCheckBizFeeItemsArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *FeeServiceCheckBizFeeItemsArgs) SetReq(val *CheckBizFeeItemsReq) {
	p.Req = val
}

var fieldIDToName_FeeServiceCheckBizFeeItemsArgs = map[int16]string{
	1: "req",
}

func (p *FeeServiceCheckBizFeeItemsArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *FeeServiceCheckBizFeeItemsArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceCheckBizFeeItemsArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceCheckBizFeeItemsArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceCheckBizFeeItemsArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCheckBizFeeItemsReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *FeeServiceCheckBizFeeItemsArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceCheckBizFeeItemsArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckBizFeeItems_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceCheckBizFeeItemsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *FeeServiceCheckBizFeeItemsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceCheckBizFeeItemsArgs(%+v)", *p)

}

type FeeServiceCheckBizFeeItemsResult struct {
	Success *CheckBizFeeItemsResp `thrift:"success,0,optional" frugal:"0,optional,CheckBizFeeItemsResp" json:"success,omitempty"`
}

func NewFeeServiceCheckBizFeeItemsResult() *FeeServiceCheckBizFeeItemsResult {
	return &FeeServiceCheckBizFeeItemsResult{}
}

func (p *FeeServiceCheckBizFeeItemsResult) InitDefault() {
}

var FeeServiceCheckBizFeeItemsResult_Success_DEFAULT *CheckBizFeeItemsResp

func (p *FeeServiceCheckBizFeeItemsResult) GetSuccess() (v *CheckBizFeeItemsResp) {
	if !p.IsSetSuccess() {
		return FeeServiceCheckBizFeeItemsResult_Success_DEFAULT
	}
	return p.Success
}
func (p *FeeServiceCheckBizFeeItemsResult) SetSuccess(x interface{}) {
	p.Success = x.(*CheckBizFeeItemsResp)
}

var fieldIDToName_FeeServiceCheckBizFeeItemsResult = map[int16]string{
	0: "success",
}

func (p *FeeServiceCheckBizFeeItemsResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *FeeServiceCheckBizFeeItemsResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceCheckBizFeeItemsResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceCheckBizFeeItemsResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FeeServiceCheckBizFeeItemsResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCheckBizFeeItemsResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *FeeServiceCheckBizFeeItemsResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FeeServiceCheckBizFeeItemsResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckBizFeeItems_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FeeServiceCheckBizFeeItemsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *FeeServiceCheckBizFeeItemsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeeServiceCheckBizFeeItemsResult(%+v)", *p)

}
