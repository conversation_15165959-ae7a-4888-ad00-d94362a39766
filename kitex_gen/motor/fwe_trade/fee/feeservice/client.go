// Code generated by Kitex v1.20.3. DO NOT EDIT.

package feeservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	fee "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/fee"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	QueryFeeItem(ctx context.Context, req *fee.QueryFeeItemReq, callOptions ...callopt.Option) (r *fee.QueryFeeItemResp, err error)
	UpsertFeeItem(ctx context.Context, req *fee.UpsertFeeItemReq, callOptions ...callopt.Option) (r *fee.UpsertFeeItemResp, err error)
	DeleteFeeItem(ctx context.Context, req *fee.DeleteFeeItemReq, callOptions ...callopt.Option) (r *fee.DeleteFeeItemResp, err error)
	QueryFeeRules(ctx context.Context, req *fee.QueryFeeRulesReq, callOptions ...callopt.Option) (r *fee.QueryFeeRulesResp, err error)
	UpsertFeeRule(ctx context.Context, req *fee.UpsertFeeRuleReq, callOptions ...callopt.Option) (r *fee.UpsertFeeRuleResp, err error)
	DeleteFeeRule(ctx context.Context, req *fee.DeleteFeeRuleReq, callOptions ...callopt.Option) (r *fee.DeleteFeeRuleResp, err error)
	QueryFeeFormulas(ctx context.Context, req *fee.QueryFeeFormulasReq, callOptions ...callopt.Option) (r *fee.QueryFeeFormulasResp, err error)
	UpsertFeeFormula(ctx context.Context, req *fee.UpsertFeeFormulaReq, callOptions ...callopt.Option) (r *fee.UpsertFeeFormulaResp, err error)
	DeleteFeeFormula(ctx context.Context, req *fee.DeleteFeeFormulaReq, callOptions ...callopt.Option) (r *fee.DeleteFeeFormulaResp, err error)
	ChargeSettle(ctx context.Context, req *fee.ChargeSettleReq, callOptions ...callopt.Option) (r *fee.ChargeSettleResp, err error)
	ChargeRefund(ctx context.Context, req *fee.ChargeRefundReq, callOptions ...callopt.Option) (r *fee.ChargeRefundResp, err error)
	CommonCharge(ctx context.Context, req *fee.CommonChargeReq, callOptions ...callopt.Option) (r *fee.CommonChargeResp, err error)
	MGetChargeRecord(ctx context.Context, req *fee.MGetChargeRecordReq, callOptions ...callopt.Option) (r *fee.MGetChargeRecordResp, err error)
	CheckBizFeeItems(ctx context.Context, req *fee.CheckBizFeeItemsReq, callOptions ...callopt.Option) (r *fee.CheckBizFeeItemsResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kFeeServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kFeeServiceClient struct {
	*kClient
}

func (p *kFeeServiceClient) QueryFeeItem(ctx context.Context, req *fee.QueryFeeItemReq, callOptions ...callopt.Option) (r *fee.QueryFeeItemResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFeeItem(ctx, req)
}

func (p *kFeeServiceClient) UpsertFeeItem(ctx context.Context, req *fee.UpsertFeeItemReq, callOptions ...callopt.Option) (r *fee.UpsertFeeItemResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpsertFeeItem(ctx, req)
}

func (p *kFeeServiceClient) DeleteFeeItem(ctx context.Context, req *fee.DeleteFeeItemReq, callOptions ...callopt.Option) (r *fee.DeleteFeeItemResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DeleteFeeItem(ctx, req)
}

func (p *kFeeServiceClient) QueryFeeRules(ctx context.Context, req *fee.QueryFeeRulesReq, callOptions ...callopt.Option) (r *fee.QueryFeeRulesResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFeeRules(ctx, req)
}

func (p *kFeeServiceClient) UpsertFeeRule(ctx context.Context, req *fee.UpsertFeeRuleReq, callOptions ...callopt.Option) (r *fee.UpsertFeeRuleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpsertFeeRule(ctx, req)
}

func (p *kFeeServiceClient) DeleteFeeRule(ctx context.Context, req *fee.DeleteFeeRuleReq, callOptions ...callopt.Option) (r *fee.DeleteFeeRuleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DeleteFeeRule(ctx, req)
}

func (p *kFeeServiceClient) QueryFeeFormulas(ctx context.Context, req *fee.QueryFeeFormulasReq, callOptions ...callopt.Option) (r *fee.QueryFeeFormulasResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFeeFormulas(ctx, req)
}

func (p *kFeeServiceClient) UpsertFeeFormula(ctx context.Context, req *fee.UpsertFeeFormulaReq, callOptions ...callopt.Option) (r *fee.UpsertFeeFormulaResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpsertFeeFormula(ctx, req)
}

func (p *kFeeServiceClient) DeleteFeeFormula(ctx context.Context, req *fee.DeleteFeeFormulaReq, callOptions ...callopt.Option) (r *fee.DeleteFeeFormulaResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DeleteFeeFormula(ctx, req)
}

func (p *kFeeServiceClient) ChargeSettle(ctx context.Context, req *fee.ChargeSettleReq, callOptions ...callopt.Option) (r *fee.ChargeSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ChargeSettle(ctx, req)
}

func (p *kFeeServiceClient) ChargeRefund(ctx context.Context, req *fee.ChargeRefundReq, callOptions ...callopt.Option) (r *fee.ChargeRefundResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ChargeRefund(ctx, req)
}

func (p *kFeeServiceClient) CommonCharge(ctx context.Context, req *fee.CommonChargeReq, callOptions ...callopt.Option) (r *fee.CommonChargeResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CommonCharge(ctx, req)
}

func (p *kFeeServiceClient) MGetChargeRecord(ctx context.Context, req *fee.MGetChargeRecordReq, callOptions ...callopt.Option) (r *fee.MGetChargeRecordResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetChargeRecord(ctx, req)
}

func (p *kFeeServiceClient) CheckBizFeeItems(ctx context.Context, req *fee.CheckBizFeeItemsReq, callOptions ...callopt.Option) (r *fee.CheckBizFeeItemsResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CheckBizFeeItems(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kFeeServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
