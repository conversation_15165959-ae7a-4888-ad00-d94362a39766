// Code generated by Kitex v1.20.3. DO NOT EDIT.

package feeservice

import (
	client "code.byted.org/kite/kitex/client"
	fee "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/fee"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"QueryFeeItem": kitex.NewMethodInfo(
		queryFeeItemHandler,
		newFeeServiceQueryFeeItemArgs,
		newFeeServiceQueryFeeItemResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpsertFeeItem": kitex.NewMethodInfo(
		upsertFeeItemHandler,
		newFeeServiceUpsertFeeItemArgs,
		newFeeServiceUpsertFeeItemResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DeleteFeeItem": kitex.NewMethodInfo(
		deleteFeeItemHandler,
		newFeeServiceDeleteFeeItemArgs,
		newFeeServiceDeleteFeeItemResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryFeeRules": kitex.NewMethodInfo(
		queryFeeRulesHandler,
		newFeeServiceQueryFeeRulesArgs,
		newFeeServiceQueryFeeRulesResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpsertFeeRule": kitex.NewMethodInfo(
		upsertFeeRuleHandler,
		newFeeServiceUpsertFeeRuleArgs,
		newFeeServiceUpsertFeeRuleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DeleteFeeRule": kitex.NewMethodInfo(
		deleteFeeRuleHandler,
		newFeeServiceDeleteFeeRuleArgs,
		newFeeServiceDeleteFeeRuleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryFeeFormulas": kitex.NewMethodInfo(
		queryFeeFormulasHandler,
		newFeeServiceQueryFeeFormulasArgs,
		newFeeServiceQueryFeeFormulasResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpsertFeeFormula": kitex.NewMethodInfo(
		upsertFeeFormulaHandler,
		newFeeServiceUpsertFeeFormulaArgs,
		newFeeServiceUpsertFeeFormulaResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DeleteFeeFormula": kitex.NewMethodInfo(
		deleteFeeFormulaHandler,
		newFeeServiceDeleteFeeFormulaArgs,
		newFeeServiceDeleteFeeFormulaResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ChargeSettle": kitex.NewMethodInfo(
		chargeSettleHandler,
		newFeeServiceChargeSettleArgs,
		newFeeServiceChargeSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ChargeRefund": kitex.NewMethodInfo(
		chargeRefundHandler,
		newFeeServiceChargeRefundArgs,
		newFeeServiceChargeRefundResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CommonCharge": kitex.NewMethodInfo(
		commonChargeHandler,
		newFeeServiceCommonChargeArgs,
		newFeeServiceCommonChargeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetChargeRecord": kitex.NewMethodInfo(
		mGetChargeRecordHandler,
		newFeeServiceMGetChargeRecordArgs,
		newFeeServiceMGetChargeRecordResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CheckBizFeeItems": kitex.NewMethodInfo(
		checkBizFeeItemsHandler,
		newFeeServiceCheckBizFeeItemsArgs,
		newFeeServiceCheckBizFeeItemsResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	feeServiceServiceInfo                = NewServiceInfo()
	feeServiceServiceInfoForClient       = NewServiceInfoForClient()
	feeServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return feeServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return feeServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return feeServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "FeeService"
	handlerType := (*fee.FeeService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "fee",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func queryFeeItemHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceQueryFeeItemArgs)
	realResult := result.(*fee.FeeServiceQueryFeeItemResult)
	success, err := handler.(fee.FeeService).QueryFeeItem(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceQueryFeeItemArgs() interface{} {
	return fee.NewFeeServiceQueryFeeItemArgs()
}

func newFeeServiceQueryFeeItemResult() interface{} {
	return fee.NewFeeServiceQueryFeeItemResult()
}

func upsertFeeItemHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceUpsertFeeItemArgs)
	realResult := result.(*fee.FeeServiceUpsertFeeItemResult)
	success, err := handler.(fee.FeeService).UpsertFeeItem(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceUpsertFeeItemArgs() interface{} {
	return fee.NewFeeServiceUpsertFeeItemArgs()
}

func newFeeServiceUpsertFeeItemResult() interface{} {
	return fee.NewFeeServiceUpsertFeeItemResult()
}

func deleteFeeItemHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceDeleteFeeItemArgs)
	realResult := result.(*fee.FeeServiceDeleteFeeItemResult)
	success, err := handler.(fee.FeeService).DeleteFeeItem(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceDeleteFeeItemArgs() interface{} {
	return fee.NewFeeServiceDeleteFeeItemArgs()
}

func newFeeServiceDeleteFeeItemResult() interface{} {
	return fee.NewFeeServiceDeleteFeeItemResult()
}

func queryFeeRulesHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceQueryFeeRulesArgs)
	realResult := result.(*fee.FeeServiceQueryFeeRulesResult)
	success, err := handler.(fee.FeeService).QueryFeeRules(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceQueryFeeRulesArgs() interface{} {
	return fee.NewFeeServiceQueryFeeRulesArgs()
}

func newFeeServiceQueryFeeRulesResult() interface{} {
	return fee.NewFeeServiceQueryFeeRulesResult()
}

func upsertFeeRuleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceUpsertFeeRuleArgs)
	realResult := result.(*fee.FeeServiceUpsertFeeRuleResult)
	success, err := handler.(fee.FeeService).UpsertFeeRule(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceUpsertFeeRuleArgs() interface{} {
	return fee.NewFeeServiceUpsertFeeRuleArgs()
}

func newFeeServiceUpsertFeeRuleResult() interface{} {
	return fee.NewFeeServiceUpsertFeeRuleResult()
}

func deleteFeeRuleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceDeleteFeeRuleArgs)
	realResult := result.(*fee.FeeServiceDeleteFeeRuleResult)
	success, err := handler.(fee.FeeService).DeleteFeeRule(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceDeleteFeeRuleArgs() interface{} {
	return fee.NewFeeServiceDeleteFeeRuleArgs()
}

func newFeeServiceDeleteFeeRuleResult() interface{} {
	return fee.NewFeeServiceDeleteFeeRuleResult()
}

func queryFeeFormulasHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceQueryFeeFormulasArgs)
	realResult := result.(*fee.FeeServiceQueryFeeFormulasResult)
	success, err := handler.(fee.FeeService).QueryFeeFormulas(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceQueryFeeFormulasArgs() interface{} {
	return fee.NewFeeServiceQueryFeeFormulasArgs()
}

func newFeeServiceQueryFeeFormulasResult() interface{} {
	return fee.NewFeeServiceQueryFeeFormulasResult()
}

func upsertFeeFormulaHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceUpsertFeeFormulaArgs)
	realResult := result.(*fee.FeeServiceUpsertFeeFormulaResult)
	success, err := handler.(fee.FeeService).UpsertFeeFormula(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceUpsertFeeFormulaArgs() interface{} {
	return fee.NewFeeServiceUpsertFeeFormulaArgs()
}

func newFeeServiceUpsertFeeFormulaResult() interface{} {
	return fee.NewFeeServiceUpsertFeeFormulaResult()
}

func deleteFeeFormulaHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceDeleteFeeFormulaArgs)
	realResult := result.(*fee.FeeServiceDeleteFeeFormulaResult)
	success, err := handler.(fee.FeeService).DeleteFeeFormula(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceDeleteFeeFormulaArgs() interface{} {
	return fee.NewFeeServiceDeleteFeeFormulaArgs()
}

func newFeeServiceDeleteFeeFormulaResult() interface{} {
	return fee.NewFeeServiceDeleteFeeFormulaResult()
}

func chargeSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceChargeSettleArgs)
	realResult := result.(*fee.FeeServiceChargeSettleResult)
	success, err := handler.(fee.FeeService).ChargeSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceChargeSettleArgs() interface{} {
	return fee.NewFeeServiceChargeSettleArgs()
}

func newFeeServiceChargeSettleResult() interface{} {
	return fee.NewFeeServiceChargeSettleResult()
}

func chargeRefundHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceChargeRefundArgs)
	realResult := result.(*fee.FeeServiceChargeRefundResult)
	success, err := handler.(fee.FeeService).ChargeRefund(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceChargeRefundArgs() interface{} {
	return fee.NewFeeServiceChargeRefundArgs()
}

func newFeeServiceChargeRefundResult() interface{} {
	return fee.NewFeeServiceChargeRefundResult()
}

func commonChargeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceCommonChargeArgs)
	realResult := result.(*fee.FeeServiceCommonChargeResult)
	success, err := handler.(fee.FeeService).CommonCharge(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceCommonChargeArgs() interface{} {
	return fee.NewFeeServiceCommonChargeArgs()
}

func newFeeServiceCommonChargeResult() interface{} {
	return fee.NewFeeServiceCommonChargeResult()
}

func mGetChargeRecordHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceMGetChargeRecordArgs)
	realResult := result.(*fee.FeeServiceMGetChargeRecordResult)
	success, err := handler.(fee.FeeService).MGetChargeRecord(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceMGetChargeRecordArgs() interface{} {
	return fee.NewFeeServiceMGetChargeRecordArgs()
}

func newFeeServiceMGetChargeRecordResult() interface{} {
	return fee.NewFeeServiceMGetChargeRecordResult()
}

func checkBizFeeItemsHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*fee.FeeServiceCheckBizFeeItemsArgs)
	realResult := result.(*fee.FeeServiceCheckBizFeeItemsResult)
	success, err := handler.(fee.FeeService).CheckBizFeeItems(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFeeServiceCheckBizFeeItemsArgs() interface{} {
	return fee.NewFeeServiceCheckBizFeeItemsArgs()
}

func newFeeServiceCheckBizFeeItemsResult() interface{} {
	return fee.NewFeeServiceCheckBizFeeItemsResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) QueryFeeItem(ctx context.Context, req *fee.QueryFeeItemReq) (r *fee.QueryFeeItemResp, err error) {
	var _args fee.FeeServiceQueryFeeItemArgs
	_args.Req = req
	var _result fee.FeeServiceQueryFeeItemResult
	if err = p.c.Call(ctx, "QueryFeeItem", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpsertFeeItem(ctx context.Context, req *fee.UpsertFeeItemReq) (r *fee.UpsertFeeItemResp, err error) {
	var _args fee.FeeServiceUpsertFeeItemArgs
	_args.Req = req
	var _result fee.FeeServiceUpsertFeeItemResult
	if err = p.c.Call(ctx, "UpsertFeeItem", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DeleteFeeItem(ctx context.Context, req *fee.DeleteFeeItemReq) (r *fee.DeleteFeeItemResp, err error) {
	var _args fee.FeeServiceDeleteFeeItemArgs
	_args.Req = req
	var _result fee.FeeServiceDeleteFeeItemResult
	if err = p.c.Call(ctx, "DeleteFeeItem", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryFeeRules(ctx context.Context, req *fee.QueryFeeRulesReq) (r *fee.QueryFeeRulesResp, err error) {
	var _args fee.FeeServiceQueryFeeRulesArgs
	_args.Req = req
	var _result fee.FeeServiceQueryFeeRulesResult
	if err = p.c.Call(ctx, "QueryFeeRules", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpsertFeeRule(ctx context.Context, req *fee.UpsertFeeRuleReq) (r *fee.UpsertFeeRuleResp, err error) {
	var _args fee.FeeServiceUpsertFeeRuleArgs
	_args.Req = req
	var _result fee.FeeServiceUpsertFeeRuleResult
	if err = p.c.Call(ctx, "UpsertFeeRule", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DeleteFeeRule(ctx context.Context, req *fee.DeleteFeeRuleReq) (r *fee.DeleteFeeRuleResp, err error) {
	var _args fee.FeeServiceDeleteFeeRuleArgs
	_args.Req = req
	var _result fee.FeeServiceDeleteFeeRuleResult
	if err = p.c.Call(ctx, "DeleteFeeRule", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryFeeFormulas(ctx context.Context, req *fee.QueryFeeFormulasReq) (r *fee.QueryFeeFormulasResp, err error) {
	var _args fee.FeeServiceQueryFeeFormulasArgs
	_args.Req = req
	var _result fee.FeeServiceQueryFeeFormulasResult
	if err = p.c.Call(ctx, "QueryFeeFormulas", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpsertFeeFormula(ctx context.Context, req *fee.UpsertFeeFormulaReq) (r *fee.UpsertFeeFormulaResp, err error) {
	var _args fee.FeeServiceUpsertFeeFormulaArgs
	_args.Req = req
	var _result fee.FeeServiceUpsertFeeFormulaResult
	if err = p.c.Call(ctx, "UpsertFeeFormula", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DeleteFeeFormula(ctx context.Context, req *fee.DeleteFeeFormulaReq) (r *fee.DeleteFeeFormulaResp, err error) {
	var _args fee.FeeServiceDeleteFeeFormulaArgs
	_args.Req = req
	var _result fee.FeeServiceDeleteFeeFormulaResult
	if err = p.c.Call(ctx, "DeleteFeeFormula", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ChargeSettle(ctx context.Context, req *fee.ChargeSettleReq) (r *fee.ChargeSettleResp, err error) {
	var _args fee.FeeServiceChargeSettleArgs
	_args.Req = req
	var _result fee.FeeServiceChargeSettleResult
	if err = p.c.Call(ctx, "ChargeSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ChargeRefund(ctx context.Context, req *fee.ChargeRefundReq) (r *fee.ChargeRefundResp, err error) {
	var _args fee.FeeServiceChargeRefundArgs
	_args.Req = req
	var _result fee.FeeServiceChargeRefundResult
	if err = p.c.Call(ctx, "ChargeRefund", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CommonCharge(ctx context.Context, req *fee.CommonChargeReq) (r *fee.CommonChargeResp, err error) {
	var _args fee.FeeServiceCommonChargeArgs
	_args.Req = req
	var _result fee.FeeServiceCommonChargeResult
	if err = p.c.Call(ctx, "CommonCharge", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetChargeRecord(ctx context.Context, req *fee.MGetChargeRecordReq) (r *fee.MGetChargeRecordResp, err error) {
	var _args fee.FeeServiceMGetChargeRecordArgs
	_args.Req = req
	var _result fee.FeeServiceMGetChargeRecordResult
	if err = p.c.Call(ctx, "MGetChargeRecord", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CheckBizFeeItems(ctx context.Context, req *fee.CheckBizFeeItemsReq) (r *fee.CheckBizFeeItemsResp, err error) {
	var _args fee.FeeServiceCheckBizFeeItemsArgs
	_args.Req = req
	var _result fee.FeeServiceCheckBizFeeItemsResult
	if err = p.c.Call(ctx, "CheckBizFeeItems", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
