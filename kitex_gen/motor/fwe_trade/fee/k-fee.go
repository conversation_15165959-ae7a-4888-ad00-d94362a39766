// Code generated by Kitex v1.20.3. DO NOT EDIT.

package fee

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"

	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
)

var (
	_ = base.KitexUnusedProtection
	_ = payment.KitexUnusedProtection
	_ = fwe_trade_common.KitexUnusedProtection
	_ = tenant_base.KitexUnusedProtection
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *FeeItem) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeItem[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeItem) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ID = _field
	return offset, nil
}

func (p *FeeItem) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FeeID = _field
	return offset, nil
}

func (p *FeeItem) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FeeName = _field
	return offset, nil
}

func (p *FeeItem) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FeeDesc = _field
	return offset, nil
}

func (p *FeeItem) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreatorName = _field
	return offset, nil
}

func (p *FeeItem) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateTime = _field
	return offset, nil
}

func (p *FeeItem) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UpdateTime = _field
	return offset, nil
}

func (p *FeeItem) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IsDeleted = _field
	return offset, nil
}

func (p *FeeItem) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeItem) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeItem) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field10Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeItem) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ID)
	return offset
}

func (p *FeeItem) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FeeID)
	return offset
}

func (p *FeeItem) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FeeName)
	return offset
}

func (p *FeeItem) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FeeDesc)
	return offset
}

func (p *FeeItem) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CreatorName)
	return offset
}

func (p *FeeItem) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CreateTime)
	return offset
}

func (p *FeeItem) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 7)
	offset += thrift.Binary.WriteI64(buf[offset:], p.UpdateTime)
	return offset
}

func (p *FeeItem) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 10)
	offset += thrift.Binary.WriteBool(buf[offset:], p.IsDeleted)
	return offset
}

func (p *FeeItem) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *FeeItem) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FeeID)
	return l
}

func (p *FeeItem) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FeeName)
	return l
}

func (p *FeeItem) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FeeDesc)
	return l
}

func (p *FeeItem) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CreatorName)
	return l
}

func (p *FeeItem) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *FeeItem) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *FeeItem) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *FeeItem) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeItem)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.ID = src.ID

	if src.FeeID != "" {
		p.FeeID = kutils.StringDeepCopy(src.FeeID)
	}

	if src.FeeName != "" {
		p.FeeName = kutils.StringDeepCopy(src.FeeName)
	}

	if src.FeeDesc != "" {
		p.FeeDesc = kutils.StringDeepCopy(src.FeeDesc)
	}

	if src.CreatorName != "" {
		p.CreatorName = kutils.StringDeepCopy(src.CreatorName)
	}

	p.CreateTime = src.CreateTime

	p.UpdateTime = src.UpdateTime

	p.IsDeleted = src.IsDeleted

	return nil
}

func (p *QueryFeeItemReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 251:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField251(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeItemReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *QueryFeeItemReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.FeeIDList = _field
	return offset, nil
}

func (p *QueryFeeItemReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContainsDeleted = _field
	return offset, nil
}

func (p *QueryFeeItemReq) FastReadField250(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Offset = _field
	return offset, nil
}

func (p *QueryFeeItemReq) FastReadField251(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Count = _field
	return offset, nil
}

func (p *QueryFeeItemReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *QueryFeeItemReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *QueryFeeItemReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField251(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *QueryFeeItemReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field10Length()
		l += p.field250Length()
		l += p.field251Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *QueryFeeItemReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFeeIDList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.FeeIDList {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *QueryFeeItemReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 10)
	offset += thrift.Binary.WriteBool(buf[offset:], p.ContainsDeleted)
	return offset
}

func (p *QueryFeeItemReq) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 250)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Offset)
	return offset
}

func (p *QueryFeeItemReq) fastWriteField251(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 251)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Count)
	return offset
}

func (p *QueryFeeItemReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.Base.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *QueryFeeItemReq) field1Length() int {
	l := 0
	if p.IsSetFeeIDList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.FeeIDList {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *QueryFeeItemReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *QueryFeeItemReq) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *QueryFeeItemReq) field251Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *QueryFeeItemReq) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Base.BLength()
	return l
}

func (p *QueryFeeItemReq) DeepCopy(s interface{}) error {
	src, ok := s.(*QueryFeeItemReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.FeeIDList != nil {
		p.FeeIDList = make([]string, 0, len(src.FeeIDList))
		for _, elem := range src.FeeIDList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.FeeIDList = append(p.FeeIDList, _elem)
		}
	}

	p.ContainsDeleted = src.ContainsDeleted

	p.Offset = src.Offset

	p.Count = src.Count

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *QueryFeeItemResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 251:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField251(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeItemResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *QueryFeeItemResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*FeeItem, 0, size)
	values := make([]FeeItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.DataList = _field
	return offset, nil
}

func (p *QueryFeeItemResp) FastReadField250(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalCount = _field
	return offset, nil
}

func (p *QueryFeeItemResp) FastReadField251(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.HasMore = _field
	return offset, nil
}

func (p *QueryFeeItemResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *QueryFeeItemResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *QueryFeeItemResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField251(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *QueryFeeItemResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field250Length()
		l += p.field251Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *QueryFeeItemResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.DataList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *QueryFeeItemResp) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 250)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TotalCount)
	return offset
}

func (p *QueryFeeItemResp) fastWriteField251(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 251)
	offset += thrift.Binary.WriteBool(buf[offset:], p.HasMore)
	return offset
}

func (p *QueryFeeItemResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *QueryFeeItemResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.DataList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *QueryFeeItemResp) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *QueryFeeItemResp) field251Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *QueryFeeItemResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *QueryFeeItemResp) DeepCopy(s interface{}) error {
	src, ok := s.(*QueryFeeItemResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.DataList != nil {
		p.DataList = make([]*FeeItem, 0, len(src.DataList))
		for _, elem := range src.DataList {
			var _elem *FeeItem
			if elem != nil {
				_elem = &FeeItem{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.DataList = append(p.DataList, _elem)
		}
	}

	p.TotalCount = src.TotalCount

	p.HasMore = src.HasMore

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *UpsertFeeItemReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCreatorName bool = false
	var issetOperatorName bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetCreatorName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOperatorName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetCreatorName {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetOperatorName {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeItemReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_UpsertFeeItemReq[fieldId]))
}

func (p *UpsertFeeItemReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ID = _field
	return offset, nil
}

func (p *UpsertFeeItemReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FeeID = _field
	return offset, nil
}

func (p *UpsertFeeItemReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FeeName = _field
	return offset, nil
}

func (p *UpsertFeeItemReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FeeDesc = _field
	return offset, nil
}

func (p *UpsertFeeItemReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreatorName = _field
	return offset, nil
}

func (p *UpsertFeeItemReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OperatorName = _field
	return offset, nil
}

func (p *UpsertFeeItemReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *UpsertFeeItemReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpsertFeeItemReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpsertFeeItemReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpsertFeeItemReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ID)
	return offset
}

func (p *UpsertFeeItemReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FeeID)
	return offset
}

func (p *UpsertFeeItemReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFeeName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.FeeName)
	}
	return offset
}

func (p *UpsertFeeItemReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFeeDesc() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.FeeDesc)
	}
	return offset
}

func (p *UpsertFeeItemReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CreatorName)
	return offset
}

func (p *UpsertFeeItemReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OperatorName)
	return offset
}

func (p *UpsertFeeItemReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.Base.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpsertFeeItemReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *UpsertFeeItemReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FeeID)
	return l
}

func (p *UpsertFeeItemReq) field3Length() int {
	l := 0
	if p.IsSetFeeName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.FeeName)
	}
	return l
}

func (p *UpsertFeeItemReq) field4Length() int {
	l := 0
	if p.IsSetFeeDesc() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.FeeDesc)
	}
	return l
}

func (p *UpsertFeeItemReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CreatorName)
	return l
}

func (p *UpsertFeeItemReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OperatorName)
	return l
}

func (p *UpsertFeeItemReq) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Base.BLength()
	return l
}

func (p *UpsertFeeItemReq) DeepCopy(s interface{}) error {
	src, ok := s.(*UpsertFeeItemReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.ID = src.ID

	if src.FeeID != "" {
		p.FeeID = kutils.StringDeepCopy(src.FeeID)
	}

	if src.FeeName != nil {
		var tmp string
		if *src.FeeName != "" {
			tmp = kutils.StringDeepCopy(*src.FeeName)
		}
		p.FeeName = &tmp
	}

	if src.FeeDesc != nil {
		var tmp string
		if *src.FeeDesc != "" {
			tmp = kutils.StringDeepCopy(*src.FeeDesc)
		}
		p.FeeDesc = &tmp
	}

	if src.CreatorName != "" {
		p.CreatorName = kutils.StringDeepCopy(src.CreatorName)
	}

	if src.OperatorName != "" {
		p.OperatorName = kutils.StringDeepCopy(src.OperatorName)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *UpsertFeeItemResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeItemResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *UpsertFeeItemResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *UpsertFeeItemResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpsertFeeItemResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpsertFeeItemResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpsertFeeItemResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpsertFeeItemResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *UpsertFeeItemResp) DeepCopy(s interface{}) error {
	src, ok := s.(*UpsertFeeItemResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *DeleteFeeItemReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOperatorName bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOperatorName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOperatorName {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeItemReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DeleteFeeItemReq[fieldId]))
}

func (p *DeleteFeeItemReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ID = _field
	return offset, nil
}

func (p *DeleteFeeItemReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OperatorName = _field
	return offset, nil
}

func (p *DeleteFeeItemReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *DeleteFeeItemReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteFeeItemReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteFeeItemReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field11Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteFeeItemReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ID)
	return offset
}

func (p *DeleteFeeItemReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OperatorName)
	return offset
}

func (p *DeleteFeeItemReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.Base.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *DeleteFeeItemReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *DeleteFeeItemReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OperatorName)
	return l
}

func (p *DeleteFeeItemReq) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Base.BLength()
	return l
}

func (p *DeleteFeeItemReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteFeeItemReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.ID = src.ID

	if src.OperatorName != "" {
		p.OperatorName = kutils.StringDeepCopy(src.OperatorName)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *DeleteFeeItemResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeItemResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DeleteFeeItemResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *DeleteFeeItemResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteFeeItemResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteFeeItemResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteFeeItemResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *DeleteFeeItemResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *DeleteFeeItemResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteFeeItemResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *FeeFormula) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 102:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField102(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 103:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField103(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeFormula[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeFormula) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FormulaID = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FormulaName = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.RoleType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.RoleType(v)
	}
	p.FromRoleType = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.RoleType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.RoleType(v)
	}
	p.ToRoleType = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Expression = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleID = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField7(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.FeeItemNameList = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.EaConfig = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field FormulaType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = FormulaType(v)
	}
	p.FormulaType = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AutoWithdraw = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField101(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreatorName = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField102(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateTime = _field
	return offset, nil
}

func (p *FeeFormula) FastReadField103(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UpdateTime = _field
	return offset, nil
}

func (p *FeeFormula) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeFormula) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField102(buf[offset:], w)
		offset += p.fastWriteField103(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeFormula) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field101Length()
		l += p.field102Length()
		l += p.field103Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeFormula) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.FormulaID)
	return offset
}

func (p *FeeFormula) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FormulaName)
	return offset
}

func (p *FeeFormula) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.FromRoleType))
	return offset
}

func (p *FeeFormula) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.ToRoleType))
	return offset
}

func (p *FeeFormula) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Expression)
	return offset
}

func (p *FeeFormula) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RuleID)
	return offset
}

func (p *FeeFormula) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 7)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FeeItemNameList {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *FeeFormula) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.EaConfig)
	return offset
}

func (p *FeeFormula) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 9)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.FormulaType))
	return offset
}

func (p *FeeFormula) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 10)
	offset += thrift.Binary.WriteBool(buf[offset:], p.AutoWithdraw)
	return offset
}

func (p *FeeFormula) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 101)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CreatorName)
	return offset
}

func (p *FeeFormula) fastWriteField102(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 102)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CreateTime)
	return offset
}

func (p *FeeFormula) fastWriteField103(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 103)
	offset += thrift.Binary.WriteI64(buf[offset:], p.UpdateTime)
	return offset
}

func (p *FeeFormula) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *FeeFormula) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FormulaName)
	return l
}

func (p *FeeFormula) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *FeeFormula) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *FeeFormula) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Expression)
	return l
}

func (p *FeeFormula) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *FeeFormula) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FeeItemNameList {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *FeeFormula) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.EaConfig)
	return l
}

func (p *FeeFormula) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *FeeFormula) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *FeeFormula) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CreatorName)
	return l
}

func (p *FeeFormula) field102Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *FeeFormula) field103Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *FeeFormula) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeFormula)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.FormulaID = src.FormulaID

	if src.FormulaName != "" {
		p.FormulaName = kutils.StringDeepCopy(src.FormulaName)
	}

	p.FromRoleType = src.FromRoleType

	p.ToRoleType = src.ToRoleType

	if src.Expression != "" {
		p.Expression = kutils.StringDeepCopy(src.Expression)
	}

	p.RuleID = src.RuleID

	if src.FeeItemNameList != nil {
		p.FeeItemNameList = make([]string, 0, len(src.FeeItemNameList))
		for _, elem := range src.FeeItemNameList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.FeeItemNameList = append(p.FeeItemNameList, _elem)
		}
	}

	if src.EaConfig != "" {
		p.EaConfig = kutils.StringDeepCopy(src.EaConfig)
	}

	p.FormulaType = src.FormulaType

	p.AutoWithdraw = src.AutoWithdraw

	if src.CreatorName != "" {
		p.CreatorName = kutils.StringDeepCopy(src.CreatorName)
	}

	p.CreateTime = src.CreateTime

	p.UpdateTime = src.UpdateTime

	return nil
}

func (p *RuleAttribute) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RuleAttribute[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RuleAttribute) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AfterSettleRefund = _field
	return offset, nil
}

func (p *RuleAttribute) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RuleAttribute) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RuleAttribute) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RuleAttribute) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 1)
	offset += thrift.Binary.WriteBool(buf[offset:], p.AfterSettleRefund)
	return offset
}

func (p *RuleAttribute) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *RuleAttribute) DeepCopy(s interface{}) error {
	src, ok := s.(*RuleAttribute)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.AfterSettleRefund = src.AfterSettleRefund

	return nil
}

func (p *FeeRule) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 102:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField102(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 103:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField103(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 104:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField104(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 105:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField105(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeRule[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeRule) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleID = _field
	return offset, nil
}

func (p *FeeRule) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleName = _field
	return offset, nil
}

func (p *FeeRule) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleDesc = _field
	return offset, nil
}

func (p *FeeRule) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field FeeRuleType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = FeeRuleType(v)
	}
	p.RuleType = _field
	return offset, nil
}

func (p *FeeRule) FastReadField5(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*FeeFormula, 0, size)
	values := make([]FeeFormula, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.FormulaList = _field
	return offset, nil
}

func (p *FeeRule) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IncludeConsumerLoan = _field
	return offset, nil
}

func (p *FeeRule) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ConditionExpression = _field
	return offset, nil
}

func (p *FeeRule) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IsFullSettle = _field
	return offset, nil
}

func (p *FeeRule) FastReadField10(buf []byte) (int, error) {
	offset := 0
	_field := NewRuleAttribute()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.RuleAttribute = _field
	return offset, nil
}

func (p *FeeRule) FastReadField101(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BizScene = _field
	return offset, nil
}

func (p *FeeRule) FastReadField102(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreatorName = _field
	return offset, nil
}

func (p *FeeRule) FastReadField103(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateTime = _field
	return offset, nil
}

func (p *FeeRule) FastReadField104(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UpdateTime = _field
	return offset, nil
}

func (p *FeeRule) FastReadField105(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Status = _field
	return offset, nil
}

func (p *FeeRule) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeRule) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField103(buf[offset:], w)
		offset += p.fastWriteField104(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField102(buf[offset:], w)
		offset += p.fastWriteField105(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeRule) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field10Length()
		l += p.field101Length()
		l += p.field102Length()
		l += p.field103Length()
		l += p.field104Length()
		l += p.field105Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeRule) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RuleID)
	return offset
}

func (p *FeeRule) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RuleName)
	return offset
}

func (p *FeeRule) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RuleDesc)
	return offset
}

func (p *FeeRule) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.RuleType))
	return offset
}

func (p *FeeRule) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 5)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FormulaList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *FeeRule) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 6)
	offset += thrift.Binary.WriteBool(buf[offset:], p.IncludeConsumerLoan)
	return offset
}

func (p *FeeRule) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ConditionExpression)
	return offset
}

func (p *FeeRule) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 8)
	offset += thrift.Binary.WriteBool(buf[offset:], p.IsFullSettle)
	return offset
}

func (p *FeeRule) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 10)
	offset += p.RuleAttribute.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeRule) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 101)
	offset += thrift.Binary.WriteI32(buf[offset:], p.BizScene)
	return offset
}

func (p *FeeRule) fastWriteField102(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 102)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CreatorName)
	return offset
}

func (p *FeeRule) fastWriteField103(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 103)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CreateTime)
	return offset
}

func (p *FeeRule) fastWriteField104(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 104)
	offset += thrift.Binary.WriteI64(buf[offset:], p.UpdateTime)
	return offset
}

func (p *FeeRule) fastWriteField105(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 105)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Status)
	return offset
}

func (p *FeeRule) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *FeeRule) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RuleName)
	return l
}

func (p *FeeRule) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RuleDesc)
	return l
}

func (p *FeeRule) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *FeeRule) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FormulaList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *FeeRule) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *FeeRule) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ConditionExpression)
	return l
}

func (p *FeeRule) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *FeeRule) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.RuleAttribute.BLength()
	return l
}

func (p *FeeRule) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *FeeRule) field102Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CreatorName)
	return l
}

func (p *FeeRule) field103Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *FeeRule) field104Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *FeeRule) field105Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Status)
	return l
}

func (p *FeeRule) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeRule)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.RuleID = src.RuleID

	if src.RuleName != "" {
		p.RuleName = kutils.StringDeepCopy(src.RuleName)
	}

	if src.RuleDesc != "" {
		p.RuleDesc = kutils.StringDeepCopy(src.RuleDesc)
	}

	p.RuleType = src.RuleType

	if src.FormulaList != nil {
		p.FormulaList = make([]*FeeFormula, 0, len(src.FormulaList))
		for _, elem := range src.FormulaList {
			var _elem *FeeFormula
			if elem != nil {
				_elem = &FeeFormula{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.FormulaList = append(p.FormulaList, _elem)
		}
	}

	p.IncludeConsumerLoan = src.IncludeConsumerLoan

	if src.ConditionExpression != "" {
		p.ConditionExpression = kutils.StringDeepCopy(src.ConditionExpression)
	}

	p.IsFullSettle = src.IsFullSettle

	var _ruleAttribute *RuleAttribute
	if src.RuleAttribute != nil {
		_ruleAttribute = &RuleAttribute{}
		if err := _ruleAttribute.DeepCopy(src.RuleAttribute); err != nil {
			return err
		}
	}
	p.RuleAttribute = _ruleAttribute

	p.BizScene = src.BizScene

	if src.CreatorName != "" {
		p.CreatorName = kutils.StringDeepCopy(src.CreatorName)
	}

	p.CreateTime = src.CreateTime

	p.UpdateTime = src.UpdateTime

	if src.Status != "" {
		p.Status = kutils.StringDeepCopy(src.Status)
	}

	return nil
}

func (p *QueryFeeRulesReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBizScene bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBizScene = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetBizScene {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeRulesReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_QueryFeeRulesReq[fieldId]))
}

func (p *QueryFeeRulesReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BizScene = _field
	return offset, nil
}

func (p *QueryFeeRulesReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *QueryFeeRulesReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *QueryFeeRulesReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *QueryFeeRulesReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *QueryFeeRulesReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.BizScene)
	return offset
}

func (p *QueryFeeRulesReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *QueryFeeRulesReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *QueryFeeRulesReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *QueryFeeRulesReq) DeepCopy(s interface{}) error {
	src, ok := s.(*QueryFeeRulesReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.BizScene = src.BizScene

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *QueryFeeRulesResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeRulesResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *QueryFeeRulesResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*FeeRule, 0, size)
	values := make([]FeeRule, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RuleList = _field
	return offset, nil
}

func (p *QueryFeeRulesResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *QueryFeeRulesResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *QueryFeeRulesResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *QueryFeeRulesResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *QueryFeeRulesResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RuleList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *QueryFeeRulesResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *QueryFeeRulesResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RuleList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *QueryFeeRulesResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *QueryFeeRulesResp) DeepCopy(s interface{}) error {
	src, ok := s.(*QueryFeeRulesResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RuleList != nil {
		p.RuleList = make([]*FeeRule, 0, len(src.RuleList))
		for _, elem := range src.RuleList {
			var _elem *FeeRule
			if elem != nil {
				_elem = &FeeRule{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RuleList = append(p.RuleList, _elem)
		}
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *UpsertFeeRuleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRule bool = false
	var issetOperator bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRule = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRule {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeRuleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_UpsertFeeRuleReq[fieldId]))
}

func (p *UpsertFeeRuleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewFeeRule()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Rule = _field
	return offset, nil
}

func (p *UpsertFeeRuleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Operator = _field
	return offset, nil
}

func (p *UpsertFeeRuleReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *UpsertFeeRuleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpsertFeeRuleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpsertFeeRuleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpsertFeeRuleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Rule.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpsertFeeRuleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Operator)
	return offset
}

func (p *UpsertFeeRuleReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *UpsertFeeRuleReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Rule.BLength()
	return l
}

func (p *UpsertFeeRuleReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Operator)
	return l
}

func (p *UpsertFeeRuleReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *UpsertFeeRuleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*UpsertFeeRuleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _rule *FeeRule
	if src.Rule != nil {
		_rule = &FeeRule{}
		if err := _rule.DeepCopy(src.Rule); err != nil {
			return err
		}
	}
	p.Rule = _rule

	if src.Operator != "" {
		p.Operator = kutils.StringDeepCopy(src.Operator)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *UpsertFeeRuleResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeRuleResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *UpsertFeeRuleResp) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewFeeRule()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Rule = _field
	return offset, nil
}

func (p *UpsertFeeRuleResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *UpsertFeeRuleResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpsertFeeRuleResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpsertFeeRuleResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpsertFeeRuleResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Rule.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpsertFeeRuleResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpsertFeeRuleResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Rule.BLength()
	return l
}

func (p *UpsertFeeRuleResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *UpsertFeeRuleResp) DeepCopy(s interface{}) error {
	src, ok := s.(*UpsertFeeRuleResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _rule *FeeRule
	if src.Rule != nil {
		_rule = &FeeRule{}
		if err := _rule.DeepCopy(src.Rule); err != nil {
			return err
		}
	}
	p.Rule = _rule

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *DeleteFeeRuleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRuleID bool = false
	var issetOperator bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRuleID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRuleID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeRuleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DeleteFeeRuleReq[fieldId]))
}

func (p *DeleteFeeRuleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleID = _field
	return offset, nil
}

func (p *DeleteFeeRuleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Operator = _field
	return offset, nil
}

func (p *DeleteFeeRuleReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *DeleteFeeRuleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteFeeRuleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteFeeRuleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteFeeRuleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RuleID)
	return offset
}

func (p *DeleteFeeRuleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Operator)
	return offset
}

func (p *DeleteFeeRuleReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DeleteFeeRuleReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *DeleteFeeRuleReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Operator)
	return l
}

func (p *DeleteFeeRuleReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *DeleteFeeRuleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteFeeRuleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.RuleID = src.RuleID

	if src.Operator != "" {
		p.Operator = kutils.StringDeepCopy(src.Operator)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *DeleteFeeRuleResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeRuleResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DeleteFeeRuleResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *DeleteFeeRuleResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteFeeRuleResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteFeeRuleResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteFeeRuleResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *DeleteFeeRuleResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *DeleteFeeRuleResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteFeeRuleResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *QueryFeeFormulasReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeFormulasReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *QueryFeeFormulasReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RuleID = _field
	return offset, nil
}

func (p *QueryFeeFormulasReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem int64
		if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.FormulaIDList = _field
	return offset, nil
}

func (p *QueryFeeFormulasReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *QueryFeeFormulasReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *QueryFeeFormulasReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *QueryFeeFormulasReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *QueryFeeFormulasReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRuleID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.RuleID)
	}
	return offset
}

func (p *QueryFeeFormulasReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFormulaIDList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.FormulaIDList {
			length++
			offset += thrift.Binary.WriteI64(buf[offset:], v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I64, length)
	}
	return offset
}

func (p *QueryFeeFormulasReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *QueryFeeFormulasReq) field1Length() int {
	l := 0
	if p.IsSetRuleID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *QueryFeeFormulasReq) field2Length() int {
	l := 0
	if p.IsSetFormulaIDList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		l +=
			thrift.Binary.I64Length() * len(p.FormulaIDList)
	}
	return l
}

func (p *QueryFeeFormulasReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *QueryFeeFormulasReq) DeepCopy(s interface{}) error {
	src, ok := s.(*QueryFeeFormulasReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RuleID != nil {
		tmp := *src.RuleID
		p.RuleID = &tmp
	}

	if src.FormulaIDList != nil {
		p.FormulaIDList = make([]int64, 0, len(src.FormulaIDList))
		for _, elem := range src.FormulaIDList {
			var _elem int64
			_elem = elem
			p.FormulaIDList = append(p.FormulaIDList, _elem)
		}
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *QueryFeeFormulasResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryFeeFormulasResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *QueryFeeFormulasResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*FeeFormula, 0, size)
	values := make([]FeeFormula, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.FormulaList = _field
	return offset, nil
}

func (p *QueryFeeFormulasResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *QueryFeeFormulasResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *QueryFeeFormulasResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *QueryFeeFormulasResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *QueryFeeFormulasResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FormulaList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *QueryFeeFormulasResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *QueryFeeFormulasResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FormulaList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *QueryFeeFormulasResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *QueryFeeFormulasResp) DeepCopy(s interface{}) error {
	src, ok := s.(*QueryFeeFormulasResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.FormulaList != nil {
		p.FormulaList = make([]*FeeFormula, 0, len(src.FormulaList))
		for _, elem := range src.FormulaList {
			var _elem *FeeFormula
			if elem != nil {
				_elem = &FeeFormula{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.FormulaList = append(p.FormulaList, _elem)
		}
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *UpsertFeeFormulaReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFormula bool = false
	var issetOperator bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetFormula = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetFormula {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeFormulaReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_UpsertFeeFormulaReq[fieldId]))
}

func (p *UpsertFeeFormulaReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewFeeFormula()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Formula = _field
	return offset, nil
}

func (p *UpsertFeeFormulaReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Operator = _field
	return offset, nil
}

func (p *UpsertFeeFormulaReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *UpsertFeeFormulaReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpsertFeeFormulaReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpsertFeeFormulaReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpsertFeeFormulaReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Formula.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpsertFeeFormulaReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Operator)
	return offset
}

func (p *UpsertFeeFormulaReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *UpsertFeeFormulaReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Formula.BLength()
	return l
}

func (p *UpsertFeeFormulaReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Operator)
	return l
}

func (p *UpsertFeeFormulaReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *UpsertFeeFormulaReq) DeepCopy(s interface{}) error {
	src, ok := s.(*UpsertFeeFormulaReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _formula *FeeFormula
	if src.Formula != nil {
		_formula = &FeeFormula{}
		if err := _formula.DeepCopy(src.Formula); err != nil {
			return err
		}
	}
	p.Formula = _formula

	if src.Operator != "" {
		p.Operator = kutils.StringDeepCopy(src.Operator)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *UpsertFeeFormulaResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpsertFeeFormulaResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *UpsertFeeFormulaResp) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewFeeFormula()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Formula = _field
	return offset, nil
}

func (p *UpsertFeeFormulaResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *UpsertFeeFormulaResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpsertFeeFormulaResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpsertFeeFormulaResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpsertFeeFormulaResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Formula.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpsertFeeFormulaResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpsertFeeFormulaResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Formula.BLength()
	return l
}

func (p *UpsertFeeFormulaResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *UpsertFeeFormulaResp) DeepCopy(s interface{}) error {
	src, ok := s.(*UpsertFeeFormulaResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _formula *FeeFormula
	if src.Formula != nil {
		_formula = &FeeFormula{}
		if err := _formula.DeepCopy(src.Formula); err != nil {
			return err
		}
	}
	p.Formula = _formula

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *DeleteFeeFormulaReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFormulaID bool = false
	var issetOperator bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetFormulaID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetFormulaID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeFormulaReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_DeleteFeeFormulaReq[fieldId]))
}

func (p *DeleteFeeFormulaReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FormulaID = _field
	return offset, nil
}

func (p *DeleteFeeFormulaReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Operator = _field
	return offset, nil
}

func (p *DeleteFeeFormulaReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *DeleteFeeFormulaReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteFeeFormulaReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteFeeFormulaReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteFeeFormulaReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.FormulaID)
	return offset
}

func (p *DeleteFeeFormulaReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Operator)
	return offset
}

func (p *DeleteFeeFormulaReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DeleteFeeFormulaReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *DeleteFeeFormulaReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Operator)
	return l
}

func (p *DeleteFeeFormulaReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *DeleteFeeFormulaReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteFeeFormulaReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.FormulaID = src.FormulaID

	if src.Operator != "" {
		p.Operator = kutils.StringDeepCopy(src.Operator)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *DeleteFeeFormulaResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteFeeFormulaResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DeleteFeeFormulaResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *DeleteFeeFormulaResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DeleteFeeFormulaResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DeleteFeeFormulaResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DeleteFeeFormulaResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *DeleteFeeFormulaResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *DeleteFeeFormulaResp) DeepCopy(s interface{}) error {
	src, ok := s.(*DeleteFeeFormulaResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *ChargeResult_) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeResult_[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ChargeResult_) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.RoleType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.RoleType(v)
	}
	p.RoleType = _field
	return offset, nil
}

func (p *ChargeResult_) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Amount = _field
	return offset, nil
}

func (p *ChargeResult_) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FormulaID = _field
	return offset, nil
}

func (p *ChargeResult_) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field payment.CurrencyType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = payment.CurrencyType(v)
	}
	p.CurrencyType = _field
	return offset, nil
}

func (p *ChargeResult_) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NeedAutoWithdraw = _field
	return offset, nil
}

func (p *ChargeResult_) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ChargeResult_) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ChargeResult_) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ChargeResult_) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.RoleType))
	return offset
}

func (p *ChargeResult_) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Amount)
	return offset
}

func (p *ChargeResult_) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.FormulaID)
	return offset
}

func (p *ChargeResult_) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.CurrencyType))
	return offset
}

func (p *ChargeResult_) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 5)
	offset += thrift.Binary.WriteBool(buf[offset:], p.NeedAutoWithdraw)
	return offset
}

func (p *ChargeResult_) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ChargeResult_) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ChargeResult_) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ChargeResult_) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ChargeResult_) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *ChargeResult_) DeepCopy(s interface{}) error {
	src, ok := s.(*ChargeResult_)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.RoleType = src.RoleType

	p.Amount = src.Amount

	p.FormulaID = src.FormulaID

	p.CurrencyType = src.CurrencyType

	p.NeedAutoWithdraw = src.NeedAutoWithdraw

	return nil
}

func (p *ChargeSettleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetRuleID bool = false
	var issetOperator bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRuleID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRuleID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 100
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeSettleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ChargeSettleReq[fieldId]))
}

func (p *ChargeSettleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *ChargeSettleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleID = _field
	return offset, nil
}

func (p *ChargeSettleReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Params = _field
	return offset, nil
}

func (p *ChargeSettleReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FulfillID = _field
	return offset, nil
}

func (p *ChargeSettleReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *fwe_trade_common.OrderType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := fwe_trade_common.OrderType(v)
		_field = &tmp
	}
	p.OrderType = _field
	return offset, nil
}

func (p *ChargeSettleReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Operator = _field
	return offset, nil
}

func (p *ChargeSettleReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *ChargeSettleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ChargeSettleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ChargeSettleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field100Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ChargeSettleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *ChargeSettleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RuleID)
	return offset
}

func (p *ChargeSettleReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetParams() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Params)
	}
	return offset
}

func (p *ChargeSettleReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFulfillID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.FulfillID)
	}
	return offset
}

func (p *ChargeSettleReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.OrderType))
	}
	return offset
}

func (p *ChargeSettleReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Operator)
	return offset
}

func (p *ChargeSettleReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ChargeSettleReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *ChargeSettleReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ChargeSettleReq) field3Length() int {
	l := 0
	if p.IsSetParams() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Params)
	}
	return l
}

func (p *ChargeSettleReq) field4Length() int {
	l := 0
	if p.IsSetFulfillID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.FulfillID)
	}
	return l
}

func (p *ChargeSettleReq) field5Length() int {
	l := 0
	if p.IsSetOrderType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *ChargeSettleReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Operator)
	return l
}

func (p *ChargeSettleReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *ChargeSettleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ChargeSettleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	p.RuleID = src.RuleID

	if src.Params != nil {
		var tmp string
		if *src.Params != "" {
			tmp = kutils.StringDeepCopy(*src.Params)
		}
		p.Params = &tmp
	}

	if src.FulfillID != nil {
		var tmp string
		if *src.FulfillID != "" {
			tmp = kutils.StringDeepCopy(*src.FulfillID)
		}
		p.FulfillID = &tmp
	}

	if src.OrderType != nil {
		tmp := *src.OrderType
		p.OrderType = &tmp
	}

	if src.Operator != "" {
		p.Operator = kutils.StringDeepCopy(src.Operator)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *ChargeSettleResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeSettleResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ChargeSettleResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*ChargeResult_, 0, size)
	values := make([]ChargeResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ChargeResultList = _field
	return offset, nil
}

func (p *ChargeSettleResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.FinanceIDList = _field
	return offset, nil
}

func (p *ChargeSettleResp) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RecordID = _field
	return offset, nil
}

func (p *ChargeSettleResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *ChargeSettleResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ChargeSettleResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ChargeSettleResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field100Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ChargeSettleResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ChargeResultList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *ChargeSettleResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FinanceIDList {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *ChargeSettleResp) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RecordID)
	return offset
}

func (p *ChargeSettleResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ChargeSettleResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ChargeResultList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *ChargeSettleResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FinanceIDList {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *ChargeSettleResp) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RecordID)
	return l
}

func (p *ChargeSettleResp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *ChargeSettleResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ChargeSettleResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ChargeResultList != nil {
		p.ChargeResultList = make([]*ChargeResult_, 0, len(src.ChargeResultList))
		for _, elem := range src.ChargeResultList {
			var _elem *ChargeResult_
			if elem != nil {
				_elem = &ChargeResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ChargeResultList = append(p.ChargeResultList, _elem)
		}
	}

	if src.FinanceIDList != nil {
		p.FinanceIDList = make([]string, 0, len(src.FinanceIDList))
		for _, elem := range src.FinanceIDList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.FinanceIDList = append(p.FinanceIDList, _elem)
		}
	}

	if src.RecordID != "" {
		p.RecordID = kutils.StringDeepCopy(src.RecordID)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *SingleRefund) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SingleRefund[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SingleRefund) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceOrderID = _field
	return offset, nil
}

func (p *SingleRefund) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Amount = _field
	return offset, nil
}

func (p *SingleRefund) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.YztOfflineAmount = _field
	return offset, nil
}

func (p *SingleRefund) FastReadField10(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.JstRefundSingle, 0, size)
	values := make([]fwe_trade_common.JstRefundSingle, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.JstRefundList = _field
	return offset, nil
}

func (p *SingleRefund) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SingleRefund) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SingleRefund) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field10Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SingleRefund) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FinanceOrderID)
	return offset
}

func (p *SingleRefund) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Amount)
	return offset
}

func (p *SingleRefund) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.YztOfflineAmount)
	return offset
}

func (p *SingleRefund) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 10)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.JstRefundList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *SingleRefund) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FinanceOrderID)
	return l
}

func (p *SingleRefund) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SingleRefund) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SingleRefund) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.JstRefundList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *SingleRefund) DeepCopy(s interface{}) error {
	src, ok := s.(*SingleRefund)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.FinanceOrderID != "" {
		p.FinanceOrderID = kutils.StringDeepCopy(src.FinanceOrderID)
	}

	p.Amount = src.Amount

	p.YztOfflineAmount = src.YztOfflineAmount

	if src.JstRefundList != nil {
		p.JstRefundList = make([]*fwe_trade_common.JstRefundSingle, 0, len(src.JstRefundList))
		for _, elem := range src.JstRefundList {
			var _elem *fwe_trade_common.JstRefundSingle
			if elem != nil {
				_elem = &fwe_trade_common.JstRefundSingle{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.JstRefundList = append(p.JstRefundList, _elem)
		}
	}

	return nil
}

func (p *ChargeRefundReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetRuleID bool = false
	var issetOperator bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRuleID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRuleID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 100
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeRefundReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_ChargeRefundReq[fieldId]))
}

func (p *ChargeRefundReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *ChargeRefundReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleID = _field
	return offset, nil
}

func (p *ChargeRefundReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*SingleRefund, 0, size)
	values := make([]SingleRefund, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RefundList = _field
	return offset, nil
}

func (p *ChargeRefundReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FulfillID = _field
	return offset, nil
}

func (p *ChargeRefundReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *fwe_trade_common.OrderType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := fwe_trade_common.OrderType(v)
		_field = &tmp
	}
	p.OrderType = _field
	return offset, nil
}

func (p *ChargeRefundReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Operator = _field
	return offset, nil
}

func (p *ChargeRefundReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *ChargeRefundReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ChargeRefundReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ChargeRefundReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field100Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ChargeRefundReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *ChargeRefundReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RuleID)
	return offset
}

func (p *ChargeRefundReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRefundList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.RefundList {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *ChargeRefundReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFulfillID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.FulfillID)
	}
	return offset
}

func (p *ChargeRefundReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.OrderType))
	}
	return offset
}

func (p *ChargeRefundReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Operator)
	return offset
}

func (p *ChargeRefundReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ChargeRefundReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *ChargeRefundReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ChargeRefundReq) field3Length() int {
	l := 0
	if p.IsSetRefundList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.RefundList {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *ChargeRefundReq) field4Length() int {
	l := 0
	if p.IsSetFulfillID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.FulfillID)
	}
	return l
}

func (p *ChargeRefundReq) field5Length() int {
	l := 0
	if p.IsSetOrderType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *ChargeRefundReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Operator)
	return l
}

func (p *ChargeRefundReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *ChargeRefundReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ChargeRefundReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	p.RuleID = src.RuleID

	if src.RefundList != nil {
		p.RefundList = make([]*SingleRefund, 0, len(src.RefundList))
		for _, elem := range src.RefundList {
			var _elem *SingleRefund
			if elem != nil {
				_elem = &SingleRefund{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RefundList = append(p.RefundList, _elem)
		}
	}

	if src.FulfillID != nil {
		var tmp string
		if *src.FulfillID != "" {
			tmp = kutils.StringDeepCopy(*src.FulfillID)
		}
		p.FulfillID = &tmp
	}

	if src.OrderType != nil {
		tmp := *src.OrderType
		p.OrderType = &tmp
	}

	if src.Operator != "" {
		p.Operator = kutils.StringDeepCopy(src.Operator)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *ChargeRefundResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ChargeRefundResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ChargeRefundResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*ChargeResult_, 0, size)
	values := make([]ChargeResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ChargeResultList = _field
	return offset, nil
}

func (p *ChargeRefundResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*SingleRefund, 0, size)
	values := make([]SingleRefund, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RefundList = _field
	return offset, nil
}

func (p *ChargeRefundResp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.RefundMode
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.RefundMode(v)
	}
	p.RefundMode = _field
	return offset, nil
}

func (p *ChargeRefundResp) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RecordID = _field
	return offset, nil
}

func (p *ChargeRefundResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *ChargeRefundResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ChargeRefundResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ChargeRefundResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field100Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ChargeRefundResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ChargeResultList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *ChargeRefundResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RefundList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *ChargeRefundResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.RefundMode))
	return offset
}

func (p *ChargeRefundResp) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RecordID)
	return offset
}

func (p *ChargeRefundResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *ChargeRefundResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ChargeResultList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *ChargeRefundResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RefundList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *ChargeRefundResp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ChargeRefundResp) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RecordID)
	return l
}

func (p *ChargeRefundResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *ChargeRefundResp) DeepCopy(s interface{}) error {
	src, ok := s.(*ChargeRefundResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ChargeResultList != nil {
		p.ChargeResultList = make([]*ChargeResult_, 0, len(src.ChargeResultList))
		for _, elem := range src.ChargeResultList {
			var _elem *ChargeResult_
			if elem != nil {
				_elem = &ChargeResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ChargeResultList = append(p.ChargeResultList, _elem)
		}
	}

	if src.RefundList != nil {
		p.RefundList = make([]*SingleRefund, 0, len(src.RefundList))
		for _, elem := range src.RefundList {
			var _elem *SingleRefund
			if elem != nil {
				_elem = &SingleRefund{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RefundList = append(p.RefundList, _elem)
		}
	}

	p.RefundMode = src.RefundMode

	if src.RecordID != "" {
		p.RecordID = kutils.StringDeepCopy(src.RecordID)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *CommonChargeReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetOrderID bool = false
	var issetRuleID bool = false
	var issetOperator bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOrderID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRuleID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetOrderID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRuleID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 100
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CommonChargeReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_CommonChargeReq[fieldId]))
}

func (p *CommonChargeReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *CommonChargeReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleID = _field
	return offset, nil
}

func (p *CommonChargeReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Params = _field
	return offset, nil
}

func (p *CommonChargeReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.IsReconcile = _field
	return offset, nil
}

func (p *CommonChargeReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FulfillID = _field
	return offset, nil
}

func (p *CommonChargeReq) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field *fwe_trade_common.OrderType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := fwe_trade_common.OrderType(v)
		_field = &tmp
	}
	p.OrderType = _field
	return offset, nil
}

func (p *CommonChargeReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Operator = _field
	return offset, nil
}

func (p *CommonChargeReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *CommonChargeReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CommonChargeReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CommonChargeReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field20Length()
		l += p.field100Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CommonChargeReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *CommonChargeReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RuleID)
	return offset
}

func (p *CommonChargeReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetParams() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Params)
	}
	return offset
}

func (p *CommonChargeReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetIsReconcile() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 4)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.IsReconcile)
	}
	return offset
}

func (p *CommonChargeReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFulfillID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.FulfillID)
	}
	return offset
}

func (p *CommonChargeReq) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 20)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.OrderType))
	}
	return offset
}

func (p *CommonChargeReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Operator)
	return offset
}

func (p *CommonChargeReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *CommonChargeReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *CommonChargeReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *CommonChargeReq) field3Length() int {
	l := 0
	if p.IsSetParams() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Params)
	}
	return l
}

func (p *CommonChargeReq) field4Length() int {
	l := 0
	if p.IsSetIsReconcile() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *CommonChargeReq) field5Length() int {
	l := 0
	if p.IsSetFulfillID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.FulfillID)
	}
	return l
}

func (p *CommonChargeReq) field20Length() int {
	l := 0
	if p.IsSetOrderType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CommonChargeReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Operator)
	return l
}

func (p *CommonChargeReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *CommonChargeReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CommonChargeReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	p.RuleID = src.RuleID

	if src.Params != nil {
		var tmp string
		if *src.Params != "" {
			tmp = kutils.StringDeepCopy(*src.Params)
		}
		p.Params = &tmp
	}

	if src.IsReconcile != nil {
		tmp := *src.IsReconcile
		p.IsReconcile = &tmp
	}

	if src.FulfillID != nil {
		var tmp string
		if *src.FulfillID != "" {
			tmp = kutils.StringDeepCopy(*src.FulfillID)
		}
		p.FulfillID = &tmp
	}

	if src.OrderType != nil {
		tmp := *src.OrderType
		p.OrderType = &tmp
	}

	if src.Operator != "" {
		p.Operator = kutils.StringDeepCopy(src.Operator)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *CommonChargeResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CommonChargeResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CommonChargeResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*ChargeResult_, 0, size)
	values := make([]ChargeResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ChargeResultList = _field
	return offset, nil
}

func (p *CommonChargeResp) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RecordID = _field
	return offset, nil
}

func (p *CommonChargeResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *CommonChargeResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CommonChargeResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CommonChargeResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field100Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CommonChargeResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ChargeResultList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *CommonChargeResp) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RecordID)
	return offset
}

func (p *CommonChargeResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *CommonChargeResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ChargeResultList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *CommonChargeResp) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RecordID)
	return l
}

func (p *CommonChargeResp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *CommonChargeResp) DeepCopy(s interface{}) error {
	src, ok := s.(*CommonChargeResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ChargeResultList != nil {
		p.ChargeResultList = make([]*ChargeResult_, 0, len(src.ChargeResultList))
		for _, elem := range src.ChargeResultList {
			var _elem *ChargeResult_
			if elem != nil {
				_elem = &ChargeResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ChargeResultList = append(p.ChargeResultList, _elem)
		}
	}

	if src.RecordID != "" {
		p.RecordID = kutils.StringDeepCopy(src.RecordID)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *FeeChargeRecord) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeChargeRecord[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeChargeRecord) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BizScene = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RecordID = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField4(buf []byte) (int, error) {
	offset := 0
	_field := NewFeeRule()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Rule = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ExprParams = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField6(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*ChargeResult_, 0, size)
	values := make([]ChargeResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ChargeResultList = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceModel = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RawReq = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField9(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*SingleRefund, 0, size)
	values := make([]SingleRefund, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RefundList = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.LogID = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AmountData = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField12(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.SettleFinanceIDList = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Operator = _field
	return offset, nil
}

func (p *FeeChargeRecord) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeChargeRecord) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeChargeRecord) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field100Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeChargeRecord) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.BizScene)
	return offset
}

func (p *FeeChargeRecord) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *FeeChargeRecord) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RecordID)
	return offset
}

func (p *FeeChargeRecord) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 4)
	offset += p.Rule.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeChargeRecord) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ExprParams)
	return offset
}

func (p *FeeChargeRecord) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 6)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ChargeResultList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *FeeChargeRecord) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FinanceModel)
	return offset
}

func (p *FeeChargeRecord) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RawReq)
	return offset
}

func (p *FeeChargeRecord) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 9)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RefundList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *FeeChargeRecord) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.LogID)
	return offset
}

func (p *FeeChargeRecord) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AmountData)
	return offset
}

func (p *FeeChargeRecord) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 12)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.SettleFinanceIDList {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *FeeChargeRecord) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Operator)
	return offset
}

func (p *FeeChargeRecord) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *FeeChargeRecord) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *FeeChargeRecord) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RecordID)
	return l
}

func (p *FeeChargeRecord) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Rule.BLength()
	return l
}

func (p *FeeChargeRecord) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ExprParams)
	return l
}

func (p *FeeChargeRecord) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ChargeResultList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *FeeChargeRecord) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FinanceModel)
	return l
}

func (p *FeeChargeRecord) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RawReq)
	return l
}

func (p *FeeChargeRecord) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RefundList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *FeeChargeRecord) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.LogID)
	return l
}

func (p *FeeChargeRecord) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AmountData)
	return l
}

func (p *FeeChargeRecord) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.SettleFinanceIDList {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *FeeChargeRecord) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Operator)
	return l
}

func (p *FeeChargeRecord) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeChargeRecord)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.BizScene = src.BizScene

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	if src.RecordID != "" {
		p.RecordID = kutils.StringDeepCopy(src.RecordID)
	}

	var _rule *FeeRule
	if src.Rule != nil {
		_rule = &FeeRule{}
		if err := _rule.DeepCopy(src.Rule); err != nil {
			return err
		}
	}
	p.Rule = _rule

	if src.ExprParams != "" {
		p.ExprParams = kutils.StringDeepCopy(src.ExprParams)
	}

	if src.ChargeResultList != nil {
		p.ChargeResultList = make([]*ChargeResult_, 0, len(src.ChargeResultList))
		for _, elem := range src.ChargeResultList {
			var _elem *ChargeResult_
			if elem != nil {
				_elem = &ChargeResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ChargeResultList = append(p.ChargeResultList, _elem)
		}
	}

	if src.FinanceModel != "" {
		p.FinanceModel = kutils.StringDeepCopy(src.FinanceModel)
	}

	if src.RawReq != "" {
		p.RawReq = kutils.StringDeepCopy(src.RawReq)
	}

	if src.RefundList != nil {
		p.RefundList = make([]*SingleRefund, 0, len(src.RefundList))
		for _, elem := range src.RefundList {
			var _elem *SingleRefund
			if elem != nil {
				_elem = &SingleRefund{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RefundList = append(p.RefundList, _elem)
		}
	}

	if src.LogID != "" {
		p.LogID = kutils.StringDeepCopy(src.LogID)
	}

	if src.AmountData != "" {
		p.AmountData = kutils.StringDeepCopy(src.AmountData)
	}

	if src.SettleFinanceIDList != nil {
		p.SettleFinanceIDList = make([]string, 0, len(src.SettleFinanceIDList))
		for _, elem := range src.SettleFinanceIDList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.SettleFinanceIDList = append(p.SettleFinanceIDList, _elem)
		}
	}

	if src.Operator != "" {
		p.Operator = kutils.StringDeepCopy(src.Operator)
	}

	return nil
}

func (p *MGetChargeRecordReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetChargeRecordReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *MGetChargeRecordReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.RecordIDList = _field
	return offset, nil
}

func (p *MGetChargeRecordReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *MGetChargeRecordReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *MGetChargeRecordReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *MGetChargeRecordReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *MGetChargeRecordReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRecordIDList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.RecordIDList {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *MGetChargeRecordReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *MGetChargeRecordReq) field1Length() int {
	l := 0
	if p.IsSetRecordIDList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.RecordIDList {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *MGetChargeRecordReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *MGetChargeRecordReq) DeepCopy(s interface{}) error {
	src, ok := s.(*MGetChargeRecordReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RecordIDList != nil {
		p.RecordIDList = make([]string, 0, len(src.RecordIDList))
		for _, elem := range src.RecordIDList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.RecordIDList = append(p.RecordIDList, _elem)
		}
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *MGetChargeRecordResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetChargeRecordResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *MGetChargeRecordResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*FeeChargeRecord, 0, size)
	values := make([]FeeChargeRecord, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RecordList = _field
	return offset, nil
}

func (p *MGetChargeRecordResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *MGetChargeRecordResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *MGetChargeRecordResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *MGetChargeRecordResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *MGetChargeRecordResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RecordList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *MGetChargeRecordResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *MGetChargeRecordResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RecordList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *MGetChargeRecordResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *MGetChargeRecordResp) DeepCopy(s interface{}) error {
	src, ok := s.(*MGetChargeRecordResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RecordList != nil {
		p.RecordList = make([]*FeeChargeRecord, 0, len(src.RecordList))
		for _, elem := range src.RecordList {
			var _elem *FeeChargeRecord
			if elem != nil {
				_elem = &FeeChargeRecord{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RecordList = append(p.RecordList, _elem)
		}
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *CheckBizFeeItemsReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckBizFeeItemsReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CheckBizFeeItemsReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BizScene = _field
	return offset, nil
}

func (p *CheckBizFeeItemsReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.FeeNameList = _field
	return offset, nil
}

func (p *CheckBizFeeItemsReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *CheckBizFeeItemsReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CheckBizFeeItemsReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CheckBizFeeItemsReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CheckBizFeeItemsReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.BizScene)
	return offset
}

func (p *CheckBizFeeItemsReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FeeNameList {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *CheckBizFeeItemsReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *CheckBizFeeItemsReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *CheckBizFeeItemsReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FeeNameList {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *CheckBizFeeItemsReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *CheckBizFeeItemsReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CheckBizFeeItemsReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.BizScene = src.BizScene

	if src.FeeNameList != nil {
		p.FeeNameList = make([]string, 0, len(src.FeeNameList))
		for _, elem := range src.FeeNameList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.FeeNameList = append(p.FeeNameList, _elem)
		}
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *CheckBizFeeItemsResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckBizFeeItemsResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CheckBizFeeItemsResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Ok = _field
	return offset, nil
}

func (p *CheckBizFeeItemsResp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *CheckBizFeeItemsResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CheckBizFeeItemsResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CheckBizFeeItemsResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CheckBizFeeItemsResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 1)
	offset += thrift.Binary.WriteBool(buf[offset:], p.Ok)
	return offset
}

func (p *CheckBizFeeItemsResp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *CheckBizFeeItemsResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *CheckBizFeeItemsResp) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *CheckBizFeeItemsResp) DeepCopy(s interface{}) error {
	src, ok := s.(*CheckBizFeeItemsResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.Ok = src.Ok

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *FeeServiceQueryFeeItemArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeItemArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceQueryFeeItemArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewQueryFeeItemReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceQueryFeeItemArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceQueryFeeItemArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceQueryFeeItemArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceQueryFeeItemArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceQueryFeeItemArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceQueryFeeItemArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceQueryFeeItemArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *QueryFeeItemReq
	if src.Req != nil {
		_req = &QueryFeeItemReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceQueryFeeItemResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeItemResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceQueryFeeItemResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewQueryFeeItemResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceQueryFeeItemResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceQueryFeeItemResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceQueryFeeItemResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceQueryFeeItemResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceQueryFeeItemResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceQueryFeeItemResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceQueryFeeItemResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *QueryFeeItemResp
	if src.Success != nil {
		_success = &QueryFeeItemResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceUpsertFeeItemArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeItemArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceUpsertFeeItemArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewUpsertFeeItemReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceUpsertFeeItemArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceUpsertFeeItemArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceUpsertFeeItemArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceUpsertFeeItemArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceUpsertFeeItemArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceUpsertFeeItemArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceUpsertFeeItemArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *UpsertFeeItemReq
	if src.Req != nil {
		_req = &UpsertFeeItemReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceUpsertFeeItemResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeItemResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceUpsertFeeItemResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewUpsertFeeItemResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceUpsertFeeItemResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceUpsertFeeItemResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceUpsertFeeItemResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceUpsertFeeItemResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceUpsertFeeItemResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceUpsertFeeItemResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceUpsertFeeItemResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *UpsertFeeItemResp
	if src.Success != nil {
		_success = &UpsertFeeItemResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceDeleteFeeItemArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeItemArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceDeleteFeeItemArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewDeleteFeeItemReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceDeleteFeeItemArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceDeleteFeeItemArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceDeleteFeeItemArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceDeleteFeeItemArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceDeleteFeeItemArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceDeleteFeeItemArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceDeleteFeeItemArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *DeleteFeeItemReq
	if src.Req != nil {
		_req = &DeleteFeeItemReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceDeleteFeeItemResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeItemResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceDeleteFeeItemResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewDeleteFeeItemResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceDeleteFeeItemResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceDeleteFeeItemResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceDeleteFeeItemResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceDeleteFeeItemResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceDeleteFeeItemResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceDeleteFeeItemResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceDeleteFeeItemResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *DeleteFeeItemResp
	if src.Success != nil {
		_success = &DeleteFeeItemResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceQueryFeeRulesArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeRulesArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceQueryFeeRulesArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewQueryFeeRulesReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceQueryFeeRulesArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceQueryFeeRulesArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceQueryFeeRulesArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceQueryFeeRulesArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceQueryFeeRulesArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceQueryFeeRulesArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceQueryFeeRulesArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *QueryFeeRulesReq
	if src.Req != nil {
		_req = &QueryFeeRulesReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceQueryFeeRulesResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeRulesResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceQueryFeeRulesResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewQueryFeeRulesResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceQueryFeeRulesResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceQueryFeeRulesResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceQueryFeeRulesResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceQueryFeeRulesResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceQueryFeeRulesResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceQueryFeeRulesResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceQueryFeeRulesResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *QueryFeeRulesResp
	if src.Success != nil {
		_success = &QueryFeeRulesResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceUpsertFeeRuleArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeRuleArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceUpsertFeeRuleArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewUpsertFeeRuleReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceUpsertFeeRuleArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceUpsertFeeRuleArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceUpsertFeeRuleArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceUpsertFeeRuleArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceUpsertFeeRuleArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceUpsertFeeRuleArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceUpsertFeeRuleArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *UpsertFeeRuleReq
	if src.Req != nil {
		_req = &UpsertFeeRuleReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceUpsertFeeRuleResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeRuleResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceUpsertFeeRuleResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewUpsertFeeRuleResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceUpsertFeeRuleResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceUpsertFeeRuleResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceUpsertFeeRuleResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceUpsertFeeRuleResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceUpsertFeeRuleResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceUpsertFeeRuleResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceUpsertFeeRuleResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *UpsertFeeRuleResp
	if src.Success != nil {
		_success = &UpsertFeeRuleResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceDeleteFeeRuleArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeRuleArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceDeleteFeeRuleArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewDeleteFeeRuleReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceDeleteFeeRuleArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceDeleteFeeRuleArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceDeleteFeeRuleArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceDeleteFeeRuleArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceDeleteFeeRuleArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceDeleteFeeRuleArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceDeleteFeeRuleArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *DeleteFeeRuleReq
	if src.Req != nil {
		_req = &DeleteFeeRuleReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceDeleteFeeRuleResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeRuleResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceDeleteFeeRuleResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewDeleteFeeRuleResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceDeleteFeeRuleResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceDeleteFeeRuleResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceDeleteFeeRuleResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceDeleteFeeRuleResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceDeleteFeeRuleResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceDeleteFeeRuleResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceDeleteFeeRuleResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *DeleteFeeRuleResp
	if src.Success != nil {
		_success = &DeleteFeeRuleResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceQueryFeeFormulasArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeFormulasArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceQueryFeeFormulasArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewQueryFeeFormulasReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceQueryFeeFormulasArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceQueryFeeFormulasArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceQueryFeeFormulasArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceQueryFeeFormulasArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceQueryFeeFormulasArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceQueryFeeFormulasArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceQueryFeeFormulasArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *QueryFeeFormulasReq
	if src.Req != nil {
		_req = &QueryFeeFormulasReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceQueryFeeFormulasResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceQueryFeeFormulasResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceQueryFeeFormulasResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewQueryFeeFormulasResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceQueryFeeFormulasResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceQueryFeeFormulasResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceQueryFeeFormulasResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceQueryFeeFormulasResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceQueryFeeFormulasResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceQueryFeeFormulasResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceQueryFeeFormulasResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *QueryFeeFormulasResp
	if src.Success != nil {
		_success = &QueryFeeFormulasResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceUpsertFeeFormulaArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeFormulaArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceUpsertFeeFormulaArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewUpsertFeeFormulaReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceUpsertFeeFormulaArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceUpsertFeeFormulaArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceUpsertFeeFormulaArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceUpsertFeeFormulaArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceUpsertFeeFormulaArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceUpsertFeeFormulaArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceUpsertFeeFormulaArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *UpsertFeeFormulaReq
	if src.Req != nil {
		_req = &UpsertFeeFormulaReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceUpsertFeeFormulaResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceUpsertFeeFormulaResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceUpsertFeeFormulaResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewUpsertFeeFormulaResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceUpsertFeeFormulaResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceUpsertFeeFormulaResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceUpsertFeeFormulaResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceUpsertFeeFormulaResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceUpsertFeeFormulaResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceUpsertFeeFormulaResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceUpsertFeeFormulaResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *UpsertFeeFormulaResp
	if src.Success != nil {
		_success = &UpsertFeeFormulaResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceDeleteFeeFormulaArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeFormulaArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceDeleteFeeFormulaArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewDeleteFeeFormulaReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceDeleteFeeFormulaArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceDeleteFeeFormulaArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceDeleteFeeFormulaArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceDeleteFeeFormulaArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceDeleteFeeFormulaArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceDeleteFeeFormulaArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceDeleteFeeFormulaArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *DeleteFeeFormulaReq
	if src.Req != nil {
		_req = &DeleteFeeFormulaReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceDeleteFeeFormulaResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceDeleteFeeFormulaResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceDeleteFeeFormulaResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewDeleteFeeFormulaResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceDeleteFeeFormulaResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceDeleteFeeFormulaResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceDeleteFeeFormulaResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceDeleteFeeFormulaResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceDeleteFeeFormulaResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceDeleteFeeFormulaResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceDeleteFeeFormulaResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *DeleteFeeFormulaResp
	if src.Success != nil {
		_success = &DeleteFeeFormulaResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceChargeSettleArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceChargeSettleArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceChargeSettleArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewChargeSettleReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceChargeSettleArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceChargeSettleArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceChargeSettleArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceChargeSettleArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceChargeSettleArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceChargeSettleArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceChargeSettleArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *ChargeSettleReq
	if src.Req != nil {
		_req = &ChargeSettleReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceChargeSettleResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceChargeSettleResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceChargeSettleResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewChargeSettleResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceChargeSettleResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceChargeSettleResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceChargeSettleResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceChargeSettleResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceChargeSettleResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceChargeSettleResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceChargeSettleResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *ChargeSettleResp
	if src.Success != nil {
		_success = &ChargeSettleResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceChargeRefundArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceChargeRefundArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceChargeRefundArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewChargeRefundReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceChargeRefundArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceChargeRefundArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceChargeRefundArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceChargeRefundArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceChargeRefundArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceChargeRefundArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceChargeRefundArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *ChargeRefundReq
	if src.Req != nil {
		_req = &ChargeRefundReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceChargeRefundResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceChargeRefundResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceChargeRefundResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewChargeRefundResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceChargeRefundResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceChargeRefundResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceChargeRefundResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceChargeRefundResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceChargeRefundResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceChargeRefundResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceChargeRefundResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *ChargeRefundResp
	if src.Success != nil {
		_success = &ChargeRefundResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceCommonChargeArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceCommonChargeArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceCommonChargeArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewCommonChargeReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceCommonChargeArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceCommonChargeArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceCommonChargeArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceCommonChargeArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceCommonChargeArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceCommonChargeArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceCommonChargeArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *CommonChargeReq
	if src.Req != nil {
		_req = &CommonChargeReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceCommonChargeResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceCommonChargeResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceCommonChargeResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewCommonChargeResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceCommonChargeResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceCommonChargeResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceCommonChargeResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceCommonChargeResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceCommonChargeResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceCommonChargeResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceCommonChargeResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *CommonChargeResp
	if src.Success != nil {
		_success = &CommonChargeResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceMGetChargeRecordArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceMGetChargeRecordArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceMGetChargeRecordArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewMGetChargeRecordReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceMGetChargeRecordArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceMGetChargeRecordArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceMGetChargeRecordArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceMGetChargeRecordArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceMGetChargeRecordArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceMGetChargeRecordArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceMGetChargeRecordArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *MGetChargeRecordReq
	if src.Req != nil {
		_req = &MGetChargeRecordReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceMGetChargeRecordResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceMGetChargeRecordResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceMGetChargeRecordResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewMGetChargeRecordResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceMGetChargeRecordResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceMGetChargeRecordResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceMGetChargeRecordResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceMGetChargeRecordResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceMGetChargeRecordResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceMGetChargeRecordResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceMGetChargeRecordResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *MGetChargeRecordResp
	if src.Success != nil {
		_success = &MGetChargeRecordResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceCheckBizFeeItemsArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceCheckBizFeeItemsArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceCheckBizFeeItemsArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewCheckBizFeeItemsReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *FeeServiceCheckBizFeeItemsArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceCheckBizFeeItemsArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceCheckBizFeeItemsArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceCheckBizFeeItemsArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *FeeServiceCheckBizFeeItemsArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *FeeServiceCheckBizFeeItemsArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceCheckBizFeeItemsArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *CheckBizFeeItemsReq
	if src.Req != nil {
		_req = &CheckBizFeeItemsReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *FeeServiceCheckBizFeeItemsResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FeeServiceCheckBizFeeItemsResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *FeeServiceCheckBizFeeItemsResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewCheckBizFeeItemsResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *FeeServiceCheckBizFeeItemsResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *FeeServiceCheckBizFeeItemsResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *FeeServiceCheckBizFeeItemsResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *FeeServiceCheckBizFeeItemsResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *FeeServiceCheckBizFeeItemsResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *FeeServiceCheckBizFeeItemsResult) DeepCopy(s interface{}) error {
	src, ok := s.(*FeeServiceCheckBizFeeItemsResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *CheckBizFeeItemsResp
	if src.Success != nil {
		_success = &CheckBizFeeItemsResp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *FeeServiceQueryFeeItemArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceQueryFeeItemResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceUpsertFeeItemArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceUpsertFeeItemResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceDeleteFeeItemArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceDeleteFeeItemResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceQueryFeeRulesArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceQueryFeeRulesResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceUpsertFeeRuleArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceUpsertFeeRuleResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceDeleteFeeRuleArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceDeleteFeeRuleResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceQueryFeeFormulasArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceQueryFeeFormulasResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceUpsertFeeFormulaArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceUpsertFeeFormulaResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceDeleteFeeFormulaArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceDeleteFeeFormulaResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceChargeSettleArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceChargeSettleResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceChargeRefundArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceChargeRefundResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceCommonChargeArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceCommonChargeResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceMGetChargeRecordArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceMGetChargeRecordResult) GetResult() interface{} {
	return p.Success
}

func (p *FeeServiceCheckBizFeeItemsArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *FeeServiceCheckBizFeeItemsResult) GetResult() interface{} {
	return p.Success
}

func (p *QueryFeeItemReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *UpsertFeeItemReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *DeleteFeeItemReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *QueryFeeRulesReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *UpsertFeeRuleReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *DeleteFeeRuleReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *QueryFeeFormulasReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *UpsertFeeFormulaReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *DeleteFeeFormulaReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *ChargeSettleReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *ChargeRefundReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *CommonChargeReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *MGetChargeRecordReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *CheckBizFeeItemsReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *QueryFeeItemResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *UpsertFeeItemResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *DeleteFeeItemResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *QueryFeeRulesResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *UpsertFeeRuleResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *DeleteFeeRuleResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *QueryFeeFormulasResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *UpsertFeeFormulaResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *DeleteFeeFormulaResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *ChargeSettleResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *ChargeRefundResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *CommonChargeResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *MGetChargeRecordResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *CheckBizFeeItemsResp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}
