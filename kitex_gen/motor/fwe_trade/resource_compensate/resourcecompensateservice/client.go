// Code generated by Kitex v1.20.3. DO NOT EDIT.

package resourcecompensateservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	resource_compensate "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/resource_compensate"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	RegisterResource(ctx context.Context, req *resource_compensate.RegisterResourceReq, callOptions ...callopt.Option) (r *resource_compensate.RegisterResourceResp, err error)
	CompensateResource(ctx context.Context, req *resource_compensate.CompensateReq, callOptions ...callopt.Option) (r *resource_compensate.CompensateResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kResourceCompensateServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kResourceCompensateServiceClient struct {
	*kClient
}

func (p *kResourceCompensateServiceClient) RegisterResource(ctx context.Context, req *resource_compensate.RegisterResourceReq, callOptions ...callopt.Option) (r *resource_compensate.RegisterResourceResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.RegisterResource(ctx, req)
}

func (p *kResourceCompensateServiceClient) CompensateResource(ctx context.Context, req *resource_compensate.CompensateReq, callOptions ...callopt.Option) (r *resource_compensate.CompensateResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CompensateResource(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kResourceCompensateServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
