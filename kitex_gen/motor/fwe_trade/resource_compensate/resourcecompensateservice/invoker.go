// Code generated by Kitex v1.12.2. DO NOT EDIT.

package resourcecompensateservice

import (
	byted "code.byted.org/kite/kitex/byted"
	server "code.byted.org/kite/kitex/server"
	resource_compensate "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/resource_compensate"
)

// NewInvoker creates a server.Invoker with the given handler and options.
func NewInvoker(handler resource_compensate.ResourceCompensateService, opts ...server.Option) server.Invoker {
	var options []server.Option

	options = append(options, byted.InvokeSuite(serviceInfo()))

	options = append(options, opts...)

	s := server.NewInvoker(options...)
	if err := s.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	if err := s.Init(); err != nil {
		panic(err)
	}
	return s
}

// NewInvokerWithBytedConfig creates a server.Invoker with the given handler and options.
func NewInvokerWithBytedConfig(handler resource_compensate.ResourceCompensateService, config *byted.ServerConfig, opts ...server.Option) server.Invoker {
	var options []server.Option
	options = append(options, byted.InvokeSuiteWithConfig(serviceInfo(), config))
	options = append(options, opts...)

	s := server.NewInvoker(options...)
	if err := s.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	if err := s.Init(); err != nil {
		panic(err)
	}
	return s
}
