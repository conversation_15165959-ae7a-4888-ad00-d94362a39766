// Code generated by Kitex v1.20.3. DO NOT EDIT.

package resourcecompensateservice

import (
	client "code.byted.org/kite/kitex/client"
	resource_compensate "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/resource_compensate"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"RegisterResource": kitex.NewMethodInfo(
		registerResourceHandler,
		newResourceCompensateServiceRegisterResourceArgs,
		newResourceCompensateServiceRegisterResourceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CompensateResource": kitex.NewMethodInfo(
		compensateResourceHandler,
		newResourceCompensateServiceCompensateResourceArgs,
		newResourceCompensateServiceCompensateResourceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	resourceCompensateServiceServiceInfo                = NewServiceInfo()
	resourceCompensateServiceServiceInfoForClient       = NewServiceInfoForClient()
	resourceCompensateServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return resourceCompensateServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return resourceCompensateServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return resourceCompensateServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "ResourceCompensateService"
	handlerType := (*resource_compensate.ResourceCompensateService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "resource_compensate",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func registerResourceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*resource_compensate.ResourceCompensateServiceRegisterResourceArgs)
	realResult := result.(*resource_compensate.ResourceCompensateServiceRegisterResourceResult)
	success, err := handler.(resource_compensate.ResourceCompensateService).RegisterResource(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newResourceCompensateServiceRegisterResourceArgs() interface{} {
	return resource_compensate.NewResourceCompensateServiceRegisterResourceArgs()
}

func newResourceCompensateServiceRegisterResourceResult() interface{} {
	return resource_compensate.NewResourceCompensateServiceRegisterResourceResult()
}

func compensateResourceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*resource_compensate.ResourceCompensateServiceCompensateResourceArgs)
	realResult := result.(*resource_compensate.ResourceCompensateServiceCompensateResourceResult)
	success, err := handler.(resource_compensate.ResourceCompensateService).CompensateResource(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newResourceCompensateServiceCompensateResourceArgs() interface{} {
	return resource_compensate.NewResourceCompensateServiceCompensateResourceArgs()
}

func newResourceCompensateServiceCompensateResourceResult() interface{} {
	return resource_compensate.NewResourceCompensateServiceCompensateResourceResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) RegisterResource(ctx context.Context, req *resource_compensate.RegisterResourceReq) (r *resource_compensate.RegisterResourceResp, err error) {
	var _args resource_compensate.ResourceCompensateServiceRegisterResourceArgs
	_args.Req = req
	var _result resource_compensate.ResourceCompensateServiceRegisterResourceResult
	if err = p.c.Call(ctx, "RegisterResource", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CompensateResource(ctx context.Context, req *resource_compensate.CompensateReq) (r *resource_compensate.CompensateResp, err error) {
	var _args resource_compensate.ResourceCompensateServiceCompensateResourceArgs
	_args.Req = req
	var _result resource_compensate.ResourceCompensateServiceCompensateResourceResult
	if err = p.c.Call(ctx, "CompensateResource", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
