// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package resource_compensate

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"context"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
)

type ResourceType int64

const (
	ResourceType_Product ResourceType = 1
	ResourceType_Coupon  ResourceType = 2
)

func (p ResourceType) String() string {
	switch p {
	case ResourceType_Product:
		return "Product"
	case ResourceType_Coupon:
		return "Coupon"
	}
	return "<UNSET>"
}

func ResourceTypeFromString(s string) (ResourceType, error) {
	switch s {
	case "Product":
		return ResourceType_Product, nil
	case "Coupon":
		return ResourceType_Coupon, nil
	}
	return ResourceType(0), fmt.Errorf("not a valid ResourceType string")
}

func ResourceTypePtr(v ResourceType) *ResourceType { return &v }
func (p *ResourceType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ResourceType(result.Int64)
	return
}

func (p *ResourceType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ResourceStatus int64

const (
	ResourceStatus_Occupied      ResourceStatus = 1
	ResourceStatus_Released      ResourceStatus = 2
	ResourceStatus_ReleaseFailed ResourceStatus = 3
)

func (p ResourceStatus) String() string {
	switch p {
	case ResourceStatus_Occupied:
		return "Occupied"
	case ResourceStatus_Released:
		return "Released"
	case ResourceStatus_ReleaseFailed:
		return "ReleaseFailed"
	}
	return "<UNSET>"
}

func ResourceStatusFromString(s string) (ResourceStatus, error) {
	switch s {
	case "Occupied":
		return ResourceStatus_Occupied, nil
	case "Released":
		return ResourceStatus_Released, nil
	case "ReleaseFailed":
		return ResourceStatus_ReleaseFailed, nil
	}
	return ResourceStatus(0), fmt.Errorf("not a valid ResourceStatus string")
}

func ResourceStatusPtr(v ResourceStatus) *ResourceStatus { return &v }
func (p *ResourceStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ResourceStatus(result.Int64)
	return
}

func (p *ResourceStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ResourceInfo struct {
	OrderID         string                     `thrift:"order_id,1" frugal:"1,default,string" json:"order_id"`
	OrderType       fwe_trade_common.OrderType `thrift:"order_type,2" frugal:"2,default,OrderType" json:"order_type"`
	ResourceType    ResourceType               `thrift:"resource_type,3" frugal:"3,default,ResourceType" json:"resource_type"`
	ResourceSubType int32                      `thrift:"resource_sub_type,4" frugal:"4,default,i32" json:"resource_sub_type"`
	OutID           string                     `thrift:"out_id,5" frugal:"5,default,string" json:"out_id"`
	OccupyNum       int32                      `thrift:"occupy_num,6" frugal:"6,default,i32" json:"occupy_num"`
	Extra           map[string]string          `thrift:"extra,100" frugal:"100,default,map<string:string>" json:"extra"`
}

func NewResourceInfo() *ResourceInfo {
	return &ResourceInfo{}
}

func (p *ResourceInfo) InitDefault() {
}

func (p *ResourceInfo) GetOrderID() (v string) {
	return p.OrderID
}

func (p *ResourceInfo) GetOrderType() (v fwe_trade_common.OrderType) {
	return p.OrderType
}

func (p *ResourceInfo) GetResourceType() (v ResourceType) {
	return p.ResourceType
}

func (p *ResourceInfo) GetResourceSubType() (v int32) {
	return p.ResourceSubType
}

func (p *ResourceInfo) GetOutID() (v string) {
	return p.OutID
}

func (p *ResourceInfo) GetOccupyNum() (v int32) {
	return p.OccupyNum
}

func (p *ResourceInfo) GetExtra() (v map[string]string) {
	return p.Extra
}
func (p *ResourceInfo) SetOrderID(val string) {
	p.OrderID = val
}
func (p *ResourceInfo) SetOrderType(val fwe_trade_common.OrderType) {
	p.OrderType = val
}
func (p *ResourceInfo) SetResourceType(val ResourceType) {
	p.ResourceType = val
}
func (p *ResourceInfo) SetResourceSubType(val int32) {
	p.ResourceSubType = val
}
func (p *ResourceInfo) SetOutID(val string) {
	p.OutID = val
}
func (p *ResourceInfo) SetOccupyNum(val int32) {
	p.OccupyNum = val
}
func (p *ResourceInfo) SetExtra(val map[string]string) {
	p.Extra = val
}

var fieldIDToName_ResourceInfo = map[int16]string{
	1:   "order_id",
	2:   "order_type",
	3:   "resource_type",
	4:   "resource_sub_type",
	5:   "out_id",
	6:   "occupy_num",
	100: "extra",
}

func (p *ResourceInfo) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ResourceInfo")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *ResourceInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field fwe_trade_common.OrderType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = fwe_trade_common.OrderType(v)
	}
	p.OrderType = _field
	return nil
}
func (p *ResourceInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field ResourceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ResourceType(v)
	}
	p.ResourceType = _field
	return nil
}
func (p *ResourceInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResourceSubType = _field
	return nil
}
func (p *ResourceInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OutID = _field
	return nil
}
func (p *ResourceInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OccupyNum = _field
	return nil
}
func (p *ResourceInfo) ReadField100(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Extra = _field
	return nil
}

func (p *ResourceInfo) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ResourceInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.OrderType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ResourceInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("resource_type", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ResourceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ResourceInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("resource_sub_type", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ResourceSubType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ResourceInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("out_id", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OutID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ResourceInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("occupy_num", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.OccupyNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ResourceInfo) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("extra", thrift.MAP, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Extra)); err != nil {
		return err
	}
	for k, v := range p.Extra {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}

func (p *ResourceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceInfo(%+v)", *p)

}

type RegisterResourceReq struct {
	RegisterList []*ResourceInfo `thrift:"register_list,1" frugal:"1,default,list<ResourceInfo>" json:"register_list"`
	Base         *base.Base      `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewRegisterResourceReq() *RegisterResourceReq {
	return &RegisterResourceReq{}
}

func (p *RegisterResourceReq) InitDefault() {
}

func (p *RegisterResourceReq) GetRegisterList() (v []*ResourceInfo) {
	return p.RegisterList
}

var RegisterResourceReq_Base_DEFAULT *base.Base

func (p *RegisterResourceReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return RegisterResourceReq_Base_DEFAULT
	}
	return p.Base
}
func (p *RegisterResourceReq) SetRegisterList(val []*ResourceInfo) {
	p.RegisterList = val
}
func (p *RegisterResourceReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_RegisterResourceReq = map[int16]string{
	1:   "register_list",
	255: "Base",
}

func (p *RegisterResourceReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *RegisterResourceReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RegisterResourceReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RegisterResourceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RegisterResourceReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ResourceInfo, 0, size)
	values := make([]ResourceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RegisterList = _field
	return nil
}
func (p *RegisterResourceReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *RegisterResourceReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RegisterResourceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RegisterResourceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RegisterResourceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("register_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RegisterList)); err != nil {
		return err
	}
	for _, v := range p.RegisterList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RegisterResourceReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *RegisterResourceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterResourceReq(%+v)", *p)

}

type RegisterResourceResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewRegisterResourceResp() *RegisterResourceResp {
	return &RegisterResourceResp{}
}

func (p *RegisterResourceResp) InitDefault() {
}

var RegisterResourceResp_BaseResp_DEFAULT *base.BaseResp

func (p *RegisterResourceResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return RegisterResourceResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *RegisterResourceResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_RegisterResourceResp = map[int16]string{
	255: "BaseResp",
}

func (p *RegisterResourceResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *RegisterResourceResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RegisterResourceResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RegisterResourceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RegisterResourceResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *RegisterResourceResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RegisterResourceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("RegisterResourceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RegisterResourceResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *RegisterResourceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterResourceResp(%+v)", *p)

}

type CompensateReq struct {
	ResourceInfo *ResourceInfo `thrift:"resource_info,1,optional" frugal:"1,optional,ResourceInfo" json:"resource_info,omitempty"`
	ResourceID   *int64        `thrift:"resource_id,2,optional" frugal:"2,optional,i64" json:"resource_id,omitempty"`
	Base         *base.Base    `thrift:"Base,255,optional" frugal:"255,optional,base.Base" json:"Base,omitempty"`
}

func NewCompensateReq() *CompensateReq {
	return &CompensateReq{}
}

func (p *CompensateReq) InitDefault() {
}

var CompensateReq_ResourceInfo_DEFAULT *ResourceInfo

func (p *CompensateReq) GetResourceInfo() (v *ResourceInfo) {
	if !p.IsSetResourceInfo() {
		return CompensateReq_ResourceInfo_DEFAULT
	}
	return p.ResourceInfo
}

var CompensateReq_ResourceID_DEFAULT int64

func (p *CompensateReq) GetResourceID() (v int64) {
	if !p.IsSetResourceID() {
		return CompensateReq_ResourceID_DEFAULT
	}
	return *p.ResourceID
}

var CompensateReq_Base_DEFAULT *base.Base

func (p *CompensateReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CompensateReq_Base_DEFAULT
	}
	return p.Base
}
func (p *CompensateReq) SetResourceInfo(val *ResourceInfo) {
	p.ResourceInfo = val
}
func (p *CompensateReq) SetResourceID(val *int64) {
	p.ResourceID = val
}
func (p *CompensateReq) SetBase(val *base.Base) {
	p.Base = val
}

var fieldIDToName_CompensateReq = map[int16]string{
	1:   "resource_info",
	2:   "resource_id",
	255: "Base",
}

func (p *CompensateReq) IsSetResourceInfo() bool {
	return p.ResourceInfo != nil
}

func (p *CompensateReq) IsSetResourceID() bool {
	return p.ResourceID != nil
}

func (p *CompensateReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *CompensateReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CompensateReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CompensateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CompensateReq) ReadField1(iprot thrift.TProtocol) error {
	_field := NewResourceInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ResourceInfo = _field
	return nil
}
func (p *CompensateReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ResourceID = _field
	return nil
}
func (p *CompensateReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CompensateReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CompensateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CompensateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CompensateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetResourceInfo() {
		if err = oprot.WriteFieldBegin("resource_info", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ResourceInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CompensateReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetResourceID() {
		if err = oprot.WriteFieldBegin("resource_id", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ResourceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CompensateReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CompensateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompensateReq(%+v)", *p)

}

type CompensateResp struct {
	BaseResp *base.BaseResp `thrift:"BaseResp,255,optional" frugal:"255,optional,base.BaseResp" json:"BaseResp,omitempty"`
}

func NewCompensateResp() *CompensateResp {
	return &CompensateResp{}
}

func (p *CompensateResp) InitDefault() {
}

var CompensateResp_BaseResp_DEFAULT *base.BaseResp

func (p *CompensateResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CompensateResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *CompensateResp) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_CompensateResp = map[int16]string{
	255: "BaseResp",
}

func (p *CompensateResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CompensateResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CompensateResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CompensateResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CompensateResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CompensateResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CompensateResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CompensateResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CompensateResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CompensateResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompensateResp(%+v)", *p)

}

type ResourceCompensateService interface {
	RegisterResource(ctx context.Context, req *RegisterResourceReq) (r *RegisterResourceResp, err error)

	CompensateResource(ctx context.Context, req *CompensateReq) (r *CompensateResp, err error)
}

type ResourceCompensateServiceRegisterResourceArgs struct {
	Req *RegisterResourceReq `thrift:"req,1" frugal:"1,default,RegisterResourceReq" json:"req"`
}

func NewResourceCompensateServiceRegisterResourceArgs() *ResourceCompensateServiceRegisterResourceArgs {
	return &ResourceCompensateServiceRegisterResourceArgs{}
}

func (p *ResourceCompensateServiceRegisterResourceArgs) InitDefault() {
}

var ResourceCompensateServiceRegisterResourceArgs_Req_DEFAULT *RegisterResourceReq

func (p *ResourceCompensateServiceRegisterResourceArgs) GetReq() (v *RegisterResourceReq) {
	if !p.IsSetReq() {
		return ResourceCompensateServiceRegisterResourceArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *ResourceCompensateServiceRegisterResourceArgs) SetReq(val *RegisterResourceReq) {
	p.Req = val
}

var fieldIDToName_ResourceCompensateServiceRegisterResourceArgs = map[int16]string{
	1: "req",
}

func (p *ResourceCompensateServiceRegisterResourceArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *ResourceCompensateServiceRegisterResourceArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ResourceCompensateServiceRegisterResourceArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCompensateServiceRegisterResourceArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceCompensateServiceRegisterResourceArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewRegisterResourceReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *ResourceCompensateServiceRegisterResourceArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ResourceCompensateServiceRegisterResourceArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("RegisterResource_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCompensateServiceRegisterResourceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResourceCompensateServiceRegisterResourceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCompensateServiceRegisterResourceArgs(%+v)", *p)

}

type ResourceCompensateServiceRegisterResourceResult struct {
	Success *RegisterResourceResp `thrift:"success,0,optional" frugal:"0,optional,RegisterResourceResp" json:"success,omitempty"`
}

func NewResourceCompensateServiceRegisterResourceResult() *ResourceCompensateServiceRegisterResourceResult {
	return &ResourceCompensateServiceRegisterResourceResult{}
}

func (p *ResourceCompensateServiceRegisterResourceResult) InitDefault() {
}

var ResourceCompensateServiceRegisterResourceResult_Success_DEFAULT *RegisterResourceResp

func (p *ResourceCompensateServiceRegisterResourceResult) GetSuccess() (v *RegisterResourceResp) {
	if !p.IsSetSuccess() {
		return ResourceCompensateServiceRegisterResourceResult_Success_DEFAULT
	}
	return p.Success
}
func (p *ResourceCompensateServiceRegisterResourceResult) SetSuccess(x interface{}) {
	p.Success = x.(*RegisterResourceResp)
}

var fieldIDToName_ResourceCompensateServiceRegisterResourceResult = map[int16]string{
	0: "success",
}

func (p *ResourceCompensateServiceRegisterResourceResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ResourceCompensateServiceRegisterResourceResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ResourceCompensateServiceRegisterResourceResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCompensateServiceRegisterResourceResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceCompensateServiceRegisterResourceResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewRegisterResourceResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ResourceCompensateServiceRegisterResourceResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ResourceCompensateServiceRegisterResourceResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("RegisterResource_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCompensateServiceRegisterResourceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ResourceCompensateServiceRegisterResourceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCompensateServiceRegisterResourceResult(%+v)", *p)

}

type ResourceCompensateServiceCompensateResourceArgs struct {
	Req *CompensateReq `thrift:"req,1" frugal:"1,default,CompensateReq" json:"req"`
}

func NewResourceCompensateServiceCompensateResourceArgs() *ResourceCompensateServiceCompensateResourceArgs {
	return &ResourceCompensateServiceCompensateResourceArgs{}
}

func (p *ResourceCompensateServiceCompensateResourceArgs) InitDefault() {
}

var ResourceCompensateServiceCompensateResourceArgs_Req_DEFAULT *CompensateReq

func (p *ResourceCompensateServiceCompensateResourceArgs) GetReq() (v *CompensateReq) {
	if !p.IsSetReq() {
		return ResourceCompensateServiceCompensateResourceArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *ResourceCompensateServiceCompensateResourceArgs) SetReq(val *CompensateReq) {
	p.Req = val
}

var fieldIDToName_ResourceCompensateServiceCompensateResourceArgs = map[int16]string{
	1: "req",
}

func (p *ResourceCompensateServiceCompensateResourceArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *ResourceCompensateServiceCompensateResourceArgs) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ResourceCompensateServiceCompensateResourceArgs")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCompensateServiceCompensateResourceArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceCompensateServiceCompensateResourceArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCompensateReq()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *ResourceCompensateServiceCompensateResourceArgs) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ResourceCompensateServiceCompensateResourceArgs")

	var fieldId int16
	if err = oprot.WriteStructBegin("CompensateResource_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCompensateServiceCompensateResourceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResourceCompensateServiceCompensateResourceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCompensateServiceCompensateResourceArgs(%+v)", *p)

}

type ResourceCompensateServiceCompensateResourceResult struct {
	Success *CompensateResp `thrift:"success,0,optional" frugal:"0,optional,CompensateResp" json:"success,omitempty"`
}

func NewResourceCompensateServiceCompensateResourceResult() *ResourceCompensateServiceCompensateResourceResult {
	return &ResourceCompensateServiceCompensateResourceResult{}
}

func (p *ResourceCompensateServiceCompensateResourceResult) InitDefault() {
}

var ResourceCompensateServiceCompensateResourceResult_Success_DEFAULT *CompensateResp

func (p *ResourceCompensateServiceCompensateResourceResult) GetSuccess() (v *CompensateResp) {
	if !p.IsSetSuccess() {
		return ResourceCompensateServiceCompensateResourceResult_Success_DEFAULT
	}
	return p.Success
}
func (p *ResourceCompensateServiceCompensateResourceResult) SetSuccess(x interface{}) {
	p.Success = x.(*CompensateResp)
}

var fieldIDToName_ResourceCompensateServiceCompensateResourceResult = map[int16]string{
	0: "success",
}

func (p *ResourceCompensateServiceCompensateResourceResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ResourceCompensateServiceCompensateResourceResult) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ResourceCompensateServiceCompensateResourceResult")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCompensateServiceCompensateResourceResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceCompensateServiceCompensateResourceResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCompensateResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ResourceCompensateServiceCompensateResourceResult) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ResourceCompensateServiceCompensateResourceResult")

	var fieldId int16
	if err = oprot.WriteStructBegin("CompensateResource_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCompensateServiceCompensateResourceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ResourceCompensateServiceCompensateResourceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCompensateServiceCompensateResourceResult(%+v)", *p)

}
