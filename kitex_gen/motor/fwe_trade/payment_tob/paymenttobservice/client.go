// Code generated by Kitex v1.20.3. DO NOT EDIT.

package paymenttobservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	payment_tob "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment_tob"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	Recharge(ctx context.Context, req *payment_tob.RechargeReq, callOptions ...callopt.Option) (r *payment_tob.RechargeRsp, err error)
	Withdraw(ctx context.Context, req *payment_tob.WithdrawReq, callOptions ...callopt.Option) (r *payment_tob.WithdrawRsp, err error)
	Pay(ctx context.Context, req *payment_tob.PayReq, callOptions ...callopt.Option) (r *payment_tob.PayRsp, err error)
	Refund(ctx context.Context, req *payment_tob.RefundReq, callOptions ...callopt.Option) (r *payment_tob.RefundRsp, err error)
	Settle(ctx context.Context, req *payment_tob.SettleReq, callOptions ...callopt.Option) (r *payment_tob.SettleRsp, err error)
	Reconciliation(ctx context.Context, req *payment_tob.ReconciliationReq, callOptions ...callopt.Option) (r *payment_tob.ReconciliationRsp, err error)
	ReconciliationData(ctx context.Context, req *payment_tob.ReconciliationDataReq, callOptions ...callopt.Option) (r *payment_tob.ReconciliationDataRsp, err error)
	TianShuPush(ctx context.Context, req *payment_tob.TianShuPushReq, callOptions ...callopt.Option) (r *payment_tob.TianShuPushRsp, err error)
	GetBalanceInfo(ctx context.Context, req *payment_tob.GetBalanceInfoReq, callOptions ...callopt.Option) (r *payment_tob.GetBalanceInfoRsp, err error)
	QueryBalanceFlow(ctx context.Context, req *payment_tob.QueryBalanceFlowReq, callOptions ...callopt.Option) (r *payment_tob.QueryBalanceFlowRsp, err error)
	QueryCode(ctx context.Context, req *payment_tob.QueryCodeReq, callOptions ...callopt.Option) (r *payment_tob.QueryCodeRsp, err error)
	MGetPayList(ctx context.Context, req *payment_tob.MGetPayListReq, callOptions ...callopt.Option) (r *payment_tob.MGetPayListRsp, err error)
	JstRawHTTP(ctx context.Context, req *payment_tob.JstRawHTTPReq, callOptions ...callopt.Option) (r *payment_tob.JstRawHTTPRsp, err error)
	AsyncCall(ctx context.Context, req *payment_tob.AsyncCallReq, callOptions ...callopt.Option) (r *payment_tob.AsyncCallRsp, err error)
	PaymentTobBizCall(ctx context.Context, req *payment_tob.PaymentTobBizCallReq, callOptions ...callopt.Option) (r *payment_tob.PaymentTobBizCallRsp, err error)
	DownloadFile(ctx context.Context, req *payment_tob.DownloadFileReq, callOptions ...callopt.Option) (r *payment_tob.DownloadFileRsp, err error)
	UnionNotifyCashPay(ctx context.Context, req *payment_tob.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment_tob.UnionNotifyResponse, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kPaymentToBServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kPaymentToBServiceClient struct {
	*kClient
}

func (p *kPaymentToBServiceClient) Recharge(ctx context.Context, req *payment_tob.RechargeReq, callOptions ...callopt.Option) (r *payment_tob.RechargeRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Recharge(ctx, req)
}

func (p *kPaymentToBServiceClient) Withdraw(ctx context.Context, req *payment_tob.WithdrawReq, callOptions ...callopt.Option) (r *payment_tob.WithdrawRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Withdraw(ctx, req)
}

func (p *kPaymentToBServiceClient) Pay(ctx context.Context, req *payment_tob.PayReq, callOptions ...callopt.Option) (r *payment_tob.PayRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Pay(ctx, req)
}

func (p *kPaymentToBServiceClient) Refund(ctx context.Context, req *payment_tob.RefundReq, callOptions ...callopt.Option) (r *payment_tob.RefundRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Refund(ctx, req)
}

func (p *kPaymentToBServiceClient) Settle(ctx context.Context, req *payment_tob.SettleReq, callOptions ...callopt.Option) (r *payment_tob.SettleRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Settle(ctx, req)
}

func (p *kPaymentToBServiceClient) Reconciliation(ctx context.Context, req *payment_tob.ReconciliationReq, callOptions ...callopt.Option) (r *payment_tob.ReconciliationRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Reconciliation(ctx, req)
}

func (p *kPaymentToBServiceClient) ReconciliationData(ctx context.Context, req *payment_tob.ReconciliationDataReq, callOptions ...callopt.Option) (r *payment_tob.ReconciliationDataRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ReconciliationData(ctx, req)
}

func (p *kPaymentToBServiceClient) TianShuPush(ctx context.Context, req *payment_tob.TianShuPushReq, callOptions ...callopt.Option) (r *payment_tob.TianShuPushRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.TianShuPush(ctx, req)
}

func (p *kPaymentToBServiceClient) GetBalanceInfo(ctx context.Context, req *payment_tob.GetBalanceInfoReq, callOptions ...callopt.Option) (r *payment_tob.GetBalanceInfoRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetBalanceInfo(ctx, req)
}

func (p *kPaymentToBServiceClient) QueryBalanceFlow(ctx context.Context, req *payment_tob.QueryBalanceFlowReq, callOptions ...callopt.Option) (r *payment_tob.QueryBalanceFlowRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryBalanceFlow(ctx, req)
}

func (p *kPaymentToBServiceClient) QueryCode(ctx context.Context, req *payment_tob.QueryCodeReq, callOptions ...callopt.Option) (r *payment_tob.QueryCodeRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryCode(ctx, req)
}

func (p *kPaymentToBServiceClient) MGetPayList(ctx context.Context, req *payment_tob.MGetPayListReq, callOptions ...callopt.Option) (r *payment_tob.MGetPayListRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetPayList(ctx, req)
}

func (p *kPaymentToBServiceClient) JstRawHTTP(ctx context.Context, req *payment_tob.JstRawHTTPReq, callOptions ...callopt.Option) (r *payment_tob.JstRawHTTPRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.JstRawHTTP(ctx, req)
}

func (p *kPaymentToBServiceClient) AsyncCall(ctx context.Context, req *payment_tob.AsyncCallReq, callOptions ...callopt.Option) (r *payment_tob.AsyncCallRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AsyncCall(ctx, req)
}

func (p *kPaymentToBServiceClient) PaymentTobBizCall(ctx context.Context, req *payment_tob.PaymentTobBizCallReq, callOptions ...callopt.Option) (r *payment_tob.PaymentTobBizCallRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PaymentTobBizCall(ctx, req)
}

func (p *kPaymentToBServiceClient) DownloadFile(ctx context.Context, req *payment_tob.DownloadFileReq, callOptions ...callopt.Option) (r *payment_tob.DownloadFileRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DownloadFile(ctx, req)
}

func (p *kPaymentToBServiceClient) UnionNotifyCashPay(ctx context.Context, req *payment_tob.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment_tob.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyCashPay(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kPaymentToBServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
