// Code generated by Kitex v1.20.3. DO NOT EDIT.

package paymenttobservice

import (
	client "code.byted.org/kite/kitex/client"
	payment_tob "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment_tob"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"Recharge": kitex.NewMethodInfo(
		rechargeHandler,
		newPaymentToBServiceRechargeArgs,
		newPaymentToBServiceRechargeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Withdraw": kitex.NewMethodInfo(
		withdrawHandler,
		newPaymentToBServiceWithdrawArgs,
		newPaymentToBServiceWithdrawResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Pay": kitex.NewMethodInfo(
		payHandler,
		newPaymentToBServicePayArgs,
		newPaymentToBServicePayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Refund": kitex.NewMethodInfo(
		refundHandler,
		newPaymentToBServiceRefundArgs,
		newPaymentToBServiceRefundResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Settle": kitex.NewMethodInfo(
		settleHandler,
		newPaymentToBServiceSettleArgs,
		newPaymentToBServiceSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Reconciliation": kitex.NewMethodInfo(
		reconciliationHandler,
		newPaymentToBServiceReconciliationArgs,
		newPaymentToBServiceReconciliationResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ReconciliationData": kitex.NewMethodInfo(
		reconciliationDataHandler,
		newPaymentToBServiceReconciliationDataArgs,
		newPaymentToBServiceReconciliationDataResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"TianShuPush": kitex.NewMethodInfo(
		tianShuPushHandler,
		newPaymentToBServiceTianShuPushArgs,
		newPaymentToBServiceTianShuPushResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetBalanceInfo": kitex.NewMethodInfo(
		getBalanceInfoHandler,
		newPaymentToBServiceGetBalanceInfoArgs,
		newPaymentToBServiceGetBalanceInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryBalanceFlow": kitex.NewMethodInfo(
		queryBalanceFlowHandler,
		newPaymentToBServiceQueryBalanceFlowArgs,
		newPaymentToBServiceQueryBalanceFlowResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryCode": kitex.NewMethodInfo(
		queryCodeHandler,
		newPaymentToBServiceQueryCodeArgs,
		newPaymentToBServiceQueryCodeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetPayList": kitex.NewMethodInfo(
		mGetPayListHandler,
		newPaymentToBServiceMGetPayListArgs,
		newPaymentToBServiceMGetPayListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"JstRawHttp": kitex.NewMethodInfo(
		jstRawHTTPHandler,
		newPaymentToBServiceJstRawHTTPArgs,
		newPaymentToBServiceJstRawHTTPResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AsyncCall": kitex.NewMethodInfo(
		asyncCallHandler,
		newPaymentToBServiceAsyncCallArgs,
		newPaymentToBServiceAsyncCallResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PaymentTobBizCall": kitex.NewMethodInfo(
		paymentTobBizCallHandler,
		newPaymentToBServicePaymentTobBizCallArgs,
		newPaymentToBServicePaymentTobBizCallResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DownloadFile": kitex.NewMethodInfo(
		downloadFileHandler,
		newPaymentToBServiceDownloadFileArgs,
		newPaymentToBServiceDownloadFileResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyCashPay": kitex.NewMethodInfo(
		unionNotifyCashPayHandler,
		newPaymentToBServiceUnionNotifyCashPayArgs,
		newPaymentToBServiceUnionNotifyCashPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	paymentToBServiceServiceInfo                = NewServiceInfo()
	paymentToBServiceServiceInfoForClient       = NewServiceInfoForClient()
	paymentToBServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return paymentToBServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return paymentToBServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return paymentToBServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "PaymentToBService"
	handlerType := (*payment_tob.PaymentToBService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "payment_tob",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func rechargeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceRechargeArgs)
	realResult := result.(*payment_tob.PaymentToBServiceRechargeResult)
	success, err := handler.(payment_tob.PaymentToBService).Recharge(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceRechargeArgs() interface{} {
	return payment_tob.NewPaymentToBServiceRechargeArgs()
}

func newPaymentToBServiceRechargeResult() interface{} {
	return payment_tob.NewPaymentToBServiceRechargeResult()
}

func withdrawHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceWithdrawArgs)
	realResult := result.(*payment_tob.PaymentToBServiceWithdrawResult)
	success, err := handler.(payment_tob.PaymentToBService).Withdraw(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceWithdrawArgs() interface{} {
	return payment_tob.NewPaymentToBServiceWithdrawArgs()
}

func newPaymentToBServiceWithdrawResult() interface{} {
	return payment_tob.NewPaymentToBServiceWithdrawResult()
}

func payHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServicePayArgs)
	realResult := result.(*payment_tob.PaymentToBServicePayResult)
	success, err := handler.(payment_tob.PaymentToBService).Pay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServicePayArgs() interface{} {
	return payment_tob.NewPaymentToBServicePayArgs()
}

func newPaymentToBServicePayResult() interface{} {
	return payment_tob.NewPaymentToBServicePayResult()
}

func refundHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceRefundArgs)
	realResult := result.(*payment_tob.PaymentToBServiceRefundResult)
	success, err := handler.(payment_tob.PaymentToBService).Refund(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceRefundArgs() interface{} {
	return payment_tob.NewPaymentToBServiceRefundArgs()
}

func newPaymentToBServiceRefundResult() interface{} {
	return payment_tob.NewPaymentToBServiceRefundResult()
}

func settleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceSettleArgs)
	realResult := result.(*payment_tob.PaymentToBServiceSettleResult)
	success, err := handler.(payment_tob.PaymentToBService).Settle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceSettleArgs() interface{} {
	return payment_tob.NewPaymentToBServiceSettleArgs()
}

func newPaymentToBServiceSettleResult() interface{} {
	return payment_tob.NewPaymentToBServiceSettleResult()
}

func reconciliationHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceReconciliationArgs)
	realResult := result.(*payment_tob.PaymentToBServiceReconciliationResult)
	success, err := handler.(payment_tob.PaymentToBService).Reconciliation(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceReconciliationArgs() interface{} {
	return payment_tob.NewPaymentToBServiceReconciliationArgs()
}

func newPaymentToBServiceReconciliationResult() interface{} {
	return payment_tob.NewPaymentToBServiceReconciliationResult()
}

func reconciliationDataHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceReconciliationDataArgs)
	realResult := result.(*payment_tob.PaymentToBServiceReconciliationDataResult)
	success, err := handler.(payment_tob.PaymentToBService).ReconciliationData(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceReconciliationDataArgs() interface{} {
	return payment_tob.NewPaymentToBServiceReconciliationDataArgs()
}

func newPaymentToBServiceReconciliationDataResult() interface{} {
	return payment_tob.NewPaymentToBServiceReconciliationDataResult()
}

func tianShuPushHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceTianShuPushArgs)
	realResult := result.(*payment_tob.PaymentToBServiceTianShuPushResult)
	success, err := handler.(payment_tob.PaymentToBService).TianShuPush(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceTianShuPushArgs() interface{} {
	return payment_tob.NewPaymentToBServiceTianShuPushArgs()
}

func newPaymentToBServiceTianShuPushResult() interface{} {
	return payment_tob.NewPaymentToBServiceTianShuPushResult()
}

func getBalanceInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceGetBalanceInfoArgs)
	realResult := result.(*payment_tob.PaymentToBServiceGetBalanceInfoResult)
	success, err := handler.(payment_tob.PaymentToBService).GetBalanceInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceGetBalanceInfoArgs() interface{} {
	return payment_tob.NewPaymentToBServiceGetBalanceInfoArgs()
}

func newPaymentToBServiceGetBalanceInfoResult() interface{} {
	return payment_tob.NewPaymentToBServiceGetBalanceInfoResult()
}

func queryBalanceFlowHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceQueryBalanceFlowArgs)
	realResult := result.(*payment_tob.PaymentToBServiceQueryBalanceFlowResult)
	success, err := handler.(payment_tob.PaymentToBService).QueryBalanceFlow(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceQueryBalanceFlowArgs() interface{} {
	return payment_tob.NewPaymentToBServiceQueryBalanceFlowArgs()
}

func newPaymentToBServiceQueryBalanceFlowResult() interface{} {
	return payment_tob.NewPaymentToBServiceQueryBalanceFlowResult()
}

func queryCodeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceQueryCodeArgs)
	realResult := result.(*payment_tob.PaymentToBServiceQueryCodeResult)
	success, err := handler.(payment_tob.PaymentToBService).QueryCode(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceQueryCodeArgs() interface{} {
	return payment_tob.NewPaymentToBServiceQueryCodeArgs()
}

func newPaymentToBServiceQueryCodeResult() interface{} {
	return payment_tob.NewPaymentToBServiceQueryCodeResult()
}

func mGetPayListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceMGetPayListArgs)
	realResult := result.(*payment_tob.PaymentToBServiceMGetPayListResult)
	success, err := handler.(payment_tob.PaymentToBService).MGetPayList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceMGetPayListArgs() interface{} {
	return payment_tob.NewPaymentToBServiceMGetPayListArgs()
}

func newPaymentToBServiceMGetPayListResult() interface{} {
	return payment_tob.NewPaymentToBServiceMGetPayListResult()
}

func jstRawHTTPHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceJstRawHTTPArgs)
	realResult := result.(*payment_tob.PaymentToBServiceJstRawHTTPResult)
	success, err := handler.(payment_tob.PaymentToBService).JstRawHTTP(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceJstRawHTTPArgs() interface{} {
	return payment_tob.NewPaymentToBServiceJstRawHTTPArgs()
}

func newPaymentToBServiceJstRawHTTPResult() interface{} {
	return payment_tob.NewPaymentToBServiceJstRawHTTPResult()
}

func asyncCallHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceAsyncCallArgs)
	realResult := result.(*payment_tob.PaymentToBServiceAsyncCallResult)
	success, err := handler.(payment_tob.PaymentToBService).AsyncCall(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceAsyncCallArgs() interface{} {
	return payment_tob.NewPaymentToBServiceAsyncCallArgs()
}

func newPaymentToBServiceAsyncCallResult() interface{} {
	return payment_tob.NewPaymentToBServiceAsyncCallResult()
}

func paymentTobBizCallHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServicePaymentTobBizCallArgs)
	realResult := result.(*payment_tob.PaymentToBServicePaymentTobBizCallResult)
	success, err := handler.(payment_tob.PaymentToBService).PaymentTobBizCall(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServicePaymentTobBizCallArgs() interface{} {
	return payment_tob.NewPaymentToBServicePaymentTobBizCallArgs()
}

func newPaymentToBServicePaymentTobBizCallResult() interface{} {
	return payment_tob.NewPaymentToBServicePaymentTobBizCallResult()
}

func downloadFileHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceDownloadFileArgs)
	realResult := result.(*payment_tob.PaymentToBServiceDownloadFileResult)
	success, err := handler.(payment_tob.PaymentToBService).DownloadFile(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceDownloadFileArgs() interface{} {
	return payment_tob.NewPaymentToBServiceDownloadFileArgs()
}

func newPaymentToBServiceDownloadFileResult() interface{} {
	return payment_tob.NewPaymentToBServiceDownloadFileResult()
}

func unionNotifyCashPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment_tob.PaymentToBServiceUnionNotifyCashPayArgs)
	realResult := result.(*payment_tob.PaymentToBServiceUnionNotifyCashPayResult)
	success, err := handler.(payment_tob.PaymentToBService).UnionNotifyCashPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentToBServiceUnionNotifyCashPayArgs() interface{} {
	return payment_tob.NewPaymentToBServiceUnionNotifyCashPayArgs()
}

func newPaymentToBServiceUnionNotifyCashPayResult() interface{} {
	return payment_tob.NewPaymentToBServiceUnionNotifyCashPayResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) Recharge(ctx context.Context, req *payment_tob.RechargeReq) (r *payment_tob.RechargeRsp, err error) {
	var _args payment_tob.PaymentToBServiceRechargeArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceRechargeResult
	if err = p.c.Call(ctx, "Recharge", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Withdraw(ctx context.Context, req *payment_tob.WithdrawReq) (r *payment_tob.WithdrawRsp, err error) {
	var _args payment_tob.PaymentToBServiceWithdrawArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceWithdrawResult
	if err = p.c.Call(ctx, "Withdraw", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Pay(ctx context.Context, req *payment_tob.PayReq) (r *payment_tob.PayRsp, err error) {
	var _args payment_tob.PaymentToBServicePayArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServicePayResult
	if err = p.c.Call(ctx, "Pay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Refund(ctx context.Context, req *payment_tob.RefundReq) (r *payment_tob.RefundRsp, err error) {
	var _args payment_tob.PaymentToBServiceRefundArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceRefundResult
	if err = p.c.Call(ctx, "Refund", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Settle(ctx context.Context, req *payment_tob.SettleReq) (r *payment_tob.SettleRsp, err error) {
	var _args payment_tob.PaymentToBServiceSettleArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceSettleResult
	if err = p.c.Call(ctx, "Settle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Reconciliation(ctx context.Context, req *payment_tob.ReconciliationReq) (r *payment_tob.ReconciliationRsp, err error) {
	var _args payment_tob.PaymentToBServiceReconciliationArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceReconciliationResult
	if err = p.c.Call(ctx, "Reconciliation", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ReconciliationData(ctx context.Context, req *payment_tob.ReconciliationDataReq) (r *payment_tob.ReconciliationDataRsp, err error) {
	var _args payment_tob.PaymentToBServiceReconciliationDataArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceReconciliationDataResult
	if err = p.c.Call(ctx, "ReconciliationData", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) TianShuPush(ctx context.Context, req *payment_tob.TianShuPushReq) (r *payment_tob.TianShuPushRsp, err error) {
	var _args payment_tob.PaymentToBServiceTianShuPushArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceTianShuPushResult
	if err = p.c.Call(ctx, "TianShuPush", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetBalanceInfo(ctx context.Context, req *payment_tob.GetBalanceInfoReq) (r *payment_tob.GetBalanceInfoRsp, err error) {
	var _args payment_tob.PaymentToBServiceGetBalanceInfoArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceGetBalanceInfoResult
	if err = p.c.Call(ctx, "GetBalanceInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryBalanceFlow(ctx context.Context, req *payment_tob.QueryBalanceFlowReq) (r *payment_tob.QueryBalanceFlowRsp, err error) {
	var _args payment_tob.PaymentToBServiceQueryBalanceFlowArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceQueryBalanceFlowResult
	if err = p.c.Call(ctx, "QueryBalanceFlow", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryCode(ctx context.Context, req *payment_tob.QueryCodeReq) (r *payment_tob.QueryCodeRsp, err error) {
	var _args payment_tob.PaymentToBServiceQueryCodeArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceQueryCodeResult
	if err = p.c.Call(ctx, "QueryCode", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetPayList(ctx context.Context, req *payment_tob.MGetPayListReq) (r *payment_tob.MGetPayListRsp, err error) {
	var _args payment_tob.PaymentToBServiceMGetPayListArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceMGetPayListResult
	if err = p.c.Call(ctx, "MGetPayList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) JstRawHTTP(ctx context.Context, req *payment_tob.JstRawHTTPReq) (r *payment_tob.JstRawHTTPRsp, err error) {
	var _args payment_tob.PaymentToBServiceJstRawHTTPArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceJstRawHTTPResult
	if err = p.c.Call(ctx, "JstRawHttp", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AsyncCall(ctx context.Context, req *payment_tob.AsyncCallReq) (r *payment_tob.AsyncCallRsp, err error) {
	var _args payment_tob.PaymentToBServiceAsyncCallArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceAsyncCallResult
	if err = p.c.Call(ctx, "AsyncCall", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PaymentTobBizCall(ctx context.Context, req *payment_tob.PaymentTobBizCallReq) (r *payment_tob.PaymentTobBizCallRsp, err error) {
	var _args payment_tob.PaymentToBServicePaymentTobBizCallArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServicePaymentTobBizCallResult
	if err = p.c.Call(ctx, "PaymentTobBizCall", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DownloadFile(ctx context.Context, req *payment_tob.DownloadFileReq) (r *payment_tob.DownloadFileRsp, err error) {
	var _args payment_tob.PaymentToBServiceDownloadFileArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceDownloadFileResult
	if err = p.c.Call(ctx, "DownloadFile", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyCashPay(ctx context.Context, req *payment_tob.UnionNotifyRequest) (r *payment_tob.UnionNotifyResponse, err error) {
	var _args payment_tob.PaymentToBServiceUnionNotifyCashPayArgs
	_args.Req = req
	var _result payment_tob.PaymentToBServiceUnionNotifyCashPayResult
	if err = p.c.Call(ctx, "UnionNotifyCashPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
