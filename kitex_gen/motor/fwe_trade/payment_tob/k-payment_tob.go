// Code generated by Kitex v1.20.3. DO NOT EDIT.

package payment_tob

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"

	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
)

var (
	_ = base.KitexUnusedProtection
	_ = fwe_trade_common.KitexUnusedProtection
	_ = tenant_base.KitexUnusedProtection
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *SubMemberInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SubMemberInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SubMemberInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FweAccountID = _field
	return offset, nil
}

func (p *SubMemberInfo) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field JstMemberType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = JstMemberType(v)
	}
	p.UIDType = _field
	return offset, nil
}

func (p *SubMemberInfo) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SubAcctNo = _field
	return offset, nil
}

func (p *SubMemberInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SubMemberInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SubMemberInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SubMemberInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FweAccountID)
	return offset
}

func (p *SubMemberInfo) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.UIDType))
	return offset
}

func (p *SubMemberInfo) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SubAcctNo)
	return offset
}

func (p *SubMemberInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FweAccountID)
	return l
}

func (p *SubMemberInfo) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SubMemberInfo) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SubAcctNo)
	return l
}

func (p *SubMemberInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*SubMemberInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.FweAccountID != "" {
		p.FweAccountID = kutils.StringDeepCopy(src.FweAccountID)
	}

	p.UIDType = src.UIDType

	if src.SubAcctNo != "" {
		p.SubAcctNo = kutils.StringDeepCopy(src.SubAcctNo)
	}

	return nil
}

func (p *CaijingParam) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 19:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField19(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CaijingParam[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CaijingParam) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *CaijingParam) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AppID = _field
	return offset, nil
}

func (p *CaijingParam) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *fwe_trade_common.CashierDeskType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := fwe_trade_common.CashierDeskType(v)
		_field = &tmp
	}
	p.CashierDeskType = _field
	return offset, nil
}

func (p *CaijingParam) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field *fwe_trade_common.OSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := fwe_trade_common.OSType(v)
		_field = &tmp
	}
	p.OsType = _field
	return offset, nil
}

func (p *CaijingParam) FastReadField16(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ExpireTime = _field
	return offset, nil
}

func (p *CaijingParam) FastReadField17(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RedirectURL = _field
	return offset, nil
}

func (p *CaijingParam) FastReadField18(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.IPAddress = _field
	return offset, nil
}

func (p *CaijingParam) FastReadField19(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]fwe_trade_common.PayLimitType, 0, size)
	for i := 0; i < size; i++ {
		var _elem fwe_trade_common.PayLimitType
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_elem = fwe_trade_common.PayLimitType(v)
		}

		_field = append(_field, _elem)
	}
	p.PayLimitList = _field
	return offset, nil
}

func (p *CaijingParam) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CaijingParam) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
		offset += p.fastWriteField19(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CaijingParam) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
		l += p.field18Length()
		l += p.field19Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CaijingParam) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *CaijingParam) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AppID)
	return offset
}

func (p *CaijingParam) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCashierDeskType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 14)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.CashierDeskType))
	}
	return offset
}

func (p *CaijingParam) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOsType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 15)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.OsType))
	}
	return offset
}

func (p *CaijingParam) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetExpireTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 16)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.ExpireTime)
	}
	return offset
}

func (p *CaijingParam) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRedirectURL() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 17)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RedirectURL)
	}
	return offset
}

func (p *CaijingParam) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetIPAddress() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 18)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.IPAddress)
	}
	return offset
}

func (p *CaijingParam) fastWriteField19(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPayLimitList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 19)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.PayLimitList {
			length++
			offset += thrift.Binary.WriteI32(buf[offset:], int32(v))
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I32, length)
	}
	return offset
}

func (p *CaijingParam) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *CaijingParam) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AppID)
	return l
}

func (p *CaijingParam) field14Length() int {
	l := 0
	if p.IsSetCashierDeskType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CaijingParam) field15Length() int {
	l := 0
	if p.IsSetOsType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CaijingParam) field16Length() int {
	l := 0
	if p.IsSetExpireTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *CaijingParam) field17Length() int {
	l := 0
	if p.IsSetRedirectURL() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RedirectURL)
	}
	return l
}

func (p *CaijingParam) field18Length() int {
	l := 0
	if p.IsSetIPAddress() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.IPAddress)
	}
	return l
}

func (p *CaijingParam) field19Length() int {
	l := 0
	if p.IsSetPayLimitList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.PayLimitList {
			_ = v
			l += thrift.Binary.I32Length()
		}
	}
	return l
}

func (p *CaijingParam) DeepCopy(s interface{}) error {
	src, ok := s.(*CaijingParam)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.AppID != "" {
		p.AppID = kutils.StringDeepCopy(src.AppID)
	}

	if src.CashierDeskType != nil {
		tmp := *src.CashierDeskType
		p.CashierDeskType = &tmp
	}

	if src.OsType != nil {
		tmp := *src.OsType
		p.OsType = &tmp
	}

	if src.ExpireTime != nil {
		tmp := *src.ExpireTime
		p.ExpireTime = &tmp
	}

	if src.RedirectURL != nil {
		var tmp string
		if *src.RedirectURL != "" {
			tmp = kutils.StringDeepCopy(*src.RedirectURL)
		}
		p.RedirectURL = &tmp
	}

	if src.IPAddress != nil {
		var tmp string
		if *src.IPAddress != "" {
			tmp = kutils.StringDeepCopy(*src.IPAddress)
		}
		p.IPAddress = &tmp
	}

	if src.PayLimitList != nil {
		p.PayLimitList = make([]fwe_trade_common.PayLimitType, 0, len(src.PayLimitList))
		for _, elem := range src.PayLimitList {
			var _elem fwe_trade_common.PayLimitType
			_elem = elem
			p.PayLimitList = append(p.PayLimitList, _elem)
		}
	}

	return nil
}

func (p *RechargeReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 251:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField251(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RechargeReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RechargeReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *RechargeReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field Channel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Channel(v)
	}
	p.Channel = _field
	return offset, nil
}

func (p *RechargeReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *RechargeReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RechargeOutID = _field
	return offset, nil
}

func (p *RechargeReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RechargeAmount = _field
	return offset, nil
}

func (p *RechargeReq) FastReadField10(buf []byte) (int, error) {
	offset := 0
	_field := NewSubMemberInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.AccountInfo = _field
	return offset, nil
}

func (p *RechargeReq) FastReadField11(buf []byte) (int, error) {
	offset := 0
	_field := NewCaijingParam()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.CaijingParam = _field
	return offset, nil
}

func (p *RechargeReq) FastReadField250(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *RechargeReq) FastReadField251(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAddr = _field
	return offset, nil
}

func (p *RechargeReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *RechargeReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RechargeReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField251(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RechargeReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field250Length()
		l += p.field251Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RechargeReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RechargeReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Channel))
	return offset
}

func (p *RechargeReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *RechargeReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RechargeOutID)
	return offset
}

func (p *RechargeReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RechargeAmount)
	return offset
}

func (p *RechargeReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 10)
	offset += p.AccountInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RechargeReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 11)
	offset += p.CaijingParam.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RechargeReq) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 250)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RechargeReq) fastWriteField251(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 251)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAddr)
	return offset
}

func (p *RechargeReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *RechargeReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *RechargeReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RechargeReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *RechargeReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RechargeOutID)
	return l
}

func (p *RechargeReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *RechargeReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.AccountInfo.BLength()
	return l
}

func (p *RechargeReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.CaijingParam.BLength()
	return l
}

func (p *RechargeReq) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *RechargeReq) field251Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAddr)
	return l
}

func (p *RechargeReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *RechargeReq) DeepCopy(s interface{}) error {
	src, ok := s.(*RechargeReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.Channel = src.Channel

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.RechargeOutID != "" {
		p.RechargeOutID = kutils.StringDeepCopy(src.RechargeOutID)
	}

	p.RechargeAmount = src.RechargeAmount

	var _accountInfo *SubMemberInfo
	if src.AccountInfo != nil {
		_accountInfo = &SubMemberInfo{}
		if err := _accountInfo.DeepCopy(src.AccountInfo); err != nil {
			return err
		}
	}
	p.AccountInfo = _accountInfo

	var _caijingParam *CaijingParam
	if src.CaijingParam != nil {
		_caijingParam = &CaijingParam{}
		if err := _caijingParam.DeepCopy(src.CaijingParam); err != nil {
			return err
		}
	}
	p.CaijingParam = _caijingParam

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	if src.CallbackAddr != "" {
		p.CallbackAddr = kutils.StringDeepCopy(src.CallbackAddr)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *RechargeRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RechargeRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RechargeRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RechargeOutID = _field
	return offset, nil
}

func (p *RechargeRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RechargeNo = _field
	return offset, nil
}

func (p *RechargeRsp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayTradeNo = _field
	return offset, nil
}

func (p *RechargeRsp) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayLink = _field
	return offset, nil
}

func (p *RechargeRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *RechargeRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RechargeRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RechargeRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RechargeRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RechargeOutID)
	return offset
}

func (p *RechargeRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RechargeNo)
	return offset
}

func (p *RechargeRsp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayTradeNo)
	return offset
}

func (p *RechargeRsp) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayLink)
	return offset
}

func (p *RechargeRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *RechargeRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RechargeOutID)
	return l
}

func (p *RechargeRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RechargeNo)
	return l
}

func (p *RechargeRsp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayTradeNo)
	return l
}

func (p *RechargeRsp) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayLink)
	return l
}

func (p *RechargeRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *RechargeRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*RechargeRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RechargeOutID != "" {
		p.RechargeOutID = kutils.StringDeepCopy(src.RechargeOutID)
	}

	if src.RechargeNo != "" {
		p.RechargeNo = kutils.StringDeepCopy(src.RechargeNo)
	}

	if src.PayTradeNo != "" {
		p.PayTradeNo = kutils.StringDeepCopy(src.PayTradeNo)
	}

	if src.PayLink != "" {
		p.PayLink = kutils.StringDeepCopy(src.PayLink)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *WithdrawReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 251:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField251(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WithdrawReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *WithdrawReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field Channel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Channel(v)
	}
	p.Channel = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WithdrawOutID = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WithdrawAmount = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField11(buf []byte) (int, error) {
	offset := 0
	_field := NewSubMemberInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.AccountInfo = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField250(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField251(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAddr = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *WithdrawReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *WithdrawReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField251(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *WithdrawReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field11Length()
		l += p.field250Length()
		l += p.field251Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *WithdrawReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *WithdrawReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Channel))
	return offset
}

func (p *WithdrawReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *WithdrawReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.WithdrawOutID)
	return offset
}

func (p *WithdrawReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.WithdrawAmount)
	return offset
}

func (p *WithdrawReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 11)
	offset += p.AccountInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *WithdrawReq) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 250)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *WithdrawReq) fastWriteField251(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 251)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAddr)
	return offset
}

func (p *WithdrawReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *WithdrawReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *WithdrawReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *WithdrawReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *WithdrawReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.WithdrawOutID)
	return l
}

func (p *WithdrawReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *WithdrawReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.AccountInfo.BLength()
	return l
}

func (p *WithdrawReq) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *WithdrawReq) field251Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAddr)
	return l
}

func (p *WithdrawReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *WithdrawReq) DeepCopy(s interface{}) error {
	src, ok := s.(*WithdrawReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.Channel = src.Channel

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.WithdrawOutID != "" {
		p.WithdrawOutID = kutils.StringDeepCopy(src.WithdrawOutID)
	}

	p.WithdrawAmount = src.WithdrawAmount

	var _accountInfo *SubMemberInfo
	if src.AccountInfo != nil {
		_accountInfo = &SubMemberInfo{}
		if err := _accountInfo.DeepCopy(src.AccountInfo); err != nil {
			return err
		}
	}
	p.AccountInfo = _accountInfo

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	if src.CallbackAddr != "" {
		p.CallbackAddr = kutils.StringDeepCopy(src.CallbackAddr)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *WithdrawRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WithdrawRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *WithdrawRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WithdrawOutID = _field
	return offset, nil
}

func (p *WithdrawRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WithdrawNo = _field
	return offset, nil
}

func (p *WithdrawRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *WithdrawRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *WithdrawRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *WithdrawRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *WithdrawRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.WithdrawOutID)
	return offset
}

func (p *WithdrawRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.WithdrawNo)
	return offset
}

func (p *WithdrawRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *WithdrawRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.WithdrawOutID)
	return l
}

func (p *WithdrawRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.WithdrawNo)
	return l
}

func (p *WithdrawRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *WithdrawRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*WithdrawRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.WithdrawOutID != "" {
		p.WithdrawOutID = kutils.StringDeepCopy(src.WithdrawOutID)
	}

	if src.WithdrawNo != "" {
		p.WithdrawNo = kutils.StringDeepCopy(src.WithdrawNo)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *PayReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 251:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField251(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PayReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PayReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *PayReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field Channel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Channel(v)
	}
	p.Channel = _field
	return offset, nil
}

func (p *PayReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *PayReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayOutID = _field
	return offset, nil
}

func (p *PayReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayAmount = _field
	return offset, nil
}

func (p *PayReq) FastReadField10(buf []byte) (int, error) {
	offset := 0
	_field := NewSubMemberInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.FromAccount = _field
	return offset, nil
}

func (p *PayReq) FastReadField11(buf []byte) (int, error) {
	offset := 0
	_field := NewSubMemberInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ToAccount = _field
	return offset, nil
}

func (p *PayReq) FastReadField12(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewProductInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ProductInfo = _field
	return offset, nil
}

func (p *PayReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Remark = _field
	return offset, nil
}

func (p *PayReq) FastReadField250(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *PayReq) FastReadField251(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAddr = _field
	return offset, nil
}

func (p *PayReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *PayReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PayReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField251(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PayReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field100Length()
		l += p.field250Length()
		l += p.field251Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PayReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PayReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Channel))
	return offset
}

func (p *PayReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *PayReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayOutID)
	return offset
}

func (p *PayReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.PayAmount)
	return offset
}

func (p *PayReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 10)
	offset += p.FromAccount.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PayReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 11)
	offset += p.ToAccount.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PayReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 12)
	offset += p.ProductInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PayReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRemark() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Remark)
	}
	return offset
}

func (p *PayReq) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 250)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PayReq) fastWriteField251(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 251)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAddr)
	return offset
}

func (p *PayReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PayReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *PayReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *PayReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *PayReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayOutID)
	return l
}

func (p *PayReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *PayReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.FromAccount.BLength()
	return l
}

func (p *PayReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ToAccount.BLength()
	return l
}

func (p *PayReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ProductInfo.BLength()
	return l
}

func (p *PayReq) field100Length() int {
	l := 0
	if p.IsSetRemark() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Remark)
	}
	return l
}

func (p *PayReq) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *PayReq) field251Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAddr)
	return l
}

func (p *PayReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *PayReq) DeepCopy(s interface{}) error {
	src, ok := s.(*PayReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.Channel = src.Channel

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.PayOutID != "" {
		p.PayOutID = kutils.StringDeepCopy(src.PayOutID)
	}

	p.PayAmount = src.PayAmount

	var _fromAccount *SubMemberInfo
	if src.FromAccount != nil {
		_fromAccount = &SubMemberInfo{}
		if err := _fromAccount.DeepCopy(src.FromAccount); err != nil {
			return err
		}
	}
	p.FromAccount = _fromAccount

	var _toAccount *SubMemberInfo
	if src.ToAccount != nil {
		_toAccount = &SubMemberInfo{}
		if err := _toAccount.DeepCopy(src.ToAccount); err != nil {
			return err
		}
	}
	p.ToAccount = _toAccount

	var _productInfo *fwe_trade_common.ProductInfo
	if src.ProductInfo != nil {
		_productInfo = &fwe_trade_common.ProductInfo{}
		if err := _productInfo.DeepCopy(src.ProductInfo); err != nil {
			return err
		}
	}
	p.ProductInfo = _productInfo

	if src.Remark != nil {
		var tmp string
		if *src.Remark != "" {
			tmp = kutils.StringDeepCopy(*src.Remark)
		}
		p.Remark = &tmp
	}

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	if src.CallbackAddr != "" {
		p.CallbackAddr = kutils.StringDeepCopy(src.CallbackAddr)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *PayRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PayRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PayRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayOutID = _field
	return offset, nil
}

func (p *PayRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayNo = _field
	return offset, nil
}

func (p *PayRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *PayRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PayRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PayRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PayRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayOutID)
	return offset
}

func (p *PayRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayNo)
	return offset
}

func (p *PayRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PayRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayOutID)
	return l
}

func (p *PayRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayNo)
	return l
}

func (p *PayRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *PayRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*PayRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PayOutID != "" {
		p.PayOutID = kutils.StringDeepCopy(src.PayOutID)
	}

	if src.PayNo != "" {
		p.PayNo = kutils.StringDeepCopy(src.PayNo)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *RefundReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 251:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField251(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RefundReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RefundReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *RefundReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field Channel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Channel(v)
	}
	p.Channel = _field
	return offset, nil
}

func (p *RefundReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *RefundReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundOutID = _field
	return offset, nil
}

func (p *RefundReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayOutID = _field
	return offset, nil
}

func (p *RefundReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundAmount = _field
	return offset, nil
}

func (p *RefundReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Remark = _field
	return offset, nil
}

func (p *RefundReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NeedWithdraw = _field
	return offset, nil
}

func (p *RefundReq) FastReadField250(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *RefundReq) FastReadField251(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAddr = _field
	return offset, nil
}

func (p *RefundReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *RefundReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RefundReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField251(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RefundReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field250Length()
		l += p.field251Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RefundReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RefundReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Channel))
	return offset
}

func (p *RefundReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *RefundReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RefundOutID)
	return offset
}

func (p *RefundReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayOutID)
	return offset
}

func (p *RefundReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 7)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RefundAmount)
	return offset
}

func (p *RefundReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRemark() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Remark)
	}
	return offset
}

func (p *RefundReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 11)
	offset += thrift.Binary.WriteBool(buf[offset:], p.NeedWithdraw)
	return offset
}

func (p *RefundReq) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 250)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RefundReq) fastWriteField251(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 251)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAddr)
	return offset
}

func (p *RefundReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *RefundReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *RefundReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RefundReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *RefundReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RefundOutID)
	return l
}

func (p *RefundReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayOutID)
	return l
}

func (p *RefundReq) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *RefundReq) field10Length() int {
	l := 0
	if p.IsSetRemark() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Remark)
	}
	return l
}

func (p *RefundReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *RefundReq) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *RefundReq) field251Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAddr)
	return l
}

func (p *RefundReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *RefundReq) DeepCopy(s interface{}) error {
	src, ok := s.(*RefundReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.Channel = src.Channel

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.RefundOutID != "" {
		p.RefundOutID = kutils.StringDeepCopy(src.RefundOutID)
	}

	if src.PayOutID != "" {
		p.PayOutID = kutils.StringDeepCopy(src.PayOutID)
	}

	p.RefundAmount = src.RefundAmount

	if src.Remark != nil {
		var tmp string
		if *src.Remark != "" {
			tmp = kutils.StringDeepCopy(*src.Remark)
		}
		p.Remark = &tmp
	}

	p.NeedWithdraw = src.NeedWithdraw

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	if src.CallbackAddr != "" {
		p.CallbackAddr = kutils.StringDeepCopy(src.CallbackAddr)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *RefundRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RefundRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RefundRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundOutID = _field
	return offset, nil
}

func (p *RefundRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundNo = _field
	return offset, nil
}

func (p *RefundRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *RefundRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RefundRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RefundRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RefundRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RefundOutID)
	return offset
}

func (p *RefundRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RefundNo)
	return offset
}

func (p *RefundRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *RefundRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RefundOutID)
	return l
}

func (p *RefundRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RefundNo)
	return l
}

func (p *RefundRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *RefundRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*RefundRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RefundOutID != "" {
		p.RefundOutID = kutils.StringDeepCopy(src.RefundOutID)
	}

	if src.RefundNo != "" {
		p.RefundNo = kutils.StringDeepCopy(src.RefundNo)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *SettleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 251:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField251(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SettleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SettleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *SettleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field Channel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Channel(v)
	}
	p.Channel = _field
	return offset, nil
}

func (p *SettleReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *SettleReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SettleOutID = _field
	return offset, nil
}

func (p *SettleReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayOutID = _field
	return offset, nil
}

func (p *SettleReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SettleAmount = _field
	return offset, nil
}

func (p *SettleReq) FastReadField250(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *SettleReq) FastReadField251(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAddr = _field
	return offset, nil
}

func (p *SettleReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *SettleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SettleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField251(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SettleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field250Length()
		l += p.field251Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SettleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *SettleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Channel))
	return offset
}

func (p *SettleReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *SettleReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SettleOutID)
	return offset
}

func (p *SettleReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayOutID)
	return offset
}

func (p *SettleReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 7)
	offset += thrift.Binary.WriteI64(buf[offset:], p.SettleAmount)
	return offset
}

func (p *SettleReq) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 250)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *SettleReq) fastWriteField251(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 251)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAddr)
	return offset
}

func (p *SettleReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *SettleReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *SettleReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SettleReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *SettleReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SettleOutID)
	return l
}

func (p *SettleReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayOutID)
	return l
}

func (p *SettleReq) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SettleReq) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *SettleReq) field251Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAddr)
	return l
}

func (p *SettleReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *SettleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*SettleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.Channel = src.Channel

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.SettleOutID != "" {
		p.SettleOutID = kutils.StringDeepCopy(src.SettleOutID)
	}

	if src.PayOutID != "" {
		p.PayOutID = kutils.StringDeepCopy(src.PayOutID)
	}

	p.SettleAmount = src.SettleAmount

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	if src.CallbackAddr != "" {
		p.CallbackAddr = kutils.StringDeepCopy(src.CallbackAddr)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *SettleRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SettleRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SettleRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SettleOutID = _field
	return offset, nil
}

func (p *SettleRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SettleNo = _field
	return offset, nil
}

func (p *SettleRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *SettleRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SettleRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SettleRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SettleRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SettleOutID)
	return offset
}

func (p *SettleRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SettleNo)
	return offset
}

func (p *SettleRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *SettleRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SettleOutID)
	return l
}

func (p *SettleRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SettleNo)
	return l
}

func (p *SettleRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *SettleRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*SettleRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SettleOutID != "" {
		p.SettleOutID = kutils.StringDeepCopy(src.SettleOutID)
	}

	if src.SettleNo != "" {
		p.SettleNo = kutils.StringDeepCopy(src.SettleNo)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *ReconciliationReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReconciliationReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ReconciliationReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *ReconciliationReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.FileTypeList = _field
	return offset, nil
}

func (p *ReconciliationReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Date = _field
	return offset, nil
}

func (p *ReconciliationReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *ReconciliationReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ReconciliationReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ReconciliationReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field4Length()
		l += p.field5Length()
		l += p.field10Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ReconciliationReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *ReconciliationReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 5)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FileTypeList {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *ReconciliationReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDate() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Date)
	}
	return offset
}

func (p *ReconciliationReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ReconciliationReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *ReconciliationReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FileTypeList {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *ReconciliationReq) field10Length() int {
	l := 0
	if p.IsSetDate() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Date)
	}
	return l
}

func (p *ReconciliationReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *ReconciliationReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ReconciliationReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.FileTypeList != nil {
		p.FileTypeList = make([]string, 0, len(src.FileTypeList))
		for _, elem := range src.FileTypeList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.FileTypeList = append(p.FileTypeList, _elem)
		}
	}

	if src.Date != nil {
		var tmp string
		if *src.Date != "" {
			tmp = kutils.StringDeepCopy(*src.Date)
		}
		p.Date = &tmp
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *ReconciliationRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReconciliationRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ReconciliationRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *ReconciliationRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ReconciliationRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ReconciliationRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ReconciliationRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ReconciliationRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *ReconciliationRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*ReconciliationRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *ReconciliationDataReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReconciliationDataReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ReconciliationDataReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *ReconciliationDataReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FileType = _field
	return offset, nil
}

func (p *ReconciliationDataReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Date = _field
	return offset, nil
}

func (p *ReconciliationDataReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *ReconciliationDataReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ReconciliationDataReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ReconciliationDataReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field4Length()
		l += p.field5Length()
		l += p.field10Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ReconciliationDataReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *ReconciliationDataReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FileType)
	return offset
}

func (p *ReconciliationDataReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDate() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Date)
	}
	return offset
}

func (p *ReconciliationDataReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ReconciliationDataReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *ReconciliationDataReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FileType)
	return l
}

func (p *ReconciliationDataReq) field10Length() int {
	l := 0
	if p.IsSetDate() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Date)
	}
	return l
}

func (p *ReconciliationDataReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *ReconciliationDataReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ReconciliationDataReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.FileType != "" {
		p.FileType = kutils.StringDeepCopy(src.FileType)
	}

	if src.Date != nil {
		var tmp string
		if *src.Date != "" {
			tmp = kutils.StringDeepCopy(*src.Date)
		}
		p.Date = &tmp
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *ReconciliationData) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReconciliationData[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ReconciliationData) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FileName = _field
	return offset, nil
}

func (p *ReconciliationData) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RandomPassword = _field
	return offset, nil
}

func (p *ReconciliationData) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FilePath = _field
	return offset, nil
}

func (p *ReconciliationData) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DrawCode = _field
	return offset, nil
}

func (p *ReconciliationData) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FormData = _field
	return offset, nil
}

func (p *ReconciliationData) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RawData = _field
	return offset, nil
}

func (p *ReconciliationData) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ReconciliationData) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ReconciliationData) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field10Length()
		l += p.field11Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ReconciliationData) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FileName)
	return offset
}

func (p *ReconciliationData) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RandomPassword)
	return offset
}

func (p *ReconciliationData) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FilePath)
	return offset
}

func (p *ReconciliationData) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.DrawCode)
	return offset
}

func (p *ReconciliationData) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FormData)
	return offset
}

func (p *ReconciliationData) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RawData)
	return offset
}

func (p *ReconciliationData) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FileName)
	return l
}

func (p *ReconciliationData) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RandomPassword)
	return l
}

func (p *ReconciliationData) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FilePath)
	return l
}

func (p *ReconciliationData) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.DrawCode)
	return l
}

func (p *ReconciliationData) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FormData)
	return l
}

func (p *ReconciliationData) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RawData)
	return l
}

func (p *ReconciliationData) DeepCopy(s interface{}) error {
	src, ok := s.(*ReconciliationData)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.FileName != "" {
		p.FileName = kutils.StringDeepCopy(src.FileName)
	}

	if src.RandomPassword != "" {
		p.RandomPassword = kutils.StringDeepCopy(src.RandomPassword)
	}

	if src.FilePath != "" {
		p.FilePath = kutils.StringDeepCopy(src.FilePath)
	}

	if src.DrawCode != "" {
		p.DrawCode = kutils.StringDeepCopy(src.DrawCode)
	}

	if src.FormData != "" {
		p.FormData = kutils.StringDeepCopy(src.FormData)
	}

	if src.RawData != "" {
		p.RawData = kutils.StringDeepCopy(src.RawData)
	}

	return nil
}

func (p *ReconciliationDataRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ReconciliationDataRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ReconciliationDataRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*ReconciliationData, 0, size)
	values := make([]ReconciliationData, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.DataList = _field
	return offset, nil
}

func (p *ReconciliationDataRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *ReconciliationDataRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ReconciliationDataRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ReconciliationDataRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ReconciliationDataRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.DataList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *ReconciliationDataRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *ReconciliationDataRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.DataList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *ReconciliationDataRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *ReconciliationDataRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*ReconciliationDataRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.DataList != nil {
		p.DataList = make([]*ReconciliationData, 0, len(src.DataList))
		for _, elem := range src.DataList {
			var _elem *ReconciliationData
			if elem != nil {
				_elem = &ReconciliationData{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.DataList = append(p.DataList, _elem)
		}
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *AsyncCallReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 251:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField251(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AsyncCallReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *AsyncCallReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *AsyncCallReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field Channel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Channel(v)
	}
	p.Channel = _field
	return offset, nil
}

func (p *AsyncCallReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallKey = _field
	return offset, nil
}

func (p *AsyncCallReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *AsyncCallReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OutID = _field
	return offset, nil
}

func (p *AsyncCallReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TradeNo = _field
	return offset, nil
}

func (p *AsyncCallReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallBody = _field
	return offset, nil
}

func (p *AsyncCallReq) FastReadField250(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *AsyncCallReq) FastReadField251(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAddr = _field
	return offset, nil
}

func (p *AsyncCallReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *AsyncCallReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *AsyncCallReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField251(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *AsyncCallReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field100Length()
		l += p.field250Length()
		l += p.field251Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *AsyncCallReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *AsyncCallReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Channel))
	return offset
}

func (p *AsyncCallReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallKey)
	return offset
}

func (p *AsyncCallReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *AsyncCallReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OutID)
	return offset
}

func (p *AsyncCallReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TradeNo)
	return offset
}

func (p *AsyncCallReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallBody)
	return offset
}

func (p *AsyncCallReq) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 250)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *AsyncCallReq) fastWriteField251(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 251)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAddr)
	return offset
}

func (p *AsyncCallReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *AsyncCallReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *AsyncCallReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *AsyncCallReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallKey)
	return l
}

func (p *AsyncCallReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *AsyncCallReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OutID)
	return l
}

func (p *AsyncCallReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TradeNo)
	return l
}

func (p *AsyncCallReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallBody)
	return l
}

func (p *AsyncCallReq) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *AsyncCallReq) field251Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAddr)
	return l
}

func (p *AsyncCallReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *AsyncCallReq) DeepCopy(s interface{}) error {
	src, ok := s.(*AsyncCallReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.Channel = src.Channel

	if src.CallKey != "" {
		p.CallKey = kutils.StringDeepCopy(src.CallKey)
	}

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.OutID != "" {
		p.OutID = kutils.StringDeepCopy(src.OutID)
	}

	if src.TradeNo != "" {
		p.TradeNo = kutils.StringDeepCopy(src.TradeNo)
	}

	if src.CallBody != "" {
		p.CallBody = kutils.StringDeepCopy(src.CallBody)
	}

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	if src.CallbackAddr != "" {
		p.CallbackAddr = kutils.StringDeepCopy(src.CallbackAddr)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *AsyncCallRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AsyncCallRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *AsyncCallRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *AsyncCallRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *AsyncCallRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *AsyncCallRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *AsyncCallRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *AsyncCallRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *AsyncCallRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*AsyncCallRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *QueryCodeReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryCodeReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *QueryCodeReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *QueryCodeReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field Channel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Channel(v)
	}
	p.Channel = _field
	return offset, nil
}

func (p *QueryCodeReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *QueryCodeReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OutID = _field
	return offset, nil
}

func (p *QueryCodeReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.TradeCategory
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.TradeCategory(v)
	}
	p.TradeCategory = _field
	return offset, nil
}

func (p *QueryCodeReq) FastReadField250(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *QueryCodeReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *QueryCodeReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *QueryCodeReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *QueryCodeReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field4Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field250Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *QueryCodeReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *QueryCodeReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Channel))
	return offset
}

func (p *QueryCodeReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *QueryCodeReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OutID)
	return offset
}

func (p *QueryCodeReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 11)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.TradeCategory))
	return offset
}

func (p *QueryCodeReq) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 250)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *QueryCodeReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *QueryCodeReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *QueryCodeReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *QueryCodeReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *QueryCodeReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OutID)
	return l
}

func (p *QueryCodeReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *QueryCodeReq) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *QueryCodeReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *QueryCodeReq) DeepCopy(s interface{}) error {
	src, ok := s.(*QueryCodeReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.Channel = src.Channel

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.OutID != "" {
		p.OutID = kutils.StringDeepCopy(src.OutID)
	}

	p.TradeCategory = src.TradeCategory

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *QueryCodeRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryCodeRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *QueryCodeRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Code = _field
	return offset, nil
}

func (p *QueryCodeRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SeqNo = _field
	return offset, nil
}

func (p *QueryCodeRsp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.JumpURL = _field
	return offset, nil
}

func (p *QueryCodeRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *QueryCodeRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *QueryCodeRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *QueryCodeRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *QueryCodeRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Code)
	return offset
}

func (p *QueryCodeRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SeqNo)
	return offset
}

func (p *QueryCodeRsp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.JumpURL)
	return offset
}

func (p *QueryCodeRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *QueryCodeRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Code)
	return l
}

func (p *QueryCodeRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SeqNo)
	return l
}

func (p *QueryCodeRsp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.JumpURL)
	return l
}

func (p *QueryCodeRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *QueryCodeRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*QueryCodeRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Code != "" {
		p.Code = kutils.StringDeepCopy(src.Code)
	}

	if src.SeqNo != "" {
		p.SeqNo = kutils.StringDeepCopy(src.SeqNo)
	}

	if src.JumpURL != "" {
		p.JumpURL = kutils.StringDeepCopy(src.JumpURL)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *WithdrawInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WithdrawInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *WithdrawInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WithdrawOutID = _field
	return offset, nil
}

func (p *WithdrawInfo) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WithdrawNo = _field
	return offset, nil
}

func (p *WithdrawInfo) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WithdrawAmount = _field
	return offset, nil
}

func (p *WithdrawInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *WithdrawInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *WithdrawInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *WithdrawInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.WithdrawOutID)
	return offset
}

func (p *WithdrawInfo) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.WithdrawNo)
	return offset
}

func (p *WithdrawInfo) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.WithdrawAmount)
	return offset
}

func (p *WithdrawInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.WithdrawOutID)
	return l
}

func (p *WithdrawInfo) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.WithdrawNo)
	return l
}

func (p *WithdrawInfo) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *WithdrawInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*WithdrawInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.WithdrawOutID != "" {
		p.WithdrawOutID = kutils.StringDeepCopy(src.WithdrawOutID)
	}

	if src.WithdrawNo != "" {
		p.WithdrawNo = kutils.StringDeepCopy(src.WithdrawNo)
	}

	p.WithdrawAmount = src.WithdrawAmount

	return nil
}

func (p *SettleInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SettleInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SettleInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SettleOutID = _field
	return offset, nil
}

func (p *SettleInfo) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SettleNo = _field
	return offset, nil
}

func (p *SettleInfo) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SettleAmount = _field
	return offset, nil
}

func (p *SettleInfo) FastReadField10(buf []byte) (int, error) {
	offset := 0
	_field := NewWithdrawInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.WithdrawInfo = _field
	return offset, nil
}

func (p *SettleInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SettleInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SettleInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field10Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SettleInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SettleOutID)
	return offset
}

func (p *SettleInfo) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SettleNo)
	return offset
}

func (p *SettleInfo) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.SettleAmount)
	return offset
}

func (p *SettleInfo) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 10)
	offset += p.WithdrawInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *SettleInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SettleOutID)
	return l
}

func (p *SettleInfo) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SettleNo)
	return l
}

func (p *SettleInfo) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SettleInfo) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.WithdrawInfo.BLength()
	return l
}

func (p *SettleInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*SettleInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.SettleOutID != "" {
		p.SettleOutID = kutils.StringDeepCopy(src.SettleOutID)
	}

	if src.SettleNo != "" {
		p.SettleNo = kutils.StringDeepCopy(src.SettleNo)
	}

	p.SettleAmount = src.SettleAmount

	var _withdrawInfo *WithdrawInfo
	if src.WithdrawInfo != nil {
		_withdrawInfo = &WithdrawInfo{}
		if err := _withdrawInfo.DeepCopy(src.WithdrawInfo); err != nil {
			return err
		}
	}
	p.WithdrawInfo = _withdrawInfo

	return nil
}

func (p *RefundContent) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RefundContent[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RefundContent) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NeedWithdraw = _field
	return offset, nil
}

func (p *RefundContent) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RefundContent) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RefundContent) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RefundContent) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 1)
	offset += thrift.Binary.WriteBool(buf[offset:], p.NeedWithdraw)
	return offset
}

func (p *RefundContent) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *RefundContent) DeepCopy(s interface{}) error {
	src, ok := s.(*RefundContent)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.NeedWithdraw = src.NeedWithdraw

	return nil
}

func (p *RefundInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RefundInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RefundInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundOutID = _field
	return offset, nil
}

func (p *RefundInfo) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundNo = _field
	return offset, nil
}

func (p *RefundInfo) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundAmount = _field
	return offset, nil
}

func (p *RefundInfo) FastReadField10(buf []byte) (int, error) {
	offset := 0
	_field := NewWithdrawInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.WithdrawInfo = _field
	return offset, nil
}

func (p *RefundInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RefundInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RefundInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field10Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RefundInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RefundOutID)
	return offset
}

func (p *RefundInfo) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RefundNo)
	return offset
}

func (p *RefundInfo) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RefundAmount)
	return offset
}

func (p *RefundInfo) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 10)
	offset += p.WithdrawInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RefundInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RefundOutID)
	return l
}

func (p *RefundInfo) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RefundNo)
	return l
}

func (p *RefundInfo) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *RefundInfo) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.WithdrawInfo.BLength()
	return l
}

func (p *RefundInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*RefundInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RefundOutID != "" {
		p.RefundOutID = kutils.StringDeepCopy(src.RefundOutID)
	}

	if src.RefundNo != "" {
		p.RefundNo = kutils.StringDeepCopy(src.RefundNo)
	}

	p.RefundAmount = src.RefundAmount

	var _withdrawInfo *WithdrawInfo
	if src.WithdrawInfo != nil {
		_withdrawInfo = &WithdrawInfo{}
		if err := _withdrawInfo.DeepCopy(src.WithdrawInfo); err != nil {
			return err
		}
	}
	p.WithdrawInfo = _withdrawInfo

	return nil
}

func (p *PayInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PayInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PayInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayOutID = _field
	return offset, nil
}

func (p *PayInfo) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayNo = _field
	return offset, nil
}

func (p *PayInfo) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayAmount = _field
	return offset, nil
}

func (p *PayInfo) FastReadField100(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*RefundInfo, 0, size)
	values := make([]RefundInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RefundList = _field
	return offset, nil
}

func (p *PayInfo) FastReadField101(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*SettleInfo, 0, size)
	values := make([]SettleInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.SettleList = _field
	return offset, nil
}

func (p *PayInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PayInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PayInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field100Length()
		l += p.field101Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PayInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayOutID)
	return offset
}

func (p *PayInfo) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayNo)
	return offset
}

func (p *PayInfo) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.PayAmount)
	return offset
}

func (p *PayInfo) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 100)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RefundList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *PayInfo) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 101)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.SettleList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *PayInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayOutID)
	return l
}

func (p *PayInfo) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayNo)
	return l
}

func (p *PayInfo) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *PayInfo) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RefundList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *PayInfo) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.SettleList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *PayInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*PayInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PayOutID != "" {
		p.PayOutID = kutils.StringDeepCopy(src.PayOutID)
	}

	if src.PayNo != "" {
		p.PayNo = kutils.StringDeepCopy(src.PayNo)
	}

	p.PayAmount = src.PayAmount

	if src.RefundList != nil {
		p.RefundList = make([]*RefundInfo, 0, len(src.RefundList))
		for _, elem := range src.RefundList {
			var _elem *RefundInfo
			if elem != nil {
				_elem = &RefundInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RefundList = append(p.RefundList, _elem)
		}
	}

	if src.SettleList != nil {
		p.SettleList = make([]*SettleInfo, 0, len(src.SettleList))
		for _, elem := range src.SettleList {
			var _elem *SettleInfo
			if elem != nil {
				_elem = &SettleInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.SettleList = append(p.SettleList, _elem)
		}
	}

	return nil
}

func (p *MGetPayListReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetPayListReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *MGetPayListReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *MGetPayListReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.ReadStrategy
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.ReadStrategy(v)
	}
	p.ReadStrategy = _field
	return offset, nil
}

func (p *MGetPayListReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.PayOutID = _field
	return offset, nil
}

func (p *MGetPayListReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NeedRefund = _field
	return offset, nil
}

func (p *MGetPayListReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NeedSettle = _field
	return offset, nil
}

func (p *MGetPayListReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NeedWithdraw = _field
	return offset, nil
}

func (p *MGetPayListReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *MGetPayListReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *MGetPayListReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *MGetPayListReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *MGetPayListReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *MGetPayListReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.ReadStrategy))
	return offset
}

func (p *MGetPayListReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.PayOutID {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *MGetPayListReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 10)
	offset += thrift.Binary.WriteBool(buf[offset:], p.NeedRefund)
	return offset
}

func (p *MGetPayListReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 11)
	offset += thrift.Binary.WriteBool(buf[offset:], p.NeedSettle)
	return offset
}

func (p *MGetPayListReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 12)
	offset += thrift.Binary.WriteBool(buf[offset:], p.NeedWithdraw)
	return offset
}

func (p *MGetPayListReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *MGetPayListReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *MGetPayListReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *MGetPayListReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.PayOutID {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *MGetPayListReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *MGetPayListReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *MGetPayListReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *MGetPayListReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *MGetPayListReq) DeepCopy(s interface{}) error {
	src, ok := s.(*MGetPayListReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.ReadStrategy = src.ReadStrategy

	if src.PayOutID != nil {
		p.PayOutID = make([]string, 0, len(src.PayOutID))
		for _, elem := range src.PayOutID {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.PayOutID = append(p.PayOutID, _elem)
		}
	}

	p.NeedRefund = src.NeedRefund

	p.NeedSettle = src.NeedSettle

	p.NeedWithdraw = src.NeedWithdraw

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *MGetPayListRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MGetPayListRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *MGetPayListRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]*PayInfo, size)
	values := make([]PayInfo, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		_val := &values[i]
		_val.InitDefault()
		if l, err := _val.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field[_key] = _val
	}
	p.PayMap = _field
	return offset, nil
}

func (p *MGetPayListRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *MGetPayListRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *MGetPayListRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *MGetPayListRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *MGetPayListRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 1)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.PayMap {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRUCT, length)
	return offset
}

func (p *MGetPayListRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *MGetPayListRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.PayMap {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += v.BLength()
	}
	return l
}

func (p *MGetPayListRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *MGetPayListRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*MGetPayListRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PayMap != nil {
		p.PayMap = make(map[string]*PayInfo, len(src.PayMap))
		for key, val := range src.PayMap {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val *PayInfo
			if val != nil {
				_val = &PayInfo{}
				if err := _val.DeepCopy(val); err != nil {
					return err
				}
			}

			p.PayMap[_key] = _val
		}
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *PaymentTobBizCallMsg) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentTobBizCallMsg[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentTobBizCallMsg) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Amount = _field
	return offset, nil
}

func (p *PaymentTobBizCallMsg) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentTobBizCallMsg) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentTobBizCallMsg) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentTobBizCallMsg) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetAmount() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.Amount)
	}
	return offset
}

func (p *PaymentTobBizCallMsg) field1Length() int {
	l := 0
	if p.IsSetAmount() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *PaymentTobBizCallMsg) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentTobBizCallMsg)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Amount != nil {
		tmp := *src.Amount
		p.Amount = &tmp
	}

	return nil
}

func (p *PaymentTobBizCallReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentTobBizCallReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentTobBizCallReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *PaymentTobBizCallReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field Channel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Channel(v)
	}
	p.Channel = _field
	return offset, nil
}

func (p *PaymentTobBizCallReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.TradeCategory
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.TradeCategory(v)
	}
	p.TradeCategory = _field
	return offset, nil
}

func (p *PaymentTobBizCallReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *PaymentTobBizCallReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OutID = _field
	return offset, nil
}

func (p *PaymentTobBizCallReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TradeNo = _field
	return offset, nil
}

func (p *PaymentTobBizCallReq) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallStatus = _field
	return offset, nil
}

func (p *PaymentTobBizCallReq) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallMsg = _field
	return offset, nil
}

func (p *PaymentTobBizCallReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *PaymentTobBizCallReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentTobBizCallReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentTobBizCallReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field20Length()
		l += p.field21Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentTobBizCallReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentTobBizCallReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Channel))
	return offset
}

func (p *PaymentTobBizCallReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.TradeCategory))
	return offset
}

func (p *PaymentTobBizCallReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *PaymentTobBizCallReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OutID)
	return offset
}

func (p *PaymentTobBizCallReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TradeNo)
	return offset
}

func (p *PaymentTobBizCallReq) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 20)
	offset += thrift.Binary.WriteI32(buf[offset:], p.CallStatus)
	return offset
}

func (p *PaymentTobBizCallReq) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 21)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallMsg)
	return offset
}

func (p *PaymentTobBizCallReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentTobBizCallReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *PaymentTobBizCallReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *PaymentTobBizCallReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *PaymentTobBizCallReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *PaymentTobBizCallReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OutID)
	return l
}

func (p *PaymentTobBizCallReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TradeNo)
	return l
}

func (p *PaymentTobBizCallReq) field20Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *PaymentTobBizCallReq) field21Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallMsg)
	return l
}

func (p *PaymentTobBizCallReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *PaymentTobBizCallReq) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentTobBizCallReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.Channel = src.Channel

	p.TradeCategory = src.TradeCategory

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.OutID != "" {
		p.OutID = kutils.StringDeepCopy(src.OutID)
	}

	if src.TradeNo != "" {
		p.TradeNo = kutils.StringDeepCopy(src.TradeNo)
	}

	p.CallStatus = src.CallStatus

	if src.CallMsg != "" {
		p.CallMsg = kutils.StringDeepCopy(src.CallMsg)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *PaymentTobBizCallRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentTobBizCallRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentTobBizCallRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *PaymentTobBizCallRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentTobBizCallRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentTobBizCallRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentTobBizCallRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentTobBizCallRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *PaymentTobBizCallRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentTobBizCallRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *JstRawHTTPReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_JstRawHTTPReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *JstRawHTTPReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.JstMethod = _field
	return offset, nil
}

func (p *JstRawHTTPReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.JstTxnCode = _field
	return offset, nil
}

func (p *JstRawHTTPReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.CnsmrSeqNo = _field
	return offset, nil
}

func (p *JstRawHTTPReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Body = _field
	return offset, nil
}

func (p *JstRawHTTPReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *JstRawHTTPReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *JstRawHTTPReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *JstRawHTTPReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field100Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *JstRawHTTPReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.JstMethod)
	return offset
}

func (p *JstRawHTTPReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.JstTxnCode)
	return offset
}

func (p *JstRawHTTPReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCnsmrSeqNo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.CnsmrSeqNo)
	}
	return offset
}

func (p *JstRawHTTPReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Body)
	return offset
}

func (p *JstRawHTTPReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *JstRawHTTPReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.JstMethod)
	return l
}

func (p *JstRawHTTPReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.JstTxnCode)
	return l
}

func (p *JstRawHTTPReq) field3Length() int {
	l := 0
	if p.IsSetCnsmrSeqNo() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.CnsmrSeqNo)
	}
	return l
}

func (p *JstRawHTTPReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Body)
	return l
}

func (p *JstRawHTTPReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *JstRawHTTPReq) DeepCopy(s interface{}) error {
	src, ok := s.(*JstRawHTTPReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.JstMethod != "" {
		p.JstMethod = kutils.StringDeepCopy(src.JstMethod)
	}

	if src.JstTxnCode != "" {
		p.JstTxnCode = kutils.StringDeepCopy(src.JstTxnCode)
	}

	if src.CnsmrSeqNo != nil {
		var tmp string
		if *src.CnsmrSeqNo != "" {
			tmp = kutils.StringDeepCopy(*src.CnsmrSeqNo)
		}
		p.CnsmrSeqNo = &tmp
	}

	if src.Body != "" {
		p.Body = kutils.StringDeepCopy(src.Body)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *JstRawHTTPRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_JstRawHTTPRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *JstRawHTTPRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Body = _field
	return offset, nil
}

func (p *JstRawHTTPRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *JstRawHTTPRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *JstRawHTTPRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *JstRawHTTPRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *JstRawHTTPRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Body)
	return offset
}

func (p *JstRawHTTPRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *JstRawHTTPRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Body)
	return l
}

func (p *JstRawHTTPRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *JstRawHTTPRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*JstRawHTTPRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Body != "" {
		p.Body = kutils.StringDeepCopy(src.Body)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *UnionNotifyRequest) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBody bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetBody = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetBody {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UnionNotifyRequest[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_UnionNotifyRequest[fieldId]))
}

func (p *UnionNotifyRequest) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Body = _field
	return offset, nil
}

func (p *UnionNotifyRequest) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Sign = _field
	return offset, nil
}

func (p *UnionNotifyRequest) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SignType = _field
	return offset, nil
}

func (p *UnionNotifyRequest) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Exts = _field
	return offset, nil
}

func (p *UnionNotifyRequest) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *UnionNotifyRequest) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UnionNotifyRequest) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UnionNotifyRequest) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field200Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UnionNotifyRequest) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Body)
	return offset
}

func (p *UnionNotifyRequest) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Sign)
	return offset
}

func (p *UnionNotifyRequest) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SignType)
	return offset
}

func (p *UnionNotifyRequest) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Exts {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *UnionNotifyRequest) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *UnionNotifyRequest) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Body)
	return l
}

func (p *UnionNotifyRequest) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Sign)
	return l
}

func (p *UnionNotifyRequest) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SignType)
	return l
}

func (p *UnionNotifyRequest) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Exts {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *UnionNotifyRequest) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *UnionNotifyRequest) DeepCopy(s interface{}) error {
	src, ok := s.(*UnionNotifyRequest)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Body != "" {
		p.Body = kutils.StringDeepCopy(src.Body)
	}

	if src.Sign != "" {
		p.Sign = kutils.StringDeepCopy(src.Sign)
	}

	if src.SignType != "" {
		p.SignType = kutils.StringDeepCopy(src.SignType)
	}

	if src.Exts != nil {
		p.Exts = make(map[string]string, len(src.Exts))
		for key, val := range src.Exts {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Exts[_key] = _val
		}
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *UnionNotifyResponse) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRetCode bool = false
	var issetRetMsg bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRetCode = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRetMsg = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetRetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRetMsg {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UnionNotifyResponse[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_UnionNotifyResponse[fieldId]))
}

func (p *UnionNotifyResponse) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RetCode = _field
	return offset, nil
}

func (p *UnionNotifyResponse) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RetMsg = _field
	return offset, nil
}

func (p *UnionNotifyResponse) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Exts = _field
	return offset, nil
}

func (p *UnionNotifyResponse) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *UnionNotifyResponse) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UnionNotifyResponse) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UnionNotifyResponse) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field200Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UnionNotifyResponse) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RetCode)
	return offset
}

func (p *UnionNotifyResponse) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RetMsg)
	return offset
}

func (p *UnionNotifyResponse) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Exts {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *UnionNotifyResponse) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
	offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UnionNotifyResponse) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RetCode)
	return l
}

func (p *UnionNotifyResponse) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RetMsg)
	return l
}

func (p *UnionNotifyResponse) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Exts {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *UnionNotifyResponse) field255Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BaseResp.BLength()
	return l
}

func (p *UnionNotifyResponse) DeepCopy(s interface{}) error {
	src, ok := s.(*UnionNotifyResponse)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RetCode != "" {
		p.RetCode = kutils.StringDeepCopy(src.RetCode)
	}

	if src.RetMsg != "" {
		p.RetMsg = kutils.StringDeepCopy(src.RetMsg)
	}

	if src.Exts != nil {
		p.Exts = make(map[string]string, len(src.Exts))
		for key, val := range src.Exts {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Exts[_key] = _val
		}
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *GetBalanceInfoReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetBalanceInfoReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *GetBalanceInfoReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *GetBalanceInfoReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field Channel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Channel(v)
	}
	p.Channel = _field
	return offset, nil
}

func (p *GetBalanceInfoReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *GetBalanceInfoReq) FastReadField10(buf []byte) (int, error) {
	offset := 0
	_field := NewSubMemberInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.AccountInfo = _field
	return offset, nil
}

func (p *GetBalanceInfoReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *GetBalanceInfoReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GetBalanceInfoReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GetBalanceInfoReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field4Length()
		l += p.field10Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GetBalanceInfoReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *GetBalanceInfoReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Channel))
	return offset
}

func (p *GetBalanceInfoReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *GetBalanceInfoReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 10)
	offset += p.AccountInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *GetBalanceInfoReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *GetBalanceInfoReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *GetBalanceInfoReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *GetBalanceInfoReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *GetBalanceInfoReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.AccountInfo.BLength()
	return l
}

func (p *GetBalanceInfoReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *GetBalanceInfoReq) DeepCopy(s interface{}) error {
	src, ok := s.(*GetBalanceInfoReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.Channel = src.Channel

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	var _accountInfo *SubMemberInfo
	if src.AccountInfo != nil {
		_accountInfo = &SubMemberInfo{}
		if err := _accountInfo.DeepCopy(src.AccountInfo); err != nil {
			return err
		}
	}
	p.AccountInfo = _accountInfo

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *BalanceInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BalanceInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *BalanceInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AvailableAmount = _field
	return offset, nil
}

func (p *BalanceInfo) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CashAmount = _field
	return offset, nil
}

func (p *BalanceInfo) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AssureAmount = _field
	return offset, nil
}

func (p *BalanceInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *BalanceInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *BalanceInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *BalanceInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.AvailableAmount)
	return offset
}

func (p *BalanceInfo) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CashAmount)
	return offset
}

func (p *BalanceInfo) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.AssureAmount)
	return offset
}

func (p *BalanceInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *BalanceInfo) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *BalanceInfo) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *BalanceInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*BalanceInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.AvailableAmount = src.AvailableAmount

	p.CashAmount = src.CashAmount

	p.AssureAmount = src.AssureAmount

	return nil
}

func (p *GetBalanceInfoRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetBalanceInfoRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *GetBalanceInfoRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewBalanceInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BalanceInfo = _field
	return offset, nil
}

func (p *GetBalanceInfoRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *GetBalanceInfoRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GetBalanceInfoRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GetBalanceInfoRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GetBalanceInfoRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.BalanceInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *GetBalanceInfoRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *GetBalanceInfoRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BalanceInfo.BLength()
	return l
}

func (p *GetBalanceInfoRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *GetBalanceInfoRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*GetBalanceInfoRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _balanceInfo *BalanceInfo
	if src.BalanceInfo != nil {
		_balanceInfo = &BalanceInfo{}
		if err := _balanceInfo.DeepCopy(src.BalanceInfo); err != nil {
			return err
		}
	}
	p.BalanceInfo = _balanceInfo

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *QueryBalanceFlowReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 251:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField251(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryBalanceFlowReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *QueryBalanceFlowReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBizIdentity()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Identity = _field
	return offset, nil
}

func (p *QueryBalanceFlowReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field Channel
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = Channel(v)
	}
	p.Channel = _field
	return offset, nil
}

func (p *QueryBalanceFlowReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *QueryBalanceFlowReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field FlowCategory
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = FlowCategory(v)
	}
	p.FlowCategory = _field
	return offset, nil
}

func (p *QueryBalanceFlowReq) FastReadField10(buf []byte) (int, error) {
	offset := 0
	_field := NewSubMemberInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.AccountInfo = _field
	return offset, nil
}

func (p *QueryBalanceFlowReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.StartDate = _field
	return offset, nil
}

func (p *QueryBalanceFlowReq) FastReadField101(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.EndDate = _field
	return offset, nil
}

func (p *QueryBalanceFlowReq) FastReadField250(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Offset = _field
	return offset, nil
}

func (p *QueryBalanceFlowReq) FastReadField251(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Limit = _field
	return offset, nil
}

func (p *QueryBalanceFlowReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *QueryBalanceFlowReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *QueryBalanceFlowReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField251(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *QueryBalanceFlowReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field10Length()
		l += p.field100Length()
		l += p.field101Length()
		l += p.field250Length()
		l += p.field251Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *QueryBalanceFlowReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Identity.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *QueryBalanceFlowReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Channel))
	return offset
}

func (p *QueryBalanceFlowReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *QueryBalanceFlowReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.FlowCategory))
	return offset
}

func (p *QueryBalanceFlowReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 10)
	offset += p.AccountInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *QueryBalanceFlowReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetStartDate() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 100)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.StartDate)
	}
	return offset
}

func (p *QueryBalanceFlowReq) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetEndDate() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 101)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.EndDate)
	}
	return offset
}

func (p *QueryBalanceFlowReq) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 250)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Offset)
	return offset
}

func (p *QueryBalanceFlowReq) fastWriteField251(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 251)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Limit)
	return offset
}

func (p *QueryBalanceFlowReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *QueryBalanceFlowReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Identity.BLength()
	return l
}

func (p *QueryBalanceFlowReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *QueryBalanceFlowReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *QueryBalanceFlowReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *QueryBalanceFlowReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.AccountInfo.BLength()
	return l
}

func (p *QueryBalanceFlowReq) field100Length() int {
	l := 0
	if p.IsSetStartDate() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *QueryBalanceFlowReq) field101Length() int {
	l := 0
	if p.IsSetEndDate() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *QueryBalanceFlowReq) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *QueryBalanceFlowReq) field251Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *QueryBalanceFlowReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *QueryBalanceFlowReq) DeepCopy(s interface{}) error {
	src, ok := s.(*QueryBalanceFlowReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _identity *fwe_trade_common.BizIdentity
	if src.Identity != nil {
		_identity = &fwe_trade_common.BizIdentity{}
		if err := _identity.DeepCopy(src.Identity); err != nil {
			return err
		}
	}
	p.Identity = _identity

	p.Channel = src.Channel

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	p.FlowCategory = src.FlowCategory

	var _accountInfo *SubMemberInfo
	if src.AccountInfo != nil {
		_accountInfo = &SubMemberInfo{}
		if err := _accountInfo.DeepCopy(src.AccountInfo); err != nil {
			return err
		}
	}
	p.AccountInfo = _accountInfo

	if src.StartDate != nil {
		tmp := *src.StartDate
		p.StartDate = &tmp
	}

	if src.EndDate != nil {
		tmp := *src.EndDate
		p.EndDate = &tmp
	}

	p.Offset = src.Offset

	p.Limit = src.Limit

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *BalanceFlow) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BalanceFlow[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *BalanceFlow) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FrontSeqNo = _field
	return offset, nil
}

func (p *BalanceFlow) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FlowAmount = _field
	return offset, nil
}

func (p *BalanceFlow) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field FlowType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = FlowType(v)
	}
	p.FlowType = _field
	return offset, nil
}

func (p *BalanceFlow) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field FlowSubCategory
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = FlowSubCategory(v)
	}
	p.FlowSubCategory = _field
	return offset, nil
}

func (p *BalanceFlow) FastReadField5(buf []byte) (int, error) {
	offset := 0
	_field := NewSubMemberInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.FromAccount = _field
	return offset, nil
}

func (p *BalanceFlow) FastReadField6(buf []byte) (int, error) {
	offset := 0
	_field := NewSubMemberInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ToAccount = _field
	return offset, nil
}

func (p *BalanceFlow) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.CommonStatus
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.CommonStatus(v)
	}
	p.Status = _field
	return offset, nil
}

func (p *BalanceFlow) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FlowTime = _field
	return offset, nil
}

func (p *BalanceFlow) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Remark = _field
	return offset, nil
}

func (p *BalanceFlow) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *BalanceFlow) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *BalanceFlow) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field10Length()
		l += p.field11Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *BalanceFlow) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FrontSeqNo)
	return offset
}

func (p *BalanceFlow) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.FlowAmount)
	return offset
}

func (p *BalanceFlow) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.FlowType))
	return offset
}

func (p *BalanceFlow) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.FlowSubCategory))
	return offset
}

func (p *BalanceFlow) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 5)
	offset += p.FromAccount.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *BalanceFlow) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 6)
	offset += p.ToAccount.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *BalanceFlow) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.Status))
	return offset
}

func (p *BalanceFlow) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 10)
	offset += thrift.Binary.WriteI64(buf[offset:], p.FlowTime)
	return offset
}

func (p *BalanceFlow) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Remark)
	return offset
}

func (p *BalanceFlow) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FrontSeqNo)
	return l
}

func (p *BalanceFlow) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *BalanceFlow) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *BalanceFlow) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *BalanceFlow) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.FromAccount.BLength()
	return l
}

func (p *BalanceFlow) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ToAccount.BLength()
	return l
}

func (p *BalanceFlow) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *BalanceFlow) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *BalanceFlow) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Remark)
	return l
}

func (p *BalanceFlow) DeepCopy(s interface{}) error {
	src, ok := s.(*BalanceFlow)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.FrontSeqNo != "" {
		p.FrontSeqNo = kutils.StringDeepCopy(src.FrontSeqNo)
	}

	p.FlowAmount = src.FlowAmount

	p.FlowType = src.FlowType

	p.FlowSubCategory = src.FlowSubCategory

	var _fromAccount *SubMemberInfo
	if src.FromAccount != nil {
		_fromAccount = &SubMemberInfo{}
		if err := _fromAccount.DeepCopy(src.FromAccount); err != nil {
			return err
		}
	}
	p.FromAccount = _fromAccount

	var _toAccount *SubMemberInfo
	if src.ToAccount != nil {
		_toAccount = &SubMemberInfo{}
		if err := _toAccount.DeepCopy(src.ToAccount); err != nil {
			return err
		}
	}
	p.ToAccount = _toAccount

	p.Status = src.Status

	p.FlowTime = src.FlowTime

	if src.Remark != "" {
		p.Remark = kutils.StringDeepCopy(src.Remark)
	}

	return nil
}

func (p *QueryBalanceFlowRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 250:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField250(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 251:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField251(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryBalanceFlowRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *QueryBalanceFlowRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*BalanceFlow, 0, size)
	values := make([]BalanceFlow, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.FlowList = _field
	return offset, nil
}

func (p *QueryBalanceFlowRsp) FastReadField250(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Total = _field
	return offset, nil
}

func (p *QueryBalanceFlowRsp) FastReadField251(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.HasMore = _field
	return offset, nil
}

func (p *QueryBalanceFlowRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *QueryBalanceFlowRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *QueryBalanceFlowRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField250(buf[offset:], w)
		offset += p.fastWriteField251(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *QueryBalanceFlowRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field250Length()
		l += p.field251Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *QueryBalanceFlowRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FlowList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *QueryBalanceFlowRsp) fastWriteField250(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 250)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Total)
	return offset
}

func (p *QueryBalanceFlowRsp) fastWriteField251(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 251)
	offset += thrift.Binary.WriteBool(buf[offset:], p.HasMore)
	return offset
}

func (p *QueryBalanceFlowRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *QueryBalanceFlowRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FlowList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *QueryBalanceFlowRsp) field250Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *QueryBalanceFlowRsp) field251Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *QueryBalanceFlowRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *QueryBalanceFlowRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*QueryBalanceFlowRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.FlowList != nil {
		p.FlowList = make([]*BalanceFlow, 0, len(src.FlowList))
		for _, elem := range src.FlowList {
			var _elem *BalanceFlow
			if elem != nil {
				_elem = &BalanceFlow{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.FlowList = append(p.FlowList, _elem)
		}
	}

	p.Total = src.Total

	p.HasMore = src.HasMore

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *DownloadFileReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadFileReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DownloadFileReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DrawCode = _field
	return offset, nil
}

func (p *DownloadFileReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RandomPassword = _field
	return offset, nil
}

func (p *DownloadFileReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *DownloadFileReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DownloadFileReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DownloadFileReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DownloadFileReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.DrawCode)
	return offset
}

func (p *DownloadFileReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RandomPassword)
	return offset
}

func (p *DownloadFileReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DownloadFileReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.DrawCode)
	return l
}

func (p *DownloadFileReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RandomPassword)
	return l
}

func (p *DownloadFileReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *DownloadFileReq) DeepCopy(s interface{}) error {
	src, ok := s.(*DownloadFileReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.DrawCode != "" {
		p.DrawCode = kutils.StringDeepCopy(src.DrawCode)
	}

	if src.RandomPassword != "" {
		p.RandomPassword = kutils.StringDeepCopy(src.RandomPassword)
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *DownloadFileRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DownloadFileRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *DownloadFileRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RawData = _field
	return offset, nil
}

func (p *DownloadFileRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *DownloadFileRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *DownloadFileRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *DownloadFileRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *DownloadFileRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RawData)
	return offset
}

func (p *DownloadFileRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *DownloadFileRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RawData)
	return l
}

func (p *DownloadFileRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *DownloadFileRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*DownloadFileRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RawData != "" {
		p.RawData = kutils.StringDeepCopy(src.RawData)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *TianShuPushReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TianShuPushReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TianShuPushReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *TianShuPushReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Date = _field
	return offset, nil
}

func (p *TianShuPushReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Seq = _field
	return offset, nil
}

func (p *TianShuPushReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FileVersion = _field
	return offset, nil
}

func (p *TianShuPushReq) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBase()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Base = _field
	return offset, nil
}

func (p *TianShuPushReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TianShuPushReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TianShuPushReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field4Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TianShuPushReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *TianShuPushReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetDate() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Date)
	}
	return offset
}

func (p *TianShuPushReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSeq() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 11)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.Seq)
	}
	return offset
}

func (p *TianShuPushReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFileVersion() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 12)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.FileVersion)
	}
	return offset
}

func (p *TianShuPushReq) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBase() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.Base.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TianShuPushReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *TianShuPushReq) field10Length() int {
	l := 0
	if p.IsSetDate() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Date)
	}
	return l
}

func (p *TianShuPushReq) field11Length() int {
	l := 0
	if p.IsSetSeq() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *TianShuPushReq) field12Length() int {
	l := 0
	if p.IsSetFileVersion() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.FileVersion)
	}
	return l
}

func (p *TianShuPushReq) field255Length() int {
	l := 0
	if p.IsSetBase() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Base.BLength()
	}
	return l
}

func (p *TianShuPushReq) DeepCopy(s interface{}) error {
	src, ok := s.(*TianShuPushReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.Date != nil {
		var tmp string
		if *src.Date != "" {
			tmp = kutils.StringDeepCopy(*src.Date)
		}
		p.Date = &tmp
	}

	if src.Seq != nil {
		tmp := *src.Seq
		p.Seq = &tmp
	}

	if src.FileVersion != nil {
		var tmp string
		if *src.FileVersion != "" {
			tmp = kutils.StringDeepCopy(*src.FileVersion)
		}
		p.FileVersion = &tmp
	}

	var _base *base.Base
	if src.Base != nil {
		_base = &base.Base{}
		if err := _base.DeepCopy(src.Base); err != nil {
			return err
		}
	}
	p.Base = _base

	return nil
}

func (p *TianShuPushRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 255:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField255(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TianShuPushRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TianShuPushRsp) FastReadField255(buf []byte) (int, error) {
	offset := 0
	_field := base.NewBaseResp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BaseResp = _field
	return offset, nil
}

func (p *TianShuPushRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TianShuPushRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField255(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TianShuPushRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field255Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TianShuPushRsp) fastWriteField255(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBaseResp() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 255)
		offset += p.BaseResp.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *TianShuPushRsp) field255Length() int {
	l := 0
	if p.IsSetBaseResp() {
		l += thrift.Binary.FieldBeginLength()
		l += p.BaseResp.BLength()
	}
	return l
}

func (p *TianShuPushRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*TianShuPushRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _baseResp *base.BaseResp
	if src.BaseResp != nil {
		_baseResp = &base.BaseResp{}
		if err := _baseResp.DeepCopy(src.BaseResp); err != nil {
			return err
		}
	}
	p.BaseResp = _baseResp

	return nil
}

func (p *PaymentToBServiceRechargeArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceRechargeArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceRechargeArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewRechargeReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceRechargeArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceRechargeArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceRechargeArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceRechargeArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceRechargeArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceRechargeArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceRechargeArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *RechargeReq
	if src.Req != nil {
		_req = &RechargeReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceRechargeResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceRechargeResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceRechargeResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewRechargeRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceRechargeResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceRechargeResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceRechargeResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceRechargeResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceRechargeResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceRechargeResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceRechargeResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *RechargeRsp
	if src.Success != nil {
		_success = &RechargeRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceWithdrawArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceWithdrawArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceWithdrawArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewWithdrawReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceWithdrawArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceWithdrawArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceWithdrawArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceWithdrawArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceWithdrawArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceWithdrawArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceWithdrawArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *WithdrawReq
	if src.Req != nil {
		_req = &WithdrawReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceWithdrawResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceWithdrawResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceWithdrawResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewWithdrawRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceWithdrawResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceWithdrawResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceWithdrawResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceWithdrawResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceWithdrawResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceWithdrawResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceWithdrawResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *WithdrawRsp
	if src.Success != nil {
		_success = &WithdrawRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServicePayArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServicePayArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServicePayArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewPayReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServicePayArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServicePayArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServicePayArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServicePayArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServicePayArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServicePayArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServicePayArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *PayReq
	if src.Req != nil {
		_req = &PayReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServicePayResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServicePayResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServicePayResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewPayRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServicePayResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServicePayResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServicePayResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServicePayResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServicePayResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServicePayResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServicePayResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *PayRsp
	if src.Success != nil {
		_success = &PayRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceRefundArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceRefundArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceRefundArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewRefundReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceRefundArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceRefundArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceRefundArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceRefundArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceRefundArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceRefundArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceRefundArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *RefundReq
	if src.Req != nil {
		_req = &RefundReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceRefundResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceRefundResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceRefundResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewRefundRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceRefundResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceRefundResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceRefundResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceRefundResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceRefundResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceRefundResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceRefundResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *RefundRsp
	if src.Success != nil {
		_success = &RefundRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceSettleArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceSettleArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceSettleArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewSettleReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceSettleArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceSettleArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceSettleArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceSettleArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceSettleArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceSettleArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceSettleArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *SettleReq
	if src.Req != nil {
		_req = &SettleReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceSettleResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceSettleResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceSettleResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewSettleRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceSettleResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceSettleResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceSettleResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceSettleResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceSettleResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceSettleResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceSettleResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *SettleRsp
	if src.Success != nil {
		_success = &SettleRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceReconciliationArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceReconciliationArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceReconciliationArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewReconciliationReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceReconciliationArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceReconciliationArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceReconciliationArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceReconciliationArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceReconciliationArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceReconciliationArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceReconciliationArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *ReconciliationReq
	if src.Req != nil {
		_req = &ReconciliationReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceReconciliationResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceReconciliationResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceReconciliationResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewReconciliationRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceReconciliationResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceReconciliationResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceReconciliationResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceReconciliationResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceReconciliationResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceReconciliationResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceReconciliationResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *ReconciliationRsp
	if src.Success != nil {
		_success = &ReconciliationRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceReconciliationDataArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceReconciliationDataArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceReconciliationDataArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewReconciliationDataReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceReconciliationDataArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceReconciliationDataArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceReconciliationDataArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceReconciliationDataArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceReconciliationDataArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceReconciliationDataArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceReconciliationDataArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *ReconciliationDataReq
	if src.Req != nil {
		_req = &ReconciliationDataReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceReconciliationDataResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceReconciliationDataResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceReconciliationDataResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewReconciliationDataRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceReconciliationDataResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceReconciliationDataResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceReconciliationDataResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceReconciliationDataResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceReconciliationDataResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceReconciliationDataResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceReconciliationDataResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *ReconciliationDataRsp
	if src.Success != nil {
		_success = &ReconciliationDataRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceTianShuPushArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceTianShuPushArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceTianShuPushArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewTianShuPushReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceTianShuPushArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceTianShuPushArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceTianShuPushArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceTianShuPushArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceTianShuPushArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceTianShuPushArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceTianShuPushArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *TianShuPushReq
	if src.Req != nil {
		_req = &TianShuPushReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceTianShuPushResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceTianShuPushResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceTianShuPushResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewTianShuPushRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceTianShuPushResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceTianShuPushResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceTianShuPushResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceTianShuPushResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceTianShuPushResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceTianShuPushResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceTianShuPushResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *TianShuPushRsp
	if src.Success != nil {
		_success = &TianShuPushRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceGetBalanceInfoArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceGetBalanceInfoArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceGetBalanceInfoArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewGetBalanceInfoReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceGetBalanceInfoArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceGetBalanceInfoArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceGetBalanceInfoArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceGetBalanceInfoArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceGetBalanceInfoArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceGetBalanceInfoArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceGetBalanceInfoArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *GetBalanceInfoReq
	if src.Req != nil {
		_req = &GetBalanceInfoReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceGetBalanceInfoResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceGetBalanceInfoResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceGetBalanceInfoResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewGetBalanceInfoRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceGetBalanceInfoResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceGetBalanceInfoResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceGetBalanceInfoResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceGetBalanceInfoResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceGetBalanceInfoResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceGetBalanceInfoResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceGetBalanceInfoResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *GetBalanceInfoRsp
	if src.Success != nil {
		_success = &GetBalanceInfoRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceQueryBalanceFlowArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceQueryBalanceFlowArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceQueryBalanceFlowArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewQueryBalanceFlowReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceQueryBalanceFlowArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceQueryBalanceFlowArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceQueryBalanceFlowArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceQueryBalanceFlowArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceQueryBalanceFlowArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceQueryBalanceFlowArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceQueryBalanceFlowArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *QueryBalanceFlowReq
	if src.Req != nil {
		_req = &QueryBalanceFlowReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceQueryBalanceFlowResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceQueryBalanceFlowResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceQueryBalanceFlowResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewQueryBalanceFlowRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceQueryBalanceFlowResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceQueryBalanceFlowResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceQueryBalanceFlowResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceQueryBalanceFlowResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceQueryBalanceFlowResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceQueryBalanceFlowResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceQueryBalanceFlowResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *QueryBalanceFlowRsp
	if src.Success != nil {
		_success = &QueryBalanceFlowRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceQueryCodeArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceQueryCodeArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceQueryCodeArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewQueryCodeReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceQueryCodeArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceQueryCodeArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceQueryCodeArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceQueryCodeArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceQueryCodeArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceQueryCodeArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceQueryCodeArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *QueryCodeReq
	if src.Req != nil {
		_req = &QueryCodeReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceQueryCodeResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceQueryCodeResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceQueryCodeResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewQueryCodeRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceQueryCodeResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceQueryCodeResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceQueryCodeResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceQueryCodeResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceQueryCodeResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceQueryCodeResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceQueryCodeResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *QueryCodeRsp
	if src.Success != nil {
		_success = &QueryCodeRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceMGetPayListArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceMGetPayListArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceMGetPayListArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewMGetPayListReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceMGetPayListArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceMGetPayListArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceMGetPayListArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceMGetPayListArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceMGetPayListArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceMGetPayListArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceMGetPayListArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *MGetPayListReq
	if src.Req != nil {
		_req = &MGetPayListReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceMGetPayListResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceMGetPayListResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceMGetPayListResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewMGetPayListRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceMGetPayListResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceMGetPayListResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceMGetPayListResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceMGetPayListResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceMGetPayListResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceMGetPayListResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceMGetPayListResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *MGetPayListRsp
	if src.Success != nil {
		_success = &MGetPayListRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceJstRawHTTPArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceJstRawHTTPArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceJstRawHTTPArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewJstRawHTTPReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceJstRawHTTPArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceJstRawHTTPArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceJstRawHTTPArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceJstRawHTTPArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceJstRawHTTPArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceJstRawHTTPArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceJstRawHTTPArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *JstRawHTTPReq
	if src.Req != nil {
		_req = &JstRawHTTPReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceJstRawHTTPResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceJstRawHTTPResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceJstRawHTTPResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewJstRawHTTPRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceJstRawHTTPResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceJstRawHTTPResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceJstRawHTTPResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceJstRawHTTPResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceJstRawHTTPResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceJstRawHTTPResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceJstRawHTTPResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *JstRawHTTPRsp
	if src.Success != nil {
		_success = &JstRawHTTPRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceAsyncCallArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceAsyncCallArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceAsyncCallArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewAsyncCallReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceAsyncCallArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceAsyncCallArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceAsyncCallArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceAsyncCallArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceAsyncCallArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceAsyncCallArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceAsyncCallArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *AsyncCallReq
	if src.Req != nil {
		_req = &AsyncCallReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceAsyncCallResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceAsyncCallResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceAsyncCallResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewAsyncCallRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceAsyncCallResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceAsyncCallResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceAsyncCallResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceAsyncCallResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceAsyncCallResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceAsyncCallResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceAsyncCallResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *AsyncCallRsp
	if src.Success != nil {
		_success = &AsyncCallRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServicePaymentTobBizCallArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServicePaymentTobBizCallArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServicePaymentTobBizCallArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewPaymentTobBizCallReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServicePaymentTobBizCallArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServicePaymentTobBizCallArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServicePaymentTobBizCallArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServicePaymentTobBizCallArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServicePaymentTobBizCallArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServicePaymentTobBizCallArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServicePaymentTobBizCallArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *PaymentTobBizCallReq
	if src.Req != nil {
		_req = &PaymentTobBizCallReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServicePaymentTobBizCallResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServicePaymentTobBizCallResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServicePaymentTobBizCallResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewPaymentTobBizCallRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServicePaymentTobBizCallResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServicePaymentTobBizCallResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServicePaymentTobBizCallResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServicePaymentTobBizCallResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServicePaymentTobBizCallResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServicePaymentTobBizCallResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServicePaymentTobBizCallResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *PaymentTobBizCallRsp
	if src.Success != nil {
		_success = &PaymentTobBizCallRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceDownloadFileArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceDownloadFileArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceDownloadFileArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewDownloadFileReq()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceDownloadFileArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceDownloadFileArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceDownloadFileArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceDownloadFileArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceDownloadFileArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceDownloadFileArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceDownloadFileArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *DownloadFileReq
	if src.Req != nil {
		_req = &DownloadFileReq{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceDownloadFileResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceDownloadFileResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceDownloadFileResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewDownloadFileRsp()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceDownloadFileResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceDownloadFileResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceDownloadFileResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceDownloadFileResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceDownloadFileResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceDownloadFileResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceDownloadFileResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *DownloadFileRsp
	if src.Success != nil {
		_success = &DownloadFileRsp{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceUnionNotifyCashPayArgs) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceUnionNotifyCashPayArgs[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceUnionNotifyCashPayArgs) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewUnionNotifyRequest()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Req = _field
	return offset, nil
}

func (p *PaymentToBServiceUnionNotifyCashPayArgs) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceUnionNotifyCashPayArgs) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceUnionNotifyCashPayArgs) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceUnionNotifyCashPayArgs) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.Req.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *PaymentToBServiceUnionNotifyCashPayArgs) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Req.BLength()
	return l
}

func (p *PaymentToBServiceUnionNotifyCashPayArgs) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceUnionNotifyCashPayArgs)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _req *UnionNotifyRequest
	if src.Req != nil {
		_req = &UnionNotifyRequest{}
		if err := _req.DeepCopy(src.Req); err != nil {
			return err
		}
	}
	p.Req = _req

	return nil
}

func (p *PaymentToBServiceUnionNotifyCashPayResult) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField0(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PaymentToBServiceUnionNotifyCashPayResult[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PaymentToBServiceUnionNotifyCashPayResult) FastReadField0(buf []byte) (int, error) {
	offset := 0
	_field := NewUnionNotifyResponse()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Success = _field
	return offset, nil
}

func (p *PaymentToBServiceUnionNotifyCashPayResult) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PaymentToBServiceUnionNotifyCashPayResult) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField0(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PaymentToBServiceUnionNotifyCashPayResult) BLength() int {
	l := 0
	if p != nil {
		l += p.field0Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PaymentToBServiceUnionNotifyCashPayResult) fastWriteField0(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSuccess() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 0)
		offset += p.Success.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PaymentToBServiceUnionNotifyCashPayResult) field0Length() int {
	l := 0
	if p.IsSetSuccess() {
		l += thrift.Binary.FieldBeginLength()
		l += p.Success.BLength()
	}
	return l
}

func (p *PaymentToBServiceUnionNotifyCashPayResult) DeepCopy(s interface{}) error {
	src, ok := s.(*PaymentToBServiceUnionNotifyCashPayResult)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _success *UnionNotifyResponse
	if src.Success != nil {
		_success = &UnionNotifyResponse{}
		if err := _success.DeepCopy(src.Success); err != nil {
			return err
		}
	}
	p.Success = _success

	return nil
}

func (p *PaymentToBServiceRechargeArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceRechargeResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceWithdrawArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceWithdrawResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServicePayArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServicePayResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceRefundArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceRefundResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceSettleArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceSettleResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceReconciliationArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceReconciliationResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceReconciliationDataArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceReconciliationDataResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceTianShuPushArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceTianShuPushResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceGetBalanceInfoArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceGetBalanceInfoResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceQueryBalanceFlowArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceQueryBalanceFlowResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceQueryCodeArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceQueryCodeResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceMGetPayListArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceMGetPayListResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceJstRawHTTPArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceJstRawHTTPResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceAsyncCallArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceAsyncCallResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServicePaymentTobBizCallArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServicePaymentTobBizCallResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceDownloadFileArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceDownloadFileResult) GetResult() interface{} {
	return p.Success
}

func (p *PaymentToBServiceUnionNotifyCashPayArgs) GetFirstArgument() interface{} {
	return p.Req
}

func (p *PaymentToBServiceUnionNotifyCashPayResult) GetResult() interface{} {
	return p.Success
}

func (p *RechargeReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *WithdrawReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *PayReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *RefundReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *SettleReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *ReconciliationReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *ReconciliationDataReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *AsyncCallReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *QueryCodeReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *MGetPayListReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *PaymentTobBizCallReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *JstRawHTTPReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *UnionNotifyRequest) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *GetBalanceInfoReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *QueryBalanceFlowReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *DownloadFileReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *TianShuPushReq) GetOrSetBase() interface{} {
	if p.Base == nil {
		p.Base = base.NewBase()
	}
	return p.Base
}

func (p *RechargeRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *WithdrawRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *PayRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *RefundRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *SettleRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *ReconciliationRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *ReconciliationDataRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *AsyncCallRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *QueryCodeRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *MGetPayListRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *PaymentTobBizCallRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *JstRawHTTPRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *UnionNotifyResponse) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *GetBalanceInfoRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *QueryBalanceFlowRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *DownloadFileRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}

func (p *TianShuPushRsp) GetOrSetBaseResp() interface{} {
	if p.BaseResp == nil {
		p.BaseResp = base.NewBaseResp()
	}
	return p.BaseResp
}
