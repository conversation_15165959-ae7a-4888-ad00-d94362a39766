// Code generated by Kitex v1.20.3. DO NOT EDIT.

package tradeorderservice

import (
	client "code.byted.org/kite/kitex/client"
	order "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"QueryOrderList": kitex.NewMethodInfo(
		queryOrderListHandler,
		newTradeOrderServiceQueryOrderListArgs,
		newTradeOrderServiceQueryOrderListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetOrderInfo": kitex.NewMethodInfo(
		mGetOrderInfoHandler,
		newTradeOrderServiceMGetOrderInfoArgs,
		newTradeOrderServiceMGetOrderInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryOrderCont": kitex.NewMethodInfo(
		queryOrderContHandler,
		newTradeOrderServiceQueryOrderContArgs,
		newTradeOrderServiceQueryOrderContResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryOrderByContSerial": kitex.NewMethodInfo(
		queryOrderByContSerialHandler,
		newTradeOrderServiceQueryOrderByContSerialArgs,
		newTradeOrderServiceQueryOrderByContSerialResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetOrderLog": kitex.NewMethodInfo(
		getOrderLogHandler,
		newTradeOrderServiceGetOrderLogArgs,
		newTradeOrderServiceGetOrderLogResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryOrdersBySettleOrderNo": kitex.NewMethodInfo(
		queryOrdersBySettleOrderNoHandler,
		newTradeOrderServiceQueryOrdersBySettleOrderNoArgs,
		newTradeOrderServiceQueryOrdersBySettleOrderNoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryOrdersByWithdrawOrderNo": kitex.NewMethodInfo(
		queryOrdersByWithdrawOrderNoHandler,
		newTradeOrderServiceQueryOrdersByWithdrawOrderNoArgs,
		newTradeOrderServiceQueryOrdersByWithdrawOrderNoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateOrder": kitex.NewMethodInfo(
		createOrderHandler,
		newTradeOrderServiceCreateOrderArgs,
		newTradeOrderServiceCreateOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateOrder": kitex.NewMethodInfo(
		updateOrderHandler,
		newTradeOrderServiceUpdateOrderArgs,
		newTradeOrderServiceUpdateOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateFinanceOrder": kitex.NewMethodInfo(
		createFinanceOrderHandler,
		newTradeOrderServiceCreateFinanceOrderArgs,
		newTradeOrderServiceCreateFinanceOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateFinanceOrder": kitex.NewMethodInfo(
		updateFinanceOrderHandler,
		newTradeOrderServiceUpdateFinanceOrderArgs,
		newTradeOrderServiceUpdateFinanceOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetFulfillOrder": kitex.NewMethodInfo(
		getFulfillOrderHandler,
		newTradeOrderServiceGetFulfillOrderArgs,
		newTradeOrderServiceGetFulfillOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryFinanceModelByOrderID": kitex.NewMethodInfo(
		queryFinanceModelByOrderIDHandler,
		newTradeOrderServiceQueryFinanceModelByOrderIDArgs,
		newTradeOrderServiceQueryFinanceModelByOrderIDResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateOrUpdateSubject": kitex.NewMethodInfo(
		createOrUpdateSubjectHandler,
		newTradeOrderServiceCreateOrUpdateSubjectArgs,
		newTradeOrderServiceCreateOrUpdateSubjectResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetAfterSaleOrderInfo": kitex.NewMethodInfo(
		mGetAfterSaleOrderInfoHandler,
		newTradeOrderServiceMGetAfterSaleOrderInfoArgs,
		newTradeOrderServiceMGetAfterSaleOrderInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetAfterSaleOrderLog": kitex.NewMethodInfo(
		getAfterSaleOrderLogHandler,
		newTradeOrderServiceGetAfterSaleOrderLogArgs,
		newTradeOrderServiceGetAfterSaleOrderLogResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryAfterSaleOrder": kitex.NewMethodInfo(
		queryAfterSaleOrderHandler,
		newTradeOrderServiceQueryAfterSaleOrderArgs,
		newTradeOrderServiceQueryAfterSaleOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryFulfillOrder": kitex.NewMethodInfo(
		queryFulfillOrderHandler,
		newTradeOrderServiceQueryFulfillOrderArgs,
		newTradeOrderServiceQueryFulfillOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryWithdrawFlow": kitex.NewMethodInfo(
		queryWithdrawFlowHandler,
		newTradeOrderServiceQueryWithdrawFlowArgs,
		newTradeOrderServiceQueryWithdrawFlowResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	tradeOrderServiceServiceInfo                = NewServiceInfo()
	tradeOrderServiceServiceInfoForClient       = NewServiceInfoForClient()
	tradeOrderServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return tradeOrderServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return tradeOrderServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return tradeOrderServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "TradeOrderService"
	handlerType := (*order.TradeOrderService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "order",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func queryOrderListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceQueryOrderListArgs)
	realResult := result.(*order.TradeOrderServiceQueryOrderListResult)
	success, err := handler.(order.TradeOrderService).QueryOrderList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceQueryOrderListArgs() interface{} {
	return order.NewTradeOrderServiceQueryOrderListArgs()
}

func newTradeOrderServiceQueryOrderListResult() interface{} {
	return order.NewTradeOrderServiceQueryOrderListResult()
}

func mGetOrderInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceMGetOrderInfoArgs)
	realResult := result.(*order.TradeOrderServiceMGetOrderInfoResult)
	success, err := handler.(order.TradeOrderService).MGetOrderInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceMGetOrderInfoArgs() interface{} {
	return order.NewTradeOrderServiceMGetOrderInfoArgs()
}

func newTradeOrderServiceMGetOrderInfoResult() interface{} {
	return order.NewTradeOrderServiceMGetOrderInfoResult()
}

func queryOrderContHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceQueryOrderContArgs)
	realResult := result.(*order.TradeOrderServiceQueryOrderContResult)
	success, err := handler.(order.TradeOrderService).QueryOrderCont(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceQueryOrderContArgs() interface{} {
	return order.NewTradeOrderServiceQueryOrderContArgs()
}

func newTradeOrderServiceQueryOrderContResult() interface{} {
	return order.NewTradeOrderServiceQueryOrderContResult()
}

func queryOrderByContSerialHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceQueryOrderByContSerialArgs)
	realResult := result.(*order.TradeOrderServiceQueryOrderByContSerialResult)
	success, err := handler.(order.TradeOrderService).QueryOrderByContSerial(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceQueryOrderByContSerialArgs() interface{} {
	return order.NewTradeOrderServiceQueryOrderByContSerialArgs()
}

func newTradeOrderServiceQueryOrderByContSerialResult() interface{} {
	return order.NewTradeOrderServiceQueryOrderByContSerialResult()
}

func getOrderLogHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceGetOrderLogArgs)
	realResult := result.(*order.TradeOrderServiceGetOrderLogResult)
	success, err := handler.(order.TradeOrderService).GetOrderLog(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceGetOrderLogArgs() interface{} {
	return order.NewTradeOrderServiceGetOrderLogArgs()
}

func newTradeOrderServiceGetOrderLogResult() interface{} {
	return order.NewTradeOrderServiceGetOrderLogResult()
}

func queryOrdersBySettleOrderNoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceQueryOrdersBySettleOrderNoArgs)
	realResult := result.(*order.TradeOrderServiceQueryOrdersBySettleOrderNoResult)
	success, err := handler.(order.TradeOrderService).QueryOrdersBySettleOrderNo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceQueryOrdersBySettleOrderNoArgs() interface{} {
	return order.NewTradeOrderServiceQueryOrdersBySettleOrderNoArgs()
}

func newTradeOrderServiceQueryOrdersBySettleOrderNoResult() interface{} {
	return order.NewTradeOrderServiceQueryOrdersBySettleOrderNoResult()
}

func queryOrdersByWithdrawOrderNoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceQueryOrdersByWithdrawOrderNoArgs)
	realResult := result.(*order.TradeOrderServiceQueryOrdersByWithdrawOrderNoResult)
	success, err := handler.(order.TradeOrderService).QueryOrdersByWithdrawOrderNo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceQueryOrdersByWithdrawOrderNoArgs() interface{} {
	return order.NewTradeOrderServiceQueryOrdersByWithdrawOrderNoArgs()
}

func newTradeOrderServiceQueryOrdersByWithdrawOrderNoResult() interface{} {
	return order.NewTradeOrderServiceQueryOrdersByWithdrawOrderNoResult()
}

func createOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceCreateOrderArgs)
	realResult := result.(*order.TradeOrderServiceCreateOrderResult)
	success, err := handler.(order.TradeOrderService).CreateOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceCreateOrderArgs() interface{} {
	return order.NewTradeOrderServiceCreateOrderArgs()
}

func newTradeOrderServiceCreateOrderResult() interface{} {
	return order.NewTradeOrderServiceCreateOrderResult()
}

func updateOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceUpdateOrderArgs)
	realResult := result.(*order.TradeOrderServiceUpdateOrderResult)
	success, err := handler.(order.TradeOrderService).UpdateOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceUpdateOrderArgs() interface{} {
	return order.NewTradeOrderServiceUpdateOrderArgs()
}

func newTradeOrderServiceUpdateOrderResult() interface{} {
	return order.NewTradeOrderServiceUpdateOrderResult()
}

func createFinanceOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceCreateFinanceOrderArgs)
	realResult := result.(*order.TradeOrderServiceCreateFinanceOrderResult)
	success, err := handler.(order.TradeOrderService).CreateFinanceOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceCreateFinanceOrderArgs() interface{} {
	return order.NewTradeOrderServiceCreateFinanceOrderArgs()
}

func newTradeOrderServiceCreateFinanceOrderResult() interface{} {
	return order.NewTradeOrderServiceCreateFinanceOrderResult()
}

func updateFinanceOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceUpdateFinanceOrderArgs)
	realResult := result.(*order.TradeOrderServiceUpdateFinanceOrderResult)
	success, err := handler.(order.TradeOrderService).UpdateFinanceOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceUpdateFinanceOrderArgs() interface{} {
	return order.NewTradeOrderServiceUpdateFinanceOrderArgs()
}

func newTradeOrderServiceUpdateFinanceOrderResult() interface{} {
	return order.NewTradeOrderServiceUpdateFinanceOrderResult()
}

func getFulfillOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceGetFulfillOrderArgs)
	realResult := result.(*order.TradeOrderServiceGetFulfillOrderResult)
	success, err := handler.(order.TradeOrderService).GetFulfillOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceGetFulfillOrderArgs() interface{} {
	return order.NewTradeOrderServiceGetFulfillOrderArgs()
}

func newTradeOrderServiceGetFulfillOrderResult() interface{} {
	return order.NewTradeOrderServiceGetFulfillOrderResult()
}

func queryFinanceModelByOrderIDHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceQueryFinanceModelByOrderIDArgs)
	realResult := result.(*order.TradeOrderServiceQueryFinanceModelByOrderIDResult)
	success, err := handler.(order.TradeOrderService).QueryFinanceModelByOrderID(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceQueryFinanceModelByOrderIDArgs() interface{} {
	return order.NewTradeOrderServiceQueryFinanceModelByOrderIDArgs()
}

func newTradeOrderServiceQueryFinanceModelByOrderIDResult() interface{} {
	return order.NewTradeOrderServiceQueryFinanceModelByOrderIDResult()
}

func createOrUpdateSubjectHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceCreateOrUpdateSubjectArgs)
	realResult := result.(*order.TradeOrderServiceCreateOrUpdateSubjectResult)
	success, err := handler.(order.TradeOrderService).CreateOrUpdateSubject(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceCreateOrUpdateSubjectArgs() interface{} {
	return order.NewTradeOrderServiceCreateOrUpdateSubjectArgs()
}

func newTradeOrderServiceCreateOrUpdateSubjectResult() interface{} {
	return order.NewTradeOrderServiceCreateOrUpdateSubjectResult()
}

func mGetAfterSaleOrderInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceMGetAfterSaleOrderInfoArgs)
	realResult := result.(*order.TradeOrderServiceMGetAfterSaleOrderInfoResult)
	success, err := handler.(order.TradeOrderService).MGetAfterSaleOrderInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceMGetAfterSaleOrderInfoArgs() interface{} {
	return order.NewTradeOrderServiceMGetAfterSaleOrderInfoArgs()
}

func newTradeOrderServiceMGetAfterSaleOrderInfoResult() interface{} {
	return order.NewTradeOrderServiceMGetAfterSaleOrderInfoResult()
}

func getAfterSaleOrderLogHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceGetAfterSaleOrderLogArgs)
	realResult := result.(*order.TradeOrderServiceGetAfterSaleOrderLogResult)
	success, err := handler.(order.TradeOrderService).GetAfterSaleOrderLog(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceGetAfterSaleOrderLogArgs() interface{} {
	return order.NewTradeOrderServiceGetAfterSaleOrderLogArgs()
}

func newTradeOrderServiceGetAfterSaleOrderLogResult() interface{} {
	return order.NewTradeOrderServiceGetAfterSaleOrderLogResult()
}

func queryAfterSaleOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceQueryAfterSaleOrderArgs)
	realResult := result.(*order.TradeOrderServiceQueryAfterSaleOrderResult)
	success, err := handler.(order.TradeOrderService).QueryAfterSaleOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceQueryAfterSaleOrderArgs() interface{} {
	return order.NewTradeOrderServiceQueryAfterSaleOrderArgs()
}

func newTradeOrderServiceQueryAfterSaleOrderResult() interface{} {
	return order.NewTradeOrderServiceQueryAfterSaleOrderResult()
}

func queryFulfillOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceQueryFulfillOrderArgs)
	realResult := result.(*order.TradeOrderServiceQueryFulfillOrderResult)
	success, err := handler.(order.TradeOrderService).QueryFulfillOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceQueryFulfillOrderArgs() interface{} {
	return order.NewTradeOrderServiceQueryFulfillOrderArgs()
}

func newTradeOrderServiceQueryFulfillOrderResult() interface{} {
	return order.NewTradeOrderServiceQueryFulfillOrderResult()
}

func queryWithdrawFlowHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*order.TradeOrderServiceQueryWithdrawFlowArgs)
	realResult := result.(*order.TradeOrderServiceQueryWithdrawFlowResult)
	success, err := handler.(order.TradeOrderService).QueryWithdrawFlow(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newTradeOrderServiceQueryWithdrawFlowArgs() interface{} {
	return order.NewTradeOrderServiceQueryWithdrawFlowArgs()
}

func newTradeOrderServiceQueryWithdrawFlowResult() interface{} {
	return order.NewTradeOrderServiceQueryWithdrawFlowResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) QueryOrderList(ctx context.Context, req *order.QueryOrderListReq) (r *order.QueryOrderListResp, err error) {
	var _args order.TradeOrderServiceQueryOrderListArgs
	_args.Req = req
	var _result order.TradeOrderServiceQueryOrderListResult
	if err = p.c.Call(ctx, "QueryOrderList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetOrderInfo(ctx context.Context, req *order.MGetOrderInfoReq) (r *order.MGetOrderInfoResp, err error) {
	var _args order.TradeOrderServiceMGetOrderInfoArgs
	_args.Req = req
	var _result order.TradeOrderServiceMGetOrderInfoResult
	if err = p.c.Call(ctx, "MGetOrderInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryOrderCont(ctx context.Context, req *order.QueryOrderContReq) (r *order.QueryOrderContResp, err error) {
	var _args order.TradeOrderServiceQueryOrderContArgs
	_args.Req = req
	var _result order.TradeOrderServiceQueryOrderContResult
	if err = p.c.Call(ctx, "QueryOrderCont", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryOrderByContSerial(ctx context.Context, req *order.QueryOrderByContSerialReq) (r *order.QueryOrderByContSerialResp, err error) {
	var _args order.TradeOrderServiceQueryOrderByContSerialArgs
	_args.Req = req
	var _result order.TradeOrderServiceQueryOrderByContSerialResult
	if err = p.c.Call(ctx, "QueryOrderByContSerial", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetOrderLog(ctx context.Context, req *order.GetOrderLogReq) (r *order.GetOrderLogResp, err error) {
	var _args order.TradeOrderServiceGetOrderLogArgs
	_args.Req = req
	var _result order.TradeOrderServiceGetOrderLogResult
	if err = p.c.Call(ctx, "GetOrderLog", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryOrdersBySettleOrderNo(ctx context.Context, req *order.QueryOrdersBySettleOrderNoReq) (r *order.QueryOrdersBySettleOrderNoResp, err error) {
	var _args order.TradeOrderServiceQueryOrdersBySettleOrderNoArgs
	_args.Req = req
	var _result order.TradeOrderServiceQueryOrdersBySettleOrderNoResult
	if err = p.c.Call(ctx, "QueryOrdersBySettleOrderNo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryOrdersByWithdrawOrderNo(ctx context.Context, req *order.QueryOrdersByWithdrawOrderNoReq) (r *order.QueryOrdersByWithdrawOrderNoResp, err error) {
	var _args order.TradeOrderServiceQueryOrdersByWithdrawOrderNoArgs
	_args.Req = req
	var _result order.TradeOrderServiceQueryOrdersByWithdrawOrderNoResult
	if err = p.c.Call(ctx, "QueryOrdersByWithdrawOrderNo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateOrder(ctx context.Context, req *order.CreateOrderReq) (r *order.CreateOrderResp, err error) {
	var _args order.TradeOrderServiceCreateOrderArgs
	_args.Req = req
	var _result order.TradeOrderServiceCreateOrderResult
	if err = p.c.Call(ctx, "CreateOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateOrder(ctx context.Context, req *order.UpdateOrderReq) (r *order.UpdateOrderResp, err error) {
	var _args order.TradeOrderServiceUpdateOrderArgs
	_args.Req = req
	var _result order.TradeOrderServiceUpdateOrderResult
	if err = p.c.Call(ctx, "UpdateOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateFinanceOrder(ctx context.Context, req *order.CreateFinanceOrderReq) (r *order.CreateFinanceOrderResp, err error) {
	var _args order.TradeOrderServiceCreateFinanceOrderArgs
	_args.Req = req
	var _result order.TradeOrderServiceCreateFinanceOrderResult
	if err = p.c.Call(ctx, "CreateFinanceOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateFinanceOrder(ctx context.Context, req *order.UpdateFinanceOrderReq) (r *order.UpdateFinanceOrderResp, err error) {
	var _args order.TradeOrderServiceUpdateFinanceOrderArgs
	_args.Req = req
	var _result order.TradeOrderServiceUpdateFinanceOrderResult
	if err = p.c.Call(ctx, "UpdateFinanceOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetFulfillOrder(ctx context.Context, req *order.GetFulfillOrderReq) (r *order.GetFulfillOrderResp, err error) {
	var _args order.TradeOrderServiceGetFulfillOrderArgs
	_args.Req = req
	var _result order.TradeOrderServiceGetFulfillOrderResult
	if err = p.c.Call(ctx, "GetFulfillOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryFinanceModelByOrderID(ctx context.Context, req *order.QueryFinanceModelByOrderIDReq) (r *order.QueryFinanceModelByOrderIDResp, err error) {
	var _args order.TradeOrderServiceQueryFinanceModelByOrderIDArgs
	_args.Req = req
	var _result order.TradeOrderServiceQueryFinanceModelByOrderIDResult
	if err = p.c.Call(ctx, "QueryFinanceModelByOrderID", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateOrUpdateSubject(ctx context.Context, req *order.CreateOrUpdateSubjectReq) (r *order.CreateOrUpdateSubjectResp, err error) {
	var _args order.TradeOrderServiceCreateOrUpdateSubjectArgs
	_args.Req = req
	var _result order.TradeOrderServiceCreateOrUpdateSubjectResult
	if err = p.c.Call(ctx, "CreateOrUpdateSubject", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetAfterSaleOrderInfo(ctx context.Context, req *order.MGetAfterSaleOrderInfoReq) (r *order.MGetAfterSaleOrderInfoResp, err error) {
	var _args order.TradeOrderServiceMGetAfterSaleOrderInfoArgs
	_args.Req = req
	var _result order.TradeOrderServiceMGetAfterSaleOrderInfoResult
	if err = p.c.Call(ctx, "MGetAfterSaleOrderInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetAfterSaleOrderLog(ctx context.Context, req *order.GetAfterSaleOrderLogReq) (r *order.GetAfterSaleOrderLogResp, err error) {
	var _args order.TradeOrderServiceGetAfterSaleOrderLogArgs
	_args.Req = req
	var _result order.TradeOrderServiceGetAfterSaleOrderLogResult
	if err = p.c.Call(ctx, "GetAfterSaleOrderLog", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryAfterSaleOrder(ctx context.Context, req *order.QueryAfterSaleOrderReq) (r *order.QueryAfterSaleOrderResp, err error) {
	var _args order.TradeOrderServiceQueryAfterSaleOrderArgs
	_args.Req = req
	var _result order.TradeOrderServiceQueryAfterSaleOrderResult
	if err = p.c.Call(ctx, "QueryAfterSaleOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryFulfillOrder(ctx context.Context, req *order.QueryFulfillOrderReq) (r *order.QueryFulfillOrderResp, err error) {
	var _args order.TradeOrderServiceQueryFulfillOrderArgs
	_args.Req = req
	var _result order.TradeOrderServiceQueryFulfillOrderResult
	if err = p.c.Call(ctx, "QueryFulfillOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryWithdrawFlow(ctx context.Context, req *order.QueryWithdrawFlowReq) (r *order.QueryWithdrawFlowResp, err error) {
	var _args order.TradeOrderServiceQueryWithdrawFlowArgs
	_args.Req = req
	var _result order.TradeOrderServiceQueryWithdrawFlowResult
	if err = p.c.Call(ctx, "QueryWithdrawFlow", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
