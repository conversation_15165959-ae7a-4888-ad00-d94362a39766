// Code generated by Kitex v1.20.3. DO NOT EDIT.

package tradeorderservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	order "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/order"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	QueryOrderList(ctx context.Context, req *order.QueryOrderListReq, callOptions ...callopt.Option) (r *order.QueryOrderListResp, err error)
	MGetOrderInfo(ctx context.Context, req *order.MGetOrderInfoReq, callOptions ...callopt.Option) (r *order.MGetOrderInfoResp, err error)
	QueryOrderCont(ctx context.Context, req *order.QueryOrderContReq, callOptions ...callopt.Option) (r *order.QueryOrderContResp, err error)
	QueryOrderByContSerial(ctx context.Context, req *order.QueryOrderByContSerialReq, callOptions ...callopt.Option) (r *order.QueryOrderByContSerialResp, err error)
	GetOrderLog(ctx context.Context, req *order.GetOrderLogReq, callOptions ...callopt.Option) (r *order.GetOrderLogResp, err error)
	QueryOrdersBySettleOrderNo(ctx context.Context, req *order.QueryOrdersBySettleOrderNoReq, callOptions ...callopt.Option) (r *order.QueryOrdersBySettleOrderNoResp, err error)
	QueryOrdersByWithdrawOrderNo(ctx context.Context, req *order.QueryOrdersByWithdrawOrderNoReq, callOptions ...callopt.Option) (r *order.QueryOrdersByWithdrawOrderNoResp, err error)
	CreateOrder(ctx context.Context, req *order.CreateOrderReq, callOptions ...callopt.Option) (r *order.CreateOrderResp, err error)
	UpdateOrder(ctx context.Context, req *order.UpdateOrderReq, callOptions ...callopt.Option) (r *order.UpdateOrderResp, err error)
	CreateFinanceOrder(ctx context.Context, req *order.CreateFinanceOrderReq, callOptions ...callopt.Option) (r *order.CreateFinanceOrderResp, err error)
	UpdateFinanceOrder(ctx context.Context, req *order.UpdateFinanceOrderReq, callOptions ...callopt.Option) (r *order.UpdateFinanceOrderResp, err error)
	GetFulfillOrder(ctx context.Context, req *order.GetFulfillOrderReq, callOptions ...callopt.Option) (r *order.GetFulfillOrderResp, err error)
	QueryFinanceModelByOrderID(ctx context.Context, req *order.QueryFinanceModelByOrderIDReq, callOptions ...callopt.Option) (r *order.QueryFinanceModelByOrderIDResp, err error)
	CreateOrUpdateSubject(ctx context.Context, req *order.CreateOrUpdateSubjectReq, callOptions ...callopt.Option) (r *order.CreateOrUpdateSubjectResp, err error)
	MGetAfterSaleOrderInfo(ctx context.Context, req *order.MGetAfterSaleOrderInfoReq, callOptions ...callopt.Option) (r *order.MGetAfterSaleOrderInfoResp, err error)
	GetAfterSaleOrderLog(ctx context.Context, req *order.GetAfterSaleOrderLogReq, callOptions ...callopt.Option) (r *order.GetAfterSaleOrderLogResp, err error)
	QueryAfterSaleOrder(ctx context.Context, req *order.QueryAfterSaleOrderReq, callOptions ...callopt.Option) (r *order.QueryAfterSaleOrderResp, err error)
	QueryFulfillOrder(ctx context.Context, req *order.QueryFulfillOrderReq, callOptions ...callopt.Option) (r *order.QueryFulfillOrderResp, err error)
	QueryWithdrawFlow(ctx context.Context, req *order.QueryWithdrawFlowReq, callOptions ...callopt.Option) (r *order.QueryWithdrawFlowResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kTradeOrderServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kTradeOrderServiceClient struct {
	*kClient
}

func (p *kTradeOrderServiceClient) QueryOrderList(ctx context.Context, req *order.QueryOrderListReq, callOptions ...callopt.Option) (r *order.QueryOrderListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryOrderList(ctx, req)
}

func (p *kTradeOrderServiceClient) MGetOrderInfo(ctx context.Context, req *order.MGetOrderInfoReq, callOptions ...callopt.Option) (r *order.MGetOrderInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetOrderInfo(ctx, req)
}

func (p *kTradeOrderServiceClient) QueryOrderCont(ctx context.Context, req *order.QueryOrderContReq, callOptions ...callopt.Option) (r *order.QueryOrderContResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryOrderCont(ctx, req)
}

func (p *kTradeOrderServiceClient) QueryOrderByContSerial(ctx context.Context, req *order.QueryOrderByContSerialReq, callOptions ...callopt.Option) (r *order.QueryOrderByContSerialResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryOrderByContSerial(ctx, req)
}

func (p *kTradeOrderServiceClient) GetOrderLog(ctx context.Context, req *order.GetOrderLogReq, callOptions ...callopt.Option) (r *order.GetOrderLogResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetOrderLog(ctx, req)
}

func (p *kTradeOrderServiceClient) QueryOrdersBySettleOrderNo(ctx context.Context, req *order.QueryOrdersBySettleOrderNoReq, callOptions ...callopt.Option) (r *order.QueryOrdersBySettleOrderNoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryOrdersBySettleOrderNo(ctx, req)
}

func (p *kTradeOrderServiceClient) QueryOrdersByWithdrawOrderNo(ctx context.Context, req *order.QueryOrdersByWithdrawOrderNoReq, callOptions ...callopt.Option) (r *order.QueryOrdersByWithdrawOrderNoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryOrdersByWithdrawOrderNo(ctx, req)
}

func (p *kTradeOrderServiceClient) CreateOrder(ctx context.Context, req *order.CreateOrderReq, callOptions ...callopt.Option) (r *order.CreateOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateOrder(ctx, req)
}

func (p *kTradeOrderServiceClient) UpdateOrder(ctx context.Context, req *order.UpdateOrderReq, callOptions ...callopt.Option) (r *order.UpdateOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateOrder(ctx, req)
}

func (p *kTradeOrderServiceClient) CreateFinanceOrder(ctx context.Context, req *order.CreateFinanceOrderReq, callOptions ...callopt.Option) (r *order.CreateFinanceOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateFinanceOrder(ctx, req)
}

func (p *kTradeOrderServiceClient) UpdateFinanceOrder(ctx context.Context, req *order.UpdateFinanceOrderReq, callOptions ...callopt.Option) (r *order.UpdateFinanceOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateFinanceOrder(ctx, req)
}

func (p *kTradeOrderServiceClient) GetFulfillOrder(ctx context.Context, req *order.GetFulfillOrderReq, callOptions ...callopt.Option) (r *order.GetFulfillOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetFulfillOrder(ctx, req)
}

func (p *kTradeOrderServiceClient) QueryFinanceModelByOrderID(ctx context.Context, req *order.QueryFinanceModelByOrderIDReq, callOptions ...callopt.Option) (r *order.QueryFinanceModelByOrderIDResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFinanceModelByOrderID(ctx, req)
}

func (p *kTradeOrderServiceClient) CreateOrUpdateSubject(ctx context.Context, req *order.CreateOrUpdateSubjectReq, callOptions ...callopt.Option) (r *order.CreateOrUpdateSubjectResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateOrUpdateSubject(ctx, req)
}

func (p *kTradeOrderServiceClient) MGetAfterSaleOrderInfo(ctx context.Context, req *order.MGetAfterSaleOrderInfoReq, callOptions ...callopt.Option) (r *order.MGetAfterSaleOrderInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetAfterSaleOrderInfo(ctx, req)
}

func (p *kTradeOrderServiceClient) GetAfterSaleOrderLog(ctx context.Context, req *order.GetAfterSaleOrderLogReq, callOptions ...callopt.Option) (r *order.GetAfterSaleOrderLogResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetAfterSaleOrderLog(ctx, req)
}

func (p *kTradeOrderServiceClient) QueryAfterSaleOrder(ctx context.Context, req *order.QueryAfterSaleOrderReq, callOptions ...callopt.Option) (r *order.QueryAfterSaleOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryAfterSaleOrder(ctx, req)
}

func (p *kTradeOrderServiceClient) QueryFulfillOrder(ctx context.Context, req *order.QueryFulfillOrderReq, callOptions ...callopt.Option) (r *order.QueryFulfillOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFulfillOrder(ctx, req)
}

func (p *kTradeOrderServiceClient) QueryWithdrawFlow(ctx context.Context, req *order.QueryWithdrawFlowReq, callOptions ...callopt.Option) (r *order.QueryWithdrawFlowResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryWithdrawFlow(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kTradeOrderServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
