// Code generated by Kitex v1.20.3. DO NOT EDIT.

package execution_common

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"

	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	core0 "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_invoice/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/douyin"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
)

var (
	_ = core.KitexUnusedProtection
	_ = core0.KitexUnusedProtection
	_ = douyin.KitexUnusedProtection
	_ = payment.KitexUnusedProtection
	_ = fwe_trade_common.KitexUnusedProtection
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *CreateFinanceReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateFinanceReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CreateFinanceReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.FinanceInfo, 0, size)
	values := make([]fwe_trade_common.FinanceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.FinanceList = _field
	return offset, nil
}

func (p *CreateFinanceReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *CreateFinanceReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *CreateFinanceReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateFinanceReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateFinanceReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateFinanceReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FinanceList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *CreateFinanceReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *CreateFinanceReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *CreateFinanceReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FinanceList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *CreateFinanceReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *CreateFinanceReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *CreateFinanceReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CreateFinanceReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.FinanceList != nil {
		p.FinanceList = make([]*fwe_trade_common.FinanceInfo, 0, len(src.FinanceList))
		for _, elem := range src.FinanceList {
			var _elem *fwe_trade_common.FinanceInfo
			if elem != nil {
				_elem = &fwe_trade_common.FinanceInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.FinanceList = append(p.FinanceList, _elem)
		}
	}

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *CreateFinanceRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CreateFinanceRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CreateFinanceRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CreateFinanceRsp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CreateFinanceRsp) DeepCopy(s interface{}) error {

	return nil
}

func (p *UpdateOrderReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 50:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField50(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 51:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField51(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 52:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField52(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 53:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField53(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 54:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField54(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 55:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField55(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 56:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField56(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 57:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField57(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 201:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField201(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 202:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField202(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateOrderReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *UpdateOrderReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.OrderName = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.OrderDesc = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TotalAmount = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FireCondition = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField50(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewProductInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ProductInfo = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField51(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BuyerInfo = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField52(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.SellerInfo = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField53(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ServiceProviderInfo = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField54(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.TalentInfo = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField55(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.FinanceInfo, 0, size)
	values := make([]fwe_trade_common.FinanceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.UpsertFinanceList = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField56(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.DeleteFinanceList = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField57(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewPurchasePlan()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.PurchasePlan = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField201(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Extra = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastReadField202(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewOperatorInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.Operator = _field
	return offset, nil
}

func (p *UpdateOrderReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpdateOrderReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField50(buf[offset:], w)
		offset += p.fastWriteField51(buf[offset:], w)
		offset += p.fastWriteField52(buf[offset:], w)
		offset += p.fastWriteField53(buf[offset:], w)
		offset += p.fastWriteField54(buf[offset:], w)
		offset += p.fastWriteField55(buf[offset:], w)
		offset += p.fastWriteField56(buf[offset:], w)
		offset += p.fastWriteField57(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
		offset += p.fastWriteField201(buf[offset:], w)
		offset += p.fastWriteField202(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpdateOrderReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field50Length()
		l += p.field51Length()
		l += p.field52Length()
		l += p.field53Length()
		l += p.field54Length()
		l += p.field55Length()
		l += p.field56Length()
		l += p.field57Length()
		l += p.field200Length()
		l += p.field201Length()
		l += p.field202Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpdateOrderReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderName() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.OrderName)
	}
	return offset
}

func (p *UpdateOrderReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderDesc() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.OrderDesc)
	}
	return offset
}

func (p *UpdateOrderReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTotalAmount() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.TotalAmount)
	}
	return offset
}

func (p *UpdateOrderReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFireCondition() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.FireCondition)
	}
	return offset
}

func (p *UpdateOrderReq) fastWriteField50(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 50)
	offset += p.ProductInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpdateOrderReq) fastWriteField51(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 51)
	offset += p.BuyerInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpdateOrderReq) fastWriteField52(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 52)
	offset += p.SellerInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpdateOrderReq) fastWriteField53(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 53)
	offset += p.ServiceProviderInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpdateOrderReq) fastWriteField54(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 54)
	offset += p.TalentInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpdateOrderReq) fastWriteField55(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 55)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.UpsertFinanceList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *UpdateOrderReq) fastWriteField56(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 56)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.DeleteFinanceList {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *UpdateOrderReq) fastWriteField57(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 57)
	offset += p.PurchasePlan.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpdateOrderReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *UpdateOrderReq) fastWriteField201(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 201)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Extra {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *UpdateOrderReq) fastWriteField202(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 202)
	offset += p.Operator.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UpdateOrderReq) field1Length() int {
	l := 0
	if p.IsSetOrderName() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.OrderName)
	}
	return l
}

func (p *UpdateOrderReq) field2Length() int {
	l := 0
	if p.IsSetOrderDesc() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.OrderDesc)
	}
	return l
}

func (p *UpdateOrderReq) field3Length() int {
	l := 0
	if p.IsSetTotalAmount() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *UpdateOrderReq) field4Length() int {
	l := 0
	if p.IsSetFireCondition() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.FireCondition)
	}
	return l
}

func (p *UpdateOrderReq) field50Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ProductInfo.BLength()
	return l
}

func (p *UpdateOrderReq) field51Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BuyerInfo.BLength()
	return l
}

func (p *UpdateOrderReq) field52Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.SellerInfo.BLength()
	return l
}

func (p *UpdateOrderReq) field53Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ServiceProviderInfo.BLength()
	return l
}

func (p *UpdateOrderReq) field54Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.TalentInfo.BLength()
	return l
}

func (p *UpdateOrderReq) field55Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.UpsertFinanceList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *UpdateOrderReq) field56Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.DeleteFinanceList {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *UpdateOrderReq) field57Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.PurchasePlan.BLength()
	return l
}

func (p *UpdateOrderReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *UpdateOrderReq) field201Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Extra {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *UpdateOrderReq) field202Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.Operator.BLength()
	return l
}

func (p *UpdateOrderReq) DeepCopy(s interface{}) error {
	src, ok := s.(*UpdateOrderReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderName != nil {
		var tmp string
		if *src.OrderName != "" {
			tmp = kutils.StringDeepCopy(*src.OrderName)
		}
		p.OrderName = &tmp
	}

	if src.OrderDesc != nil {
		var tmp string
		if *src.OrderDesc != "" {
			tmp = kutils.StringDeepCopy(*src.OrderDesc)
		}
		p.OrderDesc = &tmp
	}

	if src.TotalAmount != nil {
		tmp := *src.TotalAmount
		p.TotalAmount = &tmp
	}

	if src.FireCondition != nil {
		var tmp string
		if *src.FireCondition != "" {
			tmp = kutils.StringDeepCopy(*src.FireCondition)
		}
		p.FireCondition = &tmp
	}

	var _productInfo *fwe_trade_common.ProductInfo
	if src.ProductInfo != nil {
		_productInfo = &fwe_trade_common.ProductInfo{}
		if err := _productInfo.DeepCopy(src.ProductInfo); err != nil {
			return err
		}
	}
	p.ProductInfo = _productInfo

	var _buyerInfo *fwe_trade_common.TradeSubjectInfo
	if src.BuyerInfo != nil {
		_buyerInfo = &fwe_trade_common.TradeSubjectInfo{}
		if err := _buyerInfo.DeepCopy(src.BuyerInfo); err != nil {
			return err
		}
	}
	p.BuyerInfo = _buyerInfo

	var _sellerInfo *fwe_trade_common.TradeSubjectInfo
	if src.SellerInfo != nil {
		_sellerInfo = &fwe_trade_common.TradeSubjectInfo{}
		if err := _sellerInfo.DeepCopy(src.SellerInfo); err != nil {
			return err
		}
	}
	p.SellerInfo = _sellerInfo

	var _serviceProviderInfo *fwe_trade_common.TradeSubjectInfo
	if src.ServiceProviderInfo != nil {
		_serviceProviderInfo = &fwe_trade_common.TradeSubjectInfo{}
		if err := _serviceProviderInfo.DeepCopy(src.ServiceProviderInfo); err != nil {
			return err
		}
	}
	p.ServiceProviderInfo = _serviceProviderInfo

	var _talentInfo *fwe_trade_common.TradeSubjectInfo
	if src.TalentInfo != nil {
		_talentInfo = &fwe_trade_common.TradeSubjectInfo{}
		if err := _talentInfo.DeepCopy(src.TalentInfo); err != nil {
			return err
		}
	}
	p.TalentInfo = _talentInfo

	if src.UpsertFinanceList != nil {
		p.UpsertFinanceList = make([]*fwe_trade_common.FinanceInfo, 0, len(src.UpsertFinanceList))
		for _, elem := range src.UpsertFinanceList {
			var _elem *fwe_trade_common.FinanceInfo
			if elem != nil {
				_elem = &fwe_trade_common.FinanceInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.UpsertFinanceList = append(p.UpsertFinanceList, _elem)
		}
	}

	if src.DeleteFinanceList != nil {
		p.DeleteFinanceList = make([]string, 0, len(src.DeleteFinanceList))
		for _, elem := range src.DeleteFinanceList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.DeleteFinanceList = append(p.DeleteFinanceList, _elem)
		}
	}

	var _purchasePlan *fwe_trade_common.PurchasePlan
	if src.PurchasePlan != nil {
		_purchasePlan = &fwe_trade_common.PurchasePlan{}
		if err := _purchasePlan.DeepCopy(src.PurchasePlan); err != nil {
			return err
		}
	}
	p.PurchasePlan = _purchasePlan

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	if src.Extra != nil {
		p.Extra = make(map[string]string, len(src.Extra))
		for key, val := range src.Extra {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Extra[_key] = _val
		}
	}

	var _operator *fwe_trade_common.OperatorInfo
	if src.Operator != nil {
		_operator = &fwe_trade_common.OperatorInfo{}
		if err := _operator.DeepCopy(src.Operator); err != nil {
			return err
		}
	}
	p.Operator = _operator

	return nil
}

func (p *UpdateOrderRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *UpdateOrderRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UpdateOrderRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UpdateOrderRsp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UpdateOrderRsp) DeepCopy(s interface{}) error {

	return nil
}

func (p *UnionContCreateReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSignPartyMap bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetSignPartyMap = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetSignPartyMap {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UnionContCreateReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_UnionContCreateReq[fieldId]))
}

func (p *UnionContCreateReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContType = _field
	return offset, nil
}

func (p *UnionContCreateReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContTmplID = _field
	return offset, nil
}

func (p *UnionContCreateReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.ContTmplParams = _field
	return offset, nil
}

func (p *UnionContCreateReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[core.SignPosition]*core.SignParty, size)
	values := make([]core.SignParty, size)
	for i := 0; i < size; i++ {
		var _key core.SignPosition
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_key = core.SignPosition(v)
		}

		_val := &values[i]
		_val.InitDefault()
		if l, err := _val.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field[_key] = _val
	}
	p.SignPartyMap = _field
	return offset, nil
}

func (p *UnionContCreateReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContInAmount = _field
	return offset, nil
}

func (p *UnionContCreateReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContOutAmount = _field
	return offset, nil
}

func (p *UnionContCreateReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAction = _field
	return offset, nil
}

func (p *UnionContCreateReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UnionContCreateReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UnionContCreateReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field100Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UnionContCreateReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.ContType)
	return offset
}

func (p *UnionContCreateReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ContTmplID)
	return offset
}

func (p *UnionContCreateReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 3)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.ContTmplParams {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *UnionContCreateReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 4)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.SignPartyMap {
		length++
		offset += thrift.Binary.WriteI32(buf[offset:], int32(k))
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.I32, thrift.STRUCT, length)
	return offset
}

func (p *UnionContCreateReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 10)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ContInAmount)
	return offset
}

func (p *UnionContCreateReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 11)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ContOutAmount)
	return offset
}

func (p *UnionContCreateReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAction)
	return offset
}

func (p *UnionContCreateReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *UnionContCreateReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *UnionContCreateReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.ContTmplParams {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *UnionContCreateReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.SignPartyMap {
		_, _ = k, v

		l += thrift.Binary.I32Length()
		l += v.BLength()
	}
	return l
}

func (p *UnionContCreateReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *UnionContCreateReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *UnionContCreateReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAction)
	return l
}

func (p *UnionContCreateReq) DeepCopy(s interface{}) error {
	src, ok := s.(*UnionContCreateReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.ContType = src.ContType

	p.ContTmplID = src.ContTmplID

	if src.ContTmplParams != nil {
		p.ContTmplParams = make(map[string]string, len(src.ContTmplParams))
		for key, val := range src.ContTmplParams {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.ContTmplParams[_key] = _val
		}
	}

	if src.SignPartyMap != nil {
		p.SignPartyMap = make(map[core.SignPosition]*core.SignParty, len(src.SignPartyMap))
		for key, val := range src.SignPartyMap {
			var _key core.SignPosition
			_key = key

			var _val *core.SignParty
			if val != nil {
				_val = &core.SignParty{}
				if err := _val.DeepCopy(val); err != nil {
					return err
				}
			}

			p.SignPartyMap[_key] = _val
		}
	}

	p.ContInAmount = src.ContInAmount

	p.ContOutAmount = src.ContOutAmount

	if src.CallbackAction != "" {
		p.CallbackAction = kutils.StringDeepCopy(src.CallbackAction)
	}

	return nil
}

func (p *UnionContCreateRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UnionContCreateRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *UnionContCreateRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContNo = _field
	return offset, nil
}

func (p *UnionContCreateRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[core.SignPosition]string, size)
	for i := 0; i < size; i++ {
		var _key core.SignPosition
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_key = core.SignPosition(v)
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.SignLinks = _field
	return offset, nil
}

func (p *UnionContCreateRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UnionContCreateRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UnionContCreateRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UnionContCreateRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ContNo)
	return offset
}

func (p *UnionContCreateRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 2)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.SignLinks {
		length++
		offset += thrift.Binary.WriteI32(buf[offset:], int32(k))
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.I32, thrift.STRING, length)
	return offset
}

func (p *UnionContCreateRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ContNo)
	return l
}

func (p *UnionContCreateRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.SignLinks {
		_, _ = k, v

		l += thrift.Binary.I32Length()
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *UnionContCreateRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*UnionContCreateRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ContNo != "" {
		p.ContNo = kutils.StringDeepCopy(src.ContNo)
	}

	if src.SignLinks != nil {
		p.SignLinks = make(map[core.SignPosition]string, len(src.SignLinks))
		for key, val := range src.SignLinks {
			var _key core.SignPosition
			_key = key

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.SignLinks[_key] = _val
		}
	}

	return nil
}

func (p *ContSignReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ContSignReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ContSignReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContType = _field
	return offset, nil
}

func (p *ContSignReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContTmplID = _field
	return offset, nil
}

func (p *ContSignReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.ContTmplParams = _field
	return offset, nil
}

func (p *ContSignReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*core.SignPartyData, 0, size)
	values := make([]core.SignPartyData, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.SignPartyList = _field
	return offset, nil
}

func (p *ContSignReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RedirectURL = _field
	return offset, nil
}

func (p *ContSignReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[core.SignPosition]*core.SmsConfig, size)
	values := make([]core.SmsConfig, size)
	for i := 0; i < size; i++ {
		var _key core.SignPosition
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_key = core.SignPosition(v)
		}

		_val := &values[i]
		_val.InitDefault()
		if l, err := _val.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field[_key] = _val
	}
	p.SmsConfigMap = _field
	return offset, nil
}

func (p *ContSignReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAction = _field
	return offset, nil
}

func (p *ContSignReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *ContSignReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ContSignReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ContSignReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field100Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ContSignReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.ContType)
	return offset
}

func (p *ContSignReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ContTmplID)
	return offset
}

func (p *ContSignReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 3)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.ContTmplParams {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *ContSignReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 4)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.SignPartyList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *ContSignReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRedirectURL() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RedirectURL)
	}
	return offset
}

func (p *ContSignReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 6)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.SmsConfigMap {
		length++
		offset += thrift.Binary.WriteI32(buf[offset:], int32(k))
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.I32, thrift.STRUCT, length)
	return offset
}

func (p *ContSignReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAction)
	return offset
}

func (p *ContSignReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *ContSignReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ContSignReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ContSignReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.ContTmplParams {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *ContSignReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.SignPartyList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *ContSignReq) field5Length() int {
	l := 0
	if p.IsSetRedirectURL() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RedirectURL)
	}
	return l
}

func (p *ContSignReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.SmsConfigMap {
		_, _ = k, v

		l += thrift.Binary.I32Length()
		l += v.BLength()
	}
	return l
}

func (p *ContSignReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAction)
	return l
}

func (p *ContSignReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *ContSignReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ContSignReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.ContType = src.ContType

	p.ContTmplID = src.ContTmplID

	if src.ContTmplParams != nil {
		p.ContTmplParams = make(map[string]string, len(src.ContTmplParams))
		for key, val := range src.ContTmplParams {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.ContTmplParams[_key] = _val
		}
	}

	if src.SignPartyList != nil {
		p.SignPartyList = make([]*core.SignPartyData, 0, len(src.SignPartyList))
		for _, elem := range src.SignPartyList {
			var _elem *core.SignPartyData
			if elem != nil {
				_elem = &core.SignPartyData{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.SignPartyList = append(p.SignPartyList, _elem)
		}
	}

	if src.RedirectURL != nil {
		var tmp string
		if *src.RedirectURL != "" {
			tmp = kutils.StringDeepCopy(*src.RedirectURL)
		}
		p.RedirectURL = &tmp
	}

	if src.SmsConfigMap != nil {
		p.SmsConfigMap = make(map[core.SignPosition]*core.SmsConfig, len(src.SmsConfigMap))
		for key, val := range src.SmsConfigMap {
			var _key core.SignPosition
			_key = key

			var _val *core.SmsConfig
			if val != nil {
				_val = &core.SmsConfig{}
				if err := _val.DeepCopy(val); err != nil {
					return err
				}
			}

			p.SmsConfigMap[_key] = _val
		}
	}

	if src.CallbackAction != "" {
		p.CallbackAction = kutils.StringDeepCopy(src.CallbackAction)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *ContSignRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ContSignRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ContSignRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContSerial = _field
	return offset, nil
}

func (p *ContSignRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SignLink = _field
	return offset, nil
}

func (p *ContSignRsp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[core.SignPosition]string, size)
	for i := 0; i < size; i++ {
		var _key core.SignPosition
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_key = core.SignPosition(v)
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.SignPosLinkMap = _field
	return offset, nil
}

func (p *ContSignRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ContSignRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ContSignRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ContSignRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ContSerial)
	return offset
}

func (p *ContSignRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SignLink)
	return offset
}

func (p *ContSignRsp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 3)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.SignPosLinkMap {
		length++
		offset += thrift.Binary.WriteI32(buf[offset:], int32(k))
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.I32, thrift.STRING, length)
	return offset
}

func (p *ContSignRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ContSerial)
	return l
}

func (p *ContSignRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SignLink)
	return l
}

func (p *ContSignRsp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.SignPosLinkMap {
		_, _ = k, v

		l += thrift.Binary.I32Length()
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *ContSignRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*ContSignRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ContSerial != "" {
		p.ContSerial = kutils.StringDeepCopy(src.ContSerial)
	}

	if src.SignLink != "" {
		p.SignLink = kutils.StringDeepCopy(src.SignLink)
	}

	if src.SignPosLinkMap != nil {
		p.SignPosLinkMap = make(map[core.SignPosition]string, len(src.SignPosLinkMap))
		for key, val := range src.SignPosLinkMap {
			var _key core.SignPosition
			_key = key

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.SignPosLinkMap[_key] = _val
		}
	}

	return nil
}

func (p *ContTerminationReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ContTerminationReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ContTerminationReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContType = _field
	return offset, nil
}

func (p *ContTerminationReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SignType = _field
	return offset, nil
}

func (p *ContTerminationReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *ContTerminationReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ContTerminationReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ContTerminationReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ContTerminationReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.ContType)
	return offset
}

func (p *ContTerminationReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSignType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.SignType)
	}
	return offset
}

func (p *ContTerminationReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *ContTerminationReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ContTerminationReq) field2Length() int {
	l := 0
	if p.IsSetSignType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *ContTerminationReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *ContTerminationReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ContTerminationReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.ContType = src.ContType

	if src.SignType != nil {
		tmp := *src.SignType
		p.SignType = &tmp
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *ContTerminationRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ContTerminationRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ContTerminationRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ContTerminationRsp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ContTerminationRsp) DeepCopy(s interface{}) error {

	return nil
}

func (p *CloseContStatusReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CloseContStatusReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CloseContStatusReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContType = _field
	return offset, nil
}

func (p *CloseContStatusReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *CloseContStatusReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CloseContStatusReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CloseContStatusReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CloseContStatusReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.ContType)
	return offset
}

func (p *CloseContStatusReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *CloseContStatusReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *CloseContStatusReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *CloseContStatusReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CloseContStatusReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.ContType = src.ContType

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *CloseContStatusRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CloseContStatusRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CloseContStatusRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CloseContStatusRsp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CloseContStatusRsp) DeepCopy(s interface{}) error {

	return nil
}

func (p *UnionPayReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFinanceOrderType bool = false
	var issetTotalAmount bool = false
	var issetPayTypeList bool = false
	var issetMerchantInfoMap bool = false
	var issetFweAccountID bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetFinanceOrderType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTotalAmount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPayTypeList = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetMerchantInfoMap = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 18:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField18(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 19:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField19(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetFweAccountID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 22:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField22(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField23(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 40:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField40(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField50(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 51:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField51(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 102:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField102(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetFinanceOrderType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTotalAmount {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetPayTypeList {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetMerchantInfoMap {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetFweAccountID {
		fieldId = 20
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UnionPayReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_UnionPayReq[fieldId]))
}

func (p *UnionPayReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceOrderType = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.UserID = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *payment.CurrencyType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := payment.CurrencyType(v)
		_field = &tmp
	}
	p.Currency = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalAmount = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.EstimateSubsidyAmount = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]fwe_trade_common.UnionPayType, 0, size)
	for i := 0; i < size; i++ {
		var _elem fwe_trade_common.UnionPayType
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_elem = fwe_trade_common.UnionPayType(v)
		}

		_field = append(_field, _elem)
	}
	p.PayTypeList = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[fwe_trade_common.UnionPayType]*payment.MerchantInfo, size)
	values := make([]payment.MerchantInfo, size)
	for i := 0; i < size; i++ {
		var _key fwe_trade_common.UnionPayType
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_key = fwe_trade_common.UnionPayType(v)
		}

		_val := &values[i]
		_val.InitDefault()
		if l, err := _val.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field[_key] = _val
	}
	p.MerchantInfoMap = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.POSDeviceInfo, 0, size)
	values := make([]fwe_trade_common.POSDeviceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.PosDeviceList = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField13(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]fwe_trade_common.PayLimitType, 0, size)
	for i := 0; i < size; i++ {
		var _elem fwe_trade_common.PayLimitType
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_elem = fwe_trade_common.PayLimitType(v)
		}

		_field = append(_field, _elem)
	}
	p.PayLimitList = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *fwe_trade_common.CashierDeskType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := fwe_trade_common.CashierDeskType(v)
		_field = &tmp
	}
	p.CashierDeskType = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field *fwe_trade_common.OSType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		tmp := fwe_trade_common.OSType(v)
		_field = &tmp
	}
	p.OsType = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField16(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ExpireTime = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField17(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RedirectURL = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField18(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewPayOfflineCheckData()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.CheckData = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField19(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewJstPayParam()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.JstPayParam = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FweAccountID = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OneStepPay = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField22(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.PayUnionParam = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField23(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceAccountID = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField40(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewUnionPayRiskInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.RiskInfo = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField50(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Extra = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField51(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewSecondPayParam()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.SecondPayParam = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackEvent = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField101(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackExtra = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField102(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CloseCallbackEvent = _field
	return offset, nil
}

func (p *UnionPayReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *UnionPayReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UnionPayReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
		offset += p.fastWriteField18(buf[offset:], w)
		offset += p.fastWriteField19(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField22(buf[offset:], w)
		offset += p.fastWriteField23(buf[offset:], w)
		offset += p.fastWriteField40(buf[offset:], w)
		offset += p.fastWriteField50(buf[offset:], w)
		offset += p.fastWriteField51(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField102(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UnionPayReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
		l += p.field18Length()
		l += p.field19Length()
		l += p.field20Length()
		l += p.field21Length()
		l += p.field22Length()
		l += p.field23Length()
		l += p.field40Length()
		l += p.field50Length()
		l += p.field51Length()
		l += p.field100Length()
		l += p.field101Length()
		l += p.field102Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UnionPayReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], p.FinanceOrderType)
	return offset
}

func (p *UnionPayReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetUserID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.UserID)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCurrency() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 5)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.Currency))
	}
	return offset
}

func (p *UnionPayReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TotalAmount)
	return offset
}

func (p *UnionPayReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetEstimateSubsidyAmount() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 7)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.EstimateSubsidyAmount)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 10)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.PayTypeList {
		length++
		offset += thrift.Binary.WriteI32(buf[offset:], int32(v))
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I32, length)
	return offset
}

func (p *UnionPayReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 11)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.MerchantInfoMap {
		length++
		offset += thrift.Binary.WriteI32(buf[offset:], int32(k))
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.I32, thrift.STRUCT, length)
	return offset
}

func (p *UnionPayReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPosDeviceList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 12)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.PosDeviceList {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPayLimitList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 13)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.PayLimitList {
			length++
			offset += thrift.Binary.WriteI32(buf[offset:], int32(v))
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I32, length)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCashierDeskType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 14)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.CashierDeskType))
	}
	return offset
}

func (p *UnionPayReq) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOsType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 15)
		offset += thrift.Binary.WriteI32(buf[offset:], int32(*p.OsType))
	}
	return offset
}

func (p *UnionPayReq) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetExpireTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 16)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.ExpireTime)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRedirectURL() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 17)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RedirectURL)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField18(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCheckData() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 18)
		offset += p.CheckData.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField19(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetJstPayParam() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 19)
		offset += p.JstPayParam.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 20)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FweAccountID)
	return offset
}

func (p *UnionPayReq) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 21)
	offset += thrift.Binary.WriteBool(buf[offset:], p.OneStepPay)
	return offset
}

func (p *UnionPayReq) fastWriteField22(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPayUnionParam() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 22)
		mapBeginOffset := offset
		offset += thrift.Binary.MapBeginLength()
		var length int
		for k, v := range p.PayUnionParam {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField23(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 23)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FinanceAccountID)
	return offset
}

func (p *UnionPayReq) fastWriteField40(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 40)
	offset += p.RiskInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *UnionPayReq) fastWriteField50(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetExtra() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 50)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Extra)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField51(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSecondPayParam() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 51)
		offset += p.SecondPayParam.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *UnionPayReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackEvent)
	return offset
}

func (p *UnionPayReq) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 101)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackExtra)
	return offset
}

func (p *UnionPayReq) fastWriteField102(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 102)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CloseCallbackEvent)
	return offset
}

func (p *UnionPayReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *UnionPayReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *UnionPayReq) field4Length() int {
	l := 0
	if p.IsSetUserID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.UserID)
	}
	return l
}

func (p *UnionPayReq) field5Length() int {
	l := 0
	if p.IsSetCurrency() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *UnionPayReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *UnionPayReq) field7Length() int {
	l := 0
	if p.IsSetEstimateSubsidyAmount() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *UnionPayReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.PayTypeList {
		_ = v
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *UnionPayReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.MerchantInfoMap {
		_, _ = k, v

		l += thrift.Binary.I32Length()
		l += v.BLength()
	}
	return l
}

func (p *UnionPayReq) field12Length() int {
	l := 0
	if p.IsSetPosDeviceList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.PosDeviceList {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *UnionPayReq) field13Length() int {
	l := 0
	if p.IsSetPayLimitList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.PayLimitList {
			_ = v
			l += thrift.Binary.I32Length()
		}
	}
	return l
}

func (p *UnionPayReq) field14Length() int {
	l := 0
	if p.IsSetCashierDeskType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *UnionPayReq) field15Length() int {
	l := 0
	if p.IsSetOsType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *UnionPayReq) field16Length() int {
	l := 0
	if p.IsSetExpireTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *UnionPayReq) field17Length() int {
	l := 0
	if p.IsSetRedirectURL() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RedirectURL)
	}
	return l
}

func (p *UnionPayReq) field18Length() int {
	l := 0
	if p.IsSetCheckData() {
		l += thrift.Binary.FieldBeginLength()
		l += p.CheckData.BLength()
	}
	return l
}

func (p *UnionPayReq) field19Length() int {
	l := 0
	if p.IsSetJstPayParam() {
		l += thrift.Binary.FieldBeginLength()
		l += p.JstPayParam.BLength()
	}
	return l
}

func (p *UnionPayReq) field20Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FweAccountID)
	return l
}

func (p *UnionPayReq) field21Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *UnionPayReq) field22Length() int {
	l := 0
	if p.IsSetPayUnionParam() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.MapBeginLength()
		for k, v := range p.PayUnionParam {
			_, _ = k, v

			l += thrift.Binary.StringLengthNocopy(k)
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *UnionPayReq) field23Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FinanceAccountID)
	return l
}

func (p *UnionPayReq) field40Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.RiskInfo.BLength()
	return l
}

func (p *UnionPayReq) field50Length() int {
	l := 0
	if p.IsSetExtra() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Extra)
	}
	return l
}

func (p *UnionPayReq) field51Length() int {
	l := 0
	if p.IsSetSecondPayParam() {
		l += thrift.Binary.FieldBeginLength()
		l += p.SecondPayParam.BLength()
	}
	return l
}

func (p *UnionPayReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackEvent)
	return l
}

func (p *UnionPayReq) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackExtra)
	return l
}

func (p *UnionPayReq) field102Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CloseCallbackEvent)
	return l
}

func (p *UnionPayReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *UnionPayReq) DeepCopy(s interface{}) error {
	src, ok := s.(*UnionPayReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.FinanceOrderType = src.FinanceOrderType

	if src.UserID != nil {
		var tmp string
		if *src.UserID != "" {
			tmp = kutils.StringDeepCopy(*src.UserID)
		}
		p.UserID = &tmp
	}

	if src.Currency != nil {
		tmp := *src.Currency
		p.Currency = &tmp
	}

	p.TotalAmount = src.TotalAmount

	if src.EstimateSubsidyAmount != nil {
		tmp := *src.EstimateSubsidyAmount
		p.EstimateSubsidyAmount = &tmp
	}

	if src.PayTypeList != nil {
		p.PayTypeList = make([]fwe_trade_common.UnionPayType, 0, len(src.PayTypeList))
		for _, elem := range src.PayTypeList {
			var _elem fwe_trade_common.UnionPayType
			_elem = elem
			p.PayTypeList = append(p.PayTypeList, _elem)
		}
	}

	if src.MerchantInfoMap != nil {
		p.MerchantInfoMap = make(map[fwe_trade_common.UnionPayType]*payment.MerchantInfo, len(src.MerchantInfoMap))
		for key, val := range src.MerchantInfoMap {
			var _key fwe_trade_common.UnionPayType
			_key = key

			var _val *payment.MerchantInfo
			if val != nil {
				_val = &payment.MerchantInfo{}
				if err := _val.DeepCopy(val); err != nil {
					return err
				}
			}

			p.MerchantInfoMap[_key] = _val
		}
	}

	if src.PosDeviceList != nil {
		p.PosDeviceList = make([]*fwe_trade_common.POSDeviceInfo, 0, len(src.PosDeviceList))
		for _, elem := range src.PosDeviceList {
			var _elem *fwe_trade_common.POSDeviceInfo
			if elem != nil {
				_elem = &fwe_trade_common.POSDeviceInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.PosDeviceList = append(p.PosDeviceList, _elem)
		}
	}

	if src.PayLimitList != nil {
		p.PayLimitList = make([]fwe_trade_common.PayLimitType, 0, len(src.PayLimitList))
		for _, elem := range src.PayLimitList {
			var _elem fwe_trade_common.PayLimitType
			_elem = elem
			p.PayLimitList = append(p.PayLimitList, _elem)
		}
	}

	if src.CashierDeskType != nil {
		tmp := *src.CashierDeskType
		p.CashierDeskType = &tmp
	}

	if src.OsType != nil {
		tmp := *src.OsType
		p.OsType = &tmp
	}

	if src.ExpireTime != nil {
		tmp := *src.ExpireTime
		p.ExpireTime = &tmp
	}

	if src.RedirectURL != nil {
		var tmp string
		if *src.RedirectURL != "" {
			tmp = kutils.StringDeepCopy(*src.RedirectURL)
		}
		p.RedirectURL = &tmp
	}

	var _checkData *fwe_trade_common.PayOfflineCheckData
	if src.CheckData != nil {
		_checkData = &fwe_trade_common.PayOfflineCheckData{}
		if err := _checkData.DeepCopy(src.CheckData); err != nil {
			return err
		}
	}
	p.CheckData = _checkData

	var _jstPayParam *fwe_trade_common.JstPayParam
	if src.JstPayParam != nil {
		_jstPayParam = &fwe_trade_common.JstPayParam{}
		if err := _jstPayParam.DeepCopy(src.JstPayParam); err != nil {
			return err
		}
	}
	p.JstPayParam = _jstPayParam

	if src.FweAccountID != "" {
		p.FweAccountID = kutils.StringDeepCopy(src.FweAccountID)
	}

	p.OneStepPay = src.OneStepPay

	if src.PayUnionParam != nil {
		p.PayUnionParam = make(map[string]string, len(src.PayUnionParam))
		for key, val := range src.PayUnionParam {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.PayUnionParam[_key] = _val
		}
	}

	if src.FinanceAccountID != "" {
		p.FinanceAccountID = kutils.StringDeepCopy(src.FinanceAccountID)
	}

	var _riskInfo *fwe_trade_common.UnionPayRiskInfo
	if src.RiskInfo != nil {
		_riskInfo = &fwe_trade_common.UnionPayRiskInfo{}
		if err := _riskInfo.DeepCopy(src.RiskInfo); err != nil {
			return err
		}
	}
	p.RiskInfo = _riskInfo

	if src.Extra != nil {
		var tmp string
		if *src.Extra != "" {
			tmp = kutils.StringDeepCopy(*src.Extra)
		}
		p.Extra = &tmp
	}

	var _secondPayParam *fwe_trade_common.SecondPayParam
	if src.SecondPayParam != nil {
		_secondPayParam = &fwe_trade_common.SecondPayParam{}
		if err := _secondPayParam.DeepCopy(src.SecondPayParam); err != nil {
			return err
		}
	}
	p.SecondPayParam = _secondPayParam

	if src.CallbackEvent != "" {
		p.CallbackEvent = kutils.StringDeepCopy(src.CallbackEvent)
	}

	if src.CallbackExtra != "" {
		p.CallbackExtra = kutils.StringDeepCopy(src.CallbackExtra)
	}

	if src.CloseCallbackEvent != "" {
		p.CloseCallbackEvent = kutils.StringDeepCopy(src.CloseCallbackEvent)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *UnionPayResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UnionPayResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *UnionPayResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayUnionNo = _field
	return offset, nil
}

func (p *UnionPayResp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayUnionLink = _field
	return offset, nil
}

func (p *UnionPayResp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayData = _field
	return offset, nil
}

func (p *UnionPayResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *UnionPayResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *UnionPayResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *UnionPayResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayUnionNo)
	return offset
}

func (p *UnionPayResp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayUnionLink)
	return offset
}

func (p *UnionPayResp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayData)
	return offset
}

func (p *UnionPayResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayUnionNo)
	return l
}

func (p *UnionPayResp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayUnionLink)
	return l
}

func (p *UnionPayResp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayData)
	return l
}

func (p *UnionPayResp) DeepCopy(s interface{}) error {
	src, ok := s.(*UnionPayResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PayUnionNo != "" {
		p.PayUnionNo = kutils.StringDeepCopy(src.PayUnionNo)
	}

	if src.PayUnionLink != "" {
		p.PayUnionLink = kutils.StringDeepCopy(src.PayUnionLink)
	}

	if src.PayData != "" {
		p.PayData = kutils.StringDeepCopy(src.PayData)
	}

	return nil
}

func (p *GuaranteePayReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GuaranteePayReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *GuaranteePayReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AppID = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UID = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceOrderType = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Amount = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.CashierDeskType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.CashierDeskType(v)
	}
	p.CashierDeskType = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ExpireTime = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RedirectURL = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IPAddress = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField16(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]fwe_trade_common.PayLimitType, 0, size)
	for i := 0; i < size; i++ {
		var _elem fwe_trade_common.PayLimitType
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_elem = fwe_trade_common.PayLimitType(v)
		}

		_field = append(_field, _elem)
	}
	p.PayLimitList = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAction = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField101(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TimeoutAction = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *GuaranteePayReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GuaranteePayReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GuaranteePayReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field100Length()
		l += p.field101Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GuaranteePayReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *GuaranteePayReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AppID)
	return offset
}

func (p *GuaranteePayReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.UID)
	return offset
}

func (p *GuaranteePayReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 10)
	offset += thrift.Binary.WriteI32(buf[offset:], p.FinanceOrderType)
	return offset
}

func (p *GuaranteePayReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 11)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Amount)
	return offset
}

func (p *GuaranteePayReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 12)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.CashierDeskType))
	return offset
}

func (p *GuaranteePayReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetExpireTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 13)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.ExpireTime)
	}
	return offset
}

func (p *GuaranteePayReq) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRedirectURL() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RedirectURL)
	}
	return offset
}

func (p *GuaranteePayReq) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 15)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.IPAddress)
	return offset
}

func (p *GuaranteePayReq) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 16)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.PayLimitList {
		length++
		offset += thrift.Binary.WriteI32(buf[offset:], int32(v))
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I32, length)
	return offset
}

func (p *GuaranteePayReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAction)
	return offset
}

func (p *GuaranteePayReq) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 101)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TimeoutAction)
	return offset
}

func (p *GuaranteePayReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *GuaranteePayReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *GuaranteePayReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AppID)
	return l
}

func (p *GuaranteePayReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.UID)
	return l
}

func (p *GuaranteePayReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *GuaranteePayReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *GuaranteePayReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *GuaranteePayReq) field13Length() int {
	l := 0
	if p.IsSetExpireTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *GuaranteePayReq) field14Length() int {
	l := 0
	if p.IsSetRedirectURL() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RedirectURL)
	}
	return l
}

func (p *GuaranteePayReq) field15Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.IPAddress)
	return l
}

func (p *GuaranteePayReq) field16Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.PayLimitList {
		_ = v
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *GuaranteePayReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAction)
	return l
}

func (p *GuaranteePayReq) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TimeoutAction)
	return l
}

func (p *GuaranteePayReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *GuaranteePayReq) DeepCopy(s interface{}) error {
	src, ok := s.(*GuaranteePayReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.AppID != "" {
		p.AppID = kutils.StringDeepCopy(src.AppID)
	}

	if src.UID != "" {
		p.UID = kutils.StringDeepCopy(src.UID)
	}

	p.FinanceOrderType = src.FinanceOrderType

	p.Amount = src.Amount

	p.CashierDeskType = src.CashierDeskType

	if src.ExpireTime != nil {
		tmp := *src.ExpireTime
		p.ExpireTime = &tmp
	}

	if src.RedirectURL != nil {
		var tmp string
		if *src.RedirectURL != "" {
			tmp = kutils.StringDeepCopy(*src.RedirectURL)
		}
		p.RedirectURL = &tmp
	}

	if src.IPAddress != "" {
		p.IPAddress = kutils.StringDeepCopy(src.IPAddress)
	}

	if src.PayLimitList != nil {
		p.PayLimitList = make([]fwe_trade_common.PayLimitType, 0, len(src.PayLimitList))
		for _, elem := range src.PayLimitList {
			var _elem fwe_trade_common.PayLimitType
			_elem = elem
			p.PayLimitList = append(p.PayLimitList, _elem)
		}
	}

	if src.CallbackAction != "" {
		p.CallbackAction = kutils.StringDeepCopy(src.CallbackAction)
	}

	if src.TimeoutAction != "" {
		p.TimeoutAction = kutils.StringDeepCopy(src.TimeoutAction)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *GuaranteePayRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GuaranteePayRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *GuaranteePayRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayData = _field
	return offset, nil
}

func (p *GuaranteePayRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayOrderNo = _field
	return offset, nil
}

func (p *GuaranteePayRsp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayTradeNo = _field
	return offset, nil
}

func (p *GuaranteePayRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GuaranteePayRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GuaranteePayRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GuaranteePayRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayData)
	return offset
}

func (p *GuaranteePayRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayOrderNo)
	return offset
}

func (p *GuaranteePayRsp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayTradeNo)
	return offset
}

func (p *GuaranteePayRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayData)
	return l
}

func (p *GuaranteePayRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayOrderNo)
	return l
}

func (p *GuaranteePayRsp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayTradeNo)
	return l
}

func (p *GuaranteePayRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*GuaranteePayRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PayData != "" {
		p.PayData = kutils.StringDeepCopy(src.PayData)
	}

	if src.PayOrderNo != "" {
		p.PayOrderNo = kutils.StringDeepCopy(src.PayOrderNo)
	}

	if src.PayTradeNo != "" {
		p.PayTradeNo = kutils.StringDeepCopy(src.PayTradeNo)
	}

	return nil
}

func (p *CashPayReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CashPayReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CashPayReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AppID = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.UserID = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PayOrderNo = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceOrderType = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Amount = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.CashierDeskType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.CashierDeskType(v)
	}
	p.CashierDeskType = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ExpireTime = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RedirectURL = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IPAddress = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField16(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]fwe_trade_common.PayLimitType, 0, size)
	for i := 0; i < size; i++ {
		var _elem fwe_trade_common.PayLimitType
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l

			_elem = fwe_trade_common.PayLimitType(v)
		}

		_field = append(_field, _elem)
	}
	p.PayLimitList = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAction = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField101(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TimeoutAction = _field
	return offset, nil
}

func (p *CashPayReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *CashPayReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CashPayReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CashPayReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field100Length()
		l += p.field101Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CashPayReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *CashPayReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AppID)
	return offset
}

func (p *CashPayReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetUserID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.UserID)
	}
	return offset
}

func (p *CashPayReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPayOrderNo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.PayOrderNo)
	}
	return offset
}

func (p *CashPayReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 10)
	offset += thrift.Binary.WriteI32(buf[offset:], p.FinanceOrderType)
	return offset
}

func (p *CashPayReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 11)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Amount)
	return offset
}

func (p *CashPayReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 12)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.CashierDeskType))
	return offset
}

func (p *CashPayReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetExpireTime() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 13)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.ExpireTime)
	}
	return offset
}

func (p *CashPayReq) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRedirectURL() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RedirectURL)
	}
	return offset
}

func (p *CashPayReq) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 15)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.IPAddress)
	return offset
}

func (p *CashPayReq) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 16)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.PayLimitList {
		length++
		offset += thrift.Binary.WriteI32(buf[offset:], int32(v))
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I32, length)
	return offset
}

func (p *CashPayReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAction)
	return offset
}

func (p *CashPayReq) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 101)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TimeoutAction)
	return offset
}

func (p *CashPayReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *CashPayReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *CashPayReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AppID)
	return l
}

func (p *CashPayReq) field3Length() int {
	l := 0
	if p.IsSetUserID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.UserID)
	}
	return l
}

func (p *CashPayReq) field4Length() int {
	l := 0
	if p.IsSetPayOrderNo() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.PayOrderNo)
	}
	return l
}

func (p *CashPayReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *CashPayReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *CashPayReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *CashPayReq) field13Length() int {
	l := 0
	if p.IsSetExpireTime() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *CashPayReq) field14Length() int {
	l := 0
	if p.IsSetRedirectURL() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RedirectURL)
	}
	return l
}

func (p *CashPayReq) field15Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.IPAddress)
	return l
}

func (p *CashPayReq) field16Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.PayLimitList {
		_ = v
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *CashPayReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAction)
	return l
}

func (p *CashPayReq) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TimeoutAction)
	return l
}

func (p *CashPayReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *CashPayReq) DeepCopy(s interface{}) error {
	src, ok := s.(*CashPayReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.AppID != "" {
		p.AppID = kutils.StringDeepCopy(src.AppID)
	}

	if src.UserID != nil {
		var tmp string
		if *src.UserID != "" {
			tmp = kutils.StringDeepCopy(*src.UserID)
		}
		p.UserID = &tmp
	}

	if src.PayOrderNo != nil {
		var tmp string
		if *src.PayOrderNo != "" {
			tmp = kutils.StringDeepCopy(*src.PayOrderNo)
		}
		p.PayOrderNo = &tmp
	}

	p.FinanceOrderType = src.FinanceOrderType

	p.Amount = src.Amount

	p.CashierDeskType = src.CashierDeskType

	if src.ExpireTime != nil {
		tmp := *src.ExpireTime
		p.ExpireTime = &tmp
	}

	if src.RedirectURL != nil {
		var tmp string
		if *src.RedirectURL != "" {
			tmp = kutils.StringDeepCopy(*src.RedirectURL)
		}
		p.RedirectURL = &tmp
	}

	if src.IPAddress != "" {
		p.IPAddress = kutils.StringDeepCopy(src.IPAddress)
	}

	if src.PayLimitList != nil {
		p.PayLimitList = make([]fwe_trade_common.PayLimitType, 0, len(src.PayLimitList))
		for _, elem := range src.PayLimitList {
			var _elem fwe_trade_common.PayLimitType
			_elem = elem
			p.PayLimitList = append(p.PayLimitList, _elem)
		}
	}

	if src.CallbackAction != "" {
		p.CallbackAction = kutils.StringDeepCopy(src.CallbackAction)
	}

	if src.TimeoutAction != "" {
		p.TimeoutAction = kutils.StringDeepCopy(src.TimeoutAction)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *CashPayRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CashPayRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *CashPayRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayData = _field
	return offset, nil
}

func (p *CashPayRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayOrderNo = _field
	return offset, nil
}

func (p *CashPayRsp) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayTradeNo = _field
	return offset, nil
}

func (p *CashPayRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *CashPayRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *CashPayRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *CashPayRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayData)
	return offset
}

func (p *CashPayRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayOrderNo)
	return offset
}

func (p *CashPayRsp) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayTradeNo)
	return offset
}

func (p *CashPayRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayData)
	return l
}

func (p *CashPayRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayOrderNo)
	return l
}

func (p *CashPayRsp) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayTradeNo)
	return l
}

func (p *CashPayRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*CashPayRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PayData != "" {
		p.PayData = kutils.StringDeepCopy(src.PayData)
	}

	if src.PayOrderNo != "" {
		p.PayOrderNo = kutils.StringDeepCopy(src.PayOrderNo)
	}

	if src.PayTradeNo != "" {
		p.PayTradeNo = kutils.StringDeepCopy(src.PayTradeNo)
	}

	return nil
}

func (p *OfflinePayReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OfflinePayReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *OfflinePayReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.PayOrderNo = _field
	return offset, nil
}

func (p *OfflinePayReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceOrderType = _field
	return offset, nil
}

func (p *OfflinePayReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Amount = _field
	return offset, nil
}

func (p *OfflinePayReq) FastReadField12(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewPayOfflineCheckData()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.CheckData = _field
	return offset, nil
}

func (p *OfflinePayReq) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RedirectURL = _field
	return offset, nil
}

func (p *OfflinePayReq) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IPAddress = _field
	return offset, nil
}

func (p *OfflinePayReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAction = _field
	return offset, nil
}

func (p *OfflinePayReq) FastReadField101(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TimeoutAction = _field
	return offset, nil
}

func (p *OfflinePayReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *OfflinePayReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *OfflinePayReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *OfflinePayReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field100Length()
		l += p.field101Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *OfflinePayReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPayOrderNo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.PayOrderNo)
	}
	return offset
}

func (p *OfflinePayReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 10)
	offset += thrift.Binary.WriteI32(buf[offset:], p.FinanceOrderType)
	return offset
}

func (p *OfflinePayReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 11)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Amount)
	return offset
}

func (p *OfflinePayReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 12)
	offset += p.CheckData.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *OfflinePayReq) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRedirectURL() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RedirectURL)
	}
	return offset
}

func (p *OfflinePayReq) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 15)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.IPAddress)
	return offset
}

func (p *OfflinePayReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAction)
	return offset
}

func (p *OfflinePayReq) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 101)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TimeoutAction)
	return offset
}

func (p *OfflinePayReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *OfflinePayReq) field1Length() int {
	l := 0
	if p.IsSetPayOrderNo() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.PayOrderNo)
	}
	return l
}

func (p *OfflinePayReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *OfflinePayReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *OfflinePayReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.CheckData.BLength()
	return l
}

func (p *OfflinePayReq) field14Length() int {
	l := 0
	if p.IsSetRedirectURL() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RedirectURL)
	}
	return l
}

func (p *OfflinePayReq) field15Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.IPAddress)
	return l
}

func (p *OfflinePayReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAction)
	return l
}

func (p *OfflinePayReq) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TimeoutAction)
	return l
}

func (p *OfflinePayReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *OfflinePayReq) DeepCopy(s interface{}) error {
	src, ok := s.(*OfflinePayReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PayOrderNo != nil {
		var tmp string
		if *src.PayOrderNo != "" {
			tmp = kutils.StringDeepCopy(*src.PayOrderNo)
		}
		p.PayOrderNo = &tmp
	}

	p.FinanceOrderType = src.FinanceOrderType

	p.Amount = src.Amount

	var _checkData *fwe_trade_common.PayOfflineCheckData
	if src.CheckData != nil {
		_checkData = &fwe_trade_common.PayOfflineCheckData{}
		if err := _checkData.DeepCopy(src.CheckData); err != nil {
			return err
		}
	}
	p.CheckData = _checkData

	if src.RedirectURL != nil {
		var tmp string
		if *src.RedirectURL != "" {
			tmp = kutils.StringDeepCopy(*src.RedirectURL)
		}
		p.RedirectURL = &tmp
	}

	if src.IPAddress != "" {
		p.IPAddress = kutils.StringDeepCopy(src.IPAddress)
	}

	if src.CallbackAction != "" {
		p.CallbackAction = kutils.StringDeepCopy(src.CallbackAction)
	}

	if src.TimeoutAction != "" {
		p.TimeoutAction = kutils.StringDeepCopy(src.TimeoutAction)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *OfflinePayRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OfflinePayRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *OfflinePayRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayOrderNo = _field
	return offset, nil
}

func (p *OfflinePayRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *OfflinePayRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *OfflinePayRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *OfflinePayRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PayOrderNo)
	return offset
}

func (p *OfflinePayRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PayOrderNo)
	return l
}

func (p *OfflinePayRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*OfflinePayRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.PayOrderNo != "" {
		p.PayOrderNo = kutils.StringDeepCopy(src.PayOrderNo)
	}

	return nil
}

func (p *WithdrawReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 102:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField102(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WithdrawReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *WithdrawReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AppID = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UID = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantName = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceOrderType = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Amount = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field payment.WithdrawType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = payment.WithdrawType(v)
	}
	p.WithdrawType = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Reason = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField14(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBankInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.BankCardInfo = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IPAddress = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField16(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.FeeItem, 0, size)
	values := make([]fwe_trade_common.FeeItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.FeeItemList = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAction = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField101(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FailCallbackAction = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField102(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FweAccountID = _field
	return offset, nil
}

func (p *WithdrawReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *WithdrawReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *WithdrawReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField102(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *WithdrawReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field100Length()
		l += p.field101Length()
		l += p.field102Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *WithdrawReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *WithdrawReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AppID)
	return offset
}

func (p *WithdrawReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.UID)
	return offset
}

func (p *WithdrawReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantName)
	return offset
}

func (p *WithdrawReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 10)
	offset += thrift.Binary.WriteI32(buf[offset:], p.FinanceOrderType)
	return offset
}

func (p *WithdrawReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 11)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Amount)
	return offset
}

func (p *WithdrawReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 12)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.WithdrawType))
	return offset
}

func (p *WithdrawReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 13)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Reason)
	return offset
}

func (p *WithdrawReq) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 14)
	offset += p.BankCardInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *WithdrawReq) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 15)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.IPAddress)
	return offset
}

func (p *WithdrawReq) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 16)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FeeItemList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *WithdrawReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAction)
	return offset
}

func (p *WithdrawReq) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 101)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FailCallbackAction)
	return offset
}

func (p *WithdrawReq) fastWriteField102(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 102)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FweAccountID)
	return offset
}

func (p *WithdrawReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *WithdrawReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *WithdrawReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AppID)
	return l
}

func (p *WithdrawReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.UID)
	return l
}

func (p *WithdrawReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantName)
	return l
}

func (p *WithdrawReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *WithdrawReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *WithdrawReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *WithdrawReq) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Reason)
	return l
}

func (p *WithdrawReq) field14Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.BankCardInfo.BLength()
	return l
}

func (p *WithdrawReq) field15Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.IPAddress)
	return l
}

func (p *WithdrawReq) field16Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FeeItemList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *WithdrawReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAction)
	return l
}

func (p *WithdrawReq) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FailCallbackAction)
	return l
}

func (p *WithdrawReq) field102Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FweAccountID)
	return l
}

func (p *WithdrawReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *WithdrawReq) DeepCopy(s interface{}) error {
	src, ok := s.(*WithdrawReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.AppID != "" {
		p.AppID = kutils.StringDeepCopy(src.AppID)
	}

	if src.UID != "" {
		p.UID = kutils.StringDeepCopy(src.UID)
	}

	if src.MerchantName != "" {
		p.MerchantName = kutils.StringDeepCopy(src.MerchantName)
	}

	p.FinanceOrderType = src.FinanceOrderType

	p.Amount = src.Amount

	p.WithdrawType = src.WithdrawType

	if src.Reason != "" {
		p.Reason = kutils.StringDeepCopy(src.Reason)
	}

	var _bankCardInfo *fwe_trade_common.BankInfo
	if src.BankCardInfo != nil {
		_bankCardInfo = &fwe_trade_common.BankInfo{}
		if err := _bankCardInfo.DeepCopy(src.BankCardInfo); err != nil {
			return err
		}
	}
	p.BankCardInfo = _bankCardInfo

	if src.IPAddress != "" {
		p.IPAddress = kutils.StringDeepCopy(src.IPAddress)
	}

	if src.FeeItemList != nil {
		p.FeeItemList = make([]*fwe_trade_common.FeeItem, 0, len(src.FeeItemList))
		for _, elem := range src.FeeItemList {
			var _elem *fwe_trade_common.FeeItem
			if elem != nil {
				_elem = &fwe_trade_common.FeeItem{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.FeeItemList = append(p.FeeItemList, _elem)
		}
	}

	if src.CallbackAction != "" {
		p.CallbackAction = kutils.StringDeepCopy(src.CallbackAction)
	}

	if src.FailCallbackAction != "" {
		p.FailCallbackAction = kutils.StringDeepCopy(src.FailCallbackAction)
	}

	if src.FweAccountID != "" {
		p.FweAccountID = kutils.StringDeepCopy(src.FweAccountID)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *WithdrawRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WithdrawRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *WithdrawRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WithdrawOrderNo = _field
	return offset, nil
}

func (p *WithdrawRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.WithdrawTradeNo = _field
	return offset, nil
}

func (p *WithdrawRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *WithdrawRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *WithdrawRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *WithdrawRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.WithdrawOrderNo)
	return offset
}

func (p *WithdrawRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.WithdrawTradeNo)
	return offset
}

func (p *WithdrawRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.WithdrawOrderNo)
	return l
}

func (p *WithdrawRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.WithdrawTradeNo)
	return l
}

func (p *WithdrawRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*WithdrawRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.WithdrawOrderNo != "" {
		p.WithdrawOrderNo = kutils.StringDeepCopy(src.WithdrawOrderNo)
	}

	if src.WithdrawTradeNo != "" {
		p.WithdrawTradeNo = kutils.StringDeepCopy(src.WithdrawTradeNo)
	}

	return nil
}

func (p *SingleRefund) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SingleRefund[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SingleRefund) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceOrderType = _field
	return offset, nil
}

func (p *SingleRefund) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Amount = _field
	return offset, nil
}

func (p *SingleRefund) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.YztOfflineAmount = _field
	return offset, nil
}

func (p *SingleRefund) FastReadField10(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.JstRefundSingle, 0, size)
	values := make([]fwe_trade_common.JstRefundSingle, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.JstRefundList = _field
	return offset, nil
}

func (p *SingleRefund) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SingleRefund) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SingleRefund) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field10Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SingleRefund) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.FinanceOrderType)
	return offset
}

func (p *SingleRefund) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Amount)
	return offset
}

func (p *SingleRefund) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.YztOfflineAmount)
	return offset
}

func (p *SingleRefund) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 10)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.JstRefundList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *SingleRefund) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SingleRefund) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SingleRefund) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SingleRefund) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.JstRefundList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *SingleRefund) DeepCopy(s interface{}) error {
	src, ok := s.(*SingleRefund)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.FinanceOrderType = src.FinanceOrderType

	p.Amount = src.Amount

	p.YztOfflineAmount = src.YztOfflineAmount

	if src.JstRefundList != nil {
		p.JstRefundList = make([]*fwe_trade_common.JstRefundSingle, 0, len(src.JstRefundList))
		for _, elem := range src.JstRefundList {
			var _elem *fwe_trade_common.JstRefundSingle
			if elem != nil {
				_elem = &fwe_trade_common.JstRefundSingle{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.JstRefundList = append(p.JstRefundList, _elem)
		}
	}

	return nil
}

func (p *RefundReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 22:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField22(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 23:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField23(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 24:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField24(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField25(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 102:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField102(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RefundReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RefundReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*SingleRefund, 0, size)
	values := make([]SingleRefund, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.RefundList = _field
	return offset, nil
}

func (p *RefundReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundType = _field
	return offset, nil
}

func (p *RefundReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.NeedConfirmOfflineRefund = _field
	return offset, nil
}

func (p *RefundReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NeedWithdraw = _field
	return offset, nil
}

func (p *RefundReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Reason = _field
	return offset, nil
}

func (p *RefundReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IPAddress = _field
	return offset, nil
}

func (p *RefundReq) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleID = _field
	return offset, nil
}

func (p *RefundReq) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Params = _field
	return offset, nil
}

func (p *RefundReq) FastReadField22(buf []byte) (int, error) {
	offset := 0
	_field := payment.NewRefundSettleInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.RefundSettleInfo = _field
	return offset, nil
}

func (p *RefundReq) FastReadField23(buf []byte) (int, error) {
	offset := 0
	_field := payment.NewRefundSettleInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.YztOfflineRefundSettleInfo = _field
	return offset, nil
}

func (p *RefundReq) FastReadField24(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewLifeRefundParam()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.LifeRefundParam = _field
	return offset, nil
}

func (p *RefundReq) FastReadField25(buf []byte) (int, error) {
	offset := 0

	var _field fwe_trade_common.RefundMode
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = fwe_trade_common.RefundMode(v)
	}
	p.RefundMode = _field
	return offset, nil
}

func (p *RefundReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAction = _field
	return offset, nil
}

func (p *RefundReq) FastReadField101(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.FeeItem, 0, size)
	values := make([]fwe_trade_common.FeeItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.DeductItemList = _field
	return offset, nil
}

func (p *RefundReq) FastReadField102(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.FeeItem, 0, size)
	values := make([]fwe_trade_common.FeeItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.FeeItemList = _field
	return offset, nil
}

func (p *RefundReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *RefundReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RefundReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
		offset += p.fastWriteField22(buf[offset:], w)
		offset += p.fastWriteField23(buf[offset:], w)
		offset += p.fastWriteField24(buf[offset:], w)
		offset += p.fastWriteField25(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField102(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RefundReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field20Length()
		l += p.field21Length()
		l += p.field22Length()
		l += p.field23Length()
		l += p.field24Length()
		l += p.field25Length()
		l += p.field100Length()
		l += p.field101Length()
		l += p.field102Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RefundReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RefundList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *RefundReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.RefundType)
	return offset
}

func (p *RefundReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNeedConfirmOfflineRefund() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 3)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.NeedConfirmOfflineRefund)
	}
	return offset
}

func (p *RefundReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 4)
	offset += thrift.Binary.WriteBool(buf[offset:], p.NeedWithdraw)
	return offset
}

func (p *RefundReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Reason)
	return offset
}

func (p *RefundReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.IPAddress)
	return offset
}

func (p *RefundReq) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 20)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RuleID)
	return offset
}

func (p *RefundReq) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 21)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Params)
	return offset
}

func (p *RefundReq) fastWriteField22(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 22)
	offset += p.RefundSettleInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RefundReq) fastWriteField23(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 23)
	offset += p.YztOfflineRefundSettleInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *RefundReq) fastWriteField24(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetLifeRefundParam() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 24)
		offset += p.LifeRefundParam.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *RefundReq) fastWriteField25(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 25)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.RefundMode))
	return offset
}

func (p *RefundReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAction)
	return offset
}

func (p *RefundReq) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 101)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.DeductItemList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *RefundReq) fastWriteField102(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 102)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.FeeItemList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *RefundReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *RefundReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RefundList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *RefundReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RefundReq) field3Length() int {
	l := 0
	if p.IsSetNeedConfirmOfflineRefund() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *RefundReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *RefundReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Reason)
	return l
}

func (p *RefundReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.IPAddress)
	return l
}

func (p *RefundReq) field20Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *RefundReq) field21Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Params)
	return l
}

func (p *RefundReq) field22Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.RefundSettleInfo.BLength()
	return l
}

func (p *RefundReq) field23Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.YztOfflineRefundSettleInfo.BLength()
	return l
}

func (p *RefundReq) field24Length() int {
	l := 0
	if p.IsSetLifeRefundParam() {
		l += thrift.Binary.FieldBeginLength()
		l += p.LifeRefundParam.BLength()
	}
	return l
}

func (p *RefundReq) field25Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RefundReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAction)
	return l
}

func (p *RefundReq) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.DeductItemList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *RefundReq) field102Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.FeeItemList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *RefundReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *RefundReq) DeepCopy(s interface{}) error {
	src, ok := s.(*RefundReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.RefundList != nil {
		p.RefundList = make([]*SingleRefund, 0, len(src.RefundList))
		for _, elem := range src.RefundList {
			var _elem *SingleRefund
			if elem != nil {
				_elem = &SingleRefund{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.RefundList = append(p.RefundList, _elem)
		}
	}

	p.RefundType = src.RefundType

	if src.NeedConfirmOfflineRefund != nil {
		tmp := *src.NeedConfirmOfflineRefund
		p.NeedConfirmOfflineRefund = &tmp
	}

	p.NeedWithdraw = src.NeedWithdraw

	if src.Reason != "" {
		p.Reason = kutils.StringDeepCopy(src.Reason)
	}

	if src.IPAddress != "" {
		p.IPAddress = kutils.StringDeepCopy(src.IPAddress)
	}

	p.RuleID = src.RuleID

	if src.Params != "" {
		p.Params = kutils.StringDeepCopy(src.Params)
	}

	var _refundSettleInfo *payment.RefundSettleInfo
	if src.RefundSettleInfo != nil {
		_refundSettleInfo = &payment.RefundSettleInfo{}
		if err := _refundSettleInfo.DeepCopy(src.RefundSettleInfo); err != nil {
			return err
		}
	}
	p.RefundSettleInfo = _refundSettleInfo

	var _yztOfflineRefundSettleInfo *payment.RefundSettleInfo
	if src.YztOfflineRefundSettleInfo != nil {
		_yztOfflineRefundSettleInfo = &payment.RefundSettleInfo{}
		if err := _yztOfflineRefundSettleInfo.DeepCopy(src.YztOfflineRefundSettleInfo); err != nil {
			return err
		}
	}
	p.YztOfflineRefundSettleInfo = _yztOfflineRefundSettleInfo

	var _lifeRefundParam *fwe_trade_common.LifeRefundParam
	if src.LifeRefundParam != nil {
		_lifeRefundParam = &fwe_trade_common.LifeRefundParam{}
		if err := _lifeRefundParam.DeepCopy(src.LifeRefundParam); err != nil {
			return err
		}
	}
	p.LifeRefundParam = _lifeRefundParam

	p.RefundMode = src.RefundMode

	if src.CallbackAction != "" {
		p.CallbackAction = kutils.StringDeepCopy(src.CallbackAction)
	}

	if src.DeductItemList != nil {
		p.DeductItemList = make([]*fwe_trade_common.FeeItem, 0, len(src.DeductItemList))
		for _, elem := range src.DeductItemList {
			var _elem *fwe_trade_common.FeeItem
			if elem != nil {
				_elem = &fwe_trade_common.FeeItem{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.DeductItemList = append(p.DeductItemList, _elem)
		}
	}

	if src.FeeItemList != nil {
		p.FeeItemList = make([]*fwe_trade_common.FeeItem, 0, len(src.FeeItemList))
		for _, elem := range src.FeeItemList {
			var _elem *fwe_trade_common.FeeItem
			if elem != nil {
				_elem = &fwe_trade_common.FeeItem{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.FeeItemList = append(p.FeeItemList, _elem)
		}
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *RefundRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RefundRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RefundRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MergeRefundNo = _field
	return offset, nil
}

func (p *RefundRsp) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.RefundOrderNoList = _field
	return offset, nil
}

func (p *RefundRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RefundRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RefundRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RefundRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MergeRefundNo)
	return offset
}

func (p *RefundRsp) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RefundOrderNoList {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *RefundRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MergeRefundNo)
	return l
}

func (p *RefundRsp) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RefundOrderNoList {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *RefundRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*RefundRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.MergeRefundNo != "" {
		p.MergeRefundNo = kutils.StringDeepCopy(src.MergeRefundNo)
	}

	if src.RefundOrderNoList != nil {
		p.RefundOrderNoList = make([]string, 0, len(src.RefundOrderNoList))
		for _, elem := range src.RefundOrderNoList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.RefundOrderNoList = append(p.RefundOrderNoList, _elem)
		}
	}

	return nil
}

func (p *SettleReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField22(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField23(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 24:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField24(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 25:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField25(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 26:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField26(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SettleReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SettleReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewTradeSpiltInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.SplitInfo = _field
	return offset, nil
}

func (p *SettleReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SettleType = _field
	return offset, nil
}

func (p *SettleReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.FinanceIDList = _field
	return offset, nil
}

func (p *SettleReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem int32
		if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.FinanceTypeList = _field
	return offset, nil
}

func (p *SettleReq) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Reason = _field
	return offset, nil
}

func (p *SettleReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IPAddress = _field
	return offset, nil
}

func (p *SettleReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field *bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.IsAutoWithdraw = _field
	return offset, nil
}

func (p *SettleReq) FastReadField20(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RuleID = _field
	return offset, nil
}

func (p *SettleReq) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Params = _field
	return offset, nil
}

func (p *SettleReq) FastReadField22(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SubsidyOutUID = _field
	return offset, nil
}

func (p *SettleReq) FastReadField23(buf []byte) (int, error) {
	offset := 0

	var _field *int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SubsidyOutUIDType = _field
	return offset, nil
}

func (p *SettleReq) FastReadField24(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.SubsidyInfo, 0, size)
	values := make([]fwe_trade_common.SubsidyInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.SubsidyInfoList = _field
	return offset, nil
}

func (p *SettleReq) FastReadField25(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OmitInfraSubsidy = _field
	return offset, nil
}

func (p *SettleReq) FastReadField26(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.TotalSubsidyAmount = _field
	return offset, nil
}

func (p *SettleReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAction = _field
	return offset, nil
}

func (p *SettleReq) FastReadField101(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BizExtra = _field
	return offset, nil
}

func (p *SettleReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *SettleReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SettleReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField23(buf[offset:], w)
		offset += p.fastWriteField25(buf[offset:], w)
		offset += p.fastWriteField26(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
		offset += p.fastWriteField22(buf[offset:], w)
		offset += p.fastWriteField24(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SettleReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field20Length()
		l += p.field21Length()
		l += p.field22Length()
		l += p.field23Length()
		l += p.field24Length()
		l += p.field25Length()
		l += p.field26Length()
		l += p.field100Length()
		l += p.field101Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SettleReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.SplitInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *SettleReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.SettleType)
	return offset
}

func (p *SettleReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFinanceIDList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.FinanceIDList {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *SettleReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFinanceTypeList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 4)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.FinanceTypeList {
			length++
			offset += thrift.Binary.WriteI32(buf[offset:], v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.I32, length)
	}
	return offset
}

func (p *SettleReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Reason)
	return offset
}

func (p *SettleReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.IPAddress)
	return offset
}

func (p *SettleReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetIsAutoWithdraw() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 12)
		offset += thrift.Binary.WriteBool(buf[offset:], *p.IsAutoWithdraw)
	}
	return offset
}

func (p *SettleReq) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 20)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RuleID)
	return offset
}

func (p *SettleReq) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 21)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Params)
	return offset
}

func (p *SettleReq) fastWriteField22(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSubsidyOutUID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 22)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.SubsidyOutUID)
	}
	return offset
}

func (p *SettleReq) fastWriteField23(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSubsidyOutUIDType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 23)
		offset += thrift.Binary.WriteI32(buf[offset:], *p.SubsidyOutUIDType)
	}
	return offset
}

func (p *SettleReq) fastWriteField24(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSubsidyInfoList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 24)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.SubsidyInfoList {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *SettleReq) fastWriteField25(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 25)
	offset += thrift.Binary.WriteBool(buf[offset:], p.OmitInfraSubsidy)
	return offset
}

func (p *SettleReq) fastWriteField26(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetTotalSubsidyAmount() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 26)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.TotalSubsidyAmount)
	}
	return offset
}

func (p *SettleReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAction)
	return offset
}

func (p *SettleReq) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 101)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BizExtra)
	return offset
}

func (p *SettleReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *SettleReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.SplitInfo.BLength()
	return l
}

func (p *SettleReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *SettleReq) field3Length() int {
	l := 0
	if p.IsSetFinanceIDList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.FinanceIDList {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *SettleReq) field4Length() int {
	l := 0
	if p.IsSetFinanceTypeList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		l +=
			thrift.Binary.I32Length() * len(p.FinanceTypeList)
	}
	return l
}

func (p *SettleReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Reason)
	return l
}

func (p *SettleReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.IPAddress)
	return l
}

func (p *SettleReq) field12Length() int {
	l := 0
	if p.IsSetIsAutoWithdraw() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.BoolLength()
	}
	return l
}

func (p *SettleReq) field20Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *SettleReq) field21Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Params)
	return l
}

func (p *SettleReq) field22Length() int {
	l := 0
	if p.IsSetSubsidyOutUID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.SubsidyOutUID)
	}
	return l
}

func (p *SettleReq) field23Length() int {
	l := 0
	if p.IsSetSubsidyOutUIDType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I32Length()
	}
	return l
}

func (p *SettleReq) field24Length() int {
	l := 0
	if p.IsSetSubsidyInfoList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.SubsidyInfoList {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *SettleReq) field25Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *SettleReq) field26Length() int {
	l := 0
	if p.IsSetTotalSubsidyAmount() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *SettleReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAction)
	return l
}

func (p *SettleReq) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BizExtra)
	return l
}

func (p *SettleReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *SettleReq) DeepCopy(s interface{}) error {
	src, ok := s.(*SettleReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _splitInfo *fwe_trade_common.TradeSpiltInfo
	if src.SplitInfo != nil {
		_splitInfo = &fwe_trade_common.TradeSpiltInfo{}
		if err := _splitInfo.DeepCopy(src.SplitInfo); err != nil {
			return err
		}
	}
	p.SplitInfo = _splitInfo

	p.SettleType = src.SettleType

	if src.FinanceIDList != nil {
		p.FinanceIDList = make([]string, 0, len(src.FinanceIDList))
		for _, elem := range src.FinanceIDList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.FinanceIDList = append(p.FinanceIDList, _elem)
		}
	}

	if src.FinanceTypeList != nil {
		p.FinanceTypeList = make([]int32, 0, len(src.FinanceTypeList))
		for _, elem := range src.FinanceTypeList {
			var _elem int32
			_elem = elem
			p.FinanceTypeList = append(p.FinanceTypeList, _elem)
		}
	}

	if src.Reason != "" {
		p.Reason = kutils.StringDeepCopy(src.Reason)
	}

	if src.IPAddress != "" {
		p.IPAddress = kutils.StringDeepCopy(src.IPAddress)
	}

	if src.IsAutoWithdraw != nil {
		tmp := *src.IsAutoWithdraw
		p.IsAutoWithdraw = &tmp
	}

	p.RuleID = src.RuleID

	if src.Params != "" {
		p.Params = kutils.StringDeepCopy(src.Params)
	}

	if src.SubsidyOutUID != nil {
		var tmp string
		if *src.SubsidyOutUID != "" {
			tmp = kutils.StringDeepCopy(*src.SubsidyOutUID)
		}
		p.SubsidyOutUID = &tmp
	}

	if src.SubsidyOutUIDType != nil {
		tmp := *src.SubsidyOutUIDType
		p.SubsidyOutUIDType = &tmp
	}

	if src.SubsidyInfoList != nil {
		p.SubsidyInfoList = make([]*fwe_trade_common.SubsidyInfo, 0, len(src.SubsidyInfoList))
		for _, elem := range src.SubsidyInfoList {
			var _elem *fwe_trade_common.SubsidyInfo
			if elem != nil {
				_elem = &fwe_trade_common.SubsidyInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.SubsidyInfoList = append(p.SubsidyInfoList, _elem)
		}
	}

	p.OmitInfraSubsidy = src.OmitInfraSubsidy

	if src.TotalSubsidyAmount != nil {
		tmp := *src.TotalSubsidyAmount
		p.TotalSubsidyAmount = &tmp
	}

	if src.CallbackAction != "" {
		p.CallbackAction = kutils.StringDeepCopy(src.CallbackAction)
	}

	if src.BizExtra != "" {
		p.BizExtra = kutils.StringDeepCopy(src.BizExtra)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *SettleRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SettleRsp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *SettleRsp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MergeSettleNo = _field
	return offset, nil
}

func (p *SettleRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *SettleRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *SettleRsp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *SettleRsp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MergeSettleNo)
	return offset
}

func (p *SettleRsp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MergeSettleNo)
	return l
}

func (p *SettleRsp) DeepCopy(s interface{}) error {
	src, ok := s.(*SettleRsp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.MergeSettleNo != "" {
		p.MergeSettleNo = kutils.StringDeepCopy(src.MergeSettleNo)
	}

	return nil
}

func (p *ConfirmLoanData) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConfirmLoanData[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ConfirmLoanData) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FweAccountID = _field
	return offset, nil
}

func (p *ConfirmLoanData) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ShopID = _field
	return offset, nil
}

func (p *ConfirmLoanData) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ShopName = _field
	return offset, nil
}

func (p *ConfirmLoanData) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CarVin = _field
	return offset, nil
}

func (p *ConfirmLoanData) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BorrowerName = _field
	return offset, nil
}

func (p *ConfirmLoanData) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Amount = _field
	return offset, nil
}

func (p *ConfirmLoanData) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceName = _field
	return offset, nil
}

func (p *ConfirmLoanData) FastReadField8(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewBankInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.OutBankInfo = _field
	return offset, nil
}

func (p *ConfirmLoanData) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BizOrderID = _field
	return offset, nil
}

func (p *ConfirmLoanData) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ConfirmLoanData) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ConfirmLoanData) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ConfirmLoanData) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FweAccountID)
	return offset
}

func (p *ConfirmLoanData) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ShopID)
	return offset
}

func (p *ConfirmLoanData) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ShopName)
	return offset
}

func (p *ConfirmLoanData) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CarVin)
	return offset
}

func (p *ConfirmLoanData) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BorrowerName)
	return offset
}

func (p *ConfirmLoanData) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Amount)
	return offset
}

func (p *ConfirmLoanData) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FinanceName)
	return offset
}

func (p *ConfirmLoanData) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 8)
	offset += p.OutBankInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *ConfirmLoanData) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BizOrderID)
	return offset
}

func (p *ConfirmLoanData) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FweAccountID)
	return l
}

func (p *ConfirmLoanData) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ShopID)
	return l
}

func (p *ConfirmLoanData) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ShopName)
	return l
}

func (p *ConfirmLoanData) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CarVin)
	return l
}

func (p *ConfirmLoanData) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BorrowerName)
	return l
}

func (p *ConfirmLoanData) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ConfirmLoanData) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FinanceName)
	return l
}

func (p *ConfirmLoanData) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.OutBankInfo.BLength()
	return l
}

func (p *ConfirmLoanData) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BizOrderID)
	return l
}

func (p *ConfirmLoanData) DeepCopy(s interface{}) error {
	src, ok := s.(*ConfirmLoanData)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.FweAccountID != "" {
		p.FweAccountID = kutils.StringDeepCopy(src.FweAccountID)
	}

	if src.ShopID != "" {
		p.ShopID = kutils.StringDeepCopy(src.ShopID)
	}

	if src.ShopName != "" {
		p.ShopName = kutils.StringDeepCopy(src.ShopName)
	}

	if src.CarVin != "" {
		p.CarVin = kutils.StringDeepCopy(src.CarVin)
	}

	if src.BorrowerName != "" {
		p.BorrowerName = kutils.StringDeepCopy(src.BorrowerName)
	}

	p.Amount = src.Amount

	if src.FinanceName != "" {
		p.FinanceName = kutils.StringDeepCopy(src.FinanceName)
	}

	var _outBankInfo *fwe_trade_common.BankInfo
	if src.OutBankInfo != nil {
		_outBankInfo = &fwe_trade_common.BankInfo{}
		if err := _outBankInfo.DeepCopy(src.OutBankInfo); err != nil {
			return err
		}
	}
	p.OutBankInfo = _outBankInfo

	if src.BizOrderID != "" {
		p.BizOrderID = kutils.StringDeepCopy(src.BizOrderID)
	}

	return nil
}

func (p *ConfirmLoanReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField50(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField101(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConfirmLoanReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ConfirmLoanReq) FastReadField1(buf []byte) (int, error) {
	offset := 0
	_field := NewConfirmLoanData()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.ConfirmLoanData = _field
	return offset, nil
}

func (p *ConfirmLoanReq) FastReadField50(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FireCondition = _field
	return offset, nil
}

func (p *ConfirmLoanReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SuccCallbackAction = _field
	return offset, nil
}

func (p *ConfirmLoanReq) FastReadField101(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FailCallbackAction = _field
	return offset, nil
}

func (p *ConfirmLoanReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *ConfirmLoanReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ConfirmLoanReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField50(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField101(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ConfirmLoanReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field50Length()
		l += p.field100Length()
		l += p.field101Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ConfirmLoanReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 1)
	offset += p.ConfirmLoanData.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *ConfirmLoanReq) fastWriteField50(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFireCondition() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 50)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.FireCondition)
	}
	return offset
}

func (p *ConfirmLoanReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SuccCallbackAction)
	return offset
}

func (p *ConfirmLoanReq) fastWriteField101(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 101)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FailCallbackAction)
	return offset
}

func (p *ConfirmLoanReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *ConfirmLoanReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.ConfirmLoanData.BLength()
	return l
}

func (p *ConfirmLoanReq) field50Length() int {
	l := 0
	if p.IsSetFireCondition() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.FireCondition)
	}
	return l
}

func (p *ConfirmLoanReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SuccCallbackAction)
	return l
}

func (p *ConfirmLoanReq) field101Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FailCallbackAction)
	return l
}

func (p *ConfirmLoanReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *ConfirmLoanReq) DeepCopy(s interface{}) error {
	src, ok := s.(*ConfirmLoanReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	var _confirmLoanData *ConfirmLoanData
	if src.ConfirmLoanData != nil {
		_confirmLoanData = &ConfirmLoanData{}
		if err := _confirmLoanData.DeepCopy(src.ConfirmLoanData); err != nil {
			return err
		}
	}
	p.ConfirmLoanData = _confirmLoanData

	if src.FireCondition != nil {
		var tmp string
		if *src.FireCondition != "" {
			tmp = kutils.StringDeepCopy(*src.FireCondition)
		}
		p.FireCondition = &tmp
	}

	if src.SuccCallbackAction != "" {
		p.SuccCallbackAction = kutils.StringDeepCopy(src.SuccCallbackAction)
	}

	if src.FailCallbackAction != "" {
		p.FailCallbackAction = kutils.StringDeepCopy(src.FailCallbackAction)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *ConfirmLoanRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ConfirmLoanRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ConfirmLoanRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ConfirmLoanRsp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ConfirmLoanRsp) DeepCopy(s interface{}) error {

	return nil
}

func (p *AgreementPayReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 50:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField50(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AgreementPayReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *AgreementPayReq) FastReadField50(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FireCondition = _field
	return offset, nil
}

func (p *AgreementPayReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CallbackAction = _field
	return offset, nil
}

func (p *AgreementPayReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *AgreementPayReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *AgreementPayReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField50(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *AgreementPayReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field50Length()
		l += p.field100Length()
		l += p.field200Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *AgreementPayReq) fastWriteField50(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFireCondition() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 50)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.FireCondition)
	}
	return offset
}

func (p *AgreementPayReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CallbackAction)
	return offset
}

func (p *AgreementPayReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 200)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *AgreementPayReq) field50Length() int {
	l := 0
	if p.IsSetFireCondition() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.FireCondition)
	}
	return l
}

func (p *AgreementPayReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CallbackAction)
	return l
}

func (p *AgreementPayReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *AgreementPayReq) DeepCopy(s interface{}) error {
	src, ok := s.(*AgreementPayReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.FireCondition != nil {
		var tmp string
		if *src.FireCondition != "" {
			tmp = kutils.StringDeepCopy(*src.FireCondition)
		}
		p.FireCondition = &tmp
	}

	if src.CallbackAction != "" {
		p.CallbackAction = kutils.StringDeepCopy(src.CallbackAction)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *AgreementPayRsp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
		offset += l
		if err != nil {
			goto SkipFieldError
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *AgreementPayRsp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *AgreementPayRsp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *AgreementPayRsp) BLength() int {
	l := 0
	if p != nil {
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *AgreementPayRsp) DeepCopy(s interface{}) error {

	return nil
}

func (p *InvoiceReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInvoiceBizScene bool = false
	var issetInvoiceMode bool = false
	var issetInvoiceType bool = false
	var issetInvoiceFuncType bool = false
	var issetRelatedOutID bool = false
	var issetInvoiceItem bool = false
	var issetActualInvoiceItem bool = false
	var issetAmount bool = false
	var issetIncludeTax bool = false
	var issetTaxRate bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInvoiceBizScene = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInvoiceMode = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInvoiceType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInvoiceFuncType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetRelatedOutID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetInvoiceItem = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField22(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetActualInvoiceItem = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField23(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAmount = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField24(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetIncludeTax = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 25:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField25(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTaxRate = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField26(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField27(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField28(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField31(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField32(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField33(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField34(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField40(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 50:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField50(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 60:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField60(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 200:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField200(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 201:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField201(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 210:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField210(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 211:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField211(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 212:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField212(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetInvoiceBizScene {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInvoiceMode {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInvoiceType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInvoiceFuncType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRelatedOutID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetInvoiceItem {
		fieldId = 21
		goto RequiredFieldNotSetError
	}

	if !issetActualInvoiceItem {
		fieldId = 22
		goto RequiredFieldNotSetError
	}

	if !issetAmount {
		fieldId = 23
		goto RequiredFieldNotSetError
	}

	if !issetIncludeTax {
		fieldId = 24
		goto RequiredFieldNotSetError
	}

	if !issetTaxRate {
		fieldId = 25
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InvoiceReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_InvoiceReq[fieldId]))
}

func (p *InvoiceReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InvoiceBizScene = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InvoiceMode = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field core0.InvoiceType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = core0.InvoiceType(v)
	}
	p.InvoiceType = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field core0.InvoiceFuncType
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l

		_field = core0.InvoiceFuncType(v)
	}
	p.InvoiceFuncType = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RelatedOutID = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SubjectID = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField12(buf []byte) (int, error) {
	offset := 0
	_field := core0.NewInvoicePartyInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.CustomerInfo = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField13(buf []byte) (int, error) {
	offset := 0
	_field := core0.NewInvoicePartyInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.SalesInfo = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField21(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InvoiceItem = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField22(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ActualInvoiceItem = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField23(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Amount = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField24(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.IncludeTax = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField25(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TaxRate = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField26(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ApplyRemark = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField27(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InvoiceRemark = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField28(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InvoiceContent = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField31(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContactEmail = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField32(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContactNumber = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField33(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Receiver = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField34(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ReceiveAddress = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField40(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BusinessExt1 = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField50(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.BusinessTimeExt1 = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField60(buf []byte) (int, error) {
	offset := 0
	_field := core0.NewVehicleInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.VehicleInfo = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Extra = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField200(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OperatorID = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField201(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OperatorName = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField210(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SuccCallbackAction = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField211(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FailCallbackAction = _field
	return offset, nil
}

func (p *InvoiceReq) FastReadField212(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.Tag = _field
	return offset, nil
}

func (p *InvoiceReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *InvoiceReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField23(buf[offset:], w)
		offset += p.fastWriteField24(buf[offset:], w)
		offset += p.fastWriteField25(buf[offset:], w)
		offset += p.fastWriteField50(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
		offset += p.fastWriteField22(buf[offset:], w)
		offset += p.fastWriteField26(buf[offset:], w)
		offset += p.fastWriteField27(buf[offset:], w)
		offset += p.fastWriteField28(buf[offset:], w)
		offset += p.fastWriteField31(buf[offset:], w)
		offset += p.fastWriteField32(buf[offset:], w)
		offset += p.fastWriteField33(buf[offset:], w)
		offset += p.fastWriteField34(buf[offset:], w)
		offset += p.fastWriteField40(buf[offset:], w)
		offset += p.fastWriteField60(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
		offset += p.fastWriteField200(buf[offset:], w)
		offset += p.fastWriteField201(buf[offset:], w)
		offset += p.fastWriteField210(buf[offset:], w)
		offset += p.fastWriteField211(buf[offset:], w)
		offset += p.fastWriteField212(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *InvoiceReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field21Length()
		l += p.field22Length()
		l += p.field23Length()
		l += p.field24Length()
		l += p.field25Length()
		l += p.field26Length()
		l += p.field27Length()
		l += p.field28Length()
		l += p.field31Length()
		l += p.field32Length()
		l += p.field33Length()
		l += p.field34Length()
		l += p.field40Length()
		l += p.field50Length()
		l += p.field60Length()
		l += p.field100Length()
		l += p.field200Length()
		l += p.field201Length()
		l += p.field210Length()
		l += p.field211Length()
		l += p.field212Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *InvoiceReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.InvoiceBizScene)
	return offset
}

func (p *InvoiceReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.InvoiceMode)
	return offset
}

func (p *InvoiceReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 3)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InvoiceType))
	return offset
}

func (p *InvoiceReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], int32(p.InvoiceFuncType))
	return offset
}

func (p *InvoiceReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RelatedOutID)
	return offset
}

func (p *InvoiceReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SubjectID)
	return offset
}

func (p *InvoiceReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 12)
	offset += p.CustomerInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *InvoiceReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 13)
	offset += p.SalesInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *InvoiceReq) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 21)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InvoiceItem)
	return offset
}

func (p *InvoiceReq) fastWriteField22(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 22)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ActualInvoiceItem)
	return offset
}

func (p *InvoiceReq) fastWriteField23(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 23)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Amount)
	return offset
}

func (p *InvoiceReq) fastWriteField24(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 24)
	offset += thrift.Binary.WriteI32(buf[offset:], p.IncludeTax)
	return offset
}

func (p *InvoiceReq) fastWriteField25(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 25)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TaxRate)
	return offset
}

func (p *InvoiceReq) fastWriteField26(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 26)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ApplyRemark)
	return offset
}

func (p *InvoiceReq) fastWriteField27(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 27)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InvoiceRemark)
	return offset
}

func (p *InvoiceReq) fastWriteField28(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 28)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InvoiceContent)
	return offset
}

func (p *InvoiceReq) fastWriteField31(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 31)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ContactEmail)
	return offset
}

func (p *InvoiceReq) fastWriteField32(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 32)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ContactNumber)
	return offset
}

func (p *InvoiceReq) fastWriteField33(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 33)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Receiver)
	return offset
}

func (p *InvoiceReq) fastWriteField34(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 34)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ReceiveAddress)
	return offset
}

func (p *InvoiceReq) fastWriteField40(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBusinessExt1() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 40)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.BusinessExt1)
	}
	return offset
}

func (p *InvoiceReq) fastWriteField50(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetBusinessTimeExt1() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 50)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.BusinessTimeExt1)
	}
	return offset
}

func (p *InvoiceReq) fastWriteField60(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 60)
	offset += p.VehicleInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *InvoiceReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Extra)
	return offset
}

func (p *InvoiceReq) fastWriteField200(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 200)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OperatorID)
	return offset
}

func (p *InvoiceReq) fastWriteField201(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 201)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OperatorName)
	return offset
}

func (p *InvoiceReq) fastWriteField210(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 210)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SuccCallbackAction)
	return offset
}

func (p *InvoiceReq) fastWriteField211(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 211)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.FailCallbackAction)
	return offset
}

func (p *InvoiceReq) fastWriteField212(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 212)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.Tag {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *InvoiceReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *InvoiceReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *InvoiceReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *InvoiceReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *InvoiceReq) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RelatedOutID)
	return l
}

func (p *InvoiceReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SubjectID)
	return l
}

func (p *InvoiceReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.CustomerInfo.BLength()
	return l
}

func (p *InvoiceReq) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.SalesInfo.BLength()
	return l
}

func (p *InvoiceReq) field21Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InvoiceItem)
	return l
}

func (p *InvoiceReq) field22Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ActualInvoiceItem)
	return l
}

func (p *InvoiceReq) field23Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *InvoiceReq) field24Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *InvoiceReq) field25Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *InvoiceReq) field26Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ApplyRemark)
	return l
}

func (p *InvoiceReq) field27Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InvoiceRemark)
	return l
}

func (p *InvoiceReq) field28Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InvoiceContent)
	return l
}

func (p *InvoiceReq) field31Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ContactEmail)
	return l
}

func (p *InvoiceReq) field32Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ContactNumber)
	return l
}

func (p *InvoiceReq) field33Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Receiver)
	return l
}

func (p *InvoiceReq) field34Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ReceiveAddress)
	return l
}

func (p *InvoiceReq) field40Length() int {
	l := 0
	if p.IsSetBusinessExt1() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.BusinessExt1)
	}
	return l
}

func (p *InvoiceReq) field50Length() int {
	l := 0
	if p.IsSetBusinessTimeExt1() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *InvoiceReq) field60Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.VehicleInfo.BLength()
	return l
}

func (p *InvoiceReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Extra)
	return l
}

func (p *InvoiceReq) field200Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OperatorID)
	return l
}

func (p *InvoiceReq) field201Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OperatorName)
	return l
}

func (p *InvoiceReq) field210Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SuccCallbackAction)
	return l
}

func (p *InvoiceReq) field211Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.FailCallbackAction)
	return l
}

func (p *InvoiceReq) field212Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.Tag {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *InvoiceReq) DeepCopy(s interface{}) error {
	src, ok := s.(*InvoiceReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.InvoiceBizScene = src.InvoiceBizScene

	p.InvoiceMode = src.InvoiceMode

	p.InvoiceType = src.InvoiceType

	p.InvoiceFuncType = src.InvoiceFuncType

	if src.RelatedOutID != "" {
		p.RelatedOutID = kutils.StringDeepCopy(src.RelatedOutID)
	}

	if src.SubjectID != "" {
		p.SubjectID = kutils.StringDeepCopy(src.SubjectID)
	}

	var _customerInfo *core0.InvoicePartyInfo
	if src.CustomerInfo != nil {
		_customerInfo = &core0.InvoicePartyInfo{}
		if err := _customerInfo.DeepCopy(src.CustomerInfo); err != nil {
			return err
		}
	}
	p.CustomerInfo = _customerInfo

	var _salesInfo *core0.InvoicePartyInfo
	if src.SalesInfo != nil {
		_salesInfo = &core0.InvoicePartyInfo{}
		if err := _salesInfo.DeepCopy(src.SalesInfo); err != nil {
			return err
		}
	}
	p.SalesInfo = _salesInfo

	if src.InvoiceItem != "" {
		p.InvoiceItem = kutils.StringDeepCopy(src.InvoiceItem)
	}

	if src.ActualInvoiceItem != "" {
		p.ActualInvoiceItem = kutils.StringDeepCopy(src.ActualInvoiceItem)
	}

	p.Amount = src.Amount

	p.IncludeTax = src.IncludeTax

	p.TaxRate = src.TaxRate

	if src.ApplyRemark != "" {
		p.ApplyRemark = kutils.StringDeepCopy(src.ApplyRemark)
	}

	if src.InvoiceRemark != "" {
		p.InvoiceRemark = kutils.StringDeepCopy(src.InvoiceRemark)
	}

	if src.InvoiceContent != "" {
		p.InvoiceContent = kutils.StringDeepCopy(src.InvoiceContent)
	}

	if src.ContactEmail != "" {
		p.ContactEmail = kutils.StringDeepCopy(src.ContactEmail)
	}

	if src.ContactNumber != "" {
		p.ContactNumber = kutils.StringDeepCopy(src.ContactNumber)
	}

	if src.Receiver != "" {
		p.Receiver = kutils.StringDeepCopy(src.Receiver)
	}

	if src.ReceiveAddress != "" {
		p.ReceiveAddress = kutils.StringDeepCopy(src.ReceiveAddress)
	}

	if src.BusinessExt1 != nil {
		var tmp string
		if *src.BusinessExt1 != "" {
			tmp = kutils.StringDeepCopy(*src.BusinessExt1)
		}
		p.BusinessExt1 = &tmp
	}

	if src.BusinessTimeExt1 != nil {
		tmp := *src.BusinessTimeExt1
		p.BusinessTimeExt1 = &tmp
	}

	var _vehicleInfo *core0.VehicleInfo
	if src.VehicleInfo != nil {
		_vehicleInfo = &core0.VehicleInfo{}
		if err := _vehicleInfo.DeepCopy(src.VehicleInfo); err != nil {
			return err
		}
	}
	p.VehicleInfo = _vehicleInfo

	if src.Extra != "" {
		p.Extra = kutils.StringDeepCopy(src.Extra)
	}

	if src.OperatorID != "" {
		p.OperatorID = kutils.StringDeepCopy(src.OperatorID)
	}

	if src.OperatorName != "" {
		p.OperatorName = kutils.StringDeepCopy(src.OperatorName)
	}

	if src.SuccCallbackAction != "" {
		p.SuccCallbackAction = kutils.StringDeepCopy(src.SuccCallbackAction)
	}

	if src.FailCallbackAction != "" {
		p.FailCallbackAction = kutils.StringDeepCopy(src.FailCallbackAction)
	}

	if src.Tag != nil {
		p.Tag = make(map[string]string, len(src.Tag))
		for key, val := range src.Tag {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.Tag[_key] = _val
		}
	}

	return nil
}

func (p *InvoiceResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InvoiceResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *InvoiceResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.InvoiceOrderID = _field
	return offset, nil
}

func (p *InvoiceResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *InvoiceResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *InvoiceResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *InvoiceResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.InvoiceOrderID)
	return offset
}

func (p *InvoiceResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.InvoiceOrderID)
	return l
}

func (p *InvoiceResp) DeepCopy(s interface{}) error {
	src, ok := s.(*InvoiceResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.InvoiceOrderID != "" {
		p.InvoiceOrderID = kutils.StringDeepCopy(src.InvoiceOrderID)
	}

	return nil
}

func (p *AfterSaleStartReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AfterSaleStartReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *AfterSaleStartReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NeedAfterSaleCheck = _field
	return offset, nil
}

func (p *AfterSaleStartReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *AfterSaleStartReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *AfterSaleStartReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *AfterSaleStartReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 1)
	offset += thrift.Binary.WriteBool(buf[offset:], p.NeedAfterSaleCheck)
	return offset
}

func (p *AfterSaleStartReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *AfterSaleStartReq) DeepCopy(s interface{}) error {
	src, ok := s.(*AfterSaleStartReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.NeedAfterSaleCheck = src.NeedAfterSaleCheck

	return nil
}

func (p *TransferReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMerchantID bool = false
	var issetAppID bool = false
	var issetMerchantName bool = false
	var issetFinanceOrderType bool = false
	var issetUID bool = false
	var issetUIDType bool = false
	var issetTradeTime bool = false
	var issetPayerInfo bool = false
	var issetPayeeInfo bool = false
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetMerchantID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetAppID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetMerchantName = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetFinanceOrderType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetUID = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetUIDType = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetTradeTime = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPayerInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
				issetPayeeInfo = true
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	if !issetMerchantID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAppID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMerchantName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetFinanceOrderType {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetUID {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetUIDType {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetTradeTime {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetPayerInfo {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetPayeeInfo {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TransferReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
RequiredFieldNotSetError:
	return offset, thrift.NewProtocolException(thrift.INVALID_DATA, fmt.Sprintf("required field %s is not set", fieldIDToName_TransferReq[fieldId]))
}

func (p *TransferReq) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantID = _field
	return offset, nil
}

func (p *TransferReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AppID = _field
	return offset, nil
}

func (p *TransferReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantName = _field
	return offset, nil
}

func (p *TransferReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FcType = _field
	return offset, nil
}

func (p *TransferReq) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.FcSceneCode = _field
	return offset, nil
}

func (p *TransferReq) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FinanceOrderType = _field
	return offset, nil
}

func (p *TransferReq) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UID = _field
	return offset, nil
}

func (p *TransferReq) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UIDType = _field
	return offset, nil
}

func (p *TransferReq) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TradeTime = _field
	return offset, nil
}

func (p *TransferReq) FastReadField10(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewParticipant()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.PayerInfo = _field
	return offset, nil
}

func (p *TransferReq) FastReadField11(buf []byte) (int, error) {
	offset := 0
	_field := fwe_trade_common.NewParticipant()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.PayeeInfo = _field
	return offset, nil
}

func (p *TransferReq) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Amount = _field
	return offset, nil
}

func (p *TransferReq) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TransferName = _field
	return offset, nil
}

func (p *TransferReq) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TransferDesc = _field
	return offset, nil
}

func (p *TransferReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TransferReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TransferReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TransferReq) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantID)
	return offset
}

func (p *TransferReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AppID)
	return offset
}

func (p *TransferReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.MerchantName)
	return offset
}

func (p *TransferReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFcType() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.FcType)
	}
	return offset
}

func (p *TransferReq) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFcSceneCode() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 5)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.FcSceneCode)
	}
	return offset
}

func (p *TransferReq) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 6)
	offset += thrift.Binary.WriteI32(buf[offset:], p.FinanceOrderType)
	return offset
}

func (p *TransferReq) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.UID)
	return offset
}

func (p *TransferReq) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 8)
	offset += thrift.Binary.WriteI64(buf[offset:], p.UIDType)
	return offset
}

func (p *TransferReq) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 9)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TradeTime)
	return offset
}

func (p *TransferReq) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 10)
	offset += p.PayerInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *TransferReq) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 11)
	offset += p.PayeeInfo.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *TransferReq) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 12)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Amount)
	return offset
}

func (p *TransferReq) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 13)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TransferName)
	return offset
}

func (p *TransferReq) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TransferDesc)
	return offset
}

func (p *TransferReq) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantID)
	return l
}

func (p *TransferReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AppID)
	return l
}

func (p *TransferReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.MerchantName)
	return l
}

func (p *TransferReq) field4Length() int {
	l := 0
	if p.IsSetFcType() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.FcType)
	}
	return l
}

func (p *TransferReq) field5Length() int {
	l := 0
	if p.IsSetFcSceneCode() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *TransferReq) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *TransferReq) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.UID)
	return l
}

func (p *TransferReq) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *TransferReq) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *TransferReq) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.PayerInfo.BLength()
	return l
}

func (p *TransferReq) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.PayeeInfo.BLength()
	return l
}

func (p *TransferReq) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *TransferReq) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TransferName)
	return l
}

func (p *TransferReq) field14Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TransferDesc)
	return l
}

func (p *TransferReq) DeepCopy(s interface{}) error {
	src, ok := s.(*TransferReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.MerchantID != "" {
		p.MerchantID = kutils.StringDeepCopy(src.MerchantID)
	}

	if src.AppID != "" {
		p.AppID = kutils.StringDeepCopy(src.AppID)
	}

	if src.MerchantName != "" {
		p.MerchantName = kutils.StringDeepCopy(src.MerchantName)
	}

	if src.FcType != nil {
		var tmp string
		if *src.FcType != "" {
			tmp = kutils.StringDeepCopy(*src.FcType)
		}
		p.FcType = &tmp
	}

	if src.FcSceneCode != nil {
		tmp := *src.FcSceneCode
		p.FcSceneCode = &tmp
	}

	p.FinanceOrderType = src.FinanceOrderType

	if src.UID != "" {
		p.UID = kutils.StringDeepCopy(src.UID)
	}

	p.UIDType = src.UIDType

	p.TradeTime = src.TradeTime

	var _payerInfo *fwe_trade_common.Participant
	if src.PayerInfo != nil {
		_payerInfo = &fwe_trade_common.Participant{}
		if err := _payerInfo.DeepCopy(src.PayerInfo); err != nil {
			return err
		}
	}
	p.PayerInfo = _payerInfo

	var _payeeInfo *fwe_trade_common.Participant
	if src.PayeeInfo != nil {
		_payeeInfo = &fwe_trade_common.Participant{}
		if err := _payeeInfo.DeepCopy(src.PayeeInfo); err != nil {
			return err
		}
	}
	p.PayeeInfo = _payeeInfo

	p.Amount = src.Amount

	if src.TransferName != "" {
		p.TransferName = kutils.StringDeepCopy(src.TransferName)
	}

	if src.TransferDesc != "" {
		p.TransferDesc = kutils.StringDeepCopy(src.TransferDesc)
	}

	return nil
}

func (p *TransferResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TransferResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *TransferResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TradeNo = _field
	return offset, nil
}

func (p *TransferResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *TransferResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *TransferResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *TransferResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.TradeNo)
	return offset
}

func (p *TransferResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.TradeNo)
	return l
}

func (p *TransferResp) DeepCopy(s interface{}) error {
	src, ok := s.(*TransferResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.TradeNo != "" {
		p.TradeNo = kutils.StringDeepCopy(src.TradeNo)
	}

	return nil
}

func (p *LifeVerifyReq) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField100(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeVerifyReq[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *LifeVerifyReq) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.ItemOrder, 0, size)
	values := make([]fwe_trade_common.ItemOrder, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ItemOrderList = _field
	return offset, nil
}

func (p *LifeVerifyReq) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field bool
	if v, l, err := thrift.Binary.ReadBool(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UseAll = _field
	return offset, nil
}

func (p *LifeVerifyReq) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PoiInfo = _field
	return offset, nil
}

func (p *LifeVerifyReq) FastReadField100(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AppID = _field
	return offset, nil
}

func (p *LifeVerifyReq) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *LifeVerifyReq) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField100(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *LifeVerifyReq) BLength() int {
	l := 0
	if p != nil {
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field100Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *LifeVerifyReq) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ItemOrderList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *LifeVerifyReq) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.BOOL, 3)
	offset += thrift.Binary.WriteBool(buf[offset:], p.UseAll)
	return offset
}

func (p *LifeVerifyReq) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PoiInfo)
	return offset
}

func (p *LifeVerifyReq) fastWriteField100(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 100)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AppID)
	return offset
}

func (p *LifeVerifyReq) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ItemOrderList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *LifeVerifyReq) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.BoolLength()
	return l
}

func (p *LifeVerifyReq) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PoiInfo)
	return l
}

func (p *LifeVerifyReq) field100Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AppID)
	return l
}

func (p *LifeVerifyReq) DeepCopy(s interface{}) error {
	src, ok := s.(*LifeVerifyReq)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ItemOrderList != nil {
		p.ItemOrderList = make([]*fwe_trade_common.ItemOrder, 0, len(src.ItemOrderList))
		for _, elem := range src.ItemOrderList {
			var _elem *fwe_trade_common.ItemOrder
			if elem != nil {
				_elem = &fwe_trade_common.ItemOrder{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ItemOrderList = append(p.ItemOrderList, _elem)
		}
	}

	p.UseAll = src.UseAll

	if src.PoiInfo != "" {
		p.PoiInfo = kutils.StringDeepCopy(src.PoiInfo)
	}

	if src.AppID != "" {
		p.AppID = kutils.StringDeepCopy(src.AppID)
	}

	return nil
}

func (p *LifeVerifyResp) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeVerifyResp[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *LifeVerifyResp) FastReadField1(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*fwe_trade_common.VerifyResult_, 0, size)
	values := make([]fwe_trade_common.VerifyResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.VerifyResults = _field
	return offset, nil
}

func (p *LifeVerifyResp) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *LifeVerifyResp) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *LifeVerifyResp) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *LifeVerifyResp) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 1)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.VerifyResults {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *LifeVerifyResp) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.VerifyResults {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *LifeVerifyResp) DeepCopy(s interface{}) error {
	src, ok := s.(*LifeVerifyResp)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.VerifyResults != nil {
		p.VerifyResults = make([]*fwe_trade_common.VerifyResult_, 0, len(src.VerifyResults))
		for _, elem := range src.VerifyResults {
			var _elem *fwe_trade_common.VerifyResult_
			if elem != nil {
				_elem = &fwe_trade_common.VerifyResult_{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.VerifyResults = append(p.VerifyResults, _elem)
		}
	}

	return nil
}
