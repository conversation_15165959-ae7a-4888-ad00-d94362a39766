// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package execution_common

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	core0 "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_invoice/core"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
)

type CreateFinanceReq struct {
	FinanceList []*fwe_trade_common.FinanceInfo `thrift:"finance_list,1" frugal:"1,default,list<fwe_trade_common.FinanceInfo>" json:"finance_list"`
	MerchantID  string                          `thrift:"merchant_id,2" frugal:"2,default,string" json:"merchant_id"`
	Tag         map[string]string               `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewCreateFinanceReq() *CreateFinanceReq {
	return &CreateFinanceReq{}
}

func (p *CreateFinanceReq) InitDefault() {
}

func (p *CreateFinanceReq) GetFinanceList() (v []*fwe_trade_common.FinanceInfo) {
	return p.FinanceList
}

func (p *CreateFinanceReq) GetMerchantID() (v string) {
	return p.MerchantID
}

func (p *CreateFinanceReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *CreateFinanceReq) SetFinanceList(val []*fwe_trade_common.FinanceInfo) {
	p.FinanceList = val
}
func (p *CreateFinanceReq) SetMerchantID(val string) {
	p.MerchantID = val
}
func (p *CreateFinanceReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_CreateFinanceReq = map[int16]string{
	1:   "finance_list",
	2:   "merchant_id",
	200: "tag",
}

func (p *CreateFinanceReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateFinanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateFinanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateFinanceReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.FinanceInfo, 0, size)
	values := make([]fwe_trade_common.FinanceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FinanceList = _field
	return nil
}
func (p *CreateFinanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MerchantID = _field
	return nil
}
func (p *CreateFinanceReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *CreateFinanceReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateFinanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateFinanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateFinanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FinanceList)); err != nil {
		return err
	}
	for _, v := range p.FinanceList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateFinanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merchant_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MerchantID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateFinanceReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *CreateFinanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateFinanceReq(%+v)", *p)

}

type CreateFinanceRsp struct {
}

func NewCreateFinanceRsp() *CreateFinanceRsp {
	return &CreateFinanceRsp{}
}

func (p *CreateFinanceRsp) InitDefault() {
}

var fieldIDToName_CreateFinanceRsp = map[int16]string{}

func (p *CreateFinanceRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateFinanceRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateFinanceRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CreateFinanceRsp")

	if err = oprot.WriteStructBegin("CreateFinanceRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateFinanceRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateFinanceRsp(%+v)", *p)

}

type UpdateOrderReq struct {
	OrderName           *string                            `thrift:"order_name,1,optional" frugal:"1,optional,string" json:"order_name,omitempty"`
	OrderDesc           *string                            `thrift:"order_desc,2,optional" frugal:"2,optional,string" json:"order_desc,omitempty"`
	TotalAmount         *int64                             `thrift:"total_amount,3,optional" frugal:"3,optional,i64" json:"total_amount,omitempty"`
	FireCondition       *string                            `thrift:"fire_condition,4,optional" frugal:"4,optional,string" json:"fire_condition,omitempty"`
	ProductInfo         *fwe_trade_common.ProductInfo      `thrift:"product_info,50" frugal:"50,default,fwe_trade_common.ProductInfo" json:"product_info"`
	BuyerInfo           *fwe_trade_common.TradeSubjectInfo `thrift:"buyer_info,51" frugal:"51,default,fwe_trade_common.TradeSubjectInfo" json:"buyer_info"`
	SellerInfo          *fwe_trade_common.TradeSubjectInfo `thrift:"seller_info,52" frugal:"52,default,fwe_trade_common.TradeSubjectInfo" json:"seller_info"`
	ServiceProviderInfo *fwe_trade_common.TradeSubjectInfo `thrift:"service_provider_info,53" frugal:"53,default,fwe_trade_common.TradeSubjectInfo" json:"service_provider_info"`
	TalentInfo          *fwe_trade_common.TradeSubjectInfo `thrift:"talent_info,54" frugal:"54,default,fwe_trade_common.TradeSubjectInfo" json:"talent_info"`
	UpsertFinanceList   []*fwe_trade_common.FinanceInfo    `thrift:"upsert_finance_list,55" frugal:"55,default,list<fwe_trade_common.FinanceInfo>" json:"upsert_finance_list"`
	DeleteFinanceList   []string                           `thrift:"delete_finance_list,56" frugal:"56,default,list<string>" json:"delete_finance_list"`
	PurchasePlan        *fwe_trade_common.PurchasePlan     `thrift:"purchase_plan,57" frugal:"57,default,fwe_trade_common.PurchasePlan" json:"purchase_plan"`
	Tag                 map[string]string                  `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
	Extra               map[string]string                  `thrift:"extra,201" frugal:"201,default,map<string:string>" json:"extra"`
	Operator            *fwe_trade_common.OperatorInfo     `thrift:"operator,202" frugal:"202,default,fwe_trade_common.OperatorInfo" json:"operator"`
}

func NewUpdateOrderReq() *UpdateOrderReq {
	return &UpdateOrderReq{}
}

func (p *UpdateOrderReq) InitDefault() {
}

var UpdateOrderReq_OrderName_DEFAULT string

func (p *UpdateOrderReq) GetOrderName() (v string) {
	if !p.IsSetOrderName() {
		return UpdateOrderReq_OrderName_DEFAULT
	}
	return *p.OrderName
}

var UpdateOrderReq_OrderDesc_DEFAULT string

func (p *UpdateOrderReq) GetOrderDesc() (v string) {
	if !p.IsSetOrderDesc() {
		return UpdateOrderReq_OrderDesc_DEFAULT
	}
	return *p.OrderDesc
}

var UpdateOrderReq_TotalAmount_DEFAULT int64

func (p *UpdateOrderReq) GetTotalAmount() (v int64) {
	if !p.IsSetTotalAmount() {
		return UpdateOrderReq_TotalAmount_DEFAULT
	}
	return *p.TotalAmount
}

var UpdateOrderReq_FireCondition_DEFAULT string

func (p *UpdateOrderReq) GetFireCondition() (v string) {
	if !p.IsSetFireCondition() {
		return UpdateOrderReq_FireCondition_DEFAULT
	}
	return *p.FireCondition
}

var UpdateOrderReq_ProductInfo_DEFAULT *fwe_trade_common.ProductInfo

func (p *UpdateOrderReq) GetProductInfo() (v *fwe_trade_common.ProductInfo) {
	if !p.IsSetProductInfo() {
		return UpdateOrderReq_ProductInfo_DEFAULT
	}
	return p.ProductInfo
}

var UpdateOrderReq_BuyerInfo_DEFAULT *fwe_trade_common.TradeSubjectInfo

func (p *UpdateOrderReq) GetBuyerInfo() (v *fwe_trade_common.TradeSubjectInfo) {
	if !p.IsSetBuyerInfo() {
		return UpdateOrderReq_BuyerInfo_DEFAULT
	}
	return p.BuyerInfo
}

var UpdateOrderReq_SellerInfo_DEFAULT *fwe_trade_common.TradeSubjectInfo

func (p *UpdateOrderReq) GetSellerInfo() (v *fwe_trade_common.TradeSubjectInfo) {
	if !p.IsSetSellerInfo() {
		return UpdateOrderReq_SellerInfo_DEFAULT
	}
	return p.SellerInfo
}

var UpdateOrderReq_ServiceProviderInfo_DEFAULT *fwe_trade_common.TradeSubjectInfo

func (p *UpdateOrderReq) GetServiceProviderInfo() (v *fwe_trade_common.TradeSubjectInfo) {
	if !p.IsSetServiceProviderInfo() {
		return UpdateOrderReq_ServiceProviderInfo_DEFAULT
	}
	return p.ServiceProviderInfo
}

var UpdateOrderReq_TalentInfo_DEFAULT *fwe_trade_common.TradeSubjectInfo

func (p *UpdateOrderReq) GetTalentInfo() (v *fwe_trade_common.TradeSubjectInfo) {
	if !p.IsSetTalentInfo() {
		return UpdateOrderReq_TalentInfo_DEFAULT
	}
	return p.TalentInfo
}

func (p *UpdateOrderReq) GetUpsertFinanceList() (v []*fwe_trade_common.FinanceInfo) {
	return p.UpsertFinanceList
}

func (p *UpdateOrderReq) GetDeleteFinanceList() (v []string) {
	return p.DeleteFinanceList
}

var UpdateOrderReq_PurchasePlan_DEFAULT *fwe_trade_common.PurchasePlan

func (p *UpdateOrderReq) GetPurchasePlan() (v *fwe_trade_common.PurchasePlan) {
	if !p.IsSetPurchasePlan() {
		return UpdateOrderReq_PurchasePlan_DEFAULT
	}
	return p.PurchasePlan
}

func (p *UpdateOrderReq) GetTag() (v map[string]string) {
	return p.Tag
}

func (p *UpdateOrderReq) GetExtra() (v map[string]string) {
	return p.Extra
}

var UpdateOrderReq_Operator_DEFAULT *fwe_trade_common.OperatorInfo

func (p *UpdateOrderReq) GetOperator() (v *fwe_trade_common.OperatorInfo) {
	if !p.IsSetOperator() {
		return UpdateOrderReq_Operator_DEFAULT
	}
	return p.Operator
}
func (p *UpdateOrderReq) SetOrderName(val *string) {
	p.OrderName = val
}
func (p *UpdateOrderReq) SetOrderDesc(val *string) {
	p.OrderDesc = val
}
func (p *UpdateOrderReq) SetTotalAmount(val *int64) {
	p.TotalAmount = val
}
func (p *UpdateOrderReq) SetFireCondition(val *string) {
	p.FireCondition = val
}
func (p *UpdateOrderReq) SetProductInfo(val *fwe_trade_common.ProductInfo) {
	p.ProductInfo = val
}
func (p *UpdateOrderReq) SetBuyerInfo(val *fwe_trade_common.TradeSubjectInfo) {
	p.BuyerInfo = val
}
func (p *UpdateOrderReq) SetSellerInfo(val *fwe_trade_common.TradeSubjectInfo) {
	p.SellerInfo = val
}
func (p *UpdateOrderReq) SetServiceProviderInfo(val *fwe_trade_common.TradeSubjectInfo) {
	p.ServiceProviderInfo = val
}
func (p *UpdateOrderReq) SetTalentInfo(val *fwe_trade_common.TradeSubjectInfo) {
	p.TalentInfo = val
}
func (p *UpdateOrderReq) SetUpsertFinanceList(val []*fwe_trade_common.FinanceInfo) {
	p.UpsertFinanceList = val
}
func (p *UpdateOrderReq) SetDeleteFinanceList(val []string) {
	p.DeleteFinanceList = val
}
func (p *UpdateOrderReq) SetPurchasePlan(val *fwe_trade_common.PurchasePlan) {
	p.PurchasePlan = val
}
func (p *UpdateOrderReq) SetTag(val map[string]string) {
	p.Tag = val
}
func (p *UpdateOrderReq) SetExtra(val map[string]string) {
	p.Extra = val
}
func (p *UpdateOrderReq) SetOperator(val *fwe_trade_common.OperatorInfo) {
	p.Operator = val
}

var fieldIDToName_UpdateOrderReq = map[int16]string{
	1:   "order_name",
	2:   "order_desc",
	3:   "total_amount",
	4:   "fire_condition",
	50:  "product_info",
	51:  "buyer_info",
	52:  "seller_info",
	53:  "service_provider_info",
	54:  "talent_info",
	55:  "upsert_finance_list",
	56:  "delete_finance_list",
	57:  "purchase_plan",
	200: "tag",
	201: "extra",
	202: "operator",
}

func (p *UpdateOrderReq) IsSetOrderName() bool {
	return p.OrderName != nil
}

func (p *UpdateOrderReq) IsSetOrderDesc() bool {
	return p.OrderDesc != nil
}

func (p *UpdateOrderReq) IsSetTotalAmount() bool {
	return p.TotalAmount != nil
}

func (p *UpdateOrderReq) IsSetFireCondition() bool {
	return p.FireCondition != nil
}

func (p *UpdateOrderReq) IsSetProductInfo() bool {
	return p.ProductInfo != nil
}

func (p *UpdateOrderReq) IsSetBuyerInfo() bool {
	return p.BuyerInfo != nil
}

func (p *UpdateOrderReq) IsSetSellerInfo() bool {
	return p.SellerInfo != nil
}

func (p *UpdateOrderReq) IsSetServiceProviderInfo() bool {
	return p.ServiceProviderInfo != nil
}

func (p *UpdateOrderReq) IsSetTalentInfo() bool {
	return p.TalentInfo != nil
}

func (p *UpdateOrderReq) IsSetPurchasePlan() bool {
	return p.PurchasePlan != nil
}

func (p *UpdateOrderReq) IsSetOperator() bool {
	return p.Operator != nil
}

func (p *UpdateOrderReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpdateOrderReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 50:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField50(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 51:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField51(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 52:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField52(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 53:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField53(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 54:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField54(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 55:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField55(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 56:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField56(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 57:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField57(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 202:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField202(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateOrderReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateOrderReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OrderName = _field
	return nil
}
func (p *UpdateOrderReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OrderDesc = _field
	return nil
}
func (p *UpdateOrderReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TotalAmount = _field
	return nil
}
func (p *UpdateOrderReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FireCondition = _field
	return nil
}
func (p *UpdateOrderReq) ReadField50(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewProductInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ProductInfo = _field
	return nil
}
func (p *UpdateOrderReq) ReadField51(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BuyerInfo = _field
	return nil
}
func (p *UpdateOrderReq) ReadField52(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SellerInfo = _field
	return nil
}
func (p *UpdateOrderReq) ReadField53(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ServiceProviderInfo = _field
	return nil
}
func (p *UpdateOrderReq) ReadField54(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeSubjectInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TalentInfo = _field
	return nil
}
func (p *UpdateOrderReq) ReadField55(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.FinanceInfo, 0, size)
	values := make([]fwe_trade_common.FinanceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.UpsertFinanceList = _field
	return nil
}
func (p *UpdateOrderReq) ReadField56(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DeleteFinanceList = _field
	return nil
}
func (p *UpdateOrderReq) ReadField57(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewPurchasePlan()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PurchasePlan = _field
	return nil
}
func (p *UpdateOrderReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}
func (p *UpdateOrderReq) ReadField201(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Extra = _field
	return nil
}
func (p *UpdateOrderReq) ReadField202(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewOperatorInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Operator = _field
	return nil
}

func (p *UpdateOrderReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpdateOrderReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateOrderReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField50(oprot); err != nil {
			fieldId = 50
			goto WriteFieldError
		}
		if err = p.writeField51(oprot); err != nil {
			fieldId = 51
			goto WriteFieldError
		}
		if err = p.writeField52(oprot); err != nil {
			fieldId = 52
			goto WriteFieldError
		}
		if err = p.writeField53(oprot); err != nil {
			fieldId = 53
			goto WriteFieldError
		}
		if err = p.writeField54(oprot); err != nil {
			fieldId = 54
			goto WriteFieldError
		}
		if err = p.writeField55(oprot); err != nil {
			fieldId = 55
			goto WriteFieldError
		}
		if err = p.writeField56(oprot); err != nil {
			fieldId = 56
			goto WriteFieldError
		}
		if err = p.writeField57(oprot); err != nil {
			fieldId = 57
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField202(oprot); err != nil {
			fieldId = 202
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateOrderReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderName() {
		if err = oprot.WriteFieldBegin("order_name", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OrderName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderDesc() {
		if err = oprot.WriteFieldBegin("order_desc", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OrderDesc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTotalAmount() {
		if err = oprot.WriteFieldBegin("total_amount", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.TotalAmount); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFireCondition() {
		if err = oprot.WriteFieldBegin("fire_condition", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FireCondition); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField50(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("product_info", thrift.STRUCT, 50); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ProductInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField51(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("buyer_info", thrift.STRUCT, 51); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BuyerInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 51 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 51 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField52(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("seller_info", thrift.STRUCT, 52); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SellerInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 52 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 52 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField53(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("service_provider_info", thrift.STRUCT, 53); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ServiceProviderInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 53 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 53 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField54(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("talent_info", thrift.STRUCT, 54); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.TalentInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 54 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 54 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField55(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("upsert_finance_list", thrift.LIST, 55); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.UpsertFinanceList)); err != nil {
		return err
	}
	for _, v := range p.UpsertFinanceList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 55 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 55 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField56(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("delete_finance_list", thrift.LIST, 56); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.DeleteFinanceList)); err != nil {
		return err
	}
	for _, v := range p.DeleteFinanceList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 56 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 56 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField57(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("purchase_plan", thrift.STRUCT, 57); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PurchasePlan.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 57 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 57 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField201(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("extra", thrift.MAP, 201); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Extra)); err != nil {
		return err
	}
	for k, v := range p.Extra {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *UpdateOrderReq) writeField202(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.STRUCT, 202); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Operator.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 202 end error: ", p), err)
}

func (p *UpdateOrderReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateOrderReq(%+v)", *p)

}

type UpdateOrderRsp struct {
}

func NewUpdateOrderRsp() *UpdateOrderRsp {
	return &UpdateOrderRsp{}
}

func (p *UpdateOrderRsp) InitDefault() {
}

var fieldIDToName_UpdateOrderRsp = map[int16]string{}

func (p *UpdateOrderRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpdateOrderRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateOrderRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UpdateOrderRsp")

	if err = oprot.WriteStructBegin("UpdateOrderRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateOrderRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateOrderRsp(%+v)", *p)

}

type UnionContCreateReq struct {
	ContType       int32                                 `thrift:"cont_type,1" frugal:"1,default,i32" json:"cont_type"`
	ContTmplID     int64                                 `thrift:"cont_tmpl_id,2" frugal:"2,default,i64" json:"cont_tmpl_id"`
	ContTmplParams map[string]string                     `thrift:"cont_tmpl_params,3" frugal:"3,default,map<string:string>" json:"cont_tmpl_params"`
	SignPartyMap   map[core.SignPosition]*core.SignParty `thrift:"sign_party_map,4,required" frugal:"4,required,map<SignPosition:core.SignParty>" json:"sign_party_map"`
	ContInAmount   int64                                 `thrift:"cont_in_amount,10" frugal:"10,default,i64" json:"cont_in_amount"`
	ContOutAmount  int64                                 `thrift:"cont_out_amount,11" frugal:"11,default,i64" json:"cont_out_amount"`
	CallbackAction string                                `thrift:"callback_action,100" frugal:"100,default,string" json:"callback_action"`
}

func NewUnionContCreateReq() *UnionContCreateReq {
	return &UnionContCreateReq{}
}

func (p *UnionContCreateReq) InitDefault() {
}

func (p *UnionContCreateReq) GetContType() (v int32) {
	return p.ContType
}

func (p *UnionContCreateReq) GetContTmplID() (v int64) {
	return p.ContTmplID
}

func (p *UnionContCreateReq) GetContTmplParams() (v map[string]string) {
	return p.ContTmplParams
}

func (p *UnionContCreateReq) GetSignPartyMap() (v map[core.SignPosition]*core.SignParty) {
	return p.SignPartyMap
}

func (p *UnionContCreateReq) GetContInAmount() (v int64) {
	return p.ContInAmount
}

func (p *UnionContCreateReq) GetContOutAmount() (v int64) {
	return p.ContOutAmount
}

func (p *UnionContCreateReq) GetCallbackAction() (v string) {
	return p.CallbackAction
}
func (p *UnionContCreateReq) SetContType(val int32) {
	p.ContType = val
}
func (p *UnionContCreateReq) SetContTmplID(val int64) {
	p.ContTmplID = val
}
func (p *UnionContCreateReq) SetContTmplParams(val map[string]string) {
	p.ContTmplParams = val
}
func (p *UnionContCreateReq) SetSignPartyMap(val map[core.SignPosition]*core.SignParty) {
	p.SignPartyMap = val
}
func (p *UnionContCreateReq) SetContInAmount(val int64) {
	p.ContInAmount = val
}
func (p *UnionContCreateReq) SetContOutAmount(val int64) {
	p.ContOutAmount = val
}
func (p *UnionContCreateReq) SetCallbackAction(val string) {
	p.CallbackAction = val
}

var fieldIDToName_UnionContCreateReq = map[int16]string{
	1:   "cont_type",
	2:   "cont_tmpl_id",
	3:   "cont_tmpl_params",
	4:   "sign_party_map",
	10:  "cont_in_amount",
	11:  "cont_out_amount",
	100: "callback_action",
}

func (p *UnionContCreateReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UnionContCreateReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSignPartyMap bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetSignPartyMap = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSignPartyMap {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UnionContCreateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UnionContCreateReq[fieldId]))
}

func (p *UnionContCreateReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContType = _field
	return nil
}
func (p *UnionContCreateReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContTmplID = _field
	return nil
}
func (p *UnionContCreateReq) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ContTmplParams = _field
	return nil
}
func (p *UnionContCreateReq) ReadField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[core.SignPosition]*core.SignParty, size)
	values := make([]core.SignParty, size)
	for i := 0; i < size; i++ {
		var _key core.SignPosition
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_key = core.SignPosition(v)
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.SignPartyMap = _field
	return nil
}
func (p *UnionContCreateReq) ReadField10(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContInAmount = _field
	return nil
}
func (p *UnionContCreateReq) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContOutAmount = _field
	return nil
}
func (p *UnionContCreateReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackAction = _field
	return nil
}

func (p *UnionContCreateReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UnionContCreateReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UnionContCreateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UnionContCreateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ContType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UnionContCreateReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_tmpl_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ContTmplID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UnionContCreateReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_tmpl_params", thrift.MAP, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ContTmplParams)); err != nil {
		return err
	}
	for k, v := range p.ContTmplParams {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UnionContCreateReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sign_party_map", thrift.MAP, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.SignPartyMap)); err != nil {
		return err
	}
	for k, v := range p.SignPartyMap {
		if err := oprot.WriteI32(int32(k)); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UnionContCreateReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_in_amount", thrift.I64, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ContInAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *UnionContCreateReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_out_amount", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ContOutAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *UnionContCreateReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_action", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}

func (p *UnionContCreateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UnionContCreateReq(%+v)", *p)

}

type UnionContCreateRsp struct {
	ContNo    string                       `thrift:"cont_no,1" frugal:"1,default,string" json:"cont_no"`
	SignLinks map[core.SignPosition]string `thrift:"sign_links,2" frugal:"2,default,map<SignPosition:string>" json:"sign_links"`
}

func NewUnionContCreateRsp() *UnionContCreateRsp {
	return &UnionContCreateRsp{}
}

func (p *UnionContCreateRsp) InitDefault() {
}

func (p *UnionContCreateRsp) GetContNo() (v string) {
	return p.ContNo
}

func (p *UnionContCreateRsp) GetSignLinks() (v map[core.SignPosition]string) {
	return p.SignLinks
}
func (p *UnionContCreateRsp) SetContNo(val string) {
	p.ContNo = val
}
func (p *UnionContCreateRsp) SetSignLinks(val map[core.SignPosition]string) {
	p.SignLinks = val
}

var fieldIDToName_UnionContCreateRsp = map[int16]string{
	1: "cont_no",
	2: "sign_links",
}

func (p *UnionContCreateRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UnionContCreateRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UnionContCreateRsp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UnionContCreateRsp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContNo = _field
	return nil
}
func (p *UnionContCreateRsp) ReadField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[core.SignPosition]string, size)
	for i := 0; i < size; i++ {
		var _key core.SignPosition
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_key = core.SignPosition(v)
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.SignLinks = _field
	return nil
}

func (p *UnionContCreateRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UnionContCreateRsp")

	var fieldId int16
	if err = oprot.WriteStructBegin("UnionContCreateRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UnionContCreateRsp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ContNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UnionContCreateRsp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sign_links", thrift.MAP, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.I32, thrift.STRING, len(p.SignLinks)); err != nil {
		return err
	}
	for k, v := range p.SignLinks {
		if err := oprot.WriteI32(int32(k)); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UnionContCreateRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UnionContCreateRsp(%+v)", *p)

}

type ContSignReq struct {
	ContType       int32                                 `thrift:"cont_type,1" frugal:"1,default,i32" json:"cont_type"`
	ContTmplID     int64                                 `thrift:"cont_tmpl_id,2" frugal:"2,default,i64" json:"cont_tmpl_id"`
	ContTmplParams map[string]string                     `thrift:"cont_tmpl_params,3" frugal:"3,default,map<string:string>" json:"cont_tmpl_params"`
	SignPartyList  []*core.SignPartyData                 `thrift:"sign_party_list,4" frugal:"4,default,list<core.SignPartyData>" json:"sign_party_list"`
	RedirectURL    *string                               `thrift:"redirect_url,5,optional" frugal:"5,optional,string" json:"redirect_url,omitempty"`
	SmsConfigMap   map[core.SignPosition]*core.SmsConfig `thrift:"sms_config_map,6" frugal:"6,default,map<SignPosition:core.SmsConfig>" json:"sms_config_map"`
	CallbackAction string                                `thrift:"callback_action,100" frugal:"100,default,string" json:"callback_action"`
	Tag            map[string]string                     `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewContSignReq() *ContSignReq {
	return &ContSignReq{}
}

func (p *ContSignReq) InitDefault() {
}

func (p *ContSignReq) GetContType() (v int32) {
	return p.ContType
}

func (p *ContSignReq) GetContTmplID() (v int64) {
	return p.ContTmplID
}

func (p *ContSignReq) GetContTmplParams() (v map[string]string) {
	return p.ContTmplParams
}

func (p *ContSignReq) GetSignPartyList() (v []*core.SignPartyData) {
	return p.SignPartyList
}

var ContSignReq_RedirectURL_DEFAULT string

func (p *ContSignReq) GetRedirectURL() (v string) {
	if !p.IsSetRedirectURL() {
		return ContSignReq_RedirectURL_DEFAULT
	}
	return *p.RedirectURL
}

func (p *ContSignReq) GetSmsConfigMap() (v map[core.SignPosition]*core.SmsConfig) {
	return p.SmsConfigMap
}

func (p *ContSignReq) GetCallbackAction() (v string) {
	return p.CallbackAction
}

func (p *ContSignReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *ContSignReq) SetContType(val int32) {
	p.ContType = val
}
func (p *ContSignReq) SetContTmplID(val int64) {
	p.ContTmplID = val
}
func (p *ContSignReq) SetContTmplParams(val map[string]string) {
	p.ContTmplParams = val
}
func (p *ContSignReq) SetSignPartyList(val []*core.SignPartyData) {
	p.SignPartyList = val
}
func (p *ContSignReq) SetRedirectURL(val *string) {
	p.RedirectURL = val
}
func (p *ContSignReq) SetSmsConfigMap(val map[core.SignPosition]*core.SmsConfig) {
	p.SmsConfigMap = val
}
func (p *ContSignReq) SetCallbackAction(val string) {
	p.CallbackAction = val
}
func (p *ContSignReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_ContSignReq = map[int16]string{
	1:   "cont_type",
	2:   "cont_tmpl_id",
	3:   "cont_tmpl_params",
	4:   "sign_party_list",
	5:   "redirect_url",
	6:   "sms_config_map",
	100: "callback_action",
	200: "tag",
}

func (p *ContSignReq) IsSetRedirectURL() bool {
	return p.RedirectURL != nil
}

func (p *ContSignReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ContSignReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ContSignReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ContSignReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContType = _field
	return nil
}
func (p *ContSignReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContTmplID = _field
	return nil
}
func (p *ContSignReq) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ContTmplParams = _field
	return nil
}
func (p *ContSignReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*core.SignPartyData, 0, size)
	values := make([]core.SignPartyData, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SignPartyList = _field
	return nil
}
func (p *ContSignReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RedirectURL = _field
	return nil
}
func (p *ContSignReq) ReadField6(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[core.SignPosition]*core.SmsConfig, size)
	values := make([]core.SmsConfig, size)
	for i := 0; i < size; i++ {
		var _key core.SignPosition
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_key = core.SignPosition(v)
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.SmsConfigMap = _field
	return nil
}
func (p *ContSignReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackAction = _field
	return nil
}
func (p *ContSignReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *ContSignReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ContSignReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ContSignReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ContSignReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ContType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ContSignReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_tmpl_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ContTmplID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ContSignReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_tmpl_params", thrift.MAP, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ContTmplParams)); err != nil {
		return err
	}
	for k, v := range p.ContTmplParams {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ContSignReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sign_party_list", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SignPartyList)); err != nil {
		return err
	}
	for _, v := range p.SignPartyList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ContSignReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRedirectURL() {
		if err = oprot.WriteFieldBegin("redirect_url", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RedirectURL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ContSignReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sms_config_map", thrift.MAP, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.SmsConfigMap)); err != nil {
		return err
	}
	for k, v := range p.SmsConfigMap {
		if err := oprot.WriteI32(int32(k)); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ContSignReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_action", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *ContSignReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *ContSignReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ContSignReq(%+v)", *p)

}

type ContSignRsp struct {
	ContSerial     string                       `thrift:"cont_serial,1" frugal:"1,default,string" json:"cont_serial"`
	SignLink       string                       `thrift:"sign_link,2" frugal:"2,default,string" json:"sign_link"`
	SignPosLinkMap map[core.SignPosition]string `thrift:"sign_pos_link_map,3" frugal:"3,default,map<SignPosition:string>" json:"sign_pos_link_map"`
}

func NewContSignRsp() *ContSignRsp {
	return &ContSignRsp{}
}

func (p *ContSignRsp) InitDefault() {
}

func (p *ContSignRsp) GetContSerial() (v string) {
	return p.ContSerial
}

func (p *ContSignRsp) GetSignLink() (v string) {
	return p.SignLink
}

func (p *ContSignRsp) GetSignPosLinkMap() (v map[core.SignPosition]string) {
	return p.SignPosLinkMap
}
func (p *ContSignRsp) SetContSerial(val string) {
	p.ContSerial = val
}
func (p *ContSignRsp) SetSignLink(val string) {
	p.SignLink = val
}
func (p *ContSignRsp) SetSignPosLinkMap(val map[core.SignPosition]string) {
	p.SignPosLinkMap = val
}

var fieldIDToName_ContSignRsp = map[int16]string{
	1: "cont_serial",
	2: "sign_link",
	3: "sign_pos_link_map",
}

func (p *ContSignRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ContSignRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ContSignRsp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ContSignRsp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContSerial = _field
	return nil
}
func (p *ContSignRsp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SignLink = _field
	return nil
}
func (p *ContSignRsp) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[core.SignPosition]string, size)
	for i := 0; i < size; i++ {
		var _key core.SignPosition
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_key = core.SignPosition(v)
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.SignPosLinkMap = _field
	return nil
}

func (p *ContSignRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ContSignRsp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ContSignRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ContSignRsp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_serial", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ContSerial); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ContSignRsp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sign_link", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SignLink); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ContSignRsp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sign_pos_link_map", thrift.MAP, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.I32, thrift.STRING, len(p.SignPosLinkMap)); err != nil {
		return err
	}
	for k, v := range p.SignPosLinkMap {
		if err := oprot.WriteI32(int32(k)); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ContSignRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ContSignRsp(%+v)", *p)

}

type ContTerminationReq struct {
	ContType int32             `thrift:"cont_type,1" frugal:"1,default,i32" json:"cont_type"`
	SignType *int32            `thrift:"sign_type,2,optional" frugal:"2,optional,i32" json:"sign_type,omitempty"`
	Tag      map[string]string `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewContTerminationReq() *ContTerminationReq {
	return &ContTerminationReq{}
}

func (p *ContTerminationReq) InitDefault() {
}

func (p *ContTerminationReq) GetContType() (v int32) {
	return p.ContType
}

var ContTerminationReq_SignType_DEFAULT int32

func (p *ContTerminationReq) GetSignType() (v int32) {
	if !p.IsSetSignType() {
		return ContTerminationReq_SignType_DEFAULT
	}
	return *p.SignType
}

func (p *ContTerminationReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *ContTerminationReq) SetContType(val int32) {
	p.ContType = val
}
func (p *ContTerminationReq) SetSignType(val *int32) {
	p.SignType = val
}
func (p *ContTerminationReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_ContTerminationReq = map[int16]string{
	1:   "cont_type",
	2:   "sign_type",
	200: "tag",
}

func (p *ContTerminationReq) IsSetSignType() bool {
	return p.SignType != nil
}

func (p *ContTerminationReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ContTerminationReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ContTerminationReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ContTerminationReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContType = _field
	return nil
}
func (p *ContTerminationReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SignType = _field
	return nil
}
func (p *ContTerminationReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *ContTerminationReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ContTerminationReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ContTerminationReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ContTerminationReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ContType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ContTerminationReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSignType() {
		if err = oprot.WriteFieldBegin("sign_type", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.SignType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ContTerminationReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *ContTerminationReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ContTerminationReq(%+v)", *p)

}

type ContTerminationRsp struct {
}

func NewContTerminationRsp() *ContTerminationRsp {
	return &ContTerminationRsp{}
}

func (p *ContTerminationRsp) InitDefault() {
}

var fieldIDToName_ContTerminationRsp = map[int16]string{}

func (p *ContTerminationRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ContTerminationRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ContTerminationRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ContTerminationRsp")

	if err = oprot.WriteStructBegin("ContTerminationRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ContTerminationRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ContTerminationRsp(%+v)", *p)

}

type CloseContStatusReq struct {
	ContType int32             `thrift:"cont_type,1" frugal:"1,default,i32" json:"cont_type"`
	Tag      map[string]string `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewCloseContStatusReq() *CloseContStatusReq {
	return &CloseContStatusReq{}
}

func (p *CloseContStatusReq) InitDefault() {
}

func (p *CloseContStatusReq) GetContType() (v int32) {
	return p.ContType
}

func (p *CloseContStatusReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *CloseContStatusReq) SetContType(val int32) {
	p.ContType = val
}
func (p *CloseContStatusReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_CloseContStatusReq = map[int16]string{
	1:   "cont_type",
	200: "tag",
}

func (p *CloseContStatusReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CloseContStatusReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CloseContStatusReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CloseContStatusReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContType = _field
	return nil
}
func (p *CloseContStatusReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *CloseContStatusReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CloseContStatusReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CloseContStatusReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CloseContStatusReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cont_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ContType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CloseContStatusReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *CloseContStatusReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CloseContStatusReq(%+v)", *p)

}

type CloseContStatusRsp struct {
}

func NewCloseContStatusRsp() *CloseContStatusRsp {
	return &CloseContStatusRsp{}
}

func (p *CloseContStatusRsp) InitDefault() {
}

var fieldIDToName_CloseContStatusRsp = map[int16]string{}

func (p *CloseContStatusRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CloseContStatusRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CloseContStatusRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CloseContStatusRsp")

	if err = oprot.WriteStructBegin("CloseContStatusRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CloseContStatusRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CloseContStatusRsp(%+v)", *p)

}

type UnionPayReq struct {
	FinanceOrderType      int32                                                   `thrift:"finance_order_type,3,required" frugal:"3,required,i32" json:"finance_order_type"`
	UserID                *string                                                 `thrift:"user_id,4,optional" frugal:"4,optional,string" json:"user_id,omitempty"`
	Currency              *payment.CurrencyType                                   `thrift:"currency,5,optional" frugal:"5,optional,CurrencyType" json:"currency,omitempty"`
	TotalAmount           int64                                                   `thrift:"total_amount,6,required" frugal:"6,required,i64" json:"total_amount"`
	EstimateSubsidyAmount *int64                                                  `thrift:"estimate_subsidy_amount,7,optional" frugal:"7,optional,i64" json:"estimate_subsidy_amount,omitempty"`
	PayTypeList           []fwe_trade_common.UnionPayType                         `thrift:"pay_type_list,10,required" frugal:"10,required,list<UnionPayType>" json:"pay_type_list"`
	MerchantInfoMap       map[fwe_trade_common.UnionPayType]*payment.MerchantInfo `thrift:"merchant_info_map,11,required" frugal:"11,required,map<UnionPayType:payment.MerchantInfo>" json:"merchant_info_map"`
	PosDeviceList         []*fwe_trade_common.POSDeviceInfo                       `thrift:"pos_device_list,12,optional" frugal:"12,optional,list<fwe_trade_common.POSDeviceInfo>" json:"pos_device_list,omitempty"`
	PayLimitList          []fwe_trade_common.PayLimitType                         `thrift:"pay_limit_list,13,optional" frugal:"13,optional,list<PayLimitType>" json:"pay_limit_list,omitempty"`
	CashierDeskType       *fwe_trade_common.CashierDeskType                       `thrift:"cashier_desk_type,14,optional" frugal:"14,optional,CashierDeskType" json:"cashier_desk_type,omitempty"`
	OsType                *fwe_trade_common.OSType                                `thrift:"os_type,15,optional" frugal:"15,optional,OSType" json:"os_type,omitempty"`
	ExpireTime            *int64                                                  `thrift:"expire_time,16,optional" frugal:"16,optional,i64" json:"expire_time,omitempty"`
	RedirectURL           *string                                                 `thrift:"redirect_url,17,optional" frugal:"17,optional,string" json:"redirect_url,omitempty"`
	CheckData             *fwe_trade_common.PayOfflineCheckData                   `thrift:"check_data,18,optional" frugal:"18,optional,fwe_trade_common.PayOfflineCheckData" json:"check_data,omitempty"`
	JstPayParam           *fwe_trade_common.JstPayParam                           `thrift:"jst_pay_param,19,optional" frugal:"19,optional,fwe_trade_common.JstPayParam" json:"jst_pay_param,omitempty"`
	FweAccountID          string                                                  `thrift:"fwe_account_id,20,required" frugal:"20,required,string" json:"fwe_account_id"`
	OneStepPay            bool                                                    `thrift:"one_step_pay,21" frugal:"21,default,bool" json:"one_step_pay"`
	PayUnionParam         map[string]string                                       `thrift:"pay_union_param,22,optional" frugal:"22,optional,map<string:string>" json:"pay_union_param,omitempty"`
	FinanceAccountID      string                                                  `thrift:"finance_account_id,23" frugal:"23,default,string" json:"finance_account_id"`
	RiskInfo              *fwe_trade_common.UnionPayRiskInfo                      `thrift:"risk_info,40" frugal:"40,default,fwe_trade_common.UnionPayRiskInfo" json:"risk_info"`
	Extra                 *string                                                 `thrift:"extra,50,optional" frugal:"50,optional,string" json:"extra,omitempty"`
	SecondPayParam        *fwe_trade_common.SecondPayParam                        `thrift:"second_pay_param,51,optional" frugal:"51,optional,fwe_trade_common.SecondPayParam" json:"second_pay_param,omitempty"`
	CallbackEvent         string                                                  `thrift:"callback_event,100" frugal:"100,default,string" json:"callback_event"`
	CallbackExtra         string                                                  `thrift:"callback_extra,101" frugal:"101,default,string" json:"callback_extra"`
	CloseCallbackEvent    string                                                  `thrift:"close_callback_event,102" frugal:"102,default,string" json:"close_callback_event"`
	Tag                   map[string]string                                       `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewUnionPayReq() *UnionPayReq {
	return &UnionPayReq{}
}

func (p *UnionPayReq) InitDefault() {
}

func (p *UnionPayReq) GetFinanceOrderType() (v int32) {
	return p.FinanceOrderType
}

var UnionPayReq_UserID_DEFAULT string

func (p *UnionPayReq) GetUserID() (v string) {
	if !p.IsSetUserID() {
		return UnionPayReq_UserID_DEFAULT
	}
	return *p.UserID
}

var UnionPayReq_Currency_DEFAULT payment.CurrencyType

func (p *UnionPayReq) GetCurrency() (v payment.CurrencyType) {
	if !p.IsSetCurrency() {
		return UnionPayReq_Currency_DEFAULT
	}
	return *p.Currency
}

func (p *UnionPayReq) GetTotalAmount() (v int64) {
	return p.TotalAmount
}

var UnionPayReq_EstimateSubsidyAmount_DEFAULT int64

func (p *UnionPayReq) GetEstimateSubsidyAmount() (v int64) {
	if !p.IsSetEstimateSubsidyAmount() {
		return UnionPayReq_EstimateSubsidyAmount_DEFAULT
	}
	return *p.EstimateSubsidyAmount
}

func (p *UnionPayReq) GetPayTypeList() (v []fwe_trade_common.UnionPayType) {
	return p.PayTypeList
}

func (p *UnionPayReq) GetMerchantInfoMap() (v map[fwe_trade_common.UnionPayType]*payment.MerchantInfo) {
	return p.MerchantInfoMap
}

var UnionPayReq_PosDeviceList_DEFAULT []*fwe_trade_common.POSDeviceInfo

func (p *UnionPayReq) GetPosDeviceList() (v []*fwe_trade_common.POSDeviceInfo) {
	if !p.IsSetPosDeviceList() {
		return UnionPayReq_PosDeviceList_DEFAULT
	}
	return p.PosDeviceList
}

var UnionPayReq_PayLimitList_DEFAULT []fwe_trade_common.PayLimitType

func (p *UnionPayReq) GetPayLimitList() (v []fwe_trade_common.PayLimitType) {
	if !p.IsSetPayLimitList() {
		return UnionPayReq_PayLimitList_DEFAULT
	}
	return p.PayLimitList
}

var UnionPayReq_CashierDeskType_DEFAULT fwe_trade_common.CashierDeskType

func (p *UnionPayReq) GetCashierDeskType() (v fwe_trade_common.CashierDeskType) {
	if !p.IsSetCashierDeskType() {
		return UnionPayReq_CashierDeskType_DEFAULT
	}
	return *p.CashierDeskType
}

var UnionPayReq_OsType_DEFAULT fwe_trade_common.OSType

func (p *UnionPayReq) GetOsType() (v fwe_trade_common.OSType) {
	if !p.IsSetOsType() {
		return UnionPayReq_OsType_DEFAULT
	}
	return *p.OsType
}

var UnionPayReq_ExpireTime_DEFAULT int64

func (p *UnionPayReq) GetExpireTime() (v int64) {
	if !p.IsSetExpireTime() {
		return UnionPayReq_ExpireTime_DEFAULT
	}
	return *p.ExpireTime
}

var UnionPayReq_RedirectURL_DEFAULT string

func (p *UnionPayReq) GetRedirectURL() (v string) {
	if !p.IsSetRedirectURL() {
		return UnionPayReq_RedirectURL_DEFAULT
	}
	return *p.RedirectURL
}

var UnionPayReq_CheckData_DEFAULT *fwe_trade_common.PayOfflineCheckData

func (p *UnionPayReq) GetCheckData() (v *fwe_trade_common.PayOfflineCheckData) {
	if !p.IsSetCheckData() {
		return UnionPayReq_CheckData_DEFAULT
	}
	return p.CheckData
}

var UnionPayReq_JstPayParam_DEFAULT *fwe_trade_common.JstPayParam

func (p *UnionPayReq) GetJstPayParam() (v *fwe_trade_common.JstPayParam) {
	if !p.IsSetJstPayParam() {
		return UnionPayReq_JstPayParam_DEFAULT
	}
	return p.JstPayParam
}

func (p *UnionPayReq) GetFweAccountID() (v string) {
	return p.FweAccountID
}

func (p *UnionPayReq) GetOneStepPay() (v bool) {
	return p.OneStepPay
}

var UnionPayReq_PayUnionParam_DEFAULT map[string]string

func (p *UnionPayReq) GetPayUnionParam() (v map[string]string) {
	if !p.IsSetPayUnionParam() {
		return UnionPayReq_PayUnionParam_DEFAULT
	}
	return p.PayUnionParam
}

func (p *UnionPayReq) GetFinanceAccountID() (v string) {
	return p.FinanceAccountID
}

var UnionPayReq_RiskInfo_DEFAULT *fwe_trade_common.UnionPayRiskInfo

func (p *UnionPayReq) GetRiskInfo() (v *fwe_trade_common.UnionPayRiskInfo) {
	if !p.IsSetRiskInfo() {
		return UnionPayReq_RiskInfo_DEFAULT
	}
	return p.RiskInfo
}

var UnionPayReq_Extra_DEFAULT string

func (p *UnionPayReq) GetExtra() (v string) {
	if !p.IsSetExtra() {
		return UnionPayReq_Extra_DEFAULT
	}
	return *p.Extra
}

var UnionPayReq_SecondPayParam_DEFAULT *fwe_trade_common.SecondPayParam

func (p *UnionPayReq) GetSecondPayParam() (v *fwe_trade_common.SecondPayParam) {
	if !p.IsSetSecondPayParam() {
		return UnionPayReq_SecondPayParam_DEFAULT
	}
	return p.SecondPayParam
}

func (p *UnionPayReq) GetCallbackEvent() (v string) {
	return p.CallbackEvent
}

func (p *UnionPayReq) GetCallbackExtra() (v string) {
	return p.CallbackExtra
}

func (p *UnionPayReq) GetCloseCallbackEvent() (v string) {
	return p.CloseCallbackEvent
}

func (p *UnionPayReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *UnionPayReq) SetFinanceOrderType(val int32) {
	p.FinanceOrderType = val
}
func (p *UnionPayReq) SetUserID(val *string) {
	p.UserID = val
}
func (p *UnionPayReq) SetCurrency(val *payment.CurrencyType) {
	p.Currency = val
}
func (p *UnionPayReq) SetTotalAmount(val int64) {
	p.TotalAmount = val
}
func (p *UnionPayReq) SetEstimateSubsidyAmount(val *int64) {
	p.EstimateSubsidyAmount = val
}
func (p *UnionPayReq) SetPayTypeList(val []fwe_trade_common.UnionPayType) {
	p.PayTypeList = val
}
func (p *UnionPayReq) SetMerchantInfoMap(val map[fwe_trade_common.UnionPayType]*payment.MerchantInfo) {
	p.MerchantInfoMap = val
}
func (p *UnionPayReq) SetPosDeviceList(val []*fwe_trade_common.POSDeviceInfo) {
	p.PosDeviceList = val
}
func (p *UnionPayReq) SetPayLimitList(val []fwe_trade_common.PayLimitType) {
	p.PayLimitList = val
}
func (p *UnionPayReq) SetCashierDeskType(val *fwe_trade_common.CashierDeskType) {
	p.CashierDeskType = val
}
func (p *UnionPayReq) SetOsType(val *fwe_trade_common.OSType) {
	p.OsType = val
}
func (p *UnionPayReq) SetExpireTime(val *int64) {
	p.ExpireTime = val
}
func (p *UnionPayReq) SetRedirectURL(val *string) {
	p.RedirectURL = val
}
func (p *UnionPayReq) SetCheckData(val *fwe_trade_common.PayOfflineCheckData) {
	p.CheckData = val
}
func (p *UnionPayReq) SetJstPayParam(val *fwe_trade_common.JstPayParam) {
	p.JstPayParam = val
}
func (p *UnionPayReq) SetFweAccountID(val string) {
	p.FweAccountID = val
}
func (p *UnionPayReq) SetOneStepPay(val bool) {
	p.OneStepPay = val
}
func (p *UnionPayReq) SetPayUnionParam(val map[string]string) {
	p.PayUnionParam = val
}
func (p *UnionPayReq) SetFinanceAccountID(val string) {
	p.FinanceAccountID = val
}
func (p *UnionPayReq) SetRiskInfo(val *fwe_trade_common.UnionPayRiskInfo) {
	p.RiskInfo = val
}
func (p *UnionPayReq) SetExtra(val *string) {
	p.Extra = val
}
func (p *UnionPayReq) SetSecondPayParam(val *fwe_trade_common.SecondPayParam) {
	p.SecondPayParam = val
}
func (p *UnionPayReq) SetCallbackEvent(val string) {
	p.CallbackEvent = val
}
func (p *UnionPayReq) SetCallbackExtra(val string) {
	p.CallbackExtra = val
}
func (p *UnionPayReq) SetCloseCallbackEvent(val string) {
	p.CloseCallbackEvent = val
}
func (p *UnionPayReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_UnionPayReq = map[int16]string{
	3:   "finance_order_type",
	4:   "user_id",
	5:   "currency",
	6:   "total_amount",
	7:   "estimate_subsidy_amount",
	10:  "pay_type_list",
	11:  "merchant_info_map",
	12:  "pos_device_list",
	13:  "pay_limit_list",
	14:  "cashier_desk_type",
	15:  "os_type",
	16:  "expire_time",
	17:  "redirect_url",
	18:  "check_data",
	19:  "jst_pay_param",
	20:  "fwe_account_id",
	21:  "one_step_pay",
	22:  "pay_union_param",
	23:  "finance_account_id",
	40:  "risk_info",
	50:  "extra",
	51:  "second_pay_param",
	100: "callback_event",
	101: "callback_extra",
	102: "close_callback_event",
	200: "tag",
}

func (p *UnionPayReq) IsSetUserID() bool {
	return p.UserID != nil
}

func (p *UnionPayReq) IsSetCurrency() bool {
	return p.Currency != nil
}

func (p *UnionPayReq) IsSetEstimateSubsidyAmount() bool {
	return p.EstimateSubsidyAmount != nil
}

func (p *UnionPayReq) IsSetPosDeviceList() bool {
	return p.PosDeviceList != nil
}

func (p *UnionPayReq) IsSetPayLimitList() bool {
	return p.PayLimitList != nil
}

func (p *UnionPayReq) IsSetCashierDeskType() bool {
	return p.CashierDeskType != nil
}

func (p *UnionPayReq) IsSetOsType() bool {
	return p.OsType != nil
}

func (p *UnionPayReq) IsSetExpireTime() bool {
	return p.ExpireTime != nil
}

func (p *UnionPayReq) IsSetRedirectURL() bool {
	return p.RedirectURL != nil
}

func (p *UnionPayReq) IsSetCheckData() bool {
	return p.CheckData != nil
}

func (p *UnionPayReq) IsSetJstPayParam() bool {
	return p.JstPayParam != nil
}

func (p *UnionPayReq) IsSetPayUnionParam() bool {
	return p.PayUnionParam != nil
}

func (p *UnionPayReq) IsSetRiskInfo() bool {
	return p.RiskInfo != nil
}

func (p *UnionPayReq) IsSetExtra() bool {
	return p.Extra != nil
}

func (p *UnionPayReq) IsSetSecondPayParam() bool {
	return p.SecondPayParam != nil
}

func (p *UnionPayReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UnionPayReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFinanceOrderType bool = false
	var issetTotalAmount bool = false
	var issetPayTypeList bool = false
	var issetMerchantInfoMap bool = false
	var issetFweAccountID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetFinanceOrderType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotalAmount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetPayTypeList = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetMerchantInfoMap = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
				issetFweAccountID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 40:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField40(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField50(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 51:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField51(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 102:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField102(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFinanceOrderType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTotalAmount {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetPayTypeList {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetMerchantInfoMap {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetFweAccountID {
		fieldId = 20
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UnionPayReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UnionPayReq[fieldId]))
}

func (p *UnionPayReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceOrderType = _field
	return nil
}
func (p *UnionPayReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserID = _field
	return nil
}
func (p *UnionPayReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *payment.CurrencyType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := payment.CurrencyType(v)
		_field = &tmp
	}
	p.Currency = _field
	return nil
}
func (p *UnionPayReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalAmount = _field
	return nil
}
func (p *UnionPayReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EstimateSubsidyAmount = _field
	return nil
}
func (p *UnionPayReq) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]fwe_trade_common.UnionPayType, 0, size)
	for i := 0; i < size; i++ {

		var _elem fwe_trade_common.UnionPayType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = fwe_trade_common.UnionPayType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PayTypeList = _field
	return nil
}
func (p *UnionPayReq) ReadField11(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[fwe_trade_common.UnionPayType]*payment.MerchantInfo, size)
	values := make([]payment.MerchantInfo, size)
	for i := 0; i < size; i++ {
		var _key fwe_trade_common.UnionPayType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_key = fwe_trade_common.UnionPayType(v)
		}

		_val := &values[i]
		_val.InitDefault()
		if err := _val.Read(iprot); err != nil {
			return err
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.MerchantInfoMap = _field
	return nil
}
func (p *UnionPayReq) ReadField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.POSDeviceInfo, 0, size)
	values := make([]fwe_trade_common.POSDeviceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PosDeviceList = _field
	return nil
}
func (p *UnionPayReq) ReadField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]fwe_trade_common.PayLimitType, 0, size)
	for i := 0; i < size; i++ {

		var _elem fwe_trade_common.PayLimitType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = fwe_trade_common.PayLimitType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PayLimitList = _field
	return nil
}
func (p *UnionPayReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *fwe_trade_common.CashierDeskType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := fwe_trade_common.CashierDeskType(v)
		_field = &tmp
	}
	p.CashierDeskType = _field
	return nil
}
func (p *UnionPayReq) ReadField15(iprot thrift.TProtocol) error {

	var _field *fwe_trade_common.OSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := fwe_trade_common.OSType(v)
		_field = &tmp
	}
	p.OsType = _field
	return nil
}
func (p *UnionPayReq) ReadField16(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExpireTime = _field
	return nil
}
func (p *UnionPayReq) ReadField17(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RedirectURL = _field
	return nil
}
func (p *UnionPayReq) ReadField18(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewPayOfflineCheckData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CheckData = _field
	return nil
}
func (p *UnionPayReq) ReadField19(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewJstPayParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.JstPayParam = _field
	return nil
}
func (p *UnionPayReq) ReadField20(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FweAccountID = _field
	return nil
}
func (p *UnionPayReq) ReadField21(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OneStepPay = _field
	return nil
}
func (p *UnionPayReq) ReadField22(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.PayUnionParam = _field
	return nil
}
func (p *UnionPayReq) ReadField23(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceAccountID = _field
	return nil
}
func (p *UnionPayReq) ReadField40(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewUnionPayRiskInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RiskInfo = _field
	return nil
}
func (p *UnionPayReq) ReadField50(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Extra = _field
	return nil
}
func (p *UnionPayReq) ReadField51(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewSecondPayParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SecondPayParam = _field
	return nil
}
func (p *UnionPayReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackEvent = _field
	return nil
}
func (p *UnionPayReq) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackExtra = _field
	return nil
}
func (p *UnionPayReq) ReadField102(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CloseCallbackEvent = _field
	return nil
}
func (p *UnionPayReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *UnionPayReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UnionPayReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UnionPayReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
		if err = p.writeField40(oprot); err != nil {
			fieldId = 40
			goto WriteFieldError
		}
		if err = p.writeField50(oprot); err != nil {
			fieldId = 50
			goto WriteFieldError
		}
		if err = p.writeField51(oprot); err != nil {
			fieldId = 51
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField102(oprot); err != nil {
			fieldId = 102
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UnionPayReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_order_type", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FinanceOrderType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UnionPayReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserID() {
		if err = oprot.WriteFieldBegin("user_id", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UnionPayReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCurrency() {
		if err = oprot.WriteFieldBegin("currency", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Currency)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *UnionPayReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total_amount", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TotalAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *UnionPayReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetEstimateSubsidyAmount() {
		if err = oprot.WriteFieldBegin("estimate_subsidy_amount", thrift.I64, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.EstimateSubsidyAmount); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *UnionPayReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_type_list", thrift.LIST, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.I32, len(p.PayTypeList)); err != nil {
		return err
	}
	for _, v := range p.PayTypeList {
		if err := oprot.WriteI32(int32(v)); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *UnionPayReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merchant_info_map", thrift.MAP, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.MerchantInfoMap)); err != nil {
		return err
	}
	for k, v := range p.MerchantInfoMap {
		if err := oprot.WriteI32(int32(k)); err != nil {
			return err
		}
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *UnionPayReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetPosDeviceList() {
		if err = oprot.WriteFieldBegin("pos_device_list", thrift.LIST, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PosDeviceList)); err != nil {
			return err
		}
		for _, v := range p.PosDeviceList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *UnionPayReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetPayLimitList() {
		if err = oprot.WriteFieldBegin("pay_limit_list", thrift.LIST, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PayLimitList)); err != nil {
			return err
		}
		for _, v := range p.PayLimitList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *UnionPayReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetCashierDeskType() {
		if err = oprot.WriteFieldBegin("cashier_desk_type", thrift.I32, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.CashierDeskType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *UnionPayReq) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetOsType() {
		if err = oprot.WriteFieldBegin("os_type", thrift.I32, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OsType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *UnionPayReq) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetExpireTime() {
		if err = oprot.WriteFieldBegin("expire_time", thrift.I64, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ExpireTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *UnionPayReq) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetRedirectURL() {
		if err = oprot.WriteFieldBegin("redirect_url", thrift.STRING, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RedirectURL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}
func (p *UnionPayReq) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetCheckData() {
		if err = oprot.WriteFieldBegin("check_data", thrift.STRUCT, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CheckData.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}
func (p *UnionPayReq) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetJstPayParam() {
		if err = oprot.WriteFieldBegin("jst_pay_param", thrift.STRUCT, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.JstPayParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}
func (p *UnionPayReq) writeField20(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fwe_account_id", thrift.STRING, 20); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FweAccountID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}
func (p *UnionPayReq) writeField21(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("one_step_pay", thrift.BOOL, 21); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.OneStepPay); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}
func (p *UnionPayReq) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetPayUnionParam() {
		if err = oprot.WriteFieldBegin("pay_union_param", thrift.MAP, 22); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.PayUnionParam)); err != nil {
			return err
		}
		for k, v := range p.PayUnionParam {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}
func (p *UnionPayReq) writeField23(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_account_id", thrift.STRING, 23); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FinanceAccountID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}
func (p *UnionPayReq) writeField40(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("risk_info", thrift.STRUCT, 40); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.RiskInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 40 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 40 end error: ", p), err)
}
func (p *UnionPayReq) writeField50(oprot thrift.TProtocol) (err error) {
	if p.IsSetExtra() {
		if err = oprot.WriteFieldBegin("extra", thrift.STRING, 50); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Extra); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 end error: ", p), err)
}
func (p *UnionPayReq) writeField51(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecondPayParam() {
		if err = oprot.WriteFieldBegin("second_pay_param", thrift.STRUCT, 51); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SecondPayParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 51 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 51 end error: ", p), err)
}
func (p *UnionPayReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_event", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackEvent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *UnionPayReq) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_extra", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackExtra); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *UnionPayReq) writeField102(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("close_callback_event", thrift.STRING, 102); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CloseCallbackEvent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 end error: ", p), err)
}
func (p *UnionPayReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *UnionPayReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UnionPayReq(%+v)", *p)

}

type UnionPayResp struct {
	PayUnionNo   string `thrift:"pay_union_no,1" frugal:"1,default,string" json:"pay_union_no"`
	PayUnionLink string `thrift:"pay_union_link,2" frugal:"2,default,string" json:"pay_union_link"`
	PayData      string `thrift:"pay_data,3" frugal:"3,default,string" json:"pay_data"`
}

func NewUnionPayResp() *UnionPayResp {
	return &UnionPayResp{}
}

func (p *UnionPayResp) InitDefault() {
}

func (p *UnionPayResp) GetPayUnionNo() (v string) {
	return p.PayUnionNo
}

func (p *UnionPayResp) GetPayUnionLink() (v string) {
	return p.PayUnionLink
}

func (p *UnionPayResp) GetPayData() (v string) {
	return p.PayData
}
func (p *UnionPayResp) SetPayUnionNo(val string) {
	p.PayUnionNo = val
}
func (p *UnionPayResp) SetPayUnionLink(val string) {
	p.PayUnionLink = val
}
func (p *UnionPayResp) SetPayData(val string) {
	p.PayData = val
}

var fieldIDToName_UnionPayResp = map[int16]string{
	1: "pay_union_no",
	2: "pay_union_link",
	3: "pay_data",
}

func (p *UnionPayResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UnionPayResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UnionPayResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UnionPayResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayUnionNo = _field
	return nil
}
func (p *UnionPayResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayUnionLink = _field
	return nil
}
func (p *UnionPayResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayData = _field
	return nil
}

func (p *UnionPayResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("UnionPayResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("UnionPayResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UnionPayResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_union_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PayUnionNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UnionPayResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_union_link", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PayUnionLink); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UnionPayResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_data", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PayData); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UnionPayResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UnionPayResp(%+v)", *p)

}

type GuaranteePayReq struct {
	MerchantID       string                           `thrift:"merchant_id,1" frugal:"1,default,string" json:"merchant_id"`
	AppID            string                           `thrift:"app_id,2" frugal:"2,default,string" json:"app_id"`
	UID              string                           `thrift:"uid,3" frugal:"3,default,string" json:"uid"`
	FinanceOrderType int32                            `thrift:"finance_order_type,10" frugal:"10,default,i32" json:"finance_order_type"`
	Amount           int64                            `thrift:"amount,11" frugal:"11,default,i64" json:"amount"`
	CashierDeskType  fwe_trade_common.CashierDeskType `thrift:"cashier_desk_type,12" frugal:"12,default,CashierDeskType" json:"cashier_desk_type"`
	ExpireTime       *int64                           `thrift:"expire_time,13,optional" frugal:"13,optional,i64" json:"expire_time,omitempty"`
	RedirectURL      *string                          `thrift:"redirect_url,14,optional" frugal:"14,optional,string" json:"redirect_url,omitempty"`
	IPAddress        string                           `thrift:"ip_address,15" frugal:"15,default,string" json:"ip_address"`
	PayLimitList     []fwe_trade_common.PayLimitType  `thrift:"pay_limit_list,16" frugal:"16,default,list<PayLimitType>" json:"pay_limit_list"`
	CallbackAction   string                           `thrift:"callback_action,100" frugal:"100,default,string" json:"callback_action"`
	TimeoutAction    string                           `thrift:"timeout_action,101" frugal:"101,default,string" json:"timeout_action"`
	Tag              map[string]string                `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewGuaranteePayReq() *GuaranteePayReq {
	return &GuaranteePayReq{}
}

func (p *GuaranteePayReq) InitDefault() {
}

func (p *GuaranteePayReq) GetMerchantID() (v string) {
	return p.MerchantID
}

func (p *GuaranteePayReq) GetAppID() (v string) {
	return p.AppID
}

func (p *GuaranteePayReq) GetUID() (v string) {
	return p.UID
}

func (p *GuaranteePayReq) GetFinanceOrderType() (v int32) {
	return p.FinanceOrderType
}

func (p *GuaranteePayReq) GetAmount() (v int64) {
	return p.Amount
}

func (p *GuaranteePayReq) GetCashierDeskType() (v fwe_trade_common.CashierDeskType) {
	return p.CashierDeskType
}

var GuaranteePayReq_ExpireTime_DEFAULT int64

func (p *GuaranteePayReq) GetExpireTime() (v int64) {
	if !p.IsSetExpireTime() {
		return GuaranteePayReq_ExpireTime_DEFAULT
	}
	return *p.ExpireTime
}

var GuaranteePayReq_RedirectURL_DEFAULT string

func (p *GuaranteePayReq) GetRedirectURL() (v string) {
	if !p.IsSetRedirectURL() {
		return GuaranteePayReq_RedirectURL_DEFAULT
	}
	return *p.RedirectURL
}

func (p *GuaranteePayReq) GetIPAddress() (v string) {
	return p.IPAddress
}

func (p *GuaranteePayReq) GetPayLimitList() (v []fwe_trade_common.PayLimitType) {
	return p.PayLimitList
}

func (p *GuaranteePayReq) GetCallbackAction() (v string) {
	return p.CallbackAction
}

func (p *GuaranteePayReq) GetTimeoutAction() (v string) {
	return p.TimeoutAction
}

func (p *GuaranteePayReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *GuaranteePayReq) SetMerchantID(val string) {
	p.MerchantID = val
}
func (p *GuaranteePayReq) SetAppID(val string) {
	p.AppID = val
}
func (p *GuaranteePayReq) SetUID(val string) {
	p.UID = val
}
func (p *GuaranteePayReq) SetFinanceOrderType(val int32) {
	p.FinanceOrderType = val
}
func (p *GuaranteePayReq) SetAmount(val int64) {
	p.Amount = val
}
func (p *GuaranteePayReq) SetCashierDeskType(val fwe_trade_common.CashierDeskType) {
	p.CashierDeskType = val
}
func (p *GuaranteePayReq) SetExpireTime(val *int64) {
	p.ExpireTime = val
}
func (p *GuaranteePayReq) SetRedirectURL(val *string) {
	p.RedirectURL = val
}
func (p *GuaranteePayReq) SetIPAddress(val string) {
	p.IPAddress = val
}
func (p *GuaranteePayReq) SetPayLimitList(val []fwe_trade_common.PayLimitType) {
	p.PayLimitList = val
}
func (p *GuaranteePayReq) SetCallbackAction(val string) {
	p.CallbackAction = val
}
func (p *GuaranteePayReq) SetTimeoutAction(val string) {
	p.TimeoutAction = val
}
func (p *GuaranteePayReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_GuaranteePayReq = map[int16]string{
	1:   "merchant_id",
	2:   "app_id",
	3:   "uid",
	10:  "finance_order_type",
	11:  "amount",
	12:  "cashier_desk_type",
	13:  "expire_time",
	14:  "redirect_url",
	15:  "ip_address",
	16:  "pay_limit_list",
	100: "callback_action",
	101: "timeout_action",
	200: "tag",
}

func (p *GuaranteePayReq) IsSetExpireTime() bool {
	return p.ExpireTime != nil
}

func (p *GuaranteePayReq) IsSetRedirectURL() bool {
	return p.RedirectURL != nil
}

func (p *GuaranteePayReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GuaranteePayReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GuaranteePayReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GuaranteePayReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MerchantID = _field
	return nil
}
func (p *GuaranteePayReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppID = _field
	return nil
}
func (p *GuaranteePayReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UID = _field
	return nil
}
func (p *GuaranteePayReq) ReadField10(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceOrderType = _field
	return nil
}
func (p *GuaranteePayReq) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *GuaranteePayReq) ReadField12(iprot thrift.TProtocol) error {

	var _field fwe_trade_common.CashierDeskType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = fwe_trade_common.CashierDeskType(v)
	}
	p.CashierDeskType = _field
	return nil
}
func (p *GuaranteePayReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExpireTime = _field
	return nil
}
func (p *GuaranteePayReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RedirectURL = _field
	return nil
}
func (p *GuaranteePayReq) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IPAddress = _field
	return nil
}
func (p *GuaranteePayReq) ReadField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]fwe_trade_common.PayLimitType, 0, size)
	for i := 0; i < size; i++ {

		var _elem fwe_trade_common.PayLimitType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = fwe_trade_common.PayLimitType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PayLimitList = _field
	return nil
}
func (p *GuaranteePayReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackAction = _field
	return nil
}
func (p *GuaranteePayReq) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TimeoutAction = _field
	return nil
}
func (p *GuaranteePayReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *GuaranteePayReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GuaranteePayReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GuaranteePayReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GuaranteePayReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merchant_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MerchantID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("uid", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_order_type", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FinanceOrderType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cashier_desk_type", thrift.I32, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.CashierDeskType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetExpireTime() {
		if err = oprot.WriteFieldBegin("expire_time", thrift.I64, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ExpireTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetRedirectURL() {
		if err = oprot.WriteFieldBegin("redirect_url", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RedirectURL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ip_address", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IPAddress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_limit_list", thrift.LIST, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.I32, len(p.PayLimitList)); err != nil {
		return err
	}
	for _, v := range p.PayLimitList {
		if err := oprot.WriteI32(int32(v)); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_action", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("timeout_action", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TimeoutAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *GuaranteePayReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *GuaranteePayReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GuaranteePayReq(%+v)", *p)

}

type GuaranteePayRsp struct {
	PayData    string `thrift:"pay_data,1" frugal:"1,default,string" json:"pay_data"`
	PayOrderNo string `thrift:"pay_order_no,2" frugal:"2,default,string" json:"pay_order_no"`
	PayTradeNo string `thrift:"pay_trade_no,3" frugal:"3,default,string" json:"pay_trade_no"`
}

func NewGuaranteePayRsp() *GuaranteePayRsp {
	return &GuaranteePayRsp{}
}

func (p *GuaranteePayRsp) InitDefault() {
}

func (p *GuaranteePayRsp) GetPayData() (v string) {
	return p.PayData
}

func (p *GuaranteePayRsp) GetPayOrderNo() (v string) {
	return p.PayOrderNo
}

func (p *GuaranteePayRsp) GetPayTradeNo() (v string) {
	return p.PayTradeNo
}
func (p *GuaranteePayRsp) SetPayData(val string) {
	p.PayData = val
}
func (p *GuaranteePayRsp) SetPayOrderNo(val string) {
	p.PayOrderNo = val
}
func (p *GuaranteePayRsp) SetPayTradeNo(val string) {
	p.PayTradeNo = val
}

var fieldIDToName_GuaranteePayRsp = map[int16]string{
	1: "pay_data",
	2: "pay_order_no",
	3: "pay_trade_no",
}

func (p *GuaranteePayRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GuaranteePayRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GuaranteePayRsp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GuaranteePayRsp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayData = _field
	return nil
}
func (p *GuaranteePayRsp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayOrderNo = _field
	return nil
}
func (p *GuaranteePayRsp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayTradeNo = _field
	return nil
}

func (p *GuaranteePayRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GuaranteePayRsp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GuaranteePayRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GuaranteePayRsp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_data", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PayData); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GuaranteePayRsp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_order_no", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PayOrderNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GuaranteePayRsp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_trade_no", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PayTradeNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *GuaranteePayRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GuaranteePayRsp(%+v)", *p)

}

type CashPayReq struct {
	MerchantID       string                           `thrift:"merchant_id,1" frugal:"1,default,string" json:"merchant_id"`
	AppID            string                           `thrift:"app_id,2" frugal:"2,default,string" json:"app_id"`
	UserID           *string                          `thrift:"user_id,3,optional" frugal:"3,optional,string" json:"user_id,omitempty"`
	PayOrderNo       *string                          `thrift:"pay_order_no,4,optional" frugal:"4,optional,string" json:"pay_order_no,omitempty"`
	FinanceOrderType int32                            `thrift:"finance_order_type,10" frugal:"10,default,i32" json:"finance_order_type"`
	Amount           int64                            `thrift:"amount,11" frugal:"11,default,i64" json:"amount"`
	CashierDeskType  fwe_trade_common.CashierDeskType `thrift:"cashier_desk_type,12" frugal:"12,default,CashierDeskType" json:"cashier_desk_type"`
	ExpireTime       *int64                           `thrift:"expire_time,13,optional" frugal:"13,optional,i64" json:"expire_time,omitempty"`
	RedirectURL      *string                          `thrift:"redirect_url,14,optional" frugal:"14,optional,string" json:"redirect_url,omitempty"`
	IPAddress        string                           `thrift:"ip_address,15" frugal:"15,default,string" json:"ip_address"`
	PayLimitList     []fwe_trade_common.PayLimitType  `thrift:"pay_limit_list,16" frugal:"16,default,list<PayLimitType>" json:"pay_limit_list"`
	CallbackAction   string                           `thrift:"callback_action,100" frugal:"100,default,string" json:"callback_action"`
	TimeoutAction    string                           `thrift:"timeout_action,101" frugal:"101,default,string" json:"timeout_action"`
	Tag              map[string]string                `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewCashPayReq() *CashPayReq {
	return &CashPayReq{}
}

func (p *CashPayReq) InitDefault() {
}

func (p *CashPayReq) GetMerchantID() (v string) {
	return p.MerchantID
}

func (p *CashPayReq) GetAppID() (v string) {
	return p.AppID
}

var CashPayReq_UserID_DEFAULT string

func (p *CashPayReq) GetUserID() (v string) {
	if !p.IsSetUserID() {
		return CashPayReq_UserID_DEFAULT
	}
	return *p.UserID
}

var CashPayReq_PayOrderNo_DEFAULT string

func (p *CashPayReq) GetPayOrderNo() (v string) {
	if !p.IsSetPayOrderNo() {
		return CashPayReq_PayOrderNo_DEFAULT
	}
	return *p.PayOrderNo
}

func (p *CashPayReq) GetFinanceOrderType() (v int32) {
	return p.FinanceOrderType
}

func (p *CashPayReq) GetAmount() (v int64) {
	return p.Amount
}

func (p *CashPayReq) GetCashierDeskType() (v fwe_trade_common.CashierDeskType) {
	return p.CashierDeskType
}

var CashPayReq_ExpireTime_DEFAULT int64

func (p *CashPayReq) GetExpireTime() (v int64) {
	if !p.IsSetExpireTime() {
		return CashPayReq_ExpireTime_DEFAULT
	}
	return *p.ExpireTime
}

var CashPayReq_RedirectURL_DEFAULT string

func (p *CashPayReq) GetRedirectURL() (v string) {
	if !p.IsSetRedirectURL() {
		return CashPayReq_RedirectURL_DEFAULT
	}
	return *p.RedirectURL
}

func (p *CashPayReq) GetIPAddress() (v string) {
	return p.IPAddress
}

func (p *CashPayReq) GetPayLimitList() (v []fwe_trade_common.PayLimitType) {
	return p.PayLimitList
}

func (p *CashPayReq) GetCallbackAction() (v string) {
	return p.CallbackAction
}

func (p *CashPayReq) GetTimeoutAction() (v string) {
	return p.TimeoutAction
}

func (p *CashPayReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *CashPayReq) SetMerchantID(val string) {
	p.MerchantID = val
}
func (p *CashPayReq) SetAppID(val string) {
	p.AppID = val
}
func (p *CashPayReq) SetUserID(val *string) {
	p.UserID = val
}
func (p *CashPayReq) SetPayOrderNo(val *string) {
	p.PayOrderNo = val
}
func (p *CashPayReq) SetFinanceOrderType(val int32) {
	p.FinanceOrderType = val
}
func (p *CashPayReq) SetAmount(val int64) {
	p.Amount = val
}
func (p *CashPayReq) SetCashierDeskType(val fwe_trade_common.CashierDeskType) {
	p.CashierDeskType = val
}
func (p *CashPayReq) SetExpireTime(val *int64) {
	p.ExpireTime = val
}
func (p *CashPayReq) SetRedirectURL(val *string) {
	p.RedirectURL = val
}
func (p *CashPayReq) SetIPAddress(val string) {
	p.IPAddress = val
}
func (p *CashPayReq) SetPayLimitList(val []fwe_trade_common.PayLimitType) {
	p.PayLimitList = val
}
func (p *CashPayReq) SetCallbackAction(val string) {
	p.CallbackAction = val
}
func (p *CashPayReq) SetTimeoutAction(val string) {
	p.TimeoutAction = val
}
func (p *CashPayReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_CashPayReq = map[int16]string{
	1:   "merchant_id",
	2:   "app_id",
	3:   "user_id",
	4:   "pay_order_no",
	10:  "finance_order_type",
	11:  "amount",
	12:  "cashier_desk_type",
	13:  "expire_time",
	14:  "redirect_url",
	15:  "ip_address",
	16:  "pay_limit_list",
	100: "callback_action",
	101: "timeout_action",
	200: "tag",
}

func (p *CashPayReq) IsSetUserID() bool {
	return p.UserID != nil
}

func (p *CashPayReq) IsSetPayOrderNo() bool {
	return p.PayOrderNo != nil
}

func (p *CashPayReq) IsSetExpireTime() bool {
	return p.ExpireTime != nil
}

func (p *CashPayReq) IsSetRedirectURL() bool {
	return p.RedirectURL != nil
}

func (p *CashPayReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CashPayReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CashPayReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CashPayReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MerchantID = _field
	return nil
}
func (p *CashPayReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppID = _field
	return nil
}
func (p *CashPayReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserID = _field
	return nil
}
func (p *CashPayReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PayOrderNo = _field
	return nil
}
func (p *CashPayReq) ReadField10(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceOrderType = _field
	return nil
}
func (p *CashPayReq) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *CashPayReq) ReadField12(iprot thrift.TProtocol) error {

	var _field fwe_trade_common.CashierDeskType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = fwe_trade_common.CashierDeskType(v)
	}
	p.CashierDeskType = _field
	return nil
}
func (p *CashPayReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExpireTime = _field
	return nil
}
func (p *CashPayReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RedirectURL = _field
	return nil
}
func (p *CashPayReq) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IPAddress = _field
	return nil
}
func (p *CashPayReq) ReadField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]fwe_trade_common.PayLimitType, 0, size)
	for i := 0; i < size; i++ {

		var _elem fwe_trade_common.PayLimitType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = fwe_trade_common.PayLimitType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PayLimitList = _field
	return nil
}
func (p *CashPayReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackAction = _field
	return nil
}
func (p *CashPayReq) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TimeoutAction = _field
	return nil
}
func (p *CashPayReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *CashPayReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CashPayReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CashPayReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CashPayReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merchant_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MerchantID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CashPayReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CashPayReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserID() {
		if err = oprot.WriteFieldBegin("user_id", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UserID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CashPayReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPayOrderNo() {
		if err = oprot.WriteFieldBegin("pay_order_no", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.PayOrderNo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CashPayReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_order_type", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FinanceOrderType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *CashPayReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *CashPayReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cashier_desk_type", thrift.I32, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.CashierDeskType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *CashPayReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetExpireTime() {
		if err = oprot.WriteFieldBegin("expire_time", thrift.I64, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ExpireTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *CashPayReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetRedirectURL() {
		if err = oprot.WriteFieldBegin("redirect_url", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RedirectURL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *CashPayReq) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ip_address", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IPAddress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *CashPayReq) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_limit_list", thrift.LIST, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.I32, len(p.PayLimitList)); err != nil {
		return err
	}
	for _, v := range p.PayLimitList {
		if err := oprot.WriteI32(int32(v)); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *CashPayReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_action", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *CashPayReq) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("timeout_action", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TimeoutAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *CashPayReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *CashPayReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CashPayReq(%+v)", *p)

}

type CashPayRsp struct {
	PayData    string `thrift:"pay_data,1" frugal:"1,default,string" json:"pay_data"`
	PayOrderNo string `thrift:"pay_order_no,2" frugal:"2,default,string" json:"pay_order_no"`
	PayTradeNo string `thrift:"pay_trade_no,3" frugal:"3,default,string" json:"pay_trade_no"`
}

func NewCashPayRsp() *CashPayRsp {
	return &CashPayRsp{}
}

func (p *CashPayRsp) InitDefault() {
}

func (p *CashPayRsp) GetPayData() (v string) {
	return p.PayData
}

func (p *CashPayRsp) GetPayOrderNo() (v string) {
	return p.PayOrderNo
}

func (p *CashPayRsp) GetPayTradeNo() (v string) {
	return p.PayTradeNo
}
func (p *CashPayRsp) SetPayData(val string) {
	p.PayData = val
}
func (p *CashPayRsp) SetPayOrderNo(val string) {
	p.PayOrderNo = val
}
func (p *CashPayRsp) SetPayTradeNo(val string) {
	p.PayTradeNo = val
}

var fieldIDToName_CashPayRsp = map[int16]string{
	1: "pay_data",
	2: "pay_order_no",
	3: "pay_trade_no",
}

func (p *CashPayRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CashPayRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CashPayRsp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CashPayRsp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayData = _field
	return nil
}
func (p *CashPayRsp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayOrderNo = _field
	return nil
}
func (p *CashPayRsp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayTradeNo = _field
	return nil
}

func (p *CashPayRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("CashPayRsp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CashPayRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CashPayRsp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_data", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PayData); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CashPayRsp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_order_no", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PayOrderNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CashPayRsp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_trade_no", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PayTradeNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CashPayRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CashPayRsp(%+v)", *p)

}

type OfflinePayReq struct {
	PayOrderNo       *string                               `thrift:"pay_order_no,1,optional" frugal:"1,optional,string" json:"pay_order_no,omitempty"`
	FinanceOrderType int32                                 `thrift:"finance_order_type,10" frugal:"10,default,i32" json:"finance_order_type"`
	Amount           int64                                 `thrift:"amount,11" frugal:"11,default,i64" json:"amount"`
	CheckData        *fwe_trade_common.PayOfflineCheckData `thrift:"check_data,12" frugal:"12,default,fwe_trade_common.PayOfflineCheckData" json:"check_data"`
	RedirectURL      *string                               `thrift:"redirect_url,14,optional" frugal:"14,optional,string" json:"redirect_url,omitempty"`
	IPAddress        string                                `thrift:"ip_address,15" frugal:"15,default,string" json:"ip_address"`
	CallbackAction   string                                `thrift:"callback_action,100" frugal:"100,default,string" json:"callback_action"`
	TimeoutAction    string                                `thrift:"timeout_action,101" frugal:"101,default,string" json:"timeout_action"`
	Tag              map[string]string                     `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewOfflinePayReq() *OfflinePayReq {
	return &OfflinePayReq{}
}

func (p *OfflinePayReq) InitDefault() {
}

var OfflinePayReq_PayOrderNo_DEFAULT string

func (p *OfflinePayReq) GetPayOrderNo() (v string) {
	if !p.IsSetPayOrderNo() {
		return OfflinePayReq_PayOrderNo_DEFAULT
	}
	return *p.PayOrderNo
}

func (p *OfflinePayReq) GetFinanceOrderType() (v int32) {
	return p.FinanceOrderType
}

func (p *OfflinePayReq) GetAmount() (v int64) {
	return p.Amount
}

var OfflinePayReq_CheckData_DEFAULT *fwe_trade_common.PayOfflineCheckData

func (p *OfflinePayReq) GetCheckData() (v *fwe_trade_common.PayOfflineCheckData) {
	if !p.IsSetCheckData() {
		return OfflinePayReq_CheckData_DEFAULT
	}
	return p.CheckData
}

var OfflinePayReq_RedirectURL_DEFAULT string

func (p *OfflinePayReq) GetRedirectURL() (v string) {
	if !p.IsSetRedirectURL() {
		return OfflinePayReq_RedirectURL_DEFAULT
	}
	return *p.RedirectURL
}

func (p *OfflinePayReq) GetIPAddress() (v string) {
	return p.IPAddress
}

func (p *OfflinePayReq) GetCallbackAction() (v string) {
	return p.CallbackAction
}

func (p *OfflinePayReq) GetTimeoutAction() (v string) {
	return p.TimeoutAction
}

func (p *OfflinePayReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *OfflinePayReq) SetPayOrderNo(val *string) {
	p.PayOrderNo = val
}
func (p *OfflinePayReq) SetFinanceOrderType(val int32) {
	p.FinanceOrderType = val
}
func (p *OfflinePayReq) SetAmount(val int64) {
	p.Amount = val
}
func (p *OfflinePayReq) SetCheckData(val *fwe_trade_common.PayOfflineCheckData) {
	p.CheckData = val
}
func (p *OfflinePayReq) SetRedirectURL(val *string) {
	p.RedirectURL = val
}
func (p *OfflinePayReq) SetIPAddress(val string) {
	p.IPAddress = val
}
func (p *OfflinePayReq) SetCallbackAction(val string) {
	p.CallbackAction = val
}
func (p *OfflinePayReq) SetTimeoutAction(val string) {
	p.TimeoutAction = val
}
func (p *OfflinePayReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_OfflinePayReq = map[int16]string{
	1:   "pay_order_no",
	10:  "finance_order_type",
	11:  "amount",
	12:  "check_data",
	14:  "redirect_url",
	15:  "ip_address",
	100: "callback_action",
	101: "timeout_action",
	200: "tag",
}

func (p *OfflinePayReq) IsSetPayOrderNo() bool {
	return p.PayOrderNo != nil
}

func (p *OfflinePayReq) IsSetCheckData() bool {
	return p.CheckData != nil
}

func (p *OfflinePayReq) IsSetRedirectURL() bool {
	return p.RedirectURL != nil
}

func (p *OfflinePayReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OfflinePayReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OfflinePayReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OfflinePayReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PayOrderNo = _field
	return nil
}
func (p *OfflinePayReq) ReadField10(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceOrderType = _field
	return nil
}
func (p *OfflinePayReq) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *OfflinePayReq) ReadField12(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewPayOfflineCheckData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CheckData = _field
	return nil
}
func (p *OfflinePayReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RedirectURL = _field
	return nil
}
func (p *OfflinePayReq) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IPAddress = _field
	return nil
}
func (p *OfflinePayReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackAction = _field
	return nil
}
func (p *OfflinePayReq) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TimeoutAction = _field
	return nil
}
func (p *OfflinePayReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *OfflinePayReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OfflinePayReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("OfflinePayReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OfflinePayReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPayOrderNo() {
		if err = oprot.WriteFieldBegin("pay_order_no", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.PayOrderNo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *OfflinePayReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_order_type", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FinanceOrderType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *OfflinePayReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *OfflinePayReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("check_data", thrift.STRUCT, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CheckData.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *OfflinePayReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetRedirectURL() {
		if err = oprot.WriteFieldBegin("redirect_url", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RedirectURL); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *OfflinePayReq) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ip_address", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IPAddress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *OfflinePayReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_action", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *OfflinePayReq) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("timeout_action", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TimeoutAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *OfflinePayReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *OfflinePayReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OfflinePayReq(%+v)", *p)

}

type OfflinePayRsp struct {
	PayOrderNo string `thrift:"pay_order_no,1" frugal:"1,default,string" json:"pay_order_no"`
}

func NewOfflinePayRsp() *OfflinePayRsp {
	return &OfflinePayRsp{}
}

func (p *OfflinePayRsp) InitDefault() {
}

func (p *OfflinePayRsp) GetPayOrderNo() (v string) {
	return p.PayOrderNo
}
func (p *OfflinePayRsp) SetPayOrderNo(val string) {
	p.PayOrderNo = val
}

var fieldIDToName_OfflinePayRsp = map[int16]string{
	1: "pay_order_no",
}

func (p *OfflinePayRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OfflinePayRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OfflinePayRsp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OfflinePayRsp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayOrderNo = _field
	return nil
}

func (p *OfflinePayRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OfflinePayRsp")

	var fieldId int16
	if err = oprot.WriteStructBegin("OfflinePayRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OfflinePayRsp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_order_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PayOrderNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OfflinePayRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OfflinePayRsp(%+v)", *p)

}

type WithdrawReq struct {
	MerchantID         string                      `thrift:"merchant_id,1" frugal:"1,default,string" json:"merchant_id"`
	AppID              string                      `thrift:"app_id,2" frugal:"2,default,string" json:"app_id"`
	UID                string                      `thrift:"uid,3" frugal:"3,default,string" json:"uid"`
	MerchantName       string                      `thrift:"merchant_name,4" frugal:"4,default,string" json:"merchant_name"`
	FinanceOrderType   int32                       `thrift:"finance_order_type,10" frugal:"10,default,i32" json:"finance_order_type"`
	Amount             int64                       `thrift:"amount,11" frugal:"11,default,i64" json:"amount"`
	WithdrawType       payment.WithdrawType        `thrift:"withdraw_type,12" frugal:"12,default,WithdrawType" json:"withdraw_type"`
	Reason             string                      `thrift:"reason,13" frugal:"13,default,string" json:"reason"`
	BankCardInfo       *fwe_trade_common.BankInfo  `thrift:"bank_card_info,14" frugal:"14,default,fwe_trade_common.BankInfo" json:"bank_card_info"`
	IPAddress          string                      `thrift:"ip_address,15" frugal:"15,default,string" json:"ip_address"`
	FeeItemList        []*fwe_trade_common.FeeItem `thrift:"fee_item_list,16" frugal:"16,default,list<fwe_trade_common.FeeItem>" json:"fee_item_list"`
	CallbackAction     string                      `thrift:"callback_action,100" frugal:"100,default,string" json:"callback_action"`
	FailCallbackAction string                      `thrift:"fail_callback_action,101" frugal:"101,default,string" json:"fail_callback_action"`
	FweAccountID       string                      `thrift:"fwe_account_id,102" frugal:"102,default,string" json:"fwe_account_id"`
	Tag                map[string]string           `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewWithdrawReq() *WithdrawReq {
	return &WithdrawReq{}
}

func (p *WithdrawReq) InitDefault() {
}

func (p *WithdrawReq) GetMerchantID() (v string) {
	return p.MerchantID
}

func (p *WithdrawReq) GetAppID() (v string) {
	return p.AppID
}

func (p *WithdrawReq) GetUID() (v string) {
	return p.UID
}

func (p *WithdrawReq) GetMerchantName() (v string) {
	return p.MerchantName
}

func (p *WithdrawReq) GetFinanceOrderType() (v int32) {
	return p.FinanceOrderType
}

func (p *WithdrawReq) GetAmount() (v int64) {
	return p.Amount
}

func (p *WithdrawReq) GetWithdrawType() (v payment.WithdrawType) {
	return p.WithdrawType
}

func (p *WithdrawReq) GetReason() (v string) {
	return p.Reason
}

var WithdrawReq_BankCardInfo_DEFAULT *fwe_trade_common.BankInfo

func (p *WithdrawReq) GetBankCardInfo() (v *fwe_trade_common.BankInfo) {
	if !p.IsSetBankCardInfo() {
		return WithdrawReq_BankCardInfo_DEFAULT
	}
	return p.BankCardInfo
}

func (p *WithdrawReq) GetIPAddress() (v string) {
	return p.IPAddress
}

func (p *WithdrawReq) GetFeeItemList() (v []*fwe_trade_common.FeeItem) {
	return p.FeeItemList
}

func (p *WithdrawReq) GetCallbackAction() (v string) {
	return p.CallbackAction
}

func (p *WithdrawReq) GetFailCallbackAction() (v string) {
	return p.FailCallbackAction
}

func (p *WithdrawReq) GetFweAccountID() (v string) {
	return p.FweAccountID
}

func (p *WithdrawReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *WithdrawReq) SetMerchantID(val string) {
	p.MerchantID = val
}
func (p *WithdrawReq) SetAppID(val string) {
	p.AppID = val
}
func (p *WithdrawReq) SetUID(val string) {
	p.UID = val
}
func (p *WithdrawReq) SetMerchantName(val string) {
	p.MerchantName = val
}
func (p *WithdrawReq) SetFinanceOrderType(val int32) {
	p.FinanceOrderType = val
}
func (p *WithdrawReq) SetAmount(val int64) {
	p.Amount = val
}
func (p *WithdrawReq) SetWithdrawType(val payment.WithdrawType) {
	p.WithdrawType = val
}
func (p *WithdrawReq) SetReason(val string) {
	p.Reason = val
}
func (p *WithdrawReq) SetBankCardInfo(val *fwe_trade_common.BankInfo) {
	p.BankCardInfo = val
}
func (p *WithdrawReq) SetIPAddress(val string) {
	p.IPAddress = val
}
func (p *WithdrawReq) SetFeeItemList(val []*fwe_trade_common.FeeItem) {
	p.FeeItemList = val
}
func (p *WithdrawReq) SetCallbackAction(val string) {
	p.CallbackAction = val
}
func (p *WithdrawReq) SetFailCallbackAction(val string) {
	p.FailCallbackAction = val
}
func (p *WithdrawReq) SetFweAccountID(val string) {
	p.FweAccountID = val
}
func (p *WithdrawReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_WithdrawReq = map[int16]string{
	1:   "merchant_id",
	2:   "app_id",
	3:   "uid",
	4:   "merchant_name",
	10:  "finance_order_type",
	11:  "amount",
	12:  "withdraw_type",
	13:  "reason",
	14:  "bank_card_info",
	15:  "ip_address",
	16:  "fee_item_list",
	100: "callback_action",
	101: "fail_callback_action",
	102: "fwe_account_id",
	200: "tag",
}

func (p *WithdrawReq) IsSetBankCardInfo() bool {
	return p.BankCardInfo != nil
}

func (p *WithdrawReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("WithdrawReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 102:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField102(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WithdrawReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *WithdrawReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MerchantID = _field
	return nil
}
func (p *WithdrawReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppID = _field
	return nil
}
func (p *WithdrawReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UID = _field
	return nil
}
func (p *WithdrawReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MerchantName = _field
	return nil
}
func (p *WithdrawReq) ReadField10(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceOrderType = _field
	return nil
}
func (p *WithdrawReq) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *WithdrawReq) ReadField12(iprot thrift.TProtocol) error {

	var _field payment.WithdrawType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = payment.WithdrawType(v)
	}
	p.WithdrawType = _field
	return nil
}
func (p *WithdrawReq) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Reason = _field
	return nil
}
func (p *WithdrawReq) ReadField14(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewBankInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BankCardInfo = _field
	return nil
}
func (p *WithdrawReq) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IPAddress = _field
	return nil
}
func (p *WithdrawReq) ReadField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.FeeItem, 0, size)
	values := make([]fwe_trade_common.FeeItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FeeItemList = _field
	return nil
}
func (p *WithdrawReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackAction = _field
	return nil
}
func (p *WithdrawReq) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FailCallbackAction = _field
	return nil
}
func (p *WithdrawReq) ReadField102(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FweAccountID = _field
	return nil
}
func (p *WithdrawReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *WithdrawReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("WithdrawReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("WithdrawReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField102(oprot); err != nil {
			fieldId = 102
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WithdrawReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merchant_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MerchantID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *WithdrawReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *WithdrawReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("uid", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *WithdrawReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merchant_name", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MerchantName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *WithdrawReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_order_type", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FinanceOrderType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *WithdrawReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *WithdrawReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("withdraw_type", thrift.I32, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.WithdrawType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *WithdrawReq) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("reason", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Reason); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *WithdrawReq) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("bank_card_info", thrift.STRUCT, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BankCardInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *WithdrawReq) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ip_address", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IPAddress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *WithdrawReq) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fee_item_list", thrift.LIST, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FeeItemList)); err != nil {
		return err
	}
	for _, v := range p.FeeItemList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *WithdrawReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_action", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *WithdrawReq) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fail_callback_action", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FailCallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *WithdrawReq) writeField102(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fwe_account_id", thrift.STRING, 102); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FweAccountID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 end error: ", p), err)
}
func (p *WithdrawReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *WithdrawReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WithdrawReq(%+v)", *p)

}

type WithdrawRsp struct {
	WithdrawOrderNo string `thrift:"withdraw_order_no,1" frugal:"1,default,string" json:"withdraw_order_no"`
	WithdrawTradeNo string `thrift:"withdraw_trade_no,2" frugal:"2,default,string" json:"withdraw_trade_no"`
}

func NewWithdrawRsp() *WithdrawRsp {
	return &WithdrawRsp{}
}

func (p *WithdrawRsp) InitDefault() {
}

func (p *WithdrawRsp) GetWithdrawOrderNo() (v string) {
	return p.WithdrawOrderNo
}

func (p *WithdrawRsp) GetWithdrawTradeNo() (v string) {
	return p.WithdrawTradeNo
}
func (p *WithdrawRsp) SetWithdrawOrderNo(val string) {
	p.WithdrawOrderNo = val
}
func (p *WithdrawRsp) SetWithdrawTradeNo(val string) {
	p.WithdrawTradeNo = val
}

var fieldIDToName_WithdrawRsp = map[int16]string{
	1: "withdraw_order_no",
	2: "withdraw_trade_no",
}

func (p *WithdrawRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("WithdrawRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WithdrawRsp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *WithdrawRsp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WithdrawOrderNo = _field
	return nil
}
func (p *WithdrawRsp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WithdrawTradeNo = _field
	return nil
}

func (p *WithdrawRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("WithdrawRsp")

	var fieldId int16
	if err = oprot.WriteStructBegin("WithdrawRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WithdrawRsp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("withdraw_order_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WithdrawOrderNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *WithdrawRsp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("withdraw_trade_no", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WithdrawTradeNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *WithdrawRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WithdrawRsp(%+v)", *p)

}

type SingleRefund struct {
	FinanceOrderType int32                               `thrift:"finance_order_type,1" frugal:"1,default,i32" json:"finance_order_type"`
	Amount           int64                               `thrift:"amount,2" frugal:"2,default,i64" json:"amount"`
	YztOfflineAmount int64                               `thrift:"yzt_offline_amount,3" frugal:"3,default,i64" json:"yzt_offline_amount"`
	JstRefundList    []*fwe_trade_common.JstRefundSingle `thrift:"jst_refund_list,10" frugal:"10,default,list<fwe_trade_common.JstRefundSingle>" json:"jst_refund_list"`
}

func NewSingleRefund() *SingleRefund {
	return &SingleRefund{}
}

func (p *SingleRefund) InitDefault() {
}

func (p *SingleRefund) GetFinanceOrderType() (v int32) {
	return p.FinanceOrderType
}

func (p *SingleRefund) GetAmount() (v int64) {
	return p.Amount
}

func (p *SingleRefund) GetYztOfflineAmount() (v int64) {
	return p.YztOfflineAmount
}

func (p *SingleRefund) GetJstRefundList() (v []*fwe_trade_common.JstRefundSingle) {
	return p.JstRefundList
}
func (p *SingleRefund) SetFinanceOrderType(val int32) {
	p.FinanceOrderType = val
}
func (p *SingleRefund) SetAmount(val int64) {
	p.Amount = val
}
func (p *SingleRefund) SetYztOfflineAmount(val int64) {
	p.YztOfflineAmount = val
}
func (p *SingleRefund) SetJstRefundList(val []*fwe_trade_common.JstRefundSingle) {
	p.JstRefundList = val
}

var fieldIDToName_SingleRefund = map[int16]string{
	1:  "finance_order_type",
	2:  "amount",
	3:  "yzt_offline_amount",
	10: "jst_refund_list",
}

func (p *SingleRefund) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("SingleRefund")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SingleRefund[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SingleRefund) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceOrderType = _field
	return nil
}
func (p *SingleRefund) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *SingleRefund) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.YztOfflineAmount = _field
	return nil
}
func (p *SingleRefund) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.JstRefundSingle, 0, size)
	values := make([]fwe_trade_common.JstRefundSingle, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.JstRefundList = _field
	return nil
}

func (p *SingleRefund) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("SingleRefund")

	var fieldId int16
	if err = oprot.WriteStructBegin("SingleRefund"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SingleRefund) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_order_type", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FinanceOrderType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SingleRefund) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SingleRefund) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("yzt_offline_amount", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.YztOfflineAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SingleRefund) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("jst_refund_list", thrift.LIST, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.JstRefundList)); err != nil {
		return err
	}
	for _, v := range p.JstRefundList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SingleRefund) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SingleRefund(%+v)", *p)

}

type RefundReq struct {
	RefundList                 []*SingleRefund                   `thrift:"refund_list,1" frugal:"1,default,list<SingleRefund>" json:"refund_list"`
	RefundType                 int32                             `thrift:"refund_type,2" frugal:"2,default,i32" json:"refund_type"`
	NeedConfirmOfflineRefund   *bool                             `thrift:"need_confirm_offline_refund,3,optional" frugal:"3,optional,bool" json:"need_confirm_offline_refund,omitempty"`
	NeedWithdraw               bool                              `thrift:"need_withdraw,4" frugal:"4,default,bool" json:"need_withdraw"`
	Reason                     string                            `thrift:"reason,10" frugal:"10,default,string" json:"reason"`
	IPAddress                  string                            `thrift:"ip_address,11" frugal:"11,default,string" json:"ip_address"`
	RuleID                     int64                             `thrift:"rule_id,20" frugal:"20,default,i64" json:"rule_id"`
	Params                     string                            `thrift:"params,21" frugal:"21,default,string" json:"params"`
	RefundSettleInfo           *payment.RefundSettleInfo         `thrift:"refund_settle_info,22" frugal:"22,default,payment.RefundSettleInfo" json:"refund_settle_info"`
	YztOfflineRefundSettleInfo *payment.RefundSettleInfo         `thrift:"yzt_offline_refund_settle_info,23" frugal:"23,default,payment.RefundSettleInfo" json:"yzt_offline_refund_settle_info"`
	LifeRefundParam            *fwe_trade_common.LifeRefundParam `thrift:"life_refund_param,24,optional" frugal:"24,optional,fwe_trade_common.LifeRefundParam" json:"life_refund_param,omitempty"`
	RefundMode                 fwe_trade_common.RefundMode       `thrift:"refund_mode,25" frugal:"25,default,RefundMode" json:"refund_mode"`
	CallbackAction             string                            `thrift:"callback_action,100" frugal:"100,default,string" json:"callback_action"`
	DeductItemList             []*fwe_trade_common.FeeItem       `thrift:"deduct_item_list,101" frugal:"101,default,list<fwe_trade_common.FeeItem>" json:"deduct_item_list"`
	FeeItemList                []*fwe_trade_common.FeeItem       `thrift:"fee_item_list,102" frugal:"102,default,list<fwe_trade_common.FeeItem>" json:"fee_item_list"`
	Tag                        map[string]string                 `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewRefundReq() *RefundReq {
	return &RefundReq{}
}

func (p *RefundReq) InitDefault() {
}

func (p *RefundReq) GetRefundList() (v []*SingleRefund) {
	return p.RefundList
}

func (p *RefundReq) GetRefundType() (v int32) {
	return p.RefundType
}

var RefundReq_NeedConfirmOfflineRefund_DEFAULT bool

func (p *RefundReq) GetNeedConfirmOfflineRefund() (v bool) {
	if !p.IsSetNeedConfirmOfflineRefund() {
		return RefundReq_NeedConfirmOfflineRefund_DEFAULT
	}
	return *p.NeedConfirmOfflineRefund
}

func (p *RefundReq) GetNeedWithdraw() (v bool) {
	return p.NeedWithdraw
}

func (p *RefundReq) GetReason() (v string) {
	return p.Reason
}

func (p *RefundReq) GetIPAddress() (v string) {
	return p.IPAddress
}

func (p *RefundReq) GetRuleID() (v int64) {
	return p.RuleID
}

func (p *RefundReq) GetParams() (v string) {
	return p.Params
}

var RefundReq_RefundSettleInfo_DEFAULT *payment.RefundSettleInfo

func (p *RefundReq) GetRefundSettleInfo() (v *payment.RefundSettleInfo) {
	if !p.IsSetRefundSettleInfo() {
		return RefundReq_RefundSettleInfo_DEFAULT
	}
	return p.RefundSettleInfo
}

var RefundReq_YztOfflineRefundSettleInfo_DEFAULT *payment.RefundSettleInfo

func (p *RefundReq) GetYztOfflineRefundSettleInfo() (v *payment.RefundSettleInfo) {
	if !p.IsSetYztOfflineRefundSettleInfo() {
		return RefundReq_YztOfflineRefundSettleInfo_DEFAULT
	}
	return p.YztOfflineRefundSettleInfo
}

var RefundReq_LifeRefundParam_DEFAULT *fwe_trade_common.LifeRefundParam

func (p *RefundReq) GetLifeRefundParam() (v *fwe_trade_common.LifeRefundParam) {
	if !p.IsSetLifeRefundParam() {
		return RefundReq_LifeRefundParam_DEFAULT
	}
	return p.LifeRefundParam
}

func (p *RefundReq) GetRefundMode() (v fwe_trade_common.RefundMode) {
	return p.RefundMode
}

func (p *RefundReq) GetCallbackAction() (v string) {
	return p.CallbackAction
}

func (p *RefundReq) GetDeductItemList() (v []*fwe_trade_common.FeeItem) {
	return p.DeductItemList
}

func (p *RefundReq) GetFeeItemList() (v []*fwe_trade_common.FeeItem) {
	return p.FeeItemList
}

func (p *RefundReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *RefundReq) SetRefundList(val []*SingleRefund) {
	p.RefundList = val
}
func (p *RefundReq) SetRefundType(val int32) {
	p.RefundType = val
}
func (p *RefundReq) SetNeedConfirmOfflineRefund(val *bool) {
	p.NeedConfirmOfflineRefund = val
}
func (p *RefundReq) SetNeedWithdraw(val bool) {
	p.NeedWithdraw = val
}
func (p *RefundReq) SetReason(val string) {
	p.Reason = val
}
func (p *RefundReq) SetIPAddress(val string) {
	p.IPAddress = val
}
func (p *RefundReq) SetRuleID(val int64) {
	p.RuleID = val
}
func (p *RefundReq) SetParams(val string) {
	p.Params = val
}
func (p *RefundReq) SetRefundSettleInfo(val *payment.RefundSettleInfo) {
	p.RefundSettleInfo = val
}
func (p *RefundReq) SetYztOfflineRefundSettleInfo(val *payment.RefundSettleInfo) {
	p.YztOfflineRefundSettleInfo = val
}
func (p *RefundReq) SetLifeRefundParam(val *fwe_trade_common.LifeRefundParam) {
	p.LifeRefundParam = val
}
func (p *RefundReq) SetRefundMode(val fwe_trade_common.RefundMode) {
	p.RefundMode = val
}
func (p *RefundReq) SetCallbackAction(val string) {
	p.CallbackAction = val
}
func (p *RefundReq) SetDeductItemList(val []*fwe_trade_common.FeeItem) {
	p.DeductItemList = val
}
func (p *RefundReq) SetFeeItemList(val []*fwe_trade_common.FeeItem) {
	p.FeeItemList = val
}
func (p *RefundReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_RefundReq = map[int16]string{
	1:   "refund_list",
	2:   "refund_type",
	3:   "need_confirm_offline_refund",
	4:   "need_withdraw",
	10:  "reason",
	11:  "ip_address",
	20:  "rule_id",
	21:  "params",
	22:  "refund_settle_info",
	23:  "yzt_offline_refund_settle_info",
	24:  "life_refund_param",
	25:  "refund_mode",
	100: "callback_action",
	101: "deduct_item_list",
	102: "fee_item_list",
	200: "tag",
}

func (p *RefundReq) IsSetNeedConfirmOfflineRefund() bool {
	return p.NeedConfirmOfflineRefund != nil
}

func (p *RefundReq) IsSetRefundSettleInfo() bool {
	return p.RefundSettleInfo != nil
}

func (p *RefundReq) IsSetYztOfflineRefundSettleInfo() bool {
	return p.YztOfflineRefundSettleInfo != nil
}

func (p *RefundReq) IsSetLifeRefundParam() bool {
	return p.LifeRefundParam != nil
}

func (p *RefundReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RefundReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 24:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField24(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField25(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 102:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField102(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RefundReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RefundReq) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SingleRefund, 0, size)
	values := make([]SingleRefund, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RefundList = _field
	return nil
}
func (p *RefundReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RefundType = _field
	return nil
}
func (p *RefundReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NeedConfirmOfflineRefund = _field
	return nil
}
func (p *RefundReq) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NeedWithdraw = _field
	return nil
}
func (p *RefundReq) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Reason = _field
	return nil
}
func (p *RefundReq) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IPAddress = _field
	return nil
}
func (p *RefundReq) ReadField20(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleID = _field
	return nil
}
func (p *RefundReq) ReadField21(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Params = _field
	return nil
}
func (p *RefundReq) ReadField22(iprot thrift.TProtocol) error {
	_field := payment.NewRefundSettleInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RefundSettleInfo = _field
	return nil
}
func (p *RefundReq) ReadField23(iprot thrift.TProtocol) error {
	_field := payment.NewRefundSettleInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.YztOfflineRefundSettleInfo = _field
	return nil
}
func (p *RefundReq) ReadField24(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewLifeRefundParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.LifeRefundParam = _field
	return nil
}
func (p *RefundReq) ReadField25(iprot thrift.TProtocol) error {

	var _field fwe_trade_common.RefundMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = fwe_trade_common.RefundMode(v)
	}
	p.RefundMode = _field
	return nil
}
func (p *RefundReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackAction = _field
	return nil
}
func (p *RefundReq) ReadField101(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.FeeItem, 0, size)
	values := make([]fwe_trade_common.FeeItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DeductItemList = _field
	return nil
}
func (p *RefundReq) ReadField102(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.FeeItem, 0, size)
	values := make([]fwe_trade_common.FeeItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FeeItemList = _field
	return nil
}
func (p *RefundReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *RefundReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RefundReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RefundReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
		if err = p.writeField24(oprot); err != nil {
			fieldId = 24
			goto WriteFieldError
		}
		if err = p.writeField25(oprot); err != nil {
			fieldId = 25
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField102(oprot); err != nil {
			fieldId = 102
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RefundReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_list", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RefundList)); err != nil {
		return err
	}
	for _, v := range p.RefundList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RefundReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.RefundType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *RefundReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetNeedConfirmOfflineRefund() {
		if err = oprot.WriteFieldBegin("need_confirm_offline_refund", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.NeedConfirmOfflineRefund); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *RefundReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("need_withdraw", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.NeedWithdraw); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *RefundReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("reason", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Reason); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *RefundReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ip_address", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IPAddress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *RefundReq) writeField20(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_id", thrift.I64, 20); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RuleID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}
func (p *RefundReq) writeField21(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("params", thrift.STRING, 21); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Params); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}
func (p *RefundReq) writeField22(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_settle_info", thrift.STRUCT, 22); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.RefundSettleInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}
func (p *RefundReq) writeField23(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("yzt_offline_refund_settle_info", thrift.STRUCT, 23); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.YztOfflineRefundSettleInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}
func (p *RefundReq) writeField24(oprot thrift.TProtocol) (err error) {
	if p.IsSetLifeRefundParam() {
		if err = oprot.WriteFieldBegin("life_refund_param", thrift.STRUCT, 24); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.LifeRefundParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 end error: ", p), err)
}
func (p *RefundReq) writeField25(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_mode", thrift.I32, 25); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.RefundMode)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 end error: ", p), err)
}
func (p *RefundReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_action", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *RefundReq) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("deduct_item_list", thrift.LIST, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DeductItemList)); err != nil {
		return err
	}
	for _, v := range p.DeductItemList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *RefundReq) writeField102(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fee_item_list", thrift.LIST, 102); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FeeItemList)); err != nil {
		return err
	}
	for _, v := range p.FeeItemList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 102 end error: ", p), err)
}
func (p *RefundReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *RefundReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RefundReq(%+v)", *p)

}

type RefundRsp struct {
	MergeRefundNo     string   `thrift:"merge_refund_no,1" frugal:"1,default,string" json:"merge_refund_no"`
	RefundOrderNoList []string `thrift:"refund_order_no_list,2" frugal:"2,default,list<string>" json:"refund_order_no_list"`
}

func NewRefundRsp() *RefundRsp {
	return &RefundRsp{}
}

func (p *RefundRsp) InitDefault() {
}

func (p *RefundRsp) GetMergeRefundNo() (v string) {
	return p.MergeRefundNo
}

func (p *RefundRsp) GetRefundOrderNoList() (v []string) {
	return p.RefundOrderNoList
}
func (p *RefundRsp) SetMergeRefundNo(val string) {
	p.MergeRefundNo = val
}
func (p *RefundRsp) SetRefundOrderNoList(val []string) {
	p.RefundOrderNoList = val
}

var fieldIDToName_RefundRsp = map[int16]string{
	1: "merge_refund_no",
	2: "refund_order_no_list",
}

func (p *RefundRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RefundRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RefundRsp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RefundRsp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MergeRefundNo = _field
	return nil
}
func (p *RefundRsp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RefundOrderNoList = _field
	return nil
}

func (p *RefundRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RefundRsp")

	var fieldId int16
	if err = oprot.WriteStructBegin("RefundRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RefundRsp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merge_refund_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MergeRefundNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RefundRsp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_order_no_list", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.RefundOrderNoList)); err != nil {
		return err
	}
	for _, v := range p.RefundOrderNoList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RefundRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RefundRsp(%+v)", *p)

}

type SettleReq struct {
	SplitInfo          *fwe_trade_common.TradeSpiltInfo `thrift:"split_info,1" frugal:"1,default,fwe_trade_common.TradeSpiltInfo" json:"split_info"`
	SettleType         int32                            `thrift:"settle_type,2" frugal:"2,default,i32" json:"settle_type"`
	FinanceIDList      []string                         `thrift:"finance_id_list,3,optional" frugal:"3,optional,list<string>" json:"finance_id_list,omitempty"`
	FinanceTypeList    []int32                          `thrift:"finance_type_list,4,optional" frugal:"4,optional,list<i32>" json:"finance_type_list,omitempty"`
	Reason             string                           `thrift:"reason,10" frugal:"10,default,string" json:"reason"`
	IPAddress          string                           `thrift:"ip_address,11" frugal:"11,default,string" json:"ip_address"`
	IsAutoWithdraw     *bool                            `thrift:"is_auto_withdraw,12,optional" frugal:"12,optional,bool" json:"is_auto_withdraw,omitempty"`
	RuleID             int64                            `thrift:"rule_id,20" frugal:"20,default,i64" json:"rule_id"`
	Params             string                           `thrift:"params,21" frugal:"21,default,string" json:"params"`
	SubsidyOutUID      *string                          `thrift:"subsidy_out_uid,22,optional" frugal:"22,optional,string" json:"subsidy_out_uid,omitempty"`
	SubsidyOutUIDType  *int32                           `thrift:"subsidy_out_uid_type,23,optional" frugal:"23,optional,i32" json:"subsidy_out_uid_type,omitempty"`
	SubsidyInfoList    []*fwe_trade_common.SubsidyInfo  `thrift:"subsidy_info_list,24,optional" frugal:"24,optional,list<fwe_trade_common.SubsidyInfo>" json:"subsidy_info_list,omitempty"`
	OmitInfraSubsidy   bool                             `thrift:"omit_infra_subsidy,25" frugal:"25,default,bool" json:"omit_infra_subsidy"`
	TotalSubsidyAmount *int64                           `thrift:"total_subsidy_amount,26,optional" frugal:"26,optional,i64" json:"total_subsidy_amount,omitempty"`
	CallbackAction     string                           `thrift:"callback_action,100" frugal:"100,default,string" json:"callback_action"`
	BizExtra           string                           `thrift:"biz_extra,101" frugal:"101,default,string" json:"biz_extra"`
	Tag                map[string]string                `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewSettleReq() *SettleReq {
	return &SettleReq{}
}

func (p *SettleReq) InitDefault() {
}

var SettleReq_SplitInfo_DEFAULT *fwe_trade_common.TradeSpiltInfo

func (p *SettleReq) GetSplitInfo() (v *fwe_trade_common.TradeSpiltInfo) {
	if !p.IsSetSplitInfo() {
		return SettleReq_SplitInfo_DEFAULT
	}
	return p.SplitInfo
}

func (p *SettleReq) GetSettleType() (v int32) {
	return p.SettleType
}

var SettleReq_FinanceIDList_DEFAULT []string

func (p *SettleReq) GetFinanceIDList() (v []string) {
	if !p.IsSetFinanceIDList() {
		return SettleReq_FinanceIDList_DEFAULT
	}
	return p.FinanceIDList
}

var SettleReq_FinanceTypeList_DEFAULT []int32

func (p *SettleReq) GetFinanceTypeList() (v []int32) {
	if !p.IsSetFinanceTypeList() {
		return SettleReq_FinanceTypeList_DEFAULT
	}
	return p.FinanceTypeList
}

func (p *SettleReq) GetReason() (v string) {
	return p.Reason
}

func (p *SettleReq) GetIPAddress() (v string) {
	return p.IPAddress
}

var SettleReq_IsAutoWithdraw_DEFAULT bool

func (p *SettleReq) GetIsAutoWithdraw() (v bool) {
	if !p.IsSetIsAutoWithdraw() {
		return SettleReq_IsAutoWithdraw_DEFAULT
	}
	return *p.IsAutoWithdraw
}

func (p *SettleReq) GetRuleID() (v int64) {
	return p.RuleID
}

func (p *SettleReq) GetParams() (v string) {
	return p.Params
}

var SettleReq_SubsidyOutUID_DEFAULT string

func (p *SettleReq) GetSubsidyOutUID() (v string) {
	if !p.IsSetSubsidyOutUID() {
		return SettleReq_SubsidyOutUID_DEFAULT
	}
	return *p.SubsidyOutUID
}

var SettleReq_SubsidyOutUIDType_DEFAULT int32

func (p *SettleReq) GetSubsidyOutUIDType() (v int32) {
	if !p.IsSetSubsidyOutUIDType() {
		return SettleReq_SubsidyOutUIDType_DEFAULT
	}
	return *p.SubsidyOutUIDType
}

var SettleReq_SubsidyInfoList_DEFAULT []*fwe_trade_common.SubsidyInfo

func (p *SettleReq) GetSubsidyInfoList() (v []*fwe_trade_common.SubsidyInfo) {
	if !p.IsSetSubsidyInfoList() {
		return SettleReq_SubsidyInfoList_DEFAULT
	}
	return p.SubsidyInfoList
}

func (p *SettleReq) GetOmitInfraSubsidy() (v bool) {
	return p.OmitInfraSubsidy
}

var SettleReq_TotalSubsidyAmount_DEFAULT int64

func (p *SettleReq) GetTotalSubsidyAmount() (v int64) {
	if !p.IsSetTotalSubsidyAmount() {
		return SettleReq_TotalSubsidyAmount_DEFAULT
	}
	return *p.TotalSubsidyAmount
}

func (p *SettleReq) GetCallbackAction() (v string) {
	return p.CallbackAction
}

func (p *SettleReq) GetBizExtra() (v string) {
	return p.BizExtra
}

func (p *SettleReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *SettleReq) SetSplitInfo(val *fwe_trade_common.TradeSpiltInfo) {
	p.SplitInfo = val
}
func (p *SettleReq) SetSettleType(val int32) {
	p.SettleType = val
}
func (p *SettleReq) SetFinanceIDList(val []string) {
	p.FinanceIDList = val
}
func (p *SettleReq) SetFinanceTypeList(val []int32) {
	p.FinanceTypeList = val
}
func (p *SettleReq) SetReason(val string) {
	p.Reason = val
}
func (p *SettleReq) SetIPAddress(val string) {
	p.IPAddress = val
}
func (p *SettleReq) SetIsAutoWithdraw(val *bool) {
	p.IsAutoWithdraw = val
}
func (p *SettleReq) SetRuleID(val int64) {
	p.RuleID = val
}
func (p *SettleReq) SetParams(val string) {
	p.Params = val
}
func (p *SettleReq) SetSubsidyOutUID(val *string) {
	p.SubsidyOutUID = val
}
func (p *SettleReq) SetSubsidyOutUIDType(val *int32) {
	p.SubsidyOutUIDType = val
}
func (p *SettleReq) SetSubsidyInfoList(val []*fwe_trade_common.SubsidyInfo) {
	p.SubsidyInfoList = val
}
func (p *SettleReq) SetOmitInfraSubsidy(val bool) {
	p.OmitInfraSubsidy = val
}
func (p *SettleReq) SetTotalSubsidyAmount(val *int64) {
	p.TotalSubsidyAmount = val
}
func (p *SettleReq) SetCallbackAction(val string) {
	p.CallbackAction = val
}
func (p *SettleReq) SetBizExtra(val string) {
	p.BizExtra = val
}
func (p *SettleReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_SettleReq = map[int16]string{
	1:   "split_info",
	2:   "settle_type",
	3:   "finance_id_list",
	4:   "finance_type_list",
	10:  "reason",
	11:  "ip_address",
	12:  "is_auto_withdraw",
	20:  "rule_id",
	21:  "params",
	22:  "subsidy_out_uid",
	23:  "subsidy_out_uid_type",
	24:  "subsidy_info_list",
	25:  "omit_infra_subsidy",
	26:  "total_subsidy_amount",
	100: "callback_action",
	101: "biz_extra",
	200: "tag",
}

func (p *SettleReq) IsSetSplitInfo() bool {
	return p.SplitInfo != nil
}

func (p *SettleReq) IsSetFinanceIDList() bool {
	return p.FinanceIDList != nil
}

func (p *SettleReq) IsSetFinanceTypeList() bool {
	return p.FinanceTypeList != nil
}

func (p *SettleReq) IsSetIsAutoWithdraw() bool {
	return p.IsAutoWithdraw != nil
}

func (p *SettleReq) IsSetSubsidyOutUID() bool {
	return p.SubsidyOutUID != nil
}

func (p *SettleReq) IsSetSubsidyOutUIDType() bool {
	return p.SubsidyOutUIDType != nil
}

func (p *SettleReq) IsSetSubsidyInfoList() bool {
	return p.SubsidyInfoList != nil
}

func (p *SettleReq) IsSetTotalSubsidyAmount() bool {
	return p.TotalSubsidyAmount != nil
}

func (p *SettleReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("SettleReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 24:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField24(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 25:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField25(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 26:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField26(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SettleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SettleReq) ReadField1(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewTradeSpiltInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SplitInfo = _field
	return nil
}
func (p *SettleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SettleType = _field
	return nil
}
func (p *SettleReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FinanceIDList = _field
	return nil
}
func (p *SettleReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int32, 0, size)
	for i := 0; i < size; i++ {

		var _elem int32
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FinanceTypeList = _field
	return nil
}
func (p *SettleReq) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Reason = _field
	return nil
}
func (p *SettleReq) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IPAddress = _field
	return nil
}
func (p *SettleReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsAutoWithdraw = _field
	return nil
}
func (p *SettleReq) ReadField20(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleID = _field
	return nil
}
func (p *SettleReq) ReadField21(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Params = _field
	return nil
}
func (p *SettleReq) ReadField22(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SubsidyOutUID = _field
	return nil
}
func (p *SettleReq) ReadField23(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SubsidyOutUIDType = _field
	return nil
}
func (p *SettleReq) ReadField24(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.SubsidyInfo, 0, size)
	values := make([]fwe_trade_common.SubsidyInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SubsidyInfoList = _field
	return nil
}
func (p *SettleReq) ReadField25(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OmitInfraSubsidy = _field
	return nil
}
func (p *SettleReq) ReadField26(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TotalSubsidyAmount = _field
	return nil
}
func (p *SettleReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackAction = _field
	return nil
}
func (p *SettleReq) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizExtra = _field
	return nil
}
func (p *SettleReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *SettleReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("SettleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SettleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
		if err = p.writeField24(oprot); err != nil {
			fieldId = 24
			goto WriteFieldError
		}
		if err = p.writeField25(oprot); err != nil {
			fieldId = 25
			goto WriteFieldError
		}
		if err = p.writeField26(oprot); err != nil {
			fieldId = 26
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SettleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("split_info", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SplitInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SettleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("settle_type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.SettleType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SettleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFinanceIDList() {
		if err = oprot.WriteFieldBegin("finance_id_list", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.FinanceIDList)); err != nil {
			return err
		}
		for _, v := range p.FinanceIDList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SettleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFinanceTypeList() {
		if err = oprot.WriteFieldBegin("finance_type_list", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.FinanceTypeList)); err != nil {
			return err
		}
		for _, v := range p.FinanceTypeList {
			if err := oprot.WriteI32(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *SettleReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("reason", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Reason); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *SettleReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ip_address", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IPAddress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *SettleReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsAutoWithdraw() {
		if err = oprot.WriteFieldBegin("is_auto_withdraw", thrift.BOOL, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsAutoWithdraw); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *SettleReq) writeField20(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("rule_id", thrift.I64, 20); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RuleID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}
func (p *SettleReq) writeField21(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("params", thrift.STRING, 21); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Params); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}
func (p *SettleReq) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubsidyOutUID() {
		if err = oprot.WriteFieldBegin("subsidy_out_uid", thrift.STRING, 22); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SubsidyOutUID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}
func (p *SettleReq) writeField23(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubsidyOutUIDType() {
		if err = oprot.WriteFieldBegin("subsidy_out_uid_type", thrift.I32, 23); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.SubsidyOutUIDType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}
func (p *SettleReq) writeField24(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubsidyInfoList() {
		if err = oprot.WriteFieldBegin("subsidy_info_list", thrift.LIST, 24); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SubsidyInfoList)); err != nil {
			return err
		}
		for _, v := range p.SubsidyInfoList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 end error: ", p), err)
}
func (p *SettleReq) writeField25(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("omit_infra_subsidy", thrift.BOOL, 25); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.OmitInfraSubsidy); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 end error: ", p), err)
}
func (p *SettleReq) writeField26(oprot thrift.TProtocol) (err error) {
	if p.IsSetTotalSubsidyAmount() {
		if err = oprot.WriteFieldBegin("total_subsidy_amount", thrift.I64, 26); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.TotalSubsidyAmount); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 26 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 26 end error: ", p), err)
}
func (p *SettleReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_action", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *SettleReq) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_extra", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BizExtra); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *SettleReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *SettleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SettleReq(%+v)", *p)

}

type SettleRsp struct {
	MergeSettleNo string `thrift:"merge_settle_no,1" frugal:"1,default,string" json:"merge_settle_no"`
}

func NewSettleRsp() *SettleRsp {
	return &SettleRsp{}
}

func (p *SettleRsp) InitDefault() {
}

func (p *SettleRsp) GetMergeSettleNo() (v string) {
	return p.MergeSettleNo
}
func (p *SettleRsp) SetMergeSettleNo(val string) {
	p.MergeSettleNo = val
}

var fieldIDToName_SettleRsp = map[int16]string{
	1: "merge_settle_no",
}

func (p *SettleRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("SettleRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SettleRsp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SettleRsp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MergeSettleNo = _field
	return nil
}

func (p *SettleRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("SettleRsp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SettleRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SettleRsp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merge_settle_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MergeSettleNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SettleRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SettleRsp(%+v)", *p)

}

type ConfirmLoanData struct {
	FweAccountID string                     `thrift:"fwe_account_id,1" frugal:"1,default,string" json:"fwe_account_id"`
	ShopID       string                     `thrift:"shop_id,2" frugal:"2,default,string" json:"shop_id"`
	ShopName     string                     `thrift:"shop_name,3" frugal:"3,default,string" json:"shop_name"`
	CarVin       string                     `thrift:"car_vin,4" frugal:"4,default,string" json:"car_vin"`
	BorrowerName string                     `thrift:"borrower_name,5" frugal:"5,default,string" json:"borrower_name"`
	Amount       int64                      `thrift:"amount,6" frugal:"6,default,i64" json:"amount"`
	FinanceName  string                     `thrift:"finance_name,7" frugal:"7,default,string" json:"finance_name"`
	OutBankInfo  *fwe_trade_common.BankInfo `thrift:"out_bank_info,8" frugal:"8,default,fwe_trade_common.BankInfo" json:"out_bank_info"`
	BizOrderID   string                     `thrift:"biz_order_id,9" frugal:"9,default,string" json:"biz_order_id"`
}

func NewConfirmLoanData() *ConfirmLoanData {
	return &ConfirmLoanData{}
}

func (p *ConfirmLoanData) InitDefault() {
}

func (p *ConfirmLoanData) GetFweAccountID() (v string) {
	return p.FweAccountID
}

func (p *ConfirmLoanData) GetShopID() (v string) {
	return p.ShopID
}

func (p *ConfirmLoanData) GetShopName() (v string) {
	return p.ShopName
}

func (p *ConfirmLoanData) GetCarVin() (v string) {
	return p.CarVin
}

func (p *ConfirmLoanData) GetBorrowerName() (v string) {
	return p.BorrowerName
}

func (p *ConfirmLoanData) GetAmount() (v int64) {
	return p.Amount
}

func (p *ConfirmLoanData) GetFinanceName() (v string) {
	return p.FinanceName
}

var ConfirmLoanData_OutBankInfo_DEFAULT *fwe_trade_common.BankInfo

func (p *ConfirmLoanData) GetOutBankInfo() (v *fwe_trade_common.BankInfo) {
	if !p.IsSetOutBankInfo() {
		return ConfirmLoanData_OutBankInfo_DEFAULT
	}
	return p.OutBankInfo
}

func (p *ConfirmLoanData) GetBizOrderID() (v string) {
	return p.BizOrderID
}
func (p *ConfirmLoanData) SetFweAccountID(val string) {
	p.FweAccountID = val
}
func (p *ConfirmLoanData) SetShopID(val string) {
	p.ShopID = val
}
func (p *ConfirmLoanData) SetShopName(val string) {
	p.ShopName = val
}
func (p *ConfirmLoanData) SetCarVin(val string) {
	p.CarVin = val
}
func (p *ConfirmLoanData) SetBorrowerName(val string) {
	p.BorrowerName = val
}
func (p *ConfirmLoanData) SetAmount(val int64) {
	p.Amount = val
}
func (p *ConfirmLoanData) SetFinanceName(val string) {
	p.FinanceName = val
}
func (p *ConfirmLoanData) SetOutBankInfo(val *fwe_trade_common.BankInfo) {
	p.OutBankInfo = val
}
func (p *ConfirmLoanData) SetBizOrderID(val string) {
	p.BizOrderID = val
}

var fieldIDToName_ConfirmLoanData = map[int16]string{
	1: "fwe_account_id",
	2: "shop_id",
	3: "shop_name",
	4: "car_vin",
	5: "borrower_name",
	6: "amount",
	7: "finance_name",
	8: "out_bank_info",
	9: "biz_order_id",
}

func (p *ConfirmLoanData) IsSetOutBankInfo() bool {
	return p.OutBankInfo != nil
}

func (p *ConfirmLoanData) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ConfirmLoanData")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConfirmLoanData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConfirmLoanData) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FweAccountID = _field
	return nil
}
func (p *ConfirmLoanData) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ShopID = _field
	return nil
}
func (p *ConfirmLoanData) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ShopName = _field
	return nil
}
func (p *ConfirmLoanData) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CarVin = _field
	return nil
}
func (p *ConfirmLoanData) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BorrowerName = _field
	return nil
}
func (p *ConfirmLoanData) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *ConfirmLoanData) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceName = _field
	return nil
}
func (p *ConfirmLoanData) ReadField8(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewBankInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OutBankInfo = _field
	return nil
}
func (p *ConfirmLoanData) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizOrderID = _field
	return nil
}

func (p *ConfirmLoanData) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ConfirmLoanData")

	var fieldId int16
	if err = oprot.WriteStructBegin("ConfirmLoanData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConfirmLoanData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fwe_account_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FweAccountID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ConfirmLoanData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("shop_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ShopID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ConfirmLoanData) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("shop_name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ShopName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ConfirmLoanData) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("car_vin", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CarVin); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ConfirmLoanData) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("borrower_name", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BorrowerName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ConfirmLoanData) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ConfirmLoanData) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_name", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FinanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ConfirmLoanData) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("out_bank_info", thrift.STRUCT, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.OutBankInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *ConfirmLoanData) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_order_id", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BizOrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *ConfirmLoanData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConfirmLoanData(%+v)", *p)

}

type ConfirmLoanReq struct {
	ConfirmLoanData    *ConfirmLoanData  `thrift:"confirm_loan_data,1" frugal:"1,default,ConfirmLoanData" json:"confirm_loan_data"`
	FireCondition      *string           `thrift:"fire_condition,50,optional" frugal:"50,optional,string" json:"fire_condition,omitempty"`
	SuccCallbackAction string            `thrift:"succ_callback_action,100" frugal:"100,default,string" json:"succ_callback_action"`
	FailCallbackAction string            `thrift:"fail_callback_action,101" frugal:"101,default,string" json:"fail_callback_action"`
	Tag                map[string]string `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewConfirmLoanReq() *ConfirmLoanReq {
	return &ConfirmLoanReq{}
}

func (p *ConfirmLoanReq) InitDefault() {
}

var ConfirmLoanReq_ConfirmLoanData_DEFAULT *ConfirmLoanData

func (p *ConfirmLoanReq) GetConfirmLoanData() (v *ConfirmLoanData) {
	if !p.IsSetConfirmLoanData() {
		return ConfirmLoanReq_ConfirmLoanData_DEFAULT
	}
	return p.ConfirmLoanData
}

var ConfirmLoanReq_FireCondition_DEFAULT string

func (p *ConfirmLoanReq) GetFireCondition() (v string) {
	if !p.IsSetFireCondition() {
		return ConfirmLoanReq_FireCondition_DEFAULT
	}
	return *p.FireCondition
}

func (p *ConfirmLoanReq) GetSuccCallbackAction() (v string) {
	return p.SuccCallbackAction
}

func (p *ConfirmLoanReq) GetFailCallbackAction() (v string) {
	return p.FailCallbackAction
}

func (p *ConfirmLoanReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *ConfirmLoanReq) SetConfirmLoanData(val *ConfirmLoanData) {
	p.ConfirmLoanData = val
}
func (p *ConfirmLoanReq) SetFireCondition(val *string) {
	p.FireCondition = val
}
func (p *ConfirmLoanReq) SetSuccCallbackAction(val string) {
	p.SuccCallbackAction = val
}
func (p *ConfirmLoanReq) SetFailCallbackAction(val string) {
	p.FailCallbackAction = val
}
func (p *ConfirmLoanReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_ConfirmLoanReq = map[int16]string{
	1:   "confirm_loan_data",
	50:  "fire_condition",
	100: "succ_callback_action",
	101: "fail_callback_action",
	200: "tag",
}

func (p *ConfirmLoanReq) IsSetConfirmLoanData() bool {
	return p.ConfirmLoanData != nil
}

func (p *ConfirmLoanReq) IsSetFireCondition() bool {
	return p.FireCondition != nil
}

func (p *ConfirmLoanReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ConfirmLoanReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField50(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 101:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField101(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConfirmLoanReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConfirmLoanReq) ReadField1(iprot thrift.TProtocol) error {
	_field := NewConfirmLoanData()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ConfirmLoanData = _field
	return nil
}
func (p *ConfirmLoanReq) ReadField50(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FireCondition = _field
	return nil
}
func (p *ConfirmLoanReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SuccCallbackAction = _field
	return nil
}
func (p *ConfirmLoanReq) ReadField101(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FailCallbackAction = _field
	return nil
}
func (p *ConfirmLoanReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *ConfirmLoanReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ConfirmLoanReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ConfirmLoanReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField50(oprot); err != nil {
			fieldId = 50
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField101(oprot); err != nil {
			fieldId = 101
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConfirmLoanReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("confirm_loan_data", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.ConfirmLoanData.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ConfirmLoanReq) writeField50(oprot thrift.TProtocol) (err error) {
	if p.IsSetFireCondition() {
		if err = oprot.WriteFieldBegin("fire_condition", thrift.STRING, 50); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FireCondition); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 end error: ", p), err)
}
func (p *ConfirmLoanReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("succ_callback_action", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SuccCallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *ConfirmLoanReq) writeField101(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fail_callback_action", thrift.STRING, 101); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FailCallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 101 end error: ", p), err)
}
func (p *ConfirmLoanReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *ConfirmLoanReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConfirmLoanReq(%+v)", *p)

}

type ConfirmLoanRsp struct {
}

func NewConfirmLoanRsp() *ConfirmLoanRsp {
	return &ConfirmLoanRsp{}
}

func (p *ConfirmLoanRsp) InitDefault() {
}

var fieldIDToName_ConfirmLoanRsp = map[int16]string{}

func (p *ConfirmLoanRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ConfirmLoanRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConfirmLoanRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ConfirmLoanRsp")

	if err = oprot.WriteStructBegin("ConfirmLoanRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConfirmLoanRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConfirmLoanRsp(%+v)", *p)

}

type AgreementPayReq struct {
	FireCondition  *string           `thrift:"fire_condition,50,optional" frugal:"50,optional,string" json:"fire_condition,omitempty"`
	CallbackAction string            `thrift:"callback_action,100" frugal:"100,default,string" json:"callback_action"`
	Tag            map[string]string `thrift:"tag,200" frugal:"200,default,map<string:string>" json:"tag"`
}

func NewAgreementPayReq() *AgreementPayReq {
	return &AgreementPayReq{}
}

func (p *AgreementPayReq) InitDefault() {
}

var AgreementPayReq_FireCondition_DEFAULT string

func (p *AgreementPayReq) GetFireCondition() (v string) {
	if !p.IsSetFireCondition() {
		return AgreementPayReq_FireCondition_DEFAULT
	}
	return *p.FireCondition
}

func (p *AgreementPayReq) GetCallbackAction() (v string) {
	return p.CallbackAction
}

func (p *AgreementPayReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *AgreementPayReq) SetFireCondition(val *string) {
	p.FireCondition = val
}
func (p *AgreementPayReq) SetCallbackAction(val string) {
	p.CallbackAction = val
}
func (p *AgreementPayReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_AgreementPayReq = map[int16]string{
	50:  "fire_condition",
	100: "callback_action",
	200: "tag",
}

func (p *AgreementPayReq) IsSetFireCondition() bool {
	return p.FireCondition != nil
}

func (p *AgreementPayReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AgreementPayReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 50:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField50(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AgreementPayReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AgreementPayReq) ReadField50(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FireCondition = _field
	return nil
}
func (p *AgreementPayReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CallbackAction = _field
	return nil
}
func (p *AgreementPayReq) ReadField200(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *AgreementPayReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AgreementPayReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AgreementPayReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField50(oprot); err != nil {
			fieldId = 50
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AgreementPayReq) writeField50(oprot thrift.TProtocol) (err error) {
	if p.IsSetFireCondition() {
		if err = oprot.WriteFieldBegin("fire_condition", thrift.STRING, 50); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FireCondition); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 end error: ", p), err)
}
func (p *AgreementPayReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("callback_action", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *AgreementPayReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *AgreementPayReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgreementPayReq(%+v)", *p)

}

type AgreementPayRsp struct {
}

func NewAgreementPayRsp() *AgreementPayRsp {
	return &AgreementPayRsp{}
}

func (p *AgreementPayRsp) InitDefault() {
}

var fieldIDToName_AgreementPayRsp = map[int16]string{}

func (p *AgreementPayRsp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AgreementPayRsp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AgreementPayRsp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AgreementPayRsp")

	if err = oprot.WriteStructBegin("AgreementPayRsp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AgreementPayRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgreementPayRsp(%+v)", *p)

}

type InvoiceReq struct {
	InvoiceBizScene    int32                   `thrift:"invoice_biz_scene,1,required" frugal:"1,required,i32" json:"invoice_biz_scene"`
	InvoiceMode        int32                   `thrift:"invoice_mode,2,required" frugal:"2,required,i32" json:"invoice_mode"`
	InvoiceType        core0.InvoiceType       `thrift:"invoice_type,3,required" frugal:"3,required,InvoiceType" json:"invoice_type"`
	InvoiceFuncType    core0.InvoiceFuncType   `thrift:"invoice_func_type,4,required" frugal:"4,required,InvoiceFuncType" json:"invoice_func_type"`
	RelatedOutID       string                  `thrift:"related_out_id,5,required" frugal:"5,required,string" json:"related_out_id"`
	SubjectID          string                  `thrift:"subject_id,11" frugal:"11,default,string" json:"subject_id"`
	CustomerInfo       *core0.InvoicePartyInfo `thrift:"customer_info,12" frugal:"12,default,core.InvoicePartyInfo" json:"customer_info"`
	SalesInfo          *core0.InvoicePartyInfo `thrift:"sales_info,13" frugal:"13,default,core.InvoicePartyInfo" json:"sales_info"`
	InvoiceItem        string                  `thrift:"invoice_item,21,required" frugal:"21,required,string" json:"invoice_item"`
	ActualInvoiceItem  string                  `thrift:"actual_invoice_item,22,required" frugal:"22,required,string" json:"actual_invoice_item"`
	Amount             int64                   `thrift:"amount,23,required" frugal:"23,required,i64" json:"amount"`
	IncludeTax         int32                   `thrift:"include_tax,24,required" frugal:"24,required,i32" json:"include_tax"`
	TaxRate            int64                   `thrift:"tax_rate,25,required" frugal:"25,required,i64" json:"tax_rate"`
	ApplyRemark        string                  `thrift:"apply_remark,26" frugal:"26,default,string" json:"apply_remark"`
	InvoiceRemark      string                  `thrift:"invoice_remark,27" frugal:"27,default,string" json:"invoice_remark"`
	InvoiceContent     string                  `thrift:"invoice_content,28" frugal:"28,default,string" json:"invoice_content"`
	ContactEmail       string                  `thrift:"contact_email,31" frugal:"31,default,string" json:"contact_email"`
	ContactNumber      string                  `thrift:"contact_number,32" frugal:"32,default,string" json:"contact_number"`
	Receiver           string                  `thrift:"receiver,33" frugal:"33,default,string" json:"receiver"`
	ReceiveAddress     string                  `thrift:"receive_address,34" frugal:"34,default,string" json:"receive_address"`
	BusinessExt1       *string                 `thrift:"business_ext_1,40,optional" frugal:"40,optional,string" json:"business_ext_1,omitempty"`
	BusinessTimeExt1   *int64                  `thrift:"business_time_ext_1,50,optional" frugal:"50,optional,i64" json:"business_time_ext_1,omitempty"`
	VehicleInfo        *core0.VehicleInfo      `thrift:"vehicle_info,60" frugal:"60,default,core.VehicleInfo" json:"vehicle_info"`
	Extra              string                  `thrift:"extra,100" frugal:"100,default,string" json:"extra"`
	OperatorID         string                  `thrift:"operator_id,200" frugal:"200,default,string" json:"operator_id"`
	OperatorName       string                  `thrift:"operator_name,201" frugal:"201,default,string" json:"operator_name"`
	SuccCallbackAction string                  `thrift:"succ_callback_action,210" frugal:"210,default,string" json:"succ_callback_action"`
	FailCallbackAction string                  `thrift:"fail_callback_action,211" frugal:"211,default,string" json:"fail_callback_action"`
	Tag                map[string]string       `thrift:"tag,212" frugal:"212,default,map<string:string>" json:"tag"`
}

func NewInvoiceReq() *InvoiceReq {
	return &InvoiceReq{}
}

func (p *InvoiceReq) InitDefault() {
}

func (p *InvoiceReq) GetInvoiceBizScene() (v int32) {
	return p.InvoiceBizScene
}

func (p *InvoiceReq) GetInvoiceMode() (v int32) {
	return p.InvoiceMode
}

func (p *InvoiceReq) GetInvoiceType() (v core0.InvoiceType) {
	return p.InvoiceType
}

func (p *InvoiceReq) GetInvoiceFuncType() (v core0.InvoiceFuncType) {
	return p.InvoiceFuncType
}

func (p *InvoiceReq) GetRelatedOutID() (v string) {
	return p.RelatedOutID
}

func (p *InvoiceReq) GetSubjectID() (v string) {
	return p.SubjectID
}

var InvoiceReq_CustomerInfo_DEFAULT *core0.InvoicePartyInfo

func (p *InvoiceReq) GetCustomerInfo() (v *core0.InvoicePartyInfo) {
	if !p.IsSetCustomerInfo() {
		return InvoiceReq_CustomerInfo_DEFAULT
	}
	return p.CustomerInfo
}

var InvoiceReq_SalesInfo_DEFAULT *core0.InvoicePartyInfo

func (p *InvoiceReq) GetSalesInfo() (v *core0.InvoicePartyInfo) {
	if !p.IsSetSalesInfo() {
		return InvoiceReq_SalesInfo_DEFAULT
	}
	return p.SalesInfo
}

func (p *InvoiceReq) GetInvoiceItem() (v string) {
	return p.InvoiceItem
}

func (p *InvoiceReq) GetActualInvoiceItem() (v string) {
	return p.ActualInvoiceItem
}

func (p *InvoiceReq) GetAmount() (v int64) {
	return p.Amount
}

func (p *InvoiceReq) GetIncludeTax() (v int32) {
	return p.IncludeTax
}

func (p *InvoiceReq) GetTaxRate() (v int64) {
	return p.TaxRate
}

func (p *InvoiceReq) GetApplyRemark() (v string) {
	return p.ApplyRemark
}

func (p *InvoiceReq) GetInvoiceRemark() (v string) {
	return p.InvoiceRemark
}

func (p *InvoiceReq) GetInvoiceContent() (v string) {
	return p.InvoiceContent
}

func (p *InvoiceReq) GetContactEmail() (v string) {
	return p.ContactEmail
}

func (p *InvoiceReq) GetContactNumber() (v string) {
	return p.ContactNumber
}

func (p *InvoiceReq) GetReceiver() (v string) {
	return p.Receiver
}

func (p *InvoiceReq) GetReceiveAddress() (v string) {
	return p.ReceiveAddress
}

var InvoiceReq_BusinessExt1_DEFAULT string

func (p *InvoiceReq) GetBusinessExt1() (v string) {
	if !p.IsSetBusinessExt1() {
		return InvoiceReq_BusinessExt1_DEFAULT
	}
	return *p.BusinessExt1
}

var InvoiceReq_BusinessTimeExt1_DEFAULT int64

func (p *InvoiceReq) GetBusinessTimeExt1() (v int64) {
	if !p.IsSetBusinessTimeExt1() {
		return InvoiceReq_BusinessTimeExt1_DEFAULT
	}
	return *p.BusinessTimeExt1
}

var InvoiceReq_VehicleInfo_DEFAULT *core0.VehicleInfo

func (p *InvoiceReq) GetVehicleInfo() (v *core0.VehicleInfo) {
	if !p.IsSetVehicleInfo() {
		return InvoiceReq_VehicleInfo_DEFAULT
	}
	return p.VehicleInfo
}

func (p *InvoiceReq) GetExtra() (v string) {
	return p.Extra
}

func (p *InvoiceReq) GetOperatorID() (v string) {
	return p.OperatorID
}

func (p *InvoiceReq) GetOperatorName() (v string) {
	return p.OperatorName
}

func (p *InvoiceReq) GetSuccCallbackAction() (v string) {
	return p.SuccCallbackAction
}

func (p *InvoiceReq) GetFailCallbackAction() (v string) {
	return p.FailCallbackAction
}

func (p *InvoiceReq) GetTag() (v map[string]string) {
	return p.Tag
}
func (p *InvoiceReq) SetInvoiceBizScene(val int32) {
	p.InvoiceBizScene = val
}
func (p *InvoiceReq) SetInvoiceMode(val int32) {
	p.InvoiceMode = val
}
func (p *InvoiceReq) SetInvoiceType(val core0.InvoiceType) {
	p.InvoiceType = val
}
func (p *InvoiceReq) SetInvoiceFuncType(val core0.InvoiceFuncType) {
	p.InvoiceFuncType = val
}
func (p *InvoiceReq) SetRelatedOutID(val string) {
	p.RelatedOutID = val
}
func (p *InvoiceReq) SetSubjectID(val string) {
	p.SubjectID = val
}
func (p *InvoiceReq) SetCustomerInfo(val *core0.InvoicePartyInfo) {
	p.CustomerInfo = val
}
func (p *InvoiceReq) SetSalesInfo(val *core0.InvoicePartyInfo) {
	p.SalesInfo = val
}
func (p *InvoiceReq) SetInvoiceItem(val string) {
	p.InvoiceItem = val
}
func (p *InvoiceReq) SetActualInvoiceItem(val string) {
	p.ActualInvoiceItem = val
}
func (p *InvoiceReq) SetAmount(val int64) {
	p.Amount = val
}
func (p *InvoiceReq) SetIncludeTax(val int32) {
	p.IncludeTax = val
}
func (p *InvoiceReq) SetTaxRate(val int64) {
	p.TaxRate = val
}
func (p *InvoiceReq) SetApplyRemark(val string) {
	p.ApplyRemark = val
}
func (p *InvoiceReq) SetInvoiceRemark(val string) {
	p.InvoiceRemark = val
}
func (p *InvoiceReq) SetInvoiceContent(val string) {
	p.InvoiceContent = val
}
func (p *InvoiceReq) SetContactEmail(val string) {
	p.ContactEmail = val
}
func (p *InvoiceReq) SetContactNumber(val string) {
	p.ContactNumber = val
}
func (p *InvoiceReq) SetReceiver(val string) {
	p.Receiver = val
}
func (p *InvoiceReq) SetReceiveAddress(val string) {
	p.ReceiveAddress = val
}
func (p *InvoiceReq) SetBusinessExt1(val *string) {
	p.BusinessExt1 = val
}
func (p *InvoiceReq) SetBusinessTimeExt1(val *int64) {
	p.BusinessTimeExt1 = val
}
func (p *InvoiceReq) SetVehicleInfo(val *core0.VehicleInfo) {
	p.VehicleInfo = val
}
func (p *InvoiceReq) SetExtra(val string) {
	p.Extra = val
}
func (p *InvoiceReq) SetOperatorID(val string) {
	p.OperatorID = val
}
func (p *InvoiceReq) SetOperatorName(val string) {
	p.OperatorName = val
}
func (p *InvoiceReq) SetSuccCallbackAction(val string) {
	p.SuccCallbackAction = val
}
func (p *InvoiceReq) SetFailCallbackAction(val string) {
	p.FailCallbackAction = val
}
func (p *InvoiceReq) SetTag(val map[string]string) {
	p.Tag = val
}

var fieldIDToName_InvoiceReq = map[int16]string{
	1:   "invoice_biz_scene",
	2:   "invoice_mode",
	3:   "invoice_type",
	4:   "invoice_func_type",
	5:   "related_out_id",
	11:  "subject_id",
	12:  "customer_info",
	13:  "sales_info",
	21:  "invoice_item",
	22:  "actual_invoice_item",
	23:  "amount",
	24:  "include_tax",
	25:  "tax_rate",
	26:  "apply_remark",
	27:  "invoice_remark",
	28:  "invoice_content",
	31:  "contact_email",
	32:  "contact_number",
	33:  "receiver",
	34:  "receive_address",
	40:  "business_ext_1",
	50:  "business_time_ext_1",
	60:  "vehicle_info",
	100: "extra",
	200: "operator_id",
	201: "operator_name",
	210: "succ_callback_action",
	211: "fail_callback_action",
	212: "tag",
}

func (p *InvoiceReq) IsSetCustomerInfo() bool {
	return p.CustomerInfo != nil
}

func (p *InvoiceReq) IsSetSalesInfo() bool {
	return p.SalesInfo != nil
}

func (p *InvoiceReq) IsSetBusinessExt1() bool {
	return p.BusinessExt1 != nil
}

func (p *InvoiceReq) IsSetBusinessTimeExt1() bool {
	return p.BusinessTimeExt1 != nil
}

func (p *InvoiceReq) IsSetVehicleInfo() bool {
	return p.VehicleInfo != nil
}

func (p *InvoiceReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("InvoiceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInvoiceBizScene bool = false
	var issetInvoiceMode bool = false
	var issetInvoiceType bool = false
	var issetInvoiceFuncType bool = false
	var issetRelatedOutID bool = false
	var issetInvoiceItem bool = false
	var issetActualInvoiceItem bool = false
	var issetAmount bool = false
	var issetIncludeTax bool = false
	var issetTaxRate bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInvoiceBizScene = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInvoiceMode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInvoiceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInvoiceFuncType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetRelatedOutID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
				issetInvoiceItem = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
				issetActualInvoiceItem = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
				issetAmount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField24(iprot); err != nil {
					goto ReadFieldError
				}
				issetIncludeTax = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 25:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField25(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaxRate = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField26(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField27(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField28(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField31(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField32(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField33(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField34(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField40(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 50:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField50(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 60:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField60(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 201:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField201(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 210:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField210(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 211:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField211(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 212:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField212(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInvoiceBizScene {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInvoiceMode {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInvoiceType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInvoiceFuncType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetRelatedOutID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetInvoiceItem {
		fieldId = 21
		goto RequiredFieldNotSetError
	}

	if !issetActualInvoiceItem {
		fieldId = 22
		goto RequiredFieldNotSetError
	}

	if !issetAmount {
		fieldId = 23
		goto RequiredFieldNotSetError
	}

	if !issetIncludeTax {
		fieldId = 24
		goto RequiredFieldNotSetError
	}

	if !issetTaxRate {
		fieldId = 25
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InvoiceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InvoiceReq[fieldId]))
}

func (p *InvoiceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InvoiceBizScene = _field
	return nil
}
func (p *InvoiceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InvoiceMode = _field
	return nil
}
func (p *InvoiceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field core0.InvoiceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = core0.InvoiceType(v)
	}
	p.InvoiceType = _field
	return nil
}
func (p *InvoiceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field core0.InvoiceFuncType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = core0.InvoiceFuncType(v)
	}
	p.InvoiceFuncType = _field
	return nil
}
func (p *InvoiceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RelatedOutID = _field
	return nil
}
func (p *InvoiceReq) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SubjectID = _field
	return nil
}
func (p *InvoiceReq) ReadField12(iprot thrift.TProtocol) error {
	_field := core0.NewInvoicePartyInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CustomerInfo = _field
	return nil
}
func (p *InvoiceReq) ReadField13(iprot thrift.TProtocol) error {
	_field := core0.NewInvoicePartyInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SalesInfo = _field
	return nil
}
func (p *InvoiceReq) ReadField21(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InvoiceItem = _field
	return nil
}
func (p *InvoiceReq) ReadField22(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ActualInvoiceItem = _field
	return nil
}
func (p *InvoiceReq) ReadField23(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *InvoiceReq) ReadField24(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IncludeTax = _field
	return nil
}
func (p *InvoiceReq) ReadField25(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaxRate = _field
	return nil
}
func (p *InvoiceReq) ReadField26(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ApplyRemark = _field
	return nil
}
func (p *InvoiceReq) ReadField27(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InvoiceRemark = _field
	return nil
}
func (p *InvoiceReq) ReadField28(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InvoiceContent = _field
	return nil
}
func (p *InvoiceReq) ReadField31(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContactEmail = _field
	return nil
}
func (p *InvoiceReq) ReadField32(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContactNumber = _field
	return nil
}
func (p *InvoiceReq) ReadField33(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Receiver = _field
	return nil
}
func (p *InvoiceReq) ReadField34(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ReceiveAddress = _field
	return nil
}
func (p *InvoiceReq) ReadField40(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BusinessExt1 = _field
	return nil
}
func (p *InvoiceReq) ReadField50(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BusinessTimeExt1 = _field
	return nil
}
func (p *InvoiceReq) ReadField60(iprot thrift.TProtocol) error {
	_field := core0.NewVehicleInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.VehicleInfo = _field
	return nil
}
func (p *InvoiceReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Extra = _field
	return nil
}
func (p *InvoiceReq) ReadField200(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OperatorID = _field
	return nil
}
func (p *InvoiceReq) ReadField201(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OperatorName = _field
	return nil
}
func (p *InvoiceReq) ReadField210(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SuccCallbackAction = _field
	return nil
}
func (p *InvoiceReq) ReadField211(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FailCallbackAction = _field
	return nil
}
func (p *InvoiceReq) ReadField212(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Tag = _field
	return nil
}

func (p *InvoiceReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("InvoiceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("InvoiceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
		if err = p.writeField24(oprot); err != nil {
			fieldId = 24
			goto WriteFieldError
		}
		if err = p.writeField25(oprot); err != nil {
			fieldId = 25
			goto WriteFieldError
		}
		if err = p.writeField26(oprot); err != nil {
			fieldId = 26
			goto WriteFieldError
		}
		if err = p.writeField27(oprot); err != nil {
			fieldId = 27
			goto WriteFieldError
		}
		if err = p.writeField28(oprot); err != nil {
			fieldId = 28
			goto WriteFieldError
		}
		if err = p.writeField31(oprot); err != nil {
			fieldId = 31
			goto WriteFieldError
		}
		if err = p.writeField32(oprot); err != nil {
			fieldId = 32
			goto WriteFieldError
		}
		if err = p.writeField33(oprot); err != nil {
			fieldId = 33
			goto WriteFieldError
		}
		if err = p.writeField34(oprot); err != nil {
			fieldId = 34
			goto WriteFieldError
		}
		if err = p.writeField40(oprot); err != nil {
			fieldId = 40
			goto WriteFieldError
		}
		if err = p.writeField50(oprot); err != nil {
			fieldId = 50
			goto WriteFieldError
		}
		if err = p.writeField60(oprot); err != nil {
			fieldId = 60
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
		if err = p.writeField201(oprot); err != nil {
			fieldId = 201
			goto WriteFieldError
		}
		if err = p.writeField210(oprot); err != nil {
			fieldId = 210
			goto WriteFieldError
		}
		if err = p.writeField211(oprot); err != nil {
			fieldId = 211
			goto WriteFieldError
		}
		if err = p.writeField212(oprot); err != nil {
			fieldId = 212
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InvoiceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("invoice_biz_scene", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.InvoiceBizScene); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *InvoiceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("invoice_mode", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.InvoiceMode); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *InvoiceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("invoice_type", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InvoiceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *InvoiceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("invoice_func_type", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InvoiceFuncType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *InvoiceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("related_out_id", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RelatedOutID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *InvoiceReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("subject_id", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SubjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *InvoiceReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("customer_info", thrift.STRUCT, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CustomerInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *InvoiceReq) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sales_info", thrift.STRUCT, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.SalesInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *InvoiceReq) writeField21(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("invoice_item", thrift.STRING, 21); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InvoiceItem); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}
func (p *InvoiceReq) writeField22(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("actual_invoice_item", thrift.STRING, 22); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ActualInvoiceItem); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}
func (p *InvoiceReq) writeField23(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 23); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}
func (p *InvoiceReq) writeField24(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("include_tax", thrift.I32, 24); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.IncludeTax); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 end error: ", p), err)
}
func (p *InvoiceReq) writeField25(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tax_rate", thrift.I64, 25); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TaxRate); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 end error: ", p), err)
}
func (p *InvoiceReq) writeField26(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("apply_remark", thrift.STRING, 26); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ApplyRemark); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 26 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 26 end error: ", p), err)
}
func (p *InvoiceReq) writeField27(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("invoice_remark", thrift.STRING, 27); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InvoiceRemark); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 27 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 27 end error: ", p), err)
}
func (p *InvoiceReq) writeField28(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("invoice_content", thrift.STRING, 28); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InvoiceContent); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 28 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 28 end error: ", p), err)
}
func (p *InvoiceReq) writeField31(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("contact_email", thrift.STRING, 31); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ContactEmail); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 31 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 31 end error: ", p), err)
}
func (p *InvoiceReq) writeField32(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("contact_number", thrift.STRING, 32); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ContactNumber); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 32 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 32 end error: ", p), err)
}
func (p *InvoiceReq) writeField33(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("receiver", thrift.STRING, 33); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Receiver); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 33 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 33 end error: ", p), err)
}
func (p *InvoiceReq) writeField34(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("receive_address", thrift.STRING, 34); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ReceiveAddress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 34 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 34 end error: ", p), err)
}
func (p *InvoiceReq) writeField40(oprot thrift.TProtocol) (err error) {
	if p.IsSetBusinessExt1() {
		if err = oprot.WriteFieldBegin("business_ext_1", thrift.STRING, 40); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.BusinessExt1); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 40 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 40 end error: ", p), err)
}
func (p *InvoiceReq) writeField50(oprot thrift.TProtocol) (err error) {
	if p.IsSetBusinessTimeExt1() {
		if err = oprot.WriteFieldBegin("business_time_ext_1", thrift.I64, 50); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.BusinessTimeExt1); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 50 end error: ", p), err)
}
func (p *InvoiceReq) writeField60(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("vehicle_info", thrift.STRUCT, 60); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.VehicleInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 60 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 60 end error: ", p), err)
}
func (p *InvoiceReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("extra", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Extra); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}
func (p *InvoiceReq) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator_id", thrift.STRING, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OperatorID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}
func (p *InvoiceReq) writeField201(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator_name", thrift.STRING, 201); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OperatorName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 201 end error: ", p), err)
}
func (p *InvoiceReq) writeField210(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("succ_callback_action", thrift.STRING, 210); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SuccCallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 210 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 210 end error: ", p), err)
}
func (p *InvoiceReq) writeField211(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fail_callback_action", thrift.STRING, 211); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.FailCallbackAction); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 211 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 211 end error: ", p), err)
}
func (p *InvoiceReq) writeField212(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("tag", thrift.MAP, 212); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Tag)); err != nil {
		return err
	}
	for k, v := range p.Tag {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 212 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 212 end error: ", p), err)
}

func (p *InvoiceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InvoiceReq(%+v)", *p)

}

type InvoiceResp struct {
	InvoiceOrderID string `thrift:"invoice_order_id,1" frugal:"1,default,string" json:"invoice_order_id"`
}

func NewInvoiceResp() *InvoiceResp {
	return &InvoiceResp{}
}

func (p *InvoiceResp) InitDefault() {
}

func (p *InvoiceResp) GetInvoiceOrderID() (v string) {
	return p.InvoiceOrderID
}
func (p *InvoiceResp) SetInvoiceOrderID(val string) {
	p.InvoiceOrderID = val
}

var fieldIDToName_InvoiceResp = map[int16]string{
	1: "invoice_order_id",
}

func (p *InvoiceResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("InvoiceResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InvoiceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InvoiceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InvoiceOrderID = _field
	return nil
}

func (p *InvoiceResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("InvoiceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("InvoiceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InvoiceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("invoice_order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InvoiceOrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InvoiceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InvoiceResp(%+v)", *p)

}

type AfterSaleStartReq struct {
	NeedAfterSaleCheck bool `thrift:"need_after_sale_check,1" frugal:"1,default,bool" json:"need_after_sale_check"`
}

func NewAfterSaleStartReq() *AfterSaleStartReq {
	return &AfterSaleStartReq{}
}

func (p *AfterSaleStartReq) InitDefault() {
}

func (p *AfterSaleStartReq) GetNeedAfterSaleCheck() (v bool) {
	return p.NeedAfterSaleCheck
}
func (p *AfterSaleStartReq) SetNeedAfterSaleCheck(val bool) {
	p.NeedAfterSaleCheck = val
}

var fieldIDToName_AfterSaleStartReq = map[int16]string{
	1: "need_after_sale_check",
}

func (p *AfterSaleStartReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AfterSaleStartReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AfterSaleStartReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AfterSaleStartReq) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NeedAfterSaleCheck = _field
	return nil
}

func (p *AfterSaleStartReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AfterSaleStartReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AfterSaleStartReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AfterSaleStartReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("need_after_sale_check", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.NeedAfterSaleCheck); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AfterSaleStartReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AfterSaleStartReq(%+v)", *p)

}

type TransferReq struct {
	MerchantID       string                        `thrift:"merchant_id,1,required" frugal:"1,required,string" json:"merchant_id"`
	AppID            string                        `thrift:"app_id,2,required" frugal:"2,required,string" json:"app_id"`
	MerchantName     string                        `thrift:"merchant_name,3,required" frugal:"3,required,string" json:"merchant_name"`
	FcType           *string                       `thrift:"FcType,4,optional" frugal:"4,optional,string" json:"FcType,omitempty"`
	FcSceneCode      *int64                        `thrift:"FcSceneCode,5,optional" frugal:"5,optional,i64" json:"FcSceneCode,omitempty"`
	FinanceOrderType int32                         `thrift:"finance_order_type,6,required" frugal:"6,required,i32" json:"finance_order_type"`
	UID              string                        `thrift:"uid,7,required" frugal:"7,required,string" json:"uid"`
	UIDType          int64                         `thrift:"uid_type,8,required" frugal:"8,required,i64" json:"uid_type"`
	TradeTime        int64                         `thrift:"trade_time,9,required" frugal:"9,required,i64" json:"trade_time"`
	PayerInfo        *fwe_trade_common.Participant `thrift:"payerInfo,10,required" frugal:"10,required,fwe_trade_common.Participant" json:"payerInfo"`
	PayeeInfo        *fwe_trade_common.Participant `thrift:"payeeInfo,11,required" frugal:"11,required,fwe_trade_common.Participant" json:"payeeInfo"`
	Amount           int64                         `thrift:"amount,12" frugal:"12,default,i64" json:"amount"`
	TransferName     string                        `thrift:"transfer_name,13" frugal:"13,default,string" json:"transfer_name"`
	TransferDesc     string                        `thrift:"transfer_desc,14" frugal:"14,default,string" json:"transfer_desc"`
}

func NewTransferReq() *TransferReq {
	return &TransferReq{}
}

func (p *TransferReq) InitDefault() {
}

func (p *TransferReq) GetMerchantID() (v string) {
	return p.MerchantID
}

func (p *TransferReq) GetAppID() (v string) {
	return p.AppID
}

func (p *TransferReq) GetMerchantName() (v string) {
	return p.MerchantName
}

var TransferReq_FcType_DEFAULT string

func (p *TransferReq) GetFcType() (v string) {
	if !p.IsSetFcType() {
		return TransferReq_FcType_DEFAULT
	}
	return *p.FcType
}

var TransferReq_FcSceneCode_DEFAULT int64

func (p *TransferReq) GetFcSceneCode() (v int64) {
	if !p.IsSetFcSceneCode() {
		return TransferReq_FcSceneCode_DEFAULT
	}
	return *p.FcSceneCode
}

func (p *TransferReq) GetFinanceOrderType() (v int32) {
	return p.FinanceOrderType
}

func (p *TransferReq) GetUID() (v string) {
	return p.UID
}

func (p *TransferReq) GetUIDType() (v int64) {
	return p.UIDType
}

func (p *TransferReq) GetTradeTime() (v int64) {
	return p.TradeTime
}

var TransferReq_PayerInfo_DEFAULT *fwe_trade_common.Participant

func (p *TransferReq) GetPayerInfo() (v *fwe_trade_common.Participant) {
	if !p.IsSetPayerInfo() {
		return TransferReq_PayerInfo_DEFAULT
	}
	return p.PayerInfo
}

var TransferReq_PayeeInfo_DEFAULT *fwe_trade_common.Participant

func (p *TransferReq) GetPayeeInfo() (v *fwe_trade_common.Participant) {
	if !p.IsSetPayeeInfo() {
		return TransferReq_PayeeInfo_DEFAULT
	}
	return p.PayeeInfo
}

func (p *TransferReq) GetAmount() (v int64) {
	return p.Amount
}

func (p *TransferReq) GetTransferName() (v string) {
	return p.TransferName
}

func (p *TransferReq) GetTransferDesc() (v string) {
	return p.TransferDesc
}
func (p *TransferReq) SetMerchantID(val string) {
	p.MerchantID = val
}
func (p *TransferReq) SetAppID(val string) {
	p.AppID = val
}
func (p *TransferReq) SetMerchantName(val string) {
	p.MerchantName = val
}
func (p *TransferReq) SetFcType(val *string) {
	p.FcType = val
}
func (p *TransferReq) SetFcSceneCode(val *int64) {
	p.FcSceneCode = val
}
func (p *TransferReq) SetFinanceOrderType(val int32) {
	p.FinanceOrderType = val
}
func (p *TransferReq) SetUID(val string) {
	p.UID = val
}
func (p *TransferReq) SetUIDType(val int64) {
	p.UIDType = val
}
func (p *TransferReq) SetTradeTime(val int64) {
	p.TradeTime = val
}
func (p *TransferReq) SetPayerInfo(val *fwe_trade_common.Participant) {
	p.PayerInfo = val
}
func (p *TransferReq) SetPayeeInfo(val *fwe_trade_common.Participant) {
	p.PayeeInfo = val
}
func (p *TransferReq) SetAmount(val int64) {
	p.Amount = val
}
func (p *TransferReq) SetTransferName(val string) {
	p.TransferName = val
}
func (p *TransferReq) SetTransferDesc(val string) {
	p.TransferDesc = val
}

var fieldIDToName_TransferReq = map[int16]string{
	1:  "merchant_id",
	2:  "app_id",
	3:  "merchant_name",
	4:  "FcType",
	5:  "FcSceneCode",
	6:  "finance_order_type",
	7:  "uid",
	8:  "uid_type",
	9:  "trade_time",
	10: "payerInfo",
	11: "payeeInfo",
	12: "amount",
	13: "transfer_name",
	14: "transfer_desc",
}

func (p *TransferReq) IsSetFcType() bool {
	return p.FcType != nil
}

func (p *TransferReq) IsSetFcSceneCode() bool {
	return p.FcSceneCode != nil
}

func (p *TransferReq) IsSetPayerInfo() bool {
	return p.PayerInfo != nil
}

func (p *TransferReq) IsSetPayeeInfo() bool {
	return p.PayeeInfo != nil
}

func (p *TransferReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TransferReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMerchantID bool = false
	var issetAppID bool = false
	var issetMerchantName bool = false
	var issetFinanceOrderType bool = false
	var issetUID bool = false
	var issetUIDType bool = false
	var issetTradeTime bool = false
	var issetPayerInfo bool = false
	var issetPayeeInfo bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMerchantID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAppID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMerchantName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetFinanceOrderType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetUID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetUIDType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetTradeTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetPayerInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetPayeeInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMerchantID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAppID {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMerchantName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetFinanceOrderType {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetUID {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetUIDType {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetTradeTime {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetPayerInfo {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetPayeeInfo {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TransferReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TransferReq[fieldId]))
}

func (p *TransferReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MerchantID = _field
	return nil
}
func (p *TransferReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppID = _field
	return nil
}
func (p *TransferReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MerchantName = _field
	return nil
}
func (p *TransferReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FcType = _field
	return nil
}
func (p *TransferReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FcSceneCode = _field
	return nil
}
func (p *TransferReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FinanceOrderType = _field
	return nil
}
func (p *TransferReq) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UID = _field
	return nil
}
func (p *TransferReq) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UIDType = _field
	return nil
}
func (p *TransferReq) ReadField9(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TradeTime = _field
	return nil
}
func (p *TransferReq) ReadField10(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewParticipant()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PayerInfo = _field
	return nil
}
func (p *TransferReq) ReadField11(iprot thrift.TProtocol) error {
	_field := fwe_trade_common.NewParticipant()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PayeeInfo = _field
	return nil
}
func (p *TransferReq) ReadField12(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Amount = _field
	return nil
}
func (p *TransferReq) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TransferName = _field
	return nil
}
func (p *TransferReq) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TransferDesc = _field
	return nil
}

func (p *TransferReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TransferReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("TransferReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TransferReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merchant_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MerchantID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TransferReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *TransferReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merchant_name", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MerchantName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *TransferReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFcType() {
		if err = oprot.WriteFieldBegin("FcType", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FcType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *TransferReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetFcSceneCode() {
		if err = oprot.WriteFieldBegin("FcSceneCode", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.FcSceneCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *TransferReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("finance_order_type", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FinanceOrderType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *TransferReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("uid", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *TransferReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("uid_type", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UIDType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *TransferReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("trade_time", thrift.I64, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TradeTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *TransferReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("payerInfo", thrift.STRUCT, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PayerInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *TransferReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("payeeInfo", thrift.STRUCT, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.PayeeInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *TransferReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("amount", thrift.I64, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Amount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *TransferReq) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("transfer_name", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TransferName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *TransferReq) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("transfer_desc", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TransferDesc); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *TransferReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TransferReq(%+v)", *p)

}

type TransferResp struct {
	TradeNo string `thrift:"trade_no,1" frugal:"1,default,string" json:"trade_no"`
}

func NewTransferResp() *TransferResp {
	return &TransferResp{}
}

func (p *TransferResp) InitDefault() {
}

func (p *TransferResp) GetTradeNo() (v string) {
	return p.TradeNo
}
func (p *TransferResp) SetTradeNo(val string) {
	p.TradeNo = val
}

var fieldIDToName_TransferResp = map[int16]string{
	1: "trade_no",
}

func (p *TransferResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TransferResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TransferResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TransferResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TradeNo = _field
	return nil
}

func (p *TransferResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TransferResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("TransferResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TransferResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("trade_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TradeNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TransferResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TransferResp(%+v)", *p)

}

type LifeVerifyReq struct {
	ItemOrderList []*fwe_trade_common.ItemOrder `thrift:"item_order_list,2" frugal:"2,default,list<fwe_trade_common.ItemOrder>" json:"item_order_list"`
	UseAll        bool                          `thrift:"use_all,3" frugal:"3,default,bool" json:"use_all"`
	PoiInfo       string                        `thrift:"poi_info,4" frugal:"4,default,string" json:"poi_info"`
	AppID         string                        `thrift:"app_id,100" frugal:"100,default,string" json:"app_id"`
}

func NewLifeVerifyReq() *LifeVerifyReq {
	return &LifeVerifyReq{}
}

func (p *LifeVerifyReq) InitDefault() {
}

func (p *LifeVerifyReq) GetItemOrderList() (v []*fwe_trade_common.ItemOrder) {
	return p.ItemOrderList
}

func (p *LifeVerifyReq) GetUseAll() (v bool) {
	return p.UseAll
}

func (p *LifeVerifyReq) GetPoiInfo() (v string) {
	return p.PoiInfo
}

func (p *LifeVerifyReq) GetAppID() (v string) {
	return p.AppID
}
func (p *LifeVerifyReq) SetItemOrderList(val []*fwe_trade_common.ItemOrder) {
	p.ItemOrderList = val
}
func (p *LifeVerifyReq) SetUseAll(val bool) {
	p.UseAll = val
}
func (p *LifeVerifyReq) SetPoiInfo(val string) {
	p.PoiInfo = val
}
func (p *LifeVerifyReq) SetAppID(val string) {
	p.AppID = val
}

var fieldIDToName_LifeVerifyReq = map[int16]string{
	2:   "item_order_list",
	3:   "use_all",
	4:   "poi_info",
	100: "app_id",
}

func (p *LifeVerifyReq) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeVerifyReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 100:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField100(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeVerifyReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LifeVerifyReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.ItemOrder, 0, size)
	values := make([]fwe_trade_common.ItemOrder, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ItemOrderList = _field
	return nil
}
func (p *LifeVerifyReq) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UseAll = _field
	return nil
}
func (p *LifeVerifyReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PoiInfo = _field
	return nil
}
func (p *LifeVerifyReq) ReadField100(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppID = _field
	return nil
}

func (p *LifeVerifyReq) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeVerifyReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("LifeVerifyReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField100(oprot); err != nil {
			fieldId = 100
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LifeVerifyReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("item_order_list", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ItemOrderList)); err != nil {
		return err
	}
	for _, v := range p.ItemOrderList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LifeVerifyReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("use_all", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.UseAll); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *LifeVerifyReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("poi_info", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PoiInfo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *LifeVerifyReq) writeField100(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_id", thrift.STRING, 100); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 100 end error: ", p), err)
}

func (p *LifeVerifyReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeVerifyReq(%+v)", *p)

}

type LifeVerifyResp struct {
	VerifyResults []*fwe_trade_common.VerifyResult_ `thrift:"verify_results,1" frugal:"1,default,list<fwe_trade_common.VerifyResult_>" json:"verify_results"`
}

func NewLifeVerifyResp() *LifeVerifyResp {
	return &LifeVerifyResp{}
}

func (p *LifeVerifyResp) InitDefault() {
}

func (p *LifeVerifyResp) GetVerifyResults() (v []*fwe_trade_common.VerifyResult_) {
	return p.VerifyResults
}
func (p *LifeVerifyResp) SetVerifyResults(val []*fwe_trade_common.VerifyResult_) {
	p.VerifyResults = val
}

var fieldIDToName_LifeVerifyResp = map[int16]string{
	1: "verify_results",
}

func (p *LifeVerifyResp) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeVerifyResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeVerifyResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LifeVerifyResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*fwe_trade_common.VerifyResult_, 0, size)
	values := make([]fwe_trade_common.VerifyResult_, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.VerifyResults = _field
	return nil
}

func (p *LifeVerifyResp) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeVerifyResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("LifeVerifyResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LifeVerifyResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("verify_results", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.VerifyResults)); err != nil {
		return err
	}
	for _, v := range p.VerifyResults {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *LifeVerifyResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeVerifyResp(%+v)", *p)

}
