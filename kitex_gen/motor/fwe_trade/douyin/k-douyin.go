// Code generated by Kitex v1.20.3. DO NOT EDIT.

package douyin

import (
	"bytes"
	"fmt"
	"reflect"
	"strings"

	"github.com/cloudwego/gopkg/protocol/thrift"
	kutils "github.com/cloudwego/kitex/pkg/utils"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = thrift.STOP
)

func (p *MarketingDetailInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MarketingDetailInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *MarketingDetailInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ID = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Type = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DiscountAmount = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Title = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Note = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DiscountRange = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Subtype = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Kind = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreatorType = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.MerchantFunderType = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FunderAccountID = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Code = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field *int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.Value = _field
	return offset, nil
}

func (p *MarketingDetailInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *MarketingDetailInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *MarketingDetailInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *MarketingDetailInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ID)
	return offset
}

func (p *MarketingDetailInfo) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Type)
	return offset
}

func (p *MarketingDetailInfo) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.DiscountAmount)
	return offset
}

func (p *MarketingDetailInfo) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Title)
	return offset
}

func (p *MarketingDetailInfo) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetNote() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Note)
	}
	return offset
}

func (p *MarketingDetailInfo) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.DiscountRange)
	return offset
}

func (p *MarketingDetailInfo) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSubtype() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Subtype)
	}
	return offset
}

func (p *MarketingDetailInfo) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 8)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Kind)
	return offset
}

func (p *MarketingDetailInfo) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 9)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CreatorType)
	return offset
}

func (p *MarketingDetailInfo) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 10)
	offset += thrift.Binary.WriteI64(buf[offset:], p.MerchantFunderType)
	return offset
}

func (p *MarketingDetailInfo) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 11)
	offset += thrift.Binary.WriteI64(buf[offset:], p.FunderAccountID)
	return offset
}

func (p *MarketingDetailInfo) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCode() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 12)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.Code)
	}
	return offset
}

func (p *MarketingDetailInfo) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetValue() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 13)
		offset += thrift.Binary.WriteI64(buf[offset:], *p.Value)
	}
	return offset
}

func (p *MarketingDetailInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ID)
	return l
}

func (p *MarketingDetailInfo) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *MarketingDetailInfo) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *MarketingDetailInfo) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Title)
	return l
}

func (p *MarketingDetailInfo) field5Length() int {
	l := 0
	if p.IsSetNote() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Note)
	}
	return l
}

func (p *MarketingDetailInfo) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *MarketingDetailInfo) field7Length() int {
	l := 0
	if p.IsSetSubtype() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Subtype)
	}
	return l
}

func (p *MarketingDetailInfo) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *MarketingDetailInfo) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *MarketingDetailInfo) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *MarketingDetailInfo) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *MarketingDetailInfo) field12Length() int {
	l := 0
	if p.IsSetCode() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.Code)
	}
	return l
}

func (p *MarketingDetailInfo) field13Length() int {
	l := 0
	if p.IsSetValue() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.I64Length()
	}
	return l
}

func (p *MarketingDetailInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*MarketingDetailInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ID != "" {
		p.ID = kutils.StringDeepCopy(src.ID)
	}

	p.Type = src.Type

	p.DiscountAmount = src.DiscountAmount

	if src.Title != "" {
		p.Title = kutils.StringDeepCopy(src.Title)
	}

	if src.Note != nil {
		var tmp string
		if *src.Note != "" {
			tmp = kutils.StringDeepCopy(*src.Note)
		}
		p.Note = &tmp
	}

	p.DiscountRange = src.DiscountRange

	if src.Subtype != nil {
		var tmp string
		if *src.Subtype != "" {
			tmp = kutils.StringDeepCopy(*src.Subtype)
		}
		p.Subtype = &tmp
	}

	p.Kind = src.Kind

	p.CreatorType = src.CreatorType

	p.MerchantFunderType = src.MerchantFunderType

	p.FunderAccountID = src.FunderAccountID

	if src.Code != nil {
		var tmp string
		if *src.Code != "" {
			tmp = kutils.StringDeepCopy(*src.Code)
		}
		p.Code = &tmp
	}

	if src.Value != nil {
		tmp := *src.Value
		p.Value = &tmp
	}

	return nil
}

func (p *OrderDiscountDetail) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderDiscountDetail[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *OrderDiscountDetail) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderTotalDiscountAmount = _field
	return offset, nil
}

func (p *OrderDiscountDetail) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.GoodsTotalDiscountAmount = _field
	return offset, nil
}

func (p *OrderDiscountDetail) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*MarketingDetailInfo, 0, size)
	values := make([]MarketingDetailInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.MarketingDetailInfo = _field
	return offset, nil
}

func (p *OrderDiscountDetail) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *OrderDiscountDetail) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *OrderDiscountDetail) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *OrderDiscountDetail) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.OrderTotalDiscountAmount)
	return offset
}

func (p *OrderDiscountDetail) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.GoodsTotalDiscountAmount)
	return offset
}

func (p *OrderDiscountDetail) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetMarketingDetailInfo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.MarketingDetailInfo {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *OrderDiscountDetail) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *OrderDiscountDetail) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *OrderDiscountDetail) field3Length() int {
	l := 0
	if p.IsSetMarketingDetailInfo() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.MarketingDetailInfo {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *OrderDiscountDetail) DeepCopy(s interface{}) error {
	src, ok := s.(*OrderDiscountDetail)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.OrderTotalDiscountAmount = src.OrderTotalDiscountAmount

	p.GoodsTotalDiscountAmount = src.GoodsTotalDiscountAmount

	if src.MarketingDetailInfo != nil {
		p.MarketingDetailInfo = make([]*MarketingDetailInfo, 0, len(src.MarketingDetailInfo))
		for _, elem := range src.MarketingDetailInfo {
			var _elem *MarketingDetailInfo
			if elem != nil {
				_elem = &MarketingDetailInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.MarketingDetailInfo = append(p.MarketingDetailInfo, _elem)
		}
	}

	return nil
}

func (p *GoodsDiscountDetail) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GoodsDiscountDetail[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *GoodsDiscountDetail) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.GoodsID = _field
	return offset, nil
}

func (p *GoodsDiscountDetail) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Quantity = _field
	return offset, nil
}

func (p *GoodsDiscountDetail) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalAmount = _field
	return offset, nil
}

func (p *GoodsDiscountDetail) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalDiscountAmount = _field
	return offset, nil
}

func (p *GoodsDiscountDetail) FastReadField5(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*MarketingDetailInfo, 0, size)
	values := make([]MarketingDetailInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.MarketingDetailInfo = _field
	return offset, nil
}

func (p *GoodsDiscountDetail) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.SkuID = _field
	return offset, nil
}

func (p *GoodsDiscountDetail) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GoodsDiscountDetail) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GoodsDiscountDetail) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GoodsDiscountDetail) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.GoodsID)
	return offset
}

func (p *GoodsDiscountDetail) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Quantity)
	return offset
}

func (p *GoodsDiscountDetail) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TotalAmount)
	return offset
}

func (p *GoodsDiscountDetail) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 4)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TotalDiscountAmount)
	return offset
}

func (p *GoodsDiscountDetail) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetMarketingDetailInfo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 5)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.MarketingDetailInfo {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *GoodsDiscountDetail) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSkuID() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.SkuID)
	}
	return offset
}

func (p *GoodsDiscountDetail) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.GoodsID)
	return l
}

func (p *GoodsDiscountDetail) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *GoodsDiscountDetail) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *GoodsDiscountDetail) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *GoodsDiscountDetail) field5Length() int {
	l := 0
	if p.IsSetMarketingDetailInfo() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.MarketingDetailInfo {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *GoodsDiscountDetail) field6Length() int {
	l := 0
	if p.IsSetSkuID() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.SkuID)
	}
	return l
}

func (p *GoodsDiscountDetail) DeepCopy(s interface{}) error {
	src, ok := s.(*GoodsDiscountDetail)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.GoodsID != "" {
		p.GoodsID = kutils.StringDeepCopy(src.GoodsID)
	}

	p.Quantity = src.Quantity

	p.TotalAmount = src.TotalAmount

	p.TotalDiscountAmount = src.TotalDiscountAmount

	if src.MarketingDetailInfo != nil {
		p.MarketingDetailInfo = make([]*MarketingDetailInfo, 0, len(src.MarketingDetailInfo))
		for _, elem := range src.MarketingDetailInfo {
			var _elem *MarketingDetailInfo
			if elem != nil {
				_elem = &MarketingDetailInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.MarketingDetailInfo = append(p.MarketingDetailInfo, _elem)
		}
	}

	if src.SkuID != nil {
		var tmp string
		if *src.SkuID != "" {
			tmp = kutils.StringDeepCopy(*src.SkuID)
		}
		p.SkuID = &tmp
	}

	return nil
}

func (p *ItemDiscountDetail) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ItemDiscountDetail[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ItemDiscountDetail) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.GoodsID = _field
	return offset, nil
}

func (p *ItemDiscountDetail) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalAmount = _field
	return offset, nil
}

func (p *ItemDiscountDetail) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DiscountAmount = _field
	return offset, nil
}

func (p *ItemDiscountDetail) FastReadField4(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*MarketingDetailInfo, 0, size)
	values := make([]MarketingDetailInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.MarketingDetailInfo = _field
	return offset, nil
}

func (p *ItemDiscountDetail) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ItemDiscountDetail) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ItemDiscountDetail) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ItemDiscountDetail) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.GoodsID)
	return offset
}

func (p *ItemDiscountDetail) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TotalAmount)
	return offset
}

func (p *ItemDiscountDetail) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.DiscountAmount)
	return offset
}

func (p *ItemDiscountDetail) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetMarketingDetailInfo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 4)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.MarketingDetailInfo {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *ItemDiscountDetail) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.GoodsID)
	return l
}

func (p *ItemDiscountDetail) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ItemDiscountDetail) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ItemDiscountDetail) field4Length() int {
	l := 0
	if p.IsSetMarketingDetailInfo() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.MarketingDetailInfo {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *ItemDiscountDetail) DeepCopy(s interface{}) error {
	src, ok := s.(*ItemDiscountDetail)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.GoodsID != "" {
		p.GoodsID = kutils.StringDeepCopy(src.GoodsID)
	}

	p.TotalAmount = src.TotalAmount

	p.DiscountAmount = src.DiscountAmount

	if src.MarketingDetailInfo != nil {
		p.MarketingDetailInfo = make([]*MarketingDetailInfo, 0, len(src.MarketingDetailInfo))
		for _, elem := range src.MarketingDetailInfo {
			var _elem *MarketingDetailInfo
			if elem != nil {
				_elem = &MarketingDetailInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.MarketingDetailInfo = append(p.MarketingDetailInfo, _elem)
		}
	}

	return nil
}

func (p *PriceCalculationDetail) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PriceCalculationDetail[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *PriceCalculationDetail) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CalculationType = _field
	return offset, nil
}

func (p *PriceCalculationDetail) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewOrderDiscountDetail()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.OrderDiscountDetail = _field
	return offset, nil
}

func (p *PriceCalculationDetail) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*GoodsDiscountDetail, 0, size)
	values := make([]GoodsDiscountDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.GoodsDiscountDetail = _field
	return offset, nil
}

func (p *PriceCalculationDetail) FastReadField4(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*ItemDiscountDetail, 0, size)
	values := make([]ItemDiscountDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ItemDiscountDetail = _field
	return offset, nil
}

func (p *PriceCalculationDetail) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.ExtCalculationNo = _field
	return offset, nil
}

func (p *PriceCalculationDetail) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *PriceCalculationDetail) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *PriceCalculationDetail) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *PriceCalculationDetail) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CalculationType)
	return offset
}

func (p *PriceCalculationDetail) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetOrderDiscountDetail() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
		offset += p.OrderDiscountDetail.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *PriceCalculationDetail) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetGoodsDiscountDetail() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.GoodsDiscountDetail {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *PriceCalculationDetail) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetItemDiscountDetail() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 4)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.ItemDiscountDetail {
			length++
			offset += v.FastWriteNocopy(buf[offset:], w)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	}
	return offset
}

func (p *PriceCalculationDetail) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetExtCalculationNo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.ExtCalculationNo)
	}
	return offset
}

func (p *PriceCalculationDetail) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *PriceCalculationDetail) field2Length() int {
	l := 0
	if p.IsSetOrderDiscountDetail() {
		l += thrift.Binary.FieldBeginLength()
		l += p.OrderDiscountDetail.BLength()
	}
	return l
}

func (p *PriceCalculationDetail) field3Length() int {
	l := 0
	if p.IsSetGoodsDiscountDetail() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.GoodsDiscountDetail {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *PriceCalculationDetail) field4Length() int {
	l := 0
	if p.IsSetItemDiscountDetail() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.ItemDiscountDetail {
			_ = v
			l += v.BLength()
		}
	}
	return l
}

func (p *PriceCalculationDetail) field5Length() int {
	l := 0
	if p.IsSetExtCalculationNo() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.ExtCalculationNo)
	}
	return l
}

func (p *PriceCalculationDetail) DeepCopy(s interface{}) error {
	src, ok := s.(*PriceCalculationDetail)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.CalculationType = src.CalculationType

	var _orderDiscountDetail *OrderDiscountDetail
	if src.OrderDiscountDetail != nil {
		_orderDiscountDetail = &OrderDiscountDetail{}
		if err := _orderDiscountDetail.DeepCopy(src.OrderDiscountDetail); err != nil {
			return err
		}
	}
	p.OrderDiscountDetail = _orderDiscountDetail

	if src.GoodsDiscountDetail != nil {
		p.GoodsDiscountDetail = make([]*GoodsDiscountDetail, 0, len(src.GoodsDiscountDetail))
		for _, elem := range src.GoodsDiscountDetail {
			var _elem *GoodsDiscountDetail
			if elem != nil {
				_elem = &GoodsDiscountDetail{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.GoodsDiscountDetail = append(p.GoodsDiscountDetail, _elem)
		}
	}

	if src.ItemDiscountDetail != nil {
		p.ItemDiscountDetail = make([]*ItemDiscountDetail, 0, len(src.ItemDiscountDetail))
		for _, elem := range src.ItemDiscountDetail {
			var _elem *ItemDiscountDetail
			if elem != nil {
				_elem = &ItemDiscountDetail{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ItemDiscountDetail = append(p.ItemDiscountDetail, _elem)
		}
	}

	if src.ExtCalculationNo != nil {
		var tmp string
		if *src.ExtCalculationNo != "" {
			tmp = kutils.StringDeepCopy(*src.ExtCalculationNo)
		}
		p.ExtCalculationNo = &tmp
	}

	return nil
}

func (p *ItemOrderInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ItemOrderInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ItemOrderInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ItemOrderID = _field
	return offset, nil
}

func (p *ItemOrderInfo) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Price = _field
	return offset, nil
}

func (p *ItemOrderInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ItemOrderInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ItemOrderInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ItemOrderInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ItemOrderID)
	return offset
}

func (p *ItemOrderInfo) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Price)
	return offset
}

func (p *ItemOrderInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ItemOrderID)
	return l
}

func (p *ItemOrderInfo) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ItemOrderInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*ItemOrderInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ItemOrderID != "" {
		p.ItemOrderID = kutils.StringDeepCopy(src.ItemOrderID)
	}

	p.Price = src.Price

	return nil
}

func (p *GoodsBookInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GoodsBookInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *GoodsBookInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BookType = _field
	return offset, nil
}

func (p *GoodsBookInfo) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.CancelPolicy = _field
	return offset, nil
}

func (p *GoodsBookInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *GoodsBookInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *GoodsBookInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *GoodsBookInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.BookType)
	return offset
}

func (p *GoodsBookInfo) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCancelPolicy() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.CancelPolicy)
	}
	return offset
}

func (p *GoodsBookInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.BookType)
	return l
}

func (p *GoodsBookInfo) field2Length() int {
	l := 0
	if p.IsSetCancelPolicy() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.CancelPolicy)
	}
	return l
}

func (p *GoodsBookInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*GoodsBookInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.BookType != "" {
		p.BookType = kutils.StringDeepCopy(src.BookType)
	}

	if src.CancelPolicy != nil {
		var tmp string
		if *src.CancelPolicy != "" {
			tmp = kutils.StringDeepCopy(*src.CancelPolicy)
		}
		p.CancelPolicy = &tmp
	}

	return nil
}

func (p *Goods) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 20:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField20(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 21:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField21(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Goods[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *Goods) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ImgURL = _field
	return offset, nil
}

func (p *Goods) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Title = _field
	return offset, nil
}

func (p *Goods) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SubTitle = _field
	return offset, nil
}

func (p *Goods) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Labels = _field
	return offset, nil
}

func (p *Goods) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DateRule = _field
	return offset, nil
}

func (p *Goods) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PoiID = _field
	return offset, nil
}

func (p *Goods) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.GoodsID = _field
	return offset, nil
}

func (p *Goods) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.GoodsIDType = _field
	return offset, nil
}

func (p *Goods) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OriginPrice = _field
	return offset, nil
}

func (p *Goods) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Price = _field
	return offset, nil
}

func (p *Goods) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Quantity = _field
	return offset, nil
}

func (p *Goods) FastReadField12(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.ItemOrderIDList = _field
	return offset, nil
}

func (p *Goods) FastReadField13(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*ItemOrderInfo, 0, size)
	values := make([]ItemOrderInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ItemOrderInfo = _field
	return offset, nil
}

func (p *Goods) FastReadField20(buf []byte) (int, error) {
	offset := 0
	_field := NewGoodsBookInfo()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.GoodsBookInfo = _field
	return offset, nil
}

func (p *Goods) FastReadField21(buf []byte) (int, error) {
	offset := 0
	_field := NewPriceCalculationDetail()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.PriceCalculationDetail = _field
	return offset, nil
}

func (p *Goods) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *Goods) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField20(buf[offset:], w)
		offset += p.fastWriteField21(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *Goods) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field20Length()
		l += p.field21Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *Goods) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ImgURL)
	return offset
}

func (p *Goods) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Title)
	return offset
}

func (p *Goods) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SubTitle)
	return offset
}

func (p *Goods) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Labels)
	return offset
}

func (p *Goods) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.DateRule)
	return offset
}

func (p *Goods) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PoiID)
	return offset
}

func (p *Goods) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.GoodsID)
	return offset
}

func (p *Goods) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 8)
	offset += thrift.Binary.WriteI32(buf[offset:], p.GoodsIDType)
	return offset
}

func (p *Goods) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 9)
	offset += thrift.Binary.WriteI64(buf[offset:], p.OriginPrice)
	return offset
}

func (p *Goods) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 10)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Price)
	return offset
}

func (p *Goods) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 11)
	offset += thrift.Binary.WriteI32(buf[offset:], p.Quantity)
	return offset
}

func (p *Goods) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 12)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ItemOrderIDList {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *Goods) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 13)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ItemOrderInfo {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *Goods) fastWriteField20(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetGoodsBookInfo() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 20)
		offset += p.GoodsBookInfo.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *Goods) fastWriteField21(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetPriceCalculationDetail() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 21)
		offset += p.PriceCalculationDetail.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *Goods) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ImgURL)
	return l
}

func (p *Goods) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Title)
	return l
}

func (p *Goods) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SubTitle)
	return l
}

func (p *Goods) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Labels)
	return l
}

func (p *Goods) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.DateRule)
	return l
}

func (p *Goods) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PoiID)
	return l
}

func (p *Goods) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.GoodsID)
	return l
}

func (p *Goods) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Goods) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *Goods) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *Goods) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *Goods) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ItemOrderIDList {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *Goods) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ItemOrderInfo {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *Goods) field20Length() int {
	l := 0
	if p.IsSetGoodsBookInfo() {
		l += thrift.Binary.FieldBeginLength()
		l += p.GoodsBookInfo.BLength()
	}
	return l
}

func (p *Goods) field21Length() int {
	l := 0
	if p.IsSetPriceCalculationDetail() {
		l += thrift.Binary.FieldBeginLength()
		l += p.PriceCalculationDetail.BLength()
	}
	return l
}

func (p *Goods) DeepCopy(s interface{}) error {
	src, ok := s.(*Goods)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ImgURL != "" {
		p.ImgURL = kutils.StringDeepCopy(src.ImgURL)
	}

	if src.Title != "" {
		p.Title = kutils.StringDeepCopy(src.Title)
	}

	if src.SubTitle != "" {
		p.SubTitle = kutils.StringDeepCopy(src.SubTitle)
	}

	if src.Labels != "" {
		p.Labels = kutils.StringDeepCopy(src.Labels)
	}

	if src.DateRule != "" {
		p.DateRule = kutils.StringDeepCopy(src.DateRule)
	}

	if src.PoiID != "" {
		p.PoiID = kutils.StringDeepCopy(src.PoiID)
	}

	if src.GoodsID != "" {
		p.GoodsID = kutils.StringDeepCopy(src.GoodsID)
	}

	p.GoodsIDType = src.GoodsIDType

	p.OriginPrice = src.OriginPrice

	p.Price = src.Price

	p.Quantity = src.Quantity

	if src.ItemOrderIDList != nil {
		p.ItemOrderIDList = make([]string, 0, len(src.ItemOrderIDList))
		for _, elem := range src.ItemOrderIDList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.ItemOrderIDList = append(p.ItemOrderIDList, _elem)
		}
	}

	if src.ItemOrderInfo != nil {
		p.ItemOrderInfo = make([]*ItemOrderInfo, 0, len(src.ItemOrderInfo))
		for _, elem := range src.ItemOrderInfo {
			var _elem *ItemOrderInfo
			if elem != nil {
				_elem = &ItemOrderInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ItemOrderInfo = append(p.ItemOrderInfo, _elem)
		}
	}

	var _goodsBookInfo *GoodsBookInfo
	if src.GoodsBookInfo != nil {
		_goodsBookInfo = &GoodsBookInfo{}
		if err := _goodsBookInfo.DeepCopy(src.GoodsBookInfo); err != nil {
			return err
		}
	}
	p.GoodsBookInfo = _goodsBookInfo

	var _priceCalculationDetail *PriceCalculationDetail
	if src.PriceCalculationDetail != nil {
		_priceCalculationDetail = &PriceCalculationDetail{}
		if err := _priceCalculationDetail.DeepCopy(src.PriceCalculationDetail); err != nil {
			return err
		}
	}
	p.PriceCalculationDetail = _priceCalculationDetail

	return nil
}

func (p *ItemOrderInfoListItem) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ItemOrderInfoListItem[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ItemOrderInfoListItem) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.GoodsID = _field
	return offset, nil
}

func (p *ItemOrderInfoListItem) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.GoodsIDType = _field
	return offset, nil
}

func (p *ItemOrderInfoListItem) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SkuID = _field
	return offset, nil
}

func (p *ItemOrderInfoListItem) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.SkuIDType = _field
	return offset, nil
}

func (p *ItemOrderInfoListItem) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ItemOrderID = _field
	return offset, nil
}

func (p *ItemOrderInfoListItem) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Price = _field
	return offset, nil
}

func (p *ItemOrderInfoListItem) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CouponItemID = _field
	return offset, nil
}

func (p *ItemOrderInfoListItem) FastReadField8(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.SubItemList = _field
	return offset, nil
}

func (p *ItemOrderInfoListItem) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ItemOrderInfoListItem) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ItemOrderInfoListItem) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ItemOrderInfoListItem) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.GoodsID)
	return offset
}

func (p *ItemOrderInfoListItem) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.GoodsIDType)
	return offset
}

func (p *ItemOrderInfoListItem) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.SkuID)
	return offset
}

func (p *ItemOrderInfoListItem) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 4)
	offset += thrift.Binary.WriteI32(buf[offset:], p.SkuIDType)
	return offset
}

func (p *ItemOrderInfoListItem) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ItemOrderID)
	return offset
}

func (p *ItemOrderInfoListItem) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Price)
	return offset
}

func (p *ItemOrderInfoListItem) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 7)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CouponItemID)
	return offset
}

func (p *ItemOrderInfoListItem) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetSubItemList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 8)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.SubItemList {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *ItemOrderInfoListItem) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.GoodsID)
	return l
}

func (p *ItemOrderInfoListItem) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ItemOrderInfoListItem) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.SkuID)
	return l
}

func (p *ItemOrderInfoListItem) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *ItemOrderInfoListItem) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ItemOrderID)
	return l
}

func (p *ItemOrderInfoListItem) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ItemOrderInfoListItem) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CouponItemID)
	return l
}

func (p *ItemOrderInfoListItem) field8Length() int {
	l := 0
	if p.IsSetSubItemList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.SubItemList {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *ItemOrderInfoListItem) DeepCopy(s interface{}) error {
	src, ok := s.(*ItemOrderInfoListItem)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.GoodsID != "" {
		p.GoodsID = kutils.StringDeepCopy(src.GoodsID)
	}

	p.GoodsIDType = src.GoodsIDType

	if src.SkuID != "" {
		p.SkuID = kutils.StringDeepCopy(src.SkuID)
	}

	p.SkuIDType = src.SkuIDType

	if src.ItemOrderID != "" {
		p.ItemOrderID = kutils.StringDeepCopy(src.ItemOrderID)
	}

	p.Price = src.Price

	if src.CouponItemID != "" {
		p.CouponItemID = kutils.StringDeepCopy(src.CouponItemID)
	}

	if src.SubItemList != nil {
		p.SubItemList = make([]string, 0, len(src.SubItemList))
		for _, elem := range src.SubItemList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.SubItemList = append(p.SubItemList, _elem)
		}
	}

	return nil
}

func (p *LifeCreateRequest) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField15(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField16(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField17(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeCreateRequest[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *LifeCreateRequest) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*Goods, 0, size)
	values := make([]Goods, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.Goods = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*ItemOrderInfoListItem, 0, size)
	values := make([]ItemOrderInfoListItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ItemOrderInfoList = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.TotalAmount = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Discount = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CpExtra = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateOrderTime = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OpenID = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField9(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PhoneNum = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField10(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ContactName = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AppID = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UnionID = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.DeliveryType = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.UseLocalLifeAccount = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField15(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.BizLine = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField16(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.FulfillType = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastReadField17(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.FeeList = _field
	return offset, nil
}

func (p *LifeCreateRequest) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *LifeCreateRequest) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
		offset += p.fastWriteField15(buf[offset:], w)
		offset += p.fastWriteField16(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField17(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *LifeCreateRequest) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
		l += p.field15Length()
		l += p.field16Length()
		l += p.field17Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *LifeCreateRequest) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *LifeCreateRequest) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.Goods {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *LifeCreateRequest) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 3)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ItemOrderInfoList {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *LifeCreateRequest) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 4)
	offset += thrift.Binary.WriteI64(buf[offset:], p.TotalAmount)
	return offset
}

func (p *LifeCreateRequest) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 5)
	offset += thrift.Binary.WriteI64(buf[offset:], p.Discount)
	return offset
}

func (p *LifeCreateRequest) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 6)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.CpExtra)
	return offset
}

func (p *LifeCreateRequest) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 7)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CreateOrderTime)
	return offset
}

func (p *LifeCreateRequest) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 8)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OpenID)
	return offset
}

func (p *LifeCreateRequest) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 9)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.PhoneNum)
	return offset
}

func (p *LifeCreateRequest) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 10)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ContactName)
	return offset
}

func (p *LifeCreateRequest) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 11)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AppID)
	return offset
}

func (p *LifeCreateRequest) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 12)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.UnionID)
	return offset
}

func (p *LifeCreateRequest) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 13)
	offset += thrift.Binary.WriteI32(buf[offset:], p.DeliveryType)
	return offset
}

func (p *LifeCreateRequest) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 14)
	offset += thrift.Binary.WriteI32(buf[offset:], p.UseLocalLifeAccount)
	return offset
}

func (p *LifeCreateRequest) fastWriteField15(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 15)
	offset += thrift.Binary.WriteI32(buf[offset:], p.BizLine)
	return offset
}

func (p *LifeCreateRequest) fastWriteField16(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 16)
	offset += thrift.Binary.WriteI32(buf[offset:], p.FulfillType)
	return offset
}

func (p *LifeCreateRequest) fastWriteField17(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetFeeList() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 17)
		listBeginOffset := offset
		offset += thrift.Binary.ListBeginLength()
		var length int
		for _, v := range p.FeeList {
			length++
			offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
		}
		thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	}
	return offset
}

func (p *LifeCreateRequest) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *LifeCreateRequest) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.Goods {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *LifeCreateRequest) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ItemOrderInfoList {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *LifeCreateRequest) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *LifeCreateRequest) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *LifeCreateRequest) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.CpExtra)
	return l
}

func (p *LifeCreateRequest) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *LifeCreateRequest) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OpenID)
	return l
}

func (p *LifeCreateRequest) field9Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.PhoneNum)
	return l
}

func (p *LifeCreateRequest) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ContactName)
	return l
}

func (p *LifeCreateRequest) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AppID)
	return l
}

func (p *LifeCreateRequest) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.UnionID)
	return l
}

func (p *LifeCreateRequest) field13Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *LifeCreateRequest) field14Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *LifeCreateRequest) field15Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *LifeCreateRequest) field16Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *LifeCreateRequest) field17Length() int {
	l := 0
	if p.IsSetFeeList() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.ListBeginLength()
		for _, v := range p.FeeList {
			_ = v
			l += thrift.Binary.StringLengthNocopy(v)
		}
	}
	return l
}

func (p *LifeCreateRequest) DeepCopy(s interface{}) error {
	src, ok := s.(*LifeCreateRequest)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	if src.Goods != nil {
		p.Goods = make([]*Goods, 0, len(src.Goods))
		for _, elem := range src.Goods {
			var _elem *Goods
			if elem != nil {
				_elem = &Goods{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.Goods = append(p.Goods, _elem)
		}
	}

	if src.ItemOrderInfoList != nil {
		p.ItemOrderInfoList = make([]*ItemOrderInfoListItem, 0, len(src.ItemOrderInfoList))
		for _, elem := range src.ItemOrderInfoList {
			var _elem *ItemOrderInfoListItem
			if elem != nil {
				_elem = &ItemOrderInfoListItem{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ItemOrderInfoList = append(p.ItemOrderInfoList, _elem)
		}
	}

	p.TotalAmount = src.TotalAmount

	p.Discount = src.Discount

	if src.CpExtra != "" {
		p.CpExtra = kutils.StringDeepCopy(src.CpExtra)
	}

	p.CreateOrderTime = src.CreateOrderTime

	if src.OpenID != "" {
		p.OpenID = kutils.StringDeepCopy(src.OpenID)
	}

	if src.PhoneNum != "" {
		p.PhoneNum = kutils.StringDeepCopy(src.PhoneNum)
	}

	if src.ContactName != "" {
		p.ContactName = kutils.StringDeepCopy(src.ContactName)
	}

	if src.AppID != "" {
		p.AppID = kutils.StringDeepCopy(src.AppID)
	}

	if src.UnionID != "" {
		p.UnionID = kutils.StringDeepCopy(src.UnionID)
	}

	p.DeliveryType = src.DeliveryType

	p.UseLocalLifeAccount = src.UseLocalLifeAccount

	p.BizLine = src.BizLine

	p.FulfillType = src.FulfillType

	if src.FeeList != nil {
		p.FeeList = make([]string, 0, len(src.FeeList))
		for _, elem := range src.FeeList {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.FeeList = append(p.FeeList, _elem)
		}
	}

	return nil
}

func (p *OrderGoodsInfo) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderGoodsInfo[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *OrderGoodsInfo) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.GoodsID = _field
	return offset, nil
}

func (p *OrderGoodsInfo) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *OrderGoodsInfo) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *OrderGoodsInfo) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *OrderGoodsInfo) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.GoodsID)
	return offset
}

func (p *OrderGoodsInfo) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.GoodsID)
	return l
}

func (p *OrderGoodsInfo) DeepCopy(s interface{}) error {
	src, ok := s.(*OrderGoodsInfo)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.GoodsID != "" {
		p.GoodsID = kutils.StringDeepCopy(src.GoodsID)
	}

	return nil
}

func (p *OrderValidTime) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderValidTime[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *OrderValidTime) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.GoodsID = _field
	return offset, nil
}

func (p *OrderValidTime) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ValidStartTime = _field
	return offset, nil
}

func (p *OrderValidTime) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ValidEndTime = _field
	return offset, nil
}

func (p *OrderValidTime) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ValidDuration = _field
	return offset, nil
}

func (p *OrderValidTime) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *OrderValidTime) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *OrderValidTime) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *OrderValidTime) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.GoodsID)
	return offset
}

func (p *OrderValidTime) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ValidStartTime)
	return offset
}

func (p *OrderValidTime) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 3)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ValidEndTime)
	return offset
}

func (p *OrderValidTime) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 4)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ValidDuration)
	return offset
}

func (p *OrderValidTime) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.GoodsID)
	return l
}

func (p *OrderValidTime) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *OrderValidTime) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *OrderValidTime) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *OrderValidTime) DeepCopy(s interface{}) error {
	src, ok := s.(*OrderValidTime)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.GoodsID != "" {
		p.GoodsID = kutils.StringDeepCopy(src.GoodsID)
	}

	p.ValidStartTime = src.ValidStartTime

	p.ValidEndTime = src.ValidEndTime

	p.ValidDuration = src.ValidDuration

	return nil
}

func (p *LifeCreateData) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeCreateData[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *LifeCreateData) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OutOrderNo = _field
	return offset, nil
}

func (p *LifeCreateData) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.PayExpireSeconds = _field
	return offset, nil
}

func (p *LifeCreateData) FastReadField3(buf []byte) (int, error) {
	offset := 0
	_field := NewOrderEntrySchema()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.OrderEntrySchema = _field
	return offset, nil
}

func (p *LifeCreateData) FastReadField4(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*OrderValidTime, 0, size)
	values := make([]OrderValidTime, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.OrderValidTime = _field
	return offset, nil
}

func (p *LifeCreateData) FastReadField5(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*OrderGoodsInfo, 0, size)
	values := make([]OrderGoodsInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.OrderGoodsInfo = _field
	return offset, nil
}

func (p *LifeCreateData) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CpDeliveryType = _field
	return offset, nil
}

func (p *LifeCreateData) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *LifeCreateData) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *LifeCreateData) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *LifeCreateData) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OutOrderNo)
	return offset
}

func (p *LifeCreateData) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 2)
	offset += thrift.Binary.WriteI32(buf[offset:], p.PayExpireSeconds)
	return offset
}

func (p *LifeCreateData) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 3)
	offset += p.OrderEntrySchema.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *LifeCreateData) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 4)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.OrderValidTime {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *LifeCreateData) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 5)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.OrderGoodsInfo {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *LifeCreateData) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CpDeliveryType)
	return offset
}

func (p *LifeCreateData) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OutOrderNo)
	return l
}

func (p *LifeCreateData) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *LifeCreateData) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.OrderEntrySchema.BLength()
	return l
}

func (p *LifeCreateData) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.OrderValidTime {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *LifeCreateData) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.OrderGoodsInfo {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *LifeCreateData) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *LifeCreateData) DeepCopy(s interface{}) error {
	src, ok := s.(*LifeCreateData)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OutOrderNo != "" {
		p.OutOrderNo = kutils.StringDeepCopy(src.OutOrderNo)
	}

	p.PayExpireSeconds = src.PayExpireSeconds

	var _orderEntrySchema *OrderEntrySchema
	if src.OrderEntrySchema != nil {
		_orderEntrySchema = &OrderEntrySchema{}
		if err := _orderEntrySchema.DeepCopy(src.OrderEntrySchema); err != nil {
			return err
		}
	}
	p.OrderEntrySchema = _orderEntrySchema

	if src.OrderValidTime != nil {
		p.OrderValidTime = make([]*OrderValidTime, 0, len(src.OrderValidTime))
		for _, elem := range src.OrderValidTime {
			var _elem *OrderValidTime
			if elem != nil {
				_elem = &OrderValidTime{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.OrderValidTime = append(p.OrderValidTime, _elem)
		}
	}

	if src.OrderGoodsInfo != nil {
		p.OrderGoodsInfo = make([]*OrderGoodsInfo, 0, len(src.OrderGoodsInfo))
		for _, elem := range src.OrderGoodsInfo {
			var _elem *OrderGoodsInfo
			if elem != nil {
				_elem = &OrderGoodsInfo{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.OrderGoodsInfo = append(p.OrderGoodsInfo, _elem)
		}
	}

	p.CpDeliveryType = src.CpDeliveryType

	return nil
}

func (p *RefundItemDetail) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RefundItemDetail[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *RefundItemDetail) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ItemOrderQuantity = _field
	return offset, nil
}

func (p *RefundItemDetail) FastReadField2(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]*ItemOrderDetail, 0, size)
	values := make([]ItemOrderDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()
		if l, err := _elem.FastRead(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
		}

		_field = append(_field, _elem)
	}
	p.ItemOrderDetail = _field
	return offset, nil
}

func (p *RefundItemDetail) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *RefundItemDetail) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *RefundItemDetail) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *RefundItemDetail) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 1)
	offset += thrift.Binary.WriteI32(buf[offset:], p.ItemOrderQuantity)
	return offset
}

func (p *RefundItemDetail) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 2)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.ItemOrderDetail {
		length++
		offset += v.FastWriteNocopy(buf[offset:], w)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRUCT, length)
	return offset
}

func (p *RefundItemDetail) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *RefundItemDetail) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.ItemOrderDetail {
		_ = v
		l += v.BLength()
	}
	return l
}

func (p *RefundItemDetail) DeepCopy(s interface{}) error {
	src, ok := s.(*RefundItemDetail)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.ItemOrderQuantity = src.ItemOrderQuantity

	if src.ItemOrderDetail != nil {
		p.ItemOrderDetail = make([]*ItemOrderDetail, 0, len(src.ItemOrderDetail))
		for _, elem := range src.ItemOrderDetail {
			var _elem *ItemOrderDetail
			if elem != nil {
				_elem = &ItemOrderDetail{}
				if err := _elem.DeepCopy(elem); err != nil {
					return err
				}
			}

			p.ItemOrderDetail = append(p.ItemOrderDetail, _elem)
		}
	}

	return nil
}

func (p *LifeRefundRequest) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField4(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField5(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField6(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField7(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField8(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField9(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				l, err = p.FastReadField10(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField11(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				l, err = p.FastReadField12(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField13(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField14(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeRefundRequest[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *LifeRefundRequest) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.AppID = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OpenID = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundID = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField4(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OrderID = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField5(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OutOrderNo = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField6(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundTotalAmount = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField7(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NeedRefundAudit = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField8(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundAuditDeadline = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField9(buf []byte) (int, error) {
	offset := 0
	_field := NewRefundItemDetail()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.RefundItemDetail = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField10(buf []byte) (int, error) {
	offset := 0

	_, size, l, err := thrift.Binary.ReadListBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_elem = v
		}

		_field = append(_field, _elem)
	}
	p.RefundReason = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField11(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.CreateRefundTime = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField12(buf []byte) (int, error) {
	offset := 0

	var _field int32
	if v, l, err := thrift.Binary.ReadI32(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundSource = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField13(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.RefundDescription = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastReadField14(buf []byte) (int, error) {
	offset := 0

	var _field *string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = &v
	}
	p.CpExtra = _field
	return offset, nil
}

func (p *LifeRefundRequest) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *LifeRefundRequest) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField6(buf[offset:], w)
		offset += p.fastWriteField7(buf[offset:], w)
		offset += p.fastWriteField8(buf[offset:], w)
		offset += p.fastWriteField11(buf[offset:], w)
		offset += p.fastWriteField12(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
		offset += p.fastWriteField4(buf[offset:], w)
		offset += p.fastWriteField5(buf[offset:], w)
		offset += p.fastWriteField9(buf[offset:], w)
		offset += p.fastWriteField10(buf[offset:], w)
		offset += p.fastWriteField13(buf[offset:], w)
		offset += p.fastWriteField14(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *LifeRefundRequest) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
		l += p.field4Length()
		l += p.field5Length()
		l += p.field6Length()
		l += p.field7Length()
		l += p.field8Length()
		l += p.field9Length()
		l += p.field10Length()
		l += p.field11Length()
		l += p.field12Length()
		l += p.field13Length()
		l += p.field14Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *LifeRefundRequest) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.AppID)
	return offset
}

func (p *LifeRefundRequest) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OpenID)
	return offset
}

func (p *LifeRefundRequest) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.RefundID)
	return offset
}

func (p *LifeRefundRequest) fastWriteField4(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 4)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OrderID)
	return offset
}

func (p *LifeRefundRequest) fastWriteField5(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 5)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OutOrderNo)
	return offset
}

func (p *LifeRefundRequest) fastWriteField6(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 6)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RefundTotalAmount)
	return offset
}

func (p *LifeRefundRequest) fastWriteField7(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 7)
	offset += thrift.Binary.WriteI32(buf[offset:], p.NeedRefundAudit)
	return offset
}

func (p *LifeRefundRequest) fastWriteField8(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 8)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RefundAuditDeadline)
	return offset
}

func (p *LifeRefundRequest) fastWriteField9(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRefundItemDetail() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 9)
		offset += p.RefundItemDetail.FastWriteNocopy(buf[offset:], w)
	}
	return offset
}

func (p *LifeRefundRequest) fastWriteField10(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.LIST, 10)
	listBeginOffset := offset
	offset += thrift.Binary.ListBeginLength()
	var length int
	for _, v := range p.RefundReason {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteListBegin(buf[listBeginOffset:], thrift.STRING, length)
	return offset
}

func (p *LifeRefundRequest) fastWriteField11(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 11)
	offset += thrift.Binary.WriteI64(buf[offset:], p.CreateRefundTime)
	return offset
}

func (p *LifeRefundRequest) fastWriteField12(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I32, 12)
	offset += thrift.Binary.WriteI32(buf[offset:], p.RefundSource)
	return offset
}

func (p *LifeRefundRequest) fastWriteField13(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetRefundDescription() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 13)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.RefundDescription)
	}
	return offset
}

func (p *LifeRefundRequest) fastWriteField14(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p.IsSetCpExtra() {
		offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 14)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, *p.CpExtra)
	}
	return offset
}

func (p *LifeRefundRequest) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.AppID)
	return l
}

func (p *LifeRefundRequest) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OpenID)
	return l
}

func (p *LifeRefundRequest) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.RefundID)
	return l
}

func (p *LifeRefundRequest) field4Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OrderID)
	return l
}

func (p *LifeRefundRequest) field5Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OutOrderNo)
	return l
}

func (p *LifeRefundRequest) field6Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *LifeRefundRequest) field7Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *LifeRefundRequest) field8Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *LifeRefundRequest) field9Length() int {
	l := 0
	if p.IsSetRefundItemDetail() {
		l += thrift.Binary.FieldBeginLength()
		l += p.RefundItemDetail.BLength()
	}
	return l
}

func (p *LifeRefundRequest) field10Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.ListBeginLength()
	for _, v := range p.RefundReason {
		_ = v
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *LifeRefundRequest) field11Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *LifeRefundRequest) field12Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I32Length()
	return l
}

func (p *LifeRefundRequest) field13Length() int {
	l := 0
	if p.IsSetRefundDescription() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.RefundDescription)
	}
	return l
}

func (p *LifeRefundRequest) field14Length() int {
	l := 0
	if p.IsSetCpExtra() {
		l += thrift.Binary.FieldBeginLength()
		l += thrift.Binary.StringLengthNocopy(*p.CpExtra)
	}
	return l
}

func (p *LifeRefundRequest) DeepCopy(s interface{}) error {
	src, ok := s.(*LifeRefundRequest)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.AppID != "" {
		p.AppID = kutils.StringDeepCopy(src.AppID)
	}

	if src.OpenID != "" {
		p.OpenID = kutils.StringDeepCopy(src.OpenID)
	}

	if src.RefundID != "" {
		p.RefundID = kutils.StringDeepCopy(src.RefundID)
	}

	if src.OrderID != "" {
		p.OrderID = kutils.StringDeepCopy(src.OrderID)
	}

	if src.OutOrderNo != "" {
		p.OutOrderNo = kutils.StringDeepCopy(src.OutOrderNo)
	}

	p.RefundTotalAmount = src.RefundTotalAmount

	p.NeedRefundAudit = src.NeedRefundAudit

	p.RefundAuditDeadline = src.RefundAuditDeadline

	var _refundItemDetail *RefundItemDetail
	if src.RefundItemDetail != nil {
		_refundItemDetail = &RefundItemDetail{}
		if err := _refundItemDetail.DeepCopy(src.RefundItemDetail); err != nil {
			return err
		}
	}
	p.RefundItemDetail = _refundItemDetail

	if src.RefundReason != nil {
		p.RefundReason = make([]string, 0, len(src.RefundReason))
		for _, elem := range src.RefundReason {
			var _elem string
			if elem != "" {
				_elem = kutils.StringDeepCopy(elem)
			}
			p.RefundReason = append(p.RefundReason, _elem)
		}
	}

	p.CreateRefundTime = src.CreateRefundTime

	p.RefundSource = src.RefundSource

	if src.RefundDescription != nil {
		var tmp string
		if *src.RefundDescription != "" {
			tmp = kutils.StringDeepCopy(*src.RefundDescription)
		}
		p.RefundDescription = &tmp
	}

	if src.CpExtra != nil {
		var tmp string
		if *src.CpExtra != "" {
			tmp = kutils.StringDeepCopy(*src.CpExtra)
		}
		p.CpExtra = &tmp
	}

	return nil
}

func (p *LifeRefundData) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeRefundData[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *LifeRefundData) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.OutRefundNo = _field
	return offset, nil
}

func (p *LifeRefundData) FastReadField2(buf []byte) (int, error) {
	offset := 0
	_field := NewOrderEntrySchema()
	if l, err := _field.FastRead(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
	}
	p.OrderEntrySchema = _field
	return offset, nil
}

func (p *LifeRefundData) FastReadField3(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.NotifyURL = _field
	return offset, nil
}

func (p *LifeRefundData) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *LifeRefundData) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *LifeRefundData) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *LifeRefundData) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.OutRefundNo)
	return offset
}

func (p *LifeRefundData) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRUCT, 2)
	offset += p.OrderEntrySchema.FastWriteNocopy(buf[offset:], w)
	return offset
}

func (p *LifeRefundData) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 3)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.NotifyURL)
	return offset
}

func (p *LifeRefundData) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.OutRefundNo)
	return l
}

func (p *LifeRefundData) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += p.OrderEntrySchema.BLength()
	return l
}

func (p *LifeRefundData) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.NotifyURL)
	return l
}

func (p *LifeRefundData) DeepCopy(s interface{}) error {
	src, ok := s.(*LifeRefundData)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.OutRefundNo != "" {
		p.OutRefundNo = kutils.StringDeepCopy(src.OutRefundNo)
	}

	var _orderEntrySchema *OrderEntrySchema
	if src.OrderEntrySchema != nil {
		_orderEntrySchema = &OrderEntrySchema{}
		if err := _orderEntrySchema.DeepCopy(src.OrderEntrySchema); err != nil {
			return err
		}
	}
	p.OrderEntrySchema = _orderEntrySchema

	if src.NotifyURL != "" {
		p.NotifyURL = kutils.StringDeepCopy(src.NotifyURL)
	}

	return nil
}

func (p *ItemOrderDetail) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				l, err = p.FastReadField3(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ItemOrderDetail[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ItemOrderDetail) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ItemOrderID = _field
	return offset, nil
}

func (p *ItemOrderDetail) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.RefundAmount = _field
	return offset, nil
}

func (p *ItemOrderDetail) FastReadField3(buf []byte) (int, error) {
	offset := 0

	_, _, size, l, err := thrift.Binary.ReadMapBegin(buf[offset:])
	offset += l
	if err != nil {
		return offset, err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_key = v
		}

		var _val string
		if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
			return offset, err
		} else {
			offset += l
			_val = v
		}

		_field[_key] = _val
	}
	p.ItemDeductInfo = _field
	return offset, nil
}

func (p *ItemOrderDetail) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ItemOrderDetail) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField2(buf[offset:], w)
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField3(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ItemOrderDetail) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
		l += p.field3Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ItemOrderDetail) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.ItemOrderID)
	return offset
}

func (p *ItemOrderDetail) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.RefundAmount)
	return offset
}

func (p *ItemOrderDetail) fastWriteField3(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.MAP, 3)
	mapBeginOffset := offset
	offset += thrift.Binary.MapBeginLength()
	var length int
	for k, v := range p.ItemDeductInfo {
		length++
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, k)
		offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, v)
	}
	thrift.Binary.WriteMapBegin(buf[mapBeginOffset:], thrift.STRING, thrift.STRING, length)
	return offset
}

func (p *ItemOrderDetail) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.ItemOrderID)
	return l
}

func (p *ItemOrderDetail) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ItemOrderDetail) field3Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.MapBeginLength()
	for k, v := range p.ItemDeductInfo {
		_, _ = k, v

		l += thrift.Binary.StringLengthNocopy(k)
		l += thrift.Binary.StringLengthNocopy(v)
	}
	return l
}

func (p *ItemOrderDetail) DeepCopy(s interface{}) error {
	src, ok := s.(*ItemOrderDetail)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.ItemOrderID != "" {
		p.ItemOrderID = kutils.StringDeepCopy(src.ItemOrderID)
	}

	p.RefundAmount = src.RefundAmount

	if src.ItemDeductInfo != nil {
		p.ItemDeductInfo = make(map[string]string, len(src.ItemDeductInfo))
		for key, val := range src.ItemDeductInfo {
			var _key string
			if key != "" {
				_key = kutils.StringDeepCopy(key)
			}

			var _val string
			if val != "" {
				_val = kutils.StringDeepCopy(val)
			}

			p.ItemDeductInfo[_key] = _val
		}
	}

	return nil
}

func (p *OrderEntrySchema) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderEntrySchema[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *OrderEntrySchema) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Path = _field
	return offset, nil
}

func (p *OrderEntrySchema) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field string
	if v, l, err := thrift.Binary.ReadString(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.Params = _field
	return offset, nil
}

func (p *OrderEntrySchema) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *OrderEntrySchema) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *OrderEntrySchema) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *OrderEntrySchema) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 1)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Path)
	return offset
}

func (p *OrderEntrySchema) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.STRING, 2)
	offset += thrift.Binary.WriteStringNocopy(buf[offset:], w, p.Params)
	return offset
}

func (p *OrderEntrySchema) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Path)
	return l
}

func (p *OrderEntrySchema) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.StringLengthNocopy(p.Params)
	return l
}

func (p *OrderEntrySchema) DeepCopy(s interface{}) error {
	src, ok := s.(*OrderEntrySchema)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	if src.Path != "" {
		p.Path = kutils.StringDeepCopy(src.Path)
	}

	if src.Params != "" {
		p.Params = kutils.StringDeepCopy(src.Params)
	}

	return nil
}

func (p *ProductBase) FastRead(buf []byte) (int, error) {

	var err error
	var offset int
	var l int
	var fieldTypeId thrift.TType
	var fieldId int16
	for {
		fieldTypeId, fieldId, l, err = thrift.Binary.ReadFieldBegin(buf[offset:])
		offset += l
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField1(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				l, err = p.FastReadField2(buf[offset:])
				offset += l
				if err != nil {
					goto ReadFieldError
				}
			} else {
				l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
				offset += l
				if err != nil {
					goto SkipFieldError
				}
			}
		default:
			l, err = thrift.Binary.Skip(buf[offset:], fieldTypeId)
			offset += l
			if err != nil {
				goto SkipFieldError
			}
		}
	}

	return offset, nil
ReadFieldBeginError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ProductBase[fieldId]), err)
SkipFieldError:
	return offset, thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)
}

func (p *ProductBase) FastReadField1(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ProductID = _field
	return offset, nil
}

func (p *ProductBase) FastReadField2(buf []byte) (int, error) {
	offset := 0

	var _field int64
	if v, l, err := thrift.Binary.ReadI64(buf[offset:]); err != nil {
		return offset, err
	} else {
		offset += l
		_field = v
	}
	p.ProductVersion = _field
	return offset, nil
}

func (p *ProductBase) FastWrite(buf []byte) int {
	return p.FastWriteNocopy(buf, nil)
}

func (p *ProductBase) FastWriteNocopy(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	if p != nil {
		offset += p.fastWriteField1(buf[offset:], w)
		offset += p.fastWriteField2(buf[offset:], w)
	}
	offset += thrift.Binary.WriteFieldStop(buf[offset:])
	return offset
}

func (p *ProductBase) BLength() int {
	l := 0
	if p != nil {
		l += p.field1Length()
		l += p.field2Length()
	}
	l += thrift.Binary.FieldStopLength()
	return l
}

func (p *ProductBase) fastWriteField1(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 1)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ProductID)
	return offset
}

func (p *ProductBase) fastWriteField2(buf []byte, w thrift.NocopyWriter) int {
	offset := 0
	offset += thrift.Binary.WriteFieldBegin(buf[offset:], thrift.I64, 2)
	offset += thrift.Binary.WriteI64(buf[offset:], p.ProductVersion)
	return offset
}

func (p *ProductBase) field1Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ProductBase) field2Length() int {
	l := 0
	l += thrift.Binary.FieldBeginLength()
	l += thrift.Binary.I64Length()
	return l
}

func (p *ProductBase) DeepCopy(s interface{}) error {
	src, ok := s.(*ProductBase)
	if !ok {
		return fmt.Errorf("%T's type not matched %T", s, p)
	}

	p.ProductID = src.ProductID

	p.ProductVersion = src.ProductVersion

	return nil
}
