// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package douyin

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
)

type MarketingDetailInfo struct {
	ID                 string  `thrift:"id,1" frugal:"1,default,string" json:"id"`
	Type               int64   `thrift:"type,2" frugal:"2,default,i64" json:"type"`
	DiscountAmount     int64   `thrift:"discount_amount,3" frugal:"3,default,i64" json:"discount_amount"`
	Title              string  `thrift:"title,4" frugal:"4,default,string" json:"title"`
	Note               *string `thrift:"note,5,optional" frugal:"5,optional,string" json:"note,omitempty"`
	DiscountRange      int64   `thrift:"discount_range,6" frugal:"6,default,i64" json:"discount_range"`
	Subtype            *string `thrift:"subtype,7,optional" frugal:"7,optional,string" json:"subtype,omitempty"`
	Kind               int64   `thrift:"kind,8" frugal:"8,default,i64" json:"kind"`
	CreatorType        int64   `thrift:"creator_type,9" frugal:"9,default,i64" json:"creator_type"`
	MerchantFunderType int64   `thrift:"merchant_funder_type,10" frugal:"10,default,i64" json:"merchant_funder_type"`
	FunderAccountID    int64   `thrift:"funder_account_id,11" frugal:"11,default,i64" json:"funder_account_id"`
	Code               *string `thrift:"code,12,optional" frugal:"12,optional,string" json:"code,omitempty"`
	Value              *int64  `thrift:"value,13,optional" frugal:"13,optional,i64" json:"value,omitempty"`
}

func NewMarketingDetailInfo() *MarketingDetailInfo {
	return &MarketingDetailInfo{}
}

func (p *MarketingDetailInfo) InitDefault() {
}

func (p *MarketingDetailInfo) GetID() (v string) {
	return p.ID
}

func (p *MarketingDetailInfo) GetType() (v int64) {
	return p.Type
}

func (p *MarketingDetailInfo) GetDiscountAmount() (v int64) {
	return p.DiscountAmount
}

func (p *MarketingDetailInfo) GetTitle() (v string) {
	return p.Title
}

var MarketingDetailInfo_Note_DEFAULT string

func (p *MarketingDetailInfo) GetNote() (v string) {
	if !p.IsSetNote() {
		return MarketingDetailInfo_Note_DEFAULT
	}
	return *p.Note
}

func (p *MarketingDetailInfo) GetDiscountRange() (v int64) {
	return p.DiscountRange
}

var MarketingDetailInfo_Subtype_DEFAULT string

func (p *MarketingDetailInfo) GetSubtype() (v string) {
	if !p.IsSetSubtype() {
		return MarketingDetailInfo_Subtype_DEFAULT
	}
	return *p.Subtype
}

func (p *MarketingDetailInfo) GetKind() (v int64) {
	return p.Kind
}

func (p *MarketingDetailInfo) GetCreatorType() (v int64) {
	return p.CreatorType
}

func (p *MarketingDetailInfo) GetMerchantFunderType() (v int64) {
	return p.MerchantFunderType
}

func (p *MarketingDetailInfo) GetFunderAccountID() (v int64) {
	return p.FunderAccountID
}

var MarketingDetailInfo_Code_DEFAULT string

func (p *MarketingDetailInfo) GetCode() (v string) {
	if !p.IsSetCode() {
		return MarketingDetailInfo_Code_DEFAULT
	}
	return *p.Code
}

var MarketingDetailInfo_Value_DEFAULT int64

func (p *MarketingDetailInfo) GetValue() (v int64) {
	if !p.IsSetValue() {
		return MarketingDetailInfo_Value_DEFAULT
	}
	return *p.Value
}
func (p *MarketingDetailInfo) SetID(val string) {
	p.ID = val
}
func (p *MarketingDetailInfo) SetType(val int64) {
	p.Type = val
}
func (p *MarketingDetailInfo) SetDiscountAmount(val int64) {
	p.DiscountAmount = val
}
func (p *MarketingDetailInfo) SetTitle(val string) {
	p.Title = val
}
func (p *MarketingDetailInfo) SetNote(val *string) {
	p.Note = val
}
func (p *MarketingDetailInfo) SetDiscountRange(val int64) {
	p.DiscountRange = val
}
func (p *MarketingDetailInfo) SetSubtype(val *string) {
	p.Subtype = val
}
func (p *MarketingDetailInfo) SetKind(val int64) {
	p.Kind = val
}
func (p *MarketingDetailInfo) SetCreatorType(val int64) {
	p.CreatorType = val
}
func (p *MarketingDetailInfo) SetMerchantFunderType(val int64) {
	p.MerchantFunderType = val
}
func (p *MarketingDetailInfo) SetFunderAccountID(val int64) {
	p.FunderAccountID = val
}
func (p *MarketingDetailInfo) SetCode(val *string) {
	p.Code = val
}
func (p *MarketingDetailInfo) SetValue(val *int64) {
	p.Value = val
}

var fieldIDToName_MarketingDetailInfo = map[int16]string{
	1:  "id",
	2:  "type",
	3:  "discount_amount",
	4:  "title",
	5:  "note",
	6:  "discount_range",
	7:  "subtype",
	8:  "kind",
	9:  "creator_type",
	10: "merchant_funder_type",
	11: "funder_account_id",
	12: "code",
	13: "value",
}

func (p *MarketingDetailInfo) IsSetNote() bool {
	return p.Note != nil
}

func (p *MarketingDetailInfo) IsSetSubtype() bool {
	return p.Subtype != nil
}

func (p *MarketingDetailInfo) IsSetCode() bool {
	return p.Code != nil
}

func (p *MarketingDetailInfo) IsSetValue() bool {
	return p.Value != nil
}

func (p *MarketingDetailInfo) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MarketingDetailInfo")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MarketingDetailInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *MarketingDetailInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Type = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DiscountAmount = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Title = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Note = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DiscountRange = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Subtype = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Kind = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreatorType = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MerchantFunderType = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FunderAccountID = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Code = _field
	return nil
}
func (p *MarketingDetailInfo) ReadField13(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Value = _field
	return nil
}

func (p *MarketingDetailInfo) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("MarketingDetailInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("MarketingDetailInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MarketingDetailInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Type); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("discount_amount", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DiscountAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("title", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Title); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetNote() {
		if err = oprot.WriteFieldBegin("note", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Note); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("discount_range", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DiscountRange); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubtype() {
		if err = oprot.WriteFieldBegin("subtype", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Subtype); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("kind", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Kind); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("creator_type", thrift.I64, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreatorType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("merchant_funder_type", thrift.I64, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MerchantFunderType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("funder_account_id", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.FunderAccountID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("code", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Code); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *MarketingDetailInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetValue() {
		if err = oprot.WriteFieldBegin("value", thrift.I64, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Value); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *MarketingDetailInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MarketingDetailInfo(%+v)", *p)

}

type OrderDiscountDetail struct {
	OrderTotalDiscountAmount int64                  `thrift:"order_total_discount_amount,1" frugal:"1,default,i64" json:"order_total_discount_amount"`
	GoodsTotalDiscountAmount int64                  `thrift:"goods_total_discount_amount,2" frugal:"2,default,i64" json:"goods_total_discount_amount"`
	MarketingDetailInfo      []*MarketingDetailInfo `thrift:"marketing_detail_info,3,optional" frugal:"3,optional,list<MarketingDetailInfo>" json:"marketing_detail_info,omitempty"`
}

func NewOrderDiscountDetail() *OrderDiscountDetail {
	return &OrderDiscountDetail{}
}

func (p *OrderDiscountDetail) InitDefault() {
}

func (p *OrderDiscountDetail) GetOrderTotalDiscountAmount() (v int64) {
	return p.OrderTotalDiscountAmount
}

func (p *OrderDiscountDetail) GetGoodsTotalDiscountAmount() (v int64) {
	return p.GoodsTotalDiscountAmount
}

var OrderDiscountDetail_MarketingDetailInfo_DEFAULT []*MarketingDetailInfo

func (p *OrderDiscountDetail) GetMarketingDetailInfo() (v []*MarketingDetailInfo) {
	if !p.IsSetMarketingDetailInfo() {
		return OrderDiscountDetail_MarketingDetailInfo_DEFAULT
	}
	return p.MarketingDetailInfo
}
func (p *OrderDiscountDetail) SetOrderTotalDiscountAmount(val int64) {
	p.OrderTotalDiscountAmount = val
}
func (p *OrderDiscountDetail) SetGoodsTotalDiscountAmount(val int64) {
	p.GoodsTotalDiscountAmount = val
}
func (p *OrderDiscountDetail) SetMarketingDetailInfo(val []*MarketingDetailInfo) {
	p.MarketingDetailInfo = val
}

var fieldIDToName_OrderDiscountDetail = map[int16]string{
	1: "order_total_discount_amount",
	2: "goods_total_discount_amount",
	3: "marketing_detail_info",
}

func (p *OrderDiscountDetail) IsSetMarketingDetailInfo() bool {
	return p.MarketingDetailInfo != nil
}

func (p *OrderDiscountDetail) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OrderDiscountDetail")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderDiscountDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OrderDiscountDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderTotalDiscountAmount = _field
	return nil
}
func (p *OrderDiscountDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GoodsTotalDiscountAmount = _field
	return nil
}
func (p *OrderDiscountDetail) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MarketingDetailInfo, 0, size)
	values := make([]MarketingDetailInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MarketingDetailInfo = _field
	return nil
}

func (p *OrderDiscountDetail) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OrderDiscountDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("OrderDiscountDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OrderDiscountDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_total_discount_amount", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.OrderTotalDiscountAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *OrderDiscountDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("goods_total_discount_amount", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.GoodsTotalDiscountAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *OrderDiscountDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMarketingDetailInfo() {
		if err = oprot.WriteFieldBegin("marketing_detail_info", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MarketingDetailInfo)); err != nil {
			return err
		}
		for _, v := range p.MarketingDetailInfo {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *OrderDiscountDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderDiscountDetail(%+v)", *p)

}

type GoodsDiscountDetail struct {
	GoodsID             string                 `thrift:"goods_id,1" frugal:"1,default,string" json:"goods_id"`
	Quantity            int64                  `thrift:"quantity,2" frugal:"2,default,i64" json:"quantity"`
	TotalAmount         int64                  `thrift:"total_amount,3" frugal:"3,default,i64" json:"total_amount"`
	TotalDiscountAmount int64                  `thrift:"total_discount_amount,4" frugal:"4,default,i64" json:"total_discount_amount"`
	MarketingDetailInfo []*MarketingDetailInfo `thrift:"marketing_detail_info,5,optional" frugal:"5,optional,list<MarketingDetailInfo>" json:"marketing_detail_info,omitempty"`
	SkuID               *string                `thrift:"sku_id,6,optional" frugal:"6,optional,string" json:"sku_id,omitempty"`
}

func NewGoodsDiscountDetail() *GoodsDiscountDetail {
	return &GoodsDiscountDetail{}
}

func (p *GoodsDiscountDetail) InitDefault() {
}

func (p *GoodsDiscountDetail) GetGoodsID() (v string) {
	return p.GoodsID
}

func (p *GoodsDiscountDetail) GetQuantity() (v int64) {
	return p.Quantity
}

func (p *GoodsDiscountDetail) GetTotalAmount() (v int64) {
	return p.TotalAmount
}

func (p *GoodsDiscountDetail) GetTotalDiscountAmount() (v int64) {
	return p.TotalDiscountAmount
}

var GoodsDiscountDetail_MarketingDetailInfo_DEFAULT []*MarketingDetailInfo

func (p *GoodsDiscountDetail) GetMarketingDetailInfo() (v []*MarketingDetailInfo) {
	if !p.IsSetMarketingDetailInfo() {
		return GoodsDiscountDetail_MarketingDetailInfo_DEFAULT
	}
	return p.MarketingDetailInfo
}

var GoodsDiscountDetail_SkuID_DEFAULT string

func (p *GoodsDiscountDetail) GetSkuID() (v string) {
	if !p.IsSetSkuID() {
		return GoodsDiscountDetail_SkuID_DEFAULT
	}
	return *p.SkuID
}
func (p *GoodsDiscountDetail) SetGoodsID(val string) {
	p.GoodsID = val
}
func (p *GoodsDiscountDetail) SetQuantity(val int64) {
	p.Quantity = val
}
func (p *GoodsDiscountDetail) SetTotalAmount(val int64) {
	p.TotalAmount = val
}
func (p *GoodsDiscountDetail) SetTotalDiscountAmount(val int64) {
	p.TotalDiscountAmount = val
}
func (p *GoodsDiscountDetail) SetMarketingDetailInfo(val []*MarketingDetailInfo) {
	p.MarketingDetailInfo = val
}
func (p *GoodsDiscountDetail) SetSkuID(val *string) {
	p.SkuID = val
}

var fieldIDToName_GoodsDiscountDetail = map[int16]string{
	1: "goods_id",
	2: "quantity",
	3: "total_amount",
	4: "total_discount_amount",
	5: "marketing_detail_info",
	6: "sku_id",
}

func (p *GoodsDiscountDetail) IsSetMarketingDetailInfo() bool {
	return p.MarketingDetailInfo != nil
}

func (p *GoodsDiscountDetail) IsSetSkuID() bool {
	return p.SkuID != nil
}

func (p *GoodsDiscountDetail) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GoodsDiscountDetail")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GoodsDiscountDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GoodsDiscountDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GoodsID = _field
	return nil
}
func (p *GoodsDiscountDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Quantity = _field
	return nil
}
func (p *GoodsDiscountDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalAmount = _field
	return nil
}
func (p *GoodsDiscountDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalDiscountAmount = _field
	return nil
}
func (p *GoodsDiscountDetail) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MarketingDetailInfo, 0, size)
	values := make([]MarketingDetailInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MarketingDetailInfo = _field
	return nil
}
func (p *GoodsDiscountDetail) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SkuID = _field
	return nil
}

func (p *GoodsDiscountDetail) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GoodsDiscountDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("GoodsDiscountDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GoodsDiscountDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("goods_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GoodsID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GoodsDiscountDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("quantity", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Quantity); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GoodsDiscountDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total_amount", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TotalAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *GoodsDiscountDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total_discount_amount", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TotalDiscountAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *GoodsDiscountDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetMarketingDetailInfo() {
		if err = oprot.WriteFieldBegin("marketing_detail_info", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MarketingDetailInfo)); err != nil {
			return err
		}
		for _, v := range p.MarketingDetailInfo {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *GoodsDiscountDetail) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSkuID() {
		if err = oprot.WriteFieldBegin("sku_id", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SkuID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *GoodsDiscountDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GoodsDiscountDetail(%+v)", *p)

}

type ItemDiscountDetail struct {
	GoodsID             string                 `thrift:"goods_id,1" frugal:"1,default,string" json:"goods_id"`
	TotalAmount         int64                  `thrift:"total_amount,2" frugal:"2,default,i64" json:"total_amount"`
	DiscountAmount      int64                  `thrift:"discount_amount,3" frugal:"3,default,i64" json:"discount_amount"`
	MarketingDetailInfo []*MarketingDetailInfo `thrift:"marketing_detail_info,4,optional" frugal:"4,optional,list<MarketingDetailInfo>" json:"marketing_detail_info,omitempty"`
}

func NewItemDiscountDetail() *ItemDiscountDetail {
	return &ItemDiscountDetail{}
}

func (p *ItemDiscountDetail) InitDefault() {
}

func (p *ItemDiscountDetail) GetGoodsID() (v string) {
	return p.GoodsID
}

func (p *ItemDiscountDetail) GetTotalAmount() (v int64) {
	return p.TotalAmount
}

func (p *ItemDiscountDetail) GetDiscountAmount() (v int64) {
	return p.DiscountAmount
}

var ItemDiscountDetail_MarketingDetailInfo_DEFAULT []*MarketingDetailInfo

func (p *ItemDiscountDetail) GetMarketingDetailInfo() (v []*MarketingDetailInfo) {
	if !p.IsSetMarketingDetailInfo() {
		return ItemDiscountDetail_MarketingDetailInfo_DEFAULT
	}
	return p.MarketingDetailInfo
}
func (p *ItemDiscountDetail) SetGoodsID(val string) {
	p.GoodsID = val
}
func (p *ItemDiscountDetail) SetTotalAmount(val int64) {
	p.TotalAmount = val
}
func (p *ItemDiscountDetail) SetDiscountAmount(val int64) {
	p.DiscountAmount = val
}
func (p *ItemDiscountDetail) SetMarketingDetailInfo(val []*MarketingDetailInfo) {
	p.MarketingDetailInfo = val
}

var fieldIDToName_ItemDiscountDetail = map[int16]string{
	1: "goods_id",
	2: "total_amount",
	3: "discount_amount",
	4: "marketing_detail_info",
}

func (p *ItemDiscountDetail) IsSetMarketingDetailInfo() bool {
	return p.MarketingDetailInfo != nil
}

func (p *ItemDiscountDetail) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ItemDiscountDetail")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ItemDiscountDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ItemDiscountDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GoodsID = _field
	return nil
}
func (p *ItemDiscountDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalAmount = _field
	return nil
}
func (p *ItemDiscountDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DiscountAmount = _field
	return nil
}
func (p *ItemDiscountDetail) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MarketingDetailInfo, 0, size)
	values := make([]MarketingDetailInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.MarketingDetailInfo = _field
	return nil
}

func (p *ItemDiscountDetail) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ItemDiscountDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("ItemDiscountDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ItemDiscountDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("goods_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GoodsID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ItemDiscountDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total_amount", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TotalAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ItemDiscountDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("discount_amount", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.DiscountAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ItemDiscountDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetMarketingDetailInfo() {
		if err = oprot.WriteFieldBegin("marketing_detail_info", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MarketingDetailInfo)); err != nil {
			return err
		}
		for _, v := range p.MarketingDetailInfo {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ItemDiscountDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ItemDiscountDetail(%+v)", *p)

}

type PriceCalculationDetail struct {
	CalculationType     int64                  `thrift:"calculation_type,1" frugal:"1,default,i64" json:"calculation_type"`
	OrderDiscountDetail *OrderDiscountDetail   `thrift:"order_discount_detail,2,optional" frugal:"2,optional,OrderDiscountDetail" json:"order_discount_detail,omitempty"`
	GoodsDiscountDetail []*GoodsDiscountDetail `thrift:"goods_discount_detail,3,optional" frugal:"3,optional,list<GoodsDiscountDetail>" json:"goods_discount_detail,omitempty"`
	ItemDiscountDetail  []*ItemDiscountDetail  `thrift:"item_discount_detail,4,optional" frugal:"4,optional,list<ItemDiscountDetail>" json:"item_discount_detail,omitempty"`
	ExtCalculationNo    *string                `thrift:"ext_calculation_no,5,optional" frugal:"5,optional,string" json:"ext_calculation_no,omitempty"`
}

func NewPriceCalculationDetail() *PriceCalculationDetail {
	return &PriceCalculationDetail{}
}

func (p *PriceCalculationDetail) InitDefault() {
}

func (p *PriceCalculationDetail) GetCalculationType() (v int64) {
	return p.CalculationType
}

var PriceCalculationDetail_OrderDiscountDetail_DEFAULT *OrderDiscountDetail

func (p *PriceCalculationDetail) GetOrderDiscountDetail() (v *OrderDiscountDetail) {
	if !p.IsSetOrderDiscountDetail() {
		return PriceCalculationDetail_OrderDiscountDetail_DEFAULT
	}
	return p.OrderDiscountDetail
}

var PriceCalculationDetail_GoodsDiscountDetail_DEFAULT []*GoodsDiscountDetail

func (p *PriceCalculationDetail) GetGoodsDiscountDetail() (v []*GoodsDiscountDetail) {
	if !p.IsSetGoodsDiscountDetail() {
		return PriceCalculationDetail_GoodsDiscountDetail_DEFAULT
	}
	return p.GoodsDiscountDetail
}

var PriceCalculationDetail_ItemDiscountDetail_DEFAULT []*ItemDiscountDetail

func (p *PriceCalculationDetail) GetItemDiscountDetail() (v []*ItemDiscountDetail) {
	if !p.IsSetItemDiscountDetail() {
		return PriceCalculationDetail_ItemDiscountDetail_DEFAULT
	}
	return p.ItemDiscountDetail
}

var PriceCalculationDetail_ExtCalculationNo_DEFAULT string

func (p *PriceCalculationDetail) GetExtCalculationNo() (v string) {
	if !p.IsSetExtCalculationNo() {
		return PriceCalculationDetail_ExtCalculationNo_DEFAULT
	}
	return *p.ExtCalculationNo
}
func (p *PriceCalculationDetail) SetCalculationType(val int64) {
	p.CalculationType = val
}
func (p *PriceCalculationDetail) SetOrderDiscountDetail(val *OrderDiscountDetail) {
	p.OrderDiscountDetail = val
}
func (p *PriceCalculationDetail) SetGoodsDiscountDetail(val []*GoodsDiscountDetail) {
	p.GoodsDiscountDetail = val
}
func (p *PriceCalculationDetail) SetItemDiscountDetail(val []*ItemDiscountDetail) {
	p.ItemDiscountDetail = val
}
func (p *PriceCalculationDetail) SetExtCalculationNo(val *string) {
	p.ExtCalculationNo = val
}

var fieldIDToName_PriceCalculationDetail = map[int16]string{
	1: "calculation_type",
	2: "order_discount_detail",
	3: "goods_discount_detail",
	4: "item_discount_detail",
	5: "ext_calculation_no",
}

func (p *PriceCalculationDetail) IsSetOrderDiscountDetail() bool {
	return p.OrderDiscountDetail != nil
}

func (p *PriceCalculationDetail) IsSetGoodsDiscountDetail() bool {
	return p.GoodsDiscountDetail != nil
}

func (p *PriceCalculationDetail) IsSetItemDiscountDetail() bool {
	return p.ItemDiscountDetail != nil
}

func (p *PriceCalculationDetail) IsSetExtCalculationNo() bool {
	return p.ExtCalculationNo != nil
}

func (p *PriceCalculationDetail) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("PriceCalculationDetail")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PriceCalculationDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *PriceCalculationDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CalculationType = _field
	return nil
}
func (p *PriceCalculationDetail) ReadField2(iprot thrift.TProtocol) error {
	_field := NewOrderDiscountDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OrderDiscountDetail = _field
	return nil
}
func (p *PriceCalculationDetail) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*GoodsDiscountDetail, 0, size)
	values := make([]GoodsDiscountDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.GoodsDiscountDetail = _field
	return nil
}
func (p *PriceCalculationDetail) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ItemDiscountDetail, 0, size)
	values := make([]ItemDiscountDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ItemDiscountDetail = _field
	return nil
}
func (p *PriceCalculationDetail) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExtCalculationNo = _field
	return nil
}

func (p *PriceCalculationDetail) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("PriceCalculationDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("PriceCalculationDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PriceCalculationDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("calculation_type", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CalculationType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *PriceCalculationDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderDiscountDetail() {
		if err = oprot.WriteFieldBegin("order_discount_detail", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.OrderDiscountDetail.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *PriceCalculationDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetGoodsDiscountDetail() {
		if err = oprot.WriteFieldBegin("goods_discount_detail", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.GoodsDiscountDetail)); err != nil {
			return err
		}
		for _, v := range p.GoodsDiscountDetail {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *PriceCalculationDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetItemDiscountDetail() {
		if err = oprot.WriteFieldBegin("item_discount_detail", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ItemDiscountDetail)); err != nil {
			return err
		}
		for _, v := range p.ItemDiscountDetail {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *PriceCalculationDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetExtCalculationNo() {
		if err = oprot.WriteFieldBegin("ext_calculation_no", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ExtCalculationNo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *PriceCalculationDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PriceCalculationDetail(%+v)", *p)

}

type ItemOrderInfo struct {
	ItemOrderID string `thrift:"item_order_id,1" frugal:"1,default,string" json:"item_order_id"`
	Price       int64  `thrift:"price,2" frugal:"2,default,i64" json:"price"`
}

func NewItemOrderInfo() *ItemOrderInfo {
	return &ItemOrderInfo{}
}

func (p *ItemOrderInfo) InitDefault() {
}

func (p *ItemOrderInfo) GetItemOrderID() (v string) {
	return p.ItemOrderID
}

func (p *ItemOrderInfo) GetPrice() (v int64) {
	return p.Price
}
func (p *ItemOrderInfo) SetItemOrderID(val string) {
	p.ItemOrderID = val
}
func (p *ItemOrderInfo) SetPrice(val int64) {
	p.Price = val
}

var fieldIDToName_ItemOrderInfo = map[int16]string{
	1: "item_order_id",
	2: "price",
}

func (p *ItemOrderInfo) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ItemOrderInfo")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ItemOrderInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ItemOrderInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ItemOrderID = _field
	return nil
}
func (p *ItemOrderInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Price = _field
	return nil
}

func (p *ItemOrderInfo) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ItemOrderInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("ItemOrderInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ItemOrderInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("item_order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ItemOrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ItemOrderInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("price", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Price); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ItemOrderInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ItemOrderInfo(%+v)", *p)

}

type GoodsBookInfo struct {
	BookType     string  `thrift:"book_type,1" frugal:"1,default,string" json:"book_type"`
	CancelPolicy *string `thrift:"cancel_policy,2,optional" frugal:"2,optional,string" json:"cancel_policy,omitempty"`
}

func NewGoodsBookInfo() *GoodsBookInfo {
	return &GoodsBookInfo{}
}

func (p *GoodsBookInfo) InitDefault() {
}

func (p *GoodsBookInfo) GetBookType() (v string) {
	return p.BookType
}

var GoodsBookInfo_CancelPolicy_DEFAULT string

func (p *GoodsBookInfo) GetCancelPolicy() (v string) {
	if !p.IsSetCancelPolicy() {
		return GoodsBookInfo_CancelPolicy_DEFAULT
	}
	return *p.CancelPolicy
}
func (p *GoodsBookInfo) SetBookType(val string) {
	p.BookType = val
}
func (p *GoodsBookInfo) SetCancelPolicy(val *string) {
	p.CancelPolicy = val
}

var fieldIDToName_GoodsBookInfo = map[int16]string{
	1: "book_type",
	2: "cancel_policy",
}

func (p *GoodsBookInfo) IsSetCancelPolicy() bool {
	return p.CancelPolicy != nil
}

func (p *GoodsBookInfo) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GoodsBookInfo")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GoodsBookInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GoodsBookInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BookType = _field
	return nil
}
func (p *GoodsBookInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CancelPolicy = _field
	return nil
}

func (p *GoodsBookInfo) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GoodsBookInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("GoodsBookInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GoodsBookInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("book_type", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.BookType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GoodsBookInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCancelPolicy() {
		if err = oprot.WriteFieldBegin("cancel_policy", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CancelPolicy); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GoodsBookInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GoodsBookInfo(%+v)", *p)

}

type Goods struct {
	ImgURL                 string                  `thrift:"img_url,1" frugal:"1,default,string" json:"img_url"`
	Title                  string                  `thrift:"title,2" frugal:"2,default,string" json:"title"`
	SubTitle               string                  `thrift:"sub_title,3" frugal:"3,default,string" json:"sub_title"`
	Labels                 string                  `thrift:"labels,4" frugal:"4,default,string" json:"labels"`
	DateRule               string                  `thrift:"date_rule,5" frugal:"5,default,string" json:"date_rule"`
	PoiID                  string                  `thrift:"poi_id,6" frugal:"6,default,string" json:"poi_id"`
	GoodsID                string                  `thrift:"goods_id,7" frugal:"7,default,string" json:"goods_id"`
	GoodsIDType            int32                   `thrift:"goods_id_type,8" frugal:"8,default,i32" json:"goods_id_type"`
	OriginPrice            int64                   `thrift:"origin_price,9" frugal:"9,default,i64" json:"origin_price"`
	Price                  int64                   `thrift:"price,10" frugal:"10,default,i64" json:"price"`
	Quantity               int32                   `thrift:"quantity,11" frugal:"11,default,i32" json:"quantity"`
	ItemOrderIDList        []string                `thrift:"item_order_id_list,12" frugal:"12,default,list<string>" json:"item_order_id_list"`
	ItemOrderInfo          []*ItemOrderInfo        `thrift:"item_order_info,13" frugal:"13,default,list<ItemOrderInfo>" json:"item_order_info"`
	GoodsBookInfo          *GoodsBookInfo          `thrift:"goods_book_info,20,optional" frugal:"20,optional,GoodsBookInfo" json:"goods_book_info,omitempty"`
	PriceCalculationDetail *PriceCalculationDetail `thrift:"price_calculation_detail,21,optional" frugal:"21,optional,PriceCalculationDetail" json:"price_calculation_detail,omitempty"`
}

func NewGoods() *Goods {
	return &Goods{}
}

func (p *Goods) InitDefault() {
}

func (p *Goods) GetImgURL() (v string) {
	return p.ImgURL
}

func (p *Goods) GetTitle() (v string) {
	return p.Title
}

func (p *Goods) GetSubTitle() (v string) {
	return p.SubTitle
}

func (p *Goods) GetLabels() (v string) {
	return p.Labels
}

func (p *Goods) GetDateRule() (v string) {
	return p.DateRule
}

func (p *Goods) GetPoiID() (v string) {
	return p.PoiID
}

func (p *Goods) GetGoodsID() (v string) {
	return p.GoodsID
}

func (p *Goods) GetGoodsIDType() (v int32) {
	return p.GoodsIDType
}

func (p *Goods) GetOriginPrice() (v int64) {
	return p.OriginPrice
}

func (p *Goods) GetPrice() (v int64) {
	return p.Price
}

func (p *Goods) GetQuantity() (v int32) {
	return p.Quantity
}

func (p *Goods) GetItemOrderIDList() (v []string) {
	return p.ItemOrderIDList
}

func (p *Goods) GetItemOrderInfo() (v []*ItemOrderInfo) {
	return p.ItemOrderInfo
}

var Goods_GoodsBookInfo_DEFAULT *GoodsBookInfo

func (p *Goods) GetGoodsBookInfo() (v *GoodsBookInfo) {
	if !p.IsSetGoodsBookInfo() {
		return Goods_GoodsBookInfo_DEFAULT
	}
	return p.GoodsBookInfo
}

var Goods_PriceCalculationDetail_DEFAULT *PriceCalculationDetail

func (p *Goods) GetPriceCalculationDetail() (v *PriceCalculationDetail) {
	if !p.IsSetPriceCalculationDetail() {
		return Goods_PriceCalculationDetail_DEFAULT
	}
	return p.PriceCalculationDetail
}
func (p *Goods) SetImgURL(val string) {
	p.ImgURL = val
}
func (p *Goods) SetTitle(val string) {
	p.Title = val
}
func (p *Goods) SetSubTitle(val string) {
	p.SubTitle = val
}
func (p *Goods) SetLabels(val string) {
	p.Labels = val
}
func (p *Goods) SetDateRule(val string) {
	p.DateRule = val
}
func (p *Goods) SetPoiID(val string) {
	p.PoiID = val
}
func (p *Goods) SetGoodsID(val string) {
	p.GoodsID = val
}
func (p *Goods) SetGoodsIDType(val int32) {
	p.GoodsIDType = val
}
func (p *Goods) SetOriginPrice(val int64) {
	p.OriginPrice = val
}
func (p *Goods) SetPrice(val int64) {
	p.Price = val
}
func (p *Goods) SetQuantity(val int32) {
	p.Quantity = val
}
func (p *Goods) SetItemOrderIDList(val []string) {
	p.ItemOrderIDList = val
}
func (p *Goods) SetItemOrderInfo(val []*ItemOrderInfo) {
	p.ItemOrderInfo = val
}
func (p *Goods) SetGoodsBookInfo(val *GoodsBookInfo) {
	p.GoodsBookInfo = val
}
func (p *Goods) SetPriceCalculationDetail(val *PriceCalculationDetail) {
	p.PriceCalculationDetail = val
}

var fieldIDToName_Goods = map[int16]string{
	1:  "img_url",
	2:  "title",
	3:  "sub_title",
	4:  "labels",
	5:  "date_rule",
	6:  "poi_id",
	7:  "goods_id",
	8:  "goods_id_type",
	9:  "origin_price",
	10: "price",
	11: "quantity",
	12: "item_order_id_list",
	13: "item_order_info",
	20: "goods_book_info",
	21: "price_calculation_detail",
}

func (p *Goods) IsSetGoodsBookInfo() bool {
	return p.GoodsBookInfo != nil
}

func (p *Goods) IsSetPriceCalculationDetail() bool {
	return p.PriceCalculationDetail != nil
}

func (p *Goods) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("Goods")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Goods[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Goods) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ImgURL = _field
	return nil
}
func (p *Goods) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Title = _field
	return nil
}
func (p *Goods) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SubTitle = _field
	return nil
}
func (p *Goods) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Labels = _field
	return nil
}
func (p *Goods) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DateRule = _field
	return nil
}
func (p *Goods) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PoiID = _field
	return nil
}
func (p *Goods) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GoodsID = _field
	return nil
}
func (p *Goods) ReadField8(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GoodsIDType = _field
	return nil
}
func (p *Goods) ReadField9(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OriginPrice = _field
	return nil
}
func (p *Goods) ReadField10(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Price = _field
	return nil
}
func (p *Goods) ReadField11(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Quantity = _field
	return nil
}
func (p *Goods) ReadField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ItemOrderIDList = _field
	return nil
}
func (p *Goods) ReadField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ItemOrderInfo, 0, size)
	values := make([]ItemOrderInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ItemOrderInfo = _field
	return nil
}
func (p *Goods) ReadField20(iprot thrift.TProtocol) error {
	_field := NewGoodsBookInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.GoodsBookInfo = _field
	return nil
}
func (p *Goods) ReadField21(iprot thrift.TProtocol) error {
	_field := NewPriceCalculationDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.PriceCalculationDetail = _field
	return nil
}

func (p *Goods) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("Goods")

	var fieldId int16
	if err = oprot.WriteStructBegin("Goods"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Goods) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("img_url", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ImgURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Goods) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("title", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Title); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Goods) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sub_title", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SubTitle); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Goods) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("labels", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Labels); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *Goods) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("date_rule", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DateRule); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *Goods) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("poi_id", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PoiID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *Goods) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("goods_id", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GoodsID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *Goods) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("goods_id_type", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.GoodsIDType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *Goods) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("origin_price", thrift.I64, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.OriginPrice); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *Goods) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("price", thrift.I64, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Price); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *Goods) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("quantity", thrift.I32, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Quantity); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *Goods) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("item_order_id_list", thrift.LIST, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.ItemOrderIDList)); err != nil {
		return err
	}
	for _, v := range p.ItemOrderIDList {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *Goods) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("item_order_info", thrift.LIST, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ItemOrderInfo)); err != nil {
		return err
	}
	for _, v := range p.ItemOrderInfo {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *Goods) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetGoodsBookInfo() {
		if err = oprot.WriteFieldBegin("goods_book_info", thrift.STRUCT, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.GoodsBookInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}
func (p *Goods) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetPriceCalculationDetail() {
		if err = oprot.WriteFieldBegin("price_calculation_detail", thrift.STRUCT, 21); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.PriceCalculationDetail.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}

func (p *Goods) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Goods(%+v)", *p)

}

type ItemOrderInfoListItem struct {
	GoodsID      string   `thrift:"goods_id,1" frugal:"1,default,string" json:"goods_id"`
	GoodsIDType  int32    `thrift:"goods_id_type,2" frugal:"2,default,i32" json:"goods_id_type"`
	SkuID        string   `thrift:"sku_id,3" frugal:"3,default,string" json:"sku_id"`
	SkuIDType    int32    `thrift:"sku_id_type,4" frugal:"4,default,i32" json:"sku_id_type"`
	ItemOrderID  string   `thrift:"item_order_id,5" frugal:"5,default,string" json:"item_order_id"`
	Price        int64    `thrift:"price,6" frugal:"6,default,i64" json:"price"`
	CouponItemID string   `thrift:"coupon_item_id,7" frugal:"7,default,string" json:"coupon_item_id"`
	SubItemList  []string `thrift:"sub_item_list,8,optional" frugal:"8,optional,list<string>" json:"sub_item_list,omitempty"`
}

func NewItemOrderInfoListItem() *ItemOrderInfoListItem {
	return &ItemOrderInfoListItem{}
}

func (p *ItemOrderInfoListItem) InitDefault() {
}

func (p *ItemOrderInfoListItem) GetGoodsID() (v string) {
	return p.GoodsID
}

func (p *ItemOrderInfoListItem) GetGoodsIDType() (v int32) {
	return p.GoodsIDType
}

func (p *ItemOrderInfoListItem) GetSkuID() (v string) {
	return p.SkuID
}

func (p *ItemOrderInfoListItem) GetSkuIDType() (v int32) {
	return p.SkuIDType
}

func (p *ItemOrderInfoListItem) GetItemOrderID() (v string) {
	return p.ItemOrderID
}

func (p *ItemOrderInfoListItem) GetPrice() (v int64) {
	return p.Price
}

func (p *ItemOrderInfoListItem) GetCouponItemID() (v string) {
	return p.CouponItemID
}

var ItemOrderInfoListItem_SubItemList_DEFAULT []string

func (p *ItemOrderInfoListItem) GetSubItemList() (v []string) {
	if !p.IsSetSubItemList() {
		return ItemOrderInfoListItem_SubItemList_DEFAULT
	}
	return p.SubItemList
}
func (p *ItemOrderInfoListItem) SetGoodsID(val string) {
	p.GoodsID = val
}
func (p *ItemOrderInfoListItem) SetGoodsIDType(val int32) {
	p.GoodsIDType = val
}
func (p *ItemOrderInfoListItem) SetSkuID(val string) {
	p.SkuID = val
}
func (p *ItemOrderInfoListItem) SetSkuIDType(val int32) {
	p.SkuIDType = val
}
func (p *ItemOrderInfoListItem) SetItemOrderID(val string) {
	p.ItemOrderID = val
}
func (p *ItemOrderInfoListItem) SetPrice(val int64) {
	p.Price = val
}
func (p *ItemOrderInfoListItem) SetCouponItemID(val string) {
	p.CouponItemID = val
}
func (p *ItemOrderInfoListItem) SetSubItemList(val []string) {
	p.SubItemList = val
}

var fieldIDToName_ItemOrderInfoListItem = map[int16]string{
	1: "goods_id",
	2: "goods_id_type",
	3: "sku_id",
	4: "sku_id_type",
	5: "item_order_id",
	6: "price",
	7: "coupon_item_id",
	8: "sub_item_list",
}

func (p *ItemOrderInfoListItem) IsSetSubItemList() bool {
	return p.SubItemList != nil
}

func (p *ItemOrderInfoListItem) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ItemOrderInfoListItem")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ItemOrderInfoListItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ItemOrderInfoListItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GoodsID = _field
	return nil
}
func (p *ItemOrderInfoListItem) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GoodsIDType = _field
	return nil
}
func (p *ItemOrderInfoListItem) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SkuID = _field
	return nil
}
func (p *ItemOrderInfoListItem) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SkuIDType = _field
	return nil
}
func (p *ItemOrderInfoListItem) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ItemOrderID = _field
	return nil
}
func (p *ItemOrderInfoListItem) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Price = _field
	return nil
}
func (p *ItemOrderInfoListItem) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CouponItemID = _field
	return nil
}
func (p *ItemOrderInfoListItem) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SubItemList = _field
	return nil
}

func (p *ItemOrderInfoListItem) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ItemOrderInfoListItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("ItemOrderInfoListItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ItemOrderInfoListItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("goods_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GoodsID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ItemOrderInfoListItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("goods_id_type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.GoodsIDType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ItemOrderInfoListItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sku_id", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SkuID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ItemOrderInfoListItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("sku_id_type", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.SkuIDType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ItemOrderInfoListItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("item_order_id", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ItemOrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ItemOrderInfoListItem) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("price", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Price); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ItemOrderInfoListItem) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("coupon_item_id", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CouponItemID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *ItemOrderInfoListItem) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSubItemList() {
		if err = oprot.WriteFieldBegin("sub_item_list", thrift.LIST, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.SubItemList)); err != nil {
			return err
		}
		for _, v := range p.SubItemList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *ItemOrderInfoListItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ItemOrderInfoListItem(%+v)", *p)

}

type LifeCreateRequest struct {
	OrderID             string                   `thrift:"order_id,1" frugal:"1,default,string" json:"order_id"`
	Goods               []*Goods                 `thrift:"goods,2" frugal:"2,default,list<Goods>" json:"goods"`
	ItemOrderInfoList   []*ItemOrderInfoListItem `thrift:"item_order_info_list,3" frugal:"3,default,list<ItemOrderInfoListItem>" json:"item_order_info_list"`
	TotalAmount         int64                    `thrift:"total_amount,4" frugal:"4,default,i64" json:"total_amount"`
	Discount            int64                    `thrift:"discount,5" frugal:"5,default,i64" json:"discount"`
	CpExtra             string                   `thrift:"cp_extra,6" frugal:"6,default,string" json:"cp_extra"`
	CreateOrderTime     int64                    `thrift:"create_order_time,7" frugal:"7,default,i64" json:"create_order_time"`
	OpenID              string                   `thrift:"open_id,8" frugal:"8,default,string" json:"open_id"`
	PhoneNum            string                   `thrift:"phone_num,9" frugal:"9,default,string" json:"phone_num"`
	ContactName         string                   `thrift:"contact_name,10" frugal:"10,default,string" json:"contact_name"`
	AppID               string                   `thrift:"app_id,11" frugal:"11,default,string" json:"app_id"`
	UnionID             string                   `thrift:"union_id,12" frugal:"12,default,string" json:"union_id"`
	DeliveryType        int32                    `thrift:"delivery_type,13" frugal:"13,default,i32" json:"delivery_type"`
	UseLocalLifeAccount int32                    `thrift:"use_local_life_account,14" frugal:"14,default,i32" json:"use_local_life_account"`
	BizLine             int32                    `thrift:"biz_line,15" frugal:"15,default,i32" json:"biz_line"`
	FulfillType         int32                    `thrift:"fulfill_type,16" frugal:"16,default,i32" json:"fulfill_type"`
	FeeList             []string                 `thrift:"fee_list,17,optional" frugal:"17,optional,list<string>" json:"fee_list,omitempty"`
}

func NewLifeCreateRequest() *LifeCreateRequest {
	return &LifeCreateRequest{}
}

func (p *LifeCreateRequest) InitDefault() {
}

func (p *LifeCreateRequest) GetOrderID() (v string) {
	return p.OrderID
}

func (p *LifeCreateRequest) GetGoods() (v []*Goods) {
	return p.Goods
}

func (p *LifeCreateRequest) GetItemOrderInfoList() (v []*ItemOrderInfoListItem) {
	return p.ItemOrderInfoList
}

func (p *LifeCreateRequest) GetTotalAmount() (v int64) {
	return p.TotalAmount
}

func (p *LifeCreateRequest) GetDiscount() (v int64) {
	return p.Discount
}

func (p *LifeCreateRequest) GetCpExtra() (v string) {
	return p.CpExtra
}

func (p *LifeCreateRequest) GetCreateOrderTime() (v int64) {
	return p.CreateOrderTime
}

func (p *LifeCreateRequest) GetOpenID() (v string) {
	return p.OpenID
}

func (p *LifeCreateRequest) GetPhoneNum() (v string) {
	return p.PhoneNum
}

func (p *LifeCreateRequest) GetContactName() (v string) {
	return p.ContactName
}

func (p *LifeCreateRequest) GetAppID() (v string) {
	return p.AppID
}

func (p *LifeCreateRequest) GetUnionID() (v string) {
	return p.UnionID
}

func (p *LifeCreateRequest) GetDeliveryType() (v int32) {
	return p.DeliveryType
}

func (p *LifeCreateRequest) GetUseLocalLifeAccount() (v int32) {
	return p.UseLocalLifeAccount
}

func (p *LifeCreateRequest) GetBizLine() (v int32) {
	return p.BizLine
}

func (p *LifeCreateRequest) GetFulfillType() (v int32) {
	return p.FulfillType
}

var LifeCreateRequest_FeeList_DEFAULT []string

func (p *LifeCreateRequest) GetFeeList() (v []string) {
	if !p.IsSetFeeList() {
		return LifeCreateRequest_FeeList_DEFAULT
	}
	return p.FeeList
}
func (p *LifeCreateRequest) SetOrderID(val string) {
	p.OrderID = val
}
func (p *LifeCreateRequest) SetGoods(val []*Goods) {
	p.Goods = val
}
func (p *LifeCreateRequest) SetItemOrderInfoList(val []*ItemOrderInfoListItem) {
	p.ItemOrderInfoList = val
}
func (p *LifeCreateRequest) SetTotalAmount(val int64) {
	p.TotalAmount = val
}
func (p *LifeCreateRequest) SetDiscount(val int64) {
	p.Discount = val
}
func (p *LifeCreateRequest) SetCpExtra(val string) {
	p.CpExtra = val
}
func (p *LifeCreateRequest) SetCreateOrderTime(val int64) {
	p.CreateOrderTime = val
}
func (p *LifeCreateRequest) SetOpenID(val string) {
	p.OpenID = val
}
func (p *LifeCreateRequest) SetPhoneNum(val string) {
	p.PhoneNum = val
}
func (p *LifeCreateRequest) SetContactName(val string) {
	p.ContactName = val
}
func (p *LifeCreateRequest) SetAppID(val string) {
	p.AppID = val
}
func (p *LifeCreateRequest) SetUnionID(val string) {
	p.UnionID = val
}
func (p *LifeCreateRequest) SetDeliveryType(val int32) {
	p.DeliveryType = val
}
func (p *LifeCreateRequest) SetUseLocalLifeAccount(val int32) {
	p.UseLocalLifeAccount = val
}
func (p *LifeCreateRequest) SetBizLine(val int32) {
	p.BizLine = val
}
func (p *LifeCreateRequest) SetFulfillType(val int32) {
	p.FulfillType = val
}
func (p *LifeCreateRequest) SetFeeList(val []string) {
	p.FeeList = val
}

var fieldIDToName_LifeCreateRequest = map[int16]string{
	1:  "order_id",
	2:  "goods",
	3:  "item_order_info_list",
	4:  "total_amount",
	5:  "discount",
	6:  "cp_extra",
	7:  "create_order_time",
	8:  "open_id",
	9:  "phone_num",
	10: "contact_name",
	11: "app_id",
	12: "union_id",
	13: "delivery_type",
	14: "use_local_life_account",
	15: "biz_line",
	16: "fulfill_type",
	17: "fee_list",
}

func (p *LifeCreateRequest) IsSetFeeList() bool {
	return p.FeeList != nil
}

func (p *LifeCreateRequest) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeCreateRequest")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeCreateRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LifeCreateRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *LifeCreateRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*Goods, 0, size)
	values := make([]Goods, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Goods = _field
	return nil
}
func (p *LifeCreateRequest) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ItemOrderInfoListItem, 0, size)
	values := make([]ItemOrderInfoListItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ItemOrderInfoList = _field
	return nil
}
func (p *LifeCreateRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalAmount = _field
	return nil
}
func (p *LifeCreateRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Discount = _field
	return nil
}
func (p *LifeCreateRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CpExtra = _field
	return nil
}
func (p *LifeCreateRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateOrderTime = _field
	return nil
}
func (p *LifeCreateRequest) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OpenID = _field
	return nil
}
func (p *LifeCreateRequest) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PhoneNum = _field
	return nil
}
func (p *LifeCreateRequest) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ContactName = _field
	return nil
}
func (p *LifeCreateRequest) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppID = _field
	return nil
}
func (p *LifeCreateRequest) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UnionID = _field
	return nil
}
func (p *LifeCreateRequest) ReadField13(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DeliveryType = _field
	return nil
}
func (p *LifeCreateRequest) ReadField14(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UseLocalLifeAccount = _field
	return nil
}
func (p *LifeCreateRequest) ReadField15(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.BizLine = _field
	return nil
}
func (p *LifeCreateRequest) ReadField16(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.FulfillType = _field
	return nil
}
func (p *LifeCreateRequest) ReadField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FeeList = _field
	return nil
}

func (p *LifeCreateRequest) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeCreateRequest")

	var fieldId int16
	if err = oprot.WriteStructBegin("LifeCreateRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LifeCreateRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("goods", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Goods)); err != nil {
		return err
	}
	for _, v := range p.Goods {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("item_order_info_list", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ItemOrderInfoList)); err != nil {
		return err
	}
	for _, v := range p.ItemOrderInfoList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total_amount", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TotalAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("discount", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Discount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cp_extra", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CpExtra); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("create_order_time", thrift.I64, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreateOrderTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("open_id", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OpenID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("phone_num", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PhoneNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("contact_name", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ContactName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_id", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("union_id", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UnionID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("delivery_type", thrift.I32, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.DeliveryType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("use_local_life_account", thrift.I32, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.UseLocalLifeAccount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("biz_line", thrift.I32, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.BizLine); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("fulfill_type", thrift.I32, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.FulfillType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}
func (p *LifeCreateRequest) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetFeeList() {
		if err = oprot.WriteFieldBegin("fee_list", thrift.LIST, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.FeeList)); err != nil {
			return err
		}
		for _, v := range p.FeeList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *LifeCreateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeCreateRequest(%+v)", *p)

}

type OrderGoodsInfo struct {
	GoodsID string `thrift:"goods_id,1" frugal:"1,default,string" json:"goods_id"`
}

func NewOrderGoodsInfo() *OrderGoodsInfo {
	return &OrderGoodsInfo{}
}

func (p *OrderGoodsInfo) InitDefault() {
}

func (p *OrderGoodsInfo) GetGoodsID() (v string) {
	return p.GoodsID
}
func (p *OrderGoodsInfo) SetGoodsID(val string) {
	p.GoodsID = val
}

var fieldIDToName_OrderGoodsInfo = map[int16]string{
	1: "goods_id",
}

func (p *OrderGoodsInfo) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OrderGoodsInfo")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderGoodsInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OrderGoodsInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GoodsID = _field
	return nil
}

func (p *OrderGoodsInfo) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OrderGoodsInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("OrderGoodsInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OrderGoodsInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("goods_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GoodsID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OrderGoodsInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderGoodsInfo(%+v)", *p)

}

type OrderValidTime struct {
	GoodsID        string `thrift:"goods_id,1" frugal:"1,default,string" json:"goods_id"`
	ValidStartTime int64  `thrift:"valid_start_time,2" frugal:"2,default,i64" json:"valid_start_time"`
	ValidEndTime   int64  `thrift:"valid_end_time,3" frugal:"3,default,i64" json:"valid_end_time"`
	ValidDuration  int64  `thrift:"valid_duration,4" frugal:"4,default,i64" json:"valid_duration"`
}

func NewOrderValidTime() *OrderValidTime {
	return &OrderValidTime{}
}

func (p *OrderValidTime) InitDefault() {
}

func (p *OrderValidTime) GetGoodsID() (v string) {
	return p.GoodsID
}

func (p *OrderValidTime) GetValidStartTime() (v int64) {
	return p.ValidStartTime
}

func (p *OrderValidTime) GetValidEndTime() (v int64) {
	return p.ValidEndTime
}

func (p *OrderValidTime) GetValidDuration() (v int64) {
	return p.ValidDuration
}
func (p *OrderValidTime) SetGoodsID(val string) {
	p.GoodsID = val
}
func (p *OrderValidTime) SetValidStartTime(val int64) {
	p.ValidStartTime = val
}
func (p *OrderValidTime) SetValidEndTime(val int64) {
	p.ValidEndTime = val
}
func (p *OrderValidTime) SetValidDuration(val int64) {
	p.ValidDuration = val
}

var fieldIDToName_OrderValidTime = map[int16]string{
	1: "goods_id",
	2: "valid_start_time",
	3: "valid_end_time",
	4: "valid_duration",
}

func (p *OrderValidTime) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OrderValidTime")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderValidTime[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OrderValidTime) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.GoodsID = _field
	return nil
}
func (p *OrderValidTime) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ValidStartTime = _field
	return nil
}
func (p *OrderValidTime) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ValidEndTime = _field
	return nil
}
func (p *OrderValidTime) ReadField4(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ValidDuration = _field
	return nil
}

func (p *OrderValidTime) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OrderValidTime")

	var fieldId int16
	if err = oprot.WriteStructBegin("OrderValidTime"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OrderValidTime) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("goods_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.GoodsID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *OrderValidTime) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("valid_start_time", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ValidStartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *OrderValidTime) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("valid_end_time", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ValidEndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *OrderValidTime) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("valid_duration", thrift.I64, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ValidDuration); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *OrderValidTime) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderValidTime(%+v)", *p)

}

type LifeCreateData struct {
	OutOrderNo       string            `thrift:"out_order_no,1" frugal:"1,default,string" json:"out_order_no"`
	PayExpireSeconds int32             `thrift:"pay_expire_seconds,2" frugal:"2,default,i32" json:"pay_expire_seconds"`
	OrderEntrySchema *OrderEntrySchema `thrift:"order_entry_schema,3" frugal:"3,default,OrderEntrySchema" json:"order_entry_schema"`
	OrderValidTime   []*OrderValidTime `thrift:"order_valid_time,4" frugal:"4,default,list<OrderValidTime>" json:"order_valid_time"`
	OrderGoodsInfo   []*OrderGoodsInfo `thrift:"order_goods_info,5" frugal:"5,default,list<OrderGoodsInfo>" json:"order_goods_info"`
	CpDeliveryType   int64             `thrift:"cp_delivery_type,6" frugal:"6,default,i64" json:"cp_delivery_type"`
}

func NewLifeCreateData() *LifeCreateData {
	return &LifeCreateData{}
}

func (p *LifeCreateData) InitDefault() {
}

func (p *LifeCreateData) GetOutOrderNo() (v string) {
	return p.OutOrderNo
}

func (p *LifeCreateData) GetPayExpireSeconds() (v int32) {
	return p.PayExpireSeconds
}

var LifeCreateData_OrderEntrySchema_DEFAULT *OrderEntrySchema

func (p *LifeCreateData) GetOrderEntrySchema() (v *OrderEntrySchema) {
	if !p.IsSetOrderEntrySchema() {
		return LifeCreateData_OrderEntrySchema_DEFAULT
	}
	return p.OrderEntrySchema
}

func (p *LifeCreateData) GetOrderValidTime() (v []*OrderValidTime) {
	return p.OrderValidTime
}

func (p *LifeCreateData) GetOrderGoodsInfo() (v []*OrderGoodsInfo) {
	return p.OrderGoodsInfo
}

func (p *LifeCreateData) GetCpDeliveryType() (v int64) {
	return p.CpDeliveryType
}
func (p *LifeCreateData) SetOutOrderNo(val string) {
	p.OutOrderNo = val
}
func (p *LifeCreateData) SetPayExpireSeconds(val int32) {
	p.PayExpireSeconds = val
}
func (p *LifeCreateData) SetOrderEntrySchema(val *OrderEntrySchema) {
	p.OrderEntrySchema = val
}
func (p *LifeCreateData) SetOrderValidTime(val []*OrderValidTime) {
	p.OrderValidTime = val
}
func (p *LifeCreateData) SetOrderGoodsInfo(val []*OrderGoodsInfo) {
	p.OrderGoodsInfo = val
}
func (p *LifeCreateData) SetCpDeliveryType(val int64) {
	p.CpDeliveryType = val
}

var fieldIDToName_LifeCreateData = map[int16]string{
	1: "out_order_no",
	2: "pay_expire_seconds",
	3: "order_entry_schema",
	4: "order_valid_time",
	5: "order_goods_info",
	6: "cp_delivery_type",
}

func (p *LifeCreateData) IsSetOrderEntrySchema() bool {
	return p.OrderEntrySchema != nil
}

func (p *LifeCreateData) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeCreateData")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeCreateData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LifeCreateData) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OutOrderNo = _field
	return nil
}
func (p *LifeCreateData) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PayExpireSeconds = _field
	return nil
}
func (p *LifeCreateData) ReadField3(iprot thrift.TProtocol) error {
	_field := NewOrderEntrySchema()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OrderEntrySchema = _field
	return nil
}
func (p *LifeCreateData) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*OrderValidTime, 0, size)
	values := make([]OrderValidTime, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.OrderValidTime = _field
	return nil
}
func (p *LifeCreateData) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*OrderGoodsInfo, 0, size)
	values := make([]OrderGoodsInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.OrderGoodsInfo = _field
	return nil
}
func (p *LifeCreateData) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CpDeliveryType = _field
	return nil
}

func (p *LifeCreateData) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeCreateData")

	var fieldId int16
	if err = oprot.WriteStructBegin("LifeCreateData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LifeCreateData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("out_order_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OutOrderNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LifeCreateData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("pay_expire_seconds", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PayExpireSeconds); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LifeCreateData) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_entry_schema", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.OrderEntrySchema.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *LifeCreateData) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_valid_time", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.OrderValidTime)); err != nil {
		return err
	}
	for _, v := range p.OrderValidTime {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *LifeCreateData) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_goods_info", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.OrderGoodsInfo)); err != nil {
		return err
	}
	for _, v := range p.OrderGoodsInfo {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *LifeCreateData) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("cp_delivery_type", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CpDeliveryType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *LifeCreateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeCreateData(%+v)", *p)

}

type RefundItemDetail struct {
	ItemOrderQuantity int32              `thrift:"item_order_quantity,1" frugal:"1,default,i32" json:"item_order_quantity"`
	ItemOrderDetail   []*ItemOrderDetail `thrift:"item_order_detail,2" frugal:"2,default,list<ItemOrderDetail>" json:"item_order_detail"`
}

func NewRefundItemDetail() *RefundItemDetail {
	return &RefundItemDetail{}
}

func (p *RefundItemDetail) InitDefault() {
}

func (p *RefundItemDetail) GetItemOrderQuantity() (v int32) {
	return p.ItemOrderQuantity
}

func (p *RefundItemDetail) GetItemOrderDetail() (v []*ItemOrderDetail) {
	return p.ItemOrderDetail
}
func (p *RefundItemDetail) SetItemOrderQuantity(val int32) {
	p.ItemOrderQuantity = val
}
func (p *RefundItemDetail) SetItemOrderDetail(val []*ItemOrderDetail) {
	p.ItemOrderDetail = val
}

var fieldIDToName_RefundItemDetail = map[int16]string{
	1: "item_order_quantity",
	2: "item_order_detail",
}

func (p *RefundItemDetail) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RefundItemDetail")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RefundItemDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RefundItemDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ItemOrderQuantity = _field
	return nil
}
func (p *RefundItemDetail) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ItemOrderDetail, 0, size)
	values := make([]ItemOrderDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ItemOrderDetail = _field
	return nil
}

func (p *RefundItemDetail) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("RefundItemDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("RefundItemDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RefundItemDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("item_order_quantity", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ItemOrderQuantity); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *RefundItemDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("item_order_detail", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ItemOrderDetail)); err != nil {
		return err
	}
	for _, v := range p.ItemOrderDetail {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RefundItemDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RefundItemDetail(%+v)", *p)

}

type LifeRefundRequest struct {
	AppID               string            `thrift:"app_id,1" frugal:"1,default,string" json:"app_id"`
	OpenID              string            `thrift:"open_id,2" frugal:"2,default,string" json:"open_id"`
	RefundID            string            `thrift:"refund_id,3" frugal:"3,default,string" json:"refund_id"`
	OrderID             string            `thrift:"order_id,4" frugal:"4,default,string" json:"order_id"`
	OutOrderNo          string            `thrift:"out_order_no,5" frugal:"5,default,string" json:"out_order_no"`
	RefundTotalAmount   int64             `thrift:"refund_total_amount,6" frugal:"6,default,i64" json:"refund_total_amount"`
	NeedRefundAudit     int32             `thrift:"need_refund_audit,7" frugal:"7,default,i32" json:"need_refund_audit"`
	RefundAuditDeadline int64             `thrift:"refund_audit_deadline,8" frugal:"8,default,i64" json:"refund_audit_deadline"`
	RefundItemDetail    *RefundItemDetail `thrift:"refund_item_detail,9,optional" frugal:"9,optional,RefundItemDetail" json:"refund_item_detail,omitempty"`
	RefundReason        []string          `thrift:"refund_reason,10" frugal:"10,default,list<string>" json:"refund_reason"`
	CreateRefundTime    int64             `thrift:"create_refund_time,11" frugal:"11,default,i64" json:"create_refund_time"`
	RefundSource        int32             `thrift:"refund_source,12" frugal:"12,default,i32" json:"refund_source"`
	RefundDescription   *string           `thrift:"refund_description,13,optional" frugal:"13,optional,string" json:"refund_description,omitempty"`
	CpExtra             *string           `thrift:"cp_extra,14,optional" frugal:"14,optional,string" json:"cp_extra,omitempty"`
}

func NewLifeRefundRequest() *LifeRefundRequest {
	return &LifeRefundRequest{}
}

func (p *LifeRefundRequest) InitDefault() {
}

func (p *LifeRefundRequest) GetAppID() (v string) {
	return p.AppID
}

func (p *LifeRefundRequest) GetOpenID() (v string) {
	return p.OpenID
}

func (p *LifeRefundRequest) GetRefundID() (v string) {
	return p.RefundID
}

func (p *LifeRefundRequest) GetOrderID() (v string) {
	return p.OrderID
}

func (p *LifeRefundRequest) GetOutOrderNo() (v string) {
	return p.OutOrderNo
}

func (p *LifeRefundRequest) GetRefundTotalAmount() (v int64) {
	return p.RefundTotalAmount
}

func (p *LifeRefundRequest) GetNeedRefundAudit() (v int32) {
	return p.NeedRefundAudit
}

func (p *LifeRefundRequest) GetRefundAuditDeadline() (v int64) {
	return p.RefundAuditDeadline
}

var LifeRefundRequest_RefundItemDetail_DEFAULT *RefundItemDetail

func (p *LifeRefundRequest) GetRefundItemDetail() (v *RefundItemDetail) {
	if !p.IsSetRefundItemDetail() {
		return LifeRefundRequest_RefundItemDetail_DEFAULT
	}
	return p.RefundItemDetail
}

func (p *LifeRefundRequest) GetRefundReason() (v []string) {
	return p.RefundReason
}

func (p *LifeRefundRequest) GetCreateRefundTime() (v int64) {
	return p.CreateRefundTime
}

func (p *LifeRefundRequest) GetRefundSource() (v int32) {
	return p.RefundSource
}

var LifeRefundRequest_RefundDescription_DEFAULT string

func (p *LifeRefundRequest) GetRefundDescription() (v string) {
	if !p.IsSetRefundDescription() {
		return LifeRefundRequest_RefundDescription_DEFAULT
	}
	return *p.RefundDescription
}

var LifeRefundRequest_CpExtra_DEFAULT string

func (p *LifeRefundRequest) GetCpExtra() (v string) {
	if !p.IsSetCpExtra() {
		return LifeRefundRequest_CpExtra_DEFAULT
	}
	return *p.CpExtra
}
func (p *LifeRefundRequest) SetAppID(val string) {
	p.AppID = val
}
func (p *LifeRefundRequest) SetOpenID(val string) {
	p.OpenID = val
}
func (p *LifeRefundRequest) SetRefundID(val string) {
	p.RefundID = val
}
func (p *LifeRefundRequest) SetOrderID(val string) {
	p.OrderID = val
}
func (p *LifeRefundRequest) SetOutOrderNo(val string) {
	p.OutOrderNo = val
}
func (p *LifeRefundRequest) SetRefundTotalAmount(val int64) {
	p.RefundTotalAmount = val
}
func (p *LifeRefundRequest) SetNeedRefundAudit(val int32) {
	p.NeedRefundAudit = val
}
func (p *LifeRefundRequest) SetRefundAuditDeadline(val int64) {
	p.RefundAuditDeadline = val
}
func (p *LifeRefundRequest) SetRefundItemDetail(val *RefundItemDetail) {
	p.RefundItemDetail = val
}
func (p *LifeRefundRequest) SetRefundReason(val []string) {
	p.RefundReason = val
}
func (p *LifeRefundRequest) SetCreateRefundTime(val int64) {
	p.CreateRefundTime = val
}
func (p *LifeRefundRequest) SetRefundSource(val int32) {
	p.RefundSource = val
}
func (p *LifeRefundRequest) SetRefundDescription(val *string) {
	p.RefundDescription = val
}
func (p *LifeRefundRequest) SetCpExtra(val *string) {
	p.CpExtra = val
}

var fieldIDToName_LifeRefundRequest = map[int16]string{
	1:  "app_id",
	2:  "open_id",
	3:  "refund_id",
	4:  "order_id",
	5:  "out_order_no",
	6:  "refund_total_amount",
	7:  "need_refund_audit",
	8:  "refund_audit_deadline",
	9:  "refund_item_detail",
	10: "refund_reason",
	11: "create_refund_time",
	12: "refund_source",
	13: "refund_description",
	14: "cp_extra",
}

func (p *LifeRefundRequest) IsSetRefundItemDetail() bool {
	return p.RefundItemDetail != nil
}

func (p *LifeRefundRequest) IsSetRefundDescription() bool {
	return p.RefundDescription != nil
}

func (p *LifeRefundRequest) IsSetCpExtra() bool {
	return p.CpExtra != nil
}

func (p *LifeRefundRequest) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeRefundRequest")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeRefundRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LifeRefundRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppID = _field
	return nil
}
func (p *LifeRefundRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OpenID = _field
	return nil
}
func (p *LifeRefundRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RefundID = _field
	return nil
}
func (p *LifeRefundRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderID = _field
	return nil
}
func (p *LifeRefundRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OutOrderNo = _field
	return nil
}
func (p *LifeRefundRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RefundTotalAmount = _field
	return nil
}
func (p *LifeRefundRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NeedRefundAudit = _field
	return nil
}
func (p *LifeRefundRequest) ReadField8(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RefundAuditDeadline = _field
	return nil
}
func (p *LifeRefundRequest) ReadField9(iprot thrift.TProtocol) error {
	_field := NewRefundItemDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.RefundItemDetail = _field
	return nil
}
func (p *LifeRefundRequest) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RefundReason = _field
	return nil
}
func (p *LifeRefundRequest) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateRefundTime = _field
	return nil
}
func (p *LifeRefundRequest) ReadField12(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RefundSource = _field
	return nil
}
func (p *LifeRefundRequest) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RefundDescription = _field
	return nil
}
func (p *LifeRefundRequest) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CpExtra = _field
	return nil
}

func (p *LifeRefundRequest) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeRefundRequest")

	var fieldId int16
	if err = oprot.WriteStructBegin("LifeRefundRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LifeRefundRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("open_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OpenID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_id", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RefundID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_id", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("out_order_no", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OutOrderNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_total_amount", thrift.I64, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RefundTotalAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("need_refund_audit", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.NeedRefundAudit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_audit_deadline", thrift.I64, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RefundAuditDeadline); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetRefundItemDetail() {
		if err = oprot.WriteFieldBegin("refund_item_detail", thrift.STRUCT, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.RefundItemDetail.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_reason", thrift.LIST, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.RefundReason)); err != nil {
		return err
	}
	for _, v := range p.RefundReason {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("create_refund_time", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.CreateRefundTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_source", thrift.I32, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.RefundSource); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetRefundDescription() {
		if err = oprot.WriteFieldBegin("refund_description", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RefundDescription); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}
func (p *LifeRefundRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetCpExtra() {
		if err = oprot.WriteFieldBegin("cp_extra", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CpExtra); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *LifeRefundRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeRefundRequest(%+v)", *p)

}

type LifeRefundData struct {
	OutRefundNo      string            `thrift:"out_refund_no,1" frugal:"1,default,string" json:"out_refund_no"`
	OrderEntrySchema *OrderEntrySchema `thrift:"order_entry_schema,2" frugal:"2,default,OrderEntrySchema" json:"order_entry_schema"`
	NotifyURL        string            `thrift:"notify_url,3" frugal:"3,default,string" json:"notify_url"`
}

func NewLifeRefundData() *LifeRefundData {
	return &LifeRefundData{}
}

func (p *LifeRefundData) InitDefault() {
}

func (p *LifeRefundData) GetOutRefundNo() (v string) {
	return p.OutRefundNo
}

var LifeRefundData_OrderEntrySchema_DEFAULT *OrderEntrySchema

func (p *LifeRefundData) GetOrderEntrySchema() (v *OrderEntrySchema) {
	if !p.IsSetOrderEntrySchema() {
		return LifeRefundData_OrderEntrySchema_DEFAULT
	}
	return p.OrderEntrySchema
}

func (p *LifeRefundData) GetNotifyURL() (v string) {
	return p.NotifyURL
}
func (p *LifeRefundData) SetOutRefundNo(val string) {
	p.OutRefundNo = val
}
func (p *LifeRefundData) SetOrderEntrySchema(val *OrderEntrySchema) {
	p.OrderEntrySchema = val
}
func (p *LifeRefundData) SetNotifyURL(val string) {
	p.NotifyURL = val
}

var fieldIDToName_LifeRefundData = map[int16]string{
	1: "out_refund_no",
	2: "order_entry_schema",
	3: "notify_url",
}

func (p *LifeRefundData) IsSetOrderEntrySchema() bool {
	return p.OrderEntrySchema != nil
}

func (p *LifeRefundData) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeRefundData")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LifeRefundData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *LifeRefundData) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OutRefundNo = _field
	return nil
}
func (p *LifeRefundData) ReadField2(iprot thrift.TProtocol) error {
	_field := NewOrderEntrySchema()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OrderEntrySchema = _field
	return nil
}
func (p *LifeRefundData) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NotifyURL = _field
	return nil
}

func (p *LifeRefundData) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("LifeRefundData")

	var fieldId int16
	if err = oprot.WriteStructBegin("LifeRefundData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LifeRefundData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("out_refund_no", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OutRefundNo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LifeRefundData) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("order_entry_schema", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.OrderEntrySchema.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LifeRefundData) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("notify_url", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NotifyURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *LifeRefundData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeRefundData(%+v)", *p)

}

type ItemOrderDetail struct {
	ItemOrderID    string            `thrift:"item_order_id,1" frugal:"1,default,string" json:"item_order_id"`
	RefundAmount   int64             `thrift:"refund_amount,2" frugal:"2,default,i64" json:"refund_amount"`
	ItemDeductInfo map[string]string `thrift:"item_deduct_info,3" frugal:"3,default,map<string:string>" json:"item_deduct_info"`
}

func NewItemOrderDetail() *ItemOrderDetail {
	return &ItemOrderDetail{}
}

func (p *ItemOrderDetail) InitDefault() {
}

func (p *ItemOrderDetail) GetItemOrderID() (v string) {
	return p.ItemOrderID
}

func (p *ItemOrderDetail) GetRefundAmount() (v int64) {
	return p.RefundAmount
}

func (p *ItemOrderDetail) GetItemDeductInfo() (v map[string]string) {
	return p.ItemDeductInfo
}
func (p *ItemOrderDetail) SetItemOrderID(val string) {
	p.ItemOrderID = val
}
func (p *ItemOrderDetail) SetRefundAmount(val int64) {
	p.RefundAmount = val
}
func (p *ItemOrderDetail) SetItemDeductInfo(val map[string]string) {
	p.ItemDeductInfo = val
}

var fieldIDToName_ItemOrderDetail = map[int16]string{
	1: "item_order_id",
	2: "refund_amount",
	3: "item_deduct_info",
}

func (p *ItemOrderDetail) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ItemOrderDetail")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ItemOrderDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ItemOrderDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ItemOrderID = _field
	return nil
}
func (p *ItemOrderDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RefundAmount = _field
	return nil
}
func (p *ItemOrderDetail) ReadField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.ItemDeductInfo = _field
	return nil
}

func (p *ItemOrderDetail) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ItemOrderDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("ItemOrderDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ItemOrderDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("item_order_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ItemOrderID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ItemOrderDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("refund_amount", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RefundAmount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ItemOrderDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("item_deduct_info", thrift.MAP, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ItemDeductInfo)); err != nil {
		return err
	}
	for k, v := range p.ItemDeductInfo {
		if err := oprot.WriteString(k); err != nil {
			return err
		}
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteMapEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ItemOrderDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ItemOrderDetail(%+v)", *p)

}

type OrderEntrySchema struct {
	Path   string `thrift:"path,1" frugal:"1,default,string" json:"path"`
	Params string `thrift:"params,2" frugal:"2,default,string" json:"params"`
}

func NewOrderEntrySchema() *OrderEntrySchema {
	return &OrderEntrySchema{}
}

func (p *OrderEntrySchema) InitDefault() {
}

func (p *OrderEntrySchema) GetPath() (v string) {
	return p.Path
}

func (p *OrderEntrySchema) GetParams() (v string) {
	return p.Params
}
func (p *OrderEntrySchema) SetPath(val string) {
	p.Path = val
}
func (p *OrderEntrySchema) SetParams(val string) {
	p.Params = val
}

var fieldIDToName_OrderEntrySchema = map[int16]string{
	1: "path",
	2: "params",
}

func (p *OrderEntrySchema) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OrderEntrySchema")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OrderEntrySchema[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *OrderEntrySchema) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Path = _field
	return nil
}
func (p *OrderEntrySchema) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Params = _field
	return nil
}

func (p *OrderEntrySchema) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("OrderEntrySchema")

	var fieldId int16
	if err = oprot.WriteStructBegin("OrderEntrySchema"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OrderEntrySchema) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("path", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Path); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *OrderEntrySchema) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("params", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Params); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *OrderEntrySchema) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OrderEntrySchema(%+v)", *p)

}

type ProductBase struct {
	ProductID      int64 `thrift:"product_id,1" frugal:"1,default,i64" json:"product_id"`
	ProductVersion int64 `thrift:"product_version,2" frugal:"2,default,i64" json:"product_version"`
}

func NewProductBase() *ProductBase {
	return &ProductBase{}
}

func (p *ProductBase) InitDefault() {
}

func (p *ProductBase) GetProductID() (v int64) {
	return p.ProductID
}

func (p *ProductBase) GetProductVersion() (v int64) {
	return p.ProductVersion
}
func (p *ProductBase) SetProductID(val int64) {
	p.ProductID = val
}
func (p *ProductBase) SetProductVersion(val int64) {
	p.ProductVersion = val
}

var fieldIDToName_ProductBase = map[int16]string{
	1: "product_id",
	2: "product_version",
}

func (p *ProductBase) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ProductBase")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ProductBase[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ProductBase) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProductID = _field
	return nil
}
func (p *ProductBase) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProductVersion = _field
	return nil
}

func (p *ProductBase) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("ProductBase")

	var fieldId int16
	if err = oprot.WriteStructBegin("ProductBase"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ProductBase) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("product_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ProductID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ProductBase) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("product_version", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ProductVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ProductBase) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProductBase(%+v)", *p)

}
