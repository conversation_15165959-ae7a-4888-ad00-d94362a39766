// Code generated by Kitex v1.20.3. DO NOT EDIT.

package paymentservice

import (
	client "code.byted.org/kite/kitex/client"
	payment "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	payment_tob "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment_tob"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"CreateCashPay": kitex.NewMethodInfo(
		createCashPayHandler,
		newPaymentServiceCreateCashPayArgs,
		newPaymentServiceCreateCashPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateCashRefund": kitex.NewMethodInfo(
		createCashRefundHandler,
		newPaymentServiceCreateCashRefundArgs,
		newPaymentServiceCreateCashRefundResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"WithdrawDeposit": kitex.NewMethodInfo(
		withdrawDepositHandler,
		newPaymentServiceWithdrawDepositArgs,
		newPaymentServiceWithdrawDepositResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MergeWithdrawDeposit": kitex.NewMethodInfo(
		mergeWithdrawDepositHandler,
		newPaymentServiceMergeWithdrawDepositArgs,
		newPaymentServiceMergeWithdrawDepositResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GuaranteeWithdraw": kitex.NewMethodInfo(
		guaranteeWithdrawHandler,
		newPaymentServiceGuaranteeWithdrawArgs,
		newPaymentServiceGuaranteeWithdrawResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"TimerWithdraw": kitex.NewMethodInfo(
		timerWithdrawHandler,
		newPaymentServiceTimerWithdrawArgs,
		newPaymentServiceTimerWithdrawResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Transfer": kitex.NewMethodInfo(
		transferHandler,
		newPaymentServiceTransferArgs,
		newPaymentServiceTransferResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreatePOSPay": kitex.NewMethodInfo(
		createPOSPayHandler,
		newPaymentServiceCreatePOSPayArgs,
		newPaymentServiceCreatePOSPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ClosePOSPay": kitex.NewMethodInfo(
		closePOSPayHandler,
		newPaymentServiceClosePOSPayArgs,
		newPaymentServiceClosePOSPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreatePOSSettle": kitex.NewMethodInfo(
		createPOSSettleHandler,
		newPaymentServiceCreatePOSSettleArgs,
		newPaymentServiceCreatePOSSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateGuaranteePay": kitex.NewMethodInfo(
		createGuaranteePayHandler,
		newPaymentServiceCreateGuaranteePayArgs,
		newPaymentServiceCreateGuaranteePayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MergeRefund": kitex.NewMethodInfo(
		mergeRefundHandler,
		newPaymentServiceMergeRefundArgs,
		newPaymentServiceMergeRefundResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MergeRefundAfterSettle": kitex.NewMethodInfo(
		mergeRefundAfterSettleHandler,
		newPaymentServiceMergeRefundAfterSettleArgs,
		newPaymentServiceMergeRefundAfterSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"RefundAfterSettle": kitex.NewMethodInfo(
		refundAfterSettleHandler,
		newPaymentServiceRefundAfterSettleArgs,
		newPaymentServiceRefundAfterSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyMergeReRefundAfterSettle": kitex.NewMethodInfo(
		unionNotifyMergeReRefundAfterSettleHandler,
		newPaymentServiceUnionNotifyMergeReRefundAfterSettleArgs,
		newPaymentServiceUnionNotifyMergeReRefundAfterSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"RefundSettle": kitex.NewMethodInfo(
		refundSettleHandler,
		newPaymentServiceRefundSettleArgs,
		newPaymentServiceRefundSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SecondPayStart": kitex.NewMethodInfo(
		secondPayStartHandler,
		newPaymentServiceSecondPayStartArgs,
		newPaymentServiceSecondPayStartResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SecondSettleStart": kitex.NewMethodInfo(
		secondSettleStartHandler,
		newPaymentServiceSecondSettleStartArgs,
		newPaymentServiceSecondSettleStartResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"LifeRefundAutoAudit": kitex.NewMethodInfo(
		lifeRefundAutoAuditHandler,
		newPaymentServiceLifeRefundAutoAuditArgs,
		newPaymentServiceLifeRefundAutoAuditResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyRefundSettle": kitex.NewMethodInfo(
		unionNotifyRefundSettleHandler,
		newPaymentServiceUnionNotifyRefundSettleArgs,
		newPaymentServiceUnionNotifyRefundSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifySecondPay": kitex.NewMethodInfo(
		unionNotifySecondPayHandler,
		newPaymentServiceUnionNotifySecondPayArgs,
		newPaymentServiceUnionNotifySecondPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifySecondSettle": kitex.NewMethodInfo(
		unionNotifySecondSettleHandler,
		newPaymentServiceUnionNotifySecondSettleArgs,
		newPaymentServiceUnionNotifySecondSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MergeSettle": kitex.NewMethodInfo(
		mergeSettleHandler,
		newPaymentServiceMergeSettleArgs,
		newPaymentServiceMergeSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MergeSettleV2": kitex.NewMethodInfo(
		mergeSettleV2Handler,
		newPaymentServiceMergeSettleV2Args,
		newPaymentServiceMergeSettleV2Result,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateOfflinePay": kitex.NewMethodInfo(
		createOfflinePayHandler,
		newPaymentServiceCreateOfflinePayArgs,
		newPaymentServiceCreateOfflinePayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PayRecognition": kitex.NewMethodInfo(
		payRecognitionHandler,
		newPaymentServicePayRecognitionArgs,
		newPaymentServicePayRecognitionResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ClosePayOrder": kitex.NewMethodInfo(
		closePayOrderHandler,
		newPaymentServiceClosePayOrderArgs,
		newPaymentServiceClosePayOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateUnionPay": kitex.NewMethodInfo(
		createUnionPayHandler,
		newPaymentServiceCreateUnionPayArgs,
		newPaymentServiceCreateUnionPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryUnionPay": kitex.NewMethodInfo(
		queryUnionPayHandler,
		newPaymentServiceQueryUnionPayArgs,
		newPaymentServiceQueryUnionPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CompleteUnionPay": kitex.NewMethodInfo(
		completeUnionPayHandler,
		newPaymentServiceCompleteUnionPayArgs,
		newPaymentServiceCompleteUnionPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateUnionPayInner": kitex.NewMethodInfo(
		createUnionPayInnerHandler,
		newPaymentServiceCreateUnionPayInnerArgs,
		newPaymentServiceCreateUnionPayInnerResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CloseUnionPay": kitex.NewMethodInfo(
		closeUnionPayHandler,
		newPaymentServiceCloseUnionPayArgs,
		newPaymentServiceCloseUnionPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PaymentCallback": kitex.NewMethodInfo(
		paymentCallbackHandler,
		newPaymentServicePaymentCallbackArgs,
		newPaymentServicePaymentCallbackResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FreezeUnionPay": kitex.NewMethodInfo(
		freezeUnionPayHandler,
		newPaymentServiceFreezeUnionPayArgs,
		newPaymentServiceFreezeUnionPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnFreezeUnionPay": kitex.NewMethodInfo(
		unFreezeUnionPayHandler,
		newPaymentServiceUnFreezeUnionPayArgs,
		newPaymentServiceUnFreezeUnionPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateUnionPay": kitex.NewMethodInfo(
		updateUnionPayHandler,
		newPaymentServiceUpdateUnionPayArgs,
		newPaymentServiceUpdateUnionPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryFinancePayList": kitex.NewMethodInfo(
		queryFinancePayListHandler,
		newPaymentServiceQueryFinancePayListArgs,
		newPaymentServiceQueryFinancePayListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryOrderPayList": kitex.NewMethodInfo(
		queryOrderPayListHandler,
		newPaymentServiceQueryOrderPayListArgs,
		newPaymentServiceQueryOrderPayListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryOrderPaymentList": kitex.NewMethodInfo(
		queryOrderPaymentListHandler,
		newPaymentServiceQueryOrderPaymentListArgs,
		newPaymentServiceQueryOrderPaymentListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryPayDetail": kitex.NewMethodInfo(
		queryPayDetailHandler,
		newPaymentServiceQueryPayDetailArgs,
		newPaymentServiceQueryPayDetailResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyPOSPay": kitex.NewMethodInfo(
		unionNotifyPOSPayHandler,
		newPaymentServiceUnionNotifyPOSPayArgs,
		newPaymentServiceUnionNotifyPOSPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyPOSSettle": kitex.NewMethodInfo(
		unionNotifyPOSSettleHandler,
		newPaymentServiceUnionNotifyPOSSettleArgs,
		newPaymentServiceUnionNotifyPOSSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyCashPay": kitex.NewMethodInfo(
		unionNotifyCashPayHandler,
		newPaymentServiceUnionNotifyCashPayArgs,
		newPaymentServiceUnionNotifyCashPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyCashRefund": kitex.NewMethodInfo(
		unionNotifyCashRefundHandler,
		newPaymentServiceUnionNotifyCashRefundArgs,
		newPaymentServiceUnionNotifyCashRefundResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyWithdraw": kitex.NewMethodInfo(
		unionNotifyWithdrawHandler,
		newPaymentServiceUnionNotifyWithdrawArgs,
		newPaymentServiceUnionNotifyWithdrawResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyMergeWithdraw": kitex.NewMethodInfo(
		unionNotifyMergeWithdrawHandler,
		newPaymentServiceUnionNotifyMergeWithdrawArgs,
		newPaymentServiceUnionNotifyMergeWithdrawResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyUnionPayNormal": kitex.NewMethodInfo(
		unionNotifyUnionPayNormalHandler,
		newPaymentServiceUnionNotifyUnionPayNormalArgs,
		newPaymentServiceUnionNotifyUnionPayNormalResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyUnionPayYzt": kitex.NewMethodInfo(
		unionNotifyUnionPayYztHandler,
		newPaymentServiceUnionNotifyUnionPayYztArgs,
		newPaymentServiceUnionNotifyUnionPayYztResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyUnionPayPos": kitex.NewMethodInfo(
		unionNotifyUnionPayPosHandler,
		newPaymentServiceUnionNotifyUnionPayPosArgs,
		newPaymentServiceUnionNotifyUnionPayPosResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyGuaranteePay": kitex.NewMethodInfo(
		unionNotifyGuaranteePayHandler,
		newPaymentServiceUnionNotifyGuaranteePayArgs,
		newPaymentServiceUnionNotifyGuaranteePayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyGuaranteeWithdraw": kitex.NewMethodInfo(
		unionNotifyGuaranteeWithdrawHandler,
		newPaymentServiceUnionNotifyGuaranteeWithdrawArgs,
		newPaymentServiceUnionNotifyGuaranteeWithdrawResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyGuaranteeNormalWithdraw": kitex.NewMethodInfo(
		unionNotifyGuaranteeNormalWithdrawHandler,
		newPaymentServiceUnionNotifyGuaranteeNormalWithdrawArgs,
		newPaymentServiceUnionNotifyGuaranteeNormalWithdrawResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyMergeRefund": kitex.NewMethodInfo(
		unionNotifyMergeRefundHandler,
		newPaymentServiceUnionNotifyMergeRefundArgs,
		newPaymentServiceUnionNotifyMergeRefundResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyMergeSettle": kitex.NewMethodInfo(
		unionNotifyMergeSettleHandler,
		newPaymentServiceUnionNotifyMergeSettleArgs,
		newPaymentServiceUnionNotifyMergeSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyAgreePubPay": kitex.NewMethodInfo(
		unionNotifyAgreePubPayHandler,
		newPaymentServiceUnionNotifyAgreePubPayArgs,
		newPaymentServiceUnionNotifyAgreePubPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyAgreePriPay": kitex.NewMethodInfo(
		unionNotifyAgreePriPayHandler,
		newPaymentServiceUnionNotifyAgreePriPayArgs,
		newPaymentServiceUnionNotifyAgreePriPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyAgreePriSign": kitex.NewMethodInfo(
		unionNotifyAgreePriSignHandler,
		newPaymentServiceUnionNotifyAgreePriSignArgs,
		newPaymentServiceUnionNotifyAgreePriSignResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyAgreePriUnSign": kitex.NewMethodInfo(
		unionNotifyAgreePriUnSignHandler,
		newPaymentServiceUnionNotifyAgreePriUnSignArgs,
		newPaymentServiceUnionNotifyAgreePriUnSignResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ScanTradeOrder": kitex.NewMethodInfo(
		scanTradeOrderHandler,
		newPaymentServiceScanTradeOrderArgs,
		newPaymentServiceScanTradeOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PayTimeoutHandle": kitex.NewMethodInfo(
		payTimeoutHandleHandler,
		newPaymentServicePayTimeoutHandleArgs,
		newPaymentServicePayTimeoutHandleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"TradeCompensate": kitex.NewMethodInfo(
		tradeCompensateHandler,
		newPaymentServiceTradeCompensateArgs,
		newPaymentServiceTradeCompensateResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ScanTradeOrderV2": kitex.NewMethodInfo(
		scanTradeOrderV2Handler,
		newPaymentServiceScanTradeOrderV2Args,
		newPaymentServiceScanTradeOrderV2Result,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetFundInfo": kitex.NewMethodInfo(
		getFundInfoHandler,
		newPaymentServiceGetFundInfoArgs,
		newPaymentServiceGetFundInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AccountBalanceAlarmSchedule": kitex.NewMethodInfo(
		accountBalanceAlarmScheduleHandler,
		newPaymentServiceAccountBalanceAlarmScheduleArgs,
		newPaymentServiceAccountBalanceAlarmScheduleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AccountBalanceAlarmScheduleV2": kitex.NewMethodInfo(
		accountBalanceAlarmScheduleV2Handler,
		newPaymentServiceAccountBalanceAlarmScheduleV2Args,
		newPaymentServiceAccountBalanceAlarmScheduleV2Result,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyCloseRefund": kitex.NewMethodInfo(
		unionNotifyCloseRefundHandler,
		newPaymentServiceUnionNotifyCloseRefundArgs,
		newPaymentServiceUnionNotifyCloseRefundResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PaymentTobBizCall": kitex.NewMethodInfo(
		paymentTobBizCallHandler,
		newPaymentServicePaymentTobBizCallArgs,
		newPaymentServicePaymentTobBizCallResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PaymentYBBizCall": kitex.NewMethodInfo(
		paymentYBBizCallHandler,
		newPaymentServicePaymentYBBizCallArgs,
		newPaymentServicePaymentYBBizCallResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PaymentYBBizCallV2": kitex.NewMethodInfo(
		paymentYBBizCallV2Handler,
		newPaymentServicePaymentYBBizCallV2Args,
		newPaymentServicePaymentYBBizCallV2Result,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"YztOfflineUpsertAndAcceptance": kitex.NewMethodInfo(
		yztOfflineUpsertAndAcceptanceHandler,
		newPaymentServiceYztOfflineUpsertAndAcceptanceArgs,
		newPaymentServiceYztOfflineUpsertAndAcceptanceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"YztOfflineUpsert": kitex.NewMethodInfo(
		yztOfflineUpsertHandler,
		newPaymentServiceYztOfflineUpsertArgs,
		newPaymentServiceYztOfflineUpsertResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AllinSendAgreementSms": kitex.NewMethodInfo(
		allinSendAgreementSmsHandler,
		newPaymentServiceAllinSendAgreementSmsArgs,
		newPaymentServiceAllinSendAgreementSmsResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AllinSignAgreement": kitex.NewMethodInfo(
		allinSignAgreementHandler,
		newPaymentServiceAllinSignAgreementArgs,
		newPaymentServiceAllinSignAgreementResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AllinGetAgreementInfo": kitex.NewMethodInfo(
		allinGetAgreementInfoHandler,
		newPaymentServiceAllinGetAgreementInfoArgs,
		newPaymentServiceAllinGetAgreementInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AllinGetCardBin": kitex.NewMethodInfo(
		allinGetCardBinHandler,
		newPaymentServiceAllinGetCardBinArgs,
		newPaymentServiceAllinGetCardBinResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AgreementPriSign": kitex.NewMethodInfo(
		agreementPriSignHandler,
		newPaymentServiceAgreementPriSignArgs,
		newPaymentServiceAgreementPriSignResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AgreementPriUnSign": kitex.NewMethodInfo(
		agreementPriUnSignHandler,
		newPaymentServiceAgreementPriUnSignArgs,
		newPaymentServiceAgreementPriUnSignResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AgreementPriPay": kitex.NewMethodInfo(
		agreementPriPayHandler,
		newPaymentServiceAgreementPriPayArgs,
		newPaymentServiceAgreementPriPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryAgreementSign": kitex.NewMethodInfo(
		queryAgreementSignHandler,
		newPaymentServiceQueryAgreementSignArgs,
		newPaymentServiceQueryAgreementSignResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryAgreementPay": kitex.NewMethodInfo(
		queryAgreementPayHandler,
		newPaymentServiceQueryAgreementPayArgs,
		newPaymentServiceQueryAgreementPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AgreementPayTimeout": kitex.NewMethodInfo(
		agreementPayTimeoutHandler,
		newPaymentServiceAgreementPayTimeoutArgs,
		newPaymentServiceAgreementPayTimeoutResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AgreementPay": kitex.NewMethodInfo(
		agreementPayHandler,
		newPaymentServiceAgreementPayArgs,
		newPaymentServiceAgreementPayResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"YBScanTradeOrder": kitex.NewMethodInfo(
		yBScanTradeOrderHandler,
		newPaymentServiceYBScanTradeOrderArgs,
		newPaymentServiceYBScanTradeOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifyLife": kitex.NewMethodInfo(
		unionNotifyLifeHandler,
		newPaymentServiceUnionNotifyLifeArgs,
		newPaymentServiceUnionNotifyLifeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	paymentServiceServiceInfo                = NewServiceInfo()
	paymentServiceServiceInfoForClient       = NewServiceInfoForClient()
	paymentServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return paymentServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return paymentServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return paymentServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "PaymentService"
	handlerType := (*payment.PaymentService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "payment",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func createCashPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceCreateCashPayArgs)
	realResult := result.(*payment.PaymentServiceCreateCashPayResult)
	success, err := handler.(payment.PaymentService).CreateCashPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceCreateCashPayArgs() interface{} {
	return payment.NewPaymentServiceCreateCashPayArgs()
}

func newPaymentServiceCreateCashPayResult() interface{} {
	return payment.NewPaymentServiceCreateCashPayResult()
}

func createCashRefundHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceCreateCashRefundArgs)
	realResult := result.(*payment.PaymentServiceCreateCashRefundResult)
	success, err := handler.(payment.PaymentService).CreateCashRefund(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceCreateCashRefundArgs() interface{} {
	return payment.NewPaymentServiceCreateCashRefundArgs()
}

func newPaymentServiceCreateCashRefundResult() interface{} {
	return payment.NewPaymentServiceCreateCashRefundResult()
}

func withdrawDepositHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceWithdrawDepositArgs)
	realResult := result.(*payment.PaymentServiceWithdrawDepositResult)
	success, err := handler.(payment.PaymentService).WithdrawDeposit(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceWithdrawDepositArgs() interface{} {
	return payment.NewPaymentServiceWithdrawDepositArgs()
}

func newPaymentServiceWithdrawDepositResult() interface{} {
	return payment.NewPaymentServiceWithdrawDepositResult()
}

func mergeWithdrawDepositHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceMergeWithdrawDepositArgs)
	realResult := result.(*payment.PaymentServiceMergeWithdrawDepositResult)
	success, err := handler.(payment.PaymentService).MergeWithdrawDeposit(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceMergeWithdrawDepositArgs() interface{} {
	return payment.NewPaymentServiceMergeWithdrawDepositArgs()
}

func newPaymentServiceMergeWithdrawDepositResult() interface{} {
	return payment.NewPaymentServiceMergeWithdrawDepositResult()
}

func guaranteeWithdrawHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceGuaranteeWithdrawArgs)
	realResult := result.(*payment.PaymentServiceGuaranteeWithdrawResult)
	success, err := handler.(payment.PaymentService).GuaranteeWithdraw(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceGuaranteeWithdrawArgs() interface{} {
	return payment.NewPaymentServiceGuaranteeWithdrawArgs()
}

func newPaymentServiceGuaranteeWithdrawResult() interface{} {
	return payment.NewPaymentServiceGuaranteeWithdrawResult()
}

func timerWithdrawHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceTimerWithdrawArgs)
	realResult := result.(*payment.PaymentServiceTimerWithdrawResult)
	success, err := handler.(payment.PaymentService).TimerWithdraw(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceTimerWithdrawArgs() interface{} {
	return payment.NewPaymentServiceTimerWithdrawArgs()
}

func newPaymentServiceTimerWithdrawResult() interface{} {
	return payment.NewPaymentServiceTimerWithdrawResult()
}

func transferHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceTransferArgs)
	realResult := result.(*payment.PaymentServiceTransferResult)
	success, err := handler.(payment.PaymentService).Transfer(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceTransferArgs() interface{} {
	return payment.NewPaymentServiceTransferArgs()
}

func newPaymentServiceTransferResult() interface{} {
	return payment.NewPaymentServiceTransferResult()
}

func createPOSPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceCreatePOSPayArgs)
	realResult := result.(*payment.PaymentServiceCreatePOSPayResult)
	success, err := handler.(payment.PaymentService).CreatePOSPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceCreatePOSPayArgs() interface{} {
	return payment.NewPaymentServiceCreatePOSPayArgs()
}

func newPaymentServiceCreatePOSPayResult() interface{} {
	return payment.NewPaymentServiceCreatePOSPayResult()
}

func closePOSPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceClosePOSPayArgs)
	realResult := result.(*payment.PaymentServiceClosePOSPayResult)
	success, err := handler.(payment.PaymentService).ClosePOSPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceClosePOSPayArgs() interface{} {
	return payment.NewPaymentServiceClosePOSPayArgs()
}

func newPaymentServiceClosePOSPayResult() interface{} {
	return payment.NewPaymentServiceClosePOSPayResult()
}

func createPOSSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceCreatePOSSettleArgs)
	realResult := result.(*payment.PaymentServiceCreatePOSSettleResult)
	success, err := handler.(payment.PaymentService).CreatePOSSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceCreatePOSSettleArgs() interface{} {
	return payment.NewPaymentServiceCreatePOSSettleArgs()
}

func newPaymentServiceCreatePOSSettleResult() interface{} {
	return payment.NewPaymentServiceCreatePOSSettleResult()
}

func createGuaranteePayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceCreateGuaranteePayArgs)
	realResult := result.(*payment.PaymentServiceCreateGuaranteePayResult)
	success, err := handler.(payment.PaymentService).CreateGuaranteePay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceCreateGuaranteePayArgs() interface{} {
	return payment.NewPaymentServiceCreateGuaranteePayArgs()
}

func newPaymentServiceCreateGuaranteePayResult() interface{} {
	return payment.NewPaymentServiceCreateGuaranteePayResult()
}

func mergeRefundHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceMergeRefundArgs)
	realResult := result.(*payment.PaymentServiceMergeRefundResult)
	success, err := handler.(payment.PaymentService).MergeRefund(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceMergeRefundArgs() interface{} {
	return payment.NewPaymentServiceMergeRefundArgs()
}

func newPaymentServiceMergeRefundResult() interface{} {
	return payment.NewPaymentServiceMergeRefundResult()
}

func mergeRefundAfterSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceMergeRefundAfterSettleArgs)
	realResult := result.(*payment.PaymentServiceMergeRefundAfterSettleResult)
	success, err := handler.(payment.PaymentService).MergeRefundAfterSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceMergeRefundAfterSettleArgs() interface{} {
	return payment.NewPaymentServiceMergeRefundAfterSettleArgs()
}

func newPaymentServiceMergeRefundAfterSettleResult() interface{} {
	return payment.NewPaymentServiceMergeRefundAfterSettleResult()
}

func refundAfterSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceRefundAfterSettleArgs)
	realResult := result.(*payment.PaymentServiceRefundAfterSettleResult)
	success, err := handler.(payment.PaymentService).RefundAfterSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceRefundAfterSettleArgs() interface{} {
	return payment.NewPaymentServiceRefundAfterSettleArgs()
}

func newPaymentServiceRefundAfterSettleResult() interface{} {
	return payment.NewPaymentServiceRefundAfterSettleResult()
}

func unionNotifyMergeReRefundAfterSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyMergeReRefundAfterSettleArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyMergeReRefundAfterSettleResult)
	success, err := handler.(payment.PaymentService).UnionNotifyMergeReRefundAfterSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyMergeReRefundAfterSettleArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyMergeReRefundAfterSettleArgs()
}

func newPaymentServiceUnionNotifyMergeReRefundAfterSettleResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyMergeReRefundAfterSettleResult()
}

func refundSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceRefundSettleArgs)
	realResult := result.(*payment.PaymentServiceRefundSettleResult)
	success, err := handler.(payment.PaymentService).RefundSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceRefundSettleArgs() interface{} {
	return payment.NewPaymentServiceRefundSettleArgs()
}

func newPaymentServiceRefundSettleResult() interface{} {
	return payment.NewPaymentServiceRefundSettleResult()
}

func secondPayStartHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceSecondPayStartArgs)
	realResult := result.(*payment.PaymentServiceSecondPayStartResult)
	success, err := handler.(payment.PaymentService).SecondPayStart(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceSecondPayStartArgs() interface{} {
	return payment.NewPaymentServiceSecondPayStartArgs()
}

func newPaymentServiceSecondPayStartResult() interface{} {
	return payment.NewPaymentServiceSecondPayStartResult()
}

func secondSettleStartHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceSecondSettleStartArgs)
	realResult := result.(*payment.PaymentServiceSecondSettleStartResult)
	success, err := handler.(payment.PaymentService).SecondSettleStart(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceSecondSettleStartArgs() interface{} {
	return payment.NewPaymentServiceSecondSettleStartArgs()
}

func newPaymentServiceSecondSettleStartResult() interface{} {
	return payment.NewPaymentServiceSecondSettleStartResult()
}

func lifeRefundAutoAuditHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceLifeRefundAutoAuditArgs)
	realResult := result.(*payment.PaymentServiceLifeRefundAutoAuditResult)
	success, err := handler.(payment.PaymentService).LifeRefundAutoAudit(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceLifeRefundAutoAuditArgs() interface{} {
	return payment.NewPaymentServiceLifeRefundAutoAuditArgs()
}

func newPaymentServiceLifeRefundAutoAuditResult() interface{} {
	return payment.NewPaymentServiceLifeRefundAutoAuditResult()
}

func unionNotifyRefundSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyRefundSettleArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyRefundSettleResult)
	success, err := handler.(payment.PaymentService).UnionNotifyRefundSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyRefundSettleArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyRefundSettleArgs()
}

func newPaymentServiceUnionNotifyRefundSettleResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyRefundSettleResult()
}

func unionNotifySecondPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifySecondPayArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifySecondPayResult)
	success, err := handler.(payment.PaymentService).UnionNotifySecondPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifySecondPayArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifySecondPayArgs()
}

func newPaymentServiceUnionNotifySecondPayResult() interface{} {
	return payment.NewPaymentServiceUnionNotifySecondPayResult()
}

func unionNotifySecondSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifySecondSettleArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifySecondSettleResult)
	success, err := handler.(payment.PaymentService).UnionNotifySecondSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifySecondSettleArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifySecondSettleArgs()
}

func newPaymentServiceUnionNotifySecondSettleResult() interface{} {
	return payment.NewPaymentServiceUnionNotifySecondSettleResult()
}

func mergeSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceMergeSettleArgs)
	realResult := result.(*payment.PaymentServiceMergeSettleResult)
	success, err := handler.(payment.PaymentService).MergeSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceMergeSettleArgs() interface{} {
	return payment.NewPaymentServiceMergeSettleArgs()
}

func newPaymentServiceMergeSettleResult() interface{} {
	return payment.NewPaymentServiceMergeSettleResult()
}

func mergeSettleV2Handler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceMergeSettleV2Args)
	realResult := result.(*payment.PaymentServiceMergeSettleV2Result)
	success, err := handler.(payment.PaymentService).MergeSettleV2(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceMergeSettleV2Args() interface{} {
	return payment.NewPaymentServiceMergeSettleV2Args()
}

func newPaymentServiceMergeSettleV2Result() interface{} {
	return payment.NewPaymentServiceMergeSettleV2Result()
}

func createOfflinePayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceCreateOfflinePayArgs)
	realResult := result.(*payment.PaymentServiceCreateOfflinePayResult)
	success, err := handler.(payment.PaymentService).CreateOfflinePay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceCreateOfflinePayArgs() interface{} {
	return payment.NewPaymentServiceCreateOfflinePayArgs()
}

func newPaymentServiceCreateOfflinePayResult() interface{} {
	return payment.NewPaymentServiceCreateOfflinePayResult()
}

func payRecognitionHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServicePayRecognitionArgs)
	realResult := result.(*payment.PaymentServicePayRecognitionResult)
	success, err := handler.(payment.PaymentService).PayRecognition(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServicePayRecognitionArgs() interface{} {
	return payment.NewPaymentServicePayRecognitionArgs()
}

func newPaymentServicePayRecognitionResult() interface{} {
	return payment.NewPaymentServicePayRecognitionResult()
}

func closePayOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceClosePayOrderArgs)
	realResult := result.(*payment.PaymentServiceClosePayOrderResult)
	success, err := handler.(payment.PaymentService).ClosePayOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceClosePayOrderArgs() interface{} {
	return payment.NewPaymentServiceClosePayOrderArgs()
}

func newPaymentServiceClosePayOrderResult() interface{} {
	return payment.NewPaymentServiceClosePayOrderResult()
}

func createUnionPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceCreateUnionPayArgs)
	realResult := result.(*payment.PaymentServiceCreateUnionPayResult)
	success, err := handler.(payment.PaymentService).CreateUnionPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceCreateUnionPayArgs() interface{} {
	return payment.NewPaymentServiceCreateUnionPayArgs()
}

func newPaymentServiceCreateUnionPayResult() interface{} {
	return payment.NewPaymentServiceCreateUnionPayResult()
}

func queryUnionPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceQueryUnionPayArgs)
	realResult := result.(*payment.PaymentServiceQueryUnionPayResult)
	success, err := handler.(payment.PaymentService).QueryUnionPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceQueryUnionPayArgs() interface{} {
	return payment.NewPaymentServiceQueryUnionPayArgs()
}

func newPaymentServiceQueryUnionPayResult() interface{} {
	return payment.NewPaymentServiceQueryUnionPayResult()
}

func completeUnionPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceCompleteUnionPayArgs)
	realResult := result.(*payment.PaymentServiceCompleteUnionPayResult)
	success, err := handler.(payment.PaymentService).CompleteUnionPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceCompleteUnionPayArgs() interface{} {
	return payment.NewPaymentServiceCompleteUnionPayArgs()
}

func newPaymentServiceCompleteUnionPayResult() interface{} {
	return payment.NewPaymentServiceCompleteUnionPayResult()
}

func createUnionPayInnerHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceCreateUnionPayInnerArgs)
	realResult := result.(*payment.PaymentServiceCreateUnionPayInnerResult)
	success, err := handler.(payment.PaymentService).CreateUnionPayInner(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceCreateUnionPayInnerArgs() interface{} {
	return payment.NewPaymentServiceCreateUnionPayInnerArgs()
}

func newPaymentServiceCreateUnionPayInnerResult() interface{} {
	return payment.NewPaymentServiceCreateUnionPayInnerResult()
}

func closeUnionPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceCloseUnionPayArgs)
	realResult := result.(*payment.PaymentServiceCloseUnionPayResult)
	success, err := handler.(payment.PaymentService).CloseUnionPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceCloseUnionPayArgs() interface{} {
	return payment.NewPaymentServiceCloseUnionPayArgs()
}

func newPaymentServiceCloseUnionPayResult() interface{} {
	return payment.NewPaymentServiceCloseUnionPayResult()
}

func paymentCallbackHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServicePaymentCallbackArgs)
	realResult := result.(*payment.PaymentServicePaymentCallbackResult)
	success, err := handler.(payment.PaymentService).PaymentCallback(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServicePaymentCallbackArgs() interface{} {
	return payment.NewPaymentServicePaymentCallbackArgs()
}

func newPaymentServicePaymentCallbackResult() interface{} {
	return payment.NewPaymentServicePaymentCallbackResult()
}

func freezeUnionPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceFreezeUnionPayArgs)
	realResult := result.(*payment.PaymentServiceFreezeUnionPayResult)
	success, err := handler.(payment.PaymentService).FreezeUnionPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceFreezeUnionPayArgs() interface{} {
	return payment.NewPaymentServiceFreezeUnionPayArgs()
}

func newPaymentServiceFreezeUnionPayResult() interface{} {
	return payment.NewPaymentServiceFreezeUnionPayResult()
}

func unFreezeUnionPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnFreezeUnionPayArgs)
	realResult := result.(*payment.PaymentServiceUnFreezeUnionPayResult)
	success, err := handler.(payment.PaymentService).UnFreezeUnionPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnFreezeUnionPayArgs() interface{} {
	return payment.NewPaymentServiceUnFreezeUnionPayArgs()
}

func newPaymentServiceUnFreezeUnionPayResult() interface{} {
	return payment.NewPaymentServiceUnFreezeUnionPayResult()
}

func updateUnionPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUpdateUnionPayArgs)
	realResult := result.(*payment.PaymentServiceUpdateUnionPayResult)
	success, err := handler.(payment.PaymentService).UpdateUnionPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUpdateUnionPayArgs() interface{} {
	return payment.NewPaymentServiceUpdateUnionPayArgs()
}

func newPaymentServiceUpdateUnionPayResult() interface{} {
	return payment.NewPaymentServiceUpdateUnionPayResult()
}

func queryFinancePayListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceQueryFinancePayListArgs)
	realResult := result.(*payment.PaymentServiceQueryFinancePayListResult)
	success, err := handler.(payment.PaymentService).QueryFinancePayList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceQueryFinancePayListArgs() interface{} {
	return payment.NewPaymentServiceQueryFinancePayListArgs()
}

func newPaymentServiceQueryFinancePayListResult() interface{} {
	return payment.NewPaymentServiceQueryFinancePayListResult()
}

func queryOrderPayListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceQueryOrderPayListArgs)
	realResult := result.(*payment.PaymentServiceQueryOrderPayListResult)
	success, err := handler.(payment.PaymentService).QueryOrderPayList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceQueryOrderPayListArgs() interface{} {
	return payment.NewPaymentServiceQueryOrderPayListArgs()
}

func newPaymentServiceQueryOrderPayListResult() interface{} {
	return payment.NewPaymentServiceQueryOrderPayListResult()
}

func queryOrderPaymentListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceQueryOrderPaymentListArgs)
	realResult := result.(*payment.PaymentServiceQueryOrderPaymentListResult)
	success, err := handler.(payment.PaymentService).QueryOrderPaymentList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceQueryOrderPaymentListArgs() interface{} {
	return payment.NewPaymentServiceQueryOrderPaymentListArgs()
}

func newPaymentServiceQueryOrderPaymentListResult() interface{} {
	return payment.NewPaymentServiceQueryOrderPaymentListResult()
}

func queryPayDetailHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceQueryPayDetailArgs)
	realResult := result.(*payment.PaymentServiceQueryPayDetailResult)
	success, err := handler.(payment.PaymentService).QueryPayDetail(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceQueryPayDetailArgs() interface{} {
	return payment.NewPaymentServiceQueryPayDetailArgs()
}

func newPaymentServiceQueryPayDetailResult() interface{} {
	return payment.NewPaymentServiceQueryPayDetailResult()
}

func unionNotifyPOSPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyPOSPayArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyPOSPayResult)
	success, err := handler.(payment.PaymentService).UnionNotifyPOSPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyPOSPayArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyPOSPayArgs()
}

func newPaymentServiceUnionNotifyPOSPayResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyPOSPayResult()
}

func unionNotifyPOSSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyPOSSettleArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyPOSSettleResult)
	success, err := handler.(payment.PaymentService).UnionNotifyPOSSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyPOSSettleArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyPOSSettleArgs()
}

func newPaymentServiceUnionNotifyPOSSettleResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyPOSSettleResult()
}

func unionNotifyCashPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyCashPayArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyCashPayResult)
	success, err := handler.(payment.PaymentService).UnionNotifyCashPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyCashPayArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyCashPayArgs()
}

func newPaymentServiceUnionNotifyCashPayResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyCashPayResult()
}

func unionNotifyCashRefundHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyCashRefundArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyCashRefundResult)
	success, err := handler.(payment.PaymentService).UnionNotifyCashRefund(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyCashRefundArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyCashRefundArgs()
}

func newPaymentServiceUnionNotifyCashRefundResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyCashRefundResult()
}

func unionNotifyWithdrawHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyWithdrawArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyWithdrawResult)
	success, err := handler.(payment.PaymentService).UnionNotifyWithdraw(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyWithdrawArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyWithdrawArgs()
}

func newPaymentServiceUnionNotifyWithdrawResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyWithdrawResult()
}

func unionNotifyMergeWithdrawHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyMergeWithdrawArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyMergeWithdrawResult)
	success, err := handler.(payment.PaymentService).UnionNotifyMergeWithdraw(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyMergeWithdrawArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyMergeWithdrawArgs()
}

func newPaymentServiceUnionNotifyMergeWithdrawResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyMergeWithdrawResult()
}

func unionNotifyUnionPayNormalHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyUnionPayNormalArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyUnionPayNormalResult)
	success, err := handler.(payment.PaymentService).UnionNotifyUnionPayNormal(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyUnionPayNormalArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyUnionPayNormalArgs()
}

func newPaymentServiceUnionNotifyUnionPayNormalResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyUnionPayNormalResult()
}

func unionNotifyUnionPayYztHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyUnionPayYztArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyUnionPayYztResult)
	success, err := handler.(payment.PaymentService).UnionNotifyUnionPayYzt(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyUnionPayYztArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyUnionPayYztArgs()
}

func newPaymentServiceUnionNotifyUnionPayYztResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyUnionPayYztResult()
}

func unionNotifyUnionPayPosHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyUnionPayPosArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyUnionPayPosResult)
	success, err := handler.(payment.PaymentService).UnionNotifyUnionPayPos(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyUnionPayPosArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyUnionPayPosArgs()
}

func newPaymentServiceUnionNotifyUnionPayPosResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyUnionPayPosResult()
}

func unionNotifyGuaranteePayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyGuaranteePayArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyGuaranteePayResult)
	success, err := handler.(payment.PaymentService).UnionNotifyGuaranteePay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyGuaranteePayArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyGuaranteePayArgs()
}

func newPaymentServiceUnionNotifyGuaranteePayResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyGuaranteePayResult()
}

func unionNotifyGuaranteeWithdrawHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyGuaranteeWithdrawArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyGuaranteeWithdrawResult)
	success, err := handler.(payment.PaymentService).UnionNotifyGuaranteeWithdraw(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyGuaranteeWithdrawArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyGuaranteeWithdrawArgs()
}

func newPaymentServiceUnionNotifyGuaranteeWithdrawResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyGuaranteeWithdrawResult()
}

func unionNotifyGuaranteeNormalWithdrawHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyGuaranteeNormalWithdrawArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyGuaranteeNormalWithdrawResult)
	success, err := handler.(payment.PaymentService).UnionNotifyGuaranteeNormalWithdraw(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyGuaranteeNormalWithdrawArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyGuaranteeNormalWithdrawArgs()
}

func newPaymentServiceUnionNotifyGuaranteeNormalWithdrawResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyGuaranteeNormalWithdrawResult()
}

func unionNotifyMergeRefundHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyMergeRefundArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyMergeRefundResult)
	success, err := handler.(payment.PaymentService).UnionNotifyMergeRefund(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyMergeRefundArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyMergeRefundArgs()
}

func newPaymentServiceUnionNotifyMergeRefundResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyMergeRefundResult()
}

func unionNotifyMergeSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyMergeSettleArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyMergeSettleResult)
	success, err := handler.(payment.PaymentService).UnionNotifyMergeSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyMergeSettleArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyMergeSettleArgs()
}

func newPaymentServiceUnionNotifyMergeSettleResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyMergeSettleResult()
}

func unionNotifyAgreePubPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyAgreePubPayArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyAgreePubPayResult)
	success, err := handler.(payment.PaymentService).UnionNotifyAgreePubPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyAgreePubPayArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyAgreePubPayArgs()
}

func newPaymentServiceUnionNotifyAgreePubPayResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyAgreePubPayResult()
}

func unionNotifyAgreePriPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyAgreePriPayArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyAgreePriPayResult)
	success, err := handler.(payment.PaymentService).UnionNotifyAgreePriPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyAgreePriPayArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyAgreePriPayArgs()
}

func newPaymentServiceUnionNotifyAgreePriPayResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyAgreePriPayResult()
}

func unionNotifyAgreePriSignHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyAgreePriSignArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyAgreePriSignResult)
	success, err := handler.(payment.PaymentService).UnionNotifyAgreePriSign(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyAgreePriSignArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyAgreePriSignArgs()
}

func newPaymentServiceUnionNotifyAgreePriSignResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyAgreePriSignResult()
}

func unionNotifyAgreePriUnSignHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyAgreePriUnSignArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyAgreePriUnSignResult)
	success, err := handler.(payment.PaymentService).UnionNotifyAgreePriUnSign(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyAgreePriUnSignArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyAgreePriUnSignArgs()
}

func newPaymentServiceUnionNotifyAgreePriUnSignResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyAgreePriUnSignResult()
}

func scanTradeOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceScanTradeOrderArgs)
	realResult := result.(*payment.PaymentServiceScanTradeOrderResult)
	success, err := handler.(payment.PaymentService).ScanTradeOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceScanTradeOrderArgs() interface{} {
	return payment.NewPaymentServiceScanTradeOrderArgs()
}

func newPaymentServiceScanTradeOrderResult() interface{} {
	return payment.NewPaymentServiceScanTradeOrderResult()
}

func payTimeoutHandleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServicePayTimeoutHandleArgs)
	realResult := result.(*payment.PaymentServicePayTimeoutHandleResult)
	success, err := handler.(payment.PaymentService).PayTimeoutHandle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServicePayTimeoutHandleArgs() interface{} {
	return payment.NewPaymentServicePayTimeoutHandleArgs()
}

func newPaymentServicePayTimeoutHandleResult() interface{} {
	return payment.NewPaymentServicePayTimeoutHandleResult()
}

func tradeCompensateHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceTradeCompensateArgs)
	realResult := result.(*payment.PaymentServiceTradeCompensateResult)
	success, err := handler.(payment.PaymentService).TradeCompensate(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceTradeCompensateArgs() interface{} {
	return payment.NewPaymentServiceTradeCompensateArgs()
}

func newPaymentServiceTradeCompensateResult() interface{} {
	return payment.NewPaymentServiceTradeCompensateResult()
}

func scanTradeOrderV2Handler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceScanTradeOrderV2Args)
	realResult := result.(*payment.PaymentServiceScanTradeOrderV2Result)
	success, err := handler.(payment.PaymentService).ScanTradeOrderV2(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceScanTradeOrderV2Args() interface{} {
	return payment.NewPaymentServiceScanTradeOrderV2Args()
}

func newPaymentServiceScanTradeOrderV2Result() interface{} {
	return payment.NewPaymentServiceScanTradeOrderV2Result()
}

func getFundInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceGetFundInfoArgs)
	realResult := result.(*payment.PaymentServiceGetFundInfoResult)
	success, err := handler.(payment.PaymentService).GetFundInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceGetFundInfoArgs() interface{} {
	return payment.NewPaymentServiceGetFundInfoArgs()
}

func newPaymentServiceGetFundInfoResult() interface{} {
	return payment.NewPaymentServiceGetFundInfoResult()
}

func accountBalanceAlarmScheduleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAccountBalanceAlarmScheduleArgs)
	realResult := result.(*payment.PaymentServiceAccountBalanceAlarmScheduleResult)
	success, err := handler.(payment.PaymentService).AccountBalanceAlarmSchedule(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAccountBalanceAlarmScheduleArgs() interface{} {
	return payment.NewPaymentServiceAccountBalanceAlarmScheduleArgs()
}

func newPaymentServiceAccountBalanceAlarmScheduleResult() interface{} {
	return payment.NewPaymentServiceAccountBalanceAlarmScheduleResult()
}

func accountBalanceAlarmScheduleV2Handler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAccountBalanceAlarmScheduleV2Args)
	realResult := result.(*payment.PaymentServiceAccountBalanceAlarmScheduleV2Result)
	success, err := handler.(payment.PaymentService).AccountBalanceAlarmScheduleV2(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAccountBalanceAlarmScheduleV2Args() interface{} {
	return payment.NewPaymentServiceAccountBalanceAlarmScheduleV2Args()
}

func newPaymentServiceAccountBalanceAlarmScheduleV2Result() interface{} {
	return payment.NewPaymentServiceAccountBalanceAlarmScheduleV2Result()
}

func unionNotifyCloseRefundHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyCloseRefundArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyCloseRefundResult)
	success, err := handler.(payment.PaymentService).UnionNotifyCloseRefund(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyCloseRefundArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyCloseRefundArgs()
}

func newPaymentServiceUnionNotifyCloseRefundResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyCloseRefundResult()
}

func paymentTobBizCallHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServicePaymentTobBizCallArgs)
	realResult := result.(*payment.PaymentServicePaymentTobBizCallResult)
	success, err := handler.(payment.PaymentService).PaymentTobBizCall(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServicePaymentTobBizCallArgs() interface{} {
	return payment.NewPaymentServicePaymentTobBizCallArgs()
}

func newPaymentServicePaymentTobBizCallResult() interface{} {
	return payment.NewPaymentServicePaymentTobBizCallResult()
}

func paymentYBBizCallHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServicePaymentYBBizCallArgs)
	realResult := result.(*payment.PaymentServicePaymentYBBizCallResult)
	success, err := handler.(payment.PaymentService).PaymentYBBizCall(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServicePaymentYBBizCallArgs() interface{} {
	return payment.NewPaymentServicePaymentYBBizCallArgs()
}

func newPaymentServicePaymentYBBizCallResult() interface{} {
	return payment.NewPaymentServicePaymentYBBizCallResult()
}

func paymentYBBizCallV2Handler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServicePaymentYBBizCallV2Args)
	realResult := result.(*payment.PaymentServicePaymentYBBizCallV2Result)
	success, err := handler.(payment.PaymentService).PaymentYBBizCallV2(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServicePaymentYBBizCallV2Args() interface{} {
	return payment.NewPaymentServicePaymentYBBizCallV2Args()
}

func newPaymentServicePaymentYBBizCallV2Result() interface{} {
	return payment.NewPaymentServicePaymentYBBizCallV2Result()
}

func yztOfflineUpsertAndAcceptanceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceYztOfflineUpsertAndAcceptanceArgs)
	realResult := result.(*payment.PaymentServiceYztOfflineUpsertAndAcceptanceResult)
	success, err := handler.(payment.PaymentService).YztOfflineUpsertAndAcceptance(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceYztOfflineUpsertAndAcceptanceArgs() interface{} {
	return payment.NewPaymentServiceYztOfflineUpsertAndAcceptanceArgs()
}

func newPaymentServiceYztOfflineUpsertAndAcceptanceResult() interface{} {
	return payment.NewPaymentServiceYztOfflineUpsertAndAcceptanceResult()
}

func yztOfflineUpsertHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceYztOfflineUpsertArgs)
	realResult := result.(*payment.PaymentServiceYztOfflineUpsertResult)
	success, err := handler.(payment.PaymentService).YztOfflineUpsert(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceYztOfflineUpsertArgs() interface{} {
	return payment.NewPaymentServiceYztOfflineUpsertArgs()
}

func newPaymentServiceYztOfflineUpsertResult() interface{} {
	return payment.NewPaymentServiceYztOfflineUpsertResult()
}

func allinSendAgreementSmsHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAllinSendAgreementSmsArgs)
	realResult := result.(*payment.PaymentServiceAllinSendAgreementSmsResult)
	success, err := handler.(payment.PaymentService).AllinSendAgreementSms(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAllinSendAgreementSmsArgs() interface{} {
	return payment.NewPaymentServiceAllinSendAgreementSmsArgs()
}

func newPaymentServiceAllinSendAgreementSmsResult() interface{} {
	return payment.NewPaymentServiceAllinSendAgreementSmsResult()
}

func allinSignAgreementHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAllinSignAgreementArgs)
	realResult := result.(*payment.PaymentServiceAllinSignAgreementResult)
	success, err := handler.(payment.PaymentService).AllinSignAgreement(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAllinSignAgreementArgs() interface{} {
	return payment.NewPaymentServiceAllinSignAgreementArgs()
}

func newPaymentServiceAllinSignAgreementResult() interface{} {
	return payment.NewPaymentServiceAllinSignAgreementResult()
}

func allinGetAgreementInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAllinGetAgreementInfoArgs)
	realResult := result.(*payment.PaymentServiceAllinGetAgreementInfoResult)
	success, err := handler.(payment.PaymentService).AllinGetAgreementInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAllinGetAgreementInfoArgs() interface{} {
	return payment.NewPaymentServiceAllinGetAgreementInfoArgs()
}

func newPaymentServiceAllinGetAgreementInfoResult() interface{} {
	return payment.NewPaymentServiceAllinGetAgreementInfoResult()
}

func allinGetCardBinHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAllinGetCardBinArgs)
	realResult := result.(*payment.PaymentServiceAllinGetCardBinResult)
	success, err := handler.(payment.PaymentService).AllinGetCardBin(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAllinGetCardBinArgs() interface{} {
	return payment.NewPaymentServiceAllinGetCardBinArgs()
}

func newPaymentServiceAllinGetCardBinResult() interface{} {
	return payment.NewPaymentServiceAllinGetCardBinResult()
}

func agreementPriSignHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAgreementPriSignArgs)
	realResult := result.(*payment.PaymentServiceAgreementPriSignResult)
	success, err := handler.(payment.PaymentService).AgreementPriSign(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAgreementPriSignArgs() interface{} {
	return payment.NewPaymentServiceAgreementPriSignArgs()
}

func newPaymentServiceAgreementPriSignResult() interface{} {
	return payment.NewPaymentServiceAgreementPriSignResult()
}

func agreementPriUnSignHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAgreementPriUnSignArgs)
	realResult := result.(*payment.PaymentServiceAgreementPriUnSignResult)
	success, err := handler.(payment.PaymentService).AgreementPriUnSign(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAgreementPriUnSignArgs() interface{} {
	return payment.NewPaymentServiceAgreementPriUnSignArgs()
}

func newPaymentServiceAgreementPriUnSignResult() interface{} {
	return payment.NewPaymentServiceAgreementPriUnSignResult()
}

func agreementPriPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAgreementPriPayArgs)
	realResult := result.(*payment.PaymentServiceAgreementPriPayResult)
	success, err := handler.(payment.PaymentService).AgreementPriPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAgreementPriPayArgs() interface{} {
	return payment.NewPaymentServiceAgreementPriPayArgs()
}

func newPaymentServiceAgreementPriPayResult() interface{} {
	return payment.NewPaymentServiceAgreementPriPayResult()
}

func queryAgreementSignHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceQueryAgreementSignArgs)
	realResult := result.(*payment.PaymentServiceQueryAgreementSignResult)
	success, err := handler.(payment.PaymentService).QueryAgreementSign(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceQueryAgreementSignArgs() interface{} {
	return payment.NewPaymentServiceQueryAgreementSignArgs()
}

func newPaymentServiceQueryAgreementSignResult() interface{} {
	return payment.NewPaymentServiceQueryAgreementSignResult()
}

func queryAgreementPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceQueryAgreementPayArgs)
	realResult := result.(*payment.PaymentServiceQueryAgreementPayResult)
	success, err := handler.(payment.PaymentService).QueryAgreementPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceQueryAgreementPayArgs() interface{} {
	return payment.NewPaymentServiceQueryAgreementPayArgs()
}

func newPaymentServiceQueryAgreementPayResult() interface{} {
	return payment.NewPaymentServiceQueryAgreementPayResult()
}

func agreementPayTimeoutHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAgreementPayTimeoutArgs)
	realResult := result.(*payment.PaymentServiceAgreementPayTimeoutResult)
	success, err := handler.(payment.PaymentService).AgreementPayTimeout(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAgreementPayTimeoutArgs() interface{} {
	return payment.NewPaymentServiceAgreementPayTimeoutArgs()
}

func newPaymentServiceAgreementPayTimeoutResult() interface{} {
	return payment.NewPaymentServiceAgreementPayTimeoutResult()
}

func agreementPayHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceAgreementPayArgs)
	realResult := result.(*payment.PaymentServiceAgreementPayResult)
	success, err := handler.(payment.PaymentService).AgreementPay(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceAgreementPayArgs() interface{} {
	return payment.NewPaymentServiceAgreementPayArgs()
}

func newPaymentServiceAgreementPayResult() interface{} {
	return payment.NewPaymentServiceAgreementPayResult()
}

func yBScanTradeOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceYBScanTradeOrderArgs)
	realResult := result.(*payment.PaymentServiceYBScanTradeOrderResult)
	success, err := handler.(payment.PaymentService).YBScanTradeOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceYBScanTradeOrderArgs() interface{} {
	return payment.NewPaymentServiceYBScanTradeOrderArgs()
}

func newPaymentServiceYBScanTradeOrderResult() interface{} {
	return payment.NewPaymentServiceYBScanTradeOrderResult()
}

func unionNotifyLifeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*payment.PaymentServiceUnionNotifyLifeArgs)
	realResult := result.(*payment.PaymentServiceUnionNotifyLifeResult)
	success, err := handler.(payment.PaymentService).UnionNotifyLife(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newPaymentServiceUnionNotifyLifeArgs() interface{} {
	return payment.NewPaymentServiceUnionNotifyLifeArgs()
}

func newPaymentServiceUnionNotifyLifeResult() interface{} {
	return payment.NewPaymentServiceUnionNotifyLifeResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) CreateCashPay(ctx context.Context, req *payment.CreateCashPayReq) (r *payment.CreateCashPayRsp, err error) {
	var _args payment.PaymentServiceCreateCashPayArgs
	_args.Req = req
	var _result payment.PaymentServiceCreateCashPayResult
	if err = p.c.Call(ctx, "CreateCashPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateCashRefund(ctx context.Context, req *payment.CreateCashRefundReq) (r *payment.CreateCashRefundRsp, err error) {
	var _args payment.PaymentServiceCreateCashRefundArgs
	_args.Req = req
	var _result payment.PaymentServiceCreateCashRefundResult
	if err = p.c.Call(ctx, "CreateCashRefund", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) WithdrawDeposit(ctx context.Context, req *payment.WithdrawDepositReq) (r *payment.WithdrawDepositRsp, err error) {
	var _args payment.PaymentServiceWithdrawDepositArgs
	_args.Req = req
	var _result payment.PaymentServiceWithdrawDepositResult
	if err = p.c.Call(ctx, "WithdrawDeposit", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MergeWithdrawDeposit(ctx context.Context, req *payment.MergeWithdrawDepositReq) (r *payment.MergeWithdrawDepositResp, err error) {
	var _args payment.PaymentServiceMergeWithdrawDepositArgs
	_args.Req = req
	var _result payment.PaymentServiceMergeWithdrawDepositResult
	if err = p.c.Call(ctx, "MergeWithdrawDeposit", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GuaranteeWithdraw(ctx context.Context, req *payment.GuaranteeWithdrawReq) (r *payment.GuaranteeWithdrawResp, err error) {
	var _args payment.PaymentServiceGuaranteeWithdrawArgs
	_args.Req = req
	var _result payment.PaymentServiceGuaranteeWithdrawResult
	if err = p.c.Call(ctx, "GuaranteeWithdraw", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) TimerWithdraw(ctx context.Context, req *payment.TimerWithdrawReq) (r *payment.TimerWithdrawResp, err error) {
	var _args payment.PaymentServiceTimerWithdrawArgs
	_args.Req = req
	var _result payment.PaymentServiceTimerWithdrawResult
	if err = p.c.Call(ctx, "TimerWithdraw", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Transfer(ctx context.Context, req *payment.TransferReq) (r *payment.TransferResp, err error) {
	var _args payment.PaymentServiceTransferArgs
	_args.Req = req
	var _result payment.PaymentServiceTransferResult
	if err = p.c.Call(ctx, "Transfer", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreatePOSPay(ctx context.Context, req *payment.CreatePOSPayReq) (r *payment.CreatePOSPayResp, err error) {
	var _args payment.PaymentServiceCreatePOSPayArgs
	_args.Req = req
	var _result payment.PaymentServiceCreatePOSPayResult
	if err = p.c.Call(ctx, "CreatePOSPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ClosePOSPay(ctx context.Context, req *payment.ClosePOSPayReq) (r *payment.ClosePOSPayResp, err error) {
	var _args payment.PaymentServiceClosePOSPayArgs
	_args.Req = req
	var _result payment.PaymentServiceClosePOSPayResult
	if err = p.c.Call(ctx, "ClosePOSPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreatePOSSettle(ctx context.Context, req *payment.CreatePOSSettleReq) (r *payment.CreatePOSSettleResp, err error) {
	var _args payment.PaymentServiceCreatePOSSettleArgs
	_args.Req = req
	var _result payment.PaymentServiceCreatePOSSettleResult
	if err = p.c.Call(ctx, "CreatePOSSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateGuaranteePay(ctx context.Context, req *payment.CreateGuaranteePayReq) (r *payment.CreateGuaranteePayResp, err error) {
	var _args payment.PaymentServiceCreateGuaranteePayArgs
	_args.Req = req
	var _result payment.PaymentServiceCreateGuaranteePayResult
	if err = p.c.Call(ctx, "CreateGuaranteePay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MergeRefund(ctx context.Context, req *payment.MergeRefundReq) (r *payment.MergeRefundResp, err error) {
	var _args payment.PaymentServiceMergeRefundArgs
	_args.Req = req
	var _result payment.PaymentServiceMergeRefundResult
	if err = p.c.Call(ctx, "MergeRefund", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MergeRefundAfterSettle(ctx context.Context, req *payment.MergeRefundAfterSettleReq) (r *payment.MergeRefundAfterSettleResp, err error) {
	var _args payment.PaymentServiceMergeRefundAfterSettleArgs
	_args.Req = req
	var _result payment.PaymentServiceMergeRefundAfterSettleResult
	if err = p.c.Call(ctx, "MergeRefundAfterSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) RefundAfterSettle(ctx context.Context, req *payment.RefundAfterSettleReq) (r *payment.RefundAfterSettleResp, err error) {
	var _args payment.PaymentServiceRefundAfterSettleArgs
	_args.Req = req
	var _result payment.PaymentServiceRefundAfterSettleResult
	if err = p.c.Call(ctx, "RefundAfterSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyMergeReRefundAfterSettle(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyMergeReRefundAfterSettleArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyMergeReRefundAfterSettleResult
	if err = p.c.Call(ctx, "UnionNotifyMergeReRefundAfterSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) RefundSettle(ctx context.Context, req *payment.RefundSettleReq) (r *payment.RefundSettleResp, err error) {
	var _args payment.PaymentServiceRefundSettleArgs
	_args.Req = req
	var _result payment.PaymentServiceRefundSettleResult
	if err = p.c.Call(ctx, "RefundSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SecondPayStart(ctx context.Context, req *payment.SecondPayReq) (r *payment.SecondPayResp, err error) {
	var _args payment.PaymentServiceSecondPayStartArgs
	_args.Req = req
	var _result payment.PaymentServiceSecondPayStartResult
	if err = p.c.Call(ctx, "SecondPayStart", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SecondSettleStart(ctx context.Context, req *payment.SecondSettleReq) (r *payment.SecondSettleResp, err error) {
	var _args payment.PaymentServiceSecondSettleStartArgs
	_args.Req = req
	var _result payment.PaymentServiceSecondSettleStartResult
	if err = p.c.Call(ctx, "SecondSettleStart", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) LifeRefundAutoAudit(ctx context.Context, req *payment.LifeRefundAutoAuditReq) (r *payment.LifeRefundAutoAuditResp, err error) {
	var _args payment.PaymentServiceLifeRefundAutoAuditArgs
	_args.Req = req
	var _result payment.PaymentServiceLifeRefundAutoAuditResult
	if err = p.c.Call(ctx, "LifeRefundAutoAudit", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyRefundSettle(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyRefundSettleArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyRefundSettleResult
	if err = p.c.Call(ctx, "UnionNotifyRefundSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifySecondPay(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifySecondPayArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifySecondPayResult
	if err = p.c.Call(ctx, "UnionNotifySecondPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifySecondSettle(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifySecondSettleArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifySecondSettleResult
	if err = p.c.Call(ctx, "UnionNotifySecondSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MergeSettle(ctx context.Context, req *payment.MergeSettleReq) (r *payment.MergeSettleResp, err error) {
	var _args payment.PaymentServiceMergeSettleArgs
	_args.Req = req
	var _result payment.PaymentServiceMergeSettleResult
	if err = p.c.Call(ctx, "MergeSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MergeSettleV2(ctx context.Context, req *payment.MergeSettleV2Req) (r *payment.MergeSettleV2Resp, err error) {
	var _args payment.PaymentServiceMergeSettleV2Args
	_args.Req = req
	var _result payment.PaymentServiceMergeSettleV2Result
	if err = p.c.Call(ctx, "MergeSettleV2", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateOfflinePay(ctx context.Context, req *payment.CreateOfflinePayReq) (r *payment.CreateOfflinePayResp, err error) {
	var _args payment.PaymentServiceCreateOfflinePayArgs
	_args.Req = req
	var _result payment.PaymentServiceCreateOfflinePayResult
	if err = p.c.Call(ctx, "CreateOfflinePay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PayRecognition(ctx context.Context, req *payment.PayRecognitionReq) (r *payment.PayRecognitionResp, err error) {
	var _args payment.PaymentServicePayRecognitionArgs
	_args.Req = req
	var _result payment.PaymentServicePayRecognitionResult
	if err = p.c.Call(ctx, "PayRecognition", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ClosePayOrder(ctx context.Context, req *payment.ClosePayOrderReq) (r *payment.ClosePayOrderResp, err error) {
	var _args payment.PaymentServiceClosePayOrderArgs
	_args.Req = req
	var _result payment.PaymentServiceClosePayOrderResult
	if err = p.c.Call(ctx, "ClosePayOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateUnionPay(ctx context.Context, req *payment.CreateUnionPayReq) (r *payment.CreateUnionPayResp, err error) {
	var _args payment.PaymentServiceCreateUnionPayArgs
	_args.Req = req
	var _result payment.PaymentServiceCreateUnionPayResult
	if err = p.c.Call(ctx, "CreateUnionPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryUnionPay(ctx context.Context, req *payment.QueryUnionPayReq) (r *payment.QueryUnionPayResp, err error) {
	var _args payment.PaymentServiceQueryUnionPayArgs
	_args.Req = req
	var _result payment.PaymentServiceQueryUnionPayResult
	if err = p.c.Call(ctx, "QueryUnionPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CompleteUnionPay(ctx context.Context, req *payment.CompleteUnionPayReq) (r *payment.CompleteUnionPayResp, err error) {
	var _args payment.PaymentServiceCompleteUnionPayArgs
	_args.Req = req
	var _result payment.PaymentServiceCompleteUnionPayResult
	if err = p.c.Call(ctx, "CompleteUnionPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateUnionPayInner(ctx context.Context, req *payment.CreateUnionPayInnerReq) (r *payment.CreateUnionPayInnerResp, err error) {
	var _args payment.PaymentServiceCreateUnionPayInnerArgs
	_args.Req = req
	var _result payment.PaymentServiceCreateUnionPayInnerResult
	if err = p.c.Call(ctx, "CreateUnionPayInner", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CloseUnionPay(ctx context.Context, req *payment.CloseUnionPayReq) (r *payment.CloseUnionPayResp, err error) {
	var _args payment.PaymentServiceCloseUnionPayArgs
	_args.Req = req
	var _result payment.PaymentServiceCloseUnionPayResult
	if err = p.c.Call(ctx, "CloseUnionPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PaymentCallback(ctx context.Context, req *payment.PaymentCallbackReq) (r *payment.PaymentCallbackResp, err error) {
	var _args payment.PaymentServicePaymentCallbackArgs
	_args.Req = req
	var _result payment.PaymentServicePaymentCallbackResult
	if err = p.c.Call(ctx, "PaymentCallback", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FreezeUnionPay(ctx context.Context, req *payment.FreezeUnionPayReq) (r *payment.FreezeUnionPayResp, err error) {
	var _args payment.PaymentServiceFreezeUnionPayArgs
	_args.Req = req
	var _result payment.PaymentServiceFreezeUnionPayResult
	if err = p.c.Call(ctx, "FreezeUnionPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnFreezeUnionPay(ctx context.Context, req *payment.UnFreezeUnionPayReq) (r *payment.UnFreezeUnionPayResp, err error) {
	var _args payment.PaymentServiceUnFreezeUnionPayArgs
	_args.Req = req
	var _result payment.PaymentServiceUnFreezeUnionPayResult
	if err = p.c.Call(ctx, "UnFreezeUnionPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateUnionPay(ctx context.Context, req *payment.UpdateUnionPayReq) (r *payment.UpdateUnionPayResp, err error) {
	var _args payment.PaymentServiceUpdateUnionPayArgs
	_args.Req = req
	var _result payment.PaymentServiceUpdateUnionPayResult
	if err = p.c.Call(ctx, "UpdateUnionPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryFinancePayList(ctx context.Context, req *payment.QueryFinancePayListReq) (r *payment.QueryFinancePayListResp, err error) {
	var _args payment.PaymentServiceQueryFinancePayListArgs
	_args.Req = req
	var _result payment.PaymentServiceQueryFinancePayListResult
	if err = p.c.Call(ctx, "QueryFinancePayList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryOrderPayList(ctx context.Context, req *payment.QueryOrderPayListReq) (r *payment.QueryOrderPayListResp, err error) {
	var _args payment.PaymentServiceQueryOrderPayListArgs
	_args.Req = req
	var _result payment.PaymentServiceQueryOrderPayListResult
	if err = p.c.Call(ctx, "QueryOrderPayList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryOrderPaymentList(ctx context.Context, req *payment.QueryOrderPaymentListReq) (r *payment.QueryOrderPaymentListResp, err error) {
	var _args payment.PaymentServiceQueryOrderPaymentListArgs
	_args.Req = req
	var _result payment.PaymentServiceQueryOrderPaymentListResult
	if err = p.c.Call(ctx, "QueryOrderPaymentList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryPayDetail(ctx context.Context, req *payment.QueryPayDetailReq) (r *payment.QueryPayDetailResp, err error) {
	var _args payment.PaymentServiceQueryPayDetailArgs
	_args.Req = req
	var _result payment.PaymentServiceQueryPayDetailResult
	if err = p.c.Call(ctx, "QueryPayDetail", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyPOSPay(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyPOSPayArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyPOSPayResult
	if err = p.c.Call(ctx, "UnionNotifyPOSPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyPOSSettle(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyPOSSettleArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyPOSSettleResult
	if err = p.c.Call(ctx, "UnionNotifyPOSSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyCashPay(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyCashPayArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyCashPayResult
	if err = p.c.Call(ctx, "UnionNotifyCashPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyCashRefund(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyCashRefundArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyCashRefundResult
	if err = p.c.Call(ctx, "UnionNotifyCashRefund", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyWithdraw(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyWithdrawArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyWithdrawResult
	if err = p.c.Call(ctx, "UnionNotifyWithdraw", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyMergeWithdraw(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyMergeWithdrawArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyMergeWithdrawResult
	if err = p.c.Call(ctx, "UnionNotifyMergeWithdraw", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyUnionPayNormal(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyUnionPayNormalArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyUnionPayNormalResult
	if err = p.c.Call(ctx, "UnionNotifyUnionPayNormal", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyUnionPayYzt(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyUnionPayYztArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyUnionPayYztResult
	if err = p.c.Call(ctx, "UnionNotifyUnionPayYzt", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyUnionPayPos(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyUnionPayPosArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyUnionPayPosResult
	if err = p.c.Call(ctx, "UnionNotifyUnionPayPos", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyGuaranteePay(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyGuaranteePayArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyGuaranteePayResult
	if err = p.c.Call(ctx, "UnionNotifyGuaranteePay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyGuaranteeWithdraw(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyGuaranteeWithdrawArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyGuaranteeWithdrawResult
	if err = p.c.Call(ctx, "UnionNotifyGuaranteeWithdraw", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyGuaranteeNormalWithdraw(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyGuaranteeNormalWithdrawArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyGuaranteeNormalWithdrawResult
	if err = p.c.Call(ctx, "UnionNotifyGuaranteeNormalWithdraw", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyMergeRefund(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyMergeRefundArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyMergeRefundResult
	if err = p.c.Call(ctx, "UnionNotifyMergeRefund", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyMergeSettle(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyMergeSettleArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyMergeSettleResult
	if err = p.c.Call(ctx, "UnionNotifyMergeSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyAgreePubPay(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyAgreePubPayArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyAgreePubPayResult
	if err = p.c.Call(ctx, "UnionNotifyAgreePubPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyAgreePriPay(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyAgreePriPayArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyAgreePriPayResult
	if err = p.c.Call(ctx, "UnionNotifyAgreePriPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyAgreePriSign(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyAgreePriSignArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyAgreePriSignResult
	if err = p.c.Call(ctx, "UnionNotifyAgreePriSign", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyAgreePriUnSign(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyAgreePriUnSignArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyAgreePriUnSignResult
	if err = p.c.Call(ctx, "UnionNotifyAgreePriUnSign", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ScanTradeOrder(ctx context.Context, req *payment.ScanTradeOrderReq) (r *payment.ScanTradeOrderResp, err error) {
	var _args payment.PaymentServiceScanTradeOrderArgs
	_args.Req = req
	var _result payment.PaymentServiceScanTradeOrderResult
	if err = p.c.Call(ctx, "ScanTradeOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PayTimeoutHandle(ctx context.Context, req *payment.PayTimeoutHandleReq) (r *payment.PayTimeoutHandleResp, err error) {
	var _args payment.PaymentServicePayTimeoutHandleArgs
	_args.Req = req
	var _result payment.PaymentServicePayTimeoutHandleResult
	if err = p.c.Call(ctx, "PayTimeoutHandle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) TradeCompensate(ctx context.Context, req *payment.TradeCompensateReq) (r *payment.TradeCompensateResp, err error) {
	var _args payment.PaymentServiceTradeCompensateArgs
	_args.Req = req
	var _result payment.PaymentServiceTradeCompensateResult
	if err = p.c.Call(ctx, "TradeCompensate", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ScanTradeOrderV2(ctx context.Context, req *payment.ScanTradeOrderV2Req) (r *payment.ScanTradeOrderResp, err error) {
	var _args payment.PaymentServiceScanTradeOrderV2Args
	_args.Req = req
	var _result payment.PaymentServiceScanTradeOrderV2Result
	if err = p.c.Call(ctx, "ScanTradeOrderV2", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetFundInfo(ctx context.Context, req *payment.GetFundInfoReq) (r *payment.GetFundInfoResp, err error) {
	var _args payment.PaymentServiceGetFundInfoArgs
	_args.Req = req
	var _result payment.PaymentServiceGetFundInfoResult
	if err = p.c.Call(ctx, "GetFundInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AccountBalanceAlarmSchedule(ctx context.Context, req *payment.AccountBalanceAlarmScheduleReq) (r *payment.AccountBalanceAlarmScheduleResp, err error) {
	var _args payment.PaymentServiceAccountBalanceAlarmScheduleArgs
	_args.Req = req
	var _result payment.PaymentServiceAccountBalanceAlarmScheduleResult
	if err = p.c.Call(ctx, "AccountBalanceAlarmSchedule", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AccountBalanceAlarmScheduleV2(ctx context.Context, req *payment.AccountBalanceAlarmScheduleV2Req) (r *payment.AccountBalanceAlarmScheduleV2Resp, err error) {
	var _args payment.PaymentServiceAccountBalanceAlarmScheduleV2Args
	_args.Req = req
	var _result payment.PaymentServiceAccountBalanceAlarmScheduleV2Result
	if err = p.c.Call(ctx, "AccountBalanceAlarmScheduleV2", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyCloseRefund(ctx context.Context, req *payment.UnionNotifyRequest) (r *payment.UnionNotifyResponse, err error) {
	var _args payment.PaymentServiceUnionNotifyCloseRefundArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyCloseRefundResult
	if err = p.c.Call(ctx, "UnionNotifyCloseRefund", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PaymentTobBizCall(ctx context.Context, req *payment_tob.PaymentTobBizCallReq) (r *payment_tob.PaymentTobBizCallRsp, err error) {
	var _args payment.PaymentServicePaymentTobBizCallArgs
	_args.Req = req
	var _result payment.PaymentServicePaymentTobBizCallResult
	if err = p.c.Call(ctx, "PaymentTobBizCall", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PaymentYBBizCall(ctx context.Context, req *payment.PaymentYBBizCallReq) (r *payment.PaymentYBBizCallResp, err error) {
	var _args payment.PaymentServicePaymentYBBizCallArgs
	_args.Req = req
	var _result payment.PaymentServicePaymentYBBizCallResult
	if err = p.c.Call(ctx, "PaymentYBBizCall", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PaymentYBBizCallV2(ctx context.Context, req *payment.PaymentYBBizCallV2Req) (r *payment.PaymentYBBizCallResp, err error) {
	var _args payment.PaymentServicePaymentYBBizCallV2Args
	_args.Req = req
	var _result payment.PaymentServicePaymentYBBizCallV2Result
	if err = p.c.Call(ctx, "PaymentYBBizCallV2", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) YztOfflineUpsertAndAcceptance(ctx context.Context, req *payment.YztOfflineUpsertAndAcceptanceRequest) (r *payment.YztOfflineUpsertAndAcceptanceResponse, err error) {
	var _args payment.PaymentServiceYztOfflineUpsertAndAcceptanceArgs
	_args.Req = req
	var _result payment.PaymentServiceYztOfflineUpsertAndAcceptanceResult
	if err = p.c.Call(ctx, "YztOfflineUpsertAndAcceptance", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) YztOfflineUpsert(ctx context.Context, req *payment.YztOfflineUpsertRequest) (r *payment.YztOfflineUpsertResponse, err error) {
	var _args payment.PaymentServiceYztOfflineUpsertArgs
	_args.Req = req
	var _result payment.PaymentServiceYztOfflineUpsertResult
	if err = p.c.Call(ctx, "YztOfflineUpsert", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AllinSendAgreementSms(ctx context.Context, req *payment.AllinSendAgreementSmsReq) (r *payment.AllinSendAgreementSmsResp, err error) {
	var _args payment.PaymentServiceAllinSendAgreementSmsArgs
	_args.Req = req
	var _result payment.PaymentServiceAllinSendAgreementSmsResult
	if err = p.c.Call(ctx, "AllinSendAgreementSms", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AllinSignAgreement(ctx context.Context, req *payment.AllinSignAgreementReq) (r *payment.AllinSignAgreementResp, err error) {
	var _args payment.PaymentServiceAllinSignAgreementArgs
	_args.Req = req
	var _result payment.PaymentServiceAllinSignAgreementResult
	if err = p.c.Call(ctx, "AllinSignAgreement", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AllinGetAgreementInfo(ctx context.Context, req *payment.AllinGetAgreementInfoReq) (r *payment.AllinGetAgreementInfoResp, err error) {
	var _args payment.PaymentServiceAllinGetAgreementInfoArgs
	_args.Req = req
	var _result payment.PaymentServiceAllinGetAgreementInfoResult
	if err = p.c.Call(ctx, "AllinGetAgreementInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AllinGetCardBin(ctx context.Context, req *payment.AllinGetCardBinReq) (r *payment.AllinGetCardBinResp, err error) {
	var _args payment.PaymentServiceAllinGetCardBinArgs
	_args.Req = req
	var _result payment.PaymentServiceAllinGetCardBinResult
	if err = p.c.Call(ctx, "AllinGetCardBin", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AgreementPriSign(ctx context.Context, req *payment.AgreementPriSignReq) (r *payment.AgreementPriSignResp, err error) {
	var _args payment.PaymentServiceAgreementPriSignArgs
	_args.Req = req
	var _result payment.PaymentServiceAgreementPriSignResult
	if err = p.c.Call(ctx, "AgreementPriSign", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AgreementPriUnSign(ctx context.Context, req *payment.AgreementPriUnSignReq) (r *payment.AgreementPriUnSignResp, err error) {
	var _args payment.PaymentServiceAgreementPriUnSignArgs
	_args.Req = req
	var _result payment.PaymentServiceAgreementPriUnSignResult
	if err = p.c.Call(ctx, "AgreementPriUnSign", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AgreementPriPay(ctx context.Context, req *payment.AgreementPriPayReq) (r *payment.AgreementPriPayResp, err error) {
	var _args payment.PaymentServiceAgreementPriPayArgs
	_args.Req = req
	var _result payment.PaymentServiceAgreementPriPayResult
	if err = p.c.Call(ctx, "AgreementPriPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryAgreementSign(ctx context.Context, req *payment.QueryAgreementSignReq) (r *payment.QueryAgreementSignResp, err error) {
	var _args payment.PaymentServiceQueryAgreementSignArgs
	_args.Req = req
	var _result payment.PaymentServiceQueryAgreementSignResult
	if err = p.c.Call(ctx, "QueryAgreementSign", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryAgreementPay(ctx context.Context, req *payment.QueryAgreementPayReq) (r *payment.QueryAgreementPayResp, err error) {
	var _args payment.PaymentServiceQueryAgreementPayArgs
	_args.Req = req
	var _result payment.PaymentServiceQueryAgreementPayResult
	if err = p.c.Call(ctx, "QueryAgreementPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AgreementPayTimeout(ctx context.Context, req *payment.AgreementPayTimeoutReq) (r *payment.AgreementPayTimeoutResp, err error) {
	var _args payment.PaymentServiceAgreementPayTimeoutArgs
	_args.Req = req
	var _result payment.PaymentServiceAgreementPayTimeoutResult
	if err = p.c.Call(ctx, "AgreementPayTimeout", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AgreementPay(ctx context.Context, req *payment.AgreementPayReq) (r *payment.AgreementPayResp, err error) {
	var _args payment.PaymentServiceAgreementPayArgs
	_args.Req = req
	var _result payment.PaymentServiceAgreementPayResult
	if err = p.c.Call(ctx, "AgreementPay", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) YBScanTradeOrder(ctx context.Context, req *payment.YBScanTradeOrderReq) (r *payment.YBScanTradeOrderResp, err error) {
	var _args payment.PaymentServiceYBScanTradeOrderArgs
	_args.Req = req
	var _result payment.PaymentServiceYBScanTradeOrderResult
	if err = p.c.Call(ctx, "YBScanTradeOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifyLife(ctx context.Context, req *payment.UnionNotifyLifeReq) (r *payment.UnionNotifyLifeResp, err error) {
	var _args payment.PaymentServiceUnionNotifyLifeArgs
	_args.Req = req
	var _result payment.PaymentServiceUnionNotifyLifeResult
	if err = p.c.Call(ctx, "UnionNotifyLife", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
