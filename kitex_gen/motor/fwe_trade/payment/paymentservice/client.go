// Code generated by Kitex v1.20.3. DO NOT EDIT.

package paymentservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	payment "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment"
	payment_tob "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/payment_tob"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	CreateCashPay(ctx context.Context, req *payment.CreateCashPayReq, callOptions ...callopt.Option) (r *payment.CreateCashPayRsp, err error)
	CreateCashRefund(ctx context.Context, req *payment.CreateCashRefundReq, callOptions ...callopt.Option) (r *payment.CreateCashRefundRsp, err error)
	WithdrawDeposit(ctx context.Context, req *payment.WithdrawDepositReq, callOptions ...callopt.Option) (r *payment.WithdrawDepositRsp, err error)
	MergeWithdrawDeposit(ctx context.Context, req *payment.MergeWithdrawDepositReq, callOptions ...callopt.Option) (r *payment.MergeWithdrawDepositResp, err error)
	GuaranteeWithdraw(ctx context.Context, req *payment.GuaranteeWithdrawReq, callOptions ...callopt.Option) (r *payment.GuaranteeWithdrawResp, err error)
	TimerWithdraw(ctx context.Context, req *payment.TimerWithdrawReq, callOptions ...callopt.Option) (r *payment.TimerWithdrawResp, err error)
	Transfer(ctx context.Context, req *payment.TransferReq, callOptions ...callopt.Option) (r *payment.TransferResp, err error)
	CreatePOSPay(ctx context.Context, req *payment.CreatePOSPayReq, callOptions ...callopt.Option) (r *payment.CreatePOSPayResp, err error)
	ClosePOSPay(ctx context.Context, req *payment.ClosePOSPayReq, callOptions ...callopt.Option) (r *payment.ClosePOSPayResp, err error)
	CreatePOSSettle(ctx context.Context, req *payment.CreatePOSSettleReq, callOptions ...callopt.Option) (r *payment.CreatePOSSettleResp, err error)
	CreateGuaranteePay(ctx context.Context, req *payment.CreateGuaranteePayReq, callOptions ...callopt.Option) (r *payment.CreateGuaranteePayResp, err error)
	MergeRefund(ctx context.Context, req *payment.MergeRefundReq, callOptions ...callopt.Option) (r *payment.MergeRefundResp, err error)
	MergeRefundAfterSettle(ctx context.Context, req *payment.MergeRefundAfterSettleReq, callOptions ...callopt.Option) (r *payment.MergeRefundAfterSettleResp, err error)
	RefundAfterSettle(ctx context.Context, req *payment.RefundAfterSettleReq, callOptions ...callopt.Option) (r *payment.RefundAfterSettleResp, err error)
	UnionNotifyMergeReRefundAfterSettle(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	RefundSettle(ctx context.Context, req *payment.RefundSettleReq, callOptions ...callopt.Option) (r *payment.RefundSettleResp, err error)
	SecondPayStart(ctx context.Context, req *payment.SecondPayReq, callOptions ...callopt.Option) (r *payment.SecondPayResp, err error)
	SecondSettleStart(ctx context.Context, req *payment.SecondSettleReq, callOptions ...callopt.Option) (r *payment.SecondSettleResp, err error)
	LifeRefundAutoAudit(ctx context.Context, req *payment.LifeRefundAutoAuditReq, callOptions ...callopt.Option) (r *payment.LifeRefundAutoAuditResp, err error)
	UnionNotifyRefundSettle(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifySecondPay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifySecondSettle(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	MergeSettle(ctx context.Context, req *payment.MergeSettleReq, callOptions ...callopt.Option) (r *payment.MergeSettleResp, err error)
	MergeSettleV2(ctx context.Context, req *payment.MergeSettleV2Req, callOptions ...callopt.Option) (r *payment.MergeSettleV2Resp, err error)
	CreateOfflinePay(ctx context.Context, req *payment.CreateOfflinePayReq, callOptions ...callopt.Option) (r *payment.CreateOfflinePayResp, err error)
	PayRecognition(ctx context.Context, req *payment.PayRecognitionReq, callOptions ...callopt.Option) (r *payment.PayRecognitionResp, err error)
	ClosePayOrder(ctx context.Context, req *payment.ClosePayOrderReq, callOptions ...callopt.Option) (r *payment.ClosePayOrderResp, err error)
	CreateUnionPay(ctx context.Context, req *payment.CreateUnionPayReq, callOptions ...callopt.Option) (r *payment.CreateUnionPayResp, err error)
	QueryUnionPay(ctx context.Context, req *payment.QueryUnionPayReq, callOptions ...callopt.Option) (r *payment.QueryUnionPayResp, err error)
	CompleteUnionPay(ctx context.Context, req *payment.CompleteUnionPayReq, callOptions ...callopt.Option) (r *payment.CompleteUnionPayResp, err error)
	CreateUnionPayInner(ctx context.Context, req *payment.CreateUnionPayInnerReq, callOptions ...callopt.Option) (r *payment.CreateUnionPayInnerResp, err error)
	CloseUnionPay(ctx context.Context, req *payment.CloseUnionPayReq, callOptions ...callopt.Option) (r *payment.CloseUnionPayResp, err error)
	PaymentCallback(ctx context.Context, req *payment.PaymentCallbackReq, callOptions ...callopt.Option) (r *payment.PaymentCallbackResp, err error)
	FreezeUnionPay(ctx context.Context, req *payment.FreezeUnionPayReq, callOptions ...callopt.Option) (r *payment.FreezeUnionPayResp, err error)
	UnFreezeUnionPay(ctx context.Context, req *payment.UnFreezeUnionPayReq, callOptions ...callopt.Option) (r *payment.UnFreezeUnionPayResp, err error)
	UpdateUnionPay(ctx context.Context, req *payment.UpdateUnionPayReq, callOptions ...callopt.Option) (r *payment.UpdateUnionPayResp, err error)
	QueryFinancePayList(ctx context.Context, req *payment.QueryFinancePayListReq, callOptions ...callopt.Option) (r *payment.QueryFinancePayListResp, err error)
	QueryOrderPayList(ctx context.Context, req *payment.QueryOrderPayListReq, callOptions ...callopt.Option) (r *payment.QueryOrderPayListResp, err error)
	QueryOrderPaymentList(ctx context.Context, req *payment.QueryOrderPaymentListReq, callOptions ...callopt.Option) (r *payment.QueryOrderPaymentListResp, err error)
	QueryPayDetail(ctx context.Context, req *payment.QueryPayDetailReq, callOptions ...callopt.Option) (r *payment.QueryPayDetailResp, err error)
	UnionNotifyPOSPay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyPOSSettle(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyCashPay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyCashRefund(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyWithdraw(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyMergeWithdraw(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyUnionPayNormal(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyUnionPayYzt(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyUnionPayPos(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyGuaranteePay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyGuaranteeWithdraw(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyGuaranteeNormalWithdraw(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyMergeRefund(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyMergeSettle(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyAgreePubPay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyAgreePriPay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyAgreePriSign(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	UnionNotifyAgreePriUnSign(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	ScanTradeOrder(ctx context.Context, req *payment.ScanTradeOrderReq, callOptions ...callopt.Option) (r *payment.ScanTradeOrderResp, err error)
	PayTimeoutHandle(ctx context.Context, req *payment.PayTimeoutHandleReq, callOptions ...callopt.Option) (r *payment.PayTimeoutHandleResp, err error)
	TradeCompensate(ctx context.Context, req *payment.TradeCompensateReq, callOptions ...callopt.Option) (r *payment.TradeCompensateResp, err error)
	ScanTradeOrderV2(ctx context.Context, req *payment.ScanTradeOrderV2Req, callOptions ...callopt.Option) (r *payment.ScanTradeOrderResp, err error)
	GetFundInfo(ctx context.Context, req *payment.GetFundInfoReq, callOptions ...callopt.Option) (r *payment.GetFundInfoResp, err error)
	AccountBalanceAlarmSchedule(ctx context.Context, req *payment.AccountBalanceAlarmScheduleReq, callOptions ...callopt.Option) (r *payment.AccountBalanceAlarmScheduleResp, err error)
	AccountBalanceAlarmScheduleV2(ctx context.Context, req *payment.AccountBalanceAlarmScheduleV2Req, callOptions ...callopt.Option) (r *payment.AccountBalanceAlarmScheduleV2Resp, err error)
	UnionNotifyCloseRefund(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error)
	PaymentTobBizCall(ctx context.Context, req *payment_tob.PaymentTobBizCallReq, callOptions ...callopt.Option) (r *payment_tob.PaymentTobBizCallRsp, err error)
	PaymentYBBizCall(ctx context.Context, req *payment.PaymentYBBizCallReq, callOptions ...callopt.Option) (r *payment.PaymentYBBizCallResp, err error)
	PaymentYBBizCallV2(ctx context.Context, req *payment.PaymentYBBizCallV2Req, callOptions ...callopt.Option) (r *payment.PaymentYBBizCallResp, err error)
	YztOfflineUpsertAndAcceptance(ctx context.Context, req *payment.YztOfflineUpsertAndAcceptanceRequest, callOptions ...callopt.Option) (r *payment.YztOfflineUpsertAndAcceptanceResponse, err error)
	YztOfflineUpsert(ctx context.Context, req *payment.YztOfflineUpsertRequest, callOptions ...callopt.Option) (r *payment.YztOfflineUpsertResponse, err error)
	AllinSendAgreementSms(ctx context.Context, req *payment.AllinSendAgreementSmsReq, callOptions ...callopt.Option) (r *payment.AllinSendAgreementSmsResp, err error)
	AllinSignAgreement(ctx context.Context, req *payment.AllinSignAgreementReq, callOptions ...callopt.Option) (r *payment.AllinSignAgreementResp, err error)
	AllinGetAgreementInfo(ctx context.Context, req *payment.AllinGetAgreementInfoReq, callOptions ...callopt.Option) (r *payment.AllinGetAgreementInfoResp, err error)
	AllinGetCardBin(ctx context.Context, req *payment.AllinGetCardBinReq, callOptions ...callopt.Option) (r *payment.AllinGetCardBinResp, err error)
	AgreementPriSign(ctx context.Context, req *payment.AgreementPriSignReq, callOptions ...callopt.Option) (r *payment.AgreementPriSignResp, err error)
	AgreementPriUnSign(ctx context.Context, req *payment.AgreementPriUnSignReq, callOptions ...callopt.Option) (r *payment.AgreementPriUnSignResp, err error)
	AgreementPriPay(ctx context.Context, req *payment.AgreementPriPayReq, callOptions ...callopt.Option) (r *payment.AgreementPriPayResp, err error)
	QueryAgreementSign(ctx context.Context, req *payment.QueryAgreementSignReq, callOptions ...callopt.Option) (r *payment.QueryAgreementSignResp, err error)
	QueryAgreementPay(ctx context.Context, req *payment.QueryAgreementPayReq, callOptions ...callopt.Option) (r *payment.QueryAgreementPayResp, err error)
	AgreementPayTimeout(ctx context.Context, req *payment.AgreementPayTimeoutReq, callOptions ...callopt.Option) (r *payment.AgreementPayTimeoutResp, err error)
	AgreementPay(ctx context.Context, req *payment.AgreementPayReq, callOptions ...callopt.Option) (r *payment.AgreementPayResp, err error)
	YBScanTradeOrder(ctx context.Context, req *payment.YBScanTradeOrderReq, callOptions ...callopt.Option) (r *payment.YBScanTradeOrderResp, err error)
	UnionNotifyLife(ctx context.Context, req *payment.UnionNotifyLifeReq, callOptions ...callopt.Option) (r *payment.UnionNotifyLifeResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kPaymentServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kPaymentServiceClient struct {
	*kClient
}

func (p *kPaymentServiceClient) CreateCashPay(ctx context.Context, req *payment.CreateCashPayReq, callOptions ...callopt.Option) (r *payment.CreateCashPayRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateCashPay(ctx, req)
}

func (p *kPaymentServiceClient) CreateCashRefund(ctx context.Context, req *payment.CreateCashRefundReq, callOptions ...callopt.Option) (r *payment.CreateCashRefundRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateCashRefund(ctx, req)
}

func (p *kPaymentServiceClient) WithdrawDeposit(ctx context.Context, req *payment.WithdrawDepositReq, callOptions ...callopt.Option) (r *payment.WithdrawDepositRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.WithdrawDeposit(ctx, req)
}

func (p *kPaymentServiceClient) MergeWithdrawDeposit(ctx context.Context, req *payment.MergeWithdrawDepositReq, callOptions ...callopt.Option) (r *payment.MergeWithdrawDepositResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MergeWithdrawDeposit(ctx, req)
}

func (p *kPaymentServiceClient) GuaranteeWithdraw(ctx context.Context, req *payment.GuaranteeWithdrawReq, callOptions ...callopt.Option) (r *payment.GuaranteeWithdrawResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GuaranteeWithdraw(ctx, req)
}

func (p *kPaymentServiceClient) TimerWithdraw(ctx context.Context, req *payment.TimerWithdrawReq, callOptions ...callopt.Option) (r *payment.TimerWithdrawResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.TimerWithdraw(ctx, req)
}

func (p *kPaymentServiceClient) Transfer(ctx context.Context, req *payment.TransferReq, callOptions ...callopt.Option) (r *payment.TransferResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Transfer(ctx, req)
}

func (p *kPaymentServiceClient) CreatePOSPay(ctx context.Context, req *payment.CreatePOSPayReq, callOptions ...callopt.Option) (r *payment.CreatePOSPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreatePOSPay(ctx, req)
}

func (p *kPaymentServiceClient) ClosePOSPay(ctx context.Context, req *payment.ClosePOSPayReq, callOptions ...callopt.Option) (r *payment.ClosePOSPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ClosePOSPay(ctx, req)
}

func (p *kPaymentServiceClient) CreatePOSSettle(ctx context.Context, req *payment.CreatePOSSettleReq, callOptions ...callopt.Option) (r *payment.CreatePOSSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreatePOSSettle(ctx, req)
}

func (p *kPaymentServiceClient) CreateGuaranteePay(ctx context.Context, req *payment.CreateGuaranteePayReq, callOptions ...callopt.Option) (r *payment.CreateGuaranteePayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateGuaranteePay(ctx, req)
}

func (p *kPaymentServiceClient) MergeRefund(ctx context.Context, req *payment.MergeRefundReq, callOptions ...callopt.Option) (r *payment.MergeRefundResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MergeRefund(ctx, req)
}

func (p *kPaymentServiceClient) MergeRefundAfterSettle(ctx context.Context, req *payment.MergeRefundAfterSettleReq, callOptions ...callopt.Option) (r *payment.MergeRefundAfterSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MergeRefundAfterSettle(ctx, req)
}

func (p *kPaymentServiceClient) RefundAfterSettle(ctx context.Context, req *payment.RefundAfterSettleReq, callOptions ...callopt.Option) (r *payment.RefundAfterSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.RefundAfterSettle(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyMergeReRefundAfterSettle(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyMergeReRefundAfterSettle(ctx, req)
}

func (p *kPaymentServiceClient) RefundSettle(ctx context.Context, req *payment.RefundSettleReq, callOptions ...callopt.Option) (r *payment.RefundSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.RefundSettle(ctx, req)
}

func (p *kPaymentServiceClient) SecondPayStart(ctx context.Context, req *payment.SecondPayReq, callOptions ...callopt.Option) (r *payment.SecondPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SecondPayStart(ctx, req)
}

func (p *kPaymentServiceClient) SecondSettleStart(ctx context.Context, req *payment.SecondSettleReq, callOptions ...callopt.Option) (r *payment.SecondSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SecondSettleStart(ctx, req)
}

func (p *kPaymentServiceClient) LifeRefundAutoAudit(ctx context.Context, req *payment.LifeRefundAutoAuditReq, callOptions ...callopt.Option) (r *payment.LifeRefundAutoAuditResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.LifeRefundAutoAudit(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyRefundSettle(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyRefundSettle(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifySecondPay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifySecondPay(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifySecondSettle(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifySecondSettle(ctx, req)
}

func (p *kPaymentServiceClient) MergeSettle(ctx context.Context, req *payment.MergeSettleReq, callOptions ...callopt.Option) (r *payment.MergeSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MergeSettle(ctx, req)
}

func (p *kPaymentServiceClient) MergeSettleV2(ctx context.Context, req *payment.MergeSettleV2Req, callOptions ...callopt.Option) (r *payment.MergeSettleV2Resp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MergeSettleV2(ctx, req)
}

func (p *kPaymentServiceClient) CreateOfflinePay(ctx context.Context, req *payment.CreateOfflinePayReq, callOptions ...callopt.Option) (r *payment.CreateOfflinePayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateOfflinePay(ctx, req)
}

func (p *kPaymentServiceClient) PayRecognition(ctx context.Context, req *payment.PayRecognitionReq, callOptions ...callopt.Option) (r *payment.PayRecognitionResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PayRecognition(ctx, req)
}

func (p *kPaymentServiceClient) ClosePayOrder(ctx context.Context, req *payment.ClosePayOrderReq, callOptions ...callopt.Option) (r *payment.ClosePayOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ClosePayOrder(ctx, req)
}

func (p *kPaymentServiceClient) CreateUnionPay(ctx context.Context, req *payment.CreateUnionPayReq, callOptions ...callopt.Option) (r *payment.CreateUnionPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateUnionPay(ctx, req)
}

func (p *kPaymentServiceClient) QueryUnionPay(ctx context.Context, req *payment.QueryUnionPayReq, callOptions ...callopt.Option) (r *payment.QueryUnionPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryUnionPay(ctx, req)
}

func (p *kPaymentServiceClient) CompleteUnionPay(ctx context.Context, req *payment.CompleteUnionPayReq, callOptions ...callopt.Option) (r *payment.CompleteUnionPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CompleteUnionPay(ctx, req)
}

func (p *kPaymentServiceClient) CreateUnionPayInner(ctx context.Context, req *payment.CreateUnionPayInnerReq, callOptions ...callopt.Option) (r *payment.CreateUnionPayInnerResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateUnionPayInner(ctx, req)
}

func (p *kPaymentServiceClient) CloseUnionPay(ctx context.Context, req *payment.CloseUnionPayReq, callOptions ...callopt.Option) (r *payment.CloseUnionPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CloseUnionPay(ctx, req)
}

func (p *kPaymentServiceClient) PaymentCallback(ctx context.Context, req *payment.PaymentCallbackReq, callOptions ...callopt.Option) (r *payment.PaymentCallbackResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PaymentCallback(ctx, req)
}

func (p *kPaymentServiceClient) FreezeUnionPay(ctx context.Context, req *payment.FreezeUnionPayReq, callOptions ...callopt.Option) (r *payment.FreezeUnionPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FreezeUnionPay(ctx, req)
}

func (p *kPaymentServiceClient) UnFreezeUnionPay(ctx context.Context, req *payment.UnFreezeUnionPayReq, callOptions ...callopt.Option) (r *payment.UnFreezeUnionPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnFreezeUnionPay(ctx, req)
}

func (p *kPaymentServiceClient) UpdateUnionPay(ctx context.Context, req *payment.UpdateUnionPayReq, callOptions ...callopt.Option) (r *payment.UpdateUnionPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateUnionPay(ctx, req)
}

func (p *kPaymentServiceClient) QueryFinancePayList(ctx context.Context, req *payment.QueryFinancePayListReq, callOptions ...callopt.Option) (r *payment.QueryFinancePayListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFinancePayList(ctx, req)
}

func (p *kPaymentServiceClient) QueryOrderPayList(ctx context.Context, req *payment.QueryOrderPayListReq, callOptions ...callopt.Option) (r *payment.QueryOrderPayListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryOrderPayList(ctx, req)
}

func (p *kPaymentServiceClient) QueryOrderPaymentList(ctx context.Context, req *payment.QueryOrderPaymentListReq, callOptions ...callopt.Option) (r *payment.QueryOrderPaymentListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryOrderPaymentList(ctx, req)
}

func (p *kPaymentServiceClient) QueryPayDetail(ctx context.Context, req *payment.QueryPayDetailReq, callOptions ...callopt.Option) (r *payment.QueryPayDetailResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryPayDetail(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyPOSPay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyPOSPay(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyPOSSettle(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyPOSSettle(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyCashPay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyCashPay(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyCashRefund(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyCashRefund(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyWithdraw(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyWithdraw(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyMergeWithdraw(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyMergeWithdraw(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyUnionPayNormal(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyUnionPayNormal(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyUnionPayYzt(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyUnionPayYzt(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyUnionPayPos(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyUnionPayPos(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyGuaranteePay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyGuaranteePay(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyGuaranteeWithdraw(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyGuaranteeWithdraw(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyGuaranteeNormalWithdraw(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyGuaranteeNormalWithdraw(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyMergeRefund(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyMergeRefund(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyMergeSettle(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyMergeSettle(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyAgreePubPay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyAgreePubPay(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyAgreePriPay(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyAgreePriPay(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyAgreePriSign(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyAgreePriSign(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyAgreePriUnSign(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyAgreePriUnSign(ctx, req)
}

func (p *kPaymentServiceClient) ScanTradeOrder(ctx context.Context, req *payment.ScanTradeOrderReq, callOptions ...callopt.Option) (r *payment.ScanTradeOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ScanTradeOrder(ctx, req)
}

func (p *kPaymentServiceClient) PayTimeoutHandle(ctx context.Context, req *payment.PayTimeoutHandleReq, callOptions ...callopt.Option) (r *payment.PayTimeoutHandleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PayTimeoutHandle(ctx, req)
}

func (p *kPaymentServiceClient) TradeCompensate(ctx context.Context, req *payment.TradeCompensateReq, callOptions ...callopt.Option) (r *payment.TradeCompensateResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.TradeCompensate(ctx, req)
}

func (p *kPaymentServiceClient) ScanTradeOrderV2(ctx context.Context, req *payment.ScanTradeOrderV2Req, callOptions ...callopt.Option) (r *payment.ScanTradeOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ScanTradeOrderV2(ctx, req)
}

func (p *kPaymentServiceClient) GetFundInfo(ctx context.Context, req *payment.GetFundInfoReq, callOptions ...callopt.Option) (r *payment.GetFundInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetFundInfo(ctx, req)
}

func (p *kPaymentServiceClient) AccountBalanceAlarmSchedule(ctx context.Context, req *payment.AccountBalanceAlarmScheduleReq, callOptions ...callopt.Option) (r *payment.AccountBalanceAlarmScheduleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AccountBalanceAlarmSchedule(ctx, req)
}

func (p *kPaymentServiceClient) AccountBalanceAlarmScheduleV2(ctx context.Context, req *payment.AccountBalanceAlarmScheduleV2Req, callOptions ...callopt.Option) (r *payment.AccountBalanceAlarmScheduleV2Resp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AccountBalanceAlarmScheduleV2(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyCloseRefund(ctx context.Context, req *payment.UnionNotifyRequest, callOptions ...callopt.Option) (r *payment.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyCloseRefund(ctx, req)
}

func (p *kPaymentServiceClient) PaymentTobBizCall(ctx context.Context, req *payment_tob.PaymentTobBizCallReq, callOptions ...callopt.Option) (r *payment_tob.PaymentTobBizCallRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PaymentTobBizCall(ctx, req)
}

func (p *kPaymentServiceClient) PaymentYBBizCall(ctx context.Context, req *payment.PaymentYBBizCallReq, callOptions ...callopt.Option) (r *payment.PaymentYBBizCallResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PaymentYBBizCall(ctx, req)
}

func (p *kPaymentServiceClient) PaymentYBBizCallV2(ctx context.Context, req *payment.PaymentYBBizCallV2Req, callOptions ...callopt.Option) (r *payment.PaymentYBBizCallResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PaymentYBBizCallV2(ctx, req)
}

func (p *kPaymentServiceClient) YztOfflineUpsertAndAcceptance(ctx context.Context, req *payment.YztOfflineUpsertAndAcceptanceRequest, callOptions ...callopt.Option) (r *payment.YztOfflineUpsertAndAcceptanceResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.YztOfflineUpsertAndAcceptance(ctx, req)
}

func (p *kPaymentServiceClient) YztOfflineUpsert(ctx context.Context, req *payment.YztOfflineUpsertRequest, callOptions ...callopt.Option) (r *payment.YztOfflineUpsertResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.YztOfflineUpsert(ctx, req)
}

func (p *kPaymentServiceClient) AllinSendAgreementSms(ctx context.Context, req *payment.AllinSendAgreementSmsReq, callOptions ...callopt.Option) (r *payment.AllinSendAgreementSmsResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AllinSendAgreementSms(ctx, req)
}

func (p *kPaymentServiceClient) AllinSignAgreement(ctx context.Context, req *payment.AllinSignAgreementReq, callOptions ...callopt.Option) (r *payment.AllinSignAgreementResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AllinSignAgreement(ctx, req)
}

func (p *kPaymentServiceClient) AllinGetAgreementInfo(ctx context.Context, req *payment.AllinGetAgreementInfoReq, callOptions ...callopt.Option) (r *payment.AllinGetAgreementInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AllinGetAgreementInfo(ctx, req)
}

func (p *kPaymentServiceClient) AllinGetCardBin(ctx context.Context, req *payment.AllinGetCardBinReq, callOptions ...callopt.Option) (r *payment.AllinGetCardBinResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AllinGetCardBin(ctx, req)
}

func (p *kPaymentServiceClient) AgreementPriSign(ctx context.Context, req *payment.AgreementPriSignReq, callOptions ...callopt.Option) (r *payment.AgreementPriSignResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AgreementPriSign(ctx, req)
}

func (p *kPaymentServiceClient) AgreementPriUnSign(ctx context.Context, req *payment.AgreementPriUnSignReq, callOptions ...callopt.Option) (r *payment.AgreementPriUnSignResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AgreementPriUnSign(ctx, req)
}

func (p *kPaymentServiceClient) AgreementPriPay(ctx context.Context, req *payment.AgreementPriPayReq, callOptions ...callopt.Option) (r *payment.AgreementPriPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AgreementPriPay(ctx, req)
}

func (p *kPaymentServiceClient) QueryAgreementSign(ctx context.Context, req *payment.QueryAgreementSignReq, callOptions ...callopt.Option) (r *payment.QueryAgreementSignResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryAgreementSign(ctx, req)
}

func (p *kPaymentServiceClient) QueryAgreementPay(ctx context.Context, req *payment.QueryAgreementPayReq, callOptions ...callopt.Option) (r *payment.QueryAgreementPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryAgreementPay(ctx, req)
}

func (p *kPaymentServiceClient) AgreementPayTimeout(ctx context.Context, req *payment.AgreementPayTimeoutReq, callOptions ...callopt.Option) (r *payment.AgreementPayTimeoutResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AgreementPayTimeout(ctx, req)
}

func (p *kPaymentServiceClient) AgreementPay(ctx context.Context, req *payment.AgreementPayReq, callOptions ...callopt.Option) (r *payment.AgreementPayResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AgreementPay(ctx, req)
}

func (p *kPaymentServiceClient) YBScanTradeOrder(ctx context.Context, req *payment.YBScanTradeOrderReq, callOptions ...callopt.Option) (r *payment.YBScanTradeOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.YBScanTradeOrder(ctx, req)
}

func (p *kPaymentServiceClient) UnionNotifyLife(ctx context.Context, req *payment.UnionNotifyLifeReq, callOptions ...callopt.Option) (r *payment.UnionNotifyLifeResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifyLife(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kPaymentServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
