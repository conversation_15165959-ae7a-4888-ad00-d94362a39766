// Code generated by Kitex v1.20.3. DO NOT EDIT.

package contractservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	core "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	CreateContract(ctx context.Context, req *core.CreateContractReq, callOptions ...callopt.Option) (r *core.CreateContractResp, err error)
	QueryContract(ctx context.Context, req *core.QueryContractReq, callOptions ...callopt.Option) (r *core.QueryContractResp, err error)
	WithdrawContract(ctx context.Context, req *core.WithdrawContractReq, callOptions ...callopt.Option) (r *core.WithdrawContractResp, err error)
	ApplyStamp(ctx context.Context, req *core.ApplyStampReq, callOptions ...callopt.Option) (r *core.ApplyStampResp, err error)
	AsyncApplyStamp(ctx context.Context, req *core.AsyncApplyStampReq, callOptions ...callopt.Option) (r *core.AsyncApplyStampResp, err error)
	DownloadTmpl(ctx context.Context, req *core.DownloadTmplReq, callOptions ...callopt.Option) (r *core.DownloadTmplResp, err error)
	QueryTmpl(ctx context.Context, req *core.QueryTmplReq, callOptions ...callopt.Option) (r *core.QueryTmplResp, err error)
	PreviewContract(ctx context.Context, req *core.PreviewContractReq, callOptions ...callopt.Option) (r *core.PreviewContractResp, err error)
	DownloadContract(ctx context.Context, req *core.DownloadContractReq, callOptions ...callopt.Option) (r *core.DownloadContractResp, err error)
	CreateContractV2(ctx context.Context, req *core.CreateContractV2Req, callOptions ...callopt.Option) (r *core.CreateContractV2Resp, err error)
	CreateContractV2WithApplySign(ctx context.Context, req *core.CreateContractV2Req, callOptions ...callopt.Option) (r *core.CreateContractV2Resp, err error)
	ApplySignNoCertLink(ctx context.Context, req *core.ApplySignNoCertLinkReq, callOptions ...callopt.Option) (r *core.ApplySignNoCertLinkResp, err error)
	UnionCreateContract(ctx context.Context, req *core.UnionCreateContractReq, callOptions ...callopt.Option) (r *core.UnionCreateContractResp, err error)
	ApplyNoCertLink(ctx context.Context, req *core.ApplyNoCertLinkReq, callOptions ...callopt.Option) (r *core.ApplyNoCertLinkResp, err error)
	BindCertResult_(ctx context.Context, req *core.BindCertResultReq, callOptions ...callopt.Option) (r *core.BindCertResultResp, err error)
	QueryContractDetail(ctx context.Context, req *core.QueryContractDetailReq, callOptions ...callopt.Option) (r *core.QueryContractDetailResp, err error)
	GetViewAndDownloadURL(ctx context.Context, req *core.GetViewAndDownloadURLReq, callOptions ...callopt.Option) (r *core.GetViewAndDownloadURLResp, err error)
	ViewAndDownloadByToken(ctx context.Context, req *core.ViewAndDownloadByTokenReq, callOptions ...callopt.Option) (r *core.ViewAndDownloadByTokenResp, err error)
	CancelContract(ctx context.Context, req *core.CancelContractReq, callOptions ...callopt.Option) (r *core.CancelContractResp, err error)
	QueryContractTmplList(ctx context.Context, req *core.QueryContractTmplListReq, callOptions ...callopt.Option) (r *core.QueryContractTmplListResp, err error)
	GetStampLink(ctx context.Context, req *core.GetStampLinkReq, callOptions ...callopt.Option) (r *core.GetStampLinkResp, err error)
	FetchStampLink(ctx context.Context, req *core.FetchStampLinkReq, callOptions ...callopt.Option) (r *core.FetchStampLinkResp, err error)
	SyncSignParty(ctx context.Context, req *core.SyncSignPartyReq, callOptions ...callopt.Option) (r *core.SyncSignPartyResp, err error)
	CertSignParty(ctx context.Context, req *core.CertSignPartyReq, callOptions ...callopt.Option) (r *core.CertSignPartyResp, err error)
	CertSignPartyQuery(ctx context.Context, req *core.CertSignPartyQueryReq, callOptions ...callopt.Option) (r *core.CertSignPartyQueryResp, err error)
	DeleteSignParty(ctx context.Context, req *core.DeleteSignPartyReq, callOptions ...callopt.Option) (r *core.DeleteSignPartyResp, err error)
	PreCheckSignParty(ctx context.Context, req *core.PreCheckSignPartyReq, callOptions ...callopt.Option) (r *core.PreCheckSignPartyResp, err error)
	StampCallback(ctx context.Context, req *core.ESignCallBackReq, callOptions ...callopt.Option) (r *core.CallBackRsp, err error)
	CertSignPartyCallBack(ctx context.Context, req *core.CertSignPartyCallBackReq, callOptions ...callopt.Option) (r *core.CertSignPartyCallBackResp, err error)
	InnerSyncData(ctx context.Context, req *core.InnerSyncDataReq, callOptions ...callopt.Option) (r *core.InnerSyncDataResp, err error)
	InnerSyncContStatus(ctx context.Context, req *core.InnerSyncContStatusReq, callOptions ...callopt.Option) (r *core.InnerSyncContStatusResp, err error)
	OACallback(ctx context.Context, req *core.OACallbackReq, callOptions ...callopt.Option) (r *core.OACallbackResp, err error)
	FeishuContCallback(ctx context.Context, req *core.FeishuContCallbackReq, callOptions ...callopt.Option) (r *core.FeishuContCallbackResp, err error)
	SendSmsByMobile(ctx context.Context, req *core.SendSmsByMobileReq, callOptions ...callopt.Option) (r *core.SendSmsByMobileResp, err error)
	SyncInfraContToFeishuCont(ctx context.Context, req *core.SyncInfraContToFeishuContReq, callOptions ...callopt.Option) (r *core.SyncInfraContToFeishuContResp, err error)
	PreValidateContract(ctx context.Context, req *core.PreValidateReq, callOptions ...callopt.Option) (r *core.PreValidateResp, err error)
	QueryContFormData(ctx context.Context, req *core.ContFormDataReq, callOptions ...callopt.Option) (r *core.ContFormDataResp, err error)
	QueryFeishuCont(ctx context.Context, req *core.QueryFeishuContReq, callOptions ...callopt.Option) (r *core.QueryFeishuContResp, err error)
	DownloadFeishuFile(ctx context.Context, req *core.DownloadFeishuFileReq, callOptions ...callopt.Option) (r *core.DownloadFeishuFileResp, err error)
	QueryContractStructInfo(ctx context.Context, req *core.QueryContractStructInfoReq, callOptions ...callopt.Option) (r *core.QueryContractStructInfoResp, err error)
	ParseContractStructFields(ctx context.Context, req *core.ParseContractStructFieldsReq, callOptions ...callopt.Option) (r *core.ParseContractStructFieldsResp, err error)
	AddMineCompanyParts(ctx context.Context, req *core.AddMineCompanyPartsReq, callOptions ...callopt.Option) (r *core.AddMineCompanyPartsResp, err error)
	QueryFeishuUserInfoList(ctx context.Context, req *core.QueryFeishuUserInfoListReq, callOptions ...callopt.Option) (r *core.QueryFeishuUserInfoListResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kContractServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kContractServiceClient struct {
	*kClient
}

func (p *kContractServiceClient) CreateContract(ctx context.Context, req *core.CreateContractReq, callOptions ...callopt.Option) (r *core.CreateContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateContract(ctx, req)
}

func (p *kContractServiceClient) QueryContract(ctx context.Context, req *core.QueryContractReq, callOptions ...callopt.Option) (r *core.QueryContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryContract(ctx, req)
}

func (p *kContractServiceClient) WithdrawContract(ctx context.Context, req *core.WithdrawContractReq, callOptions ...callopt.Option) (r *core.WithdrawContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.WithdrawContract(ctx, req)
}

func (p *kContractServiceClient) ApplyStamp(ctx context.Context, req *core.ApplyStampReq, callOptions ...callopt.Option) (r *core.ApplyStampResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ApplyStamp(ctx, req)
}

func (p *kContractServiceClient) AsyncApplyStamp(ctx context.Context, req *core.AsyncApplyStampReq, callOptions ...callopt.Option) (r *core.AsyncApplyStampResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AsyncApplyStamp(ctx, req)
}

func (p *kContractServiceClient) DownloadTmpl(ctx context.Context, req *core.DownloadTmplReq, callOptions ...callopt.Option) (r *core.DownloadTmplResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DownloadTmpl(ctx, req)
}

func (p *kContractServiceClient) QueryTmpl(ctx context.Context, req *core.QueryTmplReq, callOptions ...callopt.Option) (r *core.QueryTmplResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryTmpl(ctx, req)
}

func (p *kContractServiceClient) PreviewContract(ctx context.Context, req *core.PreviewContractReq, callOptions ...callopt.Option) (r *core.PreviewContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PreviewContract(ctx, req)
}

func (p *kContractServiceClient) DownloadContract(ctx context.Context, req *core.DownloadContractReq, callOptions ...callopt.Option) (r *core.DownloadContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DownloadContract(ctx, req)
}

func (p *kContractServiceClient) CreateContractV2(ctx context.Context, req *core.CreateContractV2Req, callOptions ...callopt.Option) (r *core.CreateContractV2Resp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateContractV2(ctx, req)
}

func (p *kContractServiceClient) CreateContractV2WithApplySign(ctx context.Context, req *core.CreateContractV2Req, callOptions ...callopt.Option) (r *core.CreateContractV2Resp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateContractV2WithApplySign(ctx, req)
}

func (p *kContractServiceClient) ApplySignNoCertLink(ctx context.Context, req *core.ApplySignNoCertLinkReq, callOptions ...callopt.Option) (r *core.ApplySignNoCertLinkResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ApplySignNoCertLink(ctx, req)
}

func (p *kContractServiceClient) UnionCreateContract(ctx context.Context, req *core.UnionCreateContractReq, callOptions ...callopt.Option) (r *core.UnionCreateContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionCreateContract(ctx, req)
}

func (p *kContractServiceClient) ApplyNoCertLink(ctx context.Context, req *core.ApplyNoCertLinkReq, callOptions ...callopt.Option) (r *core.ApplyNoCertLinkResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ApplyNoCertLink(ctx, req)
}

func (p *kContractServiceClient) BindCertResult_(ctx context.Context, req *core.BindCertResultReq, callOptions ...callopt.Option) (r *core.BindCertResultResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.BindCertResult_(ctx, req)
}

func (p *kContractServiceClient) QueryContractDetail(ctx context.Context, req *core.QueryContractDetailReq, callOptions ...callopt.Option) (r *core.QueryContractDetailResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryContractDetail(ctx, req)
}

func (p *kContractServiceClient) GetViewAndDownloadURL(ctx context.Context, req *core.GetViewAndDownloadURLReq, callOptions ...callopt.Option) (r *core.GetViewAndDownloadURLResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetViewAndDownloadURL(ctx, req)
}

func (p *kContractServiceClient) ViewAndDownloadByToken(ctx context.Context, req *core.ViewAndDownloadByTokenReq, callOptions ...callopt.Option) (r *core.ViewAndDownloadByTokenResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ViewAndDownloadByToken(ctx, req)
}

func (p *kContractServiceClient) CancelContract(ctx context.Context, req *core.CancelContractReq, callOptions ...callopt.Option) (r *core.CancelContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CancelContract(ctx, req)
}

func (p *kContractServiceClient) QueryContractTmplList(ctx context.Context, req *core.QueryContractTmplListReq, callOptions ...callopt.Option) (r *core.QueryContractTmplListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryContractTmplList(ctx, req)
}

func (p *kContractServiceClient) GetStampLink(ctx context.Context, req *core.GetStampLinkReq, callOptions ...callopt.Option) (r *core.GetStampLinkResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetStampLink(ctx, req)
}

func (p *kContractServiceClient) FetchStampLink(ctx context.Context, req *core.FetchStampLinkReq, callOptions ...callopt.Option) (r *core.FetchStampLinkResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FetchStampLink(ctx, req)
}

func (p *kContractServiceClient) SyncSignParty(ctx context.Context, req *core.SyncSignPartyReq, callOptions ...callopt.Option) (r *core.SyncSignPartyResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SyncSignParty(ctx, req)
}

func (p *kContractServiceClient) CertSignParty(ctx context.Context, req *core.CertSignPartyReq, callOptions ...callopt.Option) (r *core.CertSignPartyResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CertSignParty(ctx, req)
}

func (p *kContractServiceClient) CertSignPartyQuery(ctx context.Context, req *core.CertSignPartyQueryReq, callOptions ...callopt.Option) (r *core.CertSignPartyQueryResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CertSignPartyQuery(ctx, req)
}

func (p *kContractServiceClient) DeleteSignParty(ctx context.Context, req *core.DeleteSignPartyReq, callOptions ...callopt.Option) (r *core.DeleteSignPartyResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DeleteSignParty(ctx, req)
}

func (p *kContractServiceClient) PreCheckSignParty(ctx context.Context, req *core.PreCheckSignPartyReq, callOptions ...callopt.Option) (r *core.PreCheckSignPartyResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PreCheckSignParty(ctx, req)
}

func (p *kContractServiceClient) StampCallback(ctx context.Context, req *core.ESignCallBackReq, callOptions ...callopt.Option) (r *core.CallBackRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.StampCallback(ctx, req)
}

func (p *kContractServiceClient) CertSignPartyCallBack(ctx context.Context, req *core.CertSignPartyCallBackReq, callOptions ...callopt.Option) (r *core.CertSignPartyCallBackResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CertSignPartyCallBack(ctx, req)
}

func (p *kContractServiceClient) InnerSyncData(ctx context.Context, req *core.InnerSyncDataReq, callOptions ...callopt.Option) (r *core.InnerSyncDataResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.InnerSyncData(ctx, req)
}

func (p *kContractServiceClient) InnerSyncContStatus(ctx context.Context, req *core.InnerSyncContStatusReq, callOptions ...callopt.Option) (r *core.InnerSyncContStatusResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.InnerSyncContStatus(ctx, req)
}

func (p *kContractServiceClient) OACallback(ctx context.Context, req *core.OACallbackReq, callOptions ...callopt.Option) (r *core.OACallbackResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.OACallback(ctx, req)
}

func (p *kContractServiceClient) FeishuContCallback(ctx context.Context, req *core.FeishuContCallbackReq, callOptions ...callopt.Option) (r *core.FeishuContCallbackResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FeishuContCallback(ctx, req)
}

func (p *kContractServiceClient) SendSmsByMobile(ctx context.Context, req *core.SendSmsByMobileReq, callOptions ...callopt.Option) (r *core.SendSmsByMobileResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SendSmsByMobile(ctx, req)
}

func (p *kContractServiceClient) SyncInfraContToFeishuCont(ctx context.Context, req *core.SyncInfraContToFeishuContReq, callOptions ...callopt.Option) (r *core.SyncInfraContToFeishuContResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SyncInfraContToFeishuCont(ctx, req)
}

func (p *kContractServiceClient) PreValidateContract(ctx context.Context, req *core.PreValidateReq, callOptions ...callopt.Option) (r *core.PreValidateResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PreValidateContract(ctx, req)
}

func (p *kContractServiceClient) QueryContFormData(ctx context.Context, req *core.ContFormDataReq, callOptions ...callopt.Option) (r *core.ContFormDataResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryContFormData(ctx, req)
}

func (p *kContractServiceClient) QueryFeishuCont(ctx context.Context, req *core.QueryFeishuContReq, callOptions ...callopt.Option) (r *core.QueryFeishuContResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFeishuCont(ctx, req)
}

func (p *kContractServiceClient) DownloadFeishuFile(ctx context.Context, req *core.DownloadFeishuFileReq, callOptions ...callopt.Option) (r *core.DownloadFeishuFileResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DownloadFeishuFile(ctx, req)
}

func (p *kContractServiceClient) QueryContractStructInfo(ctx context.Context, req *core.QueryContractStructInfoReq, callOptions ...callopt.Option) (r *core.QueryContractStructInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryContractStructInfo(ctx, req)
}

func (p *kContractServiceClient) ParseContractStructFields(ctx context.Context, req *core.ParseContractStructFieldsReq, callOptions ...callopt.Option) (r *core.ParseContractStructFieldsResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ParseContractStructFields(ctx, req)
}

func (p *kContractServiceClient) AddMineCompanyParts(ctx context.Context, req *core.AddMineCompanyPartsReq, callOptions ...callopt.Option) (r *core.AddMineCompanyPartsResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AddMineCompanyParts(ctx, req)
}

func (p *kContractServiceClient) QueryFeishuUserInfoList(ctx context.Context, req *core.QueryFeishuUserInfoListReq, callOptions ...callopt.Option) (r *core.QueryFeishuUserInfoListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFeishuUserInfoList(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kContractServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
