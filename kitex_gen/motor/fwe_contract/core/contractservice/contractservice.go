// Code generated by Kitex v1.20.3. DO NOT EDIT.

package contractservice

import (
	client "code.byted.org/kite/kitex/client"
	core "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_contract/core"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"CreateContract": kitex.NewMethodInfo(
		createContractHandler,
		newContractServiceCreateContractArgs,
		newContractServiceCreateContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryContract": kitex.NewMethodInfo(
		queryContractHandler,
		newContractServiceQueryContractArgs,
		newContractServiceQueryContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"WithdrawContract": kitex.NewMethodInfo(
		withdrawContractHandler,
		newContractServiceWithdrawContractArgs,
		newContractServiceWithdrawContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ApplyStamp": kitex.NewMethodInfo(
		applyStampHandler,
		newContractServiceApplyStampArgs,
		newContractServiceApplyStampResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AsyncApplyStamp": kitex.NewMethodInfo(
		asyncApplyStampHandler,
		newContractServiceAsyncApplyStampArgs,
		newContractServiceAsyncApplyStampResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DownloadTmpl": kitex.NewMethodInfo(
		downloadTmplHandler,
		newContractServiceDownloadTmplArgs,
		newContractServiceDownloadTmplResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryTmpl": kitex.NewMethodInfo(
		queryTmplHandler,
		newContractServiceQueryTmplArgs,
		newContractServiceQueryTmplResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PreviewContract": kitex.NewMethodInfo(
		previewContractHandler,
		newContractServicePreviewContractArgs,
		newContractServicePreviewContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DownloadContract": kitex.NewMethodInfo(
		downloadContractHandler,
		newContractServiceDownloadContractArgs,
		newContractServiceDownloadContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateContractV2": kitex.NewMethodInfo(
		createContractV2Handler,
		newContractServiceCreateContractV2Args,
		newContractServiceCreateContractV2Result,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateContractV2WithApplySign": kitex.NewMethodInfo(
		createContractV2WithApplySignHandler,
		newContractServiceCreateContractV2WithApplySignArgs,
		newContractServiceCreateContractV2WithApplySignResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ApplySignNoCertLink": kitex.NewMethodInfo(
		applySignNoCertLinkHandler,
		newContractServiceApplySignNoCertLinkArgs,
		newContractServiceApplySignNoCertLinkResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionCreateContract": kitex.NewMethodInfo(
		unionCreateContractHandler,
		newContractServiceUnionCreateContractArgs,
		newContractServiceUnionCreateContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ApplyNoCertLink": kitex.NewMethodInfo(
		applyNoCertLinkHandler,
		newContractServiceApplyNoCertLinkArgs,
		newContractServiceApplyNoCertLinkResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"BindCertResult": kitex.NewMethodInfo(
		bindCertResult_Handler,
		newContractServiceBindCertResultArgs,
		newContractServiceBindCertResultResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryContractDetail": kitex.NewMethodInfo(
		queryContractDetailHandler,
		newContractServiceQueryContractDetailArgs,
		newContractServiceQueryContractDetailResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetViewAndDownloadUrl": kitex.NewMethodInfo(
		getViewAndDownloadURLHandler,
		newContractServiceGetViewAndDownloadURLArgs,
		newContractServiceGetViewAndDownloadURLResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ViewAndDownloadByToken": kitex.NewMethodInfo(
		viewAndDownloadByTokenHandler,
		newContractServiceViewAndDownloadByTokenArgs,
		newContractServiceViewAndDownloadByTokenResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CancelContract": kitex.NewMethodInfo(
		cancelContractHandler,
		newContractServiceCancelContractArgs,
		newContractServiceCancelContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryContractTmplList": kitex.NewMethodInfo(
		queryContractTmplListHandler,
		newContractServiceQueryContractTmplListArgs,
		newContractServiceQueryContractTmplListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetStampLink": kitex.NewMethodInfo(
		getStampLinkHandler,
		newContractServiceGetStampLinkArgs,
		newContractServiceGetStampLinkResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FetchStampLink": kitex.NewMethodInfo(
		fetchStampLinkHandler,
		newContractServiceFetchStampLinkArgs,
		newContractServiceFetchStampLinkResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SyncSignParty": kitex.NewMethodInfo(
		syncSignPartyHandler,
		newContractServiceSyncSignPartyArgs,
		newContractServiceSyncSignPartyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CertSignParty": kitex.NewMethodInfo(
		certSignPartyHandler,
		newContractServiceCertSignPartyArgs,
		newContractServiceCertSignPartyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CertSignPartyQuery": kitex.NewMethodInfo(
		certSignPartyQueryHandler,
		newContractServiceCertSignPartyQueryArgs,
		newContractServiceCertSignPartyQueryResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DeleteSignParty": kitex.NewMethodInfo(
		deleteSignPartyHandler,
		newContractServiceDeleteSignPartyArgs,
		newContractServiceDeleteSignPartyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PreCheckSignParty": kitex.NewMethodInfo(
		preCheckSignPartyHandler,
		newContractServicePreCheckSignPartyArgs,
		newContractServicePreCheckSignPartyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"StampCallback": kitex.NewMethodInfo(
		stampCallbackHandler,
		newContractServiceStampCallbackArgs,
		newContractServiceStampCallbackResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CertSignPartyCallBack": kitex.NewMethodInfo(
		certSignPartyCallBackHandler,
		newContractServiceCertSignPartyCallBackArgs,
		newContractServiceCertSignPartyCallBackResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"InnerSyncData": kitex.NewMethodInfo(
		innerSyncDataHandler,
		newContractServiceInnerSyncDataArgs,
		newContractServiceInnerSyncDataResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"InnerSyncContStatus": kitex.NewMethodInfo(
		innerSyncContStatusHandler,
		newContractServiceInnerSyncContStatusArgs,
		newContractServiceInnerSyncContStatusResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"OACallback": kitex.NewMethodInfo(
		oACallbackHandler,
		newContractServiceOACallbackArgs,
		newContractServiceOACallbackResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FeishuContCallback": kitex.NewMethodInfo(
		feishuContCallbackHandler,
		newContractServiceFeishuContCallbackArgs,
		newContractServiceFeishuContCallbackResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SendSmsByMobile": kitex.NewMethodInfo(
		sendSmsByMobileHandler,
		newContractServiceSendSmsByMobileArgs,
		newContractServiceSendSmsByMobileResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SyncInfraContToFeishuCont": kitex.NewMethodInfo(
		syncInfraContToFeishuContHandler,
		newContractServiceSyncInfraContToFeishuContArgs,
		newContractServiceSyncInfraContToFeishuContResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PreValidateContract": kitex.NewMethodInfo(
		preValidateContractHandler,
		newContractServicePreValidateContractArgs,
		newContractServicePreValidateContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryContFormData": kitex.NewMethodInfo(
		queryContFormDataHandler,
		newContractServiceQueryContFormDataArgs,
		newContractServiceQueryContFormDataResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryFeishuCont": kitex.NewMethodInfo(
		queryFeishuContHandler,
		newContractServiceQueryFeishuContArgs,
		newContractServiceQueryFeishuContResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DownloadFeishuFile": kitex.NewMethodInfo(
		downloadFeishuFileHandler,
		newContractServiceDownloadFeishuFileArgs,
		newContractServiceDownloadFeishuFileResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryContractStructInfo": kitex.NewMethodInfo(
		queryContractStructInfoHandler,
		newContractServiceQueryContractStructInfoArgs,
		newContractServiceQueryContractStructInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ParseContractStructFields": kitex.NewMethodInfo(
		parseContractStructFieldsHandler,
		newContractServiceParseContractStructFieldsArgs,
		newContractServiceParseContractStructFieldsResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AddMineCompanyParts": kitex.NewMethodInfo(
		addMineCompanyPartsHandler,
		newContractServiceAddMineCompanyPartsArgs,
		newContractServiceAddMineCompanyPartsResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryFeishuUserInfoList": kitex.NewMethodInfo(
		queryFeishuUserInfoListHandler,
		newContractServiceQueryFeishuUserInfoListArgs,
		newContractServiceQueryFeishuUserInfoListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	contractServiceServiceInfo                = NewServiceInfo()
	contractServiceServiceInfoForClient       = NewServiceInfoForClient()
	contractServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return contractServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return contractServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return contractServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "ContractService"
	handlerType := (*core.ContractService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "core",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func createContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceCreateContractArgs)
	realResult := result.(*core.ContractServiceCreateContractResult)
	success, err := handler.(core.ContractService).CreateContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceCreateContractArgs() interface{} {
	return core.NewContractServiceCreateContractArgs()
}

func newContractServiceCreateContractResult() interface{} {
	return core.NewContractServiceCreateContractResult()
}

func queryContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceQueryContractArgs)
	realResult := result.(*core.ContractServiceQueryContractResult)
	success, err := handler.(core.ContractService).QueryContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceQueryContractArgs() interface{} {
	return core.NewContractServiceQueryContractArgs()
}

func newContractServiceQueryContractResult() interface{} {
	return core.NewContractServiceQueryContractResult()
}

func withdrawContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceWithdrawContractArgs)
	realResult := result.(*core.ContractServiceWithdrawContractResult)
	success, err := handler.(core.ContractService).WithdrawContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceWithdrawContractArgs() interface{} {
	return core.NewContractServiceWithdrawContractArgs()
}

func newContractServiceWithdrawContractResult() interface{} {
	return core.NewContractServiceWithdrawContractResult()
}

func applyStampHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceApplyStampArgs)
	realResult := result.(*core.ContractServiceApplyStampResult)
	success, err := handler.(core.ContractService).ApplyStamp(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceApplyStampArgs() interface{} {
	return core.NewContractServiceApplyStampArgs()
}

func newContractServiceApplyStampResult() interface{} {
	return core.NewContractServiceApplyStampResult()
}

func asyncApplyStampHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceAsyncApplyStampArgs)
	realResult := result.(*core.ContractServiceAsyncApplyStampResult)
	success, err := handler.(core.ContractService).AsyncApplyStamp(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceAsyncApplyStampArgs() interface{} {
	return core.NewContractServiceAsyncApplyStampArgs()
}

func newContractServiceAsyncApplyStampResult() interface{} {
	return core.NewContractServiceAsyncApplyStampResult()
}

func downloadTmplHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceDownloadTmplArgs)
	realResult := result.(*core.ContractServiceDownloadTmplResult)
	success, err := handler.(core.ContractService).DownloadTmpl(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceDownloadTmplArgs() interface{} {
	return core.NewContractServiceDownloadTmplArgs()
}

func newContractServiceDownloadTmplResult() interface{} {
	return core.NewContractServiceDownloadTmplResult()
}

func queryTmplHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceQueryTmplArgs)
	realResult := result.(*core.ContractServiceQueryTmplResult)
	success, err := handler.(core.ContractService).QueryTmpl(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceQueryTmplArgs() interface{} {
	return core.NewContractServiceQueryTmplArgs()
}

func newContractServiceQueryTmplResult() interface{} {
	return core.NewContractServiceQueryTmplResult()
}

func previewContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServicePreviewContractArgs)
	realResult := result.(*core.ContractServicePreviewContractResult)
	success, err := handler.(core.ContractService).PreviewContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServicePreviewContractArgs() interface{} {
	return core.NewContractServicePreviewContractArgs()
}

func newContractServicePreviewContractResult() interface{} {
	return core.NewContractServicePreviewContractResult()
}

func downloadContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceDownloadContractArgs)
	realResult := result.(*core.ContractServiceDownloadContractResult)
	success, err := handler.(core.ContractService).DownloadContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceDownloadContractArgs() interface{} {
	return core.NewContractServiceDownloadContractArgs()
}

func newContractServiceDownloadContractResult() interface{} {
	return core.NewContractServiceDownloadContractResult()
}

func createContractV2Handler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceCreateContractV2Args)
	realResult := result.(*core.ContractServiceCreateContractV2Result)
	success, err := handler.(core.ContractService).CreateContractV2(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceCreateContractV2Args() interface{} {
	return core.NewContractServiceCreateContractV2Args()
}

func newContractServiceCreateContractV2Result() interface{} {
	return core.NewContractServiceCreateContractV2Result()
}

func createContractV2WithApplySignHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceCreateContractV2WithApplySignArgs)
	realResult := result.(*core.ContractServiceCreateContractV2WithApplySignResult)
	success, err := handler.(core.ContractService).CreateContractV2WithApplySign(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceCreateContractV2WithApplySignArgs() interface{} {
	return core.NewContractServiceCreateContractV2WithApplySignArgs()
}

func newContractServiceCreateContractV2WithApplySignResult() interface{} {
	return core.NewContractServiceCreateContractV2WithApplySignResult()
}

func applySignNoCertLinkHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceApplySignNoCertLinkArgs)
	realResult := result.(*core.ContractServiceApplySignNoCertLinkResult)
	success, err := handler.(core.ContractService).ApplySignNoCertLink(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceApplySignNoCertLinkArgs() interface{} {
	return core.NewContractServiceApplySignNoCertLinkArgs()
}

func newContractServiceApplySignNoCertLinkResult() interface{} {
	return core.NewContractServiceApplySignNoCertLinkResult()
}

func unionCreateContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceUnionCreateContractArgs)
	realResult := result.(*core.ContractServiceUnionCreateContractResult)
	success, err := handler.(core.ContractService).UnionCreateContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceUnionCreateContractArgs() interface{} {
	return core.NewContractServiceUnionCreateContractArgs()
}

func newContractServiceUnionCreateContractResult() interface{} {
	return core.NewContractServiceUnionCreateContractResult()
}

func applyNoCertLinkHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceApplyNoCertLinkArgs)
	realResult := result.(*core.ContractServiceApplyNoCertLinkResult)
	success, err := handler.(core.ContractService).ApplyNoCertLink(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceApplyNoCertLinkArgs() interface{} {
	return core.NewContractServiceApplyNoCertLinkArgs()
}

func newContractServiceApplyNoCertLinkResult() interface{} {
	return core.NewContractServiceApplyNoCertLinkResult()
}

func bindCertResult_Handler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceBindCertResultArgs)
	realResult := result.(*core.ContractServiceBindCertResultResult)
	success, err := handler.(core.ContractService).BindCertResult_(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceBindCertResultArgs() interface{} {
	return core.NewContractServiceBindCertResultArgs()
}

func newContractServiceBindCertResultResult() interface{} {
	return core.NewContractServiceBindCertResultResult()
}

func queryContractDetailHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceQueryContractDetailArgs)
	realResult := result.(*core.ContractServiceQueryContractDetailResult)
	success, err := handler.(core.ContractService).QueryContractDetail(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceQueryContractDetailArgs() interface{} {
	return core.NewContractServiceQueryContractDetailArgs()
}

func newContractServiceQueryContractDetailResult() interface{} {
	return core.NewContractServiceQueryContractDetailResult()
}

func getViewAndDownloadURLHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceGetViewAndDownloadURLArgs)
	realResult := result.(*core.ContractServiceGetViewAndDownloadURLResult)
	success, err := handler.(core.ContractService).GetViewAndDownloadURL(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceGetViewAndDownloadURLArgs() interface{} {
	return core.NewContractServiceGetViewAndDownloadURLArgs()
}

func newContractServiceGetViewAndDownloadURLResult() interface{} {
	return core.NewContractServiceGetViewAndDownloadURLResult()
}

func viewAndDownloadByTokenHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceViewAndDownloadByTokenArgs)
	realResult := result.(*core.ContractServiceViewAndDownloadByTokenResult)
	success, err := handler.(core.ContractService).ViewAndDownloadByToken(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceViewAndDownloadByTokenArgs() interface{} {
	return core.NewContractServiceViewAndDownloadByTokenArgs()
}

func newContractServiceViewAndDownloadByTokenResult() interface{} {
	return core.NewContractServiceViewAndDownloadByTokenResult()
}

func cancelContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceCancelContractArgs)
	realResult := result.(*core.ContractServiceCancelContractResult)
	success, err := handler.(core.ContractService).CancelContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceCancelContractArgs() interface{} {
	return core.NewContractServiceCancelContractArgs()
}

func newContractServiceCancelContractResult() interface{} {
	return core.NewContractServiceCancelContractResult()
}

func queryContractTmplListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceQueryContractTmplListArgs)
	realResult := result.(*core.ContractServiceQueryContractTmplListResult)
	success, err := handler.(core.ContractService).QueryContractTmplList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceQueryContractTmplListArgs() interface{} {
	return core.NewContractServiceQueryContractTmplListArgs()
}

func newContractServiceQueryContractTmplListResult() interface{} {
	return core.NewContractServiceQueryContractTmplListResult()
}

func getStampLinkHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceGetStampLinkArgs)
	realResult := result.(*core.ContractServiceGetStampLinkResult)
	success, err := handler.(core.ContractService).GetStampLink(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceGetStampLinkArgs() interface{} {
	return core.NewContractServiceGetStampLinkArgs()
}

func newContractServiceGetStampLinkResult() interface{} {
	return core.NewContractServiceGetStampLinkResult()
}

func fetchStampLinkHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceFetchStampLinkArgs)
	realResult := result.(*core.ContractServiceFetchStampLinkResult)
	success, err := handler.(core.ContractService).FetchStampLink(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceFetchStampLinkArgs() interface{} {
	return core.NewContractServiceFetchStampLinkArgs()
}

func newContractServiceFetchStampLinkResult() interface{} {
	return core.NewContractServiceFetchStampLinkResult()
}

func syncSignPartyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceSyncSignPartyArgs)
	realResult := result.(*core.ContractServiceSyncSignPartyResult)
	success, err := handler.(core.ContractService).SyncSignParty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceSyncSignPartyArgs() interface{} {
	return core.NewContractServiceSyncSignPartyArgs()
}

func newContractServiceSyncSignPartyResult() interface{} {
	return core.NewContractServiceSyncSignPartyResult()
}

func certSignPartyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceCertSignPartyArgs)
	realResult := result.(*core.ContractServiceCertSignPartyResult)
	success, err := handler.(core.ContractService).CertSignParty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceCertSignPartyArgs() interface{} {
	return core.NewContractServiceCertSignPartyArgs()
}

func newContractServiceCertSignPartyResult() interface{} {
	return core.NewContractServiceCertSignPartyResult()
}

func certSignPartyQueryHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceCertSignPartyQueryArgs)
	realResult := result.(*core.ContractServiceCertSignPartyQueryResult)
	success, err := handler.(core.ContractService).CertSignPartyQuery(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceCertSignPartyQueryArgs() interface{} {
	return core.NewContractServiceCertSignPartyQueryArgs()
}

func newContractServiceCertSignPartyQueryResult() interface{} {
	return core.NewContractServiceCertSignPartyQueryResult()
}

func deleteSignPartyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceDeleteSignPartyArgs)
	realResult := result.(*core.ContractServiceDeleteSignPartyResult)
	success, err := handler.(core.ContractService).DeleteSignParty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceDeleteSignPartyArgs() interface{} {
	return core.NewContractServiceDeleteSignPartyArgs()
}

func newContractServiceDeleteSignPartyResult() interface{} {
	return core.NewContractServiceDeleteSignPartyResult()
}

func preCheckSignPartyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServicePreCheckSignPartyArgs)
	realResult := result.(*core.ContractServicePreCheckSignPartyResult)
	success, err := handler.(core.ContractService).PreCheckSignParty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServicePreCheckSignPartyArgs() interface{} {
	return core.NewContractServicePreCheckSignPartyArgs()
}

func newContractServicePreCheckSignPartyResult() interface{} {
	return core.NewContractServicePreCheckSignPartyResult()
}

func stampCallbackHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceStampCallbackArgs)
	realResult := result.(*core.ContractServiceStampCallbackResult)
	success, err := handler.(core.ContractService).StampCallback(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceStampCallbackArgs() interface{} {
	return core.NewContractServiceStampCallbackArgs()
}

func newContractServiceStampCallbackResult() interface{} {
	return core.NewContractServiceStampCallbackResult()
}

func certSignPartyCallBackHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceCertSignPartyCallBackArgs)
	realResult := result.(*core.ContractServiceCertSignPartyCallBackResult)
	success, err := handler.(core.ContractService).CertSignPartyCallBack(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceCertSignPartyCallBackArgs() interface{} {
	return core.NewContractServiceCertSignPartyCallBackArgs()
}

func newContractServiceCertSignPartyCallBackResult() interface{} {
	return core.NewContractServiceCertSignPartyCallBackResult()
}

func innerSyncDataHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceInnerSyncDataArgs)
	realResult := result.(*core.ContractServiceInnerSyncDataResult)
	success, err := handler.(core.ContractService).InnerSyncData(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceInnerSyncDataArgs() interface{} {
	return core.NewContractServiceInnerSyncDataArgs()
}

func newContractServiceInnerSyncDataResult() interface{} {
	return core.NewContractServiceInnerSyncDataResult()
}

func innerSyncContStatusHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceInnerSyncContStatusArgs)
	realResult := result.(*core.ContractServiceInnerSyncContStatusResult)
	success, err := handler.(core.ContractService).InnerSyncContStatus(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceInnerSyncContStatusArgs() interface{} {
	return core.NewContractServiceInnerSyncContStatusArgs()
}

func newContractServiceInnerSyncContStatusResult() interface{} {
	return core.NewContractServiceInnerSyncContStatusResult()
}

func oACallbackHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceOACallbackArgs)
	realResult := result.(*core.ContractServiceOACallbackResult)
	success, err := handler.(core.ContractService).OACallback(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceOACallbackArgs() interface{} {
	return core.NewContractServiceOACallbackArgs()
}

func newContractServiceOACallbackResult() interface{} {
	return core.NewContractServiceOACallbackResult()
}

func feishuContCallbackHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceFeishuContCallbackArgs)
	realResult := result.(*core.ContractServiceFeishuContCallbackResult)
	success, err := handler.(core.ContractService).FeishuContCallback(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceFeishuContCallbackArgs() interface{} {
	return core.NewContractServiceFeishuContCallbackArgs()
}

func newContractServiceFeishuContCallbackResult() interface{} {
	return core.NewContractServiceFeishuContCallbackResult()
}

func sendSmsByMobileHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceSendSmsByMobileArgs)
	realResult := result.(*core.ContractServiceSendSmsByMobileResult)
	success, err := handler.(core.ContractService).SendSmsByMobile(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceSendSmsByMobileArgs() interface{} {
	return core.NewContractServiceSendSmsByMobileArgs()
}

func newContractServiceSendSmsByMobileResult() interface{} {
	return core.NewContractServiceSendSmsByMobileResult()
}

func syncInfraContToFeishuContHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceSyncInfraContToFeishuContArgs)
	realResult := result.(*core.ContractServiceSyncInfraContToFeishuContResult)
	success, err := handler.(core.ContractService).SyncInfraContToFeishuCont(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceSyncInfraContToFeishuContArgs() interface{} {
	return core.NewContractServiceSyncInfraContToFeishuContArgs()
}

func newContractServiceSyncInfraContToFeishuContResult() interface{} {
	return core.NewContractServiceSyncInfraContToFeishuContResult()
}

func preValidateContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServicePreValidateContractArgs)
	realResult := result.(*core.ContractServicePreValidateContractResult)
	success, err := handler.(core.ContractService).PreValidateContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServicePreValidateContractArgs() interface{} {
	return core.NewContractServicePreValidateContractArgs()
}

func newContractServicePreValidateContractResult() interface{} {
	return core.NewContractServicePreValidateContractResult()
}

func queryContFormDataHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceQueryContFormDataArgs)
	realResult := result.(*core.ContractServiceQueryContFormDataResult)
	success, err := handler.(core.ContractService).QueryContFormData(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceQueryContFormDataArgs() interface{} {
	return core.NewContractServiceQueryContFormDataArgs()
}

func newContractServiceQueryContFormDataResult() interface{} {
	return core.NewContractServiceQueryContFormDataResult()
}

func queryFeishuContHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceQueryFeishuContArgs)
	realResult := result.(*core.ContractServiceQueryFeishuContResult)
	success, err := handler.(core.ContractService).QueryFeishuCont(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceQueryFeishuContArgs() interface{} {
	return core.NewContractServiceQueryFeishuContArgs()
}

func newContractServiceQueryFeishuContResult() interface{} {
	return core.NewContractServiceQueryFeishuContResult()
}

func downloadFeishuFileHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceDownloadFeishuFileArgs)
	realResult := result.(*core.ContractServiceDownloadFeishuFileResult)
	success, err := handler.(core.ContractService).DownloadFeishuFile(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceDownloadFeishuFileArgs() interface{} {
	return core.NewContractServiceDownloadFeishuFileArgs()
}

func newContractServiceDownloadFeishuFileResult() interface{} {
	return core.NewContractServiceDownloadFeishuFileResult()
}

func queryContractStructInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceQueryContractStructInfoArgs)
	realResult := result.(*core.ContractServiceQueryContractStructInfoResult)
	success, err := handler.(core.ContractService).QueryContractStructInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceQueryContractStructInfoArgs() interface{} {
	return core.NewContractServiceQueryContractStructInfoArgs()
}

func newContractServiceQueryContractStructInfoResult() interface{} {
	return core.NewContractServiceQueryContractStructInfoResult()
}

func parseContractStructFieldsHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceParseContractStructFieldsArgs)
	realResult := result.(*core.ContractServiceParseContractStructFieldsResult)
	success, err := handler.(core.ContractService).ParseContractStructFields(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceParseContractStructFieldsArgs() interface{} {
	return core.NewContractServiceParseContractStructFieldsArgs()
}

func newContractServiceParseContractStructFieldsResult() interface{} {
	return core.NewContractServiceParseContractStructFieldsResult()
}

func addMineCompanyPartsHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceAddMineCompanyPartsArgs)
	realResult := result.(*core.ContractServiceAddMineCompanyPartsResult)
	success, err := handler.(core.ContractService).AddMineCompanyParts(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceAddMineCompanyPartsArgs() interface{} {
	return core.NewContractServiceAddMineCompanyPartsArgs()
}

func newContractServiceAddMineCompanyPartsResult() interface{} {
	return core.NewContractServiceAddMineCompanyPartsResult()
}

func queryFeishuUserInfoListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.ContractServiceQueryFeishuUserInfoListArgs)
	realResult := result.(*core.ContractServiceQueryFeishuUserInfoListResult)
	success, err := handler.(core.ContractService).QueryFeishuUserInfoList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newContractServiceQueryFeishuUserInfoListArgs() interface{} {
	return core.NewContractServiceQueryFeishuUserInfoListArgs()
}

func newContractServiceQueryFeishuUserInfoListResult() interface{} {
	return core.NewContractServiceQueryFeishuUserInfoListResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) CreateContract(ctx context.Context, req *core.CreateContractReq) (r *core.CreateContractResp, err error) {
	var _args core.ContractServiceCreateContractArgs
	_args.Req = req
	var _result core.ContractServiceCreateContractResult
	if err = p.c.Call(ctx, "CreateContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryContract(ctx context.Context, req *core.QueryContractReq) (r *core.QueryContractResp, err error) {
	var _args core.ContractServiceQueryContractArgs
	_args.Req = req
	var _result core.ContractServiceQueryContractResult
	if err = p.c.Call(ctx, "QueryContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) WithdrawContract(ctx context.Context, req *core.WithdrawContractReq) (r *core.WithdrawContractResp, err error) {
	var _args core.ContractServiceWithdrawContractArgs
	_args.Req = req
	var _result core.ContractServiceWithdrawContractResult
	if err = p.c.Call(ctx, "WithdrawContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ApplyStamp(ctx context.Context, req *core.ApplyStampReq) (r *core.ApplyStampResp, err error) {
	var _args core.ContractServiceApplyStampArgs
	_args.Req = req
	var _result core.ContractServiceApplyStampResult
	if err = p.c.Call(ctx, "ApplyStamp", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AsyncApplyStamp(ctx context.Context, req *core.AsyncApplyStampReq) (r *core.AsyncApplyStampResp, err error) {
	var _args core.ContractServiceAsyncApplyStampArgs
	_args.Req = req
	var _result core.ContractServiceAsyncApplyStampResult
	if err = p.c.Call(ctx, "AsyncApplyStamp", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DownloadTmpl(ctx context.Context, req *core.DownloadTmplReq) (r *core.DownloadTmplResp, err error) {
	var _args core.ContractServiceDownloadTmplArgs
	_args.Req = req
	var _result core.ContractServiceDownloadTmplResult
	if err = p.c.Call(ctx, "DownloadTmpl", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryTmpl(ctx context.Context, req *core.QueryTmplReq) (r *core.QueryTmplResp, err error) {
	var _args core.ContractServiceQueryTmplArgs
	_args.Req = req
	var _result core.ContractServiceQueryTmplResult
	if err = p.c.Call(ctx, "QueryTmpl", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PreviewContract(ctx context.Context, req *core.PreviewContractReq) (r *core.PreviewContractResp, err error) {
	var _args core.ContractServicePreviewContractArgs
	_args.Req = req
	var _result core.ContractServicePreviewContractResult
	if err = p.c.Call(ctx, "PreviewContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DownloadContract(ctx context.Context, req *core.DownloadContractReq) (r *core.DownloadContractResp, err error) {
	var _args core.ContractServiceDownloadContractArgs
	_args.Req = req
	var _result core.ContractServiceDownloadContractResult
	if err = p.c.Call(ctx, "DownloadContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateContractV2(ctx context.Context, req *core.CreateContractV2Req) (r *core.CreateContractV2Resp, err error) {
	var _args core.ContractServiceCreateContractV2Args
	_args.Req = req
	var _result core.ContractServiceCreateContractV2Result
	if err = p.c.Call(ctx, "CreateContractV2", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateContractV2WithApplySign(ctx context.Context, req *core.CreateContractV2Req) (r *core.CreateContractV2Resp, err error) {
	var _args core.ContractServiceCreateContractV2WithApplySignArgs
	_args.Req = req
	var _result core.ContractServiceCreateContractV2WithApplySignResult
	if err = p.c.Call(ctx, "CreateContractV2WithApplySign", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ApplySignNoCertLink(ctx context.Context, req *core.ApplySignNoCertLinkReq) (r *core.ApplySignNoCertLinkResp, err error) {
	var _args core.ContractServiceApplySignNoCertLinkArgs
	_args.Req = req
	var _result core.ContractServiceApplySignNoCertLinkResult
	if err = p.c.Call(ctx, "ApplySignNoCertLink", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionCreateContract(ctx context.Context, req *core.UnionCreateContractReq) (r *core.UnionCreateContractResp, err error) {
	var _args core.ContractServiceUnionCreateContractArgs
	_args.Req = req
	var _result core.ContractServiceUnionCreateContractResult
	if err = p.c.Call(ctx, "UnionCreateContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ApplyNoCertLink(ctx context.Context, req *core.ApplyNoCertLinkReq) (r *core.ApplyNoCertLinkResp, err error) {
	var _args core.ContractServiceApplyNoCertLinkArgs
	_args.Req = req
	var _result core.ContractServiceApplyNoCertLinkResult
	if err = p.c.Call(ctx, "ApplyNoCertLink", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) BindCertResult_(ctx context.Context, req *core.BindCertResultReq) (r *core.BindCertResultResp, err error) {
	var _args core.ContractServiceBindCertResultArgs
	_args.Req = req
	var _result core.ContractServiceBindCertResultResult
	if err = p.c.Call(ctx, "BindCertResult", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryContractDetail(ctx context.Context, req *core.QueryContractDetailReq) (r *core.QueryContractDetailResp, err error) {
	var _args core.ContractServiceQueryContractDetailArgs
	_args.Req = req
	var _result core.ContractServiceQueryContractDetailResult
	if err = p.c.Call(ctx, "QueryContractDetail", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetViewAndDownloadURL(ctx context.Context, req *core.GetViewAndDownloadURLReq) (r *core.GetViewAndDownloadURLResp, err error) {
	var _args core.ContractServiceGetViewAndDownloadURLArgs
	_args.Req = req
	var _result core.ContractServiceGetViewAndDownloadURLResult
	if err = p.c.Call(ctx, "GetViewAndDownloadUrl", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ViewAndDownloadByToken(ctx context.Context, req *core.ViewAndDownloadByTokenReq) (r *core.ViewAndDownloadByTokenResp, err error) {
	var _args core.ContractServiceViewAndDownloadByTokenArgs
	_args.Req = req
	var _result core.ContractServiceViewAndDownloadByTokenResult
	if err = p.c.Call(ctx, "ViewAndDownloadByToken", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CancelContract(ctx context.Context, req *core.CancelContractReq) (r *core.CancelContractResp, err error) {
	var _args core.ContractServiceCancelContractArgs
	_args.Req = req
	var _result core.ContractServiceCancelContractResult
	if err = p.c.Call(ctx, "CancelContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryContractTmplList(ctx context.Context, req *core.QueryContractTmplListReq) (r *core.QueryContractTmplListResp, err error) {
	var _args core.ContractServiceQueryContractTmplListArgs
	_args.Req = req
	var _result core.ContractServiceQueryContractTmplListResult
	if err = p.c.Call(ctx, "QueryContractTmplList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetStampLink(ctx context.Context, req *core.GetStampLinkReq) (r *core.GetStampLinkResp, err error) {
	var _args core.ContractServiceGetStampLinkArgs
	_args.Req = req
	var _result core.ContractServiceGetStampLinkResult
	if err = p.c.Call(ctx, "GetStampLink", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FetchStampLink(ctx context.Context, req *core.FetchStampLinkReq) (r *core.FetchStampLinkResp, err error) {
	var _args core.ContractServiceFetchStampLinkArgs
	_args.Req = req
	var _result core.ContractServiceFetchStampLinkResult
	if err = p.c.Call(ctx, "FetchStampLink", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SyncSignParty(ctx context.Context, req *core.SyncSignPartyReq) (r *core.SyncSignPartyResp, err error) {
	var _args core.ContractServiceSyncSignPartyArgs
	_args.Req = req
	var _result core.ContractServiceSyncSignPartyResult
	if err = p.c.Call(ctx, "SyncSignParty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CertSignParty(ctx context.Context, req *core.CertSignPartyReq) (r *core.CertSignPartyResp, err error) {
	var _args core.ContractServiceCertSignPartyArgs
	_args.Req = req
	var _result core.ContractServiceCertSignPartyResult
	if err = p.c.Call(ctx, "CertSignParty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CertSignPartyQuery(ctx context.Context, req *core.CertSignPartyQueryReq) (r *core.CertSignPartyQueryResp, err error) {
	var _args core.ContractServiceCertSignPartyQueryArgs
	_args.Req = req
	var _result core.ContractServiceCertSignPartyQueryResult
	if err = p.c.Call(ctx, "CertSignPartyQuery", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DeleteSignParty(ctx context.Context, req *core.DeleteSignPartyReq) (r *core.DeleteSignPartyResp, err error) {
	var _args core.ContractServiceDeleteSignPartyArgs
	_args.Req = req
	var _result core.ContractServiceDeleteSignPartyResult
	if err = p.c.Call(ctx, "DeleteSignParty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PreCheckSignParty(ctx context.Context, req *core.PreCheckSignPartyReq) (r *core.PreCheckSignPartyResp, err error) {
	var _args core.ContractServicePreCheckSignPartyArgs
	_args.Req = req
	var _result core.ContractServicePreCheckSignPartyResult
	if err = p.c.Call(ctx, "PreCheckSignParty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) StampCallback(ctx context.Context, req *core.ESignCallBackReq) (r *core.CallBackRsp, err error) {
	var _args core.ContractServiceStampCallbackArgs
	_args.Req = req
	var _result core.ContractServiceStampCallbackResult
	if err = p.c.Call(ctx, "StampCallback", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CertSignPartyCallBack(ctx context.Context, req *core.CertSignPartyCallBackReq) (r *core.CertSignPartyCallBackResp, err error) {
	var _args core.ContractServiceCertSignPartyCallBackArgs
	_args.Req = req
	var _result core.ContractServiceCertSignPartyCallBackResult
	if err = p.c.Call(ctx, "CertSignPartyCallBack", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) InnerSyncData(ctx context.Context, req *core.InnerSyncDataReq) (r *core.InnerSyncDataResp, err error) {
	var _args core.ContractServiceInnerSyncDataArgs
	_args.Req = req
	var _result core.ContractServiceInnerSyncDataResult
	if err = p.c.Call(ctx, "InnerSyncData", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) InnerSyncContStatus(ctx context.Context, req *core.InnerSyncContStatusReq) (r *core.InnerSyncContStatusResp, err error) {
	var _args core.ContractServiceInnerSyncContStatusArgs
	_args.Req = req
	var _result core.ContractServiceInnerSyncContStatusResult
	if err = p.c.Call(ctx, "InnerSyncContStatus", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) OACallback(ctx context.Context, req *core.OACallbackReq) (r *core.OACallbackResp, err error) {
	var _args core.ContractServiceOACallbackArgs
	_args.Req = req
	var _result core.ContractServiceOACallbackResult
	if err = p.c.Call(ctx, "OACallback", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FeishuContCallback(ctx context.Context, req *core.FeishuContCallbackReq) (r *core.FeishuContCallbackResp, err error) {
	var _args core.ContractServiceFeishuContCallbackArgs
	_args.Req = req
	var _result core.ContractServiceFeishuContCallbackResult
	if err = p.c.Call(ctx, "FeishuContCallback", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SendSmsByMobile(ctx context.Context, req *core.SendSmsByMobileReq) (r *core.SendSmsByMobileResp, err error) {
	var _args core.ContractServiceSendSmsByMobileArgs
	_args.Req = req
	var _result core.ContractServiceSendSmsByMobileResult
	if err = p.c.Call(ctx, "SendSmsByMobile", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SyncInfraContToFeishuCont(ctx context.Context, req *core.SyncInfraContToFeishuContReq) (r *core.SyncInfraContToFeishuContResp, err error) {
	var _args core.ContractServiceSyncInfraContToFeishuContArgs
	_args.Req = req
	var _result core.ContractServiceSyncInfraContToFeishuContResult
	if err = p.c.Call(ctx, "SyncInfraContToFeishuCont", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PreValidateContract(ctx context.Context, req *core.PreValidateReq) (r *core.PreValidateResp, err error) {
	var _args core.ContractServicePreValidateContractArgs
	_args.Req = req
	var _result core.ContractServicePreValidateContractResult
	if err = p.c.Call(ctx, "PreValidateContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryContFormData(ctx context.Context, req *core.ContFormDataReq) (r *core.ContFormDataResp, err error) {
	var _args core.ContractServiceQueryContFormDataArgs
	_args.Req = req
	var _result core.ContractServiceQueryContFormDataResult
	if err = p.c.Call(ctx, "QueryContFormData", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryFeishuCont(ctx context.Context, req *core.QueryFeishuContReq) (r *core.QueryFeishuContResp, err error) {
	var _args core.ContractServiceQueryFeishuContArgs
	_args.Req = req
	var _result core.ContractServiceQueryFeishuContResult
	if err = p.c.Call(ctx, "QueryFeishuCont", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DownloadFeishuFile(ctx context.Context, req *core.DownloadFeishuFileReq) (r *core.DownloadFeishuFileResp, err error) {
	var _args core.ContractServiceDownloadFeishuFileArgs
	_args.Req = req
	var _result core.ContractServiceDownloadFeishuFileResult
	if err = p.c.Call(ctx, "DownloadFeishuFile", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryContractStructInfo(ctx context.Context, req *core.QueryContractStructInfoReq) (r *core.QueryContractStructInfoResp, err error) {
	var _args core.ContractServiceQueryContractStructInfoArgs
	_args.Req = req
	var _result core.ContractServiceQueryContractStructInfoResult
	if err = p.c.Call(ctx, "QueryContractStructInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ParseContractStructFields(ctx context.Context, req *core.ParseContractStructFieldsReq) (r *core.ParseContractStructFieldsResp, err error) {
	var _args core.ContractServiceParseContractStructFieldsArgs
	_args.Req = req
	var _result core.ContractServiceParseContractStructFieldsResult
	if err = p.c.Call(ctx, "ParseContractStructFields", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AddMineCompanyParts(ctx context.Context, req *core.AddMineCompanyPartsReq) (r *core.AddMineCompanyPartsResp, err error) {
	var _args core.ContractServiceAddMineCompanyPartsArgs
	_args.Req = req
	var _result core.ContractServiceAddMineCompanyPartsResult
	if err = p.c.Call(ctx, "AddMineCompanyParts", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryFeishuUserInfoList(ctx context.Context, req *core.QueryFeishuUserInfoListReq) (r *core.QueryFeishuUserInfoListResp, err error) {
	var _args core.ContractServiceQueryFeishuUserInfoListArgs
	_args.Req = req
	var _result core.ContractServiceQueryFeishuUserInfoListResult
	if err = p.c.Call(ctx, "QueryFeishuUserInfoList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
