// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package account_common

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe/fwe_base"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
)

type BoolInt int64

const (
	BoolInt_Reserved BoolInt = 0
	BoolInt_Yes      BoolInt = 1
	BoolInt_No       BoolInt = 2
)

func (p BoolInt) String() string {
	switch p {
	case BoolInt_Reserved:
		return "Reserved"
	case BoolInt_Yes:
		return "Yes"
	case BoolInt_No:
		return "No"
	}
	return "<UNSET>"
}

func BoolIntFromString(s string) (BoolInt, error) {
	switch s {
	case "Reserved":
		return BoolInt_Reserved, nil
	case "Yes":
		return BoolInt_Yes, nil
	case "No":
		return BoolInt_No, nil
	}
	return BoolInt(0), fmt.Errorf("not a valid BoolInt string")
}

func BoolIntPtr(v BoolInt) *BoolInt { return &v }
func (p *BoolInt) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = BoolInt(result.Int64)
	return
}

func (p *BoolInt) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type GeoPoint struct {
	Lat float64 `thrift:"lat,1" frugal:"1,default,double" json:"lat"`
	Lon float64 `thrift:"lon,2" frugal:"2,default,double" json:"lon"`
}

func NewGeoPoint() *GeoPoint {
	return &GeoPoint{}
}

func (p *GeoPoint) InitDefault() {
}

func (p *GeoPoint) GetLat() (v float64) {
	return p.Lat
}

func (p *GeoPoint) GetLon() (v float64) {
	return p.Lon
}
func (p *GeoPoint) SetLat(val float64) {
	p.Lat = val
}
func (p *GeoPoint) SetLon(val float64) {
	p.Lon = val
}

var fieldIDToName_GeoPoint = map[int16]string{
	1: "lat",
	2: "lon",
}

func (p *GeoPoint) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GeoPoint")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GeoPoint[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GeoPoint) ReadField1(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Lat = _field
	return nil
}
func (p *GeoPoint) ReadField2(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Lon = _field
	return nil
}

func (p *GeoPoint) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("GeoPoint")

	var fieldId int16
	if err = oprot.WriteStructBegin("GeoPoint"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GeoPoint) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("lat", thrift.DOUBLE, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Lat); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GeoPoint) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("lon", thrift.DOUBLE, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Lon); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GeoPoint) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GeoPoint(%+v)", *p)

}

type Operator struct {
	OperatorName string            `thrift:"operator_name,1" frugal:"1,default,string" json:"operator_name"`
	OperatorID   string            `thrift:"operator_id,2" frugal:"2,default,string" json:"operator_id"`
	OperatorType *fwe_base.UIDType `thrift:"operator_type,3,optional" frugal:"3,optional,UIDType" json:"operator_type,omitempty"`
}

func NewOperator() *Operator {
	return &Operator{}
}

func (p *Operator) InitDefault() {
}

func (p *Operator) GetOperatorName() (v string) {
	return p.OperatorName
}

func (p *Operator) GetOperatorID() (v string) {
	return p.OperatorID
}

var Operator_OperatorType_DEFAULT fwe_base.UIDType

func (p *Operator) GetOperatorType() (v fwe_base.UIDType) {
	if !p.IsSetOperatorType() {
		return Operator_OperatorType_DEFAULT
	}
	return *p.OperatorType
}
func (p *Operator) SetOperatorName(val string) {
	p.OperatorName = val
}
func (p *Operator) SetOperatorID(val string) {
	p.OperatorID = val
}
func (p *Operator) SetOperatorType(val *fwe_base.UIDType) {
	p.OperatorType = val
}

var fieldIDToName_Operator = map[int16]string{
	1: "operator_name",
	2: "operator_id",
	3: "operator_type",
}

func (p *Operator) IsSetOperatorType() bool {
	return p.OperatorType != nil
}

func (p *Operator) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("Operator")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Operator[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Operator) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OperatorName = _field
	return nil
}
func (p *Operator) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OperatorID = _field
	return nil
}
func (p *Operator) ReadField3(iprot thrift.TProtocol) error {

	var _field *fwe_base.UIDType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := fwe_base.UIDType(v)
		_field = &tmp
	}
	p.OperatorType = _field
	return nil
}

func (p *Operator) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("Operator")

	var fieldId int16
	if err = oprot.WriteStructBegin("Operator"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Operator) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator_name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OperatorName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Operator) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator_id", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OperatorID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Operator) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetOperatorType() {
		if err = oprot.WriteFieldBegin("operator_type", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OperatorType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *Operator) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Operator(%+v)", *p)

}

type TimeRange struct {
	StartTime int64 `thrift:"start_time,1" frugal:"1,default,i64" json:"start_time"`
	EndTime   int64 `thrift:"end_time,2" frugal:"2,default,i64" json:"end_time"`
}

func NewTimeRange() *TimeRange {
	return &TimeRange{}
}

func (p *TimeRange) InitDefault() {
}

func (p *TimeRange) GetStartTime() (v int64) {
	return p.StartTime
}

func (p *TimeRange) GetEndTime() (v int64) {
	return p.EndTime
}
func (p *TimeRange) SetStartTime(val int64) {
	p.StartTime = val
}
func (p *TimeRange) SetEndTime(val int64) {
	p.EndTime = val
}

var fieldIDToName_TimeRange = map[int16]string{
	1: "start_time",
	2: "end_time",
}

func (p *TimeRange) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TimeRange")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TimeRange[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TimeRange) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *TimeRange) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}

func (p *TimeRange) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("TimeRange")

	var fieldId int16
	if err = oprot.WriteStructBegin("TimeRange"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TimeRange) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("start_time", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TimeRange) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("end_time", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TimeRange) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TimeRange(%+v)", *p)

}

type Int64Range struct {
	Min int64 `thrift:"min,1" frugal:"1,default,i64" json:"min"`
	Max int64 `thrift:"max,2" frugal:"2,default,i64" json:"max"`
}

func NewInt64Range() *Int64Range {
	return &Int64Range{}
}

func (p *Int64Range) InitDefault() {
}

func (p *Int64Range) GetMin() (v int64) {
	return p.Min
}

func (p *Int64Range) GetMax() (v int64) {
	return p.Max
}
func (p *Int64Range) SetMin(val int64) {
	p.Min = val
}
func (p *Int64Range) SetMax(val int64) {
	p.Max = val
}

var fieldIDToName_Int64Range = map[int16]string{
	1: "min",
	2: "max",
}

func (p *Int64Range) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("Int64Range")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Int64Range[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Int64Range) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Min = _field
	return nil
}
func (p *Int64Range) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Max = _field
	return nil
}

func (p *Int64Range) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("Int64Range")

	var fieldId int16
	if err = oprot.WriteStructBegin("Int64Range"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Int64Range) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("min", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Min); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Int64Range) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("max", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Max); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Int64Range) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Int64Range(%+v)", *p)

}
