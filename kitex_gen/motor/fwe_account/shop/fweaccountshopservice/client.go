// Code generated by Kitex v1.20.3. DO NOT EDIT.

package fweaccountshopservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	shop "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	CreateShop(ctx context.Context, req *shop.CreateShopReq, callOptions ...callopt.Option) (r *shop.CreateShopResp, err error)
	UpdateShop(ctx context.Context, req *shop.UpdateShopReq, callOptions ...callopt.Option) (r *shop.UpdateShopResp, err error)
	UpdateShopStatus(ctx context.Context, req *shop.UpdateShopStatusReq, callOptions ...callopt.Option) (r *shop.UpdateShopStatusResp, err error)
	MGetShop(ctx context.Context, req *shop.MGetShopReq, callOptions ...callopt.Option) (r *shop.MGetShopResp, err error)
	ChangeOwner(ctx context.Context, req *shop.ChangeOwnerReq, callOptions ...callopt.Option) (r *shop.ChangeOwnerResp, err error)
	SettingChildren(ctx context.Context, req *shop.SettingChildrenReq, callOptions ...callopt.Option) (r *shop.SettingChildrenResp, err error)
	ListChildren(ctx context.Context, req *shop.ListChildrenReq, callOptions ...callopt.Option) (r *shop.ListChildrenResp, err error)
	SubmitShopSettle(ctx context.Context, req *shop.SubmitShopSettleReq, callOptions ...callopt.Option) (r *shop.SubmitShopSettleResp, err error)
	DetailShopSettle(ctx context.Context, req *shop.DetailShopSettleReq, callOptions ...callopt.Option) (r *shop.DetailShopSettleResp, err error)
	AuditShopSettle(ctx context.Context, req *shop.AuditShopSettleReq, callOptions ...callopt.Option) (r *shop.AuditShopSettleResp, err error)
	ActionShopSettleNode(ctx context.Context, req *shop.ActionShopSettleNodeReq, callOptions ...callopt.Option) (r *shop.ActionShopSettleNodeResp, err error)
	ListShopSettle(ctx context.Context, req *shop.ListShopSettleReq, callOptions ...callopt.Option) (r *shop.ListShopSettleResp, err error)
	DeleteShopSettle(ctx context.Context, req *shop.DeleteShopSettleReq, callOptions ...callopt.Option) (r *shop.DeleteShopSettleResp, err error)
	DeleteProductSettle(ctx context.Context, req *shop.DeleteProductSettleReq, callOptions ...callopt.Option) (r *shop.DeleteProductSettleResp, err error)
	UpdateShopSettleLevel(ctx context.Context, req *shop.UpdateShopSettleLevelReq, callOptions ...callopt.Option) (r *shop.UpdateShopSettleLevelResp, err error)
	UpdateShopSettleInfo(ctx context.Context, req *shop.UpdateShopSettleInfoReq, callOptions ...callopt.Option) (r *shop.UpdateShopSettleInfoResp, err error)
	CreateSettleContract(ctx context.Context, req *shop.CreateSettleContractReq, callOptions ...callopt.Option) (r *shop.CreateSettleContractResp, err error)
	QuerySettleContract(ctx context.Context, req *shop.QuerySettleContractReq, callOptions ...callopt.Option) (r *shop.QuerySettleContractResp, err error)
	PreviewSettleContract(ctx context.Context, req *shop.PreviewSettleContractReq, callOptions ...callopt.Option) (r *shop.PreviewSettleContractResp, err error)
	NotifySettleContract(ctx context.Context, req *shop.NotifySettleContractReq, callOptions ...callopt.Option) (r *shop.NotifySettleContractResp, err error)
	MGetCgAccount(ctx context.Context, req *shop.MGetCgAccountReq, callOptions ...callopt.Option) (r *shop.MGetCgAccountResp, err error)
	FetchPlatformURL(ctx context.Context, req *shop.FetchPlatformURLReq, callOptions ...callopt.Option) (r *shop.FetchPlatformURLResp, err error)
	FetchAccountCenterURL(ctx context.Context, req *shop.FetchAccountCenterURLReq, callOptions ...callopt.Option) (r *shop.FetchAccountCenterURLResp, err error)
	CreateFweSubMerchant(ctx context.Context, req *shop.CreateFweSubMerchantReq, callOptions ...callopt.Option) (r *shop.FweSubMerchantResp, err error)
	ModifyFweSubMerchantInfo(ctx context.Context, req *shop.ModifyFweSubMerchantReq, callOptions ...callopt.Option) (r *shop.FweSubMerchantResp, err error)
	SyncFinanceAccountReq(ctx context.Context, req *shop.SyncFinanceAccountReq, callOptions ...callopt.Option) (r *shop.SyncFinanceAccountResp, err error)
	MGetFinanceAccount(ctx context.Context, req *shop.MGetFinanceAccountReq, callOptions ...callopt.Option) (r *shop.MGetFinanceAccountResp, err error)
	GetAllAccountsByMerchantID(ctx context.Context, req *shop.GetAllAccountsByMerchantIDReq, callOptions ...callopt.Option) (r *shop.GetAllAccountsByMerchantIDResp, err error)
	CreateOrUpdateBzjAccount(ctx context.Context, req *shop.CreateOrUpdateBzjAccountReq, callOptions ...callopt.Option) (r *shop.CreateOrUpdateBzjAccountResp, err error)
	QueryBzjAccount(ctx context.Context, req *shop.QueryBzjAccountReq, callOptions ...callopt.Option) (r *shop.QueryBzjAccountResp, err error)
	ListShopRecipient(ctx context.Context, req *shop.ListShopRecipientReq, callOptions ...callopt.Option) (r *shop.ListShopRecipientResp, err error)
	AddShopRecipient(ctx context.Context, req *shop.AddShopRecipientReq, callOptions ...callopt.Option) (r *shop.AddShopRecipientResp, err error)
	UpdateShopRecipient(ctx context.Context, req *shop.UpdateShopRecipientReq, callOptions ...callopt.Option) (r *shop.UpdateShopRecipientResp, err error)
	DelShopRecipient(ctx context.Context, req *shop.DelShopDecipientReq, callOptions ...callopt.Option) (r *shop.DelShopRecipientResp, err error)
	UnionNotifySubMerchantCreate(ctx context.Context, req *shop.UnionNotifyRequest, callOptions ...callopt.Option) (r *shop.UnionNotifyResponse, err error)
	QuerySubMerchantInfo(ctx context.Context, req *shop.QuerySubMerchantInfoReq, callOptions ...callopt.Option) (r *shop.QuerySubMerchantInfoResp, err error)
	MGetSubjectQualification(ctx context.Context, req *shop.MGetSubjectQualificationReq, callOptions ...callopt.Option) (r *shop.MGetSubjectQualificationResp, err error)
	QueryFweSubMerchantList(ctx context.Context, req *shop.QueryFweSubMerchantListReq, callOptions ...callopt.Option) (r *shop.QueryFweSubMerchantListResp, err error)
	GetUserIDWithSessionID(ctx context.Context, req *shop.GetUserIDWithSessionIDReq, callOptions ...callopt.Option) (r *shop.GetUserIDWithSessionIDResp, err error)
	GetShopSubject(ctx context.Context, req *shop.GetShopSubjectReq, callOptions ...callopt.Option) (r *shop.GetShopSubjectResp, err error)
	GetTradeSubject(ctx context.Context, req *shop.GetTradeSubjectReq, callOptions ...callopt.Option) (r *shop.GetTradeSubjectResp, err error)
	MGetTradeSubjectByIds(ctx context.Context, req *shop.MGetTradeSubjectByIdsReq, callOptions ...callopt.Option) (r *shop.MGetTradeSubjectByIdsResp, err error)
	QueryCompanyRisk(ctx context.Context, req *shop.QueryCompanyRiskReq, callOptions ...callopt.Option) (r *shop.QueryCompanyRiskResp, err error)
	CheckSubjectCreateShopSettle(ctx context.Context, req *shop.CheckSubjectCreateShopSettleReq, callOptions ...callopt.Option) (r *shop.CheckSubjectCreateShopSettleResp, err error)
	CheckSettleFlowCreateSettle(ctx context.Context, req *shop.CheckSettleFlowCreateSettleReq, callOptions ...callopt.Option) (r *shop.CheckSettleFlowCreateSettleResp, err error)
	GetShopSettle(ctx context.Context, req *shop.GetShopSettleReq, callOptions ...callopt.Option) (r *shop.GetShopSettleResp, err error)
	GetProductSettle(ctx context.Context, req *shop.GetProductSettleReq, callOptions ...callopt.Option) (r *shop.GetProductSettleResp, err error)
	UpdateShopSettle(ctx context.Context, req *shop.UpdateShopSettleReq, callOptions ...callopt.Option) (r *shop.UpdateShopSettleResp, err error)
	ActionSubjectEvent(ctx context.Context, req *shop.ActionSubjectEventReq, callOptions ...callopt.Option) (r *shop.ActionSubjectEventResp, err error)
	GetShopSettleList(ctx context.Context, req *shop.GetShopSettleListReq, callOptions ...callopt.Option) (r *shop.GetShopSettleListResp, err error)
	UpdateShopSettleStatus(ctx context.Context, req *shop.UpdateShopSettleStatusReq, callOptions ...callopt.Option) (r *shop.UpdateShopSettleStatusResp, err error)
	UpdatePersonSettle(ctx context.Context, req *shop.UpdatePersonSettleReq, callOptions ...callopt.Option) (r *shop.UpdatePersonSettleResp, err error)
	ListTenantSettle(ctx context.Context, req *shop.ListTenantSettleReq, callOptions ...callopt.Option) (r *shop.ListTenantSettleResp, err error)
	ListProductSettle(ctx context.Context, req *shop.ListProductSettleReq, callOptions ...callopt.Option) (r *shop.ListProductSettleResp, err error)
	SubmitProductSettleBD(ctx context.Context, req *shop.SubmitProductSettleBDReq, callOptions ...callopt.Option) (r *shop.SubmitProductSettleBDResp, err error)
	UpdateFinanceAccount(ctx context.Context, req *shop.UpdateFinanceAccountReq, callOptions ...callopt.Option) (r *shop.UpdateFinanceAccountResp, err error)
	GetFinanceAccount(ctx context.Context, req *shop.GetFinanceAccountReq, callOptions ...callopt.Option) (r *shop.GetFinanceAccountResp, err error)
	GetSettleFlowList(ctx context.Context, req *shop.GetSettleFlowListReq, callOptions ...callopt.Option) (r *shop.GetSettleFlowListResp, err error)
	CheckWithCreateSettleFlow(ctx context.Context, req *shop.CheckWithCreateSettleFlowReq, callOptions ...callopt.Option) (r *shop.CheckWithCreateSettleFlowResp, err error)
	GetSettleFlow(ctx context.Context, req *shop.GetSettleFlowReq, callOptions ...callopt.Option) (r *shop.GetSettleFlowResp, err error)
	UpdateSettleFlow(ctx context.Context, req *shop.UpdateSettleFlowReq, callOptions ...callopt.Option) (r *shop.UpdateSettleFlowResp, err error)
	GetShopList(ctx context.Context, req *shop.GetShopListReq, callOptions ...callopt.Option) (r *shop.GetShopListResp, err error)
	MGetSubject(ctx context.Context, req *shop.MGetSubjectReq, callOptions ...callopt.Option) (r *shop.MGetSubjectResp, err error)
	GetShopMMMAccountCustomer(ctx context.Context, req *shop.GetShopMMMAccountCustomerReq, callOptions ...callopt.Option) (r *shop.GetShopMMMAccountCustomerResp, err error)
	GetSettleShopBySubject(ctx context.Context, req *shop.GetSettleShopBySubjectReq, callOptions ...callopt.Option) (r *shop.GetSettleShopBySubjectResp, err error)
	UpdateShopInfo(ctx context.Context, req *shop.UpdateShopInfoReq, callOptions ...callopt.Option) (r *shop.UpdateShopInfoResp, err error)
	MGetShopDetail(ctx context.Context, req *shop.MGetShopDetailReq, callOptions ...callopt.Option) (r *shop.MGetShopDetailResp, err error)
	SearchShop(ctx context.Context, req *shop.SearchShopReq, callOptions ...callopt.Option) (r *shop.SearchShopResp, err error)
	DeleteShop(ctx context.Context, req *shop.DeleteShopReq, callOptions ...callopt.Option) (r *shop.DeleteShopResp, err error)
	AddShopSubject(ctx context.Context, req *shop.AddShopSubjectReq, callOptions ...callopt.Option) (r *shop.AddShopSubjectResp, err error)
	QuerySubject(ctx context.Context, req *shop.QuerySubjectReq, callOptions ...callopt.Option) (r *shop.QuerySubjectResp, err error)
	SyncSupplyShop(ctx context.Context, req *shop.SyncSupplyShopReq, callOptions ...callopt.Option) (r *shop.SyncSupplyShopRsp, err error)
	ListShopByQyhOpenID(ctx context.Context, req *shop.ListShopByQyhOpenIDReq, callOptions ...callopt.Option) (r *shop.ListShopByQyhOpenIDResp, err error)
	BindShopQyhOpenID(ctx context.Context, req *shop.BindShopQyhOpenIDReq, callOptions ...callopt.Option) (r *shop.BindShopQyhOpenIDResp, err error)
	UnbindShopQyhOpenID(ctx context.Context, req *shop.UnbindShopQyhOpenIDReq, callOptions ...callopt.Option) (r *shop.UnbindShopQyhOpenIDResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kFWEAccountShopServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kFWEAccountShopServiceClient struct {
	*kClient
}

func (p *kFWEAccountShopServiceClient) CreateShop(ctx context.Context, req *shop.CreateShopReq, callOptions ...callopt.Option) (r *shop.CreateShopResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateShop(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdateShop(ctx context.Context, req *shop.UpdateShopReq, callOptions ...callopt.Option) (r *shop.UpdateShopResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateShop(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdateShopStatus(ctx context.Context, req *shop.UpdateShopStatusReq, callOptions ...callopt.Option) (r *shop.UpdateShopStatusResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateShopStatus(ctx, req)
}

func (p *kFWEAccountShopServiceClient) MGetShop(ctx context.Context, req *shop.MGetShopReq, callOptions ...callopt.Option) (r *shop.MGetShopResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetShop(ctx, req)
}

func (p *kFWEAccountShopServiceClient) ChangeOwner(ctx context.Context, req *shop.ChangeOwnerReq, callOptions ...callopt.Option) (r *shop.ChangeOwnerResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ChangeOwner(ctx, req)
}

func (p *kFWEAccountShopServiceClient) SettingChildren(ctx context.Context, req *shop.SettingChildrenReq, callOptions ...callopt.Option) (r *shop.SettingChildrenResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SettingChildren(ctx, req)
}

func (p *kFWEAccountShopServiceClient) ListChildren(ctx context.Context, req *shop.ListChildrenReq, callOptions ...callopt.Option) (r *shop.ListChildrenResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ListChildren(ctx, req)
}

func (p *kFWEAccountShopServiceClient) SubmitShopSettle(ctx context.Context, req *shop.SubmitShopSettleReq, callOptions ...callopt.Option) (r *shop.SubmitShopSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SubmitShopSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) DetailShopSettle(ctx context.Context, req *shop.DetailShopSettleReq, callOptions ...callopt.Option) (r *shop.DetailShopSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DetailShopSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) AuditShopSettle(ctx context.Context, req *shop.AuditShopSettleReq, callOptions ...callopt.Option) (r *shop.AuditShopSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AuditShopSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) ActionShopSettleNode(ctx context.Context, req *shop.ActionShopSettleNodeReq, callOptions ...callopt.Option) (r *shop.ActionShopSettleNodeResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ActionShopSettleNode(ctx, req)
}

func (p *kFWEAccountShopServiceClient) ListShopSettle(ctx context.Context, req *shop.ListShopSettleReq, callOptions ...callopt.Option) (r *shop.ListShopSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ListShopSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) DeleteShopSettle(ctx context.Context, req *shop.DeleteShopSettleReq, callOptions ...callopt.Option) (r *shop.DeleteShopSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DeleteShopSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) DeleteProductSettle(ctx context.Context, req *shop.DeleteProductSettleReq, callOptions ...callopt.Option) (r *shop.DeleteProductSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DeleteProductSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdateShopSettleLevel(ctx context.Context, req *shop.UpdateShopSettleLevelReq, callOptions ...callopt.Option) (r *shop.UpdateShopSettleLevelResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateShopSettleLevel(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdateShopSettleInfo(ctx context.Context, req *shop.UpdateShopSettleInfoReq, callOptions ...callopt.Option) (r *shop.UpdateShopSettleInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateShopSettleInfo(ctx, req)
}

func (p *kFWEAccountShopServiceClient) CreateSettleContract(ctx context.Context, req *shop.CreateSettleContractReq, callOptions ...callopt.Option) (r *shop.CreateSettleContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateSettleContract(ctx, req)
}

func (p *kFWEAccountShopServiceClient) QuerySettleContract(ctx context.Context, req *shop.QuerySettleContractReq, callOptions ...callopt.Option) (r *shop.QuerySettleContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QuerySettleContract(ctx, req)
}

func (p *kFWEAccountShopServiceClient) PreviewSettleContract(ctx context.Context, req *shop.PreviewSettleContractReq, callOptions ...callopt.Option) (r *shop.PreviewSettleContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.PreviewSettleContract(ctx, req)
}

func (p *kFWEAccountShopServiceClient) NotifySettleContract(ctx context.Context, req *shop.NotifySettleContractReq, callOptions ...callopt.Option) (r *shop.NotifySettleContractResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.NotifySettleContract(ctx, req)
}

func (p *kFWEAccountShopServiceClient) MGetCgAccount(ctx context.Context, req *shop.MGetCgAccountReq, callOptions ...callopt.Option) (r *shop.MGetCgAccountResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetCgAccount(ctx, req)
}

func (p *kFWEAccountShopServiceClient) FetchPlatformURL(ctx context.Context, req *shop.FetchPlatformURLReq, callOptions ...callopt.Option) (r *shop.FetchPlatformURLResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FetchPlatformURL(ctx, req)
}

func (p *kFWEAccountShopServiceClient) FetchAccountCenterURL(ctx context.Context, req *shop.FetchAccountCenterURLReq, callOptions ...callopt.Option) (r *shop.FetchAccountCenterURLResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FetchAccountCenterURL(ctx, req)
}

func (p *kFWEAccountShopServiceClient) CreateFweSubMerchant(ctx context.Context, req *shop.CreateFweSubMerchantReq, callOptions ...callopt.Option) (r *shop.FweSubMerchantResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateFweSubMerchant(ctx, req)
}

func (p *kFWEAccountShopServiceClient) ModifyFweSubMerchantInfo(ctx context.Context, req *shop.ModifyFweSubMerchantReq, callOptions ...callopt.Option) (r *shop.FweSubMerchantResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ModifyFweSubMerchantInfo(ctx, req)
}

func (p *kFWEAccountShopServiceClient) SyncFinanceAccountReq(ctx context.Context, req *shop.SyncFinanceAccountReq, callOptions ...callopt.Option) (r *shop.SyncFinanceAccountResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SyncFinanceAccountReq(ctx, req)
}

func (p *kFWEAccountShopServiceClient) MGetFinanceAccount(ctx context.Context, req *shop.MGetFinanceAccountReq, callOptions ...callopt.Option) (r *shop.MGetFinanceAccountResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetFinanceAccount(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetAllAccountsByMerchantID(ctx context.Context, req *shop.GetAllAccountsByMerchantIDReq, callOptions ...callopt.Option) (r *shop.GetAllAccountsByMerchantIDResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetAllAccountsByMerchantID(ctx, req)
}

func (p *kFWEAccountShopServiceClient) CreateOrUpdateBzjAccount(ctx context.Context, req *shop.CreateOrUpdateBzjAccountReq, callOptions ...callopt.Option) (r *shop.CreateOrUpdateBzjAccountResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateOrUpdateBzjAccount(ctx, req)
}

func (p *kFWEAccountShopServiceClient) QueryBzjAccount(ctx context.Context, req *shop.QueryBzjAccountReq, callOptions ...callopt.Option) (r *shop.QueryBzjAccountResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryBzjAccount(ctx, req)
}

func (p *kFWEAccountShopServiceClient) ListShopRecipient(ctx context.Context, req *shop.ListShopRecipientReq, callOptions ...callopt.Option) (r *shop.ListShopRecipientResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ListShopRecipient(ctx, req)
}

func (p *kFWEAccountShopServiceClient) AddShopRecipient(ctx context.Context, req *shop.AddShopRecipientReq, callOptions ...callopt.Option) (r *shop.AddShopRecipientResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AddShopRecipient(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdateShopRecipient(ctx context.Context, req *shop.UpdateShopRecipientReq, callOptions ...callopt.Option) (r *shop.UpdateShopRecipientResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateShopRecipient(ctx, req)
}

func (p *kFWEAccountShopServiceClient) DelShopRecipient(ctx context.Context, req *shop.DelShopDecipientReq, callOptions ...callopt.Option) (r *shop.DelShopRecipientResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DelShopRecipient(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UnionNotifySubMerchantCreate(ctx context.Context, req *shop.UnionNotifyRequest, callOptions ...callopt.Option) (r *shop.UnionNotifyResponse, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnionNotifySubMerchantCreate(ctx, req)
}

func (p *kFWEAccountShopServiceClient) QuerySubMerchantInfo(ctx context.Context, req *shop.QuerySubMerchantInfoReq, callOptions ...callopt.Option) (r *shop.QuerySubMerchantInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QuerySubMerchantInfo(ctx, req)
}

func (p *kFWEAccountShopServiceClient) MGetSubjectQualification(ctx context.Context, req *shop.MGetSubjectQualificationReq, callOptions ...callopt.Option) (r *shop.MGetSubjectQualificationResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetSubjectQualification(ctx, req)
}

func (p *kFWEAccountShopServiceClient) QueryFweSubMerchantList(ctx context.Context, req *shop.QueryFweSubMerchantListReq, callOptions ...callopt.Option) (r *shop.QueryFweSubMerchantListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryFweSubMerchantList(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetUserIDWithSessionID(ctx context.Context, req *shop.GetUserIDWithSessionIDReq, callOptions ...callopt.Option) (r *shop.GetUserIDWithSessionIDResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetUserIDWithSessionID(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetShopSubject(ctx context.Context, req *shop.GetShopSubjectReq, callOptions ...callopt.Option) (r *shop.GetShopSubjectResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetShopSubject(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetTradeSubject(ctx context.Context, req *shop.GetTradeSubjectReq, callOptions ...callopt.Option) (r *shop.GetTradeSubjectResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetTradeSubject(ctx, req)
}

func (p *kFWEAccountShopServiceClient) MGetTradeSubjectByIds(ctx context.Context, req *shop.MGetTradeSubjectByIdsReq, callOptions ...callopt.Option) (r *shop.MGetTradeSubjectByIdsResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetTradeSubjectByIds(ctx, req)
}

func (p *kFWEAccountShopServiceClient) QueryCompanyRisk(ctx context.Context, req *shop.QueryCompanyRiskReq, callOptions ...callopt.Option) (r *shop.QueryCompanyRiskResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryCompanyRisk(ctx, req)
}

func (p *kFWEAccountShopServiceClient) CheckSubjectCreateShopSettle(ctx context.Context, req *shop.CheckSubjectCreateShopSettleReq, callOptions ...callopt.Option) (r *shop.CheckSubjectCreateShopSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CheckSubjectCreateShopSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) CheckSettleFlowCreateSettle(ctx context.Context, req *shop.CheckSettleFlowCreateSettleReq, callOptions ...callopt.Option) (r *shop.CheckSettleFlowCreateSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CheckSettleFlowCreateSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetShopSettle(ctx context.Context, req *shop.GetShopSettleReq, callOptions ...callopt.Option) (r *shop.GetShopSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetShopSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetProductSettle(ctx context.Context, req *shop.GetProductSettleReq, callOptions ...callopt.Option) (r *shop.GetProductSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetProductSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdateShopSettle(ctx context.Context, req *shop.UpdateShopSettleReq, callOptions ...callopt.Option) (r *shop.UpdateShopSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateShopSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) ActionSubjectEvent(ctx context.Context, req *shop.ActionSubjectEventReq, callOptions ...callopt.Option) (r *shop.ActionSubjectEventResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ActionSubjectEvent(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetShopSettleList(ctx context.Context, req *shop.GetShopSettleListReq, callOptions ...callopt.Option) (r *shop.GetShopSettleListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetShopSettleList(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdateShopSettleStatus(ctx context.Context, req *shop.UpdateShopSettleStatusReq, callOptions ...callopt.Option) (r *shop.UpdateShopSettleStatusResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateShopSettleStatus(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdatePersonSettle(ctx context.Context, req *shop.UpdatePersonSettleReq, callOptions ...callopt.Option) (r *shop.UpdatePersonSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdatePersonSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) ListTenantSettle(ctx context.Context, req *shop.ListTenantSettleReq, callOptions ...callopt.Option) (r *shop.ListTenantSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ListTenantSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) ListProductSettle(ctx context.Context, req *shop.ListProductSettleReq, callOptions ...callopt.Option) (r *shop.ListProductSettleResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ListProductSettle(ctx, req)
}

func (p *kFWEAccountShopServiceClient) SubmitProductSettleBD(ctx context.Context, req *shop.SubmitProductSettleBDReq, callOptions ...callopt.Option) (r *shop.SubmitProductSettleBDResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SubmitProductSettleBD(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdateFinanceAccount(ctx context.Context, req *shop.UpdateFinanceAccountReq, callOptions ...callopt.Option) (r *shop.UpdateFinanceAccountResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateFinanceAccount(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetFinanceAccount(ctx context.Context, req *shop.GetFinanceAccountReq, callOptions ...callopt.Option) (r *shop.GetFinanceAccountResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetFinanceAccount(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetSettleFlowList(ctx context.Context, req *shop.GetSettleFlowListReq, callOptions ...callopt.Option) (r *shop.GetSettleFlowListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetSettleFlowList(ctx, req)
}

func (p *kFWEAccountShopServiceClient) CheckWithCreateSettleFlow(ctx context.Context, req *shop.CheckWithCreateSettleFlowReq, callOptions ...callopt.Option) (r *shop.CheckWithCreateSettleFlowResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CheckWithCreateSettleFlow(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetSettleFlow(ctx context.Context, req *shop.GetSettleFlowReq, callOptions ...callopt.Option) (r *shop.GetSettleFlowResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetSettleFlow(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdateSettleFlow(ctx context.Context, req *shop.UpdateSettleFlowReq, callOptions ...callopt.Option) (r *shop.UpdateSettleFlowResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateSettleFlow(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetShopList(ctx context.Context, req *shop.GetShopListReq, callOptions ...callopt.Option) (r *shop.GetShopListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetShopList(ctx, req)
}

func (p *kFWEAccountShopServiceClient) MGetSubject(ctx context.Context, req *shop.MGetSubjectReq, callOptions ...callopt.Option) (r *shop.MGetSubjectResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetSubject(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetShopMMMAccountCustomer(ctx context.Context, req *shop.GetShopMMMAccountCustomerReq, callOptions ...callopt.Option) (r *shop.GetShopMMMAccountCustomerResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetShopMMMAccountCustomer(ctx, req)
}

func (p *kFWEAccountShopServiceClient) GetSettleShopBySubject(ctx context.Context, req *shop.GetSettleShopBySubjectReq, callOptions ...callopt.Option) (r *shop.GetSettleShopBySubjectResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetSettleShopBySubject(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UpdateShopInfo(ctx context.Context, req *shop.UpdateShopInfoReq, callOptions ...callopt.Option) (r *shop.UpdateShopInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateShopInfo(ctx, req)
}

func (p *kFWEAccountShopServiceClient) MGetShopDetail(ctx context.Context, req *shop.MGetShopDetailReq, callOptions ...callopt.Option) (r *shop.MGetShopDetailResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetShopDetail(ctx, req)
}

func (p *kFWEAccountShopServiceClient) SearchShop(ctx context.Context, req *shop.SearchShopReq, callOptions ...callopt.Option) (r *shop.SearchShopResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SearchShop(ctx, req)
}

func (p *kFWEAccountShopServiceClient) DeleteShop(ctx context.Context, req *shop.DeleteShopReq, callOptions ...callopt.Option) (r *shop.DeleteShopResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DeleteShop(ctx, req)
}

func (p *kFWEAccountShopServiceClient) AddShopSubject(ctx context.Context, req *shop.AddShopSubjectReq, callOptions ...callopt.Option) (r *shop.AddShopSubjectResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.AddShopSubject(ctx, req)
}

func (p *kFWEAccountShopServiceClient) QuerySubject(ctx context.Context, req *shop.QuerySubjectReq, callOptions ...callopt.Option) (r *shop.QuerySubjectResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QuerySubject(ctx, req)
}

func (p *kFWEAccountShopServiceClient) SyncSupplyShop(ctx context.Context, req *shop.SyncSupplyShopReq, callOptions ...callopt.Option) (r *shop.SyncSupplyShopRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SyncSupplyShop(ctx, req)
}

func (p *kFWEAccountShopServiceClient) ListShopByQyhOpenID(ctx context.Context, req *shop.ListShopByQyhOpenIDReq, callOptions ...callopt.Option) (r *shop.ListShopByQyhOpenIDResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ListShopByQyhOpenID(ctx, req)
}

func (p *kFWEAccountShopServiceClient) BindShopQyhOpenID(ctx context.Context, req *shop.BindShopQyhOpenIDReq, callOptions ...callopt.Option) (r *shop.BindShopQyhOpenIDResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.BindShopQyhOpenID(ctx, req)
}

func (p *kFWEAccountShopServiceClient) UnbindShopQyhOpenID(ctx context.Context, req *shop.UnbindShopQyhOpenIDReq, callOptions ...callopt.Option) (r *shop.UnbindShopQyhOpenIDResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnbindShopQyhOpenID(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kFWEAccountShopServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
