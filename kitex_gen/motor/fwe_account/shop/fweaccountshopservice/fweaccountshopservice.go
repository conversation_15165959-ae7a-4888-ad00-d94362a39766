// Code generated by Kitex v1.20.3. DO NOT EDIT.

package fweaccountshopservice

import (
	client "code.byted.org/kite/kitex/client"
	shop "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_account/shop"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"CreateShop": kitex.NewMethodInfo(
		createShopHandler,
		newFWEAccountShopServiceCreateShopArgs,
		newFWEAccountShopServiceCreateShopResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateShop": kitex.NewMethodInfo(
		updateShopHandler,
		newFWEAccountShopServiceUpdateShopArgs,
		newFWEAccountShopServiceUpdateShopResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateShopStatus": kitex.NewMethodInfo(
		updateShopStatusHandler,
		newFWEAccountShopServiceUpdateShopStatusArgs,
		newFWEAccountShopServiceUpdateShopStatusResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetShop": kitex.NewMethodInfo(
		mGetShopHandler,
		newFWEAccountShopServiceMGetShopArgs,
		newFWEAccountShopServiceMGetShopResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ChangeOwner": kitex.NewMethodInfo(
		changeOwnerHandler,
		newFWEAccountShopServiceChangeOwnerArgs,
		newFWEAccountShopServiceChangeOwnerResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SettingChildren": kitex.NewMethodInfo(
		settingChildrenHandler,
		newFWEAccountShopServiceSettingChildrenArgs,
		newFWEAccountShopServiceSettingChildrenResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ListChildren": kitex.NewMethodInfo(
		listChildrenHandler,
		newFWEAccountShopServiceListChildrenArgs,
		newFWEAccountShopServiceListChildrenResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SubmitShopSettle": kitex.NewMethodInfo(
		submitShopSettleHandler,
		newFWEAccountShopServiceSubmitShopSettleArgs,
		newFWEAccountShopServiceSubmitShopSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DetailShopSettle": kitex.NewMethodInfo(
		detailShopSettleHandler,
		newFWEAccountShopServiceDetailShopSettleArgs,
		newFWEAccountShopServiceDetailShopSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AuditShopSettle": kitex.NewMethodInfo(
		auditShopSettleHandler,
		newFWEAccountShopServiceAuditShopSettleArgs,
		newFWEAccountShopServiceAuditShopSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ActionShopSettleNode": kitex.NewMethodInfo(
		actionShopSettleNodeHandler,
		newFWEAccountShopServiceActionShopSettleNodeArgs,
		newFWEAccountShopServiceActionShopSettleNodeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ListShopSettle": kitex.NewMethodInfo(
		listShopSettleHandler,
		newFWEAccountShopServiceListShopSettleArgs,
		newFWEAccountShopServiceListShopSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DeleteShopSettle": kitex.NewMethodInfo(
		deleteShopSettleHandler,
		newFWEAccountShopServiceDeleteShopSettleArgs,
		newFWEAccountShopServiceDeleteShopSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DeleteProductSettle": kitex.NewMethodInfo(
		deleteProductSettleHandler,
		newFWEAccountShopServiceDeleteProductSettleArgs,
		newFWEAccountShopServiceDeleteProductSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateShopSettleLevel": kitex.NewMethodInfo(
		updateShopSettleLevelHandler,
		newFWEAccountShopServiceUpdateShopSettleLevelArgs,
		newFWEAccountShopServiceUpdateShopSettleLevelResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateShopSettleInfo": kitex.NewMethodInfo(
		updateShopSettleInfoHandler,
		newFWEAccountShopServiceUpdateShopSettleInfoArgs,
		newFWEAccountShopServiceUpdateShopSettleInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateSettleContract": kitex.NewMethodInfo(
		createSettleContractHandler,
		newFWEAccountShopServiceCreateSettleContractArgs,
		newFWEAccountShopServiceCreateSettleContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QuerySettleContract": kitex.NewMethodInfo(
		querySettleContractHandler,
		newFWEAccountShopServiceQuerySettleContractArgs,
		newFWEAccountShopServiceQuerySettleContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"PreviewSettleContract": kitex.NewMethodInfo(
		previewSettleContractHandler,
		newFWEAccountShopServicePreviewSettleContractArgs,
		newFWEAccountShopServicePreviewSettleContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"NotifySettleContract": kitex.NewMethodInfo(
		notifySettleContractHandler,
		newFWEAccountShopServiceNotifySettleContractArgs,
		newFWEAccountShopServiceNotifySettleContractResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetCgAccount": kitex.NewMethodInfo(
		mGetCgAccountHandler,
		newFWEAccountShopServiceMGetCgAccountArgs,
		newFWEAccountShopServiceMGetCgAccountResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FetchPlatformUrl": kitex.NewMethodInfo(
		fetchPlatformURLHandler,
		newFWEAccountShopServiceFetchPlatformURLArgs,
		newFWEAccountShopServiceFetchPlatformURLResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FetchAccountCenterUrl": kitex.NewMethodInfo(
		fetchAccountCenterURLHandler,
		newFWEAccountShopServiceFetchAccountCenterURLArgs,
		newFWEAccountShopServiceFetchAccountCenterURLResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateFweSubMerchant": kitex.NewMethodInfo(
		createFweSubMerchantHandler,
		newFWEAccountShopServiceCreateFweSubMerchantArgs,
		newFWEAccountShopServiceCreateFweSubMerchantResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ModifyFweSubMerchantInfo": kitex.NewMethodInfo(
		modifyFweSubMerchantInfoHandler,
		newFWEAccountShopServiceModifyFweSubMerchantInfoArgs,
		newFWEAccountShopServiceModifyFweSubMerchantInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SyncFinanceAccountReq": kitex.NewMethodInfo(
		syncFinanceAccountReqHandler,
		newFWEAccountShopServiceSyncFinanceAccountReqArgs,
		newFWEAccountShopServiceSyncFinanceAccountReqResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetFinanceAccount": kitex.NewMethodInfo(
		mGetFinanceAccountHandler,
		newFWEAccountShopServiceMGetFinanceAccountArgs,
		newFWEAccountShopServiceMGetFinanceAccountResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetAllAccountsByMerchantID": kitex.NewMethodInfo(
		getAllAccountsByMerchantIDHandler,
		newFWEAccountShopServiceGetAllAccountsByMerchantIDArgs,
		newFWEAccountShopServiceGetAllAccountsByMerchantIDResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateOrUpdateBzjAccount": kitex.NewMethodInfo(
		createOrUpdateBzjAccountHandler,
		newFWEAccountShopServiceCreateOrUpdateBzjAccountArgs,
		newFWEAccountShopServiceCreateOrUpdateBzjAccountResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryBzjAccount": kitex.NewMethodInfo(
		queryBzjAccountHandler,
		newFWEAccountShopServiceQueryBzjAccountArgs,
		newFWEAccountShopServiceQueryBzjAccountResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ListShopRecipient": kitex.NewMethodInfo(
		listShopRecipientHandler,
		newFWEAccountShopServiceListShopRecipientArgs,
		newFWEAccountShopServiceListShopRecipientResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AddShopRecipient": kitex.NewMethodInfo(
		addShopRecipientHandler,
		newFWEAccountShopServiceAddShopRecipientArgs,
		newFWEAccountShopServiceAddShopRecipientResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateShopRecipient": kitex.NewMethodInfo(
		updateShopRecipientHandler,
		newFWEAccountShopServiceUpdateShopRecipientArgs,
		newFWEAccountShopServiceUpdateShopRecipientResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DelShopRecipient": kitex.NewMethodInfo(
		delShopRecipientHandler,
		newFWEAccountShopServiceDelShopRecipientArgs,
		newFWEAccountShopServiceDelShopRecipientResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnionNotifySubMerchantCreate": kitex.NewMethodInfo(
		unionNotifySubMerchantCreateHandler,
		newFWEAccountShopServiceUnionNotifySubMerchantCreateArgs,
		newFWEAccountShopServiceUnionNotifySubMerchantCreateResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QuerySubMerchantInfo": kitex.NewMethodInfo(
		querySubMerchantInfoHandler,
		newFWEAccountShopServiceQuerySubMerchantInfoArgs,
		newFWEAccountShopServiceQuerySubMerchantInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetSubjectQualification": kitex.NewMethodInfo(
		mGetSubjectQualificationHandler,
		newFWEAccountShopServiceMGetSubjectQualificationArgs,
		newFWEAccountShopServiceMGetSubjectQualificationResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryFweSubMerchantList": kitex.NewMethodInfo(
		queryFweSubMerchantListHandler,
		newFWEAccountShopServiceQueryFweSubMerchantListArgs,
		newFWEAccountShopServiceQueryFweSubMerchantListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetUserIdWithSessionId": kitex.NewMethodInfo(
		getUserIDWithSessionIDHandler,
		newFWEAccountShopServiceGetUserIDWithSessionIDArgs,
		newFWEAccountShopServiceGetUserIDWithSessionIDResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetShopSubject": kitex.NewMethodInfo(
		getShopSubjectHandler,
		newFWEAccountShopServiceGetShopSubjectArgs,
		newFWEAccountShopServiceGetShopSubjectResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetTradeSubject": kitex.NewMethodInfo(
		getTradeSubjectHandler,
		newFWEAccountShopServiceGetTradeSubjectArgs,
		newFWEAccountShopServiceGetTradeSubjectResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetTradeSubjectByIds": kitex.NewMethodInfo(
		mGetTradeSubjectByIdsHandler,
		newFWEAccountShopServiceMGetTradeSubjectByIdsArgs,
		newFWEAccountShopServiceMGetTradeSubjectByIdsResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryCompanyRisk": kitex.NewMethodInfo(
		queryCompanyRiskHandler,
		newFWEAccountShopServiceQueryCompanyRiskArgs,
		newFWEAccountShopServiceQueryCompanyRiskResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CheckSubjectCreateShopSettle": kitex.NewMethodInfo(
		checkSubjectCreateShopSettleHandler,
		newFWEAccountShopServiceCheckSubjectCreateShopSettleArgs,
		newFWEAccountShopServiceCheckSubjectCreateShopSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CheckSettleFlowCreateSettle": kitex.NewMethodInfo(
		checkSettleFlowCreateSettleHandler,
		newFWEAccountShopServiceCheckSettleFlowCreateSettleArgs,
		newFWEAccountShopServiceCheckSettleFlowCreateSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetShopSettle": kitex.NewMethodInfo(
		getShopSettleHandler,
		newFWEAccountShopServiceGetShopSettleArgs,
		newFWEAccountShopServiceGetShopSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetProductSettle": kitex.NewMethodInfo(
		getProductSettleHandler,
		newFWEAccountShopServiceGetProductSettleArgs,
		newFWEAccountShopServiceGetProductSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateShopSettle": kitex.NewMethodInfo(
		updateShopSettleHandler,
		newFWEAccountShopServiceUpdateShopSettleArgs,
		newFWEAccountShopServiceUpdateShopSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ActionSubjectEvent": kitex.NewMethodInfo(
		actionSubjectEventHandler,
		newFWEAccountShopServiceActionSubjectEventArgs,
		newFWEAccountShopServiceActionSubjectEventResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetShopSettleList": kitex.NewMethodInfo(
		getShopSettleListHandler,
		newFWEAccountShopServiceGetShopSettleListArgs,
		newFWEAccountShopServiceGetShopSettleListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateShopSettleStatus": kitex.NewMethodInfo(
		updateShopSettleStatusHandler,
		newFWEAccountShopServiceUpdateShopSettleStatusArgs,
		newFWEAccountShopServiceUpdateShopSettleStatusResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdatePersonSettle": kitex.NewMethodInfo(
		updatePersonSettleHandler,
		newFWEAccountShopServiceUpdatePersonSettleArgs,
		newFWEAccountShopServiceUpdatePersonSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ListTenantSettle": kitex.NewMethodInfo(
		listTenantSettleHandler,
		newFWEAccountShopServiceListTenantSettleArgs,
		newFWEAccountShopServiceListTenantSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ListProductSettle": kitex.NewMethodInfo(
		listProductSettleHandler,
		newFWEAccountShopServiceListProductSettleArgs,
		newFWEAccountShopServiceListProductSettleResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SubmitProductSettleBD": kitex.NewMethodInfo(
		submitProductSettleBDHandler,
		newFWEAccountShopServiceSubmitProductSettleBDArgs,
		newFWEAccountShopServiceSubmitProductSettleBDResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateFinanceAccount": kitex.NewMethodInfo(
		updateFinanceAccountHandler,
		newFWEAccountShopServiceUpdateFinanceAccountArgs,
		newFWEAccountShopServiceUpdateFinanceAccountResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetFinanceAccount": kitex.NewMethodInfo(
		getFinanceAccountHandler,
		newFWEAccountShopServiceGetFinanceAccountArgs,
		newFWEAccountShopServiceGetFinanceAccountResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetSettleFlowList": kitex.NewMethodInfo(
		getSettleFlowListHandler,
		newFWEAccountShopServiceGetSettleFlowListArgs,
		newFWEAccountShopServiceGetSettleFlowListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CheckWithCreateSettleFlow": kitex.NewMethodInfo(
		checkWithCreateSettleFlowHandler,
		newFWEAccountShopServiceCheckWithCreateSettleFlowArgs,
		newFWEAccountShopServiceCheckWithCreateSettleFlowResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetSettleFlow": kitex.NewMethodInfo(
		getSettleFlowHandler,
		newFWEAccountShopServiceGetSettleFlowArgs,
		newFWEAccountShopServiceGetSettleFlowResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateSettleFlow": kitex.NewMethodInfo(
		updateSettleFlowHandler,
		newFWEAccountShopServiceUpdateSettleFlowArgs,
		newFWEAccountShopServiceUpdateSettleFlowResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetShopList": kitex.NewMethodInfo(
		getShopListHandler,
		newFWEAccountShopServiceGetShopListArgs,
		newFWEAccountShopServiceGetShopListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetSubject": kitex.NewMethodInfo(
		mGetSubjectHandler,
		newFWEAccountShopServiceMGetSubjectArgs,
		newFWEAccountShopServiceMGetSubjectResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetShopMMMAccountCustomer": kitex.NewMethodInfo(
		getShopMMMAccountCustomerHandler,
		newFWEAccountShopServiceGetShopMMMAccountCustomerArgs,
		newFWEAccountShopServiceGetShopMMMAccountCustomerResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetSettleShopBySubject": kitex.NewMethodInfo(
		getSettleShopBySubjectHandler,
		newFWEAccountShopServiceGetSettleShopBySubjectArgs,
		newFWEAccountShopServiceGetSettleShopBySubjectResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateShopInfo": kitex.NewMethodInfo(
		updateShopInfoHandler,
		newFWEAccountShopServiceUpdateShopInfoArgs,
		newFWEAccountShopServiceUpdateShopInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetShopDetail": kitex.NewMethodInfo(
		mGetShopDetailHandler,
		newFWEAccountShopServiceMGetShopDetailArgs,
		newFWEAccountShopServiceMGetShopDetailResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SearchShop": kitex.NewMethodInfo(
		searchShopHandler,
		newFWEAccountShopServiceSearchShopArgs,
		newFWEAccountShopServiceSearchShopResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DeleteShop": kitex.NewMethodInfo(
		deleteShopHandler,
		newFWEAccountShopServiceDeleteShopArgs,
		newFWEAccountShopServiceDeleteShopResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"AddShopSubject": kitex.NewMethodInfo(
		addShopSubjectHandler,
		newFWEAccountShopServiceAddShopSubjectArgs,
		newFWEAccountShopServiceAddShopSubjectResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QuerySubject": kitex.NewMethodInfo(
		querySubjectHandler,
		newFWEAccountShopServiceQuerySubjectArgs,
		newFWEAccountShopServiceQuerySubjectResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SyncSupplyShop": kitex.NewMethodInfo(
		syncSupplyShopHandler,
		newFWEAccountShopServiceSyncSupplyShopArgs,
		newFWEAccountShopServiceSyncSupplyShopResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ListShopByQyhOpenID": kitex.NewMethodInfo(
		listShopByQyhOpenIDHandler,
		newFWEAccountShopServiceListShopByQyhOpenIDArgs,
		newFWEAccountShopServiceListShopByQyhOpenIDResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"BindShopQyhOpenID": kitex.NewMethodInfo(
		bindShopQyhOpenIDHandler,
		newFWEAccountShopServiceBindShopQyhOpenIDArgs,
		newFWEAccountShopServiceBindShopQyhOpenIDResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnbindShopQyhOpenID": kitex.NewMethodInfo(
		unbindShopQyhOpenIDHandler,
		newFWEAccountShopServiceUnbindShopQyhOpenIDArgs,
		newFWEAccountShopServiceUnbindShopQyhOpenIDResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	fWEAccountShopServiceServiceInfo                = NewServiceInfo()
	fWEAccountShopServiceServiceInfoForClient       = NewServiceInfoForClient()
	fWEAccountShopServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return fWEAccountShopServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return fWEAccountShopServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return fWEAccountShopServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "FWEAccountShopService"
	handlerType := (*shop.FWEAccountShopService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "shop",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func createShopHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceCreateShopArgs)
	realResult := result.(*shop.FWEAccountShopServiceCreateShopResult)
	success, err := handler.(shop.FWEAccountShopService).CreateShop(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceCreateShopArgs() interface{} {
	return shop.NewFWEAccountShopServiceCreateShopArgs()
}

func newFWEAccountShopServiceCreateShopResult() interface{} {
	return shop.NewFWEAccountShopServiceCreateShopResult()
}

func updateShopHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdateShopArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdateShopResult)
	success, err := handler.(shop.FWEAccountShopService).UpdateShop(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdateShopArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopArgs()
}

func newFWEAccountShopServiceUpdateShopResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopResult()
}

func updateShopStatusHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdateShopStatusArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdateShopStatusResult)
	success, err := handler.(shop.FWEAccountShopService).UpdateShopStatus(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdateShopStatusArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopStatusArgs()
}

func newFWEAccountShopServiceUpdateShopStatusResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopStatusResult()
}

func mGetShopHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceMGetShopArgs)
	realResult := result.(*shop.FWEAccountShopServiceMGetShopResult)
	success, err := handler.(shop.FWEAccountShopService).MGetShop(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceMGetShopArgs() interface{} {
	return shop.NewFWEAccountShopServiceMGetShopArgs()
}

func newFWEAccountShopServiceMGetShopResult() interface{} {
	return shop.NewFWEAccountShopServiceMGetShopResult()
}

func changeOwnerHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceChangeOwnerArgs)
	realResult := result.(*shop.FWEAccountShopServiceChangeOwnerResult)
	success, err := handler.(shop.FWEAccountShopService).ChangeOwner(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceChangeOwnerArgs() interface{} {
	return shop.NewFWEAccountShopServiceChangeOwnerArgs()
}

func newFWEAccountShopServiceChangeOwnerResult() interface{} {
	return shop.NewFWEAccountShopServiceChangeOwnerResult()
}

func settingChildrenHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceSettingChildrenArgs)
	realResult := result.(*shop.FWEAccountShopServiceSettingChildrenResult)
	success, err := handler.(shop.FWEAccountShopService).SettingChildren(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceSettingChildrenArgs() interface{} {
	return shop.NewFWEAccountShopServiceSettingChildrenArgs()
}

func newFWEAccountShopServiceSettingChildrenResult() interface{} {
	return shop.NewFWEAccountShopServiceSettingChildrenResult()
}

func listChildrenHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceListChildrenArgs)
	realResult := result.(*shop.FWEAccountShopServiceListChildrenResult)
	success, err := handler.(shop.FWEAccountShopService).ListChildren(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceListChildrenArgs() interface{} {
	return shop.NewFWEAccountShopServiceListChildrenArgs()
}

func newFWEAccountShopServiceListChildrenResult() interface{} {
	return shop.NewFWEAccountShopServiceListChildrenResult()
}

func submitShopSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceSubmitShopSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceSubmitShopSettleResult)
	success, err := handler.(shop.FWEAccountShopService).SubmitShopSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceSubmitShopSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceSubmitShopSettleArgs()
}

func newFWEAccountShopServiceSubmitShopSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceSubmitShopSettleResult()
}

func detailShopSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceDetailShopSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceDetailShopSettleResult)
	success, err := handler.(shop.FWEAccountShopService).DetailShopSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceDetailShopSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceDetailShopSettleArgs()
}

func newFWEAccountShopServiceDetailShopSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceDetailShopSettleResult()
}

func auditShopSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceAuditShopSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceAuditShopSettleResult)
	success, err := handler.(shop.FWEAccountShopService).AuditShopSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceAuditShopSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceAuditShopSettleArgs()
}

func newFWEAccountShopServiceAuditShopSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceAuditShopSettleResult()
}

func actionShopSettleNodeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceActionShopSettleNodeArgs)
	realResult := result.(*shop.FWEAccountShopServiceActionShopSettleNodeResult)
	success, err := handler.(shop.FWEAccountShopService).ActionShopSettleNode(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceActionShopSettleNodeArgs() interface{} {
	return shop.NewFWEAccountShopServiceActionShopSettleNodeArgs()
}

func newFWEAccountShopServiceActionShopSettleNodeResult() interface{} {
	return shop.NewFWEAccountShopServiceActionShopSettleNodeResult()
}

func listShopSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceListShopSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceListShopSettleResult)
	success, err := handler.(shop.FWEAccountShopService).ListShopSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceListShopSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceListShopSettleArgs()
}

func newFWEAccountShopServiceListShopSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceListShopSettleResult()
}

func deleteShopSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceDeleteShopSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceDeleteShopSettleResult)
	success, err := handler.(shop.FWEAccountShopService).DeleteShopSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceDeleteShopSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceDeleteShopSettleArgs()
}

func newFWEAccountShopServiceDeleteShopSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceDeleteShopSettleResult()
}

func deleteProductSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceDeleteProductSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceDeleteProductSettleResult)
	success, err := handler.(shop.FWEAccountShopService).DeleteProductSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceDeleteProductSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceDeleteProductSettleArgs()
}

func newFWEAccountShopServiceDeleteProductSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceDeleteProductSettleResult()
}

func updateShopSettleLevelHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdateShopSettleLevelArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdateShopSettleLevelResult)
	success, err := handler.(shop.FWEAccountShopService).UpdateShopSettleLevel(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdateShopSettleLevelArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopSettleLevelArgs()
}

func newFWEAccountShopServiceUpdateShopSettleLevelResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopSettleLevelResult()
}

func updateShopSettleInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdateShopSettleInfoArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdateShopSettleInfoResult)
	success, err := handler.(shop.FWEAccountShopService).UpdateShopSettleInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdateShopSettleInfoArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopSettleInfoArgs()
}

func newFWEAccountShopServiceUpdateShopSettleInfoResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopSettleInfoResult()
}

func createSettleContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceCreateSettleContractArgs)
	realResult := result.(*shop.FWEAccountShopServiceCreateSettleContractResult)
	success, err := handler.(shop.FWEAccountShopService).CreateSettleContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceCreateSettleContractArgs() interface{} {
	return shop.NewFWEAccountShopServiceCreateSettleContractArgs()
}

func newFWEAccountShopServiceCreateSettleContractResult() interface{} {
	return shop.NewFWEAccountShopServiceCreateSettleContractResult()
}

func querySettleContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceQuerySettleContractArgs)
	realResult := result.(*shop.FWEAccountShopServiceQuerySettleContractResult)
	success, err := handler.(shop.FWEAccountShopService).QuerySettleContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceQuerySettleContractArgs() interface{} {
	return shop.NewFWEAccountShopServiceQuerySettleContractArgs()
}

func newFWEAccountShopServiceQuerySettleContractResult() interface{} {
	return shop.NewFWEAccountShopServiceQuerySettleContractResult()
}

func previewSettleContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServicePreviewSettleContractArgs)
	realResult := result.(*shop.FWEAccountShopServicePreviewSettleContractResult)
	success, err := handler.(shop.FWEAccountShopService).PreviewSettleContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServicePreviewSettleContractArgs() interface{} {
	return shop.NewFWEAccountShopServicePreviewSettleContractArgs()
}

func newFWEAccountShopServicePreviewSettleContractResult() interface{} {
	return shop.NewFWEAccountShopServicePreviewSettleContractResult()
}

func notifySettleContractHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceNotifySettleContractArgs)
	realResult := result.(*shop.FWEAccountShopServiceNotifySettleContractResult)
	success, err := handler.(shop.FWEAccountShopService).NotifySettleContract(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceNotifySettleContractArgs() interface{} {
	return shop.NewFWEAccountShopServiceNotifySettleContractArgs()
}

func newFWEAccountShopServiceNotifySettleContractResult() interface{} {
	return shop.NewFWEAccountShopServiceNotifySettleContractResult()
}

func mGetCgAccountHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceMGetCgAccountArgs)
	realResult := result.(*shop.FWEAccountShopServiceMGetCgAccountResult)
	success, err := handler.(shop.FWEAccountShopService).MGetCgAccount(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceMGetCgAccountArgs() interface{} {
	return shop.NewFWEAccountShopServiceMGetCgAccountArgs()
}

func newFWEAccountShopServiceMGetCgAccountResult() interface{} {
	return shop.NewFWEAccountShopServiceMGetCgAccountResult()
}

func fetchPlatformURLHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceFetchPlatformURLArgs)
	realResult := result.(*shop.FWEAccountShopServiceFetchPlatformURLResult)
	success, err := handler.(shop.FWEAccountShopService).FetchPlatformURL(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceFetchPlatformURLArgs() interface{} {
	return shop.NewFWEAccountShopServiceFetchPlatformURLArgs()
}

func newFWEAccountShopServiceFetchPlatformURLResult() interface{} {
	return shop.NewFWEAccountShopServiceFetchPlatformURLResult()
}

func fetchAccountCenterURLHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceFetchAccountCenterURLArgs)
	realResult := result.(*shop.FWEAccountShopServiceFetchAccountCenterURLResult)
	success, err := handler.(shop.FWEAccountShopService).FetchAccountCenterURL(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceFetchAccountCenterURLArgs() interface{} {
	return shop.NewFWEAccountShopServiceFetchAccountCenterURLArgs()
}

func newFWEAccountShopServiceFetchAccountCenterURLResult() interface{} {
	return shop.NewFWEAccountShopServiceFetchAccountCenterURLResult()
}

func createFweSubMerchantHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceCreateFweSubMerchantArgs)
	realResult := result.(*shop.FWEAccountShopServiceCreateFweSubMerchantResult)
	success, err := handler.(shop.FWEAccountShopService).CreateFweSubMerchant(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceCreateFweSubMerchantArgs() interface{} {
	return shop.NewFWEAccountShopServiceCreateFweSubMerchantArgs()
}

func newFWEAccountShopServiceCreateFweSubMerchantResult() interface{} {
	return shop.NewFWEAccountShopServiceCreateFweSubMerchantResult()
}

func modifyFweSubMerchantInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceModifyFweSubMerchantInfoArgs)
	realResult := result.(*shop.FWEAccountShopServiceModifyFweSubMerchantInfoResult)
	success, err := handler.(shop.FWEAccountShopService).ModifyFweSubMerchantInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceModifyFweSubMerchantInfoArgs() interface{} {
	return shop.NewFWEAccountShopServiceModifyFweSubMerchantInfoArgs()
}

func newFWEAccountShopServiceModifyFweSubMerchantInfoResult() interface{} {
	return shop.NewFWEAccountShopServiceModifyFweSubMerchantInfoResult()
}

func syncFinanceAccountReqHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceSyncFinanceAccountReqArgs)
	realResult := result.(*shop.FWEAccountShopServiceSyncFinanceAccountReqResult)
	success, err := handler.(shop.FWEAccountShopService).SyncFinanceAccountReq(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceSyncFinanceAccountReqArgs() interface{} {
	return shop.NewFWEAccountShopServiceSyncFinanceAccountReqArgs()
}

func newFWEAccountShopServiceSyncFinanceAccountReqResult() interface{} {
	return shop.NewFWEAccountShopServiceSyncFinanceAccountReqResult()
}

func mGetFinanceAccountHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceMGetFinanceAccountArgs)
	realResult := result.(*shop.FWEAccountShopServiceMGetFinanceAccountResult)
	success, err := handler.(shop.FWEAccountShopService).MGetFinanceAccount(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceMGetFinanceAccountArgs() interface{} {
	return shop.NewFWEAccountShopServiceMGetFinanceAccountArgs()
}

func newFWEAccountShopServiceMGetFinanceAccountResult() interface{} {
	return shop.NewFWEAccountShopServiceMGetFinanceAccountResult()
}

func getAllAccountsByMerchantIDHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetAllAccountsByMerchantIDArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetAllAccountsByMerchantIDResult)
	success, err := handler.(shop.FWEAccountShopService).GetAllAccountsByMerchantID(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetAllAccountsByMerchantIDArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetAllAccountsByMerchantIDArgs()
}

func newFWEAccountShopServiceGetAllAccountsByMerchantIDResult() interface{} {
	return shop.NewFWEAccountShopServiceGetAllAccountsByMerchantIDResult()
}

func createOrUpdateBzjAccountHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceCreateOrUpdateBzjAccountArgs)
	realResult := result.(*shop.FWEAccountShopServiceCreateOrUpdateBzjAccountResult)
	success, err := handler.(shop.FWEAccountShopService).CreateOrUpdateBzjAccount(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceCreateOrUpdateBzjAccountArgs() interface{} {
	return shop.NewFWEAccountShopServiceCreateOrUpdateBzjAccountArgs()
}

func newFWEAccountShopServiceCreateOrUpdateBzjAccountResult() interface{} {
	return shop.NewFWEAccountShopServiceCreateOrUpdateBzjAccountResult()
}

func queryBzjAccountHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceQueryBzjAccountArgs)
	realResult := result.(*shop.FWEAccountShopServiceQueryBzjAccountResult)
	success, err := handler.(shop.FWEAccountShopService).QueryBzjAccount(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceQueryBzjAccountArgs() interface{} {
	return shop.NewFWEAccountShopServiceQueryBzjAccountArgs()
}

func newFWEAccountShopServiceQueryBzjAccountResult() interface{} {
	return shop.NewFWEAccountShopServiceQueryBzjAccountResult()
}

func listShopRecipientHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceListShopRecipientArgs)
	realResult := result.(*shop.FWEAccountShopServiceListShopRecipientResult)
	success, err := handler.(shop.FWEAccountShopService).ListShopRecipient(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceListShopRecipientArgs() interface{} {
	return shop.NewFWEAccountShopServiceListShopRecipientArgs()
}

func newFWEAccountShopServiceListShopRecipientResult() interface{} {
	return shop.NewFWEAccountShopServiceListShopRecipientResult()
}

func addShopRecipientHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceAddShopRecipientArgs)
	realResult := result.(*shop.FWEAccountShopServiceAddShopRecipientResult)
	success, err := handler.(shop.FWEAccountShopService).AddShopRecipient(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceAddShopRecipientArgs() interface{} {
	return shop.NewFWEAccountShopServiceAddShopRecipientArgs()
}

func newFWEAccountShopServiceAddShopRecipientResult() interface{} {
	return shop.NewFWEAccountShopServiceAddShopRecipientResult()
}

func updateShopRecipientHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdateShopRecipientArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdateShopRecipientResult)
	success, err := handler.(shop.FWEAccountShopService).UpdateShopRecipient(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdateShopRecipientArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopRecipientArgs()
}

func newFWEAccountShopServiceUpdateShopRecipientResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopRecipientResult()
}

func delShopRecipientHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceDelShopRecipientArgs)
	realResult := result.(*shop.FWEAccountShopServiceDelShopRecipientResult)
	success, err := handler.(shop.FWEAccountShopService).DelShopRecipient(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceDelShopRecipientArgs() interface{} {
	return shop.NewFWEAccountShopServiceDelShopRecipientArgs()
}

func newFWEAccountShopServiceDelShopRecipientResult() interface{} {
	return shop.NewFWEAccountShopServiceDelShopRecipientResult()
}

func unionNotifySubMerchantCreateHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUnionNotifySubMerchantCreateArgs)
	realResult := result.(*shop.FWEAccountShopServiceUnionNotifySubMerchantCreateResult)
	success, err := handler.(shop.FWEAccountShopService).UnionNotifySubMerchantCreate(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUnionNotifySubMerchantCreateArgs() interface{} {
	return shop.NewFWEAccountShopServiceUnionNotifySubMerchantCreateArgs()
}

func newFWEAccountShopServiceUnionNotifySubMerchantCreateResult() interface{} {
	return shop.NewFWEAccountShopServiceUnionNotifySubMerchantCreateResult()
}

func querySubMerchantInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceQuerySubMerchantInfoArgs)
	realResult := result.(*shop.FWEAccountShopServiceQuerySubMerchantInfoResult)
	success, err := handler.(shop.FWEAccountShopService).QuerySubMerchantInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceQuerySubMerchantInfoArgs() interface{} {
	return shop.NewFWEAccountShopServiceQuerySubMerchantInfoArgs()
}

func newFWEAccountShopServiceQuerySubMerchantInfoResult() interface{} {
	return shop.NewFWEAccountShopServiceQuerySubMerchantInfoResult()
}

func mGetSubjectQualificationHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceMGetSubjectQualificationArgs)
	realResult := result.(*shop.FWEAccountShopServiceMGetSubjectQualificationResult)
	success, err := handler.(shop.FWEAccountShopService).MGetSubjectQualification(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceMGetSubjectQualificationArgs() interface{} {
	return shop.NewFWEAccountShopServiceMGetSubjectQualificationArgs()
}

func newFWEAccountShopServiceMGetSubjectQualificationResult() interface{} {
	return shop.NewFWEAccountShopServiceMGetSubjectQualificationResult()
}

func queryFweSubMerchantListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceQueryFweSubMerchantListArgs)
	realResult := result.(*shop.FWEAccountShopServiceQueryFweSubMerchantListResult)
	success, err := handler.(shop.FWEAccountShopService).QueryFweSubMerchantList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceQueryFweSubMerchantListArgs() interface{} {
	return shop.NewFWEAccountShopServiceQueryFweSubMerchantListArgs()
}

func newFWEAccountShopServiceQueryFweSubMerchantListResult() interface{} {
	return shop.NewFWEAccountShopServiceQueryFweSubMerchantListResult()
}

func getUserIDWithSessionIDHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetUserIDWithSessionIDArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetUserIDWithSessionIDResult)
	success, err := handler.(shop.FWEAccountShopService).GetUserIDWithSessionID(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetUserIDWithSessionIDArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetUserIDWithSessionIDArgs()
}

func newFWEAccountShopServiceGetUserIDWithSessionIDResult() interface{} {
	return shop.NewFWEAccountShopServiceGetUserIDWithSessionIDResult()
}

func getShopSubjectHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetShopSubjectArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetShopSubjectResult)
	success, err := handler.(shop.FWEAccountShopService).GetShopSubject(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetShopSubjectArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetShopSubjectArgs()
}

func newFWEAccountShopServiceGetShopSubjectResult() interface{} {
	return shop.NewFWEAccountShopServiceGetShopSubjectResult()
}

func getTradeSubjectHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetTradeSubjectArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetTradeSubjectResult)
	success, err := handler.(shop.FWEAccountShopService).GetTradeSubject(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetTradeSubjectArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetTradeSubjectArgs()
}

func newFWEAccountShopServiceGetTradeSubjectResult() interface{} {
	return shop.NewFWEAccountShopServiceGetTradeSubjectResult()
}

func mGetTradeSubjectByIdsHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceMGetTradeSubjectByIdsArgs)
	realResult := result.(*shop.FWEAccountShopServiceMGetTradeSubjectByIdsResult)
	success, err := handler.(shop.FWEAccountShopService).MGetTradeSubjectByIds(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceMGetTradeSubjectByIdsArgs() interface{} {
	return shop.NewFWEAccountShopServiceMGetTradeSubjectByIdsArgs()
}

func newFWEAccountShopServiceMGetTradeSubjectByIdsResult() interface{} {
	return shop.NewFWEAccountShopServiceMGetTradeSubjectByIdsResult()
}

func queryCompanyRiskHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceQueryCompanyRiskArgs)
	realResult := result.(*shop.FWEAccountShopServiceQueryCompanyRiskResult)
	success, err := handler.(shop.FWEAccountShopService).QueryCompanyRisk(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceQueryCompanyRiskArgs() interface{} {
	return shop.NewFWEAccountShopServiceQueryCompanyRiskArgs()
}

func newFWEAccountShopServiceQueryCompanyRiskResult() interface{} {
	return shop.NewFWEAccountShopServiceQueryCompanyRiskResult()
}

func checkSubjectCreateShopSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceCheckSubjectCreateShopSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceCheckSubjectCreateShopSettleResult)
	success, err := handler.(shop.FWEAccountShopService).CheckSubjectCreateShopSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceCheckSubjectCreateShopSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceCheckSubjectCreateShopSettleArgs()
}

func newFWEAccountShopServiceCheckSubjectCreateShopSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceCheckSubjectCreateShopSettleResult()
}

func checkSettleFlowCreateSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceCheckSettleFlowCreateSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceCheckSettleFlowCreateSettleResult)
	success, err := handler.(shop.FWEAccountShopService).CheckSettleFlowCreateSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceCheckSettleFlowCreateSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceCheckSettleFlowCreateSettleArgs()
}

func newFWEAccountShopServiceCheckSettleFlowCreateSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceCheckSettleFlowCreateSettleResult()
}

func getShopSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetShopSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetShopSettleResult)
	success, err := handler.(shop.FWEAccountShopService).GetShopSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetShopSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetShopSettleArgs()
}

func newFWEAccountShopServiceGetShopSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceGetShopSettleResult()
}

func getProductSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetProductSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetProductSettleResult)
	success, err := handler.(shop.FWEAccountShopService).GetProductSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetProductSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetProductSettleArgs()
}

func newFWEAccountShopServiceGetProductSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceGetProductSettleResult()
}

func updateShopSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdateShopSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdateShopSettleResult)
	success, err := handler.(shop.FWEAccountShopService).UpdateShopSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdateShopSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopSettleArgs()
}

func newFWEAccountShopServiceUpdateShopSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopSettleResult()
}

func actionSubjectEventHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceActionSubjectEventArgs)
	realResult := result.(*shop.FWEAccountShopServiceActionSubjectEventResult)
	success, err := handler.(shop.FWEAccountShopService).ActionSubjectEvent(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceActionSubjectEventArgs() interface{} {
	return shop.NewFWEAccountShopServiceActionSubjectEventArgs()
}

func newFWEAccountShopServiceActionSubjectEventResult() interface{} {
	return shop.NewFWEAccountShopServiceActionSubjectEventResult()
}

func getShopSettleListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetShopSettleListArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetShopSettleListResult)
	success, err := handler.(shop.FWEAccountShopService).GetShopSettleList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetShopSettleListArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetShopSettleListArgs()
}

func newFWEAccountShopServiceGetShopSettleListResult() interface{} {
	return shop.NewFWEAccountShopServiceGetShopSettleListResult()
}

func updateShopSettleStatusHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdateShopSettleStatusArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdateShopSettleStatusResult)
	success, err := handler.(shop.FWEAccountShopService).UpdateShopSettleStatus(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdateShopSettleStatusArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopSettleStatusArgs()
}

func newFWEAccountShopServiceUpdateShopSettleStatusResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopSettleStatusResult()
}

func updatePersonSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdatePersonSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdatePersonSettleResult)
	success, err := handler.(shop.FWEAccountShopService).UpdatePersonSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdatePersonSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdatePersonSettleArgs()
}

func newFWEAccountShopServiceUpdatePersonSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdatePersonSettleResult()
}

func listTenantSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceListTenantSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceListTenantSettleResult)
	success, err := handler.(shop.FWEAccountShopService).ListTenantSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceListTenantSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceListTenantSettleArgs()
}

func newFWEAccountShopServiceListTenantSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceListTenantSettleResult()
}

func listProductSettleHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceListProductSettleArgs)
	realResult := result.(*shop.FWEAccountShopServiceListProductSettleResult)
	success, err := handler.(shop.FWEAccountShopService).ListProductSettle(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceListProductSettleArgs() interface{} {
	return shop.NewFWEAccountShopServiceListProductSettleArgs()
}

func newFWEAccountShopServiceListProductSettleResult() interface{} {
	return shop.NewFWEAccountShopServiceListProductSettleResult()
}

func submitProductSettleBDHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceSubmitProductSettleBDArgs)
	realResult := result.(*shop.FWEAccountShopServiceSubmitProductSettleBDResult)
	success, err := handler.(shop.FWEAccountShopService).SubmitProductSettleBD(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceSubmitProductSettleBDArgs() interface{} {
	return shop.NewFWEAccountShopServiceSubmitProductSettleBDArgs()
}

func newFWEAccountShopServiceSubmitProductSettleBDResult() interface{} {
	return shop.NewFWEAccountShopServiceSubmitProductSettleBDResult()
}

func updateFinanceAccountHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdateFinanceAccountArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdateFinanceAccountResult)
	success, err := handler.(shop.FWEAccountShopService).UpdateFinanceAccount(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdateFinanceAccountArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdateFinanceAccountArgs()
}

func newFWEAccountShopServiceUpdateFinanceAccountResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdateFinanceAccountResult()
}

func getFinanceAccountHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetFinanceAccountArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetFinanceAccountResult)
	success, err := handler.(shop.FWEAccountShopService).GetFinanceAccount(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetFinanceAccountArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetFinanceAccountArgs()
}

func newFWEAccountShopServiceGetFinanceAccountResult() interface{} {
	return shop.NewFWEAccountShopServiceGetFinanceAccountResult()
}

func getSettleFlowListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetSettleFlowListArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetSettleFlowListResult)
	success, err := handler.(shop.FWEAccountShopService).GetSettleFlowList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetSettleFlowListArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetSettleFlowListArgs()
}

func newFWEAccountShopServiceGetSettleFlowListResult() interface{} {
	return shop.NewFWEAccountShopServiceGetSettleFlowListResult()
}

func checkWithCreateSettleFlowHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceCheckWithCreateSettleFlowArgs)
	realResult := result.(*shop.FWEAccountShopServiceCheckWithCreateSettleFlowResult)
	success, err := handler.(shop.FWEAccountShopService).CheckWithCreateSettleFlow(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceCheckWithCreateSettleFlowArgs() interface{} {
	return shop.NewFWEAccountShopServiceCheckWithCreateSettleFlowArgs()
}

func newFWEAccountShopServiceCheckWithCreateSettleFlowResult() interface{} {
	return shop.NewFWEAccountShopServiceCheckWithCreateSettleFlowResult()
}

func getSettleFlowHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetSettleFlowArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetSettleFlowResult)
	success, err := handler.(shop.FWEAccountShopService).GetSettleFlow(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetSettleFlowArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetSettleFlowArgs()
}

func newFWEAccountShopServiceGetSettleFlowResult() interface{} {
	return shop.NewFWEAccountShopServiceGetSettleFlowResult()
}

func updateSettleFlowHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdateSettleFlowArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdateSettleFlowResult)
	success, err := handler.(shop.FWEAccountShopService).UpdateSettleFlow(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdateSettleFlowArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdateSettleFlowArgs()
}

func newFWEAccountShopServiceUpdateSettleFlowResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdateSettleFlowResult()
}

func getShopListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetShopListArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetShopListResult)
	success, err := handler.(shop.FWEAccountShopService).GetShopList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetShopListArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetShopListArgs()
}

func newFWEAccountShopServiceGetShopListResult() interface{} {
	return shop.NewFWEAccountShopServiceGetShopListResult()
}

func mGetSubjectHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceMGetSubjectArgs)
	realResult := result.(*shop.FWEAccountShopServiceMGetSubjectResult)
	success, err := handler.(shop.FWEAccountShopService).MGetSubject(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceMGetSubjectArgs() interface{} {
	return shop.NewFWEAccountShopServiceMGetSubjectArgs()
}

func newFWEAccountShopServiceMGetSubjectResult() interface{} {
	return shop.NewFWEAccountShopServiceMGetSubjectResult()
}

func getShopMMMAccountCustomerHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetShopMMMAccountCustomerArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetShopMMMAccountCustomerResult)
	success, err := handler.(shop.FWEAccountShopService).GetShopMMMAccountCustomer(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetShopMMMAccountCustomerArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetShopMMMAccountCustomerArgs()
}

func newFWEAccountShopServiceGetShopMMMAccountCustomerResult() interface{} {
	return shop.NewFWEAccountShopServiceGetShopMMMAccountCustomerResult()
}

func getSettleShopBySubjectHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceGetSettleShopBySubjectArgs)
	realResult := result.(*shop.FWEAccountShopServiceGetSettleShopBySubjectResult)
	success, err := handler.(shop.FWEAccountShopService).GetSettleShopBySubject(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceGetSettleShopBySubjectArgs() interface{} {
	return shop.NewFWEAccountShopServiceGetSettleShopBySubjectArgs()
}

func newFWEAccountShopServiceGetSettleShopBySubjectResult() interface{} {
	return shop.NewFWEAccountShopServiceGetSettleShopBySubjectResult()
}

func updateShopInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUpdateShopInfoArgs)
	realResult := result.(*shop.FWEAccountShopServiceUpdateShopInfoResult)
	success, err := handler.(shop.FWEAccountShopService).UpdateShopInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUpdateShopInfoArgs() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopInfoArgs()
}

func newFWEAccountShopServiceUpdateShopInfoResult() interface{} {
	return shop.NewFWEAccountShopServiceUpdateShopInfoResult()
}

func mGetShopDetailHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceMGetShopDetailArgs)
	realResult := result.(*shop.FWEAccountShopServiceMGetShopDetailResult)
	success, err := handler.(shop.FWEAccountShopService).MGetShopDetail(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceMGetShopDetailArgs() interface{} {
	return shop.NewFWEAccountShopServiceMGetShopDetailArgs()
}

func newFWEAccountShopServiceMGetShopDetailResult() interface{} {
	return shop.NewFWEAccountShopServiceMGetShopDetailResult()
}

func searchShopHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceSearchShopArgs)
	realResult := result.(*shop.FWEAccountShopServiceSearchShopResult)
	success, err := handler.(shop.FWEAccountShopService).SearchShop(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceSearchShopArgs() interface{} {
	return shop.NewFWEAccountShopServiceSearchShopArgs()
}

func newFWEAccountShopServiceSearchShopResult() interface{} {
	return shop.NewFWEAccountShopServiceSearchShopResult()
}

func deleteShopHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceDeleteShopArgs)
	realResult := result.(*shop.FWEAccountShopServiceDeleteShopResult)
	success, err := handler.(shop.FWEAccountShopService).DeleteShop(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceDeleteShopArgs() interface{} {
	return shop.NewFWEAccountShopServiceDeleteShopArgs()
}

func newFWEAccountShopServiceDeleteShopResult() interface{} {
	return shop.NewFWEAccountShopServiceDeleteShopResult()
}

func addShopSubjectHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceAddShopSubjectArgs)
	realResult := result.(*shop.FWEAccountShopServiceAddShopSubjectResult)
	success, err := handler.(shop.FWEAccountShopService).AddShopSubject(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceAddShopSubjectArgs() interface{} {
	return shop.NewFWEAccountShopServiceAddShopSubjectArgs()
}

func newFWEAccountShopServiceAddShopSubjectResult() interface{} {
	return shop.NewFWEAccountShopServiceAddShopSubjectResult()
}

func querySubjectHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceQuerySubjectArgs)
	realResult := result.(*shop.FWEAccountShopServiceQuerySubjectResult)
	success, err := handler.(shop.FWEAccountShopService).QuerySubject(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceQuerySubjectArgs() interface{} {
	return shop.NewFWEAccountShopServiceQuerySubjectArgs()
}

func newFWEAccountShopServiceQuerySubjectResult() interface{} {
	return shop.NewFWEAccountShopServiceQuerySubjectResult()
}

func syncSupplyShopHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceSyncSupplyShopArgs)
	realResult := result.(*shop.FWEAccountShopServiceSyncSupplyShopResult)
	success, err := handler.(shop.FWEAccountShopService).SyncSupplyShop(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceSyncSupplyShopArgs() interface{} {
	return shop.NewFWEAccountShopServiceSyncSupplyShopArgs()
}

func newFWEAccountShopServiceSyncSupplyShopResult() interface{} {
	return shop.NewFWEAccountShopServiceSyncSupplyShopResult()
}

func listShopByQyhOpenIDHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceListShopByQyhOpenIDArgs)
	realResult := result.(*shop.FWEAccountShopServiceListShopByQyhOpenIDResult)
	success, err := handler.(shop.FWEAccountShopService).ListShopByQyhOpenID(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceListShopByQyhOpenIDArgs() interface{} {
	return shop.NewFWEAccountShopServiceListShopByQyhOpenIDArgs()
}

func newFWEAccountShopServiceListShopByQyhOpenIDResult() interface{} {
	return shop.NewFWEAccountShopServiceListShopByQyhOpenIDResult()
}

func bindShopQyhOpenIDHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceBindShopQyhOpenIDArgs)
	realResult := result.(*shop.FWEAccountShopServiceBindShopQyhOpenIDResult)
	success, err := handler.(shop.FWEAccountShopService).BindShopQyhOpenID(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceBindShopQyhOpenIDArgs() interface{} {
	return shop.NewFWEAccountShopServiceBindShopQyhOpenIDArgs()
}

func newFWEAccountShopServiceBindShopQyhOpenIDResult() interface{} {
	return shop.NewFWEAccountShopServiceBindShopQyhOpenIDResult()
}

func unbindShopQyhOpenIDHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*shop.FWEAccountShopServiceUnbindShopQyhOpenIDArgs)
	realResult := result.(*shop.FWEAccountShopServiceUnbindShopQyhOpenIDResult)
	success, err := handler.(shop.FWEAccountShopService).UnbindShopQyhOpenID(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFWEAccountShopServiceUnbindShopQyhOpenIDArgs() interface{} {
	return shop.NewFWEAccountShopServiceUnbindShopQyhOpenIDArgs()
}

func newFWEAccountShopServiceUnbindShopQyhOpenIDResult() interface{} {
	return shop.NewFWEAccountShopServiceUnbindShopQyhOpenIDResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) CreateShop(ctx context.Context, req *shop.CreateShopReq) (r *shop.CreateShopResp, err error) {
	var _args shop.FWEAccountShopServiceCreateShopArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceCreateShopResult
	if err = p.c.Call(ctx, "CreateShop", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateShop(ctx context.Context, req *shop.UpdateShopReq) (r *shop.UpdateShopResp, err error) {
	var _args shop.FWEAccountShopServiceUpdateShopArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdateShopResult
	if err = p.c.Call(ctx, "UpdateShop", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateShopStatus(ctx context.Context, req *shop.UpdateShopStatusReq) (r *shop.UpdateShopStatusResp, err error) {
	var _args shop.FWEAccountShopServiceUpdateShopStatusArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdateShopStatusResult
	if err = p.c.Call(ctx, "UpdateShopStatus", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetShop(ctx context.Context, req *shop.MGetShopReq) (r *shop.MGetShopResp, err error) {
	var _args shop.FWEAccountShopServiceMGetShopArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceMGetShopResult
	if err = p.c.Call(ctx, "MGetShop", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ChangeOwner(ctx context.Context, req *shop.ChangeOwnerReq) (r *shop.ChangeOwnerResp, err error) {
	var _args shop.FWEAccountShopServiceChangeOwnerArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceChangeOwnerResult
	if err = p.c.Call(ctx, "ChangeOwner", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SettingChildren(ctx context.Context, req *shop.SettingChildrenReq) (r *shop.SettingChildrenResp, err error) {
	var _args shop.FWEAccountShopServiceSettingChildrenArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceSettingChildrenResult
	if err = p.c.Call(ctx, "SettingChildren", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ListChildren(ctx context.Context, req *shop.ListChildrenReq) (r *shop.ListChildrenResp, err error) {
	var _args shop.FWEAccountShopServiceListChildrenArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceListChildrenResult
	if err = p.c.Call(ctx, "ListChildren", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SubmitShopSettle(ctx context.Context, req *shop.SubmitShopSettleReq) (r *shop.SubmitShopSettleResp, err error) {
	var _args shop.FWEAccountShopServiceSubmitShopSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceSubmitShopSettleResult
	if err = p.c.Call(ctx, "SubmitShopSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DetailShopSettle(ctx context.Context, req *shop.DetailShopSettleReq) (r *shop.DetailShopSettleResp, err error) {
	var _args shop.FWEAccountShopServiceDetailShopSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceDetailShopSettleResult
	if err = p.c.Call(ctx, "DetailShopSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AuditShopSettle(ctx context.Context, req *shop.AuditShopSettleReq) (r *shop.AuditShopSettleResp, err error) {
	var _args shop.FWEAccountShopServiceAuditShopSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceAuditShopSettleResult
	if err = p.c.Call(ctx, "AuditShopSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ActionShopSettleNode(ctx context.Context, req *shop.ActionShopSettleNodeReq) (r *shop.ActionShopSettleNodeResp, err error) {
	var _args shop.FWEAccountShopServiceActionShopSettleNodeArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceActionShopSettleNodeResult
	if err = p.c.Call(ctx, "ActionShopSettleNode", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ListShopSettle(ctx context.Context, req *shop.ListShopSettleReq) (r *shop.ListShopSettleResp, err error) {
	var _args shop.FWEAccountShopServiceListShopSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceListShopSettleResult
	if err = p.c.Call(ctx, "ListShopSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DeleteShopSettle(ctx context.Context, req *shop.DeleteShopSettleReq) (r *shop.DeleteShopSettleResp, err error) {
	var _args shop.FWEAccountShopServiceDeleteShopSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceDeleteShopSettleResult
	if err = p.c.Call(ctx, "DeleteShopSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DeleteProductSettle(ctx context.Context, req *shop.DeleteProductSettleReq) (r *shop.DeleteProductSettleResp, err error) {
	var _args shop.FWEAccountShopServiceDeleteProductSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceDeleteProductSettleResult
	if err = p.c.Call(ctx, "DeleteProductSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateShopSettleLevel(ctx context.Context, req *shop.UpdateShopSettleLevelReq) (r *shop.UpdateShopSettleLevelResp, err error) {
	var _args shop.FWEAccountShopServiceUpdateShopSettleLevelArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdateShopSettleLevelResult
	if err = p.c.Call(ctx, "UpdateShopSettleLevel", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateShopSettleInfo(ctx context.Context, req *shop.UpdateShopSettleInfoReq) (r *shop.UpdateShopSettleInfoResp, err error) {
	var _args shop.FWEAccountShopServiceUpdateShopSettleInfoArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdateShopSettleInfoResult
	if err = p.c.Call(ctx, "UpdateShopSettleInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateSettleContract(ctx context.Context, req *shop.CreateSettleContractReq) (r *shop.CreateSettleContractResp, err error) {
	var _args shop.FWEAccountShopServiceCreateSettleContractArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceCreateSettleContractResult
	if err = p.c.Call(ctx, "CreateSettleContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QuerySettleContract(ctx context.Context, req *shop.QuerySettleContractReq) (r *shop.QuerySettleContractResp, err error) {
	var _args shop.FWEAccountShopServiceQuerySettleContractArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceQuerySettleContractResult
	if err = p.c.Call(ctx, "QuerySettleContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) PreviewSettleContract(ctx context.Context, req *shop.PreviewSettleContractReq) (r *shop.PreviewSettleContractResp, err error) {
	var _args shop.FWEAccountShopServicePreviewSettleContractArgs
	_args.Req = req
	var _result shop.FWEAccountShopServicePreviewSettleContractResult
	if err = p.c.Call(ctx, "PreviewSettleContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) NotifySettleContract(ctx context.Context, req *shop.NotifySettleContractReq) (r *shop.NotifySettleContractResp, err error) {
	var _args shop.FWEAccountShopServiceNotifySettleContractArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceNotifySettleContractResult
	if err = p.c.Call(ctx, "NotifySettleContract", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetCgAccount(ctx context.Context, req *shop.MGetCgAccountReq) (r *shop.MGetCgAccountResp, err error) {
	var _args shop.FWEAccountShopServiceMGetCgAccountArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceMGetCgAccountResult
	if err = p.c.Call(ctx, "MGetCgAccount", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FetchPlatformURL(ctx context.Context, req *shop.FetchPlatformURLReq) (r *shop.FetchPlatformURLResp, err error) {
	var _args shop.FWEAccountShopServiceFetchPlatformURLArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceFetchPlatformURLResult
	if err = p.c.Call(ctx, "FetchPlatformUrl", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FetchAccountCenterURL(ctx context.Context, req *shop.FetchAccountCenterURLReq) (r *shop.FetchAccountCenterURLResp, err error) {
	var _args shop.FWEAccountShopServiceFetchAccountCenterURLArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceFetchAccountCenterURLResult
	if err = p.c.Call(ctx, "FetchAccountCenterUrl", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateFweSubMerchant(ctx context.Context, req *shop.CreateFweSubMerchantReq) (r *shop.FweSubMerchantResp, err error) {
	var _args shop.FWEAccountShopServiceCreateFweSubMerchantArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceCreateFweSubMerchantResult
	if err = p.c.Call(ctx, "CreateFweSubMerchant", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ModifyFweSubMerchantInfo(ctx context.Context, req *shop.ModifyFweSubMerchantReq) (r *shop.FweSubMerchantResp, err error) {
	var _args shop.FWEAccountShopServiceModifyFweSubMerchantInfoArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceModifyFweSubMerchantInfoResult
	if err = p.c.Call(ctx, "ModifyFweSubMerchantInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SyncFinanceAccountReq(ctx context.Context, req *shop.SyncFinanceAccountReq) (r *shop.SyncFinanceAccountResp, err error) {
	var _args shop.FWEAccountShopServiceSyncFinanceAccountReqArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceSyncFinanceAccountReqResult
	if err = p.c.Call(ctx, "SyncFinanceAccountReq", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetFinanceAccount(ctx context.Context, req *shop.MGetFinanceAccountReq) (r *shop.MGetFinanceAccountResp, err error) {
	var _args shop.FWEAccountShopServiceMGetFinanceAccountArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceMGetFinanceAccountResult
	if err = p.c.Call(ctx, "MGetFinanceAccount", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetAllAccountsByMerchantID(ctx context.Context, req *shop.GetAllAccountsByMerchantIDReq) (r *shop.GetAllAccountsByMerchantIDResp, err error) {
	var _args shop.FWEAccountShopServiceGetAllAccountsByMerchantIDArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetAllAccountsByMerchantIDResult
	if err = p.c.Call(ctx, "GetAllAccountsByMerchantID", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateOrUpdateBzjAccount(ctx context.Context, req *shop.CreateOrUpdateBzjAccountReq) (r *shop.CreateOrUpdateBzjAccountResp, err error) {
	var _args shop.FWEAccountShopServiceCreateOrUpdateBzjAccountArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceCreateOrUpdateBzjAccountResult
	if err = p.c.Call(ctx, "CreateOrUpdateBzjAccount", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryBzjAccount(ctx context.Context, req *shop.QueryBzjAccountReq) (r *shop.QueryBzjAccountResp, err error) {
	var _args shop.FWEAccountShopServiceQueryBzjAccountArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceQueryBzjAccountResult
	if err = p.c.Call(ctx, "QueryBzjAccount", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ListShopRecipient(ctx context.Context, req *shop.ListShopRecipientReq) (r *shop.ListShopRecipientResp, err error) {
	var _args shop.FWEAccountShopServiceListShopRecipientArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceListShopRecipientResult
	if err = p.c.Call(ctx, "ListShopRecipient", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AddShopRecipient(ctx context.Context, req *shop.AddShopRecipientReq) (r *shop.AddShopRecipientResp, err error) {
	var _args shop.FWEAccountShopServiceAddShopRecipientArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceAddShopRecipientResult
	if err = p.c.Call(ctx, "AddShopRecipient", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateShopRecipient(ctx context.Context, req *shop.UpdateShopRecipientReq) (r *shop.UpdateShopRecipientResp, err error) {
	var _args shop.FWEAccountShopServiceUpdateShopRecipientArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdateShopRecipientResult
	if err = p.c.Call(ctx, "UpdateShopRecipient", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DelShopRecipient(ctx context.Context, req *shop.DelShopDecipientReq) (r *shop.DelShopRecipientResp, err error) {
	var _args shop.FWEAccountShopServiceDelShopRecipientArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceDelShopRecipientResult
	if err = p.c.Call(ctx, "DelShopRecipient", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnionNotifySubMerchantCreate(ctx context.Context, req *shop.UnionNotifyRequest) (r *shop.UnionNotifyResponse, err error) {
	var _args shop.FWEAccountShopServiceUnionNotifySubMerchantCreateArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUnionNotifySubMerchantCreateResult
	if err = p.c.Call(ctx, "UnionNotifySubMerchantCreate", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QuerySubMerchantInfo(ctx context.Context, req *shop.QuerySubMerchantInfoReq) (r *shop.QuerySubMerchantInfoResp, err error) {
	var _args shop.FWEAccountShopServiceQuerySubMerchantInfoArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceQuerySubMerchantInfoResult
	if err = p.c.Call(ctx, "QuerySubMerchantInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetSubjectQualification(ctx context.Context, req *shop.MGetSubjectQualificationReq) (r *shop.MGetSubjectQualificationResp, err error) {
	var _args shop.FWEAccountShopServiceMGetSubjectQualificationArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceMGetSubjectQualificationResult
	if err = p.c.Call(ctx, "MGetSubjectQualification", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryFweSubMerchantList(ctx context.Context, req *shop.QueryFweSubMerchantListReq) (r *shop.QueryFweSubMerchantListResp, err error) {
	var _args shop.FWEAccountShopServiceQueryFweSubMerchantListArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceQueryFweSubMerchantListResult
	if err = p.c.Call(ctx, "QueryFweSubMerchantList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetUserIDWithSessionID(ctx context.Context, req *shop.GetUserIDWithSessionIDReq) (r *shop.GetUserIDWithSessionIDResp, err error) {
	var _args shop.FWEAccountShopServiceGetUserIDWithSessionIDArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetUserIDWithSessionIDResult
	if err = p.c.Call(ctx, "GetUserIdWithSessionId", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetShopSubject(ctx context.Context, req *shop.GetShopSubjectReq) (r *shop.GetShopSubjectResp, err error) {
	var _args shop.FWEAccountShopServiceGetShopSubjectArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetShopSubjectResult
	if err = p.c.Call(ctx, "GetShopSubject", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetTradeSubject(ctx context.Context, req *shop.GetTradeSubjectReq) (r *shop.GetTradeSubjectResp, err error) {
	var _args shop.FWEAccountShopServiceGetTradeSubjectArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetTradeSubjectResult
	if err = p.c.Call(ctx, "GetTradeSubject", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetTradeSubjectByIds(ctx context.Context, req *shop.MGetTradeSubjectByIdsReq) (r *shop.MGetTradeSubjectByIdsResp, err error) {
	var _args shop.FWEAccountShopServiceMGetTradeSubjectByIdsArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceMGetTradeSubjectByIdsResult
	if err = p.c.Call(ctx, "MGetTradeSubjectByIds", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryCompanyRisk(ctx context.Context, req *shop.QueryCompanyRiskReq) (r *shop.QueryCompanyRiskResp, err error) {
	var _args shop.FWEAccountShopServiceQueryCompanyRiskArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceQueryCompanyRiskResult
	if err = p.c.Call(ctx, "QueryCompanyRisk", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CheckSubjectCreateShopSettle(ctx context.Context, req *shop.CheckSubjectCreateShopSettleReq) (r *shop.CheckSubjectCreateShopSettleResp, err error) {
	var _args shop.FWEAccountShopServiceCheckSubjectCreateShopSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceCheckSubjectCreateShopSettleResult
	if err = p.c.Call(ctx, "CheckSubjectCreateShopSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CheckSettleFlowCreateSettle(ctx context.Context, req *shop.CheckSettleFlowCreateSettleReq) (r *shop.CheckSettleFlowCreateSettleResp, err error) {
	var _args shop.FWEAccountShopServiceCheckSettleFlowCreateSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceCheckSettleFlowCreateSettleResult
	if err = p.c.Call(ctx, "CheckSettleFlowCreateSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetShopSettle(ctx context.Context, req *shop.GetShopSettleReq) (r *shop.GetShopSettleResp, err error) {
	var _args shop.FWEAccountShopServiceGetShopSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetShopSettleResult
	if err = p.c.Call(ctx, "GetShopSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetProductSettle(ctx context.Context, req *shop.GetProductSettleReq) (r *shop.GetProductSettleResp, err error) {
	var _args shop.FWEAccountShopServiceGetProductSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetProductSettleResult
	if err = p.c.Call(ctx, "GetProductSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateShopSettle(ctx context.Context, req *shop.UpdateShopSettleReq) (r *shop.UpdateShopSettleResp, err error) {
	var _args shop.FWEAccountShopServiceUpdateShopSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdateShopSettleResult
	if err = p.c.Call(ctx, "UpdateShopSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ActionSubjectEvent(ctx context.Context, req *shop.ActionSubjectEventReq) (r *shop.ActionSubjectEventResp, err error) {
	var _args shop.FWEAccountShopServiceActionSubjectEventArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceActionSubjectEventResult
	if err = p.c.Call(ctx, "ActionSubjectEvent", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetShopSettleList(ctx context.Context, req *shop.GetShopSettleListReq) (r *shop.GetShopSettleListResp, err error) {
	var _args shop.FWEAccountShopServiceGetShopSettleListArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetShopSettleListResult
	if err = p.c.Call(ctx, "GetShopSettleList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateShopSettleStatus(ctx context.Context, req *shop.UpdateShopSettleStatusReq) (r *shop.UpdateShopSettleStatusResp, err error) {
	var _args shop.FWEAccountShopServiceUpdateShopSettleStatusArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdateShopSettleStatusResult
	if err = p.c.Call(ctx, "UpdateShopSettleStatus", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdatePersonSettle(ctx context.Context, req *shop.UpdatePersonSettleReq) (r *shop.UpdatePersonSettleResp, err error) {
	var _args shop.FWEAccountShopServiceUpdatePersonSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdatePersonSettleResult
	if err = p.c.Call(ctx, "UpdatePersonSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ListTenantSettle(ctx context.Context, req *shop.ListTenantSettleReq) (r *shop.ListTenantSettleResp, err error) {
	var _args shop.FWEAccountShopServiceListTenantSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceListTenantSettleResult
	if err = p.c.Call(ctx, "ListTenantSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ListProductSettle(ctx context.Context, req *shop.ListProductSettleReq) (r *shop.ListProductSettleResp, err error) {
	var _args shop.FWEAccountShopServiceListProductSettleArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceListProductSettleResult
	if err = p.c.Call(ctx, "ListProductSettle", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SubmitProductSettleBD(ctx context.Context, req *shop.SubmitProductSettleBDReq) (r *shop.SubmitProductSettleBDResp, err error) {
	var _args shop.FWEAccountShopServiceSubmitProductSettleBDArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceSubmitProductSettleBDResult
	if err = p.c.Call(ctx, "SubmitProductSettleBD", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateFinanceAccount(ctx context.Context, req *shop.UpdateFinanceAccountReq) (r *shop.UpdateFinanceAccountResp, err error) {
	var _args shop.FWEAccountShopServiceUpdateFinanceAccountArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdateFinanceAccountResult
	if err = p.c.Call(ctx, "UpdateFinanceAccount", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetFinanceAccount(ctx context.Context, req *shop.GetFinanceAccountReq) (r *shop.GetFinanceAccountResp, err error) {
	var _args shop.FWEAccountShopServiceGetFinanceAccountArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetFinanceAccountResult
	if err = p.c.Call(ctx, "GetFinanceAccount", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetSettleFlowList(ctx context.Context, req *shop.GetSettleFlowListReq) (r *shop.GetSettleFlowListResp, err error) {
	var _args shop.FWEAccountShopServiceGetSettleFlowListArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetSettleFlowListResult
	if err = p.c.Call(ctx, "GetSettleFlowList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CheckWithCreateSettleFlow(ctx context.Context, req *shop.CheckWithCreateSettleFlowReq) (r *shop.CheckWithCreateSettleFlowResp, err error) {
	var _args shop.FWEAccountShopServiceCheckWithCreateSettleFlowArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceCheckWithCreateSettleFlowResult
	if err = p.c.Call(ctx, "CheckWithCreateSettleFlow", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetSettleFlow(ctx context.Context, req *shop.GetSettleFlowReq) (r *shop.GetSettleFlowResp, err error) {
	var _args shop.FWEAccountShopServiceGetSettleFlowArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetSettleFlowResult
	if err = p.c.Call(ctx, "GetSettleFlow", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateSettleFlow(ctx context.Context, req *shop.UpdateSettleFlowReq) (r *shop.UpdateSettleFlowResp, err error) {
	var _args shop.FWEAccountShopServiceUpdateSettleFlowArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdateSettleFlowResult
	if err = p.c.Call(ctx, "UpdateSettleFlow", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetShopList(ctx context.Context, req *shop.GetShopListReq) (r *shop.GetShopListResp, err error) {
	var _args shop.FWEAccountShopServiceGetShopListArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetShopListResult
	if err = p.c.Call(ctx, "GetShopList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetSubject(ctx context.Context, req *shop.MGetSubjectReq) (r *shop.MGetSubjectResp, err error) {
	var _args shop.FWEAccountShopServiceMGetSubjectArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceMGetSubjectResult
	if err = p.c.Call(ctx, "MGetSubject", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetShopMMMAccountCustomer(ctx context.Context, req *shop.GetShopMMMAccountCustomerReq) (r *shop.GetShopMMMAccountCustomerResp, err error) {
	var _args shop.FWEAccountShopServiceGetShopMMMAccountCustomerArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetShopMMMAccountCustomerResult
	if err = p.c.Call(ctx, "GetShopMMMAccountCustomer", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetSettleShopBySubject(ctx context.Context, req *shop.GetSettleShopBySubjectReq) (r *shop.GetSettleShopBySubjectResp, err error) {
	var _args shop.FWEAccountShopServiceGetSettleShopBySubjectArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceGetSettleShopBySubjectResult
	if err = p.c.Call(ctx, "GetSettleShopBySubject", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateShopInfo(ctx context.Context, req *shop.UpdateShopInfoReq) (r *shop.UpdateShopInfoResp, err error) {
	var _args shop.FWEAccountShopServiceUpdateShopInfoArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUpdateShopInfoResult
	if err = p.c.Call(ctx, "UpdateShopInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetShopDetail(ctx context.Context, req *shop.MGetShopDetailReq) (r *shop.MGetShopDetailResp, err error) {
	var _args shop.FWEAccountShopServiceMGetShopDetailArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceMGetShopDetailResult
	if err = p.c.Call(ctx, "MGetShopDetail", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SearchShop(ctx context.Context, req *shop.SearchShopReq) (r *shop.SearchShopResp, err error) {
	var _args shop.FWEAccountShopServiceSearchShopArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceSearchShopResult
	if err = p.c.Call(ctx, "SearchShop", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DeleteShop(ctx context.Context, req *shop.DeleteShopReq) (r *shop.DeleteShopResp, err error) {
	var _args shop.FWEAccountShopServiceDeleteShopArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceDeleteShopResult
	if err = p.c.Call(ctx, "DeleteShop", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) AddShopSubject(ctx context.Context, req *shop.AddShopSubjectReq) (r *shop.AddShopSubjectResp, err error) {
	var _args shop.FWEAccountShopServiceAddShopSubjectArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceAddShopSubjectResult
	if err = p.c.Call(ctx, "AddShopSubject", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QuerySubject(ctx context.Context, req *shop.QuerySubjectReq) (r *shop.QuerySubjectResp, err error) {
	var _args shop.FWEAccountShopServiceQuerySubjectArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceQuerySubjectResult
	if err = p.c.Call(ctx, "QuerySubject", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SyncSupplyShop(ctx context.Context, req *shop.SyncSupplyShopReq) (r *shop.SyncSupplyShopRsp, err error) {
	var _args shop.FWEAccountShopServiceSyncSupplyShopArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceSyncSupplyShopResult
	if err = p.c.Call(ctx, "SyncSupplyShop", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ListShopByQyhOpenID(ctx context.Context, req *shop.ListShopByQyhOpenIDReq) (r *shop.ListShopByQyhOpenIDResp, err error) {
	var _args shop.FWEAccountShopServiceListShopByQyhOpenIDArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceListShopByQyhOpenIDResult
	if err = p.c.Call(ctx, "ListShopByQyhOpenID", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) BindShopQyhOpenID(ctx context.Context, req *shop.BindShopQyhOpenIDReq) (r *shop.BindShopQyhOpenIDResp, err error) {
	var _args shop.FWEAccountShopServiceBindShopQyhOpenIDArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceBindShopQyhOpenIDResult
	if err = p.c.Call(ctx, "BindShopQyhOpenID", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnbindShopQyhOpenID(ctx context.Context, req *shop.UnbindShopQyhOpenIDReq) (r *shop.UnbindShopQyhOpenIDResp, err error) {
	var _args shop.FWEAccountShopServiceUnbindShopQyhOpenIDArgs
	_args.Req = req
	var _result shop.FWEAccountShopServiceUnbindShopQyhOpenIDResult
	if err = p.c.Call(ctx, "UnbindShopQyhOpenID", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
