// Code generated by Kitex v1.20.3. DO NOT EDIT.

package invoiceservice

import (
	client "code.byted.org/kite/kitex/client"
	core "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_invoice/core"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"QueryInvoiceEnums": kitex.NewMethodInfo(
		queryInvoiceEnumsHandler,
		newInvoiceServiceQueryInvoiceEnumsArgs,
		newInvoiceServiceQueryInvoiceEnumsResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"InvoiceOrderPush": kitex.NewMethodInfo(
		invoiceOrderPushHandler,
		newInvoiceServiceInvoiceOrderPushArgs,
		newInvoiceServiceInvoiceOrderPushResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryInvoiceOrderList": kitex.NewMethodInfo(
		queryInvoiceOrderListHandler,
		newInvoiceServiceQueryInvoiceOrderListArgs,
		newInvoiceServiceQueryInvoiceOrderListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryAccountBaseInfo": kitex.NewMethodInfo(
		queryAccountBaseInfoHandler,
		newInvoiceServiceQueryAccountBaseInfoArgs,
		newInvoiceServiceQueryAccountBaseInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryAccountInvoiceInfo": kitex.NewMethodInfo(
		queryAccountInvoiceInfoHandler,
		newInvoiceServiceQueryAccountInvoiceInfoArgs,
		newInvoiceServiceQueryAccountInvoiceInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"SubmitAccountInvoiceInfo": kitex.NewMethodInfo(
		submitAccountInvoiceInfoHandler,
		newInvoiceServiceSubmitAccountInvoiceInfoArgs,
		newInvoiceServiceSubmitAccountInvoiceInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryMineCompanyInfo": kitex.NewMethodInfo(
		queryMineCompanyInfoHandler,
		newInvoiceServiceQueryMineCompanyInfoArgs,
		newInvoiceServiceQueryMineCompanyInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryMineCompanyList": kitex.NewMethodInfo(
		queryMineCompanyListHandler,
		newInvoiceServiceQueryMineCompanyListArgs,
		newInvoiceServiceQueryMineCompanyListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MergeInvoice": kitex.NewMethodInfo(
		mergeInvoiceHandler,
		newInvoiceServiceMergeInvoiceArgs,
		newInvoiceServiceMergeInvoiceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryInvoiceRecordList": kitex.NewMethodInfo(
		queryInvoiceRecordListHandler,
		newInvoiceServiceQueryInvoiceRecordListArgs,
		newInvoiceServiceQueryInvoiceRecordListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryInvoiceRecordDetail": kitex.NewMethodInfo(
		queryInvoiceRecordDetailHandler,
		newInvoiceServiceQueryInvoiceRecordDetailArgs,
		newInvoiceServiceQueryInvoiceRecordDetailResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryInvoiceRecordOrderList": kitex.NewMethodInfo(
		queryInvoiceRecordOrderListHandler,
		newInvoiceServiceQueryInvoiceRecordOrderListArgs,
		newInvoiceServiceQueryInvoiceRecordOrderListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FillInvoiceDeliveryInfo": kitex.NewMethodInfo(
		fillInvoiceDeliveryInfoHandler,
		newInvoiceServiceFillInvoiceDeliveryInfoArgs,
		newInvoiceServiceFillInvoiceDeliveryInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ReInvoice": kitex.NewMethodInfo(
		reInvoiceHandler,
		newInvoiceServiceReInvoiceArgs,
		newInvoiceServiceReInvoiceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FetchInvoicePlatformUrl": kitex.NewMethodInfo(
		fetchInvoicePlatformURLHandler,
		newInvoiceServiceFetchInvoicePlatformURLArgs,
		newInvoiceServiceFetchInvoicePlatformURLResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetInvoiceTokenInfo": kitex.NewMethodInfo(
		getInvoiceTokenInfoHandler,
		newInvoiceServiceGetInvoiceTokenInfoArgs,
		newInvoiceServiceGetInvoiceTokenInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Invoice": kitex.NewMethodInfo(
		invoiceHandler,
		newInvoiceServiceInvoiceArgs,
		newInvoiceServiceInvoiceResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"InvoiceReverse": kitex.NewMethodInfo(
		invoiceReverseHandler,
		newInvoiceServiceInvoiceReverseArgs,
		newInvoiceServiceInvoiceReverseResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"DownloadInvoiceFile": kitex.NewMethodInfo(
		downloadInvoiceFileHandler,
		newInvoiceServiceDownloadInvoiceFileArgs,
		newInvoiceServiceDownloadInvoiceFileResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetAllOrderInfo": kitex.NewMethodInfo(
		getAllOrderInfoHandler,
		newInvoiceServiceGetAllOrderInfoArgs,
		newInvoiceServiceGetAllOrderInfoResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FillManualInvoiceUrl": kitex.NewMethodInfo(
		fillManualInvoiceURLHandler,
		newInvoiceServiceFillManualInvoiceURLArgs,
		newInvoiceServiceFillManualInvoiceURLResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	invoiceServiceServiceInfo                = NewServiceInfo()
	invoiceServiceServiceInfoForClient       = NewServiceInfoForClient()
	invoiceServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return invoiceServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return invoiceServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return invoiceServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "InvoiceService"
	handlerType := (*core.InvoiceService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "core",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func queryInvoiceEnumsHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceQueryInvoiceEnumsArgs)
	realResult := result.(*core.InvoiceServiceQueryInvoiceEnumsResult)
	success, err := handler.(core.InvoiceService).QueryInvoiceEnums(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceQueryInvoiceEnumsArgs() interface{} {
	return core.NewInvoiceServiceQueryInvoiceEnumsArgs()
}

func newInvoiceServiceQueryInvoiceEnumsResult() interface{} {
	return core.NewInvoiceServiceQueryInvoiceEnumsResult()
}

func invoiceOrderPushHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceInvoiceOrderPushArgs)
	realResult := result.(*core.InvoiceServiceInvoiceOrderPushResult)
	success, err := handler.(core.InvoiceService).InvoiceOrderPush(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceInvoiceOrderPushArgs() interface{} {
	return core.NewInvoiceServiceInvoiceOrderPushArgs()
}

func newInvoiceServiceInvoiceOrderPushResult() interface{} {
	return core.NewInvoiceServiceInvoiceOrderPushResult()
}

func queryInvoiceOrderListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceQueryInvoiceOrderListArgs)
	realResult := result.(*core.InvoiceServiceQueryInvoiceOrderListResult)
	success, err := handler.(core.InvoiceService).QueryInvoiceOrderList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceQueryInvoiceOrderListArgs() interface{} {
	return core.NewInvoiceServiceQueryInvoiceOrderListArgs()
}

func newInvoiceServiceQueryInvoiceOrderListResult() interface{} {
	return core.NewInvoiceServiceQueryInvoiceOrderListResult()
}

func queryAccountBaseInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceQueryAccountBaseInfoArgs)
	realResult := result.(*core.InvoiceServiceQueryAccountBaseInfoResult)
	success, err := handler.(core.InvoiceService).QueryAccountBaseInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceQueryAccountBaseInfoArgs() interface{} {
	return core.NewInvoiceServiceQueryAccountBaseInfoArgs()
}

func newInvoiceServiceQueryAccountBaseInfoResult() interface{} {
	return core.NewInvoiceServiceQueryAccountBaseInfoResult()
}

func queryAccountInvoiceInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceQueryAccountInvoiceInfoArgs)
	realResult := result.(*core.InvoiceServiceQueryAccountInvoiceInfoResult)
	success, err := handler.(core.InvoiceService).QueryAccountInvoiceInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceQueryAccountInvoiceInfoArgs() interface{} {
	return core.NewInvoiceServiceQueryAccountInvoiceInfoArgs()
}

func newInvoiceServiceQueryAccountInvoiceInfoResult() interface{} {
	return core.NewInvoiceServiceQueryAccountInvoiceInfoResult()
}

func submitAccountInvoiceInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceSubmitAccountInvoiceInfoArgs)
	realResult := result.(*core.InvoiceServiceSubmitAccountInvoiceInfoResult)
	success, err := handler.(core.InvoiceService).SubmitAccountInvoiceInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceSubmitAccountInvoiceInfoArgs() interface{} {
	return core.NewInvoiceServiceSubmitAccountInvoiceInfoArgs()
}

func newInvoiceServiceSubmitAccountInvoiceInfoResult() interface{} {
	return core.NewInvoiceServiceSubmitAccountInvoiceInfoResult()
}

func queryMineCompanyInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceQueryMineCompanyInfoArgs)
	realResult := result.(*core.InvoiceServiceQueryMineCompanyInfoResult)
	success, err := handler.(core.InvoiceService).QueryMineCompanyInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceQueryMineCompanyInfoArgs() interface{} {
	return core.NewInvoiceServiceQueryMineCompanyInfoArgs()
}

func newInvoiceServiceQueryMineCompanyInfoResult() interface{} {
	return core.NewInvoiceServiceQueryMineCompanyInfoResult()
}

func queryMineCompanyListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceQueryMineCompanyListArgs)
	realResult := result.(*core.InvoiceServiceQueryMineCompanyListResult)
	success, err := handler.(core.InvoiceService).QueryMineCompanyList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceQueryMineCompanyListArgs() interface{} {
	return core.NewInvoiceServiceQueryMineCompanyListArgs()
}

func newInvoiceServiceQueryMineCompanyListResult() interface{} {
	return core.NewInvoiceServiceQueryMineCompanyListResult()
}

func mergeInvoiceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceMergeInvoiceArgs)
	realResult := result.(*core.InvoiceServiceMergeInvoiceResult)
	success, err := handler.(core.InvoiceService).MergeInvoice(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceMergeInvoiceArgs() interface{} {
	return core.NewInvoiceServiceMergeInvoiceArgs()
}

func newInvoiceServiceMergeInvoiceResult() interface{} {
	return core.NewInvoiceServiceMergeInvoiceResult()
}

func queryInvoiceRecordListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceQueryInvoiceRecordListArgs)
	realResult := result.(*core.InvoiceServiceQueryInvoiceRecordListResult)
	success, err := handler.(core.InvoiceService).QueryInvoiceRecordList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceQueryInvoiceRecordListArgs() interface{} {
	return core.NewInvoiceServiceQueryInvoiceRecordListArgs()
}

func newInvoiceServiceQueryInvoiceRecordListResult() interface{} {
	return core.NewInvoiceServiceQueryInvoiceRecordListResult()
}

func queryInvoiceRecordDetailHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceQueryInvoiceRecordDetailArgs)
	realResult := result.(*core.InvoiceServiceQueryInvoiceRecordDetailResult)
	success, err := handler.(core.InvoiceService).QueryInvoiceRecordDetail(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceQueryInvoiceRecordDetailArgs() interface{} {
	return core.NewInvoiceServiceQueryInvoiceRecordDetailArgs()
}

func newInvoiceServiceQueryInvoiceRecordDetailResult() interface{} {
	return core.NewInvoiceServiceQueryInvoiceRecordDetailResult()
}

func queryInvoiceRecordOrderListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceQueryInvoiceRecordOrderListArgs)
	realResult := result.(*core.InvoiceServiceQueryInvoiceRecordOrderListResult)
	success, err := handler.(core.InvoiceService).QueryInvoiceRecordOrderList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceQueryInvoiceRecordOrderListArgs() interface{} {
	return core.NewInvoiceServiceQueryInvoiceRecordOrderListArgs()
}

func newInvoiceServiceQueryInvoiceRecordOrderListResult() interface{} {
	return core.NewInvoiceServiceQueryInvoiceRecordOrderListResult()
}

func fillInvoiceDeliveryInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceFillInvoiceDeliveryInfoArgs)
	realResult := result.(*core.InvoiceServiceFillInvoiceDeliveryInfoResult)
	success, err := handler.(core.InvoiceService).FillInvoiceDeliveryInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceFillInvoiceDeliveryInfoArgs() interface{} {
	return core.NewInvoiceServiceFillInvoiceDeliveryInfoArgs()
}

func newInvoiceServiceFillInvoiceDeliveryInfoResult() interface{} {
	return core.NewInvoiceServiceFillInvoiceDeliveryInfoResult()
}

func reInvoiceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceReInvoiceArgs)
	realResult := result.(*core.InvoiceServiceReInvoiceResult)
	success, err := handler.(core.InvoiceService).ReInvoice(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceReInvoiceArgs() interface{} {
	return core.NewInvoiceServiceReInvoiceArgs()
}

func newInvoiceServiceReInvoiceResult() interface{} {
	return core.NewInvoiceServiceReInvoiceResult()
}

func fetchInvoicePlatformURLHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceFetchInvoicePlatformURLArgs)
	realResult := result.(*core.InvoiceServiceFetchInvoicePlatformURLResult)
	success, err := handler.(core.InvoiceService).FetchInvoicePlatformURL(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceFetchInvoicePlatformURLArgs() interface{} {
	return core.NewInvoiceServiceFetchInvoicePlatformURLArgs()
}

func newInvoiceServiceFetchInvoicePlatformURLResult() interface{} {
	return core.NewInvoiceServiceFetchInvoicePlatformURLResult()
}

func getInvoiceTokenInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceGetInvoiceTokenInfoArgs)
	realResult := result.(*core.InvoiceServiceGetInvoiceTokenInfoResult)
	success, err := handler.(core.InvoiceService).GetInvoiceTokenInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceGetInvoiceTokenInfoArgs() interface{} {
	return core.NewInvoiceServiceGetInvoiceTokenInfoArgs()
}

func newInvoiceServiceGetInvoiceTokenInfoResult() interface{} {
	return core.NewInvoiceServiceGetInvoiceTokenInfoResult()
}

func invoiceHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceInvoiceArgs)
	realResult := result.(*core.InvoiceServiceInvoiceResult)
	success, err := handler.(core.InvoiceService).Invoice(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceInvoiceArgs() interface{} {
	return core.NewInvoiceServiceInvoiceArgs()
}

func newInvoiceServiceInvoiceResult() interface{} {
	return core.NewInvoiceServiceInvoiceResult()
}

func invoiceReverseHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceInvoiceReverseArgs)
	realResult := result.(*core.InvoiceServiceInvoiceReverseResult)
	success, err := handler.(core.InvoiceService).InvoiceReverse(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceInvoiceReverseArgs() interface{} {
	return core.NewInvoiceServiceInvoiceReverseArgs()
}

func newInvoiceServiceInvoiceReverseResult() interface{} {
	return core.NewInvoiceServiceInvoiceReverseResult()
}

func downloadInvoiceFileHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceDownloadInvoiceFileArgs)
	realResult := result.(*core.InvoiceServiceDownloadInvoiceFileResult)
	success, err := handler.(core.InvoiceService).DownloadInvoiceFile(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceDownloadInvoiceFileArgs() interface{} {
	return core.NewInvoiceServiceDownloadInvoiceFileArgs()
}

func newInvoiceServiceDownloadInvoiceFileResult() interface{} {
	return core.NewInvoiceServiceDownloadInvoiceFileResult()
}

func getAllOrderInfoHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceGetAllOrderInfoArgs)
	realResult := result.(*core.InvoiceServiceGetAllOrderInfoResult)
	success, err := handler.(core.InvoiceService).GetAllOrderInfo(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceGetAllOrderInfoArgs() interface{} {
	return core.NewInvoiceServiceGetAllOrderInfoArgs()
}

func newInvoiceServiceGetAllOrderInfoResult() interface{} {
	return core.NewInvoiceServiceGetAllOrderInfoResult()
}

func fillManualInvoiceURLHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*core.InvoiceServiceFillManualInvoiceURLArgs)
	realResult := result.(*core.InvoiceServiceFillManualInvoiceURLResult)
	success, err := handler.(core.InvoiceService).FillManualInvoiceURL(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newInvoiceServiceFillManualInvoiceURLArgs() interface{} {
	return core.NewInvoiceServiceFillManualInvoiceURLArgs()
}

func newInvoiceServiceFillManualInvoiceURLResult() interface{} {
	return core.NewInvoiceServiceFillManualInvoiceURLResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) QueryInvoiceEnums(ctx context.Context, req *core.QueryInvoiceEnumsReq) (r *core.QueryInvoiceEnumsResp, err error) {
	var _args core.InvoiceServiceQueryInvoiceEnumsArgs
	_args.Req = req
	var _result core.InvoiceServiceQueryInvoiceEnumsResult
	if err = p.c.Call(ctx, "QueryInvoiceEnums", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) InvoiceOrderPush(ctx context.Context, req *core.InvoiceOrderPushReq) (r *core.InvoiceOrderPushResp, err error) {
	var _args core.InvoiceServiceInvoiceOrderPushArgs
	_args.Req = req
	var _result core.InvoiceServiceInvoiceOrderPushResult
	if err = p.c.Call(ctx, "InvoiceOrderPush", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryInvoiceOrderList(ctx context.Context, req *core.QueryInvoiceOrderListReq) (r *core.QueryInvoiceOrderListResp, err error) {
	var _args core.InvoiceServiceQueryInvoiceOrderListArgs
	_args.Req = req
	var _result core.InvoiceServiceQueryInvoiceOrderListResult
	if err = p.c.Call(ctx, "QueryInvoiceOrderList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryAccountBaseInfo(ctx context.Context, req *core.QueryAccountBaseInfoReq) (r *core.QueryAccountBaseInfoResp, err error) {
	var _args core.InvoiceServiceQueryAccountBaseInfoArgs
	_args.Req = req
	var _result core.InvoiceServiceQueryAccountBaseInfoResult
	if err = p.c.Call(ctx, "QueryAccountBaseInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryAccountInvoiceInfo(ctx context.Context, req *core.QueryAccountInvoiceInfoReq) (r *core.QueryAccountInvoiceInfoResp, err error) {
	var _args core.InvoiceServiceQueryAccountInvoiceInfoArgs
	_args.Req = req
	var _result core.InvoiceServiceQueryAccountInvoiceInfoResult
	if err = p.c.Call(ctx, "QueryAccountInvoiceInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) SubmitAccountInvoiceInfo(ctx context.Context, req *core.SubmitAccountInvoiceInfoReq) (r *core.SubmitAccountInvoiceInfoResp, err error) {
	var _args core.InvoiceServiceSubmitAccountInvoiceInfoArgs
	_args.Req = req
	var _result core.InvoiceServiceSubmitAccountInvoiceInfoResult
	if err = p.c.Call(ctx, "SubmitAccountInvoiceInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryMineCompanyInfo(ctx context.Context, req *core.QueryMineCompanyInfoReq) (r *core.QueryMineCompanyInfoResp, err error) {
	var _args core.InvoiceServiceQueryMineCompanyInfoArgs
	_args.Req = req
	var _result core.InvoiceServiceQueryMineCompanyInfoResult
	if err = p.c.Call(ctx, "QueryMineCompanyInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryMineCompanyList(ctx context.Context, req *core.QueryMineCompanyListReq) (r *core.QueryMineCompanyListResp, err error) {
	var _args core.InvoiceServiceQueryMineCompanyListArgs
	_args.Req = req
	var _result core.InvoiceServiceQueryMineCompanyListResult
	if err = p.c.Call(ctx, "QueryMineCompanyList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MergeInvoice(ctx context.Context, req *core.MergeInvoiceReq) (r *core.MergeInvoiceResp, err error) {
	var _args core.InvoiceServiceMergeInvoiceArgs
	_args.Req = req
	var _result core.InvoiceServiceMergeInvoiceResult
	if err = p.c.Call(ctx, "MergeInvoice", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryInvoiceRecordList(ctx context.Context, req *core.QueryInvoiceRecordListReq) (r *core.QueryInvoiceRecordListResp, err error) {
	var _args core.InvoiceServiceQueryInvoiceRecordListArgs
	_args.Req = req
	var _result core.InvoiceServiceQueryInvoiceRecordListResult
	if err = p.c.Call(ctx, "QueryInvoiceRecordList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryInvoiceRecordDetail(ctx context.Context, req *core.QueryInvoiceRecordDetailReq) (r *core.QueryInvoiceRecordDetailResp, err error) {
	var _args core.InvoiceServiceQueryInvoiceRecordDetailArgs
	_args.Req = req
	var _result core.InvoiceServiceQueryInvoiceRecordDetailResult
	if err = p.c.Call(ctx, "QueryInvoiceRecordDetail", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryInvoiceRecordOrderList(ctx context.Context, req *core.QueryInvoiceRecordOrderListReq) (r *core.QueryInvoiceRecordOrderListResp, err error) {
	var _args core.InvoiceServiceQueryInvoiceRecordOrderListArgs
	_args.Req = req
	var _result core.InvoiceServiceQueryInvoiceRecordOrderListResult
	if err = p.c.Call(ctx, "QueryInvoiceRecordOrderList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FillInvoiceDeliveryInfo(ctx context.Context, req *core.FillInvoiceDeliveryInfoReq) (r *core.FillInvoiceDeliveryInfoResp, err error) {
	var _args core.InvoiceServiceFillInvoiceDeliveryInfoArgs
	_args.Req = req
	var _result core.InvoiceServiceFillInvoiceDeliveryInfoResult
	if err = p.c.Call(ctx, "FillInvoiceDeliveryInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ReInvoice(ctx context.Context, req *core.ReInvoiceReq) (r *core.ReInvoiceResp, err error) {
	var _args core.InvoiceServiceReInvoiceArgs
	_args.Req = req
	var _result core.InvoiceServiceReInvoiceResult
	if err = p.c.Call(ctx, "ReInvoice", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FetchInvoicePlatformURL(ctx context.Context, req *core.FetchInvoicePlatformURLReq) (r *core.FetchInvoicePlatformURLResp, err error) {
	var _args core.InvoiceServiceFetchInvoicePlatformURLArgs
	_args.Req = req
	var _result core.InvoiceServiceFetchInvoicePlatformURLResult
	if err = p.c.Call(ctx, "FetchInvoicePlatformUrl", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetInvoiceTokenInfo(ctx context.Context, req *core.GetInvoiceTokenInfoReq) (r *core.GetInvoiceTokenInfoResp, err error) {
	var _args core.InvoiceServiceGetInvoiceTokenInfoArgs
	_args.Req = req
	var _result core.InvoiceServiceGetInvoiceTokenInfoResult
	if err = p.c.Call(ctx, "GetInvoiceTokenInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Invoice(ctx context.Context, req *core.InvoiceReq) (r *core.InvoiceResp, err error) {
	var _args core.InvoiceServiceInvoiceArgs
	_args.Req = req
	var _result core.InvoiceServiceInvoiceResult
	if err = p.c.Call(ctx, "Invoice", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) InvoiceReverse(ctx context.Context, req *core.InvoiceReverseReq) (r *core.InvoiceReverseResp, err error) {
	var _args core.InvoiceServiceInvoiceReverseArgs
	_args.Req = req
	var _result core.InvoiceServiceInvoiceReverseResult
	if err = p.c.Call(ctx, "InvoiceReverse", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) DownloadInvoiceFile(ctx context.Context, req *core.DownloadInvoiceFileReq) (r *core.DownloadInvoiceFileResp, err error) {
	var _args core.InvoiceServiceDownloadInvoiceFileArgs
	_args.Req = req
	var _result core.InvoiceServiceDownloadInvoiceFileResult
	if err = p.c.Call(ctx, "DownloadInvoiceFile", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetAllOrderInfo(ctx context.Context, req *core.GetAllOrderInfoReq) (r *core.GetAllOrderInfoResp, err error) {
	var _args core.InvoiceServiceGetAllOrderInfoArgs
	_args.Req = req
	var _result core.InvoiceServiceGetAllOrderInfoResult
	if err = p.c.Call(ctx, "GetAllOrderInfo", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FillManualInvoiceURL(ctx context.Context, req *core.FillManualInvoiceURLReq) (r *core.FillManualInvoiceURLResp, err error) {
	var _args core.InvoiceServiceFillManualInvoiceURLArgs
	_args.Req = req
	var _result core.InvoiceServiceFillManualInvoiceURLResult
	if err = p.c.Call(ctx, "FillManualInvoiceUrl", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
