// Code generated by Kitex v1.20.3. DO NOT EDIT.
package invoiceservice

import (
	byted "code.byted.org/kite/kitex/byted"
	server "code.byted.org/kite/kitex/server"
	core "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_invoice/core"
)

// NewServer creates a server.Server with the given handler and options.
func NewServer(handler core.InvoiceService, opts ...server.Option) server.Server {
	var options []server.Option

	options = append(options, byted.ServerSuite(serviceInfo()))

	options = append(options, opts...)
	options = append(options, server.WithCompatibleMiddlewareForUnary())

	svr := server.NewServer(options...)
	if err := svr.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	return svr
}

// NewServerWithBytedConfig creates a server.Server with the given handler and options.
func NewServerWithBytedConfig(handler core.InvoiceService, config *byted.ServerConfig, opts ...server.Option) server.Server {
	var options []server.Option
	options = append(options, byted.ServerSuiteWithConfig(serviceInfo(), config))
	options = append(options, server.WithCompatibleMiddlewareForUnary())
	options = append(options, opts...)

	svr := server.NewServer(options...)
	if err := svr.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	return svr
}

func RegisterService(svr server.Server, handler core.InvoiceService, opts ...server.RegisterOption) error {
	return svr.RegisterService(serviceInfo(), handler, opts...)
}
