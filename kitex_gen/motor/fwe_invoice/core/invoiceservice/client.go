// Code generated by Kitex v1.20.3. DO NOT EDIT.

package invoiceservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	core "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_invoice/core"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	QueryInvoiceEnums(ctx context.Context, req *core.QueryInvoiceEnumsReq, callOptions ...callopt.Option) (r *core.QueryInvoiceEnumsResp, err error)
	InvoiceOrderPush(ctx context.Context, req *core.InvoiceOrderPushReq, callOptions ...callopt.Option) (r *core.InvoiceOrderPushResp, err error)
	QueryInvoiceOrderList(ctx context.Context, req *core.QueryInvoiceOrderListReq, callOptions ...callopt.Option) (r *core.QueryInvoiceOrderListResp, err error)
	QueryAccountBaseInfo(ctx context.Context, req *core.QueryAccountBaseInfoReq, callOptions ...callopt.Option) (r *core.QueryAccountBaseInfoResp, err error)
	QueryAccountInvoiceInfo(ctx context.Context, req *core.QueryAccountInvoiceInfoReq, callOptions ...callopt.Option) (r *core.QueryAccountInvoiceInfoResp, err error)
	SubmitAccountInvoiceInfo(ctx context.Context, req *core.SubmitAccountInvoiceInfoReq, callOptions ...callopt.Option) (r *core.SubmitAccountInvoiceInfoResp, err error)
	QueryMineCompanyInfo(ctx context.Context, req *core.QueryMineCompanyInfoReq, callOptions ...callopt.Option) (r *core.QueryMineCompanyInfoResp, err error)
	QueryMineCompanyList(ctx context.Context, req *core.QueryMineCompanyListReq, callOptions ...callopt.Option) (r *core.QueryMineCompanyListResp, err error)
	MergeInvoice(ctx context.Context, req *core.MergeInvoiceReq, callOptions ...callopt.Option) (r *core.MergeInvoiceResp, err error)
	QueryInvoiceRecordList(ctx context.Context, req *core.QueryInvoiceRecordListReq, callOptions ...callopt.Option) (r *core.QueryInvoiceRecordListResp, err error)
	QueryInvoiceRecordDetail(ctx context.Context, req *core.QueryInvoiceRecordDetailReq, callOptions ...callopt.Option) (r *core.QueryInvoiceRecordDetailResp, err error)
	QueryInvoiceRecordOrderList(ctx context.Context, req *core.QueryInvoiceRecordOrderListReq, callOptions ...callopt.Option) (r *core.QueryInvoiceRecordOrderListResp, err error)
	FillInvoiceDeliveryInfo(ctx context.Context, req *core.FillInvoiceDeliveryInfoReq, callOptions ...callopt.Option) (r *core.FillInvoiceDeliveryInfoResp, err error)
	ReInvoice(ctx context.Context, req *core.ReInvoiceReq, callOptions ...callopt.Option) (r *core.ReInvoiceResp, err error)
	FetchInvoicePlatformURL(ctx context.Context, req *core.FetchInvoicePlatformURLReq, callOptions ...callopt.Option) (r *core.FetchInvoicePlatformURLResp, err error)
	GetInvoiceTokenInfo(ctx context.Context, req *core.GetInvoiceTokenInfoReq, callOptions ...callopt.Option) (r *core.GetInvoiceTokenInfoResp, err error)
	Invoice(ctx context.Context, req *core.InvoiceReq, callOptions ...callopt.Option) (r *core.InvoiceResp, err error)
	InvoiceReverse(ctx context.Context, req *core.InvoiceReverseReq, callOptions ...callopt.Option) (r *core.InvoiceReverseResp, err error)
	DownloadInvoiceFile(ctx context.Context, req *core.DownloadInvoiceFileReq, callOptions ...callopt.Option) (r *core.DownloadInvoiceFileResp, err error)
	GetAllOrderInfo(ctx context.Context, req *core.GetAllOrderInfoReq, callOptions ...callopt.Option) (r *core.GetAllOrderInfoResp, err error)
	FillManualInvoiceURL(ctx context.Context, req *core.FillManualInvoiceURLReq, callOptions ...callopt.Option) (r *core.FillManualInvoiceURLResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kInvoiceServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kInvoiceServiceClient struct {
	*kClient
}

func (p *kInvoiceServiceClient) QueryInvoiceEnums(ctx context.Context, req *core.QueryInvoiceEnumsReq, callOptions ...callopt.Option) (r *core.QueryInvoiceEnumsResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryInvoiceEnums(ctx, req)
}

func (p *kInvoiceServiceClient) InvoiceOrderPush(ctx context.Context, req *core.InvoiceOrderPushReq, callOptions ...callopt.Option) (r *core.InvoiceOrderPushResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.InvoiceOrderPush(ctx, req)
}

func (p *kInvoiceServiceClient) QueryInvoiceOrderList(ctx context.Context, req *core.QueryInvoiceOrderListReq, callOptions ...callopt.Option) (r *core.QueryInvoiceOrderListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryInvoiceOrderList(ctx, req)
}

func (p *kInvoiceServiceClient) QueryAccountBaseInfo(ctx context.Context, req *core.QueryAccountBaseInfoReq, callOptions ...callopt.Option) (r *core.QueryAccountBaseInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryAccountBaseInfo(ctx, req)
}

func (p *kInvoiceServiceClient) QueryAccountInvoiceInfo(ctx context.Context, req *core.QueryAccountInvoiceInfoReq, callOptions ...callopt.Option) (r *core.QueryAccountInvoiceInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryAccountInvoiceInfo(ctx, req)
}

func (p *kInvoiceServiceClient) SubmitAccountInvoiceInfo(ctx context.Context, req *core.SubmitAccountInvoiceInfoReq, callOptions ...callopt.Option) (r *core.SubmitAccountInvoiceInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.SubmitAccountInvoiceInfo(ctx, req)
}

func (p *kInvoiceServiceClient) QueryMineCompanyInfo(ctx context.Context, req *core.QueryMineCompanyInfoReq, callOptions ...callopt.Option) (r *core.QueryMineCompanyInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryMineCompanyInfo(ctx, req)
}

func (p *kInvoiceServiceClient) QueryMineCompanyList(ctx context.Context, req *core.QueryMineCompanyListReq, callOptions ...callopt.Option) (r *core.QueryMineCompanyListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryMineCompanyList(ctx, req)
}

func (p *kInvoiceServiceClient) MergeInvoice(ctx context.Context, req *core.MergeInvoiceReq, callOptions ...callopt.Option) (r *core.MergeInvoiceResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MergeInvoice(ctx, req)
}

func (p *kInvoiceServiceClient) QueryInvoiceRecordList(ctx context.Context, req *core.QueryInvoiceRecordListReq, callOptions ...callopt.Option) (r *core.QueryInvoiceRecordListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryInvoiceRecordList(ctx, req)
}

func (p *kInvoiceServiceClient) QueryInvoiceRecordDetail(ctx context.Context, req *core.QueryInvoiceRecordDetailReq, callOptions ...callopt.Option) (r *core.QueryInvoiceRecordDetailResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryInvoiceRecordDetail(ctx, req)
}

func (p *kInvoiceServiceClient) QueryInvoiceRecordOrderList(ctx context.Context, req *core.QueryInvoiceRecordOrderListReq, callOptions ...callopt.Option) (r *core.QueryInvoiceRecordOrderListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryInvoiceRecordOrderList(ctx, req)
}

func (p *kInvoiceServiceClient) FillInvoiceDeliveryInfo(ctx context.Context, req *core.FillInvoiceDeliveryInfoReq, callOptions ...callopt.Option) (r *core.FillInvoiceDeliveryInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FillInvoiceDeliveryInfo(ctx, req)
}

func (p *kInvoiceServiceClient) ReInvoice(ctx context.Context, req *core.ReInvoiceReq, callOptions ...callopt.Option) (r *core.ReInvoiceResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ReInvoice(ctx, req)
}

func (p *kInvoiceServiceClient) FetchInvoicePlatformURL(ctx context.Context, req *core.FetchInvoicePlatformURLReq, callOptions ...callopt.Option) (r *core.FetchInvoicePlatformURLResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FetchInvoicePlatformURL(ctx, req)
}

func (p *kInvoiceServiceClient) GetInvoiceTokenInfo(ctx context.Context, req *core.GetInvoiceTokenInfoReq, callOptions ...callopt.Option) (r *core.GetInvoiceTokenInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetInvoiceTokenInfo(ctx, req)
}

func (p *kInvoiceServiceClient) Invoice(ctx context.Context, req *core.InvoiceReq, callOptions ...callopt.Option) (r *core.InvoiceResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Invoice(ctx, req)
}

func (p *kInvoiceServiceClient) InvoiceReverse(ctx context.Context, req *core.InvoiceReverseReq, callOptions ...callopt.Option) (r *core.InvoiceReverseResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.InvoiceReverse(ctx, req)
}

func (p *kInvoiceServiceClient) DownloadInvoiceFile(ctx context.Context, req *core.DownloadInvoiceFileReq, callOptions ...callopt.Option) (r *core.DownloadInvoiceFileResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.DownloadInvoiceFile(ctx, req)
}

func (p *kInvoiceServiceClient) GetAllOrderInfo(ctx context.Context, req *core.GetAllOrderInfoReq, callOptions ...callopt.Option) (r *core.GetAllOrderInfoResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetAllOrderInfo(ctx, req)
}

func (p *kInvoiceServiceClient) FillManualInvoiceURL(ctx context.Context, req *core.FillManualInvoiceURLReq, callOptions ...callopt.Option) (r *core.FillManualInvoiceURLResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FillManualInvoiceURL(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kInvoiceServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
