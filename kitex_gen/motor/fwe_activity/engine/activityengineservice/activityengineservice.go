// Code generated by Kitex v1.20.3. DO NOT EDIT.

package activityengineservice

import (
	client "code.byted.org/kite/kitex/client"
	engine "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_activity/engine"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"CreateActivity": kitex.NewMethodInfo(
		createActivityHandler,
		newActivityEngineServiceCreateActivityArgs,
		newActivityEngineServiceCreateActivityResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateActivity": kitex.NewMethodInfo(
		updateActivityHandler,
		newActivityEngineServiceUpdateActivityArgs,
		newActivityEngineServiceUpdateActivityResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateCoupon": kitex.NewMethodInfo(
		createCouponHandler,
		newActivityEngineServiceCreateCouponArgs,
		newActivityEngineServiceCreateCouponResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateCoupon": kitex.NewMethodInfo(
		updateCouponHandler,
		newActivityEngineServiceUpdateCouponArgs,
		newActivityEngineServiceUpdateCouponResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"OpCoupon": kitex.NewMethodInfo(
		opCouponHandler,
		newActivityEngineServiceOpCouponArgs,
		newActivityEngineServiceOpCouponResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateOrder": kitex.NewMethodInfo(
		createOrderHandler,
		newActivityEngineServiceCreateOrderArgs,
		newActivityEngineServiceCreateOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"FreezeOrder": kitex.NewMethodInfo(
		freezeOrderHandler,
		newActivityEngineServiceFreezeOrderArgs,
		newActivityEngineServiceFreezeOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UnfreezeOrder": kitex.NewMethodInfo(
		unfreezeOrderHandler,
		newActivityEngineServiceUnfreezeOrderArgs,
		newActivityEngineServiceUnfreezeOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ApproveOrder": kitex.NewMethodInfo(
		approveOrderHandler,
		newActivityEngineServiceApproveOrderArgs,
		newActivityEngineServiceApproveOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CancelOrder": kitex.NewMethodInfo(
		cancelOrderHandler,
		newActivityEngineServiceCancelOrderArgs,
		newActivityEngineServiceCancelOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ActionOrder": kitex.NewMethodInfo(
		actionOrderHandler,
		newActivityEngineServiceActionOrderArgs,
		newActivityEngineServiceActionOrderResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ClaimCoupon": kitex.NewMethodInfo(
		claimCouponHandler,
		newActivityEngineServiceClaimCouponArgs,
		newActivityEngineServiceClaimCouponResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ConfigEnum": kitex.NewMethodInfo(
		configEnumHandler,
		newActivityEngineServiceConfigEnumArgs,
		newActivityEngineServiceConfigEnumResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryCouponB": kitex.NewMethodInfo(
		queryCouponBHandler,
		newActivityEngineServiceQueryCouponBArgs,
		newActivityEngineServiceQueryCouponBResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetCoupon": kitex.NewMethodInfo(
		mGetCouponHandler,
		newActivityEngineServiceMGetCouponArgs,
		newActivityEngineServiceMGetCouponResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetCouponSnapshot": kitex.NewMethodInfo(
		mGetCouponSnapshotHandler,
		newActivityEngineServiceMGetCouponSnapshotArgs,
		newActivityEngineServiceMGetCouponSnapshotResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryCouponC": kitex.NewMethodInfo(
		queryCouponCHandler,
		newActivityEngineServiceQueryCouponCArgs,
		newActivityEngineServiceQueryCouponCResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryCouponLogList": kitex.NewMethodInfo(
		queryCouponLogListHandler,
		newActivityEngineServiceQueryCouponLogListArgs,
		newActivityEngineServiceQueryCouponLogListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryActivityB": kitex.NewMethodInfo(
		queryActivityBHandler,
		newActivityEngineServiceQueryActivityBArgs,
		newActivityEngineServiceQueryActivityBResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryOrderC": kitex.NewMethodInfo(
		queryOrderCHandler,
		newActivityEngineServiceQueryOrderCArgs,
		newActivityEngineServiceQueryOrderCResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryOrderB": kitex.NewMethodInfo(
		queryOrderBHandler,
		newActivityEngineServiceQueryOrderBArgs,
		newActivityEngineServiceQueryOrderBResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateMarketingActivity": kitex.NewMethodInfo(
		createMarketingActivityHandler,
		newActivityEngineServiceCreateMarketingActivityArgs,
		newActivityEngineServiceCreateMarketingActivityResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateMarketingActivity": kitex.NewMethodInfo(
		updateMarketingActivityHandler,
		newActivityEngineServiceUpdateMarketingActivityArgs,
		newActivityEngineServiceUpdateMarketingActivityResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"OnlineMarketingActivity": kitex.NewMethodInfo(
		onlineMarketingActivityHandler,
		newActivityEngineServiceOnlineMarketingActivityArgs,
		newActivityEngineServiceOnlineMarketingActivityResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"OfflineMarketingActivity": kitex.NewMethodInfo(
		offlineMarketingActivityHandler,
		newActivityEngineServiceOfflineMarketingActivityArgs,
		newActivityEngineServiceOfflineMarketingActivityResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryMarketingActivity": kitex.NewMethodInfo(
		queryMarketingActivityHandler,
		newActivityEngineServiceQueryMarketingActivityArgs,
		newActivityEngineServiceQueryMarketingActivityResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"OpMarketingActivitySku": kitex.NewMethodInfo(
		opMarketingActivitySkuHandler,
		newActivityEngineServiceOpMarketingActivitySkuArgs,
		newActivityEngineServiceOpMarketingActivitySkuResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryMarketingActivitySku": kitex.NewMethodInfo(
		queryMarketingActivitySkuHandler,
		newActivityEngineServiceQueryMarketingActivitySkuArgs,
		newActivityEngineServiceQueryMarketingActivitySkuResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QuerySkuMarketingActivityC": kitex.NewMethodInfo(
		querySkuMarketingActivityCHandler,
		newActivityEngineServiceQuerySkuMarketingActivityCArgs,
		newActivityEngineServiceQuerySkuMarketingActivityCResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryMarketingActivityProduct": kitex.NewMethodInfo(
		queryMarketingActivityProductHandler,
		newActivityEngineServiceQueryMarketingActivityProductArgs,
		newActivityEngineServiceQueryMarketingActivityProductResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"Ocr": kitex.NewMethodInfo(
		ocrHandler,
		newActivityEngineServiceOcrArgs,
		newActivityEngineServiceOcrResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	activityEngineServiceServiceInfo                = NewServiceInfo()
	activityEngineServiceServiceInfoForClient       = NewServiceInfoForClient()
	activityEngineServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return activityEngineServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return activityEngineServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return activityEngineServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "ActivityEngineService"
	handlerType := (*engine.ActivityEngineService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "engine",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func createActivityHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceCreateActivityArgs)
	realResult := result.(*engine.ActivityEngineServiceCreateActivityResult)
	success, err := handler.(engine.ActivityEngineService).CreateActivity(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceCreateActivityArgs() interface{} {
	return engine.NewActivityEngineServiceCreateActivityArgs()
}

func newActivityEngineServiceCreateActivityResult() interface{} {
	return engine.NewActivityEngineServiceCreateActivityResult()
}

func updateActivityHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceUpdateActivityArgs)
	realResult := result.(*engine.ActivityEngineServiceUpdateActivityResult)
	success, err := handler.(engine.ActivityEngineService).UpdateActivity(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceUpdateActivityArgs() interface{} {
	return engine.NewActivityEngineServiceUpdateActivityArgs()
}

func newActivityEngineServiceUpdateActivityResult() interface{} {
	return engine.NewActivityEngineServiceUpdateActivityResult()
}

func createCouponHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceCreateCouponArgs)
	realResult := result.(*engine.ActivityEngineServiceCreateCouponResult)
	success, err := handler.(engine.ActivityEngineService).CreateCoupon(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceCreateCouponArgs() interface{} {
	return engine.NewActivityEngineServiceCreateCouponArgs()
}

func newActivityEngineServiceCreateCouponResult() interface{} {
	return engine.NewActivityEngineServiceCreateCouponResult()
}

func updateCouponHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceUpdateCouponArgs)
	realResult := result.(*engine.ActivityEngineServiceUpdateCouponResult)
	success, err := handler.(engine.ActivityEngineService).UpdateCoupon(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceUpdateCouponArgs() interface{} {
	return engine.NewActivityEngineServiceUpdateCouponArgs()
}

func newActivityEngineServiceUpdateCouponResult() interface{} {
	return engine.NewActivityEngineServiceUpdateCouponResult()
}

func opCouponHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceOpCouponArgs)
	realResult := result.(*engine.ActivityEngineServiceOpCouponResult)
	success, err := handler.(engine.ActivityEngineService).OpCoupon(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceOpCouponArgs() interface{} {
	return engine.NewActivityEngineServiceOpCouponArgs()
}

func newActivityEngineServiceOpCouponResult() interface{} {
	return engine.NewActivityEngineServiceOpCouponResult()
}

func createOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceCreateOrderArgs)
	realResult := result.(*engine.ActivityEngineServiceCreateOrderResult)
	success, err := handler.(engine.ActivityEngineService).CreateOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceCreateOrderArgs() interface{} {
	return engine.NewActivityEngineServiceCreateOrderArgs()
}

func newActivityEngineServiceCreateOrderResult() interface{} {
	return engine.NewActivityEngineServiceCreateOrderResult()
}

func freezeOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceFreezeOrderArgs)
	realResult := result.(*engine.ActivityEngineServiceFreezeOrderResult)
	success, err := handler.(engine.ActivityEngineService).FreezeOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceFreezeOrderArgs() interface{} {
	return engine.NewActivityEngineServiceFreezeOrderArgs()
}

func newActivityEngineServiceFreezeOrderResult() interface{} {
	return engine.NewActivityEngineServiceFreezeOrderResult()
}

func unfreezeOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceUnfreezeOrderArgs)
	realResult := result.(*engine.ActivityEngineServiceUnfreezeOrderResult)
	success, err := handler.(engine.ActivityEngineService).UnfreezeOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceUnfreezeOrderArgs() interface{} {
	return engine.NewActivityEngineServiceUnfreezeOrderArgs()
}

func newActivityEngineServiceUnfreezeOrderResult() interface{} {
	return engine.NewActivityEngineServiceUnfreezeOrderResult()
}

func approveOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceApproveOrderArgs)
	realResult := result.(*engine.ActivityEngineServiceApproveOrderResult)
	success, err := handler.(engine.ActivityEngineService).ApproveOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceApproveOrderArgs() interface{} {
	return engine.NewActivityEngineServiceApproveOrderArgs()
}

func newActivityEngineServiceApproveOrderResult() interface{} {
	return engine.NewActivityEngineServiceApproveOrderResult()
}

func cancelOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceCancelOrderArgs)
	realResult := result.(*engine.ActivityEngineServiceCancelOrderResult)
	success, err := handler.(engine.ActivityEngineService).CancelOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceCancelOrderArgs() interface{} {
	return engine.NewActivityEngineServiceCancelOrderArgs()
}

func newActivityEngineServiceCancelOrderResult() interface{} {
	return engine.NewActivityEngineServiceCancelOrderResult()
}

func actionOrderHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceActionOrderArgs)
	realResult := result.(*engine.ActivityEngineServiceActionOrderResult)
	success, err := handler.(engine.ActivityEngineService).ActionOrder(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceActionOrderArgs() interface{} {
	return engine.NewActivityEngineServiceActionOrderArgs()
}

func newActivityEngineServiceActionOrderResult() interface{} {
	return engine.NewActivityEngineServiceActionOrderResult()
}

func claimCouponHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceClaimCouponArgs)
	realResult := result.(*engine.ActivityEngineServiceClaimCouponResult)
	success, err := handler.(engine.ActivityEngineService).ClaimCoupon(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceClaimCouponArgs() interface{} {
	return engine.NewActivityEngineServiceClaimCouponArgs()
}

func newActivityEngineServiceClaimCouponResult() interface{} {
	return engine.NewActivityEngineServiceClaimCouponResult()
}

func configEnumHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceConfigEnumArgs)
	realResult := result.(*engine.ActivityEngineServiceConfigEnumResult)
	success, err := handler.(engine.ActivityEngineService).ConfigEnum(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceConfigEnumArgs() interface{} {
	return engine.NewActivityEngineServiceConfigEnumArgs()
}

func newActivityEngineServiceConfigEnumResult() interface{} {
	return engine.NewActivityEngineServiceConfigEnumResult()
}

func queryCouponBHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceQueryCouponBArgs)
	realResult := result.(*engine.ActivityEngineServiceQueryCouponBResult)
	success, err := handler.(engine.ActivityEngineService).QueryCouponB(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceQueryCouponBArgs() interface{} {
	return engine.NewActivityEngineServiceQueryCouponBArgs()
}

func newActivityEngineServiceQueryCouponBResult() interface{} {
	return engine.NewActivityEngineServiceQueryCouponBResult()
}

func mGetCouponHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceMGetCouponArgs)
	realResult := result.(*engine.ActivityEngineServiceMGetCouponResult)
	success, err := handler.(engine.ActivityEngineService).MGetCoupon(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceMGetCouponArgs() interface{} {
	return engine.NewActivityEngineServiceMGetCouponArgs()
}

func newActivityEngineServiceMGetCouponResult() interface{} {
	return engine.NewActivityEngineServiceMGetCouponResult()
}

func mGetCouponSnapshotHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceMGetCouponSnapshotArgs)
	realResult := result.(*engine.ActivityEngineServiceMGetCouponSnapshotResult)
	success, err := handler.(engine.ActivityEngineService).MGetCouponSnapshot(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceMGetCouponSnapshotArgs() interface{} {
	return engine.NewActivityEngineServiceMGetCouponSnapshotArgs()
}

func newActivityEngineServiceMGetCouponSnapshotResult() interface{} {
	return engine.NewActivityEngineServiceMGetCouponSnapshotResult()
}

func queryCouponCHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceQueryCouponCArgs)
	realResult := result.(*engine.ActivityEngineServiceQueryCouponCResult)
	success, err := handler.(engine.ActivityEngineService).QueryCouponC(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceQueryCouponCArgs() interface{} {
	return engine.NewActivityEngineServiceQueryCouponCArgs()
}

func newActivityEngineServiceQueryCouponCResult() interface{} {
	return engine.NewActivityEngineServiceQueryCouponCResult()
}

func queryCouponLogListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceQueryCouponLogListArgs)
	realResult := result.(*engine.ActivityEngineServiceQueryCouponLogListResult)
	success, err := handler.(engine.ActivityEngineService).QueryCouponLogList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceQueryCouponLogListArgs() interface{} {
	return engine.NewActivityEngineServiceQueryCouponLogListArgs()
}

func newActivityEngineServiceQueryCouponLogListResult() interface{} {
	return engine.NewActivityEngineServiceQueryCouponLogListResult()
}

func queryActivityBHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceQueryActivityBArgs)
	realResult := result.(*engine.ActivityEngineServiceQueryActivityBResult)
	success, err := handler.(engine.ActivityEngineService).QueryActivityB(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceQueryActivityBArgs() interface{} {
	return engine.NewActivityEngineServiceQueryActivityBArgs()
}

func newActivityEngineServiceQueryActivityBResult() interface{} {
	return engine.NewActivityEngineServiceQueryActivityBResult()
}

func queryOrderCHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceQueryOrderCArgs)
	realResult := result.(*engine.ActivityEngineServiceQueryOrderCResult)
	success, err := handler.(engine.ActivityEngineService).QueryOrderC(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceQueryOrderCArgs() interface{} {
	return engine.NewActivityEngineServiceQueryOrderCArgs()
}

func newActivityEngineServiceQueryOrderCResult() interface{} {
	return engine.NewActivityEngineServiceQueryOrderCResult()
}

func queryOrderBHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceQueryOrderBArgs)
	realResult := result.(*engine.ActivityEngineServiceQueryOrderBResult)
	success, err := handler.(engine.ActivityEngineService).QueryOrderB(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceQueryOrderBArgs() interface{} {
	return engine.NewActivityEngineServiceQueryOrderBArgs()
}

func newActivityEngineServiceQueryOrderBResult() interface{} {
	return engine.NewActivityEngineServiceQueryOrderBResult()
}

func createMarketingActivityHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceCreateMarketingActivityArgs)
	realResult := result.(*engine.ActivityEngineServiceCreateMarketingActivityResult)
	success, err := handler.(engine.ActivityEngineService).CreateMarketingActivity(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceCreateMarketingActivityArgs() interface{} {
	return engine.NewActivityEngineServiceCreateMarketingActivityArgs()
}

func newActivityEngineServiceCreateMarketingActivityResult() interface{} {
	return engine.NewActivityEngineServiceCreateMarketingActivityResult()
}

func updateMarketingActivityHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceUpdateMarketingActivityArgs)
	realResult := result.(*engine.ActivityEngineServiceUpdateMarketingActivityResult)
	success, err := handler.(engine.ActivityEngineService).UpdateMarketingActivity(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceUpdateMarketingActivityArgs() interface{} {
	return engine.NewActivityEngineServiceUpdateMarketingActivityArgs()
}

func newActivityEngineServiceUpdateMarketingActivityResult() interface{} {
	return engine.NewActivityEngineServiceUpdateMarketingActivityResult()
}

func onlineMarketingActivityHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceOnlineMarketingActivityArgs)
	realResult := result.(*engine.ActivityEngineServiceOnlineMarketingActivityResult)
	success, err := handler.(engine.ActivityEngineService).OnlineMarketingActivity(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceOnlineMarketingActivityArgs() interface{} {
	return engine.NewActivityEngineServiceOnlineMarketingActivityArgs()
}

func newActivityEngineServiceOnlineMarketingActivityResult() interface{} {
	return engine.NewActivityEngineServiceOnlineMarketingActivityResult()
}

func offlineMarketingActivityHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceOfflineMarketingActivityArgs)
	realResult := result.(*engine.ActivityEngineServiceOfflineMarketingActivityResult)
	success, err := handler.(engine.ActivityEngineService).OfflineMarketingActivity(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceOfflineMarketingActivityArgs() interface{} {
	return engine.NewActivityEngineServiceOfflineMarketingActivityArgs()
}

func newActivityEngineServiceOfflineMarketingActivityResult() interface{} {
	return engine.NewActivityEngineServiceOfflineMarketingActivityResult()
}

func queryMarketingActivityHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceQueryMarketingActivityArgs)
	realResult := result.(*engine.ActivityEngineServiceQueryMarketingActivityResult)
	success, err := handler.(engine.ActivityEngineService).QueryMarketingActivity(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceQueryMarketingActivityArgs() interface{} {
	return engine.NewActivityEngineServiceQueryMarketingActivityArgs()
}

func newActivityEngineServiceQueryMarketingActivityResult() interface{} {
	return engine.NewActivityEngineServiceQueryMarketingActivityResult()
}

func opMarketingActivitySkuHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceOpMarketingActivitySkuArgs)
	realResult := result.(*engine.ActivityEngineServiceOpMarketingActivitySkuResult)
	success, err := handler.(engine.ActivityEngineService).OpMarketingActivitySku(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceOpMarketingActivitySkuArgs() interface{} {
	return engine.NewActivityEngineServiceOpMarketingActivitySkuArgs()
}

func newActivityEngineServiceOpMarketingActivitySkuResult() interface{} {
	return engine.NewActivityEngineServiceOpMarketingActivitySkuResult()
}

func queryMarketingActivitySkuHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceQueryMarketingActivitySkuArgs)
	realResult := result.(*engine.ActivityEngineServiceQueryMarketingActivitySkuResult)
	success, err := handler.(engine.ActivityEngineService).QueryMarketingActivitySku(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceQueryMarketingActivitySkuArgs() interface{} {
	return engine.NewActivityEngineServiceQueryMarketingActivitySkuArgs()
}

func newActivityEngineServiceQueryMarketingActivitySkuResult() interface{} {
	return engine.NewActivityEngineServiceQueryMarketingActivitySkuResult()
}

func querySkuMarketingActivityCHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceQuerySkuMarketingActivityCArgs)
	realResult := result.(*engine.ActivityEngineServiceQuerySkuMarketingActivityCResult)
	success, err := handler.(engine.ActivityEngineService).QuerySkuMarketingActivityC(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceQuerySkuMarketingActivityCArgs() interface{} {
	return engine.NewActivityEngineServiceQuerySkuMarketingActivityCArgs()
}

func newActivityEngineServiceQuerySkuMarketingActivityCResult() interface{} {
	return engine.NewActivityEngineServiceQuerySkuMarketingActivityCResult()
}

func queryMarketingActivityProductHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceQueryMarketingActivityProductArgs)
	realResult := result.(*engine.ActivityEngineServiceQueryMarketingActivityProductResult)
	success, err := handler.(engine.ActivityEngineService).QueryMarketingActivityProduct(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceQueryMarketingActivityProductArgs() interface{} {
	return engine.NewActivityEngineServiceQueryMarketingActivityProductArgs()
}

func newActivityEngineServiceQueryMarketingActivityProductResult() interface{} {
	return engine.NewActivityEngineServiceQueryMarketingActivityProductResult()
}

func ocrHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*engine.ActivityEngineServiceOcrArgs)
	realResult := result.(*engine.ActivityEngineServiceOcrResult)
	success, err := handler.(engine.ActivityEngineService).Ocr(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newActivityEngineServiceOcrArgs() interface{} {
	return engine.NewActivityEngineServiceOcrArgs()
}

func newActivityEngineServiceOcrResult() interface{} {
	return engine.NewActivityEngineServiceOcrResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) CreateActivity(ctx context.Context, req *engine.CreateActivityReq) (r *engine.CreateActivityResp, err error) {
	var _args engine.ActivityEngineServiceCreateActivityArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceCreateActivityResult
	if err = p.c.Call(ctx, "CreateActivity", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateActivity(ctx context.Context, req *engine.UpdateActivityReq) (r *engine.UpdateActivityResp, err error) {
	var _args engine.ActivityEngineServiceUpdateActivityArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceUpdateActivityResult
	if err = p.c.Call(ctx, "UpdateActivity", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateCoupon(ctx context.Context, req *engine.CreateCouponReq) (r *engine.CreateCouponResp, err error) {
	var _args engine.ActivityEngineServiceCreateCouponArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceCreateCouponResult
	if err = p.c.Call(ctx, "CreateCoupon", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateCoupon(ctx context.Context, req *engine.UpdateCouponReq) (r *engine.UpdateCouponResp, err error) {
	var _args engine.ActivityEngineServiceUpdateCouponArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceUpdateCouponResult
	if err = p.c.Call(ctx, "UpdateCoupon", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) OpCoupon(ctx context.Context, req *engine.OpCouponReq) (r *engine.OpCouponResp, err error) {
	var _args engine.ActivityEngineServiceOpCouponArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceOpCouponResult
	if err = p.c.Call(ctx, "OpCoupon", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateOrder(ctx context.Context, req *engine.CreateOrderReq) (r *engine.CreateOrderResp, err error) {
	var _args engine.ActivityEngineServiceCreateOrderArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceCreateOrderResult
	if err = p.c.Call(ctx, "CreateOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) FreezeOrder(ctx context.Context, req *engine.FreezeOrderReq) (r *engine.FreezeOrderResp, err error) {
	var _args engine.ActivityEngineServiceFreezeOrderArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceFreezeOrderResult
	if err = p.c.Call(ctx, "FreezeOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UnfreezeOrder(ctx context.Context, req *engine.UnfreezeOrderReq) (r *engine.UnfreezeOrderResp, err error) {
	var _args engine.ActivityEngineServiceUnfreezeOrderArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceUnfreezeOrderResult
	if err = p.c.Call(ctx, "UnfreezeOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ApproveOrder(ctx context.Context, req *engine.ApproveOrderReq) (r *engine.ApproveOrderResp, err error) {
	var _args engine.ActivityEngineServiceApproveOrderArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceApproveOrderResult
	if err = p.c.Call(ctx, "ApproveOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CancelOrder(ctx context.Context, req *engine.CancelOrderReq) (r *engine.CancelOrderResp, err error) {
	var _args engine.ActivityEngineServiceCancelOrderArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceCancelOrderResult
	if err = p.c.Call(ctx, "CancelOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ActionOrder(ctx context.Context, req *engine.ActionOrderReq) (r *engine.ActionOrderResp, err error) {
	var _args engine.ActivityEngineServiceActionOrderArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceActionOrderResult
	if err = p.c.Call(ctx, "ActionOrder", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ClaimCoupon(ctx context.Context, req *engine.ClaimCouponReq) (r *engine.ClaimCouponResp, err error) {
	var _args engine.ActivityEngineServiceClaimCouponArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceClaimCouponResult
	if err = p.c.Call(ctx, "ClaimCoupon", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ConfigEnum(ctx context.Context, req *engine.ConfigEnumReq) (r *engine.ConfigEnumResp, err error) {
	var _args engine.ActivityEngineServiceConfigEnumArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceConfigEnumResult
	if err = p.c.Call(ctx, "ConfigEnum", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryCouponB(ctx context.Context, req *engine.QueryCouponBReq) (r *engine.QueryCouponBResp, err error) {
	var _args engine.ActivityEngineServiceQueryCouponBArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceQueryCouponBResult
	if err = p.c.Call(ctx, "QueryCouponB", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetCoupon(ctx context.Context, req *engine.MGetCouponReq) (r *engine.MGetCouponResp, err error) {
	var _args engine.ActivityEngineServiceMGetCouponArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceMGetCouponResult
	if err = p.c.Call(ctx, "MGetCoupon", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetCouponSnapshot(ctx context.Context, req *engine.MGetCouponSnapshotReq) (r *engine.MGetCouponSnapshotResp, err error) {
	var _args engine.ActivityEngineServiceMGetCouponSnapshotArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceMGetCouponSnapshotResult
	if err = p.c.Call(ctx, "MGetCouponSnapshot", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryCouponC(ctx context.Context, req *engine.QueryCouponCReq) (r *engine.QueryCouponCResp, err error) {
	var _args engine.ActivityEngineServiceQueryCouponCArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceQueryCouponCResult
	if err = p.c.Call(ctx, "QueryCouponC", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryCouponLogList(ctx context.Context, req *engine.QueryCouponLogListReq) (r *engine.QueryCouponLogListResp, err error) {
	var _args engine.ActivityEngineServiceQueryCouponLogListArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceQueryCouponLogListResult
	if err = p.c.Call(ctx, "QueryCouponLogList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryActivityB(ctx context.Context, req *engine.QueryActivityBReq) (r *engine.QueryActivityBResp, err error) {
	var _args engine.ActivityEngineServiceQueryActivityBArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceQueryActivityBResult
	if err = p.c.Call(ctx, "QueryActivityB", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryOrderC(ctx context.Context, req *engine.QueryOrderCReq) (r *engine.QueryOrderCResp, err error) {
	var _args engine.ActivityEngineServiceQueryOrderCArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceQueryOrderCResult
	if err = p.c.Call(ctx, "QueryOrderC", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryOrderB(ctx context.Context, req *engine.QueryOrderBReq) (r *engine.QueryOrderBResp, err error) {
	var _args engine.ActivityEngineServiceQueryOrderBArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceQueryOrderBResult
	if err = p.c.Call(ctx, "QueryOrderB", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateMarketingActivity(ctx context.Context, req *engine.CreateMarketingActivityReq) (r *engine.CreateMarketingActivityResp, err error) {
	var _args engine.ActivityEngineServiceCreateMarketingActivityArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceCreateMarketingActivityResult
	if err = p.c.Call(ctx, "CreateMarketingActivity", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateMarketingActivity(ctx context.Context, req *engine.UpdateMarketingActivityReq) (r *engine.UpdateMarketingActivityResp, err error) {
	var _args engine.ActivityEngineServiceUpdateMarketingActivityArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceUpdateMarketingActivityResult
	if err = p.c.Call(ctx, "UpdateMarketingActivity", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) OnlineMarketingActivity(ctx context.Context, req *engine.OnlineMarketingActivityReq) (r *engine.OnlineMarketingActivityResp, err error) {
	var _args engine.ActivityEngineServiceOnlineMarketingActivityArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceOnlineMarketingActivityResult
	if err = p.c.Call(ctx, "OnlineMarketingActivity", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) OfflineMarketingActivity(ctx context.Context, req *engine.OfflineMarketingActivityReq) (r *engine.OfflineMarketingActivityResp, err error) {
	var _args engine.ActivityEngineServiceOfflineMarketingActivityArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceOfflineMarketingActivityResult
	if err = p.c.Call(ctx, "OfflineMarketingActivity", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryMarketingActivity(ctx context.Context, req *engine.QueryMarketingActivityReq) (r *engine.QueryMarketingActivityResp, err error) {
	var _args engine.ActivityEngineServiceQueryMarketingActivityArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceQueryMarketingActivityResult
	if err = p.c.Call(ctx, "QueryMarketingActivity", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) OpMarketingActivitySku(ctx context.Context, req *engine.OpMarketingActivitySkuReq) (r *engine.OpMarketingActivitySkuResp, err error) {
	var _args engine.ActivityEngineServiceOpMarketingActivitySkuArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceOpMarketingActivitySkuResult
	if err = p.c.Call(ctx, "OpMarketingActivitySku", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryMarketingActivitySku(ctx context.Context, req *engine.QueryMarketingActivitySkuReq) (r *engine.QueryMarketingActivitySkuResp, err error) {
	var _args engine.ActivityEngineServiceQueryMarketingActivitySkuArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceQueryMarketingActivitySkuResult
	if err = p.c.Call(ctx, "QueryMarketingActivitySku", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QuerySkuMarketingActivityC(ctx context.Context, req *engine.QuerySkuMarketingActivityCReq) (r *engine.QuerySkuMarketingActivityCResp, err error) {
	var _args engine.ActivityEngineServiceQuerySkuMarketingActivityCArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceQuerySkuMarketingActivityCResult
	if err = p.c.Call(ctx, "QuerySkuMarketingActivityC", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryMarketingActivityProduct(ctx context.Context, req *engine.QueryMarketingActivityProductReq) (r *engine.QueryMarketingActivityProductResp, err error) {
	var _args engine.ActivityEngineServiceQueryMarketingActivityProductArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceQueryMarketingActivityProductResult
	if err = p.c.Call(ctx, "QueryMarketingActivityProduct", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) Ocr(ctx context.Context, req *engine.OcrReq) (r *engine.OcrResp, err error) {
	var _args engine.ActivityEngineServiceOcrArgs
	_args.Req = req
	var _result engine.ActivityEngineServiceOcrResult
	if err = p.c.Call(ctx, "Ocr", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
