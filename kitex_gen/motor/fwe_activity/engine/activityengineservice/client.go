// Code generated by Kitex v1.20.3. DO NOT EDIT.

package activityengineservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	engine "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_activity/engine"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	CreateActivity(ctx context.Context, req *engine.CreateActivityReq, callOptions ...callopt.Option) (r *engine.CreateActivityResp, err error)
	UpdateActivity(ctx context.Context, req *engine.UpdateActivityReq, callOptions ...callopt.Option) (r *engine.UpdateActivityResp, err error)
	CreateCoupon(ctx context.Context, req *engine.CreateCouponReq, callOptions ...callopt.Option) (r *engine.CreateCouponResp, err error)
	UpdateCoupon(ctx context.Context, req *engine.UpdateCouponReq, callOptions ...callopt.Option) (r *engine.UpdateCouponResp, err error)
	OpCoupon(ctx context.Context, req *engine.OpCouponReq, callOptions ...callopt.Option) (r *engine.OpCouponResp, err error)
	CreateOrder(ctx context.Context, req *engine.CreateOrderReq, callOptions ...callopt.Option) (r *engine.CreateOrderResp, err error)
	FreezeOrder(ctx context.Context, req *engine.FreezeOrderReq, callOptions ...callopt.Option) (r *engine.FreezeOrderResp, err error)
	UnfreezeOrder(ctx context.Context, req *engine.UnfreezeOrderReq, callOptions ...callopt.Option) (r *engine.UnfreezeOrderResp, err error)
	ApproveOrder(ctx context.Context, req *engine.ApproveOrderReq, callOptions ...callopt.Option) (r *engine.ApproveOrderResp, err error)
	CancelOrder(ctx context.Context, req *engine.CancelOrderReq, callOptions ...callopt.Option) (r *engine.CancelOrderResp, err error)
	ActionOrder(ctx context.Context, req *engine.ActionOrderReq, callOptions ...callopt.Option) (r *engine.ActionOrderResp, err error)
	ClaimCoupon(ctx context.Context, req *engine.ClaimCouponReq, callOptions ...callopt.Option) (r *engine.ClaimCouponResp, err error)
	ConfigEnum(ctx context.Context, req *engine.ConfigEnumReq, callOptions ...callopt.Option) (r *engine.ConfigEnumResp, err error)
	QueryCouponB(ctx context.Context, req *engine.QueryCouponBReq, callOptions ...callopt.Option) (r *engine.QueryCouponBResp, err error)
	MGetCoupon(ctx context.Context, req *engine.MGetCouponReq, callOptions ...callopt.Option) (r *engine.MGetCouponResp, err error)
	MGetCouponSnapshot(ctx context.Context, req *engine.MGetCouponSnapshotReq, callOptions ...callopt.Option) (r *engine.MGetCouponSnapshotResp, err error)
	QueryCouponC(ctx context.Context, req *engine.QueryCouponCReq, callOptions ...callopt.Option) (r *engine.QueryCouponCResp, err error)
	QueryCouponLogList(ctx context.Context, req *engine.QueryCouponLogListReq, callOptions ...callopt.Option) (r *engine.QueryCouponLogListResp, err error)
	QueryActivityB(ctx context.Context, req *engine.QueryActivityBReq, callOptions ...callopt.Option) (r *engine.QueryActivityBResp, err error)
	QueryOrderC(ctx context.Context, req *engine.QueryOrderCReq, callOptions ...callopt.Option) (r *engine.QueryOrderCResp, err error)
	QueryOrderB(ctx context.Context, req *engine.QueryOrderBReq, callOptions ...callopt.Option) (r *engine.QueryOrderBResp, err error)
	CreateMarketingActivity(ctx context.Context, req *engine.CreateMarketingActivityReq, callOptions ...callopt.Option) (r *engine.CreateMarketingActivityResp, err error)
	UpdateMarketingActivity(ctx context.Context, req *engine.UpdateMarketingActivityReq, callOptions ...callopt.Option) (r *engine.UpdateMarketingActivityResp, err error)
	OnlineMarketingActivity(ctx context.Context, req *engine.OnlineMarketingActivityReq, callOptions ...callopt.Option) (r *engine.OnlineMarketingActivityResp, err error)
	OfflineMarketingActivity(ctx context.Context, req *engine.OfflineMarketingActivityReq, callOptions ...callopt.Option) (r *engine.OfflineMarketingActivityResp, err error)
	QueryMarketingActivity(ctx context.Context, req *engine.QueryMarketingActivityReq, callOptions ...callopt.Option) (r *engine.QueryMarketingActivityResp, err error)
	OpMarketingActivitySku(ctx context.Context, req *engine.OpMarketingActivitySkuReq, callOptions ...callopt.Option) (r *engine.OpMarketingActivitySkuResp, err error)
	QueryMarketingActivitySku(ctx context.Context, req *engine.QueryMarketingActivitySkuReq, callOptions ...callopt.Option) (r *engine.QueryMarketingActivitySkuResp, err error)
	QuerySkuMarketingActivityC(ctx context.Context, req *engine.QuerySkuMarketingActivityCReq, callOptions ...callopt.Option) (r *engine.QuerySkuMarketingActivityCResp, err error)
	QueryMarketingActivityProduct(ctx context.Context, req *engine.QueryMarketingActivityProductReq, callOptions ...callopt.Option) (r *engine.QueryMarketingActivityProductResp, err error)
	Ocr(ctx context.Context, req *engine.OcrReq, callOptions ...callopt.Option) (r *engine.OcrResp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kActivityEngineServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kActivityEngineServiceClient struct {
	*kClient
}

func (p *kActivityEngineServiceClient) CreateActivity(ctx context.Context, req *engine.CreateActivityReq, callOptions ...callopt.Option) (r *engine.CreateActivityResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateActivity(ctx, req)
}

func (p *kActivityEngineServiceClient) UpdateActivity(ctx context.Context, req *engine.UpdateActivityReq, callOptions ...callopt.Option) (r *engine.UpdateActivityResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateActivity(ctx, req)
}

func (p *kActivityEngineServiceClient) CreateCoupon(ctx context.Context, req *engine.CreateCouponReq, callOptions ...callopt.Option) (r *engine.CreateCouponResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateCoupon(ctx, req)
}

func (p *kActivityEngineServiceClient) UpdateCoupon(ctx context.Context, req *engine.UpdateCouponReq, callOptions ...callopt.Option) (r *engine.UpdateCouponResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateCoupon(ctx, req)
}

func (p *kActivityEngineServiceClient) OpCoupon(ctx context.Context, req *engine.OpCouponReq, callOptions ...callopt.Option) (r *engine.OpCouponResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.OpCoupon(ctx, req)
}

func (p *kActivityEngineServiceClient) CreateOrder(ctx context.Context, req *engine.CreateOrderReq, callOptions ...callopt.Option) (r *engine.CreateOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateOrder(ctx, req)
}

func (p *kActivityEngineServiceClient) FreezeOrder(ctx context.Context, req *engine.FreezeOrderReq, callOptions ...callopt.Option) (r *engine.FreezeOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.FreezeOrder(ctx, req)
}

func (p *kActivityEngineServiceClient) UnfreezeOrder(ctx context.Context, req *engine.UnfreezeOrderReq, callOptions ...callopt.Option) (r *engine.UnfreezeOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UnfreezeOrder(ctx, req)
}

func (p *kActivityEngineServiceClient) ApproveOrder(ctx context.Context, req *engine.ApproveOrderReq, callOptions ...callopt.Option) (r *engine.ApproveOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ApproveOrder(ctx, req)
}

func (p *kActivityEngineServiceClient) CancelOrder(ctx context.Context, req *engine.CancelOrderReq, callOptions ...callopt.Option) (r *engine.CancelOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CancelOrder(ctx, req)
}

func (p *kActivityEngineServiceClient) ActionOrder(ctx context.Context, req *engine.ActionOrderReq, callOptions ...callopt.Option) (r *engine.ActionOrderResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ActionOrder(ctx, req)
}

func (p *kActivityEngineServiceClient) ClaimCoupon(ctx context.Context, req *engine.ClaimCouponReq, callOptions ...callopt.Option) (r *engine.ClaimCouponResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ClaimCoupon(ctx, req)
}

func (p *kActivityEngineServiceClient) ConfigEnum(ctx context.Context, req *engine.ConfigEnumReq, callOptions ...callopt.Option) (r *engine.ConfigEnumResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ConfigEnum(ctx, req)
}

func (p *kActivityEngineServiceClient) QueryCouponB(ctx context.Context, req *engine.QueryCouponBReq, callOptions ...callopt.Option) (r *engine.QueryCouponBResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryCouponB(ctx, req)
}

func (p *kActivityEngineServiceClient) MGetCoupon(ctx context.Context, req *engine.MGetCouponReq, callOptions ...callopt.Option) (r *engine.MGetCouponResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetCoupon(ctx, req)
}

func (p *kActivityEngineServiceClient) MGetCouponSnapshot(ctx context.Context, req *engine.MGetCouponSnapshotReq, callOptions ...callopt.Option) (r *engine.MGetCouponSnapshotResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetCouponSnapshot(ctx, req)
}

func (p *kActivityEngineServiceClient) QueryCouponC(ctx context.Context, req *engine.QueryCouponCReq, callOptions ...callopt.Option) (r *engine.QueryCouponCResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryCouponC(ctx, req)
}

func (p *kActivityEngineServiceClient) QueryCouponLogList(ctx context.Context, req *engine.QueryCouponLogListReq, callOptions ...callopt.Option) (r *engine.QueryCouponLogListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryCouponLogList(ctx, req)
}

func (p *kActivityEngineServiceClient) QueryActivityB(ctx context.Context, req *engine.QueryActivityBReq, callOptions ...callopt.Option) (r *engine.QueryActivityBResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryActivityB(ctx, req)
}

func (p *kActivityEngineServiceClient) QueryOrderC(ctx context.Context, req *engine.QueryOrderCReq, callOptions ...callopt.Option) (r *engine.QueryOrderCResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryOrderC(ctx, req)
}

func (p *kActivityEngineServiceClient) QueryOrderB(ctx context.Context, req *engine.QueryOrderBReq, callOptions ...callopt.Option) (r *engine.QueryOrderBResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryOrderB(ctx, req)
}

func (p *kActivityEngineServiceClient) CreateMarketingActivity(ctx context.Context, req *engine.CreateMarketingActivityReq, callOptions ...callopt.Option) (r *engine.CreateMarketingActivityResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateMarketingActivity(ctx, req)
}

func (p *kActivityEngineServiceClient) UpdateMarketingActivity(ctx context.Context, req *engine.UpdateMarketingActivityReq, callOptions ...callopt.Option) (r *engine.UpdateMarketingActivityResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateMarketingActivity(ctx, req)
}

func (p *kActivityEngineServiceClient) OnlineMarketingActivity(ctx context.Context, req *engine.OnlineMarketingActivityReq, callOptions ...callopt.Option) (r *engine.OnlineMarketingActivityResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.OnlineMarketingActivity(ctx, req)
}

func (p *kActivityEngineServiceClient) OfflineMarketingActivity(ctx context.Context, req *engine.OfflineMarketingActivityReq, callOptions ...callopt.Option) (r *engine.OfflineMarketingActivityResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.OfflineMarketingActivity(ctx, req)
}

func (p *kActivityEngineServiceClient) QueryMarketingActivity(ctx context.Context, req *engine.QueryMarketingActivityReq, callOptions ...callopt.Option) (r *engine.QueryMarketingActivityResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryMarketingActivity(ctx, req)
}

func (p *kActivityEngineServiceClient) OpMarketingActivitySku(ctx context.Context, req *engine.OpMarketingActivitySkuReq, callOptions ...callopt.Option) (r *engine.OpMarketingActivitySkuResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.OpMarketingActivitySku(ctx, req)
}

func (p *kActivityEngineServiceClient) QueryMarketingActivitySku(ctx context.Context, req *engine.QueryMarketingActivitySkuReq, callOptions ...callopt.Option) (r *engine.QueryMarketingActivitySkuResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryMarketingActivitySku(ctx, req)
}

func (p *kActivityEngineServiceClient) QuerySkuMarketingActivityC(ctx context.Context, req *engine.QuerySkuMarketingActivityCReq, callOptions ...callopt.Option) (r *engine.QuerySkuMarketingActivityCResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QuerySkuMarketingActivityC(ctx, req)
}

func (p *kActivityEngineServiceClient) QueryMarketingActivityProduct(ctx context.Context, req *engine.QueryMarketingActivityProductReq, callOptions ...callopt.Option) (r *engine.QueryMarketingActivityProductResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryMarketingActivityProduct(ctx, req)
}

func (p *kActivityEngineServiceClient) Ocr(ctx context.Context, req *engine.OcrReq, callOptions ...callopt.Option) (r *engine.OcrResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.Ocr(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kActivityEngineServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
