// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package fwe_base

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/base"
	"database/sql"
	"database/sql/driver"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
)

type AccountType int64

const (
	AccountType_Dealer              AccountType = 1
	AccountType_SubDealer           AccountType = 2
	AccountType_FinancialLease      AccountType = 3
	AccountType_Manufacturer        AccountType = 4
	AccountType_Group               AccountType = 5
	AccountType_FlagshipStore       AccountType = 6
	AccountType_Custom              AccountType = 7
	AccountType_Platform            AccountType = 8
	AccountType_SecondHand          AccountType = 9
	AccountType_MarketAD            AccountType = 10
	AccountType_AfterSales          AccountType = 11
	AccountType_Trade               AccountType = 12
	AccountType_ExternalPlatform    AccountType = 13
	AccountType_ThirdAdx            AccountType = 14
	AccountType_Finance             AccountType = 15
	AccountType_AdDsp               AccountType = 16
	AccountType_CISN                AccountType = 17
	AccountType_Retail              AccountType = 18
	AccountType_EWDMS               AccountType = 19
	AccountType_Open                AccountType = 20
	AccountType_Auction             AccountType = 21
	AccountType_SecondHandTrade     AccountType = 22
	AccountType_SCN                 AccountType = 23
	AccountType_AiPlus              AccountType = 24
	AccountType_DDT                 AccountType = 25
	AccountType_UgUnion             AccountType = 26
	AccountType_ECN                 AccountType = 27
	AccountType_AfterMarket         AccountType = 28
	AccountType_AutoEngineBI        AccountType = 29
	AccountType_Agent               AccountType = 30
	AccountType_ShInspection        AccountType = 31
	AccountType_NewCarTrade         AccountType = 33
	AccountType_AfterMarketSupply   AccountType = 34
	AccountType_FinanceSaaSZF       AccountType = 35
	AccountType_MarketShop          AccountType = 36
	AccountType_MctSale             AccountType = 37
	AccountType_CISNSupply          AccountType = 38
	AccountType_SecondHandAuction   AccountType = 39
	AccountType_TradeScm            AccountType = 40
	AccountType_CustomerManage      AccountType = 41
	AccountType_CollectCar          AccountType = 42
	AccountType_CarSupply           AccountType = 43
	AccountType_NewCarEcom          AccountType = 44
	AccountType_AuctionCollectCar   AccountType = 45
	AccountType_PersonCollectCar    AccountType = 46
	AccountType_WmsSaaS             AccountType = 47
	AccountType_AutoEngineInnerBI   AccountType = 48
	AccountType_SecondHandDBox      AccountType = 49
	AccountType_TmsSaaS             AccountType = 50
	AccountType_GovernmentWriteoff  AccountType = 51
	AccountType_LiveSaaS            AccountType = 52
	AccountType_MotorcycleSaas      AccountType = 53
	AccountType_SupplyAdmin         AccountType = 54
	AccountType_InsurancePlatform   AccountType = 55
	AccountType_WmsOuterSystem      AccountType = 56
	AccountType_NewCarEcomSale      AccountType = 57
	AccountType_FSInsight           AccountType = 58
	AccountType_FactorySaaS         AccountType = 59
	AccountType_DEMatrix            AccountType = 60
	AccountType_ServiceProvider     AccountType = 61
	AccountType_BlueBird            AccountType = 62
	AccountType_ServiceMarket       AccountType = 63
	AccountType_BpiDcdAd            AccountType = 65
	AccountType_AutoPrd             AccountType = 66
	AccountType_MembershipAgent     AccountType = 67
	AccountType_MallChehou          AccountType = 68
	AccountType_CarResourceProvider AccountType = 69
	AccountType_AdMatch             AccountType = 70
	AccountType_LeadsCenter         AccountType = 100
	AccountType_FweTrade            AccountType = 101
	AccountType_FwePlatform         AccountType = 102
	AccountType_FweSampleSaaS       AccountType = 103
	AccountType_DcarAdmin           AccountType = 110
	AccountType_ShCustomerEnd       AccountType = 113
)

func (p AccountType) String() string {
	switch p {
	case AccountType_Dealer:
		return "Dealer"
	case AccountType_SubDealer:
		return "SubDealer"
	case AccountType_FinancialLease:
		return "FinancialLease"
	case AccountType_Manufacturer:
		return "Manufacturer"
	case AccountType_Group:
		return "Group"
	case AccountType_FlagshipStore:
		return "FlagshipStore"
	case AccountType_Custom:
		return "Custom"
	case AccountType_Platform:
		return "Platform"
	case AccountType_SecondHand:
		return "SecondHand"
	case AccountType_MarketAD:
		return "MarketAD"
	case AccountType_AfterSales:
		return "AfterSales"
	case AccountType_Trade:
		return "Trade"
	case AccountType_ExternalPlatform:
		return "ExternalPlatform"
	case AccountType_ThirdAdx:
		return "ThirdAdx"
	case AccountType_Finance:
		return "Finance"
	case AccountType_AdDsp:
		return "AdDsp"
	case AccountType_CISN:
		return "CISN"
	case AccountType_Retail:
		return "Retail"
	case AccountType_EWDMS:
		return "EWDMS"
	case AccountType_Open:
		return "Open"
	case AccountType_Auction:
		return "Auction"
	case AccountType_SecondHandTrade:
		return "SecondHandTrade"
	case AccountType_SCN:
		return "SCN"
	case AccountType_AiPlus:
		return "AiPlus"
	case AccountType_DDT:
		return "DDT"
	case AccountType_UgUnion:
		return "UgUnion"
	case AccountType_ECN:
		return "ECN"
	case AccountType_AfterMarket:
		return "AfterMarket"
	case AccountType_AutoEngineBI:
		return "AutoEngineBI"
	case AccountType_Agent:
		return "Agent"
	case AccountType_ShInspection:
		return "ShInspection"
	case AccountType_NewCarTrade:
		return "NewCarTrade"
	case AccountType_AfterMarketSupply:
		return "AfterMarketSupply"
	case AccountType_FinanceSaaSZF:
		return "FinanceSaaSZF"
	case AccountType_MarketShop:
		return "MarketShop"
	case AccountType_MctSale:
		return "MctSale"
	case AccountType_CISNSupply:
		return "CISNSupply"
	case AccountType_SecondHandAuction:
		return "SecondHandAuction"
	case AccountType_TradeScm:
		return "TradeScm"
	case AccountType_CustomerManage:
		return "CustomerManage"
	case AccountType_CollectCar:
		return "CollectCar"
	case AccountType_CarSupply:
		return "CarSupply"
	case AccountType_NewCarEcom:
		return "NewCarEcom"
	case AccountType_AuctionCollectCar:
		return "AuctionCollectCar"
	case AccountType_PersonCollectCar:
		return "PersonCollectCar"
	case AccountType_WmsSaaS:
		return "WmsSaaS"
	case AccountType_AutoEngineInnerBI:
		return "AutoEngineInnerBI"
	case AccountType_SecondHandDBox:
		return "SecondHandDBox"
	case AccountType_TmsSaaS:
		return "TmsSaaS"
	case AccountType_GovernmentWriteoff:
		return "GovernmentWriteoff"
	case AccountType_LiveSaaS:
		return "LiveSaaS"
	case AccountType_MotorcycleSaas:
		return "MotorcycleSaas"
	case AccountType_SupplyAdmin:
		return "SupplyAdmin"
	case AccountType_InsurancePlatform:
		return "InsurancePlatform"
	case AccountType_WmsOuterSystem:
		return "WmsOuterSystem"
	case AccountType_NewCarEcomSale:
		return "NewCarEcomSale"
	case AccountType_FSInsight:
		return "FSInsight"
	case AccountType_FactorySaaS:
		return "FactorySaaS"
	case AccountType_DEMatrix:
		return "DEMatrix"
	case AccountType_ServiceProvider:
		return "ServiceProvider"
	case AccountType_BlueBird:
		return "BlueBird"
	case AccountType_ServiceMarket:
		return "ServiceMarket"
	case AccountType_BpiDcdAd:
		return "BpiDcdAd"
	case AccountType_AutoPrd:
		return "AutoPrd"
	case AccountType_MembershipAgent:
		return "MembershipAgent"
	case AccountType_MallChehou:
		return "MallChehou"
	case AccountType_CarResourceProvider:
		return "CarResourceProvider"
	case AccountType_AdMatch:
		return "AdMatch"
	case AccountType_LeadsCenter:
		return "LeadsCenter"
	case AccountType_FweTrade:
		return "FweTrade"
	case AccountType_FwePlatform:
		return "FwePlatform"
	case AccountType_FweSampleSaaS:
		return "FweSampleSaaS"
	case AccountType_DcarAdmin:
		return "DcarAdmin"
	case AccountType_ShCustomerEnd:
		return "ShCustomerEnd"
	}
	return "<UNSET>"
}

func AccountTypeFromString(s string) (AccountType, error) {
	switch s {
	case "Dealer":
		return AccountType_Dealer, nil
	case "SubDealer":
		return AccountType_SubDealer, nil
	case "FinancialLease":
		return AccountType_FinancialLease, nil
	case "Manufacturer":
		return AccountType_Manufacturer, nil
	case "Group":
		return AccountType_Group, nil
	case "FlagshipStore":
		return AccountType_FlagshipStore, nil
	case "Custom":
		return AccountType_Custom, nil
	case "Platform":
		return AccountType_Platform, nil
	case "SecondHand":
		return AccountType_SecondHand, nil
	case "MarketAD":
		return AccountType_MarketAD, nil
	case "AfterSales":
		return AccountType_AfterSales, nil
	case "Trade":
		return AccountType_Trade, nil
	case "ExternalPlatform":
		return AccountType_ExternalPlatform, nil
	case "ThirdAdx":
		return AccountType_ThirdAdx, nil
	case "Finance":
		return AccountType_Finance, nil
	case "AdDsp":
		return AccountType_AdDsp, nil
	case "CISN":
		return AccountType_CISN, nil
	case "Retail":
		return AccountType_Retail, nil
	case "EWDMS":
		return AccountType_EWDMS, nil
	case "Open":
		return AccountType_Open, nil
	case "Auction":
		return AccountType_Auction, nil
	case "SecondHandTrade":
		return AccountType_SecondHandTrade, nil
	case "SCN":
		return AccountType_SCN, nil
	case "AiPlus":
		return AccountType_AiPlus, nil
	case "DDT":
		return AccountType_DDT, nil
	case "UgUnion":
		return AccountType_UgUnion, nil
	case "ECN":
		return AccountType_ECN, nil
	case "AfterMarket":
		return AccountType_AfterMarket, nil
	case "AutoEngineBI":
		return AccountType_AutoEngineBI, nil
	case "Agent":
		return AccountType_Agent, nil
	case "ShInspection":
		return AccountType_ShInspection, nil
	case "NewCarTrade":
		return AccountType_NewCarTrade, nil
	case "AfterMarketSupply":
		return AccountType_AfterMarketSupply, nil
	case "FinanceSaaSZF":
		return AccountType_FinanceSaaSZF, nil
	case "MarketShop":
		return AccountType_MarketShop, nil
	case "MctSale":
		return AccountType_MctSale, nil
	case "CISNSupply":
		return AccountType_CISNSupply, nil
	case "SecondHandAuction":
		return AccountType_SecondHandAuction, nil
	case "TradeScm":
		return AccountType_TradeScm, nil
	case "CustomerManage":
		return AccountType_CustomerManage, nil
	case "CollectCar":
		return AccountType_CollectCar, nil
	case "CarSupply":
		return AccountType_CarSupply, nil
	case "NewCarEcom":
		return AccountType_NewCarEcom, nil
	case "AuctionCollectCar":
		return AccountType_AuctionCollectCar, nil
	case "PersonCollectCar":
		return AccountType_PersonCollectCar, nil
	case "WmsSaaS":
		return AccountType_WmsSaaS, nil
	case "AutoEngineInnerBI":
		return AccountType_AutoEngineInnerBI, nil
	case "SecondHandDBox":
		return AccountType_SecondHandDBox, nil
	case "TmsSaaS":
		return AccountType_TmsSaaS, nil
	case "GovernmentWriteoff":
		return AccountType_GovernmentWriteoff, nil
	case "LiveSaaS":
		return AccountType_LiveSaaS, nil
	case "MotorcycleSaas":
		return AccountType_MotorcycleSaas, nil
	case "SupplyAdmin":
		return AccountType_SupplyAdmin, nil
	case "InsurancePlatform":
		return AccountType_InsurancePlatform, nil
	case "WmsOuterSystem":
		return AccountType_WmsOuterSystem, nil
	case "NewCarEcomSale":
		return AccountType_NewCarEcomSale, nil
	case "FSInsight":
		return AccountType_FSInsight, nil
	case "FactorySaaS":
		return AccountType_FactorySaaS, nil
	case "DEMatrix":
		return AccountType_DEMatrix, nil
	case "ServiceProvider":
		return AccountType_ServiceProvider, nil
	case "BlueBird":
		return AccountType_BlueBird, nil
	case "ServiceMarket":
		return AccountType_ServiceMarket, nil
	case "BpiDcdAd":
		return AccountType_BpiDcdAd, nil
	case "AutoPrd":
		return AccountType_AutoPrd, nil
	case "MembershipAgent":
		return AccountType_MembershipAgent, nil
	case "MallChehou":
		return AccountType_MallChehou, nil
	case "CarResourceProvider":
		return AccountType_CarResourceProvider, nil
	case "AdMatch":
		return AccountType_AdMatch, nil
	case "LeadsCenter":
		return AccountType_LeadsCenter, nil
	case "FweTrade":
		return AccountType_FweTrade, nil
	case "FwePlatform":
		return AccountType_FwePlatform, nil
	case "FweSampleSaaS":
		return AccountType_FweSampleSaaS, nil
	case "DcarAdmin":
		return AccountType_DcarAdmin, nil
	case "ShCustomerEnd":
		return AccountType_ShCustomerEnd, nil
	}
	return AccountType(0), fmt.Errorf("not a valid AccountType string")
}

func AccountTypePtr(v AccountType) *AccountType { return &v }
func (p *AccountType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = AccountType(result.Int64)
	return
}

func (p *AccountType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type UIDType int64

const (
	UIDType_TT          UIDType = 1
	UIDType_DY          UIDType = 2
	UIDType_AD          UIDType = 3
	UIDType_EmailPrefix UIDType = 10
	UIDType_QyhOpenID   UIDType = 21
	UIDType_FW          UIDType = 200
	UIDType_PeopleId    UIDType = 201
)

func (p UIDType) String() string {
	switch p {
	case UIDType_TT:
		return "TT"
	case UIDType_DY:
		return "DY"
	case UIDType_AD:
		return "AD"
	case UIDType_EmailPrefix:
		return "EmailPrefix"
	case UIDType_QyhOpenID:
		return "QyhOpenID"
	case UIDType_FW:
		return "FW"
	case UIDType_PeopleId:
		return "PeopleId"
	}
	return "<UNSET>"
}

func UIDTypeFromString(s string) (UIDType, error) {
	switch s {
	case "TT":
		return UIDType_TT, nil
	case "DY":
		return UIDType_DY, nil
	case "AD":
		return UIDType_AD, nil
	case "EmailPrefix":
		return UIDType_EmailPrefix, nil
	case "QyhOpenID":
		return UIDType_QyhOpenID, nil
	case "FW":
		return UIDType_FW, nil
	case "PeopleId":
		return UIDType_PeopleId, nil
	}
	return UIDType(0), fmt.Errorf("not a valid UIDType string")
}

func UIDTypePtr(v UIDType) *UIDType { return &v }
func (p *UIDType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = UIDType(result.Int64)
	return
}

func (p *UIDType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type FieldValueType int64

const (
	FieldValueType_String  FieldValueType = 0
	FieldValueType_Int64   FieldValueType = 1
	FieldValueType_Boolean FieldValueType = 2
)

func (p FieldValueType) String() string {
	switch p {
	case FieldValueType_String:
		return "String"
	case FieldValueType_Int64:
		return "Int64"
	case FieldValueType_Boolean:
		return "Boolean"
	}
	return "<UNSET>"
}

func FieldValueTypeFromString(s string) (FieldValueType, error) {
	switch s {
	case "String":
		return FieldValueType_String, nil
	case "Int64":
		return FieldValueType_Int64, nil
	case "Boolean":
		return FieldValueType_Boolean, nil
	}
	return FieldValueType(0), fmt.Errorf("not a valid FieldValueType string")
}

func FieldValueTypePtr(v FieldValueType) *FieldValueType { return &v }
func (p *FieldValueType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = FieldValueType(result.Int64)
	return
}

func (p *FieldValueType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type AttrType int64

const (
	AttrType_Int64        AttrType = 1
	AttrType_Float64      AttrType = 2
	AttrType_String       AttrType = 3
	AttrType_Bool         AttrType = 4
	AttrType_Int64Slice   AttrType = 5
	AttrType_Float64Slice AttrType = 6
	AttrType_StringSlice  AttrType = 7
	AttrType_BoolSlice    AttrType = 8
)

func (p AttrType) String() string {
	switch p {
	case AttrType_Int64:
		return "Int64"
	case AttrType_Float64:
		return "Float64"
	case AttrType_String:
		return "String"
	case AttrType_Bool:
		return "Bool"
	case AttrType_Int64Slice:
		return "Int64Slice"
	case AttrType_Float64Slice:
		return "Float64Slice"
	case AttrType_StringSlice:
		return "StringSlice"
	case AttrType_BoolSlice:
		return "BoolSlice"
	}
	return "<UNSET>"
}

func AttrTypeFromString(s string) (AttrType, error) {
	switch s {
	case "Int64":
		return AttrType_Int64, nil
	case "Float64":
		return AttrType_Float64, nil
	case "String":
		return AttrType_String, nil
	case "Bool":
		return AttrType_Bool, nil
	case "Int64Slice":
		return AttrType_Int64Slice, nil
	case "Float64Slice":
		return AttrType_Float64Slice, nil
	case "StringSlice":
		return AttrType_StringSlice, nil
	case "BoolSlice":
		return AttrType_BoolSlice, nil
	}
	return AttrType(0), fmt.Errorf("not a valid AttrType string")
}

func AttrTypePtr(v AttrType) *AttrType { return &v }
func (p *AttrType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = AttrType(result.Int64)
	return
}

func (p *AttrType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type OrderType int64

const (
	OrderType_ASC  OrderType = 1
	OrderType_DESC OrderType = 2
)

func (p OrderType) String() string {
	switch p {
	case OrderType_ASC:
		return "ASC"
	case OrderType_DESC:
		return "DESC"
	}
	return "<UNSET>"
}

func OrderTypeFromString(s string) (OrderType, error) {
	switch s {
	case "ASC":
		return OrderType_ASC, nil
	case "DESC":
		return OrderType_DESC, nil
	}
	return OrderType(0), fmt.Errorf("not a valid OrderType string")
}

func OrderTypePtr(v OrderType) *OrderType { return &v }
func (p *OrderType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OrderType(result.Int64)
	return
}

func (p *OrderType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ParamOperator int64

const (
	ParamOperator_AND ParamOperator = 1
	ParamOperator_OR  ParamOperator = 2
)

func (p ParamOperator) String() string {
	switch p {
	case ParamOperator_AND:
		return "AND"
	case ParamOperator_OR:
		return "OR"
	}
	return "<UNSET>"
}

func ParamOperatorFromString(s string) (ParamOperator, error) {
	switch s {
	case "AND":
		return ParamOperator_AND, nil
	case "OR":
		return ParamOperator_OR, nil
	}
	return ParamOperator(0), fmt.Errorf("not a valid ParamOperator string")
}

func ParamOperatorPtr(v ParamOperator) *ParamOperator { return &v }
func (p *ParamOperator) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ParamOperator(result.Int64)
	return
}

func (p *ParamOperator) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ParamItemOperator int64

const (
	ParamItemOperator_EQUAL     ParamItemOperator = 1
	ParamItemOperator_NOT_EQUAL ParamItemOperator = 2
	ParamItemOperator_LT        ParamItemOperator = 3
	ParamItemOperator_GT        ParamItemOperator = 4
	ParamItemOperator_LTE       ParamItemOperator = 5
	ParamItemOperator_GTE       ParamItemOperator = 6
	ParamItemOperator_LIKE      ParamItemOperator = 7
	ParamItemOperator_IN        ParamItemOperator = 8
	ParamItemOperator_NOT_IN    ParamItemOperator = 9
)

func (p ParamItemOperator) String() string {
	switch p {
	case ParamItemOperator_EQUAL:
		return "EQUAL"
	case ParamItemOperator_NOT_EQUAL:
		return "NOT_EQUAL"
	case ParamItemOperator_LT:
		return "LT"
	case ParamItemOperator_GT:
		return "GT"
	case ParamItemOperator_LTE:
		return "LTE"
	case ParamItemOperator_GTE:
		return "GTE"
	case ParamItemOperator_LIKE:
		return "LIKE"
	case ParamItemOperator_IN:
		return "IN"
	case ParamItemOperator_NOT_IN:
		return "NOT_IN"
	}
	return "<UNSET>"
}

func ParamItemOperatorFromString(s string) (ParamItemOperator, error) {
	switch s {
	case "EQUAL":
		return ParamItemOperator_EQUAL, nil
	case "NOT_EQUAL":
		return ParamItemOperator_NOT_EQUAL, nil
	case "LT":
		return ParamItemOperator_LT, nil
	case "GT":
		return ParamItemOperator_GT, nil
	case "LTE":
		return ParamItemOperator_LTE, nil
	case "GTE":
		return ParamItemOperator_GTE, nil
	case "LIKE":
		return ParamItemOperator_LIKE, nil
	case "IN":
		return ParamItemOperator_IN, nil
	case "NOT_IN":
		return ParamItemOperator_NOT_IN, nil
	}
	return ParamItemOperator(0), fmt.Errorf("not a valid ParamItemOperator string")
}

func ParamItemOperatorPtr(v ParamItemOperator) *ParamItemOperator { return &v }
func (p *ParamItemOperator) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ParamItemOperator(result.Int64)
	return
}

func (p *ParamItemOperator) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type StatusType int64

const (
	StatusType_OnLine  StatusType = 1
	StatusType_Offline StatusType = 2
)

func (p StatusType) String() string {
	switch p {
	case StatusType_OnLine:
		return "OnLine"
	case StatusType_Offline:
		return "Offline"
	}
	return "<UNSET>"
}

func StatusTypeFromString(s string) (StatusType, error) {
	switch s {
	case "OnLine":
		return StatusType_OnLine, nil
	case "Offline":
		return StatusType_Offline, nil
	}
	return StatusType(0), fmt.Errorf("not a valid StatusType string")
}

func StatusTypePtr(v StatusType) *StatusType { return &v }
func (p *StatusType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = StatusType(result.Int64)
	return
}

func (p *StatusType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ExtendType int64

const (
	ExtendType_STRING ExtendType = 1
	ExtendType_MAP    ExtendType = 2
)

func (p ExtendType) String() string {
	switch p {
	case ExtendType_STRING:
		return "STRING"
	case ExtendType_MAP:
		return "MAP"
	}
	return "<UNSET>"
}

func ExtendTypeFromString(s string) (ExtendType, error) {
	switch s {
	case "STRING":
		return ExtendType_STRING, nil
	case "MAP":
		return ExtendType_MAP, nil
	}
	return ExtendType(0), fmt.Errorf("not a valid ExtendType string")
}

func ExtendTypePtr(v ExtendType) *ExtendType { return &v }
func (p *ExtendType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ExtendType(result.Int64)
	return
}

func (p *ExtendType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type Credential struct {
	AppKey    string `thrift:"app_key,1" frugal:"1,default,string" json:"app_key"`
	AppSecret string `thrift:"app_secret,2" frugal:"2,default,string" json:"app_secret"`
}

func NewCredential() *Credential {
	return &Credential{}
}

func (p *Credential) InitDefault() {
}

func (p *Credential) GetAppKey() (v string) {
	return p.AppKey
}

func (p *Credential) GetAppSecret() (v string) {
	return p.AppSecret
}
func (p *Credential) SetAppKey(val string) {
	p.AppKey = val
}
func (p *Credential) SetAppSecret(val string) {
	p.AppSecret = val
}

var fieldIDToName_Credential = map[int16]string{
	1: "app_key",
	2: "app_secret",
}

func (p *Credential) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("Credential")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Credential[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Credential) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppKey = _field
	return nil
}
func (p *Credential) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AppSecret = _field
	return nil
}

func (p *Credential) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("Credential")

	var fieldId int16
	if err = oprot.WriteStructBegin("Credential"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Credential) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_key", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppKey); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Credential) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("app_secret", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AppSecret); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *Credential) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Credential(%+v)", *p)

}

type FieldValue struct {
	ValueType    FieldValueType `thrift:"ValueType,1" frugal:"1,default,FieldValueType" json:"ValueType"`
	StringValue  *string        `thrift:"StringValue,2,optional" frugal:"2,optional,string" json:"StringValue,omitempty"`
	Int64Value   *int64         `thrift:"Int64Value,3,optional" frugal:"3,optional,i64" json:"Int64Value,omitempty"`
	BooleanValue *bool          `thrift:"BooleanValue,4,optional" frugal:"4,optional,bool" json:"BooleanValue,omitempty"`
}

func NewFieldValue() *FieldValue {
	return &FieldValue{}
}

func (p *FieldValue) InitDefault() {
}

func (p *FieldValue) GetValueType() (v FieldValueType) {
	return p.ValueType
}

var FieldValue_StringValue_DEFAULT string

func (p *FieldValue) GetStringValue() (v string) {
	if !p.IsSetStringValue() {
		return FieldValue_StringValue_DEFAULT
	}
	return *p.StringValue
}

var FieldValue_Int64Value_DEFAULT int64

func (p *FieldValue) GetInt64Value() (v int64) {
	if !p.IsSetInt64Value() {
		return FieldValue_Int64Value_DEFAULT
	}
	return *p.Int64Value
}

var FieldValue_BooleanValue_DEFAULT bool

func (p *FieldValue) GetBooleanValue() (v bool) {
	if !p.IsSetBooleanValue() {
		return FieldValue_BooleanValue_DEFAULT
	}
	return *p.BooleanValue
}
func (p *FieldValue) SetValueType(val FieldValueType) {
	p.ValueType = val
}
func (p *FieldValue) SetStringValue(val *string) {
	p.StringValue = val
}
func (p *FieldValue) SetInt64Value(val *int64) {
	p.Int64Value = val
}
func (p *FieldValue) SetBooleanValue(val *bool) {
	p.BooleanValue = val
}

var fieldIDToName_FieldValue = map[int16]string{
	1: "ValueType",
	2: "StringValue",
	3: "Int64Value",
	4: "BooleanValue",
}

func (p *FieldValue) IsSetStringValue() bool {
	return p.StringValue != nil
}

func (p *FieldValue) IsSetInt64Value() bool {
	return p.Int64Value != nil
}

func (p *FieldValue) IsSetBooleanValue() bool {
	return p.BooleanValue != nil
}

func (p *FieldValue) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FieldValue")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_FieldValue[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *FieldValue) ReadField1(iprot thrift.TProtocol) error {

	var _field FieldValueType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = FieldValueType(v)
	}
	p.ValueType = _field
	return nil
}
func (p *FieldValue) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StringValue = _field
	return nil
}
func (p *FieldValue) ReadField3(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Int64Value = _field
	return nil
}
func (p *FieldValue) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BooleanValue = _field
	return nil
}

func (p *FieldValue) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("FieldValue")

	var fieldId int16
	if err = oprot.WriteStructBegin("FieldValue"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *FieldValue) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ValueType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ValueType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *FieldValue) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStringValue() {
		if err = oprot.WriteFieldBegin("StringValue", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.StringValue); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *FieldValue) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInt64Value() {
		if err = oprot.WriteFieldBegin("Int64Value", thrift.I64, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.Int64Value); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *FieldValue) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetBooleanValue() {
		if err = oprot.WriteFieldBegin("BooleanValue", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.BooleanValue); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *FieldValue) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FieldValue(%+v)", *p)

}

type SimpleResponse struct {
	Success  bool           `thrift:"success,1" frugal:"1,default,bool" json:"success"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255" frugal:"255,default,base.BaseResp" json:"BaseResp"`
}

func NewSimpleResponse() *SimpleResponse {
	return &SimpleResponse{}
}

func (p *SimpleResponse) InitDefault() {
}

func (p *SimpleResponse) GetSuccess() (v bool) {
	return p.Success
}

var SimpleResponse_BaseResp_DEFAULT *base.BaseResp

func (p *SimpleResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return SimpleResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}
func (p *SimpleResponse) SetSuccess(val bool) {
	p.Success = val
}
func (p *SimpleResponse) SetBaseResp(val *base.BaseResp) {
	p.BaseResp = val
}

var fieldIDToName_SimpleResponse = map[int16]string{
	1:   "success",
	255: "BaseResp",
}

func (p *SimpleResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *SimpleResponse) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("SimpleResponse")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SimpleResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SimpleResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}
func (p *SimpleResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *SimpleResponse) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("SimpleResponse")

	var fieldId int16
	if err = oprot.WriteStructBegin("SimpleResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SimpleResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SimpleResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *SimpleResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SimpleResponse(%+v)", *p)

}

type AttrField struct {
	Name  string   `thrift:"name,1" frugal:"1,default,string" json:"name"`
	Type  AttrType `thrift:"type,2" frugal:"2,default,AttrType" json:"type"`
	Value string   `thrift:"value,3" frugal:"3,default,string" json:"value"`
}

func NewAttrField() *AttrField {
	return &AttrField{}
}

func (p *AttrField) InitDefault() {
}

func (p *AttrField) GetName() (v string) {
	return p.Name
}

func (p *AttrField) GetType() (v AttrType) {
	return p.Type
}

func (p *AttrField) GetValue() (v string) {
	return p.Value
}
func (p *AttrField) SetName(val string) {
	p.Name = val
}
func (p *AttrField) SetType(val AttrType) {
	p.Type = val
}
func (p *AttrField) SetValue(val string) {
	p.Value = val
}

var fieldIDToName_AttrField = map[int16]string{
	1: "name",
	2: "type",
	3: "value",
}

func (p *AttrField) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AttrField")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AttrField[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AttrField) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *AttrField) ReadField2(iprot thrift.TProtocol) error {

	var _field AttrType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = AttrType(v)
	}
	p.Type = _field
	return nil
}
func (p *AttrField) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Value = _field
	return nil
}

func (p *AttrField) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AttrField")

	var fieldId int16
	if err = oprot.WriteStructBegin("AttrField"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AttrField) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AttrField) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *AttrField) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("value", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Value); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AttrField) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AttrField(%+v)", *p)

}

type AttrOrderItem struct {
	Field *AttrField `thrift:"field,1" frugal:"1,default,AttrField" json:"field"`
	Type  OrderType  `thrift:"type,2" frugal:"2,default,OrderType" json:"type"`
}

func NewAttrOrderItem() *AttrOrderItem {
	return &AttrOrderItem{}
}

func (p *AttrOrderItem) InitDefault() {
}

var AttrOrderItem_Field_DEFAULT *AttrField

func (p *AttrOrderItem) GetField() (v *AttrField) {
	if !p.IsSetField() {
		return AttrOrderItem_Field_DEFAULT
	}
	return p.Field
}

func (p *AttrOrderItem) GetType() (v OrderType) {
	return p.Type
}
func (p *AttrOrderItem) SetField(val *AttrField) {
	p.Field = val
}
func (p *AttrOrderItem) SetType(val OrderType) {
	p.Type = val
}

var fieldIDToName_AttrOrderItem = map[int16]string{
	1: "field",
	2: "type",
}

func (p *AttrOrderItem) IsSetField() bool {
	return p.Field != nil
}

func (p *AttrOrderItem) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AttrOrderItem")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AttrOrderItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AttrOrderItem) ReadField1(iprot thrift.TProtocol) error {
	_field := NewAttrField()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Field = _field
	return nil
}
func (p *AttrOrderItem) ReadField2(iprot thrift.TProtocol) error {

	var _field OrderType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = OrderType(v)
	}
	p.Type = _field
	return nil
}

func (p *AttrOrderItem) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AttrOrderItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("AttrOrderItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AttrOrderItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("field", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Field.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AttrOrderItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Type)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AttrOrderItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AttrOrderItem(%+v)", *p)

}

type AttrParamItem struct {
	Operator   ParamItemOperator `thrift:"operator,1" frugal:"1,default,ParamItemOperator" json:"operator"`
	ParamName  string            `thrift:"param_name,2" frugal:"2,default,string" json:"param_name"`
	ParamValue string            `thrift:"param_value,3" frugal:"3,default,string" json:"param_value"`
}

func NewAttrParamItem() *AttrParamItem {
	return &AttrParamItem{}
}

func (p *AttrParamItem) InitDefault() {
}

func (p *AttrParamItem) GetOperator() (v ParamItemOperator) {
	return p.Operator
}

func (p *AttrParamItem) GetParamName() (v string) {
	return p.ParamName
}

func (p *AttrParamItem) GetParamValue() (v string) {
	return p.ParamValue
}
func (p *AttrParamItem) SetOperator(val ParamItemOperator) {
	p.Operator = val
}
func (p *AttrParamItem) SetParamName(val string) {
	p.ParamName = val
}
func (p *AttrParamItem) SetParamValue(val string) {
	p.ParamValue = val
}

var fieldIDToName_AttrParamItem = map[int16]string{
	1: "operator",
	2: "param_name",
	3: "param_value",
}

func (p *AttrParamItem) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AttrParamItem")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AttrParamItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AttrParamItem) ReadField1(iprot thrift.TProtocol) error {

	var _field ParamItemOperator
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ParamItemOperator(v)
	}
	p.Operator = _field
	return nil
}
func (p *AttrParamItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ParamName = _field
	return nil
}
func (p *AttrParamItem) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ParamValue = _field
	return nil
}

func (p *AttrParamItem) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AttrParamItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("AttrParamItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AttrParamItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Operator)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AttrParamItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("param_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ParamName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *AttrParamItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("param_value", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ParamValue); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AttrParamItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AttrParamItem(%+v)", *p)

}

type AttrParam struct {
	ParamItem []*AttrParamItem `thrift:"param_item,1" frugal:"1,default,list<AttrParamItem>" json:"param_item"`
	Operator  ParamOperator    `thrift:"operator,2" frugal:"2,default,ParamOperator" json:"operator"`
	AttrParam []*AttrParam     `thrift:"attr_param,3" frugal:"3,default,list<AttrParam>" json:"attr_param"`
}

func NewAttrParam() *AttrParam {
	return &AttrParam{}
}

func (p *AttrParam) InitDefault() {
}

func (p *AttrParam) GetParamItem() (v []*AttrParamItem) {
	return p.ParamItem
}

func (p *AttrParam) GetOperator() (v ParamOperator) {
	return p.Operator
}

func (p *AttrParam) GetAttrParam() (v []*AttrParam) {
	return p.AttrParam
}
func (p *AttrParam) SetParamItem(val []*AttrParamItem) {
	p.ParamItem = val
}
func (p *AttrParam) SetOperator(val ParamOperator) {
	p.Operator = val
}
func (p *AttrParam) SetAttrParam(val []*AttrParam) {
	p.AttrParam = val
}

var fieldIDToName_AttrParam = map[int16]string{
	1: "param_item",
	2: "operator",
	3: "attr_param",
}

func (p *AttrParam) Read(iprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AttrParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AttrParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AttrParam) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AttrParamItem, 0, size)
	values := make([]AttrParamItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ParamItem = _field
	return nil
}
func (p *AttrParam) ReadField2(iprot thrift.TProtocol) error {

	var _field ParamOperator
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ParamOperator(v)
	}
	p.Operator = _field
	return nil
}
func (p *AttrParam) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*AttrParam, 0, size)
	values := make([]AttrParam, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.AttrParam = _field
	return nil
}

func (p *AttrParam) Write(oprot thrift.TProtocol) (err error) {
	apache_warning.WarningApache("AttrParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("AttrParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AttrParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("param_item", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ParamItem)); err != nil {
		return err
	}
	for _, v := range p.ParamItem {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AttrParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("operator", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Operator)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *AttrParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("attr_param", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AttrParam)); err != nil {
		return err
	}
	for _, v := range p.AttrParam {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AttrParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AttrParam(%+v)", *p)

}
