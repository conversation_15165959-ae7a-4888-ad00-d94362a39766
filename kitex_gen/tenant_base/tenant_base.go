// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package tenant_base

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
)

type TenantType int64

const (
	TenantType_Dealer              TenantType = 1
	TenantType_SubDealer           TenantType = 2
	TenantType_FinancialLease      TenantType = 3
	TenantType_Manufacturer        TenantType = 4
	TenantType_Group               TenantType = 5
	TenantType_FlagshipStore       TenantType = 6
	TenantType_Custom              TenantType = 7
	TenantType_Platform            TenantType = 8
	TenantType_SecondHand          TenantType = 9
	TenantType_MarketAD            TenantType = 10
	TenantType_AfterSales          TenantType = 11
	TenantType_Trade               TenantType = 12
	TenantType_ExternalPlatform    TenantType = 13
	TenantType_ThirdAdx            TenantType = 14
	TenantType_Finance             TenantType = 15
	TenantType_AdDsp               TenantType = 16
	TenantType_CISN                TenantType = 17
	TenantType_Retail              TenantType = 18
	TenantType_EWDMS               TenantType = 19
	TenantType_Open                TenantType = 20
	TenantType_Auction             TenantType = 21
	TenantType_SecondHandTrade     TenantType = 22
	TenantType_GOVERNMENT          TenantType = 23
	TenantType_AiPlus              TenantType = 24
	TenantType_DDT                 TenantType = 25
	TenantType_UgUnion             TenantType = 26
	TenantType_ECN                 TenantType = 27
	TenantType_AfterMarket         TenantType = 28
	TenantType_AutoEngineBI        TenantType = 29
	TenantType_Agent               TenantType = 30
	TenantType_ShInspection        TenantType = 31
	TenantType_NewCarTrade         TenantType = 33
	TenantType_AfterMarketSupply   TenantType = 34
	TenantType_FinanceSaaSZF       TenantType = 35
	TenantType_MarketShop          TenantType = 36
	TenantType_MctSale             TenantType = 37
	TenantType_CarScm              TenantType = 38
	TenantType_SecondHandAuction   TenantType = 39
	TenantType_TradeScm            TenantType = 40
	TenantType_CustomerManage      TenantType = 41
	TenantType_CollectCar          TenantType = 42
	TenantType_CarSupply           TenantType = 43
	TenantType_NewCarEcom          TenantType = 44
	TenantType_AuctionCollectCar   TenantType = 45
	TenantType_PersonCollectCar    TenantType = 46
	TenantType_WmsSaaS             TenantType = 47
	TenantType_AutoEngineInnerBI   TenantType = 48
	TenantType_SecondHandDBox      TenantType = 49
	TenantType_TmsSaaS             TenantType = 50
	TenantType_GovernmentWriteoff  TenantType = 51
	TenantType_LiveSaaS            TenantType = 52
	TenantType_MotorcycleSaas      TenantType = 53
	TenantType_SupplyAdmin         TenantType = 54
	TenantType_InsurancePlatform   TenantType = 55
	TenantType_WmsOuterSystem      TenantType = 56
	TenantType_NewCarEcomSale      TenantType = 57
	TenantType_FSInsight           TenantType = 58
	TenantType_FactorySaaS         TenantType = 59
	TenantType_DEMatrix            TenantType = 60
	TenantType_ServiceProvider     TenantType = 61
	TenantType_BlueBird            TenantType = 62
	TenantType_ServiceMarket       TenantType = 63
	TenantType_DongCheHao          TenantType = 64
	TenantType_BpiDcdAd            TenantType = 65
	TenantType_AutoPrd             TenantType = 66
	TenantType_MembershipAgent     TenantType = 67
	TenantType_MallChehou          TenantType = 68
	TenantType_CarResourceProvider TenantType = 69
	TenantType_AdMatch             TenantType = 70
	TenantType_RepurchaseGoods     TenantType = 71
	TenantType_ChargeStation       TenantType = 72
	TenantType_Vision              TenantType = 73
	TenantType_LeadsCenter         TenantType = 100
	TenantType_FweTrade            TenantType = 101
	TenantType_FwePlatform         TenantType = 102
	TenantType_FweSampleSaaS       TenantType = 103
	TenantType_FuHuaHF             TenantType = 104
	TenantType_BPICustomer         TenantType = 105
	TenantType_IM                  TenantType = 106
	TenantType_BPIContract         TenantType = 107
	TenantType_BPICredit           TenantType = 108
	TenantType_BPISettlement       TenantType = 109
	TenantType_DcarAdmin           TenantType = 110
	TenantType_BPIRebate           TenantType = 111
	TenantType_BPIDeposit          TenantType = 112
	TenantType_ShCustomerEnd       TenantType = 113
	TenantType_EE                  TenantType = 114
)

func (p TenantType) String() string {
	switch p {
	case TenantType_Dealer:
		return "Dealer"
	case TenantType_SubDealer:
		return "SubDealer"
	case TenantType_FinancialLease:
		return "FinancialLease"
	case TenantType_Manufacturer:
		return "Manufacturer"
	case TenantType_Group:
		return "Group"
	case TenantType_FlagshipStore:
		return "FlagshipStore"
	case TenantType_Custom:
		return "Custom"
	case TenantType_Platform:
		return "Platform"
	case TenantType_SecondHand:
		return "SecondHand"
	case TenantType_MarketAD:
		return "MarketAD"
	case TenantType_AfterSales:
		return "AfterSales"
	case TenantType_Trade:
		return "Trade"
	case TenantType_ExternalPlatform:
		return "ExternalPlatform"
	case TenantType_ThirdAdx:
		return "ThirdAdx"
	case TenantType_Finance:
		return "Finance"
	case TenantType_AdDsp:
		return "AdDsp"
	case TenantType_CISN:
		return "CISN"
	case TenantType_Retail:
		return "Retail"
	case TenantType_EWDMS:
		return "EWDMS"
	case TenantType_Open:
		return "Open"
	case TenantType_Auction:
		return "Auction"
	case TenantType_SecondHandTrade:
		return "SecondHandTrade"
	case TenantType_GOVERNMENT:
		return "GOVERNMENT"
	case TenantType_AiPlus:
		return "AiPlus"
	case TenantType_DDT:
		return "DDT"
	case TenantType_UgUnion:
		return "UgUnion"
	case TenantType_ECN:
		return "ECN"
	case TenantType_AfterMarket:
		return "AfterMarket"
	case TenantType_AutoEngineBI:
		return "AutoEngineBI"
	case TenantType_Agent:
		return "Agent"
	case TenantType_ShInspection:
		return "ShInspection"
	case TenantType_NewCarTrade:
		return "NewCarTrade"
	case TenantType_AfterMarketSupply:
		return "AfterMarketSupply"
	case TenantType_FinanceSaaSZF:
		return "FinanceSaaSZF"
	case TenantType_MarketShop:
		return "MarketShop"
	case TenantType_MctSale:
		return "MctSale"
	case TenantType_CarScm:
		return "CarScm"
	case TenantType_SecondHandAuction:
		return "SecondHandAuction"
	case TenantType_TradeScm:
		return "TradeScm"
	case TenantType_CustomerManage:
		return "CustomerManage"
	case TenantType_CollectCar:
		return "CollectCar"
	case TenantType_CarSupply:
		return "CarSupply"
	case TenantType_NewCarEcom:
		return "NewCarEcom"
	case TenantType_AuctionCollectCar:
		return "AuctionCollectCar"
	case TenantType_PersonCollectCar:
		return "PersonCollectCar"
	case TenantType_WmsSaaS:
		return "WmsSaaS"
	case TenantType_AutoEngineInnerBI:
		return "AutoEngineInnerBI"
	case TenantType_SecondHandDBox:
		return "SecondHandDBox"
	case TenantType_TmsSaaS:
		return "TmsSaaS"
	case TenantType_GovernmentWriteoff:
		return "GovernmentWriteoff"
	case TenantType_LiveSaaS:
		return "LiveSaaS"
	case TenantType_MotorcycleSaas:
		return "MotorcycleSaas"
	case TenantType_SupplyAdmin:
		return "SupplyAdmin"
	case TenantType_InsurancePlatform:
		return "InsurancePlatform"
	case TenantType_WmsOuterSystem:
		return "WmsOuterSystem"
	case TenantType_NewCarEcomSale:
		return "NewCarEcomSale"
	case TenantType_FSInsight:
		return "FSInsight"
	case TenantType_FactorySaaS:
		return "FactorySaaS"
	case TenantType_DEMatrix:
		return "DEMatrix"
	case TenantType_ServiceProvider:
		return "ServiceProvider"
	case TenantType_BlueBird:
		return "BlueBird"
	case TenantType_ServiceMarket:
		return "ServiceMarket"
	case TenantType_DongCheHao:
		return "DongCheHao"
	case TenantType_BpiDcdAd:
		return "BpiDcdAd"
	case TenantType_AutoPrd:
		return "AutoPrd"
	case TenantType_MembershipAgent:
		return "MembershipAgent"
	case TenantType_MallChehou:
		return "MallChehou"
	case TenantType_CarResourceProvider:
		return "CarResourceProvider"
	case TenantType_AdMatch:
		return "AdMatch"
	case TenantType_RepurchaseGoods:
		return "RepurchaseGoods"
	case TenantType_ChargeStation:
		return "ChargeStation"
	case TenantType_Vision:
		return "Vision"
	case TenantType_LeadsCenter:
		return "LeadsCenter"
	case TenantType_FweTrade:
		return "FweTrade"
	case TenantType_FwePlatform:
		return "FwePlatform"
	case TenantType_FweSampleSaaS:
		return "FweSampleSaaS"
	case TenantType_FuHuaHF:
		return "FuHuaHF"
	case TenantType_BPICustomer:
		return "BPICustomer"
	case TenantType_IM:
		return "IM"
	case TenantType_BPIContract:
		return "BPIContract"
	case TenantType_BPICredit:
		return "BPICredit"
	case TenantType_BPISettlement:
		return "BPISettlement"
	case TenantType_DcarAdmin:
		return "DcarAdmin"
	case TenantType_BPIRebate:
		return "BPIRebate"
	case TenantType_BPIDeposit:
		return "BPIDeposit"
	case TenantType_ShCustomerEnd:
		return "ShCustomerEnd"
	case TenantType_EE:
		return "EE"
	}
	return "<UNSET>"
}

func TenantTypeFromString(s string) (TenantType, error) {
	switch s {
	case "Dealer":
		return TenantType_Dealer, nil
	case "SubDealer":
		return TenantType_SubDealer, nil
	case "FinancialLease":
		return TenantType_FinancialLease, nil
	case "Manufacturer":
		return TenantType_Manufacturer, nil
	case "Group":
		return TenantType_Group, nil
	case "FlagshipStore":
		return TenantType_FlagshipStore, nil
	case "Custom":
		return TenantType_Custom, nil
	case "Platform":
		return TenantType_Platform, nil
	case "SecondHand":
		return TenantType_SecondHand, nil
	case "MarketAD":
		return TenantType_MarketAD, nil
	case "AfterSales":
		return TenantType_AfterSales, nil
	case "Trade":
		return TenantType_Trade, nil
	case "ExternalPlatform":
		return TenantType_ExternalPlatform, nil
	case "ThirdAdx":
		return TenantType_ThirdAdx, nil
	case "Finance":
		return TenantType_Finance, nil
	case "AdDsp":
		return TenantType_AdDsp, nil
	case "CISN":
		return TenantType_CISN, nil
	case "Retail":
		return TenantType_Retail, nil
	case "EWDMS":
		return TenantType_EWDMS, nil
	case "Open":
		return TenantType_Open, nil
	case "Auction":
		return TenantType_Auction, nil
	case "SecondHandTrade":
		return TenantType_SecondHandTrade, nil
	case "GOVERNMENT":
		return TenantType_GOVERNMENT, nil
	case "AiPlus":
		return TenantType_AiPlus, nil
	case "DDT":
		return TenantType_DDT, nil
	case "UgUnion":
		return TenantType_UgUnion, nil
	case "ECN":
		return TenantType_ECN, nil
	case "AfterMarket":
		return TenantType_AfterMarket, nil
	case "AutoEngineBI":
		return TenantType_AutoEngineBI, nil
	case "Agent":
		return TenantType_Agent, nil
	case "ShInspection":
		return TenantType_ShInspection, nil
	case "NewCarTrade":
		return TenantType_NewCarTrade, nil
	case "AfterMarketSupply":
		return TenantType_AfterMarketSupply, nil
	case "FinanceSaaSZF":
		return TenantType_FinanceSaaSZF, nil
	case "MarketShop":
		return TenantType_MarketShop, nil
	case "MctSale":
		return TenantType_MctSale, nil
	case "CarScm":
		return TenantType_CarScm, nil
	case "SecondHandAuction":
		return TenantType_SecondHandAuction, nil
	case "TradeScm":
		return TenantType_TradeScm, nil
	case "CustomerManage":
		return TenantType_CustomerManage, nil
	case "CollectCar":
		return TenantType_CollectCar, nil
	case "CarSupply":
		return TenantType_CarSupply, nil
	case "NewCarEcom":
		return TenantType_NewCarEcom, nil
	case "AuctionCollectCar":
		return TenantType_AuctionCollectCar, nil
	case "PersonCollectCar":
		return TenantType_PersonCollectCar, nil
	case "WmsSaaS":
		return TenantType_WmsSaaS, nil
	case "AutoEngineInnerBI":
		return TenantType_AutoEngineInnerBI, nil
	case "SecondHandDBox":
		return TenantType_SecondHandDBox, nil
	case "TmsSaaS":
		return TenantType_TmsSaaS, nil
	case "GovernmentWriteoff":
		return TenantType_GovernmentWriteoff, nil
	case "LiveSaaS":
		return TenantType_LiveSaaS, nil
	case "MotorcycleSaas":
		return TenantType_MotorcycleSaas, nil
	case "SupplyAdmin":
		return TenantType_SupplyAdmin, nil
	case "InsurancePlatform":
		return TenantType_InsurancePlatform, nil
	case "WmsOuterSystem":
		return TenantType_WmsOuterSystem, nil
	case "NewCarEcomSale":
		return TenantType_NewCarEcomSale, nil
	case "FSInsight":
		return TenantType_FSInsight, nil
	case "FactorySaaS":
		return TenantType_FactorySaaS, nil
	case "DEMatrix":
		return TenantType_DEMatrix, nil
	case "ServiceProvider":
		return TenantType_ServiceProvider, nil
	case "BlueBird":
		return TenantType_BlueBird, nil
	case "ServiceMarket":
		return TenantType_ServiceMarket, nil
	case "DongCheHao":
		return TenantType_DongCheHao, nil
	case "BpiDcdAd":
		return TenantType_BpiDcdAd, nil
	case "AutoPrd":
		return TenantType_AutoPrd, nil
	case "MembershipAgent":
		return TenantType_MembershipAgent, nil
	case "MallChehou":
		return TenantType_MallChehou, nil
	case "CarResourceProvider":
		return TenantType_CarResourceProvider, nil
	case "AdMatch":
		return TenantType_AdMatch, nil
	case "RepurchaseGoods":
		return TenantType_RepurchaseGoods, nil
	case "ChargeStation":
		return TenantType_ChargeStation, nil
	case "Vision":
		return TenantType_Vision, nil
	case "LeadsCenter":
		return TenantType_LeadsCenter, nil
	case "FweTrade":
		return TenantType_FweTrade, nil
	case "FwePlatform":
		return TenantType_FwePlatform, nil
	case "FweSampleSaaS":
		return TenantType_FweSampleSaaS, nil
	case "FuHuaHF":
		return TenantType_FuHuaHF, nil
	case "BPICustomer":
		return TenantType_BPICustomer, nil
	case "IM":
		return TenantType_IM, nil
	case "BPIContract":
		return TenantType_BPIContract, nil
	case "BPICredit":
		return TenantType_BPICredit, nil
	case "BPISettlement":
		return TenantType_BPISettlement, nil
	case "DcarAdmin":
		return TenantType_DcarAdmin, nil
	case "BPIRebate":
		return TenantType_BPIRebate, nil
	case "BPIDeposit":
		return TenantType_BPIDeposit, nil
	case "ShCustomerEnd":
		return TenantType_ShCustomerEnd, nil
	case "EE":
		return TenantType_EE, nil
	}
	return TenantType(0), fmt.Errorf("not a valid TenantType string")
}

func TenantTypePtr(v TenantType) *TenantType { return &v }
func (p *TenantType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TenantType(result.Int64)
	return
}

func (p *TenantType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}
