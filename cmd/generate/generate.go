package main

import (
	"code.byted.org/gorm/bytedgen"
	"code.byted.org/gorm/bytedgorm"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

// generate code
func main() {
	// init db

	// todo delete
	db, err := gorm.Open(
		mysql.Open("m_609797_a_w:s1gUajQksALy3Lq_f70oC3WxydrYY4B7@tcp(fdbd:dc03:ff:501:1378:50de:83a7:6aa1)/motor_trade_infra"),
		bytedgorm.WithDefaults(),
		bytedgorm.Logger{
			IgnoreRecordNotFoundError: true,
		},
	)
	if err != nil {
		panic(err)
	}

	//caller.InitMysqlDefault()
	//db = caller.DBDefault(context.Background())

	// specify the output directory (default: "./query")
	// ### if you want to query without context constrain, set mode gen.WithoutContext ###
	g := bytedgen.NewGenerator(gen.Config{
		OutPath:      "/Users/<USER>/go/src/code.byted.org/motor/fwe_trade_engine/biz/dal/db_query",
		ModelPkgPath: "/Users/<USER>/go/src/code.byted.org/motor/fwe_trade_engine/biz/model/db_model",
		//OutPath:      "../../biz/dal/db_query",
		//ModelPkgPath: "../../biz/model/db_model",
		/* Mode: gen.WithoutContext,*/
		//if you want the nullable field generation property to be pointer type, set FieldNullable true
		/* FieldNullable: true,*/
		Mode:              gen.WithDefaultQuery,
		FieldNullable:     true,
		FieldWithIndexTag: true,
		FieldWithTypeTag:  true,
	})

	// reuse the database connection in Project or create a connection here
	// if you want to use GenerateModel/GenerateModelAs, UseDB is necessray or it will panic
	g.UseDB(db)

	// apply basic crud api on structs or table models which is specified by table name with function
	// GenerateModel/GenerateModelAs. And generator will generate table models' code when calling Excute.
	g.ApplyBasic(
		g.GenerateModel("fwe_order"),
		g.GenerateModel("f_finance_order", gen.FieldIgnore("after_sale_id", "combine_order_id")),
		g.GenerateModel("fwe_order_contract"),
		g.GenerateModel("fwe_order_tag"),
		g.GenerateModel("f_order_split_info"),
		g.GenerateModel("fwe_order_log"),
		g.GenerateModel("f_ea_income"),
		g.GenerateModel("fwe_order_subject"),
		g.GenerateModel("fwe_order_debug_log"),
		g.GenerateModel("f_settle_finance_order"),
		g.GenerateModel("f_refund_finance_order"),
		//g.GenerateModel("f_charge_order"),
		//g.GenerateModel("f_charge_order_detail"),
	)

	// apply diy interfaces on structs or table models
	// g.ApplyInterface(func(method model.Method) {}, model.User{}, g.GenerateModel("company"))

	// execute the action of code generation
	g.Execute()
}
