package main

import (
	"context"

	"code.byted.org/motor/fwe_trade_engine/biz/handler"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
)

type TradeEngineServiceImpl struct{}

// CreateOrder implements the TradeEngineServiceImpl interface.
func (s *TradeEngineServiceImpl) CreateOrder(ctx context.Context, req *engine.CreateOrderReq) (resp *engine.CreateOrderResp, err error) {
	return handler.NewCreateOrderHandler().Process(ctx, req), nil
}

// ActionOrder implements the TradeEngineServiceImpl interface.
func (s *TradeEngineServiceImpl) ActionOrder(ctx context.Context, req *engine.ActionOrderReq) (resp *engine.ActionOrderResp, err error) {
	return handler.NewActionOrderHandler().Process(ctx, req), nil
}

// MGetOrderInfo implements the TradeEngineServiceImpl interface.
func (s *TradeEngineServiceImpl) MGetOrderInfo(ctx context.Context, req *engine.MGetOrderInfoReq) (resp *engine.MGetOrderInfoResp, err error) {
	return handler.NewMGetOrderHandler().Process(ctx, req), nil
}

// GetOrderLog implements the TradeEngineServiceImpl interface.
func (s *TradeEngineServiceImpl) GetOrderLog(ctx context.Context, req *engine.GetOrderLogReq) (resp *engine.GetOrderLogResp, err error) {
	return handler.NewGetOrderLogHandler().Process(ctx, req), nil
}

// BindUserOrder implements the TradeEngineServiceImpl interface.
func (s *TradeEngineServiceImpl) BindUserOrder(ctx context.Context, req *engine.BindUserOrderReq) (resp *engine.BindUserOrderResp, err error) {
	return handler.NewBindUserOrderHandler().Process(ctx, req), nil
}

// GetOrderInfo implements the TradeEngineServiceImpl interface.
func (s *TradeEngineServiceImpl) GetOrderInfo(ctx context.Context, req *engine.GetOrderInfoReq) (resp *engine.OrderInfoDetailResp, err error) {
	return handler.NewGetOrderInfoHandler().Process(ctx, req), nil
}

// UpdateOrderProduct implements the TradeEngineServiceImpl interface.
func (s *TradeEngineServiceImpl) UpdateOrderProduct(ctx context.Context, req *engine.UpdateOrderProductReq) (resp *engine.UpdateOrderProductResp, err error) {
	return handler.NewUpdateOrderProductHandler().Process(ctx, req), nil
}

// RepostOrderMessage implements the TradeEngineServiceImpl interface.
func (s *TradeEngineServiceImpl) RepostOrderMessage(ctx context.Context, req *engine.RepostOrderMessageReq) (resp *engine.RepostOrderMessageResp, err error) {
	return handler.NewRepostOrderMessageHandler().Process(ctx, req), nil
}
