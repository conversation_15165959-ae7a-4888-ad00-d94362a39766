package main

import (
	"code.byted.org/motor/fwe_ecom_product_common/util/util_location"
	"log"

	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/server"
	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/dal"
	"code.byted.org/motor/fwe_trade_engine/biz/middleware"
	"code.byted.org/motor/fwe_trade_engine/biz/statemachine"
	"code.byted.org/motor/fwe_trade_engine/biz/utils"
	engine "code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine/tradeengineservice"
	"code.byted.org/security/sensitive_finder_engine"
)

func Init() {
	caller.Init()
	dal.Init()
	statemachine.Init()
	utils.Init()
	util_location.Init()

}

func main() {
	var err error
	svr := engine.NewServer(new(TradeEngineServiceImpl), server.WithMiddleware(middleware.LogMiddleware))
	err = logs.DefaultLogger().AddProcessor(sensitive_finder_engine.SecdataEngineSensitiveLogsProcessor()) // 手机号等敏感信息日志打印脱敏
	if err != nil {
		panic(err)
	}
	Init()
	err = svr.Run()
	if err != nil {
		log.Println(err.Error())
	}
}
