package main

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/bfsm"
	"code.byted.org/motor/fwe_trade_common/statemachine/nc_state/nc_ecom"
	"context"
	"fmt"
	"github.com/apaxa-go/helper/strconvh"
	"github.com/tidwall/gjson"
	"testing"
	"time"
	"unicode/utf8"

	"code.byted.org/motor/fwe_trade_engine/biz/model/action_model"

	"code.byted.org/motor/fwe_trade_engine/biz/caller"
	"code.byted.org/motor/fwe_trade_engine/biz/consts"
	"code.byted.org/motor/fwe_trade_engine/biz/dal"
	"code.byted.org/motor/fwe_trade_engine/biz/dal/db_query"
	"code.byted.org/motor/fwe_trade_engine/biz/service"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade/engine"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/motor/fwe_trade_common"
	"code.byted.org/motor/fwe_trade_engine/kitex_gen/tenant_base"
	"code.byted.org/motor/gopkg/tools"
	"github.com/bytedance/sonic"
	"gorm.io/gorm"
)

/*func init() {
	caller.Init()
	dal.Init()
	statemachine.Init()
	utils.Init()
}*/

func getIdentity() *fwe_trade_common.BizIdentity {
	return &fwe_trade_common.BizIdentity{
		BizScene:   consts.BizSceneSHBuyPersonCar.Int32(),
		TenantType: tenant_base.TenantType_SecondHandTrade,
	}
}

func getOperator() *fwe_trade_common.OperatorInfo {
	return &fwe_trade_common.OperatorInfo{
		OperatorID:   "郭晓东id",
		OperatorName: "郭晓东name",
	}
}

func TestMGetOrderInfo(t *testing.T) {
	Init()
	var (
		impl = &TradeEngineServiceImpl{}
		ctx  = context.Background()
	)
	req := &engine.MGetOrderInfoReq{
		OrderIds: []string{"7124216867798929452"},
		Identity: getIdentity(),
	}
	rsp, err := impl.MGetOrderInfo(ctx, req)
	fmt.Println(tools.GetLogStr(rsp), err)
}

// doas -p motor.fwe_trade.engine go test -v  -run "TestGetSplitInfos" handler_test.go
func TestGetSplitInfos(t *testing.T) {
	ctx := context.Background()
	infos, err := service.NewSplitInfoService().GetSplitInfosByOrderID(ctx, "2233")
	fmt.Println(tools.GetLogStr(infos), err)
	time.Sleep(1 * time.Second)
}

func TestUpdateExtra(t *testing.T) {
	Init()
	var (
		impl = &TradeEngineServiceImpl{}
		ctx  = context.Background()
	)
	req := &engine.ActionOrderReq{
		OrderID:    "7135084992467587116",
		Action:     "UpdateExtra",
		BizRequest: "{\"Extra\":\"a\":\"b\"}",
		Operator:   getOperator(),
		Identity: &fwe_trade_common.BizIdentity{
			BizScene:   consts.BizSceneSHBackCompanyCar.Int32(),
			TenantType: tenant_base.TenantType_SecondHandTrade,
		},
	}
	rsp, err := impl.ActionOrder(ctx, req)
	fmt.Println(tools.GetLogStr(rsp), err)
	time.Sleep(1 * time.Second)
}

func TestJson(t *testing.T) {

	// 结构体
	level1 := struct{ Str string }{Str: "st \n ed"}
	level1Str, _ := sonic.MarshalString(level1)
	fmt.Println("level1Str", level1Str, len(level1Str))
	level1StrStr, _ := sonic.MarshalString(level1Str)
	fmt.Println("level1StrStr", level1StrStr)

	// 结构体
	level2 := struct{ Str string }{Str: level1Str}
	level2Str, _ := sonic.MarshalString(level2)
	fmt.Println("level2Str", level2Str, len(level2Str))
	level2StrStr, _ := sonic.MarshalString(level2Str)
	fmt.Println("level2StrStr", level2StrStr)

	level3 := struct{ Str string }{Str: level2Str}
	level3Str, _ := sonic.MarshalString(level3)
	fmt.Println("level3Str", level3Str, len(level3Str))
	level3StrStr, _ := sonic.MarshalString(level3Str)
	fmt.Println("level3StrStr", level3StrStr)

	// 输入
	input := map[string]string{"level1Str": level1Str, "level2Str": level2Str, "level3Str": level3Str}

	extraMap := map[string]string{"level3Str": level3Str}
	req := &action_model.ExtraUpdateReq{Extra: extraMap}
	fmt.Println(sonic.MarshalString(req))

	// db
	ctx := context.Background()
	caller.InitMysql()
	dal.Init()

	// json整体写入
	inputStr, _ := sonic.MarshalString(input)
	_, _ = db_query.Q.WithContext(ctx).FweOrder.
		Where(db_query.Q.FweOrder.OrderID.Eq("7133409765761241132")).
		UpdateColumnSimple(db_query.Q.FweOrder.ProductID.Value(inputStr))

	// json_set写入
	_ = caller.DB(ctx).Table("fwe_order").
		Where("order_id = ?", "7133409765761241132").
		Updates(map[string]interface{}{
			"product_extra": gorm.Expr(fmt.Sprintf("JSON_SET(product_extra, '$.%s', '%s', '$.%s', '%s', '$.%s', '%s')",
				"level1Str", level1StrStr[1:len(level1StrStr)-1],
				"level2Str", level2StrStr[1:len(level2StrStr)-1],
				"level3Str", level3StrStr[1:len(level3StrStr)-1])),
		}).Error

	// 查询
	order, _ := db_query.Q.WithContext(ctx).FweOrder.
		Where(db_query.Q.FweOrder.OrderID.Eq("7133409765761241132")).First()
	fmt.Println("order productID", order.ProductID, len(order.ProductID))
	fmt.Println("order productExtra", *order.ProductExtra, len(*order.ProductExtra))

	/*var dataList string
	_ = caller.DB(ctx).Table("fwe_order").
		Select("JSON_KEYS(product_extra)").
		Where("order_id = ?", "7133409765761241132").Find(&dataList)
	fmt.Println("dataList", dataList)
	keys := strings.Split(dataList[1:len(dataList) - 1], ",")

	var valList string
	caller.DB(ctx).Table("fwe_order").
		Select(fmt.Sprintf("JSON_EXTRACT(product_extra, '$.%s', '$.%s')", keys[0], keys[1])).
		Where("order_id = ?", "7133409765761241132").
		Find(&valList) // ignore_security_alert
	fmt.Println("valList", valList)
	vals := strings.Split(valList[1:len(valList) - 1], ",")
	fmt.Println("val1", vals[0], len(vals[0]), vals[0] == str)*/

	// json 反序列化
	var output1 map[string]string
	_ = sonic.UnmarshalString(order.ProductID, &output1)
	fmt.Println("output level1Str", output1, len(output1["level1Str"]), output1["level1Str"] == level1Str)
	fmt.Println("output level2Str", output1, len(output1["level2Str"]), output1["level2Str"] == level2Str)
	fmt.Println("output Level3Str", output1, len(output1["level3Str"]), output1["level3Str"] == level3Str)

	// json 反序列化
	var output2 map[string]string
	_ = sonic.UnmarshalString(*order.ProductExtra, &output2)
	fmt.Println("output Level1Str", output2, len(output2["level1Str"]), output2["level1Str"] == level1Str)
	fmt.Println("output Level2Str", output2, len(output2["level2Str"]), output2["level2Str"] == level2Str)
	fmt.Println("output Level3Str", output2, len(output2["level3Str"]), output2["level3Str"] == level3Str)

	// json 反序列化
	/*var output2 map[string]json.RawMessage
	_ = sonic.UnmarshalString(*order.ProductExtra, &output2)
	var outputLevel2Str string
	_ = sonic.Unmarshal(output2["level2Str"], &outputLevel2Str)
	fmt.Println("output Level2Str", outputLevel2Str, len(outputLevel2Str), outputLevel2Str == level2Str)*/
}

func TestTT(t *testing.T) {
	bfsm.Init(map[string]bfsm.BizDesc{
		"440200": nc_ecom.NCEcomFixedPriceSM,
	}, nil)
	stateMachine, err := bfsm.NewFSM(strconvh.FormatInt32(440200), 20202, nil)
	if err != nil {
		logs.Error("[ActionBaseExecution] err=%v", err.Error())
		panic(err)
	}

	err = stateMachine.Fire(context.Background(), "PayFinish_Earnest", map[string]interface{}{"after_payment_type": 1, "before_payment_type": 1})
	if err != nil {
		panic(err)
	}

	fmt.Println(stateMachine.CurState())
}

func TestStringLength(t *testing.T) {
	str1 := "黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪黑色奥迪"
	t.Logf("str1 length : %d", utf8.RuneCountInString(str1))
}

func TestGetMissingKey(t *testing.T) {
	jsonData := `{"foo": "bar","test_order_limit_amount":100}`
	value := gjson.Get(jsonData, "test_order_limit_amount").Int()
	if value != 0 {
		t.Errorf("Expected 0, got %d", value)
	}
}
