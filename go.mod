module code.byted.org/motor/fwe_trade_engine

go 1.19

replace github.com/apache/thrift => github.com/apache/thrift v0.13.0

require (
	code.byted.org/aurora/block v0.2.7
	code.byted.org/gopkg/env v1.6.26
	code.byted.org/gopkg/idgenerator v1.0.15
	code.byted.org/gopkg/lang v0.21.8
	code.byted.org/gopkg/logs v1.2.26
	code.byted.org/gopkg/metrics v1.4.25
	code.byted.org/gopkg/tccclient v1.6.0
	code.byted.org/gorm/bytedgen v0.3.25
	code.byted.org/gorm/bytedgorm v0.9.7
	code.byted.org/kite/kitex v1.18.1
	code.byted.org/kite/kitutil v3.8.8+incompatible
	code.byted.org/kv/goredis v5.4.0+incompatible
	code.byted.org/motor/bfsm v0.0.25
	code.byted.org/motor/dealer_utils_go v1.0.6
	code.byted.org/motor/fwe_trade_common v0.0.289
	code.byted.org/motor/gopkg v1.3.35
	code.byted.org/overpass/motor_fwe_contract_core v0.0.0-20220810102039-e297d3c600ac
	code.byted.org/overpass/motor_fwe_ecom_product_stock v0.0.0-20230221035248-7bf564862e78
	code.byted.org/overpass/motor_trade_audit v0.0.0-20221206123556-cd54e98a12c2
	code.byted.org/rocketmq/rocketmq-go-proxy v1.5.20
	code.byted.org/security/sensitive_finder_engine v0.3.18
	github.com/apache/thrift v0.19.0 // indirect
	github.com/apaxa-go/helper v0.0.0-20180607175117-61d31b1c31c3
	github.com/bytedance/sonic v1.13.2
	github.com/cloudwego/kitex v0.12.1
	github.com/go-sql-driver/mysql v1.8.0
	github.com/jinzhu/copier v0.4.0
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/tidwall/gjson v1.18.0
	golang.org/x/text v0.16.0
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	gorm.io/gen v0.3.25
	gorm.io/gorm v1.25.7
	gorm.io/plugin/dbresolver v1.5.1
)

require (
	code.byted.org/hystrix/hystrix-go v0.0.0-20190214095017-a2a890c81cd5 // indirect
	code.byted.org/iespkg/bytedkits-go/goext v0.4.0 // indirect
	code.byted.org/iespkg/retry-go v0.1.2 // indirect
	code.byted.org/kite/kitc v3.10.26+incompatible // indirect
	code.byted.org/overpass/common v0.0.0-20240815141408-18f972b75038
	code.byted.org/rocketmq/rocketmq-go-proxy-mqmesh-interceptor v1.0.18 // indirect
	github.com/emicklei/dot v1.3.1 // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	golang.org/x/sync v0.8.0 // indirect
)

require (
	code.byted.org/gopkg/ctxvalues v0.7.0
	code.byted.org/gopkg/gorm v2.0.1+incompatible
	code.byted.org/gopkg/logs/v2 v2.1.57
	code.byted.org/kitex/apache_monitor v0.1.0
	code.byted.org/lang/gg v0.21.0
	code.byted.org/motor/fwe_ecom_lib/ecom_robot v0.0.3
	code.byted.org/motor/fwe_ecom_product_common v0.0.37
	code.byted.org/motor/pack_airpass v0.0.0-20250423095100-910616533d91
	code.byted.org/motor/scheduler-go v1.0.2
	code.byted.org/motor/trade_pkg v1.0.58
	code.byted.org/overpass/motor_crm_wb_core v0.0.0-20250604074348-3b8d01d24427
	code.byted.org/overpass/motor_dealer_douyin_open_proxy v0.0.0-20250528044235-eccbc8bd026e
	code.byted.org/overpass/motor_fwe_ecom_product_item v0.0.0-20250707111036-3bf6970c76a5
	code.byted.org/overpass/motor_fwe_trade_payment v0.0.0-20250804071559-e00fdc1b32c0
	code.byted.org/overpass/motor_fwe_trade_product_am_supply v0.0.0-20240312172329-35858a4dda41
	code.byted.org/overpass/motor_fwe_trade_product_cps_ecom v0.0.0-20250225063637-094ea4cb5f9d
	code.byted.org/overpass/motor_service_rpc_idl_common v0.0.0-20250804070252-e99d65dbd1fd
	code.byted.org/overpass/toutiao_user_hash_mobile v0.0.0-20240312040749-8f3560fdee49
	github.com/aws/smithy-go v1.11.2
	github.com/cloudwego/gopkg v0.1.3
	github.com/cloudwego/kitex/pkg/protocol/bthrift v0.0.0-20250731054635-6c996664cebe
	github.com/fatih/structs v1.1.0
	github.com/tidwall/sjson v1.2.5
	gorm.io/driver/mysql v1.5.2
	gotest.tools v2.2.0+incompatible
)

require (
	code.byted.org/aiops/apm_vendor_byted v0.0.27 // indirect
	code.byted.org/aiops/metrics_codec v0.0.24 // indirect
	code.byted.org/aiops/monitoring-common-go v0.0.5 // indirect
	code.byted.org/aurora/govaluate v0.1.4 // indirect
	code.byted.org/bytedtrace-contrib/kitex-go v1.1.48 // indirect
	code.byted.org/bytedtrace/bytedtrace-client-go v1.2.3-pre // indirect
	code.byted.org/bytedtrace/bytedtrace-common/go v0.0.13 // indirect
	code.byted.org/bytedtrace/bytedtrace-compatible-lightweight-go v1.0.2 // indirect
	code.byted.org/bytedtrace/bytedtrace-conf-provider-client-go v0.0.26 // indirect
	code.byted.org/bytedtrace/bytedtrace-gls-switch v1.3.0 // indirect
	code.byted.org/bytedtrace/interface-go v1.0.20 // indirect
	code.byted.org/bytedtrace/serializer-go v1.0.0 // indirect
	code.byted.org/cpputil/model v0.0.0-20240312021728-1d2b41e42c90 // indirect
	code.byted.org/go-lark/lark v1.19.0 // indirect
	code.byted.org/golf/consul v2.1.13+incompatible // indirect
	code.byted.org/golf/ssconf v0.0.1 // indirect
	code.byted.org/gopkg/apm_vendor_interface v0.0.3 // indirect
	code.byted.org/gopkg/asyncache v0.0.0-20210129072708-1df5611dba17 // indirect
	code.byted.org/gopkg/asynccache v0.0.0-20210422090342-26f94f7676b8 // indirect
	code.byted.org/gopkg/bytedmysql v1.1.15 // indirect
	code.byted.org/gopkg/consul v1.2.6 // indirect
	code.byted.org/gopkg/context v0.0.1 // indirect
	code.byted.org/gopkg/debug v0.10.1 // indirect
	code.byted.org/gopkg/etcd_util v2.3.3+incompatible // indirect
	code.byted.org/gopkg/etcdproxy v0.1.1 // indirect
	code.byted.org/gopkg/logid v0.0.0-20241008043456-230d03adb830 // indirect
	code.byted.org/gopkg/metainfo v0.1.4 // indirect
	code.byted.org/gopkg/metrics/v3 v3.1.35 // indirect
	code.byted.org/gopkg/metrics/v4 v4.1.4 // indirect
	code.byted.org/gopkg/metrics_core v0.0.39 // indirect
	code.byted.org/gopkg/net2 v1.5.0 // indirect
	code.byted.org/gopkg/pkg v0.0.0-20210817064112-6fe00340bb36 // indirect
	code.byted.org/gopkg/stats v1.2.12 // indirect
	code.byted.org/gopkg/thrift v1.14.2 // indirect
	code.byted.org/inf/authcenter v1.5.0 // indirect
	code.byted.org/inf/infsecc v1.0.3 // indirect
	code.byted.org/kite/endpoint v3.7.5+incompatible // indirect
	code.byted.org/kite/kitex-overpass-suite v0.0.35 // indirect
	code.byted.org/kite/kitex/pkg/protocol/bthrift v0.0.0-20250717120948-754033068c26 // indirect
	code.byted.org/kite/rpal v0.1.22 // indirect
	code.byted.org/kv/backoff v0.0.0-20191031070508-5d868504e646 // indirect
	code.byted.org/kv/circuitbreaker v0.0.0-20200212034351-d3f51a5b9165 // indirect
	code.byted.org/kv/redis-v6 v1.0.26 // indirect
	code.byted.org/lang/trace v0.0.3 // indirect
	code.byted.org/lidar/profiler v0.4.4 // indirect
	code.byted.org/lidar/profiler/kitex v0.4.6 // indirect
	code.byted.org/log_market/gosdk v0.0.0-20230524072203-e069d8367314 // indirect
	code.byted.org/log_market/loghelper v0.1.11 // indirect
	code.byted.org/log_market/tracelog v0.1.5 // indirect
	code.byted.org/log_market/ttlogagent_gosdk v0.0.7 // indirect
	code.byted.org/log_market/ttlogagent_gosdk/v4 v4.0.53 // indirect
	code.byted.org/middleware/fic_client v0.2.8 // indirect
	code.byted.org/middleware/gocaller v0.0.6 // indirect
	code.byted.org/motor/fwe_ecom_lib/ecom_err v0.0.10 // indirect
	code.byted.org/motor/fwe_ecom_lib/tools v0.0.1 // indirect
	code.byted.org/overpass/motor_car_common v0.0.0-20250507132218-97a0c792eafa // indirect
	code.byted.org/overpass/motor_fwe_trade_meta v0.0.0-20240312165640-270c4f206ecf // indirect
	code.byted.org/overpass/motor_scheduler_core v0.0.0-20240912093004-9e4357f218d4 // indirect
	code.byted.org/overpass/motor_trade_dms_order v0.0.0-20250606095737-d4ea2b7e07ea // indirect
	code.byted.org/overpass/toutiao_location_district v0.0.0-20230109141449-351ee1ae65e9 // indirect
	code.byted.org/security/certinfo v1.0.2 // indirect
	code.byted.org/security/cryptoutils v1.1.3 // indirect
	code.byted.org/security/go-spiffe-v2 v1.0.8 // indirect
	code.byted.org/security/gokms-extension v1.0.1 // indirect
	code.byted.org/security/golangope v0.0.1 // indirect
	code.byted.org/security/kms-v2-sdk-golang v1.2.94 // indirect
	code.byted.org/security/memfd v0.0.1 // indirect
	code.byted.org/security/spiffe_spire v0.0.0-20201116193931-c566c1c41bdf // indirect
	code.byted.org/security/volc_kms_encryption_sdk/v2 v2.0.7 // indirect
	code.byted.org/security/volczti-helper v1.5.7 // indirect
	code.byted.org/security/zero-trust-identity-helper v1.0.14 // indirect
	code.byted.org/security/zti-jwt-helper-golang v1.0.16 // indirect
	code.byted.org/service_mesh/shmipc v0.2.16 // indirect
	code.byted.org/trace/trace-client-go v1.3.7 // indirect
	code.byted.org/xiaoganbo/stm v1.7.0 // indirect
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/3vilive/sizeof v0.0.0-20220507072046-f0cfbbd2c289 // indirect
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible // indirect
	github.com/andres-erbsen/clock v0.0.0-20160526145045-9e14626cd129 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bits-and-blooms/bitset v1.13.0 // indirect
	github.com/bits-and-blooms/bloom/v3 v3.6.0 // indirect
	github.com/bluele/gcache v0.0.2 // indirect
	github.com/bufbuild/protocompile v0.10.0 // indirect
	github.com/bytedance/gopkg v0.1.1 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/caarlos0/env/v6 v6.10.1 // indirect
	github.com/capitalone/fpe v1.2.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/cloudwego/configmanager v0.2.2 // indirect
	github.com/cloudwego/dynamicgo v0.4.7-0.20241220085612-55704ea4ca8f // indirect
	github.com/cloudwego/fastpb v0.0.5 // indirect
	github.com/cloudwego/frugal v0.2.3 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/cloudwego/localsession v0.1.1 // indirect
	github.com/cloudwego/netpoll v0.6.5 // indirect
	github.com/cloudwego/runtimex v0.1.0 // indirect
	github.com/cloudwego/thriftgo v0.3.18 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/demdxx/gocast v1.2.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230111030713-bf00bc1b83b6 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/erikstmartin/go-testdb v0.0.0-20160219214506-8d10e4a1bae5 // indirect
	github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a // indirect
	github.com/fatih/color v1.14.1 // indirect
	github.com/fatih/structtag v1.2.0 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/go-jose/go-jose/v3 v3.0.3 // indirect
	github.com/go-kit/log v0.2.1 // indirect
	github.com/go-logfmt/logfmt v0.6.0 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/pprof v0.0.0-20240727154555-813a5fbdbec8 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gorilla/mux v1.8.1 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware v1.3.0 // indirect
	github.com/hashicorp/go-hclog v1.5.0 // indirect
	github.com/hashicorp/golang-lru v1.0.2 // indirect
	github.com/hbollon/go-edlib v1.6.0 // indirect
	github.com/iancoleman/strcase v0.2.0 // indirect
	github.com/influxdata/influxdb1-client v0.0.0-20220302092344-a9ab5670611c // indirect
	github.com/jhump/protoreflect v1.16.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/jxskiss/base62 v1.0.0 // indirect
	github.com/klauspost/compress v1.17.9 // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.1 // indirect
	github.com/miscreant/miscreant.go v0.0.0-20200214223636-26d376326b75 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/gls v0.0.0-20220109145502-612d0167dce5 // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/opentracing/opentracing-go v1.2.1-0.20210205174328-3088eee7e4d2 // indirect
	github.com/orcaman/concurrent-map v1.0.0 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/power-devops/perfstat v0.0.0-20221212215047-62379fc7944b // indirect
	github.com/prometheus/client_golang v1.12.2 // indirect
	github.com/prometheus/client_model v0.4.0 // indirect
	github.com/prometheus/common v0.32.1 // indirect
	github.com/prometheus/procfs v0.7.3 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/shirou/gopsutil/v3 v3.23.12 // indirect
	github.com/spiffe/spire-api-sdk v1.9.6 // indirect
	github.com/stretchr/testify v1.9.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/yusufpapurcu/wmi v1.2.3 // indirect
	github.com/zeebo/errs v1.3.0 // indirect
	go.mozilla.org/pkcs7 v0.0.0-20210826202110-33d05740a352 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go4.org/unsafe/assume-no-moving-gc v0.0.0-20230525183740-e7c30c78aeb2 // indirect
	golang.org/x/arch v0.8.0 // indirect
	golang.org/x/crypto v0.23.0 // indirect
	golang.org/x/mod v0.17.0 // indirect
	golang.org/x/net v0.25.0 // indirect
	golang.org/x/sys v0.24.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240415180920-8c6c420018be // indirect
	google.golang.org/grpc v1.63.2 // indirect
	google.golang.org/protobuf v1.34.2 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/datatypes v1.1.1-0.20230130040222-c43177d3cf8c // indirect
	gorm.io/hints v1.1.2 // indirect
)
